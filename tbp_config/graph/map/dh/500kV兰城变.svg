<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549596979202" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="ACLineSegment:500kV线路_0" viewBox="0,0,40,30">
   <use terminal-index="0" type="0" x="39.47302405498282" xlink:href="#terminal" y="1.14455272363816"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.5" x2="27.16666666666667" y1="15.58333333333333" y2="15.58333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.25" x2="27.25" y1="21.25" y2="25.83333333333334"/>
   <path d="M 36.6667 24.0833 L 36.6667 28.0833 L 34.6667 28.0833" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.25" x2="27.25" y1="12.5" y2="19.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.25" x2="29.25" y1="12.41666666666666" y2="12.41666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.16666666666667" x2="33.16666666666667" y1="27.58333333333333" y2="28.58333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.25" x2="29.25" y1="10.41666666666666" y2="10.41666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.91666666666667" x2="33.91666666666667" y1="27.08333333333333" y2="29.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.83333333333333" x2="32.83333333333333" y1="19.75" y2="21.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.66666666666667" x2="34.66666666666667" y1="26.58333333333333" y2="29.58333333333333"/>
   <path d="M 20.75 14.5833 L 22.75 14.5833 L 21.75 20.3333 L 20.75 14.5833" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 27.25 10.3333 L 27.25 8.33333 L 21.75 8.33333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.91666666666667" x2="31.66666666666667" y1="19.75" y2="18.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.83333333333334" x2="33.91666666666666" y1="19.75" y2="18.66666666666667"/>
   <path d="M 32.8333 14.3333 L 31.8333 16.3333 L 33.8333 16.3333 L 32.8333 14.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="10" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,21.75,17.58) scale(1,1) translate(0,0)" width="4" x="19.75" y="12.58"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.75" x2="21.75" y1="1.083333333333332" y2="14.58333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.25" x2="39.46434707903781" y1="1.099999999999985" y2="1.099999999999985"/>
   <ellipse cx="32.78" cy="23.83" fill-opacity="0" rx="2.33" ry="2.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="32.78" cy="15.53" fill-opacity="0" rx="2.33" ry="2.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="32.78" cy="19.72" fill-opacity="0" rx="2.33" ry="2.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.69166666666667" x2="27.69166666666667" y1="27.35833333333333" y2="27.35833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.66666666666667" x2="36.66666666666667" y1="22.33333333333333" y2="23.74999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.69166666666667" x2="28.69166666666667" y1="25.85833333333333" y2="25.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.75" x2="33.83333333333334" y1="24" y2="22.91666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.75" x2="21.75" y1="22.58333333333333" y2="25.83333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.83333333333334" x2="31.58333333333334" y1="23.99999999999999" y2="22.99999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.19166666666667" x2="28.19166666666667" y1="26.60833333333333" y2="26.60833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.75000000000001" x2="35.50000000000001" y1="22.33333333333333" y2="21.33333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.66666666666666" x2="37.74999999999999" y1="22.33333333333333" y2="21.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.66666666666667" x2="36.66666666666667" y1="17.75" y2="19.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.75" x2="32.75" y1="24" y2="25.41666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.33333333333334" x2="29.33333333333334" y1="19.16666666666666" y2="19.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.33333333333334" x2="29.33333333333334" y1="21.16666666666666" y2="21.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.75000000000001" x2="35.50000000000001" y1="17.75" y2="16.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.66666666666666" x2="37.74999999999999" y1="17.75" y2="16.66666666666666"/>
   <ellipse cx="36.61" cy="21.83" fill-opacity="0" rx="2.33" ry="2.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="36.61" cy="17.63" fill-opacity="0" rx="2.33" ry="2.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.69166666666667" x2="22.69166666666667" y1="26.60833333333333" y2="26.60833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19166666666667" x2="23.19166666666667" y1="25.85833333333333" y2="25.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.19166666666667" x2="22.19166666666667" y1="27.35833333333333" y2="27.35833333333333"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="500kV兰城变" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="82.56999999999999" id="1" preserveAspectRatio="xMidYMid slice" width="249.69" x="29.81" xlink:href="logo.png" y="29.43"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,154.653,70.7136) scale(1,1) translate(-6.61842e-15,0)" writing-mode="lr" x="154.65" xml:space="preserve" y="74.20999999999999" zvalue="1861"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,183.5,70.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="183.5" xml:space="preserve" y="79.69" zvalue="1862">500kV兰城变</text>
  <line fill="none" id="39" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="379.5" x2="379.5" y1="6" y2="1036" zvalue="1865"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,76.75,207.25) scale(1,1) translate(0,0)" writing-mode="lr" x="76.75" xml:space="preserve" y="211.75" zvalue="1942">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1255,375.932) scale(1,1) translate(0,0)" writing-mode="lr" x="1255" xml:space="preserve" y="380.43" zvalue="1954">500kVⅡ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1257.26,797.092) scale(1,1) translate(0,0)" writing-mode="lr" x="1257.26" xml:space="preserve" y="801.59" zvalue="1955">500kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="102" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1025.38,470.543) scale(1,1) translate(0,0)" writing-mode="lr" x="1025.38" xml:space="preserve" y="475.04" zvalue="1956">5343</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="101" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1023.47,425.113) scale(1,1) translate(0,0)" writing-mode="lr" x="1023.47" xml:space="preserve" y="429.61" zvalue="1958">53432</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1025.05,514.076) scale(1,1) translate(0,0)" writing-mode="lr" x="1025.05" xml:space="preserve" y="518.58" zvalue="1960">53431</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1023.39,601.275) scale(1,1) translate(0,0)" writing-mode="lr" x="1023.39" xml:space="preserve" y="605.78" zvalue="1965">5342</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1023.31,562.721) scale(1,1) translate(0,0)" writing-mode="lr" x="1023.31" xml:space="preserve" y="567.22" zvalue="1966">53422</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1023.92,654.073) scale(1,1) translate(0,0)" writing-mode="lr" x="1023.92" xml:space="preserve" y="658.5700000000001" zvalue="1969">53421</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1022.87,744.157) scale(1,1) translate(0,0)" writing-mode="lr" x="1022.87" xml:space="preserve" y="748.66" zvalue="1973">5341</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1023.31,697.212) scale(1,1) translate(0,0)" writing-mode="lr" x="1023.31" xml:space="preserve" y="701.71" zvalue="1974">53412</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1023.92,789.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1023.92" xml:space="preserve" y="794.33" zvalue="1977">53411</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1139.75,215.105) scale(1,1) translate(0,0)" writing-mode="lr" x="1139.75" xml:space="preserve" y="219.61" zvalue="1986">500kV德兰线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,977.73,540.257) scale(1,1) translate(0,0)" writing-mode="lr" x="977.73" xml:space="preserve" y="544.76" zvalue="1989">534367</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,985.711,675.497) scale(1,1) translate(0,0)" writing-mode="lr" x="985.71" xml:space="preserve" y="680" zvalue="1991">534167</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1089.55,755.431) scale(1,1) translate(0,0)" writing-mode="lr" x="1089.55" xml:space="preserve" y="759.9299999999999" zvalue="2056">534117</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1089.55,702.964) scale(1,1) translate(0,0)" writing-mode="lr" x="1089.55" xml:space="preserve" y="707.46" zvalue="2058">534127</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1090.57,615.284) scale(1,1) translate(0,0)" writing-mode="lr" x="1090.57" xml:space="preserve" y="619.78" zvalue="2062">534217</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1091.73,569.578) scale(1,1) translate(0,0)" writing-mode="lr" x="1091.73" xml:space="preserve" y="574.08" zvalue="2064">534227</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1091.05,481.85) scale(1,1) translate(0,0)" writing-mode="lr" x="1091.05" xml:space="preserve" y="486.35" zvalue="2068">534317</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1091.05,434.569) scale(1,1) translate(0,0)" writing-mode="lr" x="1091.05" xml:space="preserve" y="439.07" zvalue="2070">534327</text>
 </g>
 <g id="ButtonClass"/>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="247">
   <path class="kv500" d="M 977.65 398.6 L 1283 398.6" stroke-width="4" zvalue="1953"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674264154117" ObjectName="500kVⅡ母"/>
   <cge:TPSR_Ref TObjectID="9288674264154117"/></metadata>
  <path d="M 977.65 398.6 L 1283 398.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="246">
   <path class="kv500" d="M 973.24 814.76 L 1278.59 814.76" stroke-width="4" zvalue="1954"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674264088581" ObjectName="500kVⅠ母"/>
   <cge:TPSR_Ref TObjectID="9288674264088581"/></metadata>
  <path d="M 973.24 814.76 L 1278.59 814.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="245">
   <use class="kv500" height="20" transform="rotate(0,1052.68,470.919) scale(1.59227,1.58597) translate(-388.597,-168.132)" width="10" x="1044.714828796538" xlink:href="#Breaker:开关_0" y="455.0596156562617" zvalue="1955"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924601249796" ObjectName="5343断路器"/>
   <cge:TPSR_Ref TObjectID="6473924601249796"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1052.68,470.919) scale(1.59227,1.58597) translate(-388.597,-168.132)" width="10" x="1044.714828796538" y="455.0596156562617"/></g>
  <g id="239">
   <use class="kv500" height="20" transform="rotate(0,1052.18,604.141) scale(1.59227,1.58597) translate(-388.413,-217.354)" width="10" x="1044.218294225091" xlink:href="#Breaker:开关_0" y="588.2813710919795" zvalue="1964"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924601184260" ObjectName="5342断路器"/>
   <cge:TPSR_Ref TObjectID="6473924601184260"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1052.18,604.141) scale(1.59227,1.58597) translate(-388.413,-217.354)" width="10" x="1044.218294225091" y="588.2813710919795"/></g>
  <g id="234">
   <use class="kv500" height="20" transform="rotate(0,1052.18,744.976) scale(1.59227,1.58597) translate(-388.413,-269.388)" width="10" x="1044.218294225091" xlink:href="#Breaker:开关_0" y="729.1157982668818" zvalue="1972"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924601118724" ObjectName="5341断路器"/>
   <cge:TPSR_Ref TObjectID="6473924601118724"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1052.18,744.976) scale(1.59227,1.58597) translate(-388.413,-269.388)" width="10" x="1044.218294225091" y="729.1157982668818"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="244">
   <use class="kv500" height="30" transform="rotate(0,1052.68,425.003) scale(1.15801,0.930438) translate(-142.454,30.7311)" width="15" x="1043.991070702702" xlink:href="#Disconnector:刀闸_0" y="411.0465929298123" zvalue="1957"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450365816837" ObjectName="53432隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450365816837"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1052.68,425.003) scale(1.15801,0.930438) translate(-142.454,30.7311)" width="15" x="1043.991070702702" y="411.0465929298123"/></g>
  <g id="243">
   <use class="kv500" height="30" transform="rotate(0,1052.1,516.595) scale(1.15801,0.930438) translate(-142.375,37.5788)" width="15" x="1043.412064227634" xlink:href="#Disconnector:刀闸_0" y="502.638814026161" zvalue="1959"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450365751301" ObjectName="53431隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450365751301"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1052.1,516.595) scale(1.15801,0.930438) translate(-142.375,37.5788)" width="15" x="1043.412064227634" y="502.638814026161"/></g>
  <g id="238">
   <use class="kv500" height="30" transform="rotate(0,1052.07,563.54) scale(1.15801,0.930438) translate(-142.371,41.0885)" width="15" x="1043.381246461215" xlink:href="#Disconnector:刀闸_0" y="549.5836230844617" zvalue="1965"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450365685765" ObjectName="53422隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450365685765"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1052.07,563.54) scale(1.15801,0.930438) translate(-142.371,41.0885)" width="15" x="1043.381246461215" y="549.5836230844617"/></g>
  <g id="237">
   <use class="kv500" height="30" transform="rotate(0,1052.16,654.892) scale(1.15801,0.930438) translate(-142.384,47.9183)" width="15" x="1043.477810119311" xlink:href="#Disconnector:刀闸_0" y="640.9356839546682" zvalue="1968"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450365620229" ObjectName="53421隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450365620229"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1052.16,654.892) scale(1.15801,0.930438) translate(-142.384,47.9183)" width="15" x="1043.477810119311" y="640.9356839546682"/></g>
  <g id="233">
   <use class="kv500" height="30" transform="rotate(0,1052.07,698.031) scale(1.15801,0.930438) translate(-142.371,51.1435)" width="15" x="1043.381246461215" xlink:href="#Disconnector:刀闸_0" y="684.0741571433766" zvalue="1973"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450365554693" ObjectName="53412隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450365554693"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1052.07,698.031) scale(1.15801,0.930438) translate(-142.371,51.1435)" width="15" x="1043.381246461215" y="684.0741571433766"/></g>
  <g id="232">
   <use class="kv500" height="30" transform="rotate(0,1052.16,790.652) scale(1.15801,0.930438) translate(-142.384,58.0681)" width="15" x="1043.477810119311" xlink:href="#Disconnector:刀闸_0" y="776.6949966367808" zvalue="1976"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450365489157" ObjectName="53411隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450365489157"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1052.16,790.652) scale(1.15801,0.930438) translate(-142.384,58.0681)" width="15" x="1043.477810119311" y="776.6949966367808"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="242">
   <path class="kv500" d="M 1052.78 398.6 L 1052.78 411.51" stroke-width="1" zvalue="1961"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="247@0" LinkObjectIDznd="244@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1052.78 398.6 L 1052.78 411.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="241">
   <path class="kv500" d="M 1052.75 438.72 L 1052.62 455.75" stroke-width="1" zvalue="1962"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="244@1" LinkObjectIDznd="245@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1052.75 438.72 L 1052.62 455.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="240">
   <path class="kv500" d="M 1052.78 486.07 L 1052.78 503.1" stroke-width="1" zvalue="1963"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="245@1" LinkObjectIDznd="243@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1052.78 486.07 L 1052.78 503.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="236">
   <path class="kv500" d="M 1052.14 577.26 L 1052.13 588.97" stroke-width="1" zvalue="1970"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="238@1" LinkObjectIDznd="239@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1052.14 577.26 L 1052.13 588.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="235">
   <path class="kv500" d="M 1052.29 619.29 L 1052.26 641.4" stroke-width="1" zvalue="1971"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="239@1" LinkObjectIDznd="237@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1052.29 619.29 L 1052.26 641.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="231">
   <path class="kv500" d="M 1052.23 668.61 L 1052.17 684.54" stroke-width="1" zvalue="1978"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="237@1" LinkObjectIDznd="233@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1052.23 668.61 L 1052.17 684.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="230">
   <path class="kv500" d="M 1052.14 711.75 L 1052.13 729.8" stroke-width="1" zvalue="1979"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="233@1" LinkObjectIDznd="234@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1052.14 711.75 L 1052.13 729.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="229">
   <path class="kv500" d="M 1052.29 760.12 L 1052.26 777.16" stroke-width="1" zvalue="1980"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="234@1" LinkObjectIDznd="232@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1052.29 760.12 L 1052.26 777.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="228">
   <path class="kv500" d="M 1052.23 804.37 L 1052.23 814.76" stroke-width="1" zvalue="1981"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="232@1" LinkObjectIDznd="246@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1052.23 804.37 L 1052.23 814.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="227">
   <path class="kv500" d="M 1052.17 530.31 L 1052.17 550.05" stroke-width="1" zvalue="1982"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="243@1" LinkObjectIDznd="238@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1052.17 530.31 L 1052.17 550.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="216">
   <path class="kv500" d="M 1034.94 676.61 L 1052.2 676.61" stroke-width="1" zvalue="1991"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="217@0" LinkObjectIDznd="231" MaxPinNum="2"/>
   </metadata>
  <path d="M 1034.94 676.61 L 1052.2 676.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="152">
   <path class="kv500" d="M 1066.94 719.75 L 1052.14 719.75" stroke-width="1" zvalue="2058"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="155@0" LinkObjectIDznd="230" MaxPinNum="2"/>
   </metadata>
  <path d="M 1066.94 719.75 L 1052.14 719.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="147">
   <path class="kv500" d="M 1066.94 768.12 L 1052.28 768.12" stroke-width="1" zvalue="2060"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157@0" LinkObjectIDznd="229" MaxPinNum="2"/>
   </metadata>
  <path d="M 1066.94 768.12 L 1052.28 768.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="144">
   <path class="kv500" d="M 1068.1 581.25 L 1052.13 581.25" stroke-width="1" zvalue="2065"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@0" LinkObjectIDznd="236" MaxPinNum="2"/>
   </metadata>
  <path d="M 1068.1 581.25 L 1052.13 581.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="143">
   <path class="kv500" d="M 1066.94 627.97 L 1052.28 627.97" stroke-width="1" zvalue="2066"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="146@0" LinkObjectIDznd="235" MaxPinNum="2"/>
   </metadata>
  <path d="M 1066.94 627.97 L 1052.28 627.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="140">
   <path class="kv500" d="M 1066.94 448.18 L 1052.68 448.37" stroke-width="1" zvalue="2071"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="141@0" LinkObjectIDznd="241" MaxPinNum="2"/>
   </metadata>
  <path d="M 1066.94 448.18 L 1052.68 448.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="139">
   <path class="kv500" d="M 1066.94 496.24 L 1052.78 496.2" stroke-width="1" zvalue="2072"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="142@0" LinkObjectIDznd="240" MaxPinNum="2"/>
   </metadata>
  <path d="M 1066.94 496.24 L 1052.78 496.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="251">
   <path class="kv500" d="M 1031.28 540.22 L 1052.17 540.22" stroke-width="1" zvalue="2183"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="219@0" LinkObjectIDznd="227" MaxPinNum="2"/>
   </metadata>
  <path d="M 1031.28 540.22 L 1052.17 540.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="252">
   <path class="kv500" d="M 1050.42 540.22 L 1138.51 540.22 L 1138.51 276.75" stroke-width="1" zvalue="2184"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="251" LinkObjectIDznd="224@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1050.42 540.22 L 1138.51 540.22 L 1138.51 276.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="224">
   <use class="kv500" height="30" transform="rotate(90,1153.15,254.203) scale(1.15801,-1.05732) translate(-154.19,-493.767)" width="40" x="1129.994596208631" xlink:href="#ACLineSegment:500kV线路_0" y="238.343729816117" zvalue="1985"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249331597315" ObjectName="500kV德兰线"/>
   <cge:TPSR_Ref TObjectID="8444249331597315_5066549596979202"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1153.15,254.203) scale(1.15801,-1.05732) translate(-154.19,-493.767)" width="40" x="1129.994596208631" y="238.343729816117"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="219">
   <use class="kv500" height="20" transform="rotate(90,1018.91,540.275) scale(-1.15801,1.26878) translate(-1897.99,-111.764)" width="10" x="1013.116595609889" xlink:href="#GroundDisconnector:地刀_0" y="527.5872928333455" zvalue="1987"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450365358085" ObjectName="534367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450365358085"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1018.91,540.275) scale(-1.15801,1.26878) translate(-1897.99,-111.764)" width="10" x="1013.116595609889" y="527.5872928333455"/></g>
  <g id="217">
   <use class="kv500" height="20" transform="rotate(90,1022.57,676.668) scale(-1.15801,1.26878) translate(-1904.81,-140.658)" width="10" x="1016.777766374504" xlink:href="#GroundDisconnector:地刀_0" y="663.9802739796813" zvalue="1990"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450365227013" ObjectName="534167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450365227013"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1022.57,676.668) scale(-1.15801,1.26878) translate(-1904.81,-140.658)" width="10" x="1016.777766374504" y="663.9802739796813"/></g>
  <g id="157">
   <use class="kv500" height="20" transform="rotate(270,1079.31,768.179) scale(1.15801,1.26878) translate(-146.484,-160.044)" width="10" x="1073.520400931173" xlink:href="#GroundDisconnector:地刀_0" y="755.4916902867998" zvalue="2055"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450365095941" ObjectName="534117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450365095941"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1079.31,768.179) scale(1.15801,1.26878) translate(-146.484,-160.044)" width="10" x="1073.520400931173" y="755.4916902867998"/></g>
  <g id="155">
   <use class="kv500" height="20" transform="rotate(270,1079.31,719.807) scale(1.15801,1.26878) translate(-146.484,-149.796)" width="10" x="1073.520400931173" xlink:href="#GroundDisconnector:地刀_0" y="707.1187471683899" zvalue="2057"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450364964869" ObjectName="534127接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450364964869"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1079.31,719.807) scale(1.15801,1.26878) translate(-146.484,-149.796)" width="10" x="1073.520400931173" y="707.1187471683899"/></g>
  <g id="146">
   <use class="kv500" height="20" transform="rotate(270,1079.31,628.032) scale(1.15801,1.26878) translate(-146.484,-130.355)" width="10" x="1073.520400931173" xlink:href="#GroundDisconnector:地刀_0" y="615.3444733898926" zvalue="2061"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450364833797" ObjectName="534217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450364833797"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1079.31,628.032) scale(1.15801,1.26878) translate(-146.484,-130.355)" width="10" x="1073.520400931173" y="615.3444733898926"/></g>
  <g id="145">
   <use class="kv500" height="20" transform="rotate(270,1080.47,581.303) scale(1.15801,1.26878) translate(-146.642,-120.456)" width="10" x="1074.67841388131" xlink:href="#GroundDisconnector:地刀_0" y="568.6153024324213" zvalue="2063"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450364702725" ObjectName="534227接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450364702725"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1080.47,581.303) scale(1.15801,1.26878) translate(-146.642,-120.456)" width="10" x="1074.67841388131" y="568.6153024324213"/></g>
  <g id="142">
   <use class="kv500" height="20" transform="rotate(270,1079.31,496.295) scale(1.15801,1.26878) translate(-146.484,-102.448)" width="10" x="1073.520400931173" xlink:href="#GroundDisconnector:地刀_0" y="483.6071346782012" zvalue="2067"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450364571653" ObjectName="534317接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450364571653"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1079.31,496.295) scale(1.15801,1.26878) translate(-146.484,-102.448)" width="10" x="1073.520400931173" y="483.6071346782012"/></g>
  <g id="141">
   <use class="kv500" height="20" transform="rotate(270,1079.31,448.24) scale(1.15801,1.26878) translate(-146.484,-92.2676)" width="10" x="1073.520400931173" xlink:href="#GroundDisconnector:地刀_0" y="435.5521443246029" zvalue="2069"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450364440581" ObjectName="534327接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450364440581"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1079.31,448.24) scale(1.15801,1.26878) translate(-146.484,-92.2676)" width="10" x="1073.520400931173" y="435.5521443246029"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="105">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="105" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1139.23,166.779) scale(1,1) translate(3.67165e-13,-4.17116e-13)" writing-mode="lr" x="1139.39" xml:space="preserve" y="171.68" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128263811076" ObjectName="P"/>
   </metadata>
  </g>
  <g id="104">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="104" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1139.23,182.106) scale(1,1) translate(3.67165e-13,-4.57955e-13)" writing-mode="lr" x="1139.39" xml:space="preserve" y="187.01" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128263876612" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="103">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="103" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1139.23,197.433) scale(1,1) translate(3.67165e-13,-4.98794e-13)" writing-mode="lr" x="1139.39" xml:space="preserve" y="202.33" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128263942148" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
</svg>