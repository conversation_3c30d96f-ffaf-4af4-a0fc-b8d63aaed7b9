<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549683552257" height="1045" id="thSvg" source="NR-PCS9000" viewBox="0 0 1900 1045" width="1900">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="Accessory:避雷器PT带熔断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="18" xlink:href="#terminal" y="1.066666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.24166666666667" x2="10.86666666666667" y1="1" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.26666666666667" x2="25.26666666666667" y1="6.583333333333332" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="12" y2="1"/>
   <ellipse cx="10.62" cy="16.75" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.62" cy="20.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.78" cy="24.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.05" cy="20.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.86666666666667" x2="14.86666666666667" y1="23.25" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.61666666666667" x2="10.61666666666667" y1="19" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.616666666666671" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.61666666666667" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.866666666666671" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="26.75" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067752" x2="5.636382113821139" y1="22.23028455284553" y2="20.91546973803071"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.10250677506775" x2="5.63638211382114" y1="18.28584010840109" y2="19.60065492321591"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067754" x2="8.102506775067756" y1="18.28584010840108" y2="22.23028455284553"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,10.86,6.21) scale(-1,1) translate(-1734.33,0)" width="4.92" x="8.4" y="2.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.53333333333333" x2="27.36666666666667" y1="18.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="6.583333333333337" y2="12.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="14.83333333333334" y2="18.43333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="26.95" y1="12.5" y2="9.166666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.23333333333333" x2="23.65000000000001" y1="12.53333333333333" y2="9.283333333333333"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,25.36,10.96) scale(-1,1) translate(-2314.33,0)" width="4.92" x="22.9" y="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.93333333333334" x2="27.01666666666667" y1="19.75" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.28333333333333" x2="26.41666666666666" y1="21" y2="21"/>
  </symbol>
  <symbol id="Accessory:线路PT带避雷器0904_0" viewBox="0,0,20,40">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.9166666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="12.91666666666667" y1="23.75" y2="23.75"/>
   <rect fill-opacity="0" height="10" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,11) scale(1,1) translate(0,0)" width="6" x="7" y="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.988213005145578" x2="9.988213005145578" y1="1.029523490692871" y2="18.75"/>
   <ellipse cx="9.76" cy="32.23" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.76" cy="24.17" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="12.91666666666667" y1="32.91666666666667" y2="32.91666666666667"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV油松岭变" InitShowingPlane="" fill="rgb(0,0,0)" height="1045" width="1900" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="82.56999999999999" id="1" preserveAspectRatio="xMidYMid slice" width="249.69" x="67.31" xlink:href="logo.png" y="34.5"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="197" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,192.153,75.785) scale(1,1) translate(-1.49451e-14,0)" writing-mode="lr" x="192.15" xml:space="preserve" y="79.29000000000001" zvalue="1199"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,221,75.7618) scale(1,1) translate(0,0)" writing-mode="lr" x="221" xml:space="preserve" y="84.76000000000001" zvalue="1200">35kV油松岭变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="69" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,311.438,390.75) scale(1,1) translate(0,0)" width="72.88" x="275" y="378.75" zvalue="1357"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,311.438,390.75) scale(1,1) translate(0,0)" writing-mode="lr" x="311.44" xml:space="preserve" y="395.25" zvalue="1357">小电流接地</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="63" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,108.625,317.75) scale(1,1) translate(0,0)" width="72.88" x="72.19" y="305.75" zvalue="1358"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,108.625,317.75) scale(1,1) translate(0,0)" writing-mode="lr" x="108.63" xml:space="preserve" y="322.25" zvalue="1358">全站公用</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="66" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,210.531,390.75) scale(1,1) translate(0,0)" width="72.88" x="174.09" y="378.75" zvalue="1359"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,210.531,390.75) scale(1,1) translate(0,0)" writing-mode="lr" x="210.53" xml:space="preserve" y="395.25" zvalue="1359">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="65" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,108.625,390.75) scale(1,1) translate(0,0)" width="72.88" x="72.19" y="378.75" zvalue="1360"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,108.625,390.75) scale(1,1) translate(0,0)" writing-mode="lr" x="108.63" xml:space="preserve" y="395.25" zvalue="1360">AVC功能</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="64" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,108.625,354.25) scale(1,1) translate(0,0)" width="72.88" x="72.19" y="342.25" zvalue="1361"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,108.625,354.25) scale(1,1) translate(0,0)" writing-mode="lr" x="108.63" xml:space="preserve" y="358.75" zvalue="1361">信号一览</text>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="27" id="8" stroke="rgb(0,0,0)" text-anchor="middle" x="534.5" xml:space="preserve" y="150" zvalue="1443">停电调电程序化操</text>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="27" id="8" stroke="rgb(0,0,0)" text-anchor="middle" writing-mode="lr" x="534.5" xml:space="preserve" y="179" zvalue="1443">作</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,746.92,323.984) scale(1,1) translate(0,0)" writing-mode="lr" x="746.92" xml:space="preserve" y="328.48" zvalue="7">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1508.96,462.659) scale(1,1) translate(0,0)" writing-mode="lr" x="1508.96" xml:space="preserve" y="467.16" zvalue="62">35kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,995.571,104.75) scale(1,1) translate(0,0)" writing-mode="lr" x="995.5700000000001" xml:space="preserve" y="109.25" zvalue="299">35kV旧油线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,986.056,200.692) scale(1,1) translate(0,0)" writing-mode="lr" x="986.0599999999999" xml:space="preserve" y="205.19" zvalue="302">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1053.72,175.136) scale(1,1) translate(0,0)" writing-mode="lr" x="1053.72" xml:space="preserve" y="179.64" zvalue="304">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="104" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,855.84,411.737) scale(1,1) translate(0,0)" writing-mode="lr" x="855.84" xml:space="preserve" y="416.24" zvalue="354">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="110" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,855.84,371.404) scale(1,1) translate(0,0)" writing-mode="lr" x="855.84" xml:space="preserve" y="375.9" zvalue="358">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="307" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,798.451,378.128) scale(1,1) translate(0,0)" writing-mode="lr" x="798.45" xml:space="preserve" y="382.63" zvalue="568">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="176" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1426.55,600.292) scale(1,1) translate(0,0)" writing-mode="lr" x="1426.55" xml:space="preserve" y="604.79" zvalue="737">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="175" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1376.91,432.62) scale(1,1) translate(0,0)" writing-mode="lr" x="1376.91" xml:space="preserve" y="437.12" zvalue="739">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="168" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1379.24,374.715) scale(1,1) translate(0,0)" writing-mode="lr" x="1379.24" xml:space="preserve" y="379.22" zvalue="741">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="159" stroke="rgb(255,255,255)" text-anchor="middle" x="1334.046875" xml:space="preserve" y="512.4445958905778" zvalue="747">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="159" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1334.046875" xml:space="preserve" y="529.4445958905778" zvalue="747">1000kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="158" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1413.81,638.53) scale(1,1) translate(0,0)" writing-mode="lr" x="1413.81" xml:space="preserve" y="643.03" zvalue="752">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="120" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,937.405,674.607) scale(1,1) translate(0,0)" writing-mode="lr" x="937.4" xml:space="preserve" y="679.11" zvalue="791">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,914.603,815.224) scale(1,1) translate(0,0)" writing-mode="lr" x="914.6" xml:space="preserve" y="819.72" zvalue="872">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,919.739,774.868) scale(1,1) translate(0,-1.01901e-12)" writing-mode="lr" x="919.74" xml:space="preserve" y="779.37" zvalue="874">071</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,898.179,935.375) scale(1,1) translate(0,0)" writing-mode="lr" x="898.1799999999999" xml:space="preserve" y="939.88" zvalue="878">10kV椿头塘线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,917.655,727.891) scale(1,1) translate(0,0)" writing-mode="lr" x="917.65" xml:space="preserve" y="732.39" zvalue="882">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,739.244,675.656) scale(1,1) translate(0,0)" writing-mode="lr" x="739.24" xml:space="preserve" y="680.16" zvalue="979">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,759.788,546.857) scale(1,1) translate(0,-1.19206e-13)" writing-mode="lr" x="759.79" xml:space="preserve" y="551.36" zvalue="983">10kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,809.668,633.036) scale(1,1) translate(0,0)" writing-mode="lr" x="809.67" xml:space="preserve" y="637.54" zvalue="1087">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1352,419.981) scale(1,1) translate(0,0)" writing-mode="lr" x="1352" xml:space="preserve" y="424.48" zvalue="1093">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1087.18,935.375) scale(1,1) translate(0,0)" writing-mode="lr" x="1087.18" xml:space="preserve" y="939.88" zvalue="1103">备用</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1236.18,935.375) scale(1,1) translate(0,0)" writing-mode="lr" x="1236.18" xml:space="preserve" y="939.88" zvalue="1118">备用</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="112" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,942.898,828.981) scale(1,1) translate(0,0)" writing-mode="lr" x="942.9" xml:space="preserve" y="833.48" zvalue="1127">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="129" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1377.6,815.224) scale(1,1) translate(0,0)" writing-mode="lr" x="1377.6" xml:space="preserve" y="819.72" zvalue="1138">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="127" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1382.74,774.868) scale(1,1) translate(0,-1.01901e-12)" writing-mode="lr" x="1382.74" xml:space="preserve" y="779.37" zvalue="1140">074</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="125" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1361.18,935.375) scale(1,1) translate(0,0)" writing-mode="lr" x="1361.18" xml:space="preserve" y="939.88" zvalue="1144">10kV油松岭线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="124" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1379.1,731.224) scale(1,1) translate(0,0)" writing-mode="lr" x="1379.1" xml:space="preserve" y="735.72" zvalue="1148">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1410.57,830.314) scale(1,1) translate(0,0)" writing-mode="lr" x="1410.57" xml:space="preserve" y="834.8099999999999" zvalue="1152">67</text>
  <line fill="none" id="190" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.00000000000068" x2="388.0000000000002" y1="152.941921185511" y2="152.941921185511" zvalue="1202"/>
  <line fill="none" id="189" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="389" x2="389" y1="11.07142857142861" y2="1041.071428571429" zvalue="1203"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="22" x2="203" y1="165.0714285714286" y2="165.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="22" x2="203" y1="191.0714285714286" y2="191.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="22" x2="22" y1="165.0714285714286" y2="191.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="203" y1="165.0714285714286" y2="191.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="384" y1="165.0714285714286" y2="165.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="384" y1="191.0714285714286" y2="191.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="203" y1="165.0714285714286" y2="191.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="384" x2="384" y1="165.0714285714286" y2="191.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="22" x2="203" y1="191.0714285714286" y2="191.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="22" x2="203" y1="215.3214285714286" y2="215.3214285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="22" x2="22" y1="191.0714285714286" y2="215.3214285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="203" y1="191.0714285714286" y2="215.3214285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="384" y1="191.0714285714286" y2="191.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="384" y1="215.3214285714286" y2="215.3214285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="203" y1="191.0714285714286" y2="215.3214285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="384" x2="384" y1="191.0714285714286" y2="215.3214285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="22" x2="203" y1="215.3214285714286" y2="215.3214285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="22" x2="203" y1="238.0714285714286" y2="238.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="22" x2="22" y1="215.3214285714286" y2="238.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="203" y1="215.3214285714286" y2="238.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="384" y1="215.3214285714286" y2="215.3214285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="384" y1="238.0714285714286" y2="238.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="203" y1="215.3214285714286" y2="238.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="384" x2="384" y1="215.3214285714286" y2="238.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="22" x2="203" y1="238.0714285714286" y2="238.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="22" x2="203" y1="260.8214285714286" y2="260.8214285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="22" x2="22" y1="238.0714285714286" y2="260.8214285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="203" y1="238.0714285714286" y2="260.8214285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="384" y1="238.0714285714286" y2="238.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="384" y1="260.8214285714286" y2="260.8214285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="203" y1="238.0714285714286" y2="260.8214285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="384" x2="384" y1="238.0714285714286" y2="260.8214285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="22" x2="203" y1="260.8214285714286" y2="260.8214285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="22" x2="203" y1="283.5714285714286" y2="283.5714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="22" x2="22" y1="260.8214285714286" y2="283.5714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="203" y1="260.8214285714286" y2="283.5714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="384" y1="260.8214285714286" y2="260.8214285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="384" y1="283.5714285714286" y2="283.5714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="203" y1="260.8214285714286" y2="283.5714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="384" x2="384" y1="260.8214285714286" y2="283.5714285714286"/>
  <line fill="none" id="182" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.00000000000068" x2="388.0000000000002" y1="622.9419211855111" y2="622.9419211855111" zvalue="1205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="73" x2="118.7745" y1="445.0714285714286" y2="445.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="73" x2="118.7745" y1="483.3537285714286" y2="483.3537285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="73" x2="73" y1="445.0714285714286" y2="483.3537285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="118.7745" y1="445.0714285714286" y2="483.3537285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="177.5809" y1="445.0714285714286" y2="445.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="177.5809" y1="483.3537285714286" y2="483.3537285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="118.7745" y1="445.0714285714286" y2="483.3537285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="177.5809" y1="445.0714285714286" y2="483.3537285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="236.3873" y1="445.0714285714286" y2="445.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="236.3873" y1="483.3537285714286" y2="483.3537285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="177.5809" y1="445.0714285714286" y2="483.3537285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3873" x2="236.3873" y1="445.0714285714286" y2="483.3537285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3872" x2="295.1936000000001" y1="445.0714285714286" y2="445.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3872" x2="295.1936000000001" y1="483.3537285714286" y2="483.3537285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3872" x2="236.3872" y1="445.0714285714286" y2="483.3537285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="295.1936000000001" y1="445.0714285714286" y2="483.3537285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="354" y1="445.0714285714286" y2="445.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="354" y1="483.3537285714286" y2="483.3537285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="295.1936000000001" y1="445.0714285714286" y2="483.3537285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="354" x2="354" y1="445.0714285714286" y2="483.3537285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="73" x2="118.7745" y1="483.3537285714286" y2="483.3537285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="73" x2="118.7745" y1="508.0331285714286" y2="508.0331285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="73" x2="73" y1="483.3537285714286" y2="508.0331285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="118.7745" y1="483.3537285714286" y2="508.0331285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="177.5809" y1="483.3537285714286" y2="483.3537285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="177.5809" y1="508.0331285714286" y2="508.0331285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="118.7745" y1="483.3537285714286" y2="508.0331285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="177.5809" y1="483.3537285714286" y2="508.0331285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="236.3873" y1="483.3537285714286" y2="483.3537285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="236.3873" y1="508.0331285714286" y2="508.0331285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="177.5809" y1="483.3537285714286" y2="508.0331285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3873" x2="236.3873" y1="483.3537285714286" y2="508.0331285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3872" x2="295.1936000000001" y1="483.3537285714286" y2="483.3537285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3872" x2="295.1936000000001" y1="508.0331285714286" y2="508.0331285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3872" x2="236.3872" y1="483.3537285714286" y2="508.0331285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="295.1936000000001" y1="483.3537285714286" y2="508.0331285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="354" y1="483.3537285714286" y2="483.3537285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="354" y1="508.0331285714286" y2="508.0331285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="295.1936000000001" y1="483.3537285714286" y2="508.0331285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="354" x2="354" y1="483.3537285714286" y2="508.0331285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="73" x2="118.7745" y1="508.0331285714286" y2="508.0331285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="73" x2="118.7745" y1="532.7125285714286" y2="532.7125285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="73" x2="73" y1="508.0331285714286" y2="532.7125285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="118.7745" y1="508.0331285714286" y2="532.7125285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="177.5809" y1="508.0331285714286" y2="508.0331285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="177.5809" y1="532.7125285714286" y2="532.7125285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="118.7745" y1="508.0331285714286" y2="532.7125285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="177.5809" y1="508.0331285714286" y2="532.7125285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="236.3873" y1="508.0331285714286" y2="508.0331285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="236.3873" y1="532.7125285714286" y2="532.7125285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="177.5809" y1="508.0331285714286" y2="532.7125285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3873" x2="236.3873" y1="508.0331285714286" y2="532.7125285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3872" x2="295.1936000000001" y1="508.0331285714286" y2="508.0331285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3872" x2="295.1936000000001" y1="532.7125285714286" y2="532.7125285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3872" x2="236.3872" y1="508.0331285714286" y2="532.7125285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="295.1936000000001" y1="508.0331285714286" y2="532.7125285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="354" y1="508.0331285714286" y2="508.0331285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="354" y1="532.7125285714286" y2="532.7125285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="295.1936000000001" y1="508.0331285714286" y2="532.7125285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="354" x2="354" y1="508.0331285714286" y2="532.7125285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="73" x2="118.7745" y1="532.7125285714286" y2="532.7125285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="73" x2="118.7745" y1="557.3919285714286" y2="557.3919285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="73" x2="73" y1="532.7125285714286" y2="557.3919285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="118.7745" y1="532.7125285714286" y2="557.3919285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="177.5809" y1="532.7125285714286" y2="532.7125285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="177.5809" y1="557.3919285714286" y2="557.3919285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="118.7745" y1="532.7125285714286" y2="557.3919285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="177.5809" y1="532.7125285714286" y2="557.3919285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="236.3873" y1="532.7125285714286" y2="532.7125285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="236.3873" y1="557.3919285714286" y2="557.3919285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="177.5809" y1="532.7125285714286" y2="557.3919285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3873" x2="236.3873" y1="532.7125285714286" y2="557.3919285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3872" x2="295.1936000000001" y1="532.7125285714286" y2="532.7125285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3872" x2="295.1936000000001" y1="557.3919285714286" y2="557.3919285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3872" x2="236.3872" y1="532.7125285714286" y2="557.3919285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="295.1936000000001" y1="532.7125285714286" y2="557.3919285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="354" y1="532.7125285714286" y2="532.7125285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="354" y1="557.3919285714286" y2="557.3919285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="295.1936000000001" y1="532.7125285714286" y2="557.3919285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="354" x2="354" y1="532.7125285714286" y2="557.3919285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="73" x2="118.7745" y1="557.3920285714286" y2="557.3920285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="73" x2="118.7745" y1="582.0714285714286" y2="582.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="73" x2="73" y1="557.3920285714286" y2="582.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="118.7745" y1="557.3920285714286" y2="582.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="177.5809" y1="557.3920285714286" y2="557.3920285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="177.5809" y1="582.0714285714286" y2="582.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="118.7745" y1="557.3920285714286" y2="582.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="177.5809" y1="557.3920285714286" y2="582.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="236.3873" y1="557.3920285714286" y2="557.3920285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="236.3873" y1="582.0714285714286" y2="582.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="177.5809" y1="557.3920285714286" y2="582.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3873" x2="236.3873" y1="557.3920285714286" y2="582.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3872" x2="295.1936000000001" y1="557.3920285714286" y2="557.3920285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3872" x2="295.1936000000001" y1="582.0714285714286" y2="582.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3872" x2="236.3872" y1="557.3920285714286" y2="582.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="295.1936000000001" y1="557.3920285714286" y2="582.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="354" y1="557.3920285714286" y2="557.3920285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="354" y1="582.0714285714286" y2="582.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="295.1936000000001" y1="557.3920285714286" y2="582.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="354" x2="354" y1="557.3920285714286" y2="582.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="73" x2="118.7745" y1="582.0714285714286" y2="582.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="73" x2="118.7745" y1="606.7508285714287" y2="606.7508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="73" x2="73" y1="582.0714285714286" y2="606.7508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="118.7745" y1="582.0714285714286" y2="606.7508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="177.5809" y1="582.0714285714286" y2="582.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="177.5809" y1="606.7508285714287" y2="606.7508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="118.7745" y1="582.0714285714286" y2="606.7508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="177.5809" y1="582.0714285714286" y2="606.7508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="236.3873" y1="582.0714285714286" y2="582.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="236.3873" y1="606.7508285714287" y2="606.7508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="177.5809" y1="582.0714285714286" y2="606.7508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3873" x2="236.3873" y1="582.0714285714286" y2="606.7508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3872" x2="295.1936000000001" y1="582.0714285714286" y2="582.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3872" x2="295.1936000000001" y1="606.7508285714287" y2="606.7508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3872" x2="236.3872" y1="582.0714285714286" y2="606.7508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="295.1936000000001" y1="582.0714285714286" y2="606.7508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="354" y1="582.0714285714286" y2="582.0714285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="354" y1="606.7508285714287" y2="606.7508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="295.1936000000001" y1="582.0714285714286" y2="606.7508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="354" x2="354" y1="582.0714285714286" y2="606.7508285714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="21" x2="111" y1="938.0714285714287" y2="938.0714285714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="21" x2="111" y1="977.2347285714287" y2="977.2347285714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="21" x2="21" y1="938.0714285714287" y2="977.2347285714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="111" x2="111" y1="938.0714285714287" y2="977.2347285714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="111" x2="381" y1="938.0714285714287" y2="938.0714285714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="111" x2="381" y1="977.2347285714287" y2="977.2347285714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="111" x2="111" y1="938.0714285714287" y2="977.2347285714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="381" x2="381" y1="938.0714285714287" y2="977.2347285714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="21" x2="111" y1="977.2346985714287" y2="977.2346985714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="21" x2="111" y1="1005.153098571429" y2="1005.153098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="21" x2="21" y1="977.2346985714287" y2="1005.153098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="111" x2="111" y1="977.2346985714287" y2="1005.153098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="111" x2="201" y1="977.2346985714287" y2="977.2346985714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="111" x2="201" y1="1005.153098571429" y2="1005.153098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="111" x2="111" y1="977.2346985714287" y2="1005.153098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="201" x2="201" y1="977.2346985714287" y2="1005.153098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="201.0000000000001" x2="291.0000000000001" y1="977.2346985714287" y2="977.2346985714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="201.0000000000001" x2="291.0000000000001" y1="1005.153098571429" y2="1005.153098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="201.0000000000001" x2="201.0000000000001" y1="977.2346985714287" y2="1005.153098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="291.0000000000001" x2="291.0000000000001" y1="977.2346985714287" y2="1005.153098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="291" x2="381" y1="977.2346985714287" y2="977.2346985714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="291" x2="381" y1="1005.153098571429" y2="1005.153098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="291" x2="291" y1="977.2346985714287" y2="1005.153098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="381" x2="381" y1="977.2346985714287" y2="1005.153098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="21" x2="111" y1="1005.153028571429" y2="1005.153028571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="21" x2="111" y1="1033.071428571429" y2="1033.071428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="21" x2="21" y1="1005.153028571429" y2="1033.071428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="111" x2="111" y1="1005.153028571429" y2="1033.071428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="111" x2="201" y1="1005.153028571429" y2="1005.153028571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="111" x2="201" y1="1033.071428571429" y2="1033.071428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="111" x2="111" y1="1005.153028571429" y2="1033.071428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="201" x2="201" y1="1005.153028571429" y2="1033.071428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="201.0000000000001" x2="291.0000000000001" y1="1005.153028571429" y2="1005.153028571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="201.0000000000001" x2="291.0000000000001" y1="1033.071428571429" y2="1033.071428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="201.0000000000001" x2="201.0000000000001" y1="1005.153028571429" y2="1033.071428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="291.0000000000001" x2="291.0000000000001" y1="1005.153028571429" y2="1033.071428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="291" x2="381" y1="1005.153028571429" y2="1005.153028571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="291" x2="381" y1="1033.071428571429" y2="1033.071428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="291" x2="291" y1="1005.153028571429" y2="1033.071428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="381" x2="381" y1="1005.153028571429" y2="1033.071428571429"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="178" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,66,959.071) scale(1,1) translate(0,0)" writing-mode="lr" x="66" xml:space="preserve" y="965.0700000000001" zvalue="1209">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="177" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,63,993.071) scale(1,1) translate(0,0)" writing-mode="lr" x="63" xml:space="preserve" y="999.0700000000001" zvalue="1210">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="174" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,244,993.071) scale(1,1) translate(0,0)" writing-mode="lr" x="244" xml:space="preserve" y="999.0700000000001" zvalue="1211">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="173" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62,1021.07) scale(1,1) translate(0,0)" writing-mode="lr" x="62" xml:space="preserve" y="1027.07" zvalue="1212">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="172" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,244,1020.07) scale(1,1) translate(0,0)" writing-mode="lr" x="244" xml:space="preserve" y="1026.07" zvalue="1213">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="169" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,145.528,463.683) scale(1,1) translate(0,0)" writing-mode="lr" x="145.5278049045141" xml:space="preserve" y="468.1825396825397" zvalue="1214">35kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="166" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.5,652.571) scale(1,1) translate(0,0)" writing-mode="lr" x="86.5" xml:space="preserve" y="657.0714285714287" zvalue="1216">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="165" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,219.399,319.913) scale(1,1) translate(0,0)" writing-mode="lr" x="219.4" xml:space="preserve" y="324.41" zvalue="1217">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="164" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,324.399,319.913) scale(1,1) translate(0,0)" writing-mode="lr" x="324.4" xml:space="preserve" y="324.41" zvalue="1218">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="163" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,264.931,463.683) scale(1,1) translate(0,0)" writing-mode="lr" x="264.9305758608716" xml:space="preserve" y="468.1825396825397" zvalue="1219">10kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="162" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,98,495.821) scale(1,1) translate(0,-1.06875e-13)" writing-mode="lr" x="98" xml:space="preserve" y="500.3214285714286" zvalue="1220">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="161" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,98,521.321) scale(1,1) translate(0,0)" writing-mode="lr" x="98" xml:space="preserve" y="525.8214285714287" zvalue="1221">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="160" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,98,544.321) scale(1,1) translate(0,5.8822e-14)" writing-mode="lr" x="98" xml:space="preserve" y="548.8214285714286" zvalue="1222">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="157" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,98,567.321) scale(1,1) translate(0,0)" writing-mode="lr" x="98" xml:space="preserve" y="571.8214285714287" zvalue="1223">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="156" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,98,594.321) scale(1,1) translate(0,0)" writing-mode="lr" x="98" xml:space="preserve" y="598.8214285714287" zvalue="1224">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="155" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,246.67,960.071) scale(1,1) translate(0,0)" writing-mode="lr" x="246.67" xml:space="preserve" y="966.0700000000001" zvalue="1225">YouSongLing-01-2021</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="154" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,336.054,992.071) scale(1,1) translate(0,0)" writing-mode="lr" x="336.05" xml:space="preserve" y="998.0700000000001" zvalue="1226">20200923</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="153" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,60,179.071) scale(1,1) translate(0,0)" writing-mode="lr" x="60" xml:space="preserve" y="183.57" zvalue="1227">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="152" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,240,179.071) scale(1,1) translate(0,0)" writing-mode="lr" x="240" xml:space="preserve" y="183.57" zvalue="1228">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="151" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,67.6875,203.321) scale(1,1) translate(0,0)" writing-mode="lr" x="67.69" xml:space="preserve" y="207.82" zvalue="1229">35kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="150" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,67.1875,251.071) scale(1,1) translate(0,0)" writing-mode="lr" x="67.19" xml:space="preserve" y="255.57" zvalue="1230">1号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="149" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,248.75,250.571) scale(1,1) translate(0,0)" writing-mode="lr" x="248.75" xml:space="preserve" y="255.07" zvalue="1231">2号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="146" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,68.6875,227.321) scale(1,1) translate(0,0)" writing-mode="lr" x="68.69" xml:space="preserve" y="231.82" zvalue="1234">10kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="144" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,247.75,227.071) scale(1,1) translate(0,0)" writing-mode="lr" x="247.75" xml:space="preserve" y="231.57" zvalue="1235">10kVⅡ母频率</text>
  
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="209" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1048.32,598.864) scale(1,1) translate(0,0)" writing-mode="lr" x="1048.32" xml:space="preserve" y="603.36" zvalue="1265">002</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="208" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,996.626,431.191) scale(1,1) translate(0,0)" writing-mode="lr" x="996.63" xml:space="preserve" y="435.69" zvalue="1267">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="207" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,998.957,377.287) scale(1,1) translate(0,0)" writing-mode="lr" x="998.96" xml:space="preserve" y="381.79" zvalue="1269">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="206" stroke="rgb(255,255,255)" text-anchor="middle" x="957.390625" xml:space="preserve" y="507.3932566048635" zvalue="1273">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="206" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="957.390625" xml:space="preserve" y="524.3932566048635" zvalue="1273">2000kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="205" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1039.74,637.101) scale(1,1) translate(0,0)" writing-mode="lr" x="1039.74" xml:space="preserve" y="641.6" zvalue="1278">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="204" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,971.714,414.552) scale(1,1) translate(0,0)" writing-mode="lr" x="971.71" xml:space="preserve" y="419.05" zvalue="1281">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="238" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,818.001,482.375) scale(1,1) translate(0,0)" writing-mode="lr" x="818" xml:space="preserve" y="486.88" zvalue="1291">35kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,985.882,306.392) scale(1,1) translate(0,0)" writing-mode="lr" x="985.88" xml:space="preserve" y="310.89" zvalue="1382">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="135" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1022.71,256.807) scale(1,1) translate(0,0)" writing-mode="lr" x="1022.71" xml:space="preserve" y="261.31" zvalue="1386">371</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="138" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1055.01,285.912) scale(1,1) translate(0,0)" writing-mode="lr" x="1055.01" xml:space="preserve" y="290.41" zvalue="1390">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="141" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1056.19,227.088) scale(1,1) translate(0,0)" writing-mode="lr" x="1056.19" xml:space="preserve" y="231.59" zvalue="1393">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="195" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1014.76,160.506) scale(1,1) translate(0,0)" writing-mode="lr" x="1014.76" xml:space="preserve" y="165.01" zvalue="1398">9</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="269" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1363.1,104) scale(1,1) translate(0,0)" writing-mode="lr" x="1363.1" xml:space="preserve" y="108.5" zvalue="1402">35kV大油线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="268" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1349.59,199.942) scale(1,1) translate(0,0)" writing-mode="lr" x="1349.59" xml:space="preserve" y="204.44" zvalue="1404">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="267" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1421.25,174.386) scale(1,1) translate(0,0)" writing-mode="lr" x="1421.25" xml:space="preserve" y="178.89" zvalue="1406">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="249" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1353.73,305.642) scale(1,1) translate(0,0)" writing-mode="lr" x="1353.73" xml:space="preserve" y="310.14" zvalue="1411">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="228" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1390.24,256.057) scale(1,1) translate(0,0)" writing-mode="lr" x="1390.24" xml:space="preserve" y="260.56" zvalue="1414">372</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="218" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1422.54,287.58) scale(1,1) translate(0,0)" writing-mode="lr" x="1422.54" xml:space="preserve" y="292.08" zvalue="1419">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="200" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1423.72,226.338) scale(1,1) translate(0,0)" writing-mode="lr" x="1423.72" xml:space="preserve" y="230.84" zvalue="1421">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="198" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1382.29,159.756) scale(1,1) translate(0,0)" writing-mode="lr" x="1382.29" xml:space="preserve" y="164.26" zvalue="1426">9</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,155,992.071) scale(1,1) translate(0,0)" writing-mode="lr" x="155" xml:space="preserve" y="998.0700000000001" zvalue="1430">段勇</text>
  <ellipse cx="974.66" cy="258.16" fill="rgb(255,0,0)" fill-opacity="1" id="219" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1449"/>
  <ellipse cx="1345.66" cy="258.16" fill="rgb(255,0,0)" fill-opacity="1" id="227" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1451"/>
 </g>
 <g id="ButtonClass">
  <g href="小电流装置20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="275" y="378.75" zvalue="1357"/></g>
  <g href="全站公用20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="72.19" y="305.75" zvalue="1358"/></g>
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="174.09" y="378.75" zvalue="1359"/></g>
  <g href="单厂站信息-20210617.svg"><rect fill-opacity="0" height="24" width="72.88" x="72.19" y="378.75" zvalue="1360"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="72.19" y="342.25" zvalue="1361"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="128">
   <path class="kv35" d="M 732.71 339.84 L 1556.98 339.84" stroke-width="6" zvalue="6"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674419343363" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674419343363"/></metadata>
  <path d="M 732.71 339.84 L 1556.98 339.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="542">
   <path class="kv10" d="M 670.57 696.75 L 1574.86 696.75" stroke-width="4" zvalue="790"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674419408899" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674419408899"/></metadata>
  <path d="M 670.57 696.75 L 1574.86 696.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="533">
   <use class="kv35" height="30" transform="rotate(180,1507.89,424.257) scale(2.03365,-2.03365) translate(-751.949,-617.37)" width="28" x="1479.418096165756" xlink:href="#EnergyConsumer:站用变DY接地_0" y="393.7521636351881" zvalue="61"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454721536003" ObjectName="35kV1号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1507.89,424.257) scale(2.03365,-2.03365) translate(-751.949,-617.37)" width="28" x="1479.418096165756" y="393.7521636351881"/></g>
  <g id="476">
   <use class="kv10" height="30" transform="rotate(0,896.929,901.5) scale(1.25,-1.25) translate(-177.886,-1618.95)" width="12" x="889.4287060935333" xlink:href="#EnergyConsumer:负荷_0" y="882.75" zvalue="876"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454721994755" ObjectName="10kV椿头塘线"/>
   <cge:TPSR_Ref TObjectID="6192454721994755"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,896.929,901.5) scale(1.25,-1.25) translate(-177.886,-1618.95)" width="12" x="889.4287060935333" y="882.75"/></g>
  <g id="72">
   <use class="kv10" height="30" transform="rotate(0,1085.93,901.5) scale(1.25,-1.25) translate(-215.686,-1618.95)" width="12" x="1078.428706093533" xlink:href="#EnergyConsumer:负荷_0" y="882.75" zvalue="1101"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454722519043" ObjectName="10kV备用1线"/>
   <cge:TPSR_Ref TObjectID="6192454722519043"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1085.93,901.5) scale(1.25,-1.25) translate(-215.686,-1618.95)" width="12" x="1078.428706093533" y="882.75"/></g>
  <g id="101">
   <use class="kv10" height="30" transform="rotate(0,1234.93,901.5) scale(1.25,-1.25) translate(-245.486,-1618.95)" width="12" x="1227.428706093533" xlink:href="#EnergyConsumer:负荷_0" y="882.75" zvalue="1116"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454722781187" ObjectName="10kV备用2线"/>
   <cge:TPSR_Ref TObjectID="6192454722781187"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1234.93,901.5) scale(1.25,-1.25) translate(-245.486,-1618.95)" width="12" x="1227.428706093533" y="882.75"/></g>
  <g id="184">
   <use class="kv10" height="30" transform="rotate(0,1359.93,901.5) scale(1.25,-1.25) translate(-270.486,-1618.95)" width="12" x="1352.428706093533" xlink:href="#EnergyConsumer:负荷_0" y="882.75" zvalue="1142"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454723567619" ObjectName="10kV油松岭线"/>
   <cge:TPSR_Ref TObjectID="6192454723567619"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1359.93,901.5) scale(1.25,-1.25) translate(-270.486,-1618.95)" width="12" x="1352.428706093533" y="882.75"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="53">
   <use class="kv35" height="30" transform="rotate(0,1507.79,366.373) scale(1,1) translate(0,0)" width="15" x="1500.285714285714" xlink:href="#Disconnector:令克_0" y="351.3733757564003" zvalue="171"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450121957381" ObjectName="35kV1号站用变隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450121957381"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1507.79,366.373) scale(1,1) translate(0,0)" width="15" x="1500.285714285714" y="351.3733757564003"/></g>
  <g id="68">
   <use class="kv35" height="30" transform="rotate(0,996.195,198.526) scale(-0.932942,0.6712) translate(-2064.5,92.3193)" width="15" x="989.197969331993" xlink:href="#Disconnector:刀闸_0" y="188.4575673790615" zvalue="301"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450121826309" ObjectName="35kV旧油线3716隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450121826309"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,996.195,198.526) scale(-0.932942,0.6712) translate(-2064.5,92.3193)" width="15" x="989.197969331993" y="188.4575673790615"/></g>
  <g id="304">
   <use class="kv35" height="30" transform="rotate(180,819.225,379.128) scale(0.947693,-0.6712) translate(44.8241,-948.91)" width="15" x="812.1174667593289" xlink:href="#Disconnector:刀闸_0" y="369.0596384992546" zvalue="567"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454721601539" ObjectName="35kV母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454721601539"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,819.225,379.128) scale(0.947693,-0.6712) translate(44.8241,-948.91)" width="15" x="812.1174667593289" y="369.0596384992546"/></g>
  <g id="220">
   <use class="kv35" height="30" transform="rotate(0,1398.28,374.408) scale(0.947693,-0.6712) translate(76.7843,-937.159)" width="15" x="1391.168203242874" xlink:href="#Disconnector:刀闸_0" y="364.3400530849659" zvalue="740"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454721732611" ObjectName="#1主变35kV侧3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454721732611"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1398.28,374.408) scale(0.947693,-0.6712) translate(76.7843,-937.159)" width="15" x="1391.168203242874" y="364.3400530849659"/></g>
  <g id="192">
   <use class="kv10" height="30" transform="rotate(180,1398.21,639.53) scale(-0.947693,0.6712) translate(-2873.98,308.353)" width="15" x="1391.098827388838" xlink:href="#Disconnector:刀闸_0" y="629.4619653011533" zvalue="750"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454721667075" ObjectName="#1主变10kV侧0011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454721667075"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1398.21,639.53) scale(-0.947693,0.6712) translate(-2873.98,308.353)" width="15" x="1391.098827388838" y="629.4619653011533"/></g>
  <g id="479">
   <use class="kv10" height="30" transform="rotate(180,896.871,814.641) scale(-0.947693,-0.6712) translate(-1843.64,-2033.28)" width="15" x="889.7630441166826" xlink:href="#Disconnector:刀闸_0" y="804.573089599609" zvalue="871"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454722125827" ObjectName="10kV椿头塘线0716隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454722125827"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,896.871,814.641) scale(-0.947693,-0.6712) translate(-1843.64,-2033.28)" width="15" x="889.7630441166826" y="804.573089599609"/></g>
  <g id="472">
   <use class="kv10" height="30" transform="rotate(180,896.741,727.308) scale(-0.947693,-0.6712) translate(-1843.37,-1815.83)" width="15" x="889.6328447786561" xlink:href="#Disconnector:刀闸_0" y="717.2397430789308" zvalue="881"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454721929219" ObjectName="10kV椿头塘线0711隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454721929219"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,896.741,727.308) scale(-0.947693,-0.6712) translate(-1843.37,-1815.83)" width="15" x="889.6328447786561" y="717.2397430789308"/></g>
  <g id="248">
   <use class="kv10" height="30" transform="rotate(180,763.274,674.963) scale(0.947693,-0.6712) translate(41.7359,-1685.5)" width="15" x="756.166458682462" xlink:href="#Disconnector:刀闸_0" y="664.8950416154146" zvalue="978"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454721863683" ObjectName="10kV母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454721863683"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,763.274,674.963) scale(0.947693,-0.6712) translate(41.7359,-1685.5)" width="15" x="756.166458682462" y="664.8950416154146"/></g>
  <g id="75">
   <use class="kv10" height="30" transform="rotate(180,1085.87,814.641) scale(-0.947693,-0.6712) translate(-2232.07,-2033.28)" width="15" x="1078.763044116683" xlink:href="#Disconnector:刀闸_0" y="804.573089599609" zvalue="1096"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454722650115" ObjectName="10kV备用1线0726隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454722650115"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1085.87,814.641) scale(-0.947693,-0.6712) translate(-2232.07,-2033.28)" width="15" x="1078.763044116683" y="804.573089599609"/></g>
  <g id="44">
   <use class="kv10" height="30" transform="rotate(180,1084.19,730.641) scale(-0.947693,-0.6712) translate(-2228.61,-1824.13)" width="15" x="1077.080942529329" xlink:href="#Disconnector:刀闸_0" y="720.5730764122643" zvalue="1106"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454722453507" ObjectName="10kV备用1线0721隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454722453507"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1084.19,730.641) scale(-0.947693,-0.6712) translate(-2228.61,-1824.13)" width="15" x="1077.080942529329" y="720.5730764122643"/></g>
  <g id="108">
   <use class="kv10" height="30" transform="rotate(180,1234.87,814.641) scale(-0.947693,-0.6712) translate(-2538.29,-2033.28)" width="15" x="1227.763044116683" xlink:href="#Disconnector:刀闸_0" y="804.573089599609" zvalue="1111"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454722912259" ObjectName="10kV备用2线0736隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454722912259"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1234.87,814.641) scale(-0.947693,-0.6712) translate(-2538.29,-2033.28)" width="15" x="1227.763044116683" y="804.573089599609"/></g>
  <g id="97">
   <use class="kv10" height="30" transform="rotate(180,1233.19,730.641) scale(-0.947693,-0.6712) translate(-2534.83,-1824.13)" width="15" x="1226.080942529329" xlink:href="#Disconnector:刀闸_0" y="720.5730764122643" zvalue="1121"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454722715651" ObjectName="10kV备用2线0731隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454722715651"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1233.19,730.641) scale(-0.947693,-0.6712) translate(-2534.83,-1824.13)" width="15" x="1226.080942529329" y="720.5730764122643"/></g>
  <g id="187">
   <use class="kv10" height="30" transform="rotate(180,1359.87,814.641) scale(-0.947693,-0.6712) translate(-2795.19,-2033.28)" width="15" x="1352.763044116683" xlink:href="#Disconnector:刀闸_0" y="804.573089599609" zvalue="1137"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454723698691" ObjectName="10kV油松岭线0746隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454723698691"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1359.87,814.641) scale(-0.947693,-0.6712) translate(-2795.19,-2033.28)" width="15" x="1352.763044116683" y="804.573089599609"/></g>
  <g id="145">
   <use class="kv10" height="30" transform="rotate(180,1358.19,730.641) scale(-0.947693,-0.6712) translate(-2791.73,-1824.13)" width="15" x="1351.080942529329" xlink:href="#Disconnector:刀闸_0" y="720.5730764122643" zvalue="1147"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454723502083" ObjectName="10kV油松岭线0741隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454723502083"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1358.19,730.641) scale(-0.947693,-0.6712) translate(-2791.73,-1824.13)" width="15" x="1351.080942529329" y="720.5730764122643"/></g>
  <g id="224">
   <use class="kv35" height="30" transform="rotate(0,1017.99,376.979) scale(0.947693,-0.6712) translate(55.7948,-943.561)" width="15" x="1010.882488957159" xlink:href="#Disconnector:刀闸_0" y="366.9114816563945" zvalue="1268"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450122285061" ObjectName="#2主变35kV侧3021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450122285061"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1017.99,376.979) scale(0.947693,-0.6712) translate(55.7948,-943.561)" width="15" x="1010.882488957159" y="366.9114816563945"/></g>
  <g id="215">
   <use class="kv10" height="30" transform="rotate(180,1020.13,638.101) scale(-0.947693,0.6712) translate(-2096.96,307.653)" width="15" x="1013.023403139529" xlink:href="#Disconnector:刀闸_0" y="628.0333938725819" zvalue="1276"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454723960835" ObjectName="#2主变10kV侧0021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454723960835"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1020.13,638.101) scale(-0.947693,0.6712) translate(-2096.96,307.653)" width="15" x="1013.023403139529" y="628.0333938725819"/></g>
  <g id="96">
   <use class="kv35" height="30" transform="rotate(0,996.056,307.392) scale(-0.932942,0.6712) translate(-2064.21,145.649)" width="15" x="989.0588235294118" xlink:href="#Disconnector:刀闸_0" y="297.3235294117648" zvalue="1381"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450122350597" ObjectName="35kV旧油线3711隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450122350597"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,996.056,307.392) scale(-0.932942,0.6712) translate(-2064.21,145.649)" width="15" x="989.0588235294118" y="297.3235294117648"/></g>
  <g id="148">
   <use class="kv35" height="30" transform="rotate(270,1016.53,153.039) scale(-0.932942,0.6712) translate(-2106.62,70.0367)" width="15" x="1009.529411764706" xlink:href="#Disconnector:刀闸_0" y="142.9705882352943" zvalue="1397"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450122743813" ObjectName="35kV旧油线3719隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450122743813"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1016.53,153.039) scale(-0.932942,0.6712) translate(-2106.62,70.0367)" width="15" x="1009.529411764706" y="142.9705882352943"/></g>
  <g id="298">
   <use class="kv35" height="30" transform="rotate(0,1363.73,197.776) scale(-0.932942,0.6712) translate(-2825.98,91.9519)" width="15" x="1356.729853233449" xlink:href="#Disconnector:刀闸_0" y="187.7075673790615" zvalue="1403"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450041085957" ObjectName="35kV大油线3726隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450041085957"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1363.73,197.776) scale(-0.932942,0.6712) translate(-2825.98,91.9519)" width="15" x="1356.729853233449" y="187.7075673790615"/></g>
  <g id="293">
   <use class="kv35" height="30" transform="rotate(0,1363.91,306.642) scale(-0.932942,0.6712) translate(-2826.35,145.282)" width="15" x="1356.910101207576" xlink:href="#Disconnector:刀闸_0" y="296.5735294117648" zvalue="1410"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450040823813" ObjectName="35kV大油线3721隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450040823813"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1363.91,306.642) scale(-0.932942,0.6712) translate(-2826.35,145.282)" width="15" x="1356.910101207576" y="296.5735294117648"/></g>
  <g id="272">
   <use class="kv35" height="30" transform="rotate(270,1384.06,152.289) scale(-0.932942,0.6712) translate(-2868.1,69.6693)" width="15" x="1377.061295666162" xlink:href="#Disconnector:刀闸_0" y="142.2205882352943" zvalue="1425"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450040430597" ObjectName="35kV大油线3729隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450040430597"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1384.06,152.289) scale(-0.932942,0.6712) translate(-2868.1,69.6693)" width="15" x="1377.061295666162" y="142.2205882352943"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="70">
   <use class="kv35" height="30" transform="rotate(0,996.113,124.782) scale(1.98323,0.522926) translate(-490.403,106.684)" width="7" x="989.1718437948283" xlink:href="#ACLineSegment:线路_0" y="116.937764596597" zvalue="297"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249330548738" ObjectName="35kV旧油线"/>
   <cge:TPSR_Ref TObjectID="8444249330548738_5066549683552257"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,996.113,124.782) scale(1.98323,0.522926) translate(-490.403,106.684)" width="7" x="989.1718437948283" y="116.937764596597"/></g>
  <g id="299">
   <use class="kv35" height="30" transform="rotate(0,1363.65,124.032) scale(1.98323,0.522926) translate(-672.615,106)" width="7" x="1356.703727696284" xlink:href="#ACLineSegment:线路_0" y="116.187764596597" zvalue="1401"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249305448454" ObjectName="35kV大油线"/>
   <cge:TPSR_Ref TObjectID="8444249305448454_5066549683552257"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1363.65,124.032) scale(1.98323,0.522926) translate(-672.615,106)" width="7" x="1356.703727696284" y="116.187764596597"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="67">
   <use class="kv35" height="20" transform="rotate(90,1030.92,175.252) scale(1.22679,-1.0068) translate(-189.447,-349.253)" width="10" x="1024.788665379807" xlink:href="#GroundDisconnector:地刀_0" y="165.1842824715292" zvalue="303"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450121760773" ObjectName="35kV旧油线37167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450121760773"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1030.92,175.252) scale(1.22679,-1.0068) translate(-189.447,-349.253)" width="10" x="1024.788665379807" y="165.1842824715292"/></g>
  <g id="103">
   <use class="kv35" height="20" transform="rotate(90,853.817,397.737) scale(1.24619,-1.0068) translate(-167.442,-792.72)" width="10" x="847.586408959255" xlink:href="#GroundDisconnector:地刀_0" y="387.6691309563777" zvalue="353"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454721273859" ObjectName="35kV母线电压互感器39017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454721273859"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,853.817,397.737) scale(1.24619,-1.0068) translate(-167.442,-792.72)" width="10" x="847.586408959255" y="387.6691309563777"/></g>
  <g id="114">
   <use class="kv35" height="20" transform="rotate(90,853.817,358.404) scale(1.24619,-1.0068) translate(-167.442,-714.319)" width="10" x="847.586408959255" xlink:href="#GroundDisconnector:地刀_0" y="348.3357976230444" zvalue="357"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454721404931" ObjectName="35kV母线电压互感器39010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454721404931"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,853.817,358.404) scale(1.24619,-1.0068) translate(-167.442,-714.319)" width="10" x="847.586408959255" y="348.3357976230444"/></g>
  <g id="25">
   <use class="kv10" height="20" transform="rotate(90,802.334,650.886) scale(1.24619,-1.0068) translate(-157.272,-1297.31)" width="10" x="796.1032469692942" xlink:href="#GroundDisconnector:地刀_0" y="640.8176891788829" zvalue="1086"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454722256899" ObjectName="10kV母线电压互感器09017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454722256899"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,802.334,650.886) scale(1.24619,-1.0068) translate(-157.272,-1297.31)" width="10" x="796.1032469692942" y="640.8176891788829"/></g>
  <g id="41">
   <use class="kv35" height="20" transform="rotate(270,1349.98,401.981) scale(-1.24619,-1.0068) translate(-2432.03,-801.179)" width="10" x="1343.746163808365" xlink:href="#GroundDisconnector:地刀_0" y="391.9129272741209" zvalue="1092"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454722387971" ObjectName="#1主变35kV侧30117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454722387971"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1349.98,401.981) scale(-1.24619,-1.0068) translate(-2432.03,-801.179)" width="10" x="1343.746163808365" y="391.9129272741209"/></g>
  <g id="111">
   <use class="kv10" height="20" transform="rotate(90,941.334,840.648) scale(-1.24619,-1.0068) translate(-1695.47,-1675.55)" width="10" x="935.1032469692944" xlink:href="#GroundDisconnector:地刀_0" y="830.5795837682617" zvalue="1126"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454723043331" ObjectName="10kV椿头塘线07167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454723043331"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,941.334,840.648) scale(-1.24619,-1.0068) translate(-1695.47,-1675.55)" width="10" x="935.1032469692944" y="830.5795837682617"/></g>
  <g id="117">
   <use class="kv10" height="20" transform="rotate(90,1129.67,840.648) scale(-1.24619,-1.0068) translate(-2034.94,-1675.55)" width="10" x="1123.436580302628" xlink:href="#GroundDisconnector:地刀_0" y="830.5795837682617" zvalue="1129"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454723174403" ObjectName="10kV备用1线07267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454723174403"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1129.67,840.648) scale(-1.24619,-1.0068) translate(-2034.94,-1675.55)" width="10" x="1123.436580302628" y="830.5795837682617"/></g>
  <g id="121">
   <use class="kv10" height="20" transform="rotate(90,1278.67,840.648) scale(-1.24619,-1.0068) translate(-2303.5,-1675.55)" width="10" x="1272.436580302628" xlink:href="#GroundDisconnector:地刀_0" y="830.5795837682617" zvalue="1133"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454723305475" ObjectName="10kV备用2线07367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454723305475"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1278.67,840.648) scale(-1.24619,-1.0068) translate(-2303.5,-1675.55)" width="10" x="1272.436580302628" y="830.5795837682617"/></g>
  <g id="131">
   <use class="kv10" height="20" transform="rotate(90,1406.33,841.981) scale(-1.24619,-1.0068) translate(-2533.61,-1678.21)" width="10" x="1400.103246969295" xlink:href="#GroundDisconnector:地刀_0" y="831.9129272741209" zvalue="1151"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454723436547" ObjectName="10kV油松岭线07467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454723436547"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1406.33,841.981) scale(-1.24619,-1.0068) translate(-2533.61,-1678.21)" width="10" x="1400.103246969295" y="831.9129272741209"/></g>
  <g id="211">
   <use class="kv35" height="20" transform="rotate(270,969.691,400.552) scale(-1.24619,-1.0068) translate(-1746.59,-798.331)" width="10" x="963.4604495226511" xlink:href="#GroundDisconnector:地刀_0" y="390.4843558455495" zvalue="1280"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450122219525" ObjectName="#2主变35kV侧30217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450122219525"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,969.691,400.552) scale(-1.24619,-1.0068) translate(-1746.59,-798.331)" width="10" x="963.4604495226511" y="390.4843558455495"/></g>
  <g id="136">
   <use class="kv35" height="20" transform="rotate(90,1034.42,285.693) scale(1.22679,-1.0068) translate(-190.094,-569.388)" width="10" x="1028.286998657385" xlink:href="#GroundDisconnector:地刀_0" y="275.6247660484976" zvalue="1389"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450122481669" ObjectName="35kV旧油线37117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450122481669"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1034.42,285.693) scale(1.22679,-1.0068) translate(-190.094,-569.388)" width="10" x="1028.286998657385" y="275.6247660484976"/></g>
  <g id="139">
   <use class="kv35" height="20" transform="rotate(90,1032.66,226.869) scale(1.22679,-1.0068) translate(-189.767,-452.138)" width="10" x="1026.522292775032" xlink:href="#GroundDisconnector:地刀_0" y="216.8012366367328" zvalue="1392"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450122612741" ObjectName="35kV旧油线37160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450122612741"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1032.66,226.869) scale(1.22679,-1.0068) translate(-189.767,-452.138)" width="10" x="1026.522292775032" y="216.8012366367328"/></g>
  <g id="297">
   <use class="kv35" height="20" transform="rotate(90,1398.45,174.502) scale(1.22679,-1.0068) translate(-257.391,-347.758)" width="10" x="1392.320549281263" xlink:href="#GroundDisconnector:地刀_0" y="164.4342824715292" zvalue="1405"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450041020421" ObjectName="35kV大油线37267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450041020421"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1398.45,174.502) scale(1.22679,-1.0068) translate(-257.391,-347.758)" width="10" x="1392.320549281263" y="164.4342824715292"/></g>
  <g id="288">
   <use class="kv35" height="20" transform="rotate(90,1401.95,287.361) scale(1.22679,-1.0068) translate(-258.037,-572.713)" width="10" x="1395.818882558841" xlink:href="#GroundDisconnector:地刀_0" y="277.2931646496471" zvalue="1417"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450040758277" ObjectName="35kV大油线37217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450040758277"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1401.95,287.361) scale(1.22679,-1.0068) translate(-258.037,-572.713)" width="10" x="1395.818882558841" y="277.2931646496471"/></g>
  <g id="286">
   <use class="kv35" height="20" transform="rotate(90,1400.19,226.119) scale(1.22679,-1.0068) translate(-257.711,-450.643)" width="10" x="1394.054176676488" xlink:href="#GroundDisconnector:地刀_0" y="216.0512366367329" zvalue="1420"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450040627205" ObjectName="35kV大油线37260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450040627205"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1400.19,226.119) scale(1.22679,-1.0068) translate(-257.711,-450.643)" width="10" x="1394.054176676488" y="216.0512366367329"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="19">
   <path class="kv35" d="M 996.11 188.79 L 996.11 132.55" stroke-width="1" zvalue="308"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="68@0" LinkObjectIDznd="70@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 996.11 188.79 L 996.11 132.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="16">
   <path class="kv35" d="M 1021.11 175.31 L 996.11 175.31" stroke-width="1" zvalue="311"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@0" LinkObjectIDznd="19" MaxPinNum="2"/>
   </metadata>
  <path d="M 1021.11 175.31 L 996.11 175.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="212">
   <path class="kv35" d="M 1398.41 419.31 L 1398.36 384.14" stroke-width="1" zvalue="743"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="234@1" LinkObjectIDznd="220@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1398.41 419.31 L 1398.36 384.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="191">
   <path class="kv10" d="M 1398.26 629.63 L 1398.26 609.25" stroke-width="1" zvalue="751"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="192@1" LinkObjectIDznd="235@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1398.26 629.63 L 1398.26 609.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="475">
   <path class="kv10" d="M 896.93 824.54 L 896.93 884.62" stroke-width="1" zvalue="877"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="479@1" LinkObjectIDznd="476@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 896.93 824.54 L 896.93 884.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="474">
   <path class="kv10" d="M 923.93 876.5 L 923.93 862.58 L 896.93 862.58" stroke-width="1" zvalue="879"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="477@0" LinkObjectIDznd="475" MaxPinNum="2"/>
   </metadata>
  <path d="M 923.93 876.5 L 923.93 862.58 L 896.93 862.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="473">
   <path class="kv10" d="M 896.95 787.07 L 896.95 804.91" stroke-width="1" zvalue="880"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="478@0" LinkObjectIDznd="479@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 896.95 787.07 L 896.95 804.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="471">
   <path class="kv10" d="M 896.8 737.2 L 896.8 761.19" stroke-width="1" zvalue="883"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="472@1" LinkObjectIDznd="478@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 896.8 737.2 L 896.8 761.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="568">
   <path class="kv10" d="M 1398.29 649.27 L 1398.29 696.75" stroke-width="1" zvalue="993"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="192@0" LinkObjectIDznd="542@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1398.29 649.27 L 1398.29 696.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="kv35" d="M 1359.79 402.04 L 1398.39 402.04" stroke-width="1" zvalue="1094"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41@0" LinkObjectIDznd="212" MaxPinNum="2"/>
   </metadata>
  <path d="M 1359.79 402.04 L 1398.39 402.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="kv10" d="M 1085.93 824.54 L 1085.93 884.62" stroke-width="1" zvalue="1102"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="75@1" LinkObjectIDznd="72@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1085.93 824.54 L 1085.93 884.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="55">
   <path class="kv10" d="M 1112.93 876.5 L 1112.93 862.58 L 1085.93 862.58" stroke-width="1" zvalue="1104"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="73@0" LinkObjectIDznd="57" MaxPinNum="2"/>
   </metadata>
  <path d="M 1112.93 876.5 L 1112.93 862.58 L 1085.93 862.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="kv10" d="M 1084.7 787.07 L 1084.7 804.91" stroke-width="1" zvalue="1105"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="74@0" LinkObjectIDznd="75@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1084.7 787.07 L 1084.7 804.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="38">
   <path class="kv10" d="M 1084.25 740.54 L 1084.25 761.19" stroke-width="1" zvalue="1108"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44@1" LinkObjectIDznd="74@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1084.25 740.54 L 1084.25 761.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="100">
   <path class="kv10" d="M 1234.93 824.54 L 1234.93 884.62" stroke-width="1" zvalue="1117"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@1" LinkObjectIDznd="101@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1234.93 824.54 L 1234.93 884.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="99">
   <path class="kv10" d="M 1261.93 876.5 L 1261.93 862.58 L 1234.93 862.58" stroke-width="1" zvalue="1119"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@0" LinkObjectIDznd="100" MaxPinNum="2"/>
   </metadata>
  <path d="M 1261.93 876.5 L 1261.93 862.58 L 1234.93 862.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="kv10" d="M 1234.95 787.07 L 1234.95 804.91" stroke-width="1" zvalue="1120"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@0" LinkObjectIDznd="108@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1234.95 787.07 L 1234.95 804.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="94">
   <path class="kv10" d="M 1233.25 740.54 L 1233.25 761.19" stroke-width="1" zvalue="1123"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="97@1" LinkObjectIDznd="107@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1233.25 740.54 L 1233.25 761.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="kv10" d="M 1233.27 720.91 L 1233.27 696.75" stroke-width="1" zvalue="1124"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="97@0" LinkObjectIDznd="542@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1233.27 720.91 L 1233.27 696.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="183">
   <path class="kv10" d="M 1359.93 824.54 L 1359.93 884.62" stroke-width="1" zvalue="1143"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="187@1" LinkObjectIDznd="184@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1359.93 824.54 L 1359.93 884.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="171">
   <path class="kv10" d="M 1386.93 876.5 L 1386.93 862.58 L 1359.93 862.58" stroke-width="1" zvalue="1145"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="185@0" LinkObjectIDznd="183" MaxPinNum="2"/>
   </metadata>
  <path d="M 1386.93 876.5 L 1386.93 862.58 L 1359.93 862.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="170">
   <path class="kv10" d="M 1359.95 787.07 L 1359.95 804.91" stroke-width="1" zvalue="1146"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186@0" LinkObjectIDznd="187@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1359.95 787.07 L 1359.95 804.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="133">
   <path class="kv10" d="M 1358.25 740.54 L 1358.25 761.19" stroke-width="1" zvalue="1149"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@1" LinkObjectIDznd="186@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1358.25 740.54 L 1358.25 761.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="132">
   <path class="kv10" d="M 1358.27 720.91 L 1358.27 696.75" stroke-width="1" zvalue="1150"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@0" LinkObjectIDznd="542@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1358.27 720.91 L 1358.27 696.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="223">
   <path class="kv35" d="M 1018.05 367.08 L 1018.05 339.84" stroke-width="1" zvalue="1270"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="224@1" LinkObjectIDznd="128@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1018.05 367.08 L 1018.05 339.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="222">
   <path class="kv35" d="M 1018.13 417.88 L 1018.07 386.71" stroke-width="1" zvalue="1271"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="225@1" LinkObjectIDznd="224@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1018.13 417.88 L 1018.07 386.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="217">
   <path class="kv35" d="M 1020.23 477.19 L 1020.23 443.77" stroke-width="1" zvalue="1274"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="221@0" LinkObjectIDznd="225@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1020.23 477.19 L 1020.23 443.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="216">
   <path class="kv10" d="M 1019.88 581.93 L 1019.88 552.29" stroke-width="1" zvalue="1275"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="226@1" LinkObjectIDznd="221@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1019.88 581.93 L 1019.88 552.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="214">
   <path class="kv10" d="M 1020.19 628.21 L 1020.19 607.82" stroke-width="1" zvalue="1277"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="215@1" LinkObjectIDznd="226@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1020.19 628.21 L 1020.19 607.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="213">
   <path class="kv10" d="M 1020.21 647.84 L 1020.21 696.75" stroke-width="1" zvalue="1279"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="215@0" LinkObjectIDznd="542@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1020.21 647.84 L 1020.21 696.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="210">
   <path class="kv35" d="M 979.51 400.61 L 1018.1 400.61" stroke-width="1" zvalue="1282"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="211@0" LinkObjectIDznd="222" MaxPinNum="2"/>
   </metadata>
  <path d="M 979.51 400.61 L 1018.1 400.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="236">
   <path class="kv10" d="M 1084.27 720.91 L 1084.27 696.75" stroke-width="1" zvalue="1289"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44@0" LinkObjectIDznd="542@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1084.27 720.91 L 1084.27 696.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="4">
   <path class="kv10" d="M 1398.11 583.36 L 1398.11 552.27" stroke-width="1" zvalue="1314"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="235@1" LinkObjectIDznd="202@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1398.11 583.36 L 1398.11 552.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="8">
   <path class="kv35" d="M 1398.57 474.69 L 1398.57 445.2" stroke-width="1" zvalue="1318"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="202@0" LinkObjectIDznd="234@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1398.57 474.69 L 1398.57 445.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="2">
   <path class="kv10" d="M 1396.52 841.92 L 1359.93 841.92" stroke-width="1" zvalue="1362"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="131@0" LinkObjectIDznd="183" MaxPinNum="2"/>
   </metadata>
  <path d="M 1396.52 841.92 L 1359.93 841.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="7">
   <path class="kv10" d="M 1268.85 840.59 L 1234.93 840.59" stroke-width="1" zvalue="1363"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="121@0" LinkObjectIDznd="100" MaxPinNum="2"/>
   </metadata>
  <path d="M 1268.85 840.59 L 1234.93 840.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="kv10" d="M 1119.85 840.59 L 1085.93 840.59" stroke-width="1" zvalue="1364"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="117@0" LinkObjectIDznd="57" MaxPinNum="2"/>
   </metadata>
  <path d="M 1119.85 840.59 L 1085.93 840.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="kv10" d="M 931.52 840.59 L 896.93 840.59" stroke-width="1" zvalue="1365"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="111@0" LinkObjectIDznd="475" MaxPinNum="2"/>
   </metadata>
  <path d="M 931.52 840.59 L 896.93 840.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="kv10" d="M 762.78 617.33 L 762.78 665.23" stroke-width="1" zvalue="1367"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="245@0" LinkObjectIDznd="248@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 762.78 617.33 L 762.78 665.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv10" d="M 763.22 684.86 L 763.22 696.75" stroke-width="1" zvalue="1368"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="248@1" LinkObjectIDznd="542@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 763.22 684.86 L 763.22 696.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="kv10" d="M 792.52 650.95 L 762.78 650.95" stroke-width="1" zvalue="1369"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="25@0" LinkObjectIDznd="76" MaxPinNum="2"/>
   </metadata>
  <path d="M 792.52 650.95 L 762.78 650.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="81">
   <path class="kv35" d="M 1398.33 364.51 L 1398.33 339.84" stroke-width="1" zvalue="1370"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="220@1" LinkObjectIDznd="128@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1398.33 364.51 L 1398.33 339.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="82">
   <path class="kv10" d="M 896.82 717.57 L 896.82 696.75" stroke-width="1" zvalue="1371"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="472@0" LinkObjectIDznd="542@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 896.82 717.57 L 896.82 696.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="84">
   <path class="kv35" d="M 819.81 417.93 L 819.81 389.02" stroke-width="1" zvalue="1373"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="237@0" LinkObjectIDznd="304@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 819.81 417.93 L 819.81 389.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="kv35" d="M 819.14 369.39 L 819.14 339.84" stroke-width="1" zvalue="1374"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="304@0" LinkObjectIDznd="128@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 819.14 369.39 L 819.14 339.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="86">
   <path class="kv35" d="M 844 358.47 L 819.14 358.47" stroke-width="1" zvalue="1375"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="85" MaxPinNum="2"/>
   </metadata>
  <path d="M 844 358.47 L 819.14 358.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="kv35" d="M 844 397.8 L 819.81 397.8" stroke-width="1" zvalue="1376"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="103@0" LinkObjectIDznd="84" MaxPinNum="2"/>
   </metadata>
  <path d="M 844 397.8 L 819.81 397.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="kv35" d="M 1507.7 394.97 L 1507.7 378.62" stroke-width="1" zvalue="1378"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="533@0" LinkObjectIDznd="53@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1507.7 394.97 L 1507.7 378.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="kv35" d="M 1507.87 353.12 L 1507.87 339.84" stroke-width="1" zvalue="1379"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@0" LinkObjectIDznd="128@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1507.87 353.12 L 1507.87 339.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="105">
   <path class="kv35" d="M 996 339.84 L 996 317.29" stroke-width="1" zvalue="1382"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="128@4" LinkObjectIDznd="96@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 996 339.84 L 996 317.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="126">
   <path class="kv35" d="M 995.97 297.66 L 995.97 270.76" stroke-width="1" zvalue="1386"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="96@0" LinkObjectIDznd="119@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 995.97 297.66 L 995.97 270.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="134">
   <path class="kv35" d="M 996.14 244.87 L 996.14 208.42" stroke-width="1" zvalue="1387"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="119@1" LinkObjectIDznd="68@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 996.14 244.87 L 996.14 208.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="137">
   <path class="kv35" d="M 1024.6 285.75 L 995.97 285.75" stroke-width="1" zvalue="1390"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="136@0" LinkObjectIDznd="126" MaxPinNum="2"/>
   </metadata>
  <path d="M 1024.6 285.75 L 995.97 285.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="140">
   <path class="kv35" d="M 1022.84 226.93 L 996.14 226.93" stroke-width="1" zvalue="1393"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="139@0" LinkObjectIDznd="134" MaxPinNum="2"/>
   </metadata>
  <path d="M 1022.84 226.93 L 996.14 226.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="142">
   <path class="kv35" d="M 961.47 159.54 L 961.47 165.44 L 996.11 165.44" stroke-width="1" zvalue="1394"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="201@0" LinkObjectIDznd="19" MaxPinNum="2"/>
   </metadata>
  <path d="M 961.47 159.54 L 961.47 165.44 L 996.11 165.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="181">
   <path class="kv35" d="M 1006.79 153.12 L 996.11 153.12" stroke-width="1" zvalue="1398"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="148@0" LinkObjectIDznd="19" MaxPinNum="2"/>
   </metadata>
  <path d="M 1006.79 153.12 L 996.11 153.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="193">
   <path class="kv35" d="M 1026.42 153.1 L 1037.88 153.1 L 1037.88 145.44" stroke-width="1" zvalue="1399"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="148@1" LinkObjectIDznd="143@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1026.42 153.1 L 1037.88 153.1 L 1037.88 145.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="296">
   <path class="kv35" d="M 1363.65 188.04 L 1363.65 131.8" stroke-width="1" zvalue="1407"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="298@0" LinkObjectIDznd="299@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1363.65 188.04 L 1363.65 131.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="295">
   <path class="kv35" d="M 1388.64 174.56 L 1363.65 174.56" stroke-width="1" zvalue="1408"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="297@0" LinkObjectIDznd="296" MaxPinNum="2"/>
   </metadata>
  <path d="M 1388.64 174.56 L 1363.65 174.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="292">
   <path class="kv35" d="M 1363.85 339.84 L 1363.85 316.54" stroke-width="1" zvalue="1412"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="128@5" LinkObjectIDznd="293@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1363.85 339.84 L 1363.85 316.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="290">
   <path class="kv35" d="M 1363.83 296.91 L 1363.83 270.01" stroke-width="1" zvalue="1415"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="293@0" LinkObjectIDznd="291@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1363.83 296.91 L 1363.83 270.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="289">
   <path class="kv35" d="M 1363.67 244.12 L 1363.67 207.67" stroke-width="1" zvalue="1416"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="291@1" LinkObjectIDznd="298@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1363.67 244.12 L 1363.67 207.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="287">
   <path class="kv35" d="M 1392.14 287.42 L 1363.83 287.42" stroke-width="1" zvalue="1418"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="288@0" LinkObjectIDznd="290" MaxPinNum="2"/>
   </metadata>
  <path d="M 1392.14 287.42 L 1363.83 287.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="285">
   <path class="kv35" d="M 1390.37 226.18 L 1363.67 226.18" stroke-width="1" zvalue="1422"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="286@0" LinkObjectIDznd="289" MaxPinNum="2"/>
   </metadata>
  <path d="M 1390.37 226.18 L 1363.67 226.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="284">
   <path class="kv35" d="M 1329 158.79 L 1329 164.69 L 1363.65 164.69" stroke-width="1" zvalue="1423"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="294@0" LinkObjectIDznd="296" MaxPinNum="2"/>
   </metadata>
  <path d="M 1329 158.79 L 1329 164.69 L 1363.65 164.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="271">
   <path class="kv35" d="M 1374.32 152.37 L 1363.65 152.37" stroke-width="1" zvalue="1427"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@0" LinkObjectIDznd="296" MaxPinNum="2"/>
   </metadata>
  <path d="M 1374.32 152.37 L 1363.65 152.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="270">
   <path class="kv35" d="M 1393.95 152.35 L 1405.41 152.35 L 1405.41 144.69" stroke-width="1" zvalue="1428"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@1" LinkObjectIDznd="283@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1393.95 152.35 L 1405.41 152.35 L 1405.41 144.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="235">
   <use class="kv10" height="20" transform="rotate(180,1398.21,596.292) scale(1.5542,1.35421) translate(-495.806,-152.425)" width="10" x="1390.441685923746" xlink:href="#Breaker:开关_0" y="582.75" zvalue="736"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925207588867" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473925207588867"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1398.21,596.292) scale(1.5542,1.35421) translate(-495.806,-152.425)" width="10" x="1390.441685923746" y="582.75"/></g>
  <g id="234">
   <use class="kv35" height="20" transform="rotate(180,1398.52,432.245) scale(1.5542,1.35421) translate(-495.915,-109.517)" width="10" x="1390.745282151472" xlink:href="#Breaker:开关_0" y="418.7024104979477" zvalue="738"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925207523331" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473925207523331"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1398.52,432.245) scale(1.5542,1.35421) translate(-495.915,-109.517)" width="10" x="1390.745282151472" y="418.7024104979477"/></g>
  <g id="478">
   <use class="kv10" height="20" transform="rotate(180,896.902,774.118) scale(1.5542,1.35421) translate(-317.048,-198.938)" width="10" x="889.1311232494973" xlink:href="#Breaker:开关_0" y="760.5758941321849" zvalue="873"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925207654403" ObjectName="10kV椿头塘线071断路器"/>
   <cge:TPSR_Ref TObjectID="6473925207654403"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,896.902,774.118) scale(1.5542,1.35421) translate(-317.048,-198.938)" width="10" x="889.1311232494973" y="760.5758941321849"/></g>
  <g id="74">
   <use class="kv10" height="20" transform="rotate(180,1084.65,774.118) scale(1.5542,1.35421) translate(-383.996,-198.938)" width="10" x="1076.881123249497" xlink:href="#Breaker:开关_0" y="760.5758941321849" zvalue="1098"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925207719939" ObjectName="10kV备用1线072断路器"/>
   <cge:TPSR_Ref TObjectID="6473925207719939"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1084.65,774.118) scale(1.5542,1.35421) translate(-383.996,-198.938)" width="10" x="1076.881123249497" y="760.5758941321849"/></g>
  <g id="107">
   <use class="kv10" height="20" transform="rotate(180,1234.9,774.118) scale(1.5542,1.35421) translate(-437.573,-198.938)" width="10" x="1227.131123249497" xlink:href="#Breaker:开关_0" y="760.5758941321849" zvalue="1113"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925207785475" ObjectName="10kV备用2线073断路器"/>
   <cge:TPSR_Ref TObjectID="6473925207785475"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1234.9,774.118) scale(1.5542,1.35421) translate(-437.573,-198.938)" width="10" x="1227.131123249497" y="760.5758941321849"/></g>
  <g id="186">
   <use class="kv10" height="20" transform="rotate(180,1359.9,774.118) scale(1.5542,1.35421) translate(-482.145,-198.938)" width="10" x="1352.131123249497" xlink:href="#Breaker:开关_0" y="760.5758941321849" zvalue="1139"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925207851011" ObjectName="10kV油松岭线074断路器"/>
   <cge:TPSR_Ref TObjectID="6473925207851011"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1359.9,774.118) scale(1.5542,1.35421) translate(-482.145,-198.938)" width="10" x="1352.131123249497" y="760.5758941321849"/></g>
  <g id="226">
   <use class="kv10" height="20" transform="rotate(180,1019.98,594.864) scale(1.5542,1.35421) translate(-360.936,-152.052)" width="10" x="1012.211629982108" xlink:href="#Breaker:开关_0" y="581.3214285714286" zvalue="1264"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925207982083" ObjectName="#2主变10kV侧002断路器"/>
   <cge:TPSR_Ref TObjectID="6473925207982083"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1019.98,594.864) scale(1.5542,1.35421) translate(-360.936,-152.052)" width="10" x="1012.211629982108" y="581.3214285714286"/></g>
  <g id="225">
   <use class="kv35" height="20" transform="rotate(180,1018.23,430.816) scale(1.5542,1.35421) translate(-360.312,-109.143)" width="10" x="1010.459567865758" xlink:href="#Breaker:开关_0" y="417.2738390693762" zvalue="1266"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925207916547" ObjectName="#2主变35kV侧302断路器"/>
   <cge:TPSR_Ref TObjectID="6473925207916547"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1018.23,430.816) scale(1.5542,1.35421) translate(-360.312,-109.143)" width="10" x="1010.459567865758" y="417.2738390693762"/></g>
  <g id="119">
   <use class="kv35" height="20" transform="rotate(180,996.242,257.807) scale(1.5542,1.35421) translate(-352.471,-63.8905)" width="10" x="988.4705882352941" xlink:href="#Breaker:开关_0" y="244.264705882353" zvalue="1385"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924570775556" ObjectName="35kV旧油线371断路器"/>
   <cge:TPSR_Ref TObjectID="6473924570775556"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,996.242,257.807) scale(1.5542,1.35421) translate(-352.471,-63.8905)" width="10" x="988.4705882352941" y="244.264705882353"/></g>
  <g id="291">
   <use class="kv35" height="20" transform="rotate(180,1363.77,257.057) scale(1.5542,1.35421) translate(-483.526,-63.6943)" width="10" x="1356.00247213675" xlink:href="#Breaker:开关_0" y="243.514705882353" zvalue="1413"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924560224260" ObjectName="35kV大油线372断路器"/>
   <cge:TPSR_Ref TObjectID="6473924560224260"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1363.77,257.057) scale(1.5542,1.35421) translate(-483.526,-63.6943)" width="10" x="1356.00247213675" y="243.514705882353"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="202">
   <g id="2020">
    <use class="kv35" height="60" transform="rotate(0,1398.51,513.405) scale(1.36955,1.3141) translate(-369.972,-113.293)" width="40" x="1371.12" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="473.98" zvalue="746"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874589470722" ObjectName="35"/>
    </metadata>
   </g>
   <g id="2021">
    <use class="kv10" height="60" transform="rotate(0,1398.51,513.405) scale(1.36955,1.3141) translate(-369.972,-113.293)" width="40" x="1371.12" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="473.98" zvalue="746"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874589536258" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399532019714" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399532019714"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1398.51,513.405) scale(1.36955,1.3141) translate(-369.972,-113.293)" width="40" x="1371.12" y="473.98"/></g>
  <g id="221">
   <g id="2210">
    <use class="kv35" height="60" transform="rotate(0,1020.18,514.664) scale(1.39111,1.27213) translate(-278.998,-101.931)" width="40" x="992.36" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="476.5" zvalue="1272"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874588946434" ObjectName="35"/>
    </metadata>
   </g>
   <g id="2211">
    <use class="kv10" height="60" transform="rotate(0,1020.18,514.664) scale(1.39111,1.27213) translate(-278.998,-101.931)" width="40" x="992.36" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="476.5" zvalue="1272"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874589011970" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399531757570" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399531757570"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1020.18,514.664) scale(1.39111,1.27213) translate(-278.998,-101.931)" width="40" x="992.36" y="476.5"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="477">
   <use class="kv10" height="26" transform="rotate(0,923.959,887.969) scale(-0.838049,0.927421) translate(-2027.44,68.548)" width="12" x="918.9303009724933" xlink:href="#Accessory:避雷器1_0" y="875.9126333680678" zvalue="875"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454722060291" ObjectName="10kV椿头塘线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,923.959,887.969) scale(-0.838049,0.927421) translate(-2027.44,68.548)" width="12" x="918.9303009724933" y="875.9126333680678"/></g>
  <g id="245">
   <use class="kv10" height="18" transform="rotate(180,765.865,588.875) scale(-3.31977,3.40278) translate(-979.165,-394.193)" width="15" x="740.9668073532737" xlink:href="#Accessory:PT8_0" y="558.25" zvalue="982"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454721798147" ObjectName="10kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(180,765.865,588.875) scale(-3.31977,3.40278) translate(-979.165,-394.193)" width="15" x="740.9668073532737" y="558.25"/></g>
  <g id="73">
   <use class="kv10" height="26" transform="rotate(0,1112.96,887.969) scale(-0.838049,0.927421) translate(-2441.97,68.548)" width="12" x="1107.930300972493" xlink:href="#Accessory:避雷器1_0" y="875.9126333680678" zvalue="1100"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454722584579" ObjectName="10kV备用1线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1112.96,887.969) scale(-0.838049,0.927421) translate(-2441.97,68.548)" width="12" x="1107.930300972493" y="875.9126333680678"/></g>
  <g id="102">
   <use class="kv10" height="26" transform="rotate(0,1261.96,887.969) scale(-0.838049,0.927421) translate(-2768.76,68.548)" width="12" x="1256.930300972494" xlink:href="#Accessory:避雷器1_0" y="875.9126333680678" zvalue="1115"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454722846723" ObjectName="10kV备用2线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1261.96,887.969) scale(-0.838049,0.927421) translate(-2768.76,68.548)" width="12" x="1256.930300972494" y="875.9126333680678"/></g>
  <g id="185">
   <use class="kv10" height="26" transform="rotate(0,1386.96,887.969) scale(-0.838049,0.927421) translate(-3042.92,68.548)" width="12" x="1381.930300972494" xlink:href="#Accessory:避雷器1_0" y="875.9126333680678" zvalue="1141"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454723633155" ObjectName="10kV油松岭线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1386.96,887.969) scale(-0.838049,0.927421) translate(-3042.92,68.548)" width="12" x="1381.930300972494" y="875.9126333680678"/></g>
  <g id="201">
   <use class="kv35" height="26" transform="rotate(180,961.496,148.07) scale(0.838049,0.927421) translate(184.836,10.6443)" width="12" x="956.468116098544" xlink:href="#Accessory:避雷器1_0" y="136.0134737042025" zvalue="1261"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450039906309" ObjectName="35kV旧油线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,961.496,148.07) scale(0.838049,0.927421) translate(184.836,10.6443)" width="12" x="956.468116098544" y="136.0134737042025"/></g>
  <g id="237">
   <use class="kv35" height="30" transform="rotate(0,821.834,442.083) scale(1.7875,1.73333) translate(-350.254,-176.035)" width="30" x="795.0219779551512" xlink:href="#Accessory:避雷器PT带熔断器_0" y="416.0833333333334" zvalue="1290"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454724091907" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,821.834,442.083) scale(1.7875,1.73333) translate(-350.254,-176.035)" width="30" x="795.0219779551512" y="416.0833333333334"/></g>
  <g id="143">
   <use class="kv35" height="40" transform="rotate(0,1037.88,133.088) scale(0.647059,-0.647059) translate(562.588,-345.829)" width="20" x="1031.411764705882" xlink:href="#Accessory:线路PT带避雷器0904_0" y="120.1470588235294" zvalue="1395"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450122678277" ObjectName="35kV旧油线线路PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1037.88,133.088) scale(0.647059,-0.647059) translate(562.588,-345.829)" width="20" x="1031.411764705882" y="120.1470588235294"/></g>
  <g id="294">
   <use class="kv35" height="26" transform="rotate(180,1329.03,147.32) scale(0.838049,0.927421) translate(255.861,10.5856)" width="12" x="1324" xlink:href="#Accessory:避雷器1_0" y="135.2634737042025" zvalue="1409"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450040889349" ObjectName="35kV大油线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,1329.03,147.32) scale(0.838049,0.927421) translate(255.861,10.5856)" width="12" x="1324" y="135.2634737042025"/></g>
  <g id="283">
   <use class="kv35" height="40" transform="rotate(0,1405.41,132.338) scale(0.647059,-0.647059) translate(763.06,-343.92)" width="20" x="1398.943648607338" xlink:href="#Accessory:线路PT带避雷器0904_0" y="119.3970588235294" zvalue="1424"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450040496133" ObjectName="35kV大油线线路PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1405.41,132.338) scale(0.647059,-0.647059) translate(763.06,-343.92)" width="20" x="1398.943648607338" y="119.3970588235294"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="282">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="282" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,146.639,521.321) scale(1,1) translate(1.34645e-14,0)" writing-mode="lr" x="146.75" xml:space="preserve" y="526.09" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136147595266" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="281">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="281" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,146.639,544.321) scale(1,1) translate(1.34645e-14,5.96547e-14)" writing-mode="lr" x="146.75" xml:space="preserve" y="549.09" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136147660802" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="280">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="280" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,146.639,567.321) scale(1,1) translate(1.34645e-14,-1.24416e-13)" writing-mode="lr" x="146.75" xml:space="preserve" y="572.09" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136147726338" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="279">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="279" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,146.639,495.821) scale(1,1) translate(1.34645e-14,1.0854e-13)" writing-mode="lr" x="146.75" xml:space="preserve" y="500.59" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136147857410" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="278">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="278" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,151,203.183) scale(1,1) translate(0,0)" writing-mode="lr" x="151.15" xml:space="preserve" y="209.46" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136147988482" ObjectName="F"/>
   </metadata>
  </g>
  <g id="277">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="277" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,266.042,521.321) scale(1,1) translate(2.67209e-14,0)" writing-mode="lr" x="266.15" xml:space="preserve" y="526.09" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136154279938" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="276">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="276" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,266.042,544.321) scale(1,1) translate(2.67209e-14,-1.19309e-13)" writing-mode="lr" x="266.15" xml:space="preserve" y="549.09" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136154345474" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="275">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="275" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,266.042,567.321) scale(1,1) translate(2.67209e-14,-1.24416e-13)" writing-mode="lr" x="266.15" xml:space="preserve" y="572.09" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136154411010" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="274">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="274" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,266.042,495.821) scale(1,1) translate(2.67209e-14,1.0854e-13)" writing-mode="lr" x="266.15" xml:space="preserve" y="500.59" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136154542082" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="273">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="273" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,149.75,226.821) scale(1,1) translate(0,0)" writing-mode="lr" x="149.9" xml:space="preserve" y="233.09" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136154673154" ObjectName="F"/>
   </metadata>
  </g>
  <g id="250">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="250" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,151,250.266) scale(1,1) translate(0,0)" writing-mode="lr" x="151.15" xml:space="preserve" y="256.54" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136201269250" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="48">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,328,249.266) scale(1,1) translate(0,0)" writing-mode="lr" x="328.15" xml:space="preserve" y="255.54" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136159588354" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="246">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="246" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,146.639,594.321) scale(1,1) translate(1.34645e-14,4.5644e-13)" writing-mode="lr" x="146.75" xml:space="preserve" y="599.09" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136148054018" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="18">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="18" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,266.042,594.321) scale(1,1) translate(2.67209e-14,4.5644e-13)" writing-mode="lr" x="266.15" xml:space="preserve" y="599.09" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136154738690" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="241">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="241" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,151,178.571) scale(1,1) translate(0,0)" writing-mode="lr" x="151.15" xml:space="preserve" y="184.9" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136162209794" ObjectName=""/>
   </metadata>
  </g>
  <g id="229">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="229" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,328,178.571) scale(1,1) translate(0,0)" writing-mode="lr" x="328.15" xml:space="preserve" y="184.92" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136162275330" ObjectName=""/>
   </metadata>
  </g>
  <g id="239">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="239" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,992.113,32.4378) scale(1,1) translate(0,0)" writing-mode="lr" x="992.3099999999999" xml:space="preserve" y="37.35" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136148119554" ObjectName="P"/>
   </metadata>
  </g>
  <g id="240">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="240" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,992.113,55.4378) scale(1,1) translate(0,0)" writing-mode="lr" x="992.3099999999999" xml:space="preserve" y="60.35" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136148185090" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="242">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="242" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,992.113,78.4378) scale(1,1) translate(0,0)" writing-mode="lr" x="992.3099999999999" xml:space="preserve" y="83.34999999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136148250626" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="243">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="243" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,896.929,965) scale(1,1) translate(0,0)" writing-mode="lr" x="897.12" xml:space="preserve" y="969.91" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136152838146" ObjectName="P"/>
   </metadata>
  </g>
  <g id="244">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="244" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1358.68,966.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1358.87" xml:space="preserve" y="971.16" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136157687810" ObjectName="P"/>
   </metadata>
  </g>
  <g id="251">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="251" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,898.179,988) scale(1,1) translate(0,0)" writing-mode="lr" x="898.37" xml:space="preserve" y="992.91" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136152903682" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="252">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="252" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1358.68,989.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1358.87" xml:space="preserve" y="994.16" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136157753346" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="253">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="253" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,898.179,1011) scale(1,1) translate(0,0)" writing-mode="lr" x="898.37" xml:space="preserve" y="1015.91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136152969218" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="254">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="254" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1359.93,1011) scale(1,1) translate(0,0)" writing-mode="lr" x="1360.12" xml:space="preserve" y="1015.91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136157818882" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="255">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="255" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1305.15,399.29) scale(1,1) translate(0,-2.57654e-13)" writing-mode="lr" x="1305.39" xml:space="preserve" y="404.28" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136200810498" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="256">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="256" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1093.19,387.533) scale(1,1) translate(0,0)" writing-mode="lr" x="1093.43" xml:space="preserve" y="392.52" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136159129602" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="257">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="257" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1303.15,421.29) scale(1,1) translate(0,0)" writing-mode="lr" x="1303.39" xml:space="preserve" y="426.28" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136200876034" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="258">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="258" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1093.19,414.533) scale(1,1) translate(0,0)" writing-mode="lr" x="1093.43" xml:space="preserve" y="419.52" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136159195138" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="259">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="259" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1304.15,444.29) scale(1,1) translate(0,0)" writing-mode="lr" x="1304.39" xml:space="preserve" y="449.28" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136201072642" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="260">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="260" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1093.19,441.533) scale(1,1) translate(0,0)" writing-mode="lr" x="1093.43" xml:space="preserve" y="446.52" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136159391746" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="261">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="261" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1124.19,580.276) scale(1,1) translate(0,6.30358e-14)" writing-mode="lr" x="1124.43" xml:space="preserve" y="585.26" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136159260674" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="262">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="262" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1124.19,606.276) scale(1,1) translate(0,0)" writing-mode="lr" x="1124.43" xml:space="preserve" y="611.26" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136159326210" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="263">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="263" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1124.19,633.276) scale(1,1) translate(0,0)" writing-mode="lr" x="1124.43" xml:space="preserve" y="638.26" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136159719426" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="264">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="264" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1332.15,588.032) scale(1,1) translate(0,0)" writing-mode="lr" x="1332.39" xml:space="preserve" y="593.02" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136200941570" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="265">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="265" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1332.15,615.032) scale(1,1) translate(0,0)" writing-mode="lr" x="1332.39" xml:space="preserve" y="620.02" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136201007106" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="266">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="266" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1332.15,642.032) scale(1,1) translate(0,-4.19353e-13)" writing-mode="lr" x="1332.39" xml:space="preserve" y="647.02" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136201400322" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="109">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="109" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1363.65,32.6878) scale(1,1) translate(0,0)" writing-mode="lr" x="1363.18" xml:space="preserve" y="37.47" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126756155396" ObjectName="P"/>
   </metadata>
  </g>
  <g id="113">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="113" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1363.65,53.6878) scale(1,1) translate(0,0)" writing-mode="lr" x="1363.18" xml:space="preserve" y="58.47" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126756220932" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="115">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="115" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1363.65,78.6878) scale(1,1) translate(0,0)" writing-mode="lr" x="1363.18" xml:space="preserve" y="83.47" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126756286468" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="130">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="130" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,818.361,501.5) scale(1,1) translate(8.80407e-14,-1.09801e-13)" writing-mode="lr" x="818.47" xml:space="preserve" y="506.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136147857410" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="203">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="203" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,758.361,528.5) scale(1,1) translate(8.13793e-14,0)" writing-mode="lr" x="758.47" xml:space="preserve" y="533.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136154542082" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="897">
   <use height="30" transform="rotate(0,357.625,320.571) scale(0.708333,0.665547) translate(142.882,156.078)" width="30" x="347" xlink:href="#State:红绿圆(方形)_0" y="310.59" zvalue="1250"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374927659009" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,357.625,320.571) scale(0.708333,0.665547) translate(142.882,156.078)" width="30" x="347" y="310.59"/></g>
  <g id="1035">
   <use height="30" transform="rotate(0,262,320.571) scale(0.708333,0.665547) translate(103.507,156.078)" width="30" x="251.38" xlink:href="#State:红绿圆(方形)_0" y="310.59" zvalue="1251"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562961900044289" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,262,320.571) scale(0.708333,0.665547) translate(103.507,156.078)" width="30" x="251.38" y="310.59"/></g>
 </g>
</svg>