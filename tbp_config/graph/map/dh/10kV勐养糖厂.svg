<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549585707010" height="1045" id="thSvg" source="NR-PCS9000" viewBox="0 0 1900 1045" width="1900">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="EnergyConsumer:糖厂负荷_0" viewBox="0,0,21,30">
   <use terminal-index="0" type="0" x="7.166666666666666" xlink:href="#terminal" y="0.1666666666666714"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.25" x2="19.25" y1="24" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.75" x2="18.75" y1="25" y2="25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.75" x2="17.75" y1="26.25" y2="26.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="25.25" y2="30.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.25" x2="17.25" y1="19" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.25" x2="17.25" y1="19" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.097025948103791" x2="7.097025948103791" y1="1.79293756914176" y2="0.2382945839350779"/>
   <ellipse cx="6.92" cy="8.35" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7" cy="18.92" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.111718118722353" x2="7.111718118722353" y1="16.35874008086165" y2="18.95772818087859"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.705265608193804" x2="7.11171811872235" y1="21.55671628089551" y2="18.95772818087858"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.518170629250881" x2="7.111718118722335" y1="21.55671628089551" y2="18.95772818087858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.111718118722353" x2="7.111718118722353" y1="5.503560677018264" y2="8.102548777035198"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.705265608193804" x2="7.11171811872235" y1="10.70153687705211" y2="8.102548777035178"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.518170629250881" x2="7.111718118722335" y1="10.70153687705211" y2="8.102548777035178"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV勐养糖厂" InitShowingPlane="" fill="rgb(0,0,0)" height="1045" width="1900" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="59.4" id="1" preserveAspectRatio="xMidYMid slice" width="196.94" x="69.31" xlink:href="logo.png" y="14.93"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="181" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,167.778,44.6272) scale(1,1) translate(-1.53892e-14,0)" writing-mode="lr" x="167.78" xml:space="preserve" y="49.13" zvalue="1"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="174" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,167.046,93.6272) scale(1,1) translate(8.02766e-15,-5.67788e-14)" writing-mode="lr" x="167.05" xml:space="preserve" y="98.13" zvalue="179"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="21" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,178.75,42.8894) scale(1,1) translate(0,2.31822e-15)" writing-mode="lr" x="178.75" xml:space="preserve" y="50.39" zvalue="180">    10kV勐养糖厂</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="4" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,96.625,325.25) scale(1,1) translate(0,0)" width="72.88" x="60.19" y="313.25" zvalue="534"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,96.625,325.25) scale(1,1) translate(0,0)" writing-mode="lr" x="96.63" xml:space="preserve" y="329.75" zvalue="534">信号一览</text>
  <line fill="none" id="156" stroke="rgb(0,39,45)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.22222222222285" x2="318.8097708603564" y1="76.70055797356122" y2="76.70055797356122" zvalue="190"/>
  <line fill="none" id="130" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="375" x2="375" y1="49.5" y2="1039.5" zvalue="210"/>
  <line fill="none" id="136" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.47692454998014" x2="318.0881846035993" y1="169.1279458827686" y2="169.1279458827686" zvalue="501"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="113.0114285714285" y1="928.0627909390723" y2="928.0627909390723"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="113.0114285714285" y1="979.4022909390724" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="41.57142857142856" y1="928.0627909390723" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0114285714285" x2="113.0114285714285" y1="928.0627909390723" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="347.5719285714285" y1="928.0627909390723" y2="928.0627909390723"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="347.5719285714285" y1="979.4022909390724" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="113.0119285714286" y1="928.0627909390723" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="347.5719285714285" x2="347.5719285714285" y1="928.0627909390723" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="113.0114285714285" y1="979.4022709390724" y2="979.4022709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="113.0114285714285" y1="1006.879770939072" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="41.57142857142856" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0114285714285" x2="113.0114285714285" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="184.0472285714286" y1="979.4022709390724" y2="979.4022709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="184.0472285714286" y1="1006.879770939072" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="113.0119285714286" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="184.0472285714286" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="265.8093285714285" y1="979.4022709390724" y2="979.4022709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="265.8093285714285" y1="1006.879770939072" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="184.0472285714286" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8093285714285" x2="265.8093285714285" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8092285714285" x2="347.5713285714286" y1="979.4022709390724" y2="979.4022709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8092285714285" x2="347.5713285714286" y1="1006.879770939072" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8092285714285" x2="265.8092285714285" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="347.5713285714286" x2="347.5713285714286" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="113.0114285714285" y1="1006.879690939072" y2="1006.879690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="113.0114285714285" y1="1034.357190939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="41.57142857142856" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0114285714285" x2="113.0114285714285" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="184.0472285714286" y1="1006.879690939072" y2="1006.879690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="184.0472285714286" y1="1034.357190939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="113.0119285714286" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="184.0472285714286" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="265.8093285714285" y1="1006.879690939072" y2="1006.879690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="265.8093285714285" y1="1034.357190939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="184.0472285714286" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8093285714285" x2="265.8093285714285" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8092285714285" x2="347.5713285714286" y1="1006.879690939072" y2="1006.879690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8092285714285" x2="347.5713285714286" y1="1034.357190939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8092285714285" x2="265.8092285714285" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="347.5713285714286" x2="347.5713285714286" y1="1006.879690939072" y2="1034.357190939072"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="134" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,196.463,957.636) scale(1,1) translate(0,1.05117e-13)" writing-mode="lr" x="46.93" xml:space="preserve" y="963.64" zvalue="503">参考图号  MengYangTangChang-01-2014</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="109" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,119.149,995.005) scale(1,1) translate(5.03187e-14,-1.52972e-12)" writing-mode="lr" x="56.65" xml:space="preserve" y="1001" zvalue="504">制图             </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="98" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,262.452,996.005) scale(1,1) translate(1.29063e-13,-1.53127e-12)" writing-mode="lr" x="193.75" xml:space="preserve" y="1002" zvalue="505">绘制日期    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,70.2423,1024.56) scale(1,1) translate(-4.58202e-14,1.01293e-12)" writing-mode="lr" x="70.23999999999999" xml:space="preserve" y="1030.56" zvalue="506">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="96" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,270.746,1022.56) scale(1,1) translate(0,1.12325e-13)" writing-mode="lr" x="193.92" xml:space="preserve" y="1028.56" zvalue="507">更新日期    20210816</text>
  <line fill="none" id="91" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="40.85611355802348" x2="345.4673736116426" y1="623.8945306693694" y2="623.8945306693694" zvalue="508"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.9207,640.936) scale(1,1) translate(2.94653e-15,-1.38164e-13)" writing-mode="lr" x="83.92070806204504" xml:space="preserve" y="645.4363811688671" zvalue="510">危险点(双击编辑）：</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="172.3571428571429" y2="172.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="198.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="2.571428571428442" y1="172.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="172.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="172.3571428571429" y2="172.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="198.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="172.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.5714285714284" x2="364.5714285714284" y1="172.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="198.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="222.6071428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="2.571428571428442" y1="198.3571428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="198.3571428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="198.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="222.6071428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="198.3571428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.5714285714284" x2="364.5714285714284" y1="198.3571428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="222.6071428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="245.3571428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="2.571428571428442" y1="222.6071428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="222.6071428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="222.6071428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="245.3571428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="222.6071428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.5714285714284" x2="364.5714285714284" y1="222.6071428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="245.3571428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="268.1071428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="2.571428571428442" y1="245.3571428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="245.3571428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="245.3571428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="268.1071428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="245.3571428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.5714285714284" x2="364.5714285714284" y1="245.3571428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="268.1071428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="290.8571428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="2.571428571428442" y1="268.1071428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="268.1071428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="268.1071428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="290.8571428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="268.1071428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.5714285714284" x2="364.5714285714284" y1="268.1071428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="452.3571428571429" y2="452.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="53.57142857142856" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="452.3571428571429" y2="452.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="452.3571428571429" y2="452.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9587285714285" x2="216.9587285714285" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="452.3571428571429" y2="452.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="216.9586285714286" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="452.3571428571429" y2="452.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.5714285714286" x2="334.5714285714286" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="53.57142857142856" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9587285714285" x2="216.9587285714285" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="216.9586285714286" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.5714285714286" x2="334.5714285714286" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="53.57142857142856" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9587285714285" x2="216.9587285714285" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="216.9586285714286" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.5714285714286" x2="334.5714285714286" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="564.6776428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="53.57142857142856" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="564.6776428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="564.6776428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9587285714285" x2="216.9587285714285" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="564.6776428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="216.9586285714286" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="564.6776428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.5714285714286" x2="334.5714285714286" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="564.6777428571429" y2="564.6777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="53.57142857142856" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="564.6777428571429" y2="564.6777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="564.6777428571429" y2="564.6777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9587285714285" x2="216.9587285714285" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="564.6777428571429" y2="564.6777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="216.9586285714286" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="564.6777428571429" y2="564.6777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.5714285714286" x2="334.5714285714286" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="614.0365428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="53.57142857142856" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="614.0365428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="614.0365428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9587285714285" x2="216.9587285714285" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="614.0365428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="216.9586285714286" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="614.0365428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.5714285714286" x2="334.5714285714286" y1="589.3571428571429" y2="614.0365428571429"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" x="130.578125" xml:space="preserve" y="467.359375" zvalue="513">35kV     母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="130.578125" xml:space="preserve" y="483.359375" zvalue="513">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,199.97,327.199) scale(1,1) translate(0,0)" writing-mode="lr" x="199.97" xml:space="preserve" y="331.7" zvalue="514">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,304.97,327.199) scale(1,1) translate(0,0)" writing-mode="lr" x="304.97" xml:space="preserve" y="331.7" zvalue="515">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.5714,502.857) scale(1,1) translate(0,0)" writing-mode="lr" x="78.57142857142856" xml:space="preserve" y="507.3571428571429" zvalue="517">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.5714,528.357) scale(1,1) translate(0,0)" writing-mode="lr" x="78.57142857142856" xml:space="preserve" y="532.8571428571429" zvalue="518">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.5714,553.857) scale(1,1) translate(0,0)" writing-mode="lr" x="78.57142857142856" xml:space="preserve" y="558.3571428571429" zvalue="519">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.5714,579.357) scale(1,1) translate(0,0)" writing-mode="lr" x="78.57142857142856" xml:space="preserve" y="583.8571428571429" zvalue="520">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.5714,604.857) scale(1,1) translate(0,0)" writing-mode="lr" x="78.57142857142856" xml:space="preserve" y="609.3571428571429" zvalue="521">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,40.5714,186.357) scale(1,1) translate(0,0)" writing-mode="lr" x="40.57" xml:space="preserve" y="191.86" zvalue="522">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.821,186.357) scale(1,1) translate(0,0)" writing-mode="lr" x="226.82" xml:space="preserve" y="191.86" zvalue="523">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44.2589,210.607) scale(1,1) translate(0,0)" writing-mode="lr" x="44.26" xml:space="preserve" y="215.11" zvalue="524">35kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,653.75,488) scale(1,1) translate(0,0)" writing-mode="lr" x="653.75" xml:space="preserve" y="492.5" zvalue="662">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1116,260.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1116" xml:space="preserve" y="265" zvalue="664">10kV勐糖线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,773.125,601.5) scale(1,1) translate(0,0)" writing-mode="lr" x="773.13" xml:space="preserve" y="606" zvalue="666">042</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,759.5,554.5) scale(1,1) translate(0,0)" writing-mode="lr" x="759.5" xml:space="preserve" y="559" zvalue="667">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1428.34,373) scale(1,1) translate(0,0)" writing-mode="lr" x="1428.34" xml:space="preserve" y="377.5" zvalue="683">10kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1455.12,480.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1455.13" xml:space="preserve" y="485" zvalue="685">0901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,795.5,702) scale(1,1) translate(0,0)" writing-mode="lr" x="795.5" xml:space="preserve" y="706.5" zvalue="698">800kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1137.8,416.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1137.8" xml:space="preserve" y="421" zvalue="700">041</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1130.25,363.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1130.25" xml:space="preserve" y="368" zvalue="703">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1125.5,466.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1125.5" xml:space="preserve" y="471" zvalue="707">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,755,765) scale(1,1) translate(0,0)" writing-mode="lr" x="755" xml:space="preserve" y="769.5" zvalue="710">#1压榨变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,901.125,601.5) scale(1,1) translate(0,0)" writing-mode="lr" x="901.13" xml:space="preserve" y="606" zvalue="712">043</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,887.5,554.5) scale(1,1) translate(0,0)" writing-mode="lr" x="887.5" xml:space="preserve" y="559" zvalue="715">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,923.5,702) scale(1,1) translate(0,0)" writing-mode="lr" x="923.5" xml:space="preserve" y="706.5" zvalue="718">800kVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,883,765) scale(1,1) translate(0,0)" writing-mode="lr" x="883" xml:space="preserve" y="769.5" zvalue="720">#2压榨变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1025.12,601.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1025.13" xml:space="preserve" y="606" zvalue="727">044</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1011.5,554.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1011.5" xml:space="preserve" y="559" zvalue="730">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1047.5,702) scale(1,1) translate(0,0)" writing-mode="lr" x="1047.5" xml:space="preserve" y="706.5" zvalue="733">800kVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1007,765) scale(1,1) translate(0,0)" writing-mode="lr" x="1007" xml:space="preserve" y="769.5" zvalue="735">#1锅炉变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="106" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1153.12,601.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1153.13" xml:space="preserve" y="606" zvalue="738">045</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="105" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1139.5,554.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1139.5" xml:space="preserve" y="559" zvalue="741">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="108" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1175.5,702) scale(1,1) translate(0,0)" writing-mode="lr" x="1175.5" xml:space="preserve" y="706.5" zvalue="744">315kVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="104" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1135,765) scale(1,1) translate(0,0)" writing-mode="lr" x="1135" xml:space="preserve" y="769.5" zvalue="746">#2锅炉变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="119" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1281.12,601.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1281.13" xml:space="preserve" y="606" zvalue="751">046</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1267.5,554.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1267.5" xml:space="preserve" y="559" zvalue="754">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="121" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1303.5,702) scale(1,1) translate(0,0)" writing-mode="lr" x="1303.5" xml:space="preserve" y="706.5" zvalue="757">1000kVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="117" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1263,765) scale(1,1) translate(0,0)" writing-mode="lr" x="1263" xml:space="preserve" y="769.5" zvalue="759">制炼变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="129" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1417.12,601.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1417.13" xml:space="preserve" y="606" zvalue="762">047</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="128" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1403.5,554.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1403.5" xml:space="preserve" y="559" zvalue="765">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="132" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1439.5,702) scale(1,1) translate(0,0)" writing-mode="lr" x="1439.5" xml:space="preserve" y="706.5" zvalue="768">630kVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="127" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1399,765) scale(1,1) translate(0,0)" writing-mode="lr" x="1399" xml:space="preserve" y="769.5" zvalue="770">水泵变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="149" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1549.12,601.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1549.13" xml:space="preserve" y="606" zvalue="773">048</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="147" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1535.5,554.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1535.5" xml:space="preserve" y="559" zvalue="776">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="151" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1533.5,764) scale(1,1) translate(0,0)" writing-mode="lr" x="1533.5" xml:space="preserve" y="768.5" zvalue="779">3000kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="146" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1526.75,746) scale(1,1) translate(0,0)" writing-mode="lr" x="1526.75" xml:space="preserve" y="750.5" zvalue="781">#1F</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="60.19" y="313.25" zvalue="534"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="51">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="51" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,127.571,210.468) scale(1,1) translate(0,0)" writing-mode="lr" x="127.72" xml:space="preserve" y="216.9" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="8" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,127.571,186.5) scale(1,1) translate(0,0)" writing-mode="lr" x="127.72" xml:space="preserve" y="192.93" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125170642948" ObjectName=""/>
   </metadata>
  </g>
  <g id="13">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="13" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,304.571,186.357) scale(1,1) translate(0,0)" writing-mode="lr" x="304.72" xml:space="preserve" y="192.79" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125170708484" ObjectName=""/>
   </metadata>
  </g>
  <g id="144">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="144" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,125.071,528.093) scale(1,1) translate(0,0)" writing-mode="lr" x="125.2" xml:space="preserve" y="533" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="143">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="143" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,125.071,553.218) scale(1,1) translate(0,-1.20508e-13)" writing-mode="lr" x="125.2" xml:space="preserve" y="558.13" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="142">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="142" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,125.071,578.343) scale(1,1) translate(0,-2.52173e-13)" writing-mode="lr" x="125.2" xml:space="preserve" y="583.25" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="141">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="141" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,126.5,502.968) scale(1,1) translate(0,0)" writing-mode="lr" x="126.63" xml:space="preserve" y="507.88" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="148">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="148" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,124.321,603.468) scale(1,1) translate(0,0)" writing-mode="lr" x="124.45" xml:space="preserve" y="608.38" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="U0"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="160">
   <use height="30" transform="rotate(0,334.196,327.857) scale(0.708333,0.665547) translate(133.235,159.739)" width="30" x="323.57" xlink:href="#State:红绿圆(方形)_0" y="317.87" zvalue="538"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.196,327.857) scale(0.708333,0.665547) translate(133.235,159.739)" width="30" x="323.57" y="317.87"/></g>
  <g id="54">
   <use height="30" transform="rotate(0,238.571,327.857) scale(0.708333,0.665547) translate(93.8603,159.739)" width="30" x="227.95" xlink:href="#State:红绿圆(方形)_0" y="317.87" zvalue="539"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,238.571,327.857) scale(0.708333,0.665547) translate(93.8603,159.739)" width="30" x="227.95" y="317.87"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="19">
   <path class="kv10" d="M 668 515 L 1598 515" stroke-width="4" zvalue="661"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674241740805" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674241740805"/></metadata>
  <path d="M 668 515 L 1598 515" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="5">
   <use class="kv10" height="20" transform="rotate(0,752,602.5) scale(1.5,1.35) translate(-248.167,-152.704)" width="10" x="744.5" xlink:href="#Breaker:小车断路器_0" y="589" zvalue="665"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924505632773" ObjectName="#1压榨变042断路器"/>
   <cge:TPSR_Ref TObjectID="6473924505632773"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,752,602.5) scale(1.5,1.35) translate(-248.167,-152.704)" width="10" x="744.5" y="589"/></g>
  <g id="42">
   <use class="kv10" height="20" transform="rotate(0,1117.05,417.5) scale(1.5,1.35) translate(-369.85,-104.741)" width="10" x="1109.55" xlink:href="#Breaker:开关_0" y="404" zvalue="699"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924505698309" ObjectName="10kV勐糖线041断路器"/>
   <cge:TPSR_Ref TObjectID="6473924505698309"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1117.05,417.5) scale(1.5,1.35) translate(-369.85,-104.741)" width="10" x="1109.55" y="404"/></g>
  <g id="81">
   <use class="kv10" height="20" transform="rotate(0,880,602.5) scale(1.5,1.35) translate(-290.833,-152.704)" width="10" x="872.5" xlink:href="#Breaker:小车断路器_0" y="589" zvalue="711"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924505763846" ObjectName="#2压榨变043断路器"/>
   <cge:TPSR_Ref TObjectID="6473924505763846"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,880,602.5) scale(1.5,1.35) translate(-290.833,-152.704)" width="10" x="872.5" y="589"/></g>
  <g id="103">
   <use class="kv10" height="20" transform="rotate(0,1004,602.5) scale(1.5,1.35) translate(-332.167,-152.704)" width="10" x="996.5" xlink:href="#Breaker:小车断路器_0" y="589" zvalue="726"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924505829382" ObjectName="#1锅炉变044断路器"/>
   <cge:TPSR_Ref TObjectID="6473924505829382"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1004,602.5) scale(1.5,1.35) translate(-332.167,-152.704)" width="10" x="996.5" y="589"/></g>
  <g id="114">
   <use class="kv10" height="20" transform="rotate(0,1132,602.5) scale(1.5,1.35) translate(-374.833,-152.704)" width="10" x="1124.5" xlink:href="#Breaker:小车断路器_0" y="589" zvalue="737"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924505894917" ObjectName="#2锅炉变045断路器"/>
   <cge:TPSR_Ref TObjectID="6473924505894917"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1132,602.5) scale(1.5,1.35) translate(-374.833,-152.704)" width="10" x="1124.5" y="589"/></g>
  <g id="126">
   <use class="kv10" height="20" transform="rotate(0,1260,602.5) scale(1.5,1.35) translate(-417.5,-152.704)" width="10" x="1252.5" xlink:href="#Breaker:小车断路器_0" y="589" zvalue="750"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924505960453" ObjectName="制炼变046断路器"/>
   <cge:TPSR_Ref TObjectID="6473924505960453"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1260,602.5) scale(1.5,1.35) translate(-417.5,-152.704)" width="10" x="1252.5" y="589"/></g>
  <g id="145">
   <use class="kv10" height="20" transform="rotate(0,1396,602.5) scale(1.5,1.35) translate(-462.833,-152.704)" width="10" x="1388.5" xlink:href="#Breaker:小车断路器_0" y="589" zvalue="761"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924506025989" ObjectName="水泵变047断路器"/>
   <cge:TPSR_Ref TObjectID="6473924506025989"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1396,602.5) scale(1.5,1.35) translate(-462.833,-152.704)" width="10" x="1388.5" y="589"/></g>
  <g id="157">
   <use class="kv10" height="20" transform="rotate(0,1528,602.5) scale(1.5,1.35) translate(-506.833,-152.704)" width="10" x="1520.5" xlink:href="#Breaker:小车断路器_0" y="589" zvalue="772"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924506091525" ObjectName="#1F048断路器"/>
   <cge:TPSR_Ref TObjectID="6473924506091525"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1528,602.5) scale(1.5,1.35) translate(-506.833,-152.704)" width="10" x="1520.5" y="589"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="7">
   <use class="kv10" height="30" transform="rotate(0,751,555.5) scale(1,0.733333) translate(0,198)" width="15" x="743.5" xlink:href="#Disconnector:刀闸_0" y="544.5" zvalue="666"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449773043717" ObjectName="#1压榨变0421隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449773043717"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,751,555.5) scale(1,0.733333) translate(0,198)" width="15" x="743.5" y="544.5"/></g>
  <g id="32">
   <use class="kv10" height="30" transform="rotate(0,1431,481.5) scale(1,0.733333) translate(0,171.091)" width="15" x="1423.5" xlink:href="#Disconnector:刀闸_0" y="470.5" zvalue="684"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449773174789" ObjectName="10kV母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449773174789"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1431,481.5) scale(1,0.733333) translate(0,171.091)" width="15" x="1423.5" y="470.5"/></g>
  <g id="49">
   <use class="kv10" height="30" transform="rotate(0,1117,362.5) scale(1,0.733333) translate(0,127.818)" width="15" x="1109.5" xlink:href="#Disconnector:刀闸_0" y="351.5" zvalue="702"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449773240325" ObjectName="10kV勐糖线0416隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449773240325"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1117,362.5) scale(1,0.733333) translate(0,127.818)" width="15" x="1109.5" y="351.5"/></g>
  <g id="55">
   <use class="kv10" height="30" transform="rotate(0,1116.5,467.5) scale(1,0.733333) translate(0,166)" width="15" x="1109" xlink:href="#Disconnector:刀闸_0" y="456.5" zvalue="706"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449773305861" ObjectName="10kV勐糖线0411隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449773305861"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1116.5,467.5) scale(1,0.733333) translate(0,166)" width="15" x="1109" y="456.5"/></g>
  <g id="80">
   <use class="kv10" height="30" transform="rotate(0,879,555.5) scale(1,0.733333) translate(0,198)" width="15" x="871.5" xlink:href="#Disconnector:刀闸_0" y="544.5" zvalue="713"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449773502469" ObjectName="#2压榨变0431隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449773502469"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,879,555.5) scale(1,0.733333) translate(0,198)" width="15" x="871.5" y="544.5"/></g>
  <g id="102">
   <use class="kv10" height="30" transform="rotate(0,1003,555.5) scale(1,0.733333) translate(0,198)" width="15" x="995.5" xlink:href="#Disconnector:刀闸_0" y="544.5" zvalue="728"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449773633541" ObjectName="#1锅炉变0441隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449773633541"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1003,555.5) scale(1,0.733333) translate(0,198)" width="15" x="995.5" y="544.5"/></g>
  <g id="113">
   <use class="kv10" height="30" transform="rotate(0,1131,555.5) scale(1,0.733333) translate(0,198)" width="15" x="1123.5" xlink:href="#Disconnector:刀闸_0" y="544.5" zvalue="739"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449773764613" ObjectName="#2锅炉变0451隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449773764613"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1131,555.5) scale(1,0.733333) translate(0,198)" width="15" x="1123.5" y="544.5"/></g>
  <g id="125">
   <use class="kv10" height="30" transform="rotate(0,1259,555.5) scale(1,0.733333) translate(0,198)" width="15" x="1251.5" xlink:href="#Disconnector:刀闸_0" y="544.5" zvalue="752"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449773895685" ObjectName="制炼变0461隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449773895685"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1259,555.5) scale(1,0.733333) translate(0,198)" width="15" x="1251.5" y="544.5"/></g>
  <g id="140">
   <use class="kv10" height="30" transform="rotate(0,1395,555.5) scale(1,0.733333) translate(0,198)" width="15" x="1387.5" xlink:href="#Disconnector:刀闸_0" y="544.5" zvalue="763"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449774026758" ObjectName="水泵变0471隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449774026758"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1395,555.5) scale(1,0.733333) translate(0,198)" width="15" x="1387.5" y="544.5"/></g>
  <g id="155">
   <use class="kv10" height="30" transform="rotate(0,1527,555.5) scale(1,0.733333) translate(0,198)" width="15" x="1519.5" xlink:href="#Disconnector:刀闸_0" y="544.5" zvalue="774"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449774157830" ObjectName="#1F0481隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449774157830"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1527,555.5) scale(1,0.733333) translate(0,198)" width="15" x="1519.5" y="544.5"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="10">
   <path class="kv10" d="M 751.09 544.86 L 751.09 515" stroke-width="1" zvalue="667"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="7@0" LinkObjectIDznd="19@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 751.09 544.86 L 751.09 515" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="11">
   <path class="kv10" d="M 751.06 566.31 L 751.06 590.01" stroke-width="1" zvalue="668"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="7@1" LinkObjectIDznd="5@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 751.06 566.31 L 751.06 590.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="kv10" d="M 1117 321.82 L 1117.09 351.86" stroke-width="1" zvalue="703"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1@0" LinkObjectIDznd="49@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1117 321.82 L 1117.09 351.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="53">
   <path class="kv10" d="M 1117.06 373.31 L 1117 404.59" stroke-width="1" zvalue="704"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@1" LinkObjectIDznd="42@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1117.06 373.31 L 1117 404.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="kv10" d="M 1117.15 430.39 L 1117.15 456.86" stroke-width="1" zvalue="707"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="42@1" LinkObjectIDznd="55@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1117.15 430.39 L 1117.15 456.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv10" d="M 1116.56 478.31 L 1116.56 515" stroke-width="1" zvalue="708"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="55@1" LinkObjectIDznd="19@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1116.56 478.31 L 1116.56 515" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="kv10" d="M 879.09 544.86 L 879.09 515" stroke-width="1" zvalue="714"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="80@0" LinkObjectIDznd="19@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 879.09 544.86 L 879.09 515" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv10" d="M 879.06 566.31 L 879.06 590.01" stroke-width="1" zvalue="716"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="80@1" LinkObjectIDznd="81@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 879.06 566.31 L 879.06 590.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="kv10" d="M 879.66 673.92 L 879.66 614.65" stroke-width="1" zvalue="717"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="72@0" LinkObjectIDznd="81@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 879.66 673.92 L 879.66 614.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="kv10" d="M 751.66 673.92 L 751.66 614.65" stroke-width="1" zvalue="724"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="59@0" LinkObjectIDznd="5@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 751.66 673.92 L 751.66 614.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv10" d="M 1003.09 544.86 L 1003.09 515" stroke-width="1" zvalue="729"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@0" LinkObjectIDznd="19@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 1003.09 544.86 L 1003.09 515" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="100">
   <path class="kv10" d="M 1003.06 566.31 L 1003.06 590.01" stroke-width="1" zvalue="731"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@1" LinkObjectIDznd="103@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1003.06 566.31 L 1003.06 590.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="99">
   <path class="kv10" d="M 1003.66 673.92 L 1003.66 614.65" stroke-width="1" zvalue="732"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="94@0" LinkObjectIDznd="103@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1003.66 673.92 L 1003.66 614.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="kv10" d="M 1131.09 544.86 L 1131.09 515" stroke-width="1" zvalue="740"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="113@0" LinkObjectIDznd="19@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1131.09 544.86 L 1131.09 515" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="111">
   <path class="kv10" d="M 1131.06 566.31 L 1131.06 590.01" stroke-width="1" zvalue="742"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="113@1" LinkObjectIDznd="114@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1131.06 566.31 L 1131.06 590.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="kv10" d="M 1131.66 673.92 L 1131.66 614.65" stroke-width="1" zvalue="743"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@0" LinkObjectIDznd="114@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1131.66 673.92 L 1131.66 614.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="115">
   <path class="kv10" d="M 1431.09 470.86 L 1431.09 447.72" stroke-width="1" zvalue="747"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="32@0" LinkObjectIDznd="29@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1431.09 470.86 L 1431.09 447.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="116">
   <path class="kv10" d="M 1431.06 492.31 L 1431.06 515" stroke-width="1" zvalue="748"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="32@1" LinkObjectIDznd="19@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1431.06 492.31 L 1431.06 515" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="kv10" d="M 1259.09 544.86 L 1259.09 515" stroke-width="1" zvalue="753"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@0" LinkObjectIDznd="19@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1259.09 544.86 L 1259.09 515" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="123">
   <path class="kv10" d="M 1259.06 566.31 L 1259.06 590.01" stroke-width="1" zvalue="755"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@1" LinkObjectIDznd="126@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1259.06 566.31 L 1259.06 590.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="122">
   <path class="kv10" d="M 1259.66 673.92 L 1259.66 614.65" stroke-width="1" zvalue="756"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="120@0" LinkObjectIDznd="126@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1259.66 673.92 L 1259.66 614.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="139">
   <path class="kv10" d="M 1395.09 544.86 L 1395.09 515" stroke-width="1" zvalue="764"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="140@0" LinkObjectIDznd="19@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1395.09 544.86 L 1395.09 515" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="138">
   <path class="kv10" d="M 1395.06 566.31 L 1395.06 590.01" stroke-width="1" zvalue="766"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="140@1" LinkObjectIDznd="145@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1395.06 566.31 L 1395.06 590.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="133">
   <path class="kv10" d="M 1395.66 673.92 L 1395.66 614.65" stroke-width="1" zvalue="767"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="131@0" LinkObjectIDznd="145@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1395.66 673.92 L 1395.66 614.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="154">
   <path class="kv10" d="M 1527.09 544.86 L 1527.09 515" stroke-width="1" zvalue="775"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="155@0" LinkObjectIDznd="19@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1527.09 544.86 L 1527.09 515" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="153">
   <path class="kv10" d="M 1527.06 566.31 L 1527.06 590.01" stroke-width="1" zvalue="777"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="155@1" LinkObjectIDznd="157@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1527.06 566.31 L 1527.06 590.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="152">
   <path class="kv10" d="M 1528 674.9 L 1528 614.65" stroke-width="1" zvalue="778"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@0" LinkObjectIDznd="157@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1528 674.9 L 1528 614.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="29">
   <use class="kv10" height="18" transform="rotate(0,1433.67,424.5) scale(2.77778,-2.77778) translate(-904.216,-561.32)" width="15" x="1412.837546007025" xlink:href="#Accessory:PT8_0" y="399.5" zvalue="682"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449773109253" ObjectName="10kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1433.67,424.5) scale(2.77778,-2.77778) translate(-904.216,-561.32)" width="15" x="1412.837546007025" y="399.5"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="59">
   <use class="kv10" height="30" transform="rotate(0,760.1,711.5) scale(2.53333,2.53333) translate(-443.961,-407.645)" width="21" x="733.5" xlink:href="#EnergyConsumer:糖厂负荷_0" y="673.5" zvalue="709"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449773371397" ObjectName="#1压榨变"/>
   <cge:TPSR_Ref TObjectID="6192449773371397"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,760.1,711.5) scale(2.53333,2.53333) translate(-443.961,-407.645)" width="21" x="733.5" y="673.5"/></g>
  <g id="72">
   <use class="kv10" height="30" transform="rotate(0,888.1,711.5) scale(2.53333,2.53333) translate(-521.434,-407.645)" width="21" x="861.5" xlink:href="#EnergyConsumer:糖厂负荷_0" y="673.5" zvalue="719"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449773436933" ObjectName="#2压榨变"/>
   <cge:TPSR_Ref TObjectID="6192449773436933"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,888.1,711.5) scale(2.53333,2.53333) translate(-521.434,-407.645)" width="21" x="861.5" y="673.5"/></g>
  <g id="94">
   <use class="kv10" height="30" transform="rotate(0,1012.1,711.5) scale(2.53333,2.53333) translate(-596.487,-407.645)" width="21" x="985.5" xlink:href="#EnergyConsumer:糖厂负荷_0" y="673.5" zvalue="734"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449773568005" ObjectName="#1锅炉变"/>
   <cge:TPSR_Ref TObjectID="6192449773568005"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1012.1,711.5) scale(2.53333,2.53333) translate(-596.487,-407.645)" width="21" x="985.5" y="673.5"/></g>
  <g id="107">
   <use class="kv10" height="30" transform="rotate(0,1140.1,711.5) scale(2.53333,2.53333) translate(-673.961,-407.645)" width="21" x="1113.5" xlink:href="#EnergyConsumer:糖厂负荷_0" y="673.5" zvalue="745"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449773699077" ObjectName="#2锅炉变"/>
   <cge:TPSR_Ref TObjectID="6192449773699077"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1140.1,711.5) scale(2.53333,2.53333) translate(-673.961,-407.645)" width="21" x="1113.5" y="673.5"/></g>
  <g id="120">
   <use class="kv10" height="30" transform="rotate(0,1268.1,711.5) scale(2.53333,2.53333) translate(-751.434,-407.645)" width="21" x="1241.5" xlink:href="#EnergyConsumer:糖厂负荷_0" y="673.5" zvalue="758"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449773830149" ObjectName="制炼变"/>
   <cge:TPSR_Ref TObjectID="6192449773830149"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1268.1,711.5) scale(2.53333,2.53333) translate(-751.434,-407.645)" width="21" x="1241.5" y="673.5"/></g>
  <g id="131">
   <use class="kv10" height="30" transform="rotate(0,1404.1,711.5) scale(2.53333,2.53333) translate(-833.75,-407.645)" width="21" x="1377.5" xlink:href="#EnergyConsumer:糖厂负荷_0" y="673.5" zvalue="769"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449773961221" ObjectName="水泵变"/>
   <cge:TPSR_Ref TObjectID="6192449773961221"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1404.1,711.5) scale(2.53333,2.53333) translate(-833.75,-407.645)" width="21" x="1377.5" y="673.5"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="150">
   <use class="kv10" height="30" transform="rotate(0,1528,698.447) scale(1.51667,1.59649) translate(-512.777,-252.011)" width="30" x="1505.25" xlink:href="#Generator:发电机_0" y="674.5" zvalue="780"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449774092294" ObjectName="#1F"/>
   <cge:TPSR_Ref TObjectID="6192449774092294"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1528,698.447) scale(1.51667,1.59649) translate(-512.777,-252.011)" width="30" x="1505.25" y="674.5"/></g>
 </g>
</svg>