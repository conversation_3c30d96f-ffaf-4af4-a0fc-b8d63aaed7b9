<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549595734018" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="ACLineSegment:带避雷器线路PT的线路_0" viewBox="0,0,28,25">
   <use terminal-index="0" type="0" x="7.889690721649489" xlink:href="#terminal" y="23.4778860569715"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.705760715003304" x2="3.705760715003304" y1="17.98165807560139" y2="20.28165807560139"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.1" x2="7.9" y1="20.35" y2="20.35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.99493209648661" x2="15.73801133176259" y1="13.42856966348774" y2="13.42856966348774"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.05708746256016" x2="12.05708746256016" y1="18.21462834614074" y2="20.31394252867895"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.05708746256016" x2="12.05708746256016" y1="15.83326413498526" y2="10.847392951457"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.881013745704481" x2="7.881013745704481" y1="23.33200899550225" y2="0.6833333333333229"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.0715036948084" x2="12.0715036948084" y1="8.626739580957119" y2="6.39621826201027"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="9.520172840926069" x2="14.92116229358282" y1="6.262853550392695" y2="6.262853550392695"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.11740027094565" x2="12.11740027094565" y1="10.74142612107711" y2="10.74142612107711"/>
   <ellipse cx="23.44" cy="13.33" fill-opacity="0" rx="3.84" ry="3.87" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="21.45" cy="8.369999999999999" fill-opacity="0" rx="3.84" ry="3.87" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.43" cy="13.38" fill-opacity="0" rx="3.84" ry="3.87" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.49620888348457" x2="14.79890446679221" y1="10.7414261210771" y2="10.7414261210771"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.39664267427136" x2="14.13972190954734" y1="5.17713898780255" y2="5.17713898780255"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.18422026904639" x2="12.89979825188121" y1="4.36285306585993" y2="4.36285306585993"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.11740027094565" x2="12.11740027094565" y1="18.06999941856068" y2="18.06999941856068"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.49620888348457" x2="14.79890446679221" y1="18.06999941856068" y2="18.06999941856068"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.496208883484574" x2="14.79890446679222" y1="8.705711316220558" y2="8.705711316220558"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.49620888348457" x2="14.79890446679221" y1="16.03428461370413" y2="16.03428461370413"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(90,3.74,12.62) scale(1,1) translate(0,0)" width="10.7" x="-1.61" y="10.12"/>
   <path d="M 5.09221 10.9572 L 2.32976 10.9572 L 3.734 14.8326 L 5.09221 10.9572" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.786782704008788" x2="1.643104543089253" y1="5.14716501266186" y2="5.14716501266186"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.71494362354902" x2="3.71494362354902" y1="14.69035020469595" y2="5.122943730448588"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.981067506052216" x2="2.218615398772524" y1="4.178313724130515" y2="4.178313724130515"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.405556650368943" x2="3.139432767865758" y1="3.451675257731946" y2="3.451675257731946"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.85" x2="3.65" y1="20.35" y2="20.35"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Accessory:平河光伏_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="14.02572331551165" xlink:href="#terminal" y="28.09945819018008"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="5.5" y1="18.94406992327969" y2="17.94406992327969"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="5.5" y1="14.94406992327969" y2="15.94406992327969"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="7.5" y1="18.94406992327969" y2="14.94406992327969"/>
   <ellipse cx="13.98" cy="23.99" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.39" cy="10.58" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.94906826265991" x2="13.94906826265991" y1="21.73154965466559" y2="24.2066296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.94906826265991" x2="16.34906826265991" y1="24.20662962336982" y2="25.44416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.94906826265991" x2="11.54906826265991" y1="24.20662962336982" y2="25.44416960772192"/>
   <ellipse cx="13.98" cy="16.99" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="6.98" cy="23.99" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.949068262659913" x2="9.349068262659905" y1="24.20662962336982" y2="25.44416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.949068262659909" x2="6.949068262659909" y1="21.73154965466559" y2="24.2066296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.949068262659906" x2="4.549068262659913" y1="24.20662962336982" y2="25.44416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.94906826265991" x2="13.94906826265991" y1="14.7315496546656" y2="17.2066296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.94906826265991" x2="16.34906826265991" y1="17.20662962336982" y2="18.44416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.94906826265991" x2="11.54906826265991" y1="17.20662962336982" y2="18.44416960772192"/>
   <ellipse cx="6.98" cy="16.99" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.48240159599324" x2="12.88240159599324" y1="10.67329629003649" y2="11.91083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.48240159599324" x2="8.082401595993243" y1="10.67329629003649" y2="11.91083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.48240159599324" x2="10.48240159599324" y1="8.198216321332257" y2="10.67329629003646"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="Breaker:手车开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:手车开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="Breaker:手车开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.45181617405996" x2="1.714850492606708" y1="5.276370517142858" y2="14.05696281619048"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.583333333333333" x2="8.583333333333334" y1="5.166666666666667" y2="14.16666666666667"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="Accessory:避雷器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000003" y2="8.666666666666668"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="12.6" y2="18.6"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
  </symbol>
  <symbol id="PowerTransformer2:平河光伏1_0" viewBox="0,0,31,30">
   <use terminal-index="0" type="1" x="10.75731595793324" xlink:href="#terminal" y="1.08736282578875"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.77194787379973" x2="6.705871323769093" y1="7.640146319158664" y2="3.944602328551218"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.98333333333333" x2="10.76463191586648" y1="3.694602328551216" y2="7.640146319158664"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.75935070873343" x2="10.75935070873343" y1="7.654778235025146" y2="11.68728637061797"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="21.28783721993599" x2="20.28783721993599" y1="2.175582990397805" y2="5.175582990397805"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.2894375857338822" x2="21.28943758573388" y1="13.09064929126657" y2="2.173982624599901"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="18.28052126200274" x2="21.28052126200274" y1="1.175582990397805" y2="2.175582990397805"/>
   <ellipse cx="10.84" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:平河光伏1_1" viewBox="0,0,31,30">
   <use terminal-index="1" type="1" x="10.75" xlink:href="#terminal" y="29.05121170553269"/>
   <ellipse cx="10.84" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.93861454046639" x2="6.872537990435758" y1="21.30681298582533" y2="17.61126899521788"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.15" x2="10.93129858253315" y1="17.36126899521788" y2="21.30681298582533"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.92601737540009" x2="10.92601737540009" y1="21.32144490169181" y2="25.35395303728464"/>
   <use terminal-index="3" type="2" x="10.75" xlink:href="#terminal" y="21.25"/>
  </symbol>
  <symbol id="Accessory:接地变接地设备_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="0.75"/>
   <rect fill-opacity="0" height="13.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.94,7.54) scale(1,1) translate(0,0)" width="6.08" x="2.9" y="0.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.2499999999999956" x2="11.58333333333334" y1="20.49453511141348" y2="20.49453511141348"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.833333333333332" x2="10" y1="22.90451817731685" y2="22.90451817731685"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6" x2="6" y1="20.66666666666667" y2="14.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.934588934424228" x2="7.898744398909104" y1="25.38116790988687" y2="25.38116790988687"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
  <symbol id="Accessory:中间电缆_0" viewBox="0,0,8,21">
   <use terminal-index="0" type="0" x="4" xlink:href="#terminal" y="10.5"/>
   <path d="M 1.08333 0.5 L 7 0.5 L 4 7.13889 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4" x2="4" y1="20.83333333333333" y2="0.5000000000000036"/>
   <path d="M 1.08333 20.6389 L 7 20.6389 L 4 14 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Accessory:四绕组PT带熔断器_0" viewBox="0,0,35,35">
   <use terminal-index="0" type="0" x="17.56245852479325" xlink:href="#terminal" y="34.62373692455962"/>
   <rect fill-opacity="0" height="3.55" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,30.81,10.63) scale(1,1) translate(0,0)" width="8.92" x="26.34" y="8.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.61245852479332" x2="34.61245852479332" y1="14.09040359122622" y2="7.090403591226224"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.16666666666667" x2="31" y1="17.94225544307809" y2="17.94225544307809"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.86245852479331" x2="30.86245852479331" y1="17.93299618381883" y2="15.09040359122624"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.6124585247933" x2="26.6124585247933" y1="16.09040359122623" y2="14.09040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.61245852479333" x2="34.61245852479333" y1="7.09040359122622" y2="5.09040359122622"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,17.58,28.25) scale(1,1) translate(0,0)" width="5" x="15.08" y="23.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.647721108666" x2="30.647721108666" y1="6.086748218596288" y2="2.916666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.71438777533266" x2="28.71438777533266" y1="2.766127685651949" y2="2.766127685651949"/>
   <ellipse cx="14.04" cy="17.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.05048569403981" x2="22.05048569403981" y1="29.78084700683307" y2="29.78084700683307"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.7608382776216" x2="28.48402243293775" y1="3.004208141873306" y2="3.004208141873306"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.01083827762157" x2="29.40068909960439" y1="1.75420814187332" y2="1.75420814187332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.51083827762159" x2="30.06735576627107" y1="0.2542081418732955" y2="0.2542081418732955"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.51457106407813" x2="19.32090482830307" y1="19.30654513693459" y2="16.68563107372743"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58713830216066" x2="22.81883560169719" y1="16.79040359122622" y2="16.79040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.89410730945214" x2="23.0877735452272" y1="19.40299989806297" y2="16.78208583485582"/>
   <ellipse cx="14.04" cy="10.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="21.04" cy="17.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.3644003886642" x2="22.3644003886642" y1="25.44213090500034" y2="25.44213090500034"/>
   <ellipse cx="21.04" cy="10.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.4422038255927" x2="16.4422038255927" y1="12.07076893362115" y2="12.07076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.19220382559271" x2="17.19220382559271" y1="18.32076893362116" y2="18.32076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.87303257583199" x2="23.32581493230132" y1="10.72611741162254" y2="11.85543752773796"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.92264294445647" x2="20.87303257583197" y1="12.24992879670395" y2="10.72611741162254"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.873032575832" x2="20.37063985073817" y1="10.72611741162252" y2="8.072985910425688"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.94220382559271" x2="16.94220382559271" y1="11.57076893362115" y2="11.57076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583199" x2="13.12063985073816" y1="17.97611741162255" y2="15.32298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.67264294445646" x2="13.62303257583197" y1="19.49992879670398" y2="17.97611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583198" x2="16.07581493230131" y1="17.97611741162255" y2="19.10543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583198" x2="13.12063985073816" y1="10.72611741162253" y2="8.072985910425691"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.67264294445646" x2="13.62303257583196" y1="12.24992879670396" y2="10.72611741162255"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583199" x2="16.07581493230131" y1="10.72611741162254" y2="11.85543752773796"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.59103363843511" x2="17.59103363843511" y1="20.40822158129308" y2="34.83333333333334"/>
  </symbol>
  <symbol id="Disconnector:联体小车刀闸2_0" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="9.583333333333332" y2="1.050000000000001"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.083333333333333" x2="7.083333333333333" y1="26.41666666666667" y2="34.83333333333334"/>
   <rect fill-opacity="0" height="17" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,18) scale(1,1) translate(0,0)" width="8" x="3" y="9.5"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
  </symbol>
  <symbol id="Disconnector:联体小车刀闸2_1" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="35" y2="1.166666666666664"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <rect fill-opacity="0" height="17" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,18) scale(1,1) translate(0,0)" width="8" x="3" y="9.5"/>
  </symbol>
  <symbol id="Disconnector:联体小车刀闸2_2" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.083333333333333" x2="7.083333333333333" y1="9.999999999999995" y2="1.299999999999994"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="1.499999999999999" y1="26" y2="9.966666666666669"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.999999999999998" x2="12.41666666666667" y1="26.25" y2="10.05"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变YD2022_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="24" y1="24" y2="18"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.98980556533989" x2="12.83661970177291" y1="29.68333333333333" y2="27.75505857643124"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.9898055653399" x2="15.14299142890688" y1="29.68333333333333" y2="27.75505857643124"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.83661970177294" x2="15.1429914289069" y1="27.75505857643125" y2="27.75505857643125"/>
  </symbol>
  <symbol id="Accessory:放电间隙4_0" viewBox="0,0,15,15">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="0.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.517857142857143" x2="7.517857142857143" y1="0.4377686487201995" y2="11.33333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.517857142857143" x2="7.517857142857143" y1="13.5" y2="11.35783935656103"/>
   <path d="M 7.53329 8.11336 L 4.01543 4.05453 L 10.9321 4.05453 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:R_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.95" xlink:href="#terminal" y="2.95"/>
   <rect fill-opacity="0" height="15.05" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.35,10.58) scale(1,1) translate(0,0)" width="6.1" x="2.3" y="3.05"/>
  </symbol>
  <symbol id="EnergyConsumer:单绕组PT避雷器_0" viewBox="0,0,40,45">
   <use terminal-index="0" type="0" x="17" xlink:href="#terminal" y="44.5"/>
   <path d="M 17 16 L 8 16 L 8 28 L 8 28" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="7" y1="28.75" y2="22.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="9" y1="28.75" y2="22.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.16022336769755" x2="17.16022336769755" y1="44.33333333333334" y2="3.833333333333332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="8.5" y1="40" y2="40"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="21.91094883543335" x2="27.38823024054981" y1="15.92541949757221" y2="15.92541949757221"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,8.04,27.92) scale(1,1) translate(0,0)" width="6.08" x="5" y="20.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="8" y1="35" y2="38"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="10.5" y1="38" y2="38"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="19.46058610156545" x2="17.15436235204273" y1="15.92541949757221" y2="15.92541949757221"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.50545055364641" x2="24.50545055364641" y1="16.04138864447597" y2="22.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.5" x2="9.5" y1="39" y2="39"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.46383161512025" x2="34.41666666666666" y1="15.99758812251703" y2="15.99758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="27.35940244368076" x2="27.35940244368076" y1="15.99758812251703" y2="15.99758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="34.36598892707138" x2="34.36598892707138" y1="12.75" y2="19.24517624503405"/>
   <ellipse cx="24.51" cy="26.82" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="31.55" cy="31.08" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="24.76" cy="35.08" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="27.35940244368077" x2="27.35940244368077" y1="13.48325293741726" y2="18.58409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="35.51910080183274" x2="35.51910080183274" y1="14.01295093653441" y2="18.34306843322376"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="36.38393470790376" x2="36.38393470790376" y1="14.91505874834466" y2="16.89969593432727"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="19.57589728904158" x2="19.57589728904158" y1="15.99758812251703" y2="15.99758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="19.49256395570825" x2="19.49256395570825" y1="13.33333333333333" y2="18.43417232847779"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="29.52148720885832" x2="29.52148720885832" y1="13.48325293741726" y2="18.58409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="21.73798205421914" x2="21.73798205421914" y1="13.48325293741726" y2="18.58409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.91666666666667" x2="26.91666666666667" y1="27.08333333333334" y2="27.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.33333333333334" x2="34.33333333333334" y1="31.08333333333334" y2="31.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.58333333333334" x2="27.58333333333334" y1="35" y2="35"/>
   <path d="M 17.0833 0.416667 L 15.6667 7.33333 L 17.1667 3.83333 L 18.6667 7.5 L 17.119 0.2" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id=":光伏发电2_0" viewBox="0,0,10,30">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="29.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.000411522633746" x2="5.000411522633746" y1="29.91666666666667" y2="1.116666666666665"/>
   <path d="M 4.91667 14.5224 L 3.5 7.60569 L 5 11.1057 L 6.5 7.43903 L 4.95238 14.739" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷SVG_0" viewBox="0,0,19,38">
   <use terminal-index="0" type="0" x="9.5" xlink:href="#terminal" y="1"/>
   <text fill="rgb(0,0,0)" font-family="宋体" font-size="3" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" text-anchor="middle" x="9.598524611034447" xml:space="preserve" y="27.83333333333334">IGBT</text>
   <rect fill-opacity="0" height="11.97" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,9.34,27.32) scale(1,1) translate(0,0)" width="6.24" x="6.22" y="21.33"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.418412848215781" x2="9.654606963472311" y1="7.405540230476685" y2="7.405540230476685"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.654606963472311" x2="9.654606963472311" y1="7.405540230476694" y2="15.00091408531136"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.072888846454279" x2="13.34180025300773" y1="37.78754286994457" y2="37.78754286994457"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.381314666673847" x2="9.381314666673847" y1="33.16666666666667" y2="37.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.28748845890916" x2="13.28748845890916" y1="35.14710565745744" y2="37.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.089989994732893" x2="6.089989994732893" y1="35.0999590698429" y2="37.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.632742212616975" x2="9.632742212616975" y1="21.40056988126481" y2="14.86865633162021"/>
   <path d="M 1.99207 7.50181 A 6.04954 7.86213 -90 1 0 9.30345 1.46688" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV平河光伏电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="256" x="62.32" xlink:href="logo.png" y="55.5"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="33" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,190.321,85.5) scale(1,1) translate(0,0)" writing-mode="lr" x="190.32" xml:space="preserve" y="89" zvalue="8"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,190.821,85.1903) scale(1,1) translate(0,0)" writing-mode="lr" x="190.82" xml:space="preserve" y="94.19" zvalue="9">110kV平河光伏电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="1" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,83.9464,238.25) scale(1,1) translate(0,0)" width="72.88" x="47.51" y="226.25" zvalue="42"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.9464,238.25) scale(1,1) translate(0,0)" writing-mode="lr" x="83.95" xml:space="preserve" y="242.75" zvalue="42">信号一览</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="309" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,83.7232,294.571) scale(1,1) translate(0,0)" width="72.88" x="47.29" y="282.57" zvalue="540"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.7232,294.571) scale(1,1) translate(0,0)" writing-mode="lr" x="83.72" xml:space="preserve" y="299.07" zvalue="540">AVC / AGC</text>
  <line fill="none" id="31" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="396.3214285714287" x2="396.3214285714287" y1="23.5" y2="1053.5" zvalue="10"/>
  <line fill="none" id="29" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.32142857142912" x2="389.3214285714287" y1="159.3704926140824" y2="159.3704926140824" zvalue="12"/>
  <line fill="none" id="28" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.32142857142912" x2="389.3214285714287" y1="629.3704926140824" y2="629.3704926140824" zvalue="13"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="22.32142857142867" x2="112.3214285714287" y1="944.5" y2="944.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="22.32142857142867" x2="112.3214285714287" y1="983.6632999999999" y2="983.6632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="22.32142857142867" x2="22.32142857142867" y1="944.5" y2="983.6632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="112.3214285714287" x2="112.3214285714287" y1="944.5" y2="983.6632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="112.3214285714287" x2="382.3214285714287" y1="944.5" y2="944.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="112.3214285714287" x2="382.3214285714287" y1="983.6632999999999" y2="983.6632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="112.3214285714287" x2="112.3214285714287" y1="944.5" y2="983.6632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="382.3214285714287" x2="382.3214285714287" y1="944.5" y2="983.6632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="22.32142857142867" x2="112.3214285714287" y1="983.66327" y2="983.66327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="22.32142857142867" x2="112.3214285714287" y1="1011.58167" y2="1011.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="22.32142857142867" x2="22.32142857142867" y1="983.66327" y2="1011.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="112.3214285714287" x2="112.3214285714287" y1="983.66327" y2="1011.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="112.3214285714287" x2="202.3214285714287" y1="983.66327" y2="983.66327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="112.3214285714287" x2="202.3214285714287" y1="1011.58167" y2="1011.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="112.3214285714287" x2="112.3214285714287" y1="983.66327" y2="1011.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="202.3214285714287" x2="202.3214285714287" y1="983.66327" y2="1011.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="202.3214285714288" x2="292.3214285714288" y1="983.66327" y2="983.66327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="202.3214285714288" x2="292.3214285714288" y1="1011.58167" y2="1011.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="202.3214285714288" x2="202.3214285714288" y1="983.66327" y2="1011.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="292.3214285714288" x2="292.3214285714288" y1="983.66327" y2="1011.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="292.3214285714287" x2="382.3214285714287" y1="983.66327" y2="983.66327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="292.3214285714287" x2="382.3214285714287" y1="1011.58167" y2="1011.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="292.3214285714287" x2="292.3214285714287" y1="983.66327" y2="1011.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="382.3214285714287" x2="382.3214285714287" y1="983.66327" y2="1011.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="22.32142857142867" x2="112.3214285714287" y1="1011.5816" y2="1011.5816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="22.32142857142867" x2="112.3214285714287" y1="1039.5" y2="1039.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="22.32142857142867" x2="22.32142857142867" y1="1011.5816" y2="1039.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="112.3214285714287" x2="112.3214285714287" y1="1011.5816" y2="1039.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="112.3214285714287" x2="202.3214285714287" y1="1011.5816" y2="1011.5816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="112.3214285714287" x2="202.3214285714287" y1="1039.5" y2="1039.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="112.3214285714287" x2="112.3214285714287" y1="1011.5816" y2="1039.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="202.3214285714287" x2="202.3214285714287" y1="1011.5816" y2="1039.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="202.3214285714288" x2="292.3214285714288" y1="1011.5816" y2="1011.5816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="202.3214285714288" x2="292.3214285714288" y1="1039.5" y2="1039.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="202.3214285714288" x2="202.3214285714288" y1="1011.5816" y2="1039.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="292.3214285714288" x2="292.3214285714288" y1="1011.5816" y2="1039.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="292.3214285714287" x2="382.3214285714287" y1="1011.5816" y2="1011.5816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="292.3214285714287" x2="382.3214285714287" y1="1039.5" y2="1039.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="292.3214285714287" x2="292.3214285714287" y1="1011.5816" y2="1039.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="382.3214285714287" x2="382.3214285714287" y1="1011.5816" y2="1039.5"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,67.3214,964.5) scale(1,1) translate(0,0)" writing-mode="lr" x="67.31999999999999" xml:space="preserve" y="970.5" zvalue="15">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,64.3214,998.5) scale(1,1) translate(0,0)" writing-mode="lr" x="64.31999999999999" xml:space="preserve" y="1004.5" zvalue="16">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,246.321,998.5) scale(1,1) translate(0,0)" writing-mode="lr" x="246.32" xml:space="preserve" y="1004.5" zvalue="17">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,63.3214,1026.5) scale(1,1) translate(0,0)" writing-mode="lr" x="63.32" xml:space="preserve" y="1032.5" zvalue="18">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,245.321,1026.5) scale(1,1) translate(0,0)" writing-mode="lr" x="245.32" xml:space="preserve" y="1032.5" zvalue="19">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,87.8214,659) scale(1,1) translate(0,0)" writing-mode="lr" x="87.82142857142867" xml:space="preserve" y="663.5" zvalue="21">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,247.376,966.5) scale(1,1) translate(0,0)" writing-mode="lr" x="247.38" xml:space="preserve" y="972.5" zvalue="22">PingHeGF-01-2022</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,157.376,998.5) scale(1,1) translate(0,0)" writing-mode="lr" x="157.38" xml:space="preserve" y="1004.5" zvalue="23">杨立超</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,337.376,998.5) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="1004.5" zvalue="24">20221013</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="20.25" x2="201.25" y1="163.5" y2="163.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="20.25" x2="201.25" y1="189.5" y2="189.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="20.25" x2="20.25" y1="163.5" y2="189.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.25" x2="201.25" y1="163.5" y2="189.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.25" x2="382.25" y1="163.5" y2="163.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.25" x2="382.25" y1="189.5" y2="189.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.25" x2="201.25" y1="163.5" y2="189.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="382.25" x2="382.25" y1="163.5" y2="189.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="20.25" x2="201.25" y1="189.5" y2="189.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="20.25" x2="201.25" y1="213" y2="213"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="20.25" x2="20.25" y1="189.5" y2="213"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.25" x2="201.25" y1="189.5" y2="213"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.25" x2="382.25" y1="189.5" y2="189.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.25" x2="382.25" y1="213" y2="213"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.25" x2="201.25" y1="189.5" y2="213"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="382.25" x2="382.25" y1="189.5" y2="213"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="50.72202829616253" x2="99.21602829616245" y1="456.6666435058594" y2="456.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="50.72202829616253" x2="99.21602829616245" y1="494.1566435058594" y2="494.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="50.72202829616253" x2="50.72202829616253" y1="456.6666435058594" y2="494.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="99.21602829616245" y1="456.6666435058594" y2="494.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="161.5161282961625" y1="456.6666435058594" y2="456.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="161.5161282961625" y1="494.1566435058594" y2="494.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="99.21602829616245" y1="456.6666435058594" y2="494.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5161282961625" x2="161.5161282961625" y1="456.6666435058594" y2="494.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5160282961625" x2="234.1818282961625" y1="456.6666435058594" y2="456.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5160282961625" x2="234.1818282961625" y1="494.1566435058594" y2="494.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5160282961625" x2="161.5160282961625" y1="456.6666435058594" y2="494.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1818282961625" x2="234.1818282961625" y1="456.6666435058594" y2="494.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1817282961625" x2="296.4818282961625" y1="456.6666435058594" y2="456.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1817282961625" x2="296.4818282961625" y1="494.1566435058594" y2="494.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1817282961625" x2="234.1817282961625" y1="456.6666435058594" y2="494.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4818282961625" x2="296.4818282961625" y1="456.6666435058594" y2="494.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4817282961625" x2="358.7818282961625" y1="456.6666435058594" y2="456.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4817282961625" x2="358.7818282961625" y1="494.1566435058594" y2="494.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4817282961625" x2="296.4817282961625" y1="456.6666435058594" y2="494.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="358.7818282961625" x2="358.7818282961625" y1="456.6666435058594" y2="494.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="50.72202829616253" x2="99.21602829616245" y1="494.1567435058594" y2="494.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="50.72202829616253" x2="99.21602829616245" y1="518.3253435058593" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="50.72202829616253" x2="50.72202829616253" y1="494.1567435058594" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="99.21602829616245" y1="494.1567435058594" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="161.5161282961625" y1="494.1567435058594" y2="494.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="161.5161282961625" y1="518.3253435058593" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="99.21602829616245" y1="494.1567435058594" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5161282961625" x2="161.5161282961625" y1="494.1567435058594" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5160282961625" x2="234.1818282961625" y1="494.1567435058594" y2="494.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5160282961625" x2="234.1818282961625" y1="518.3253435058593" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5160282961625" x2="161.5160282961625" y1="494.1567435058594" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1818282961625" x2="234.1818282961625" y1="494.1567435058594" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1817282961625" x2="296.4818282961625" y1="494.1567435058594" y2="494.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1817282961625" x2="296.4818282961625" y1="518.3253435058593" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1817282961625" x2="234.1817282961625" y1="494.1567435058594" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4818282961625" x2="296.4818282961625" y1="494.1567435058594" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4817282961625" x2="358.7818282961625" y1="494.1567435058594" y2="494.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4817282961625" x2="358.7818282961625" y1="518.3253435058593" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4817282961625" x2="296.4817282961625" y1="494.1567435058594" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="358.7818282961625" x2="358.7818282961625" y1="494.1567435058594" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="50.72202829616253" x2="99.21602829616245" y1="518.3253435058593" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="50.72202829616253" x2="99.21602829616245" y1="542.4939435058594" y2="542.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="50.72202829616253" x2="50.72202829616253" y1="518.3253435058593" y2="542.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="99.21602829616245" y1="518.3253435058593" y2="542.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="161.5161282961625" y1="518.3253435058593" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="161.5161282961625" y1="542.4939435058594" y2="542.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="99.21602829616245" y1="518.3253435058593" y2="542.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5161282961625" x2="161.5161282961625" y1="518.3253435058593" y2="542.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5160282961625" x2="234.1818282961625" y1="518.3253435058593" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5160282961625" x2="234.1818282961625" y1="542.4939435058594" y2="542.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5160282961625" x2="161.5160282961625" y1="518.3253435058593" y2="542.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1818282961625" x2="234.1818282961625" y1="518.3253435058593" y2="542.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1817282961625" x2="296.4818282961625" y1="518.3253435058593" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1817282961625" x2="296.4818282961625" y1="542.4939435058594" y2="542.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1817282961625" x2="234.1817282961625" y1="518.3253435058593" y2="542.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4818282961625" x2="296.4818282961625" y1="518.3253435058593" y2="542.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4817282961625" x2="358.7818282961625" y1="518.3253435058593" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4817282961625" x2="358.7818282961625" y1="542.4939435058594" y2="542.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4817282961625" x2="296.4817282961625" y1="518.3253435058593" y2="542.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="358.7818282961625" x2="358.7818282961625" y1="518.3253435058593" y2="542.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="50.72202829616253" x2="99.21602829616245" y1="542.4940335058594" y2="542.4940335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="50.72202829616253" x2="99.21602829616245" y1="566.6626335058594" y2="566.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="50.72202829616253" x2="50.72202829616253" y1="542.4940335058594" y2="566.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="99.21602829616245" y1="542.4940335058594" y2="566.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="161.5161282961625" y1="542.4940335058594" y2="542.4940335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="161.5161282961625" y1="566.6626335058594" y2="566.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="99.21602829616245" y1="542.4940335058594" y2="566.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5161282961625" x2="161.5161282961625" y1="542.4940335058594" y2="566.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5160282961625" x2="234.1818282961625" y1="542.4940335058594" y2="542.4940335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5160282961625" x2="234.1818282961625" y1="566.6626335058594" y2="566.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5160282961625" x2="161.5160282961625" y1="542.4940335058594" y2="566.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1818282961625" x2="234.1818282961625" y1="542.4940335058594" y2="566.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1817282961625" x2="296.4818282961625" y1="542.4940335058594" y2="542.4940335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1817282961625" x2="296.4818282961625" y1="566.6626335058594" y2="566.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1817282961625" x2="234.1817282961625" y1="542.4940335058594" y2="566.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4818282961625" x2="296.4818282961625" y1="542.4940335058594" y2="566.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4817282961625" x2="358.7818282961625" y1="542.4940335058594" y2="542.4940335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4817282961625" x2="358.7818282961625" y1="566.6626335058594" y2="566.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4817282961625" x2="296.4817282961625" y1="542.4940335058594" y2="566.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="358.7818282961625" x2="358.7818282961625" y1="542.4940335058594" y2="566.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="50.72202829616253" x2="99.21602829616245" y1="566.6628435058594" y2="566.6628435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="50.72202829616253" x2="99.21602829616245" y1="590.8314435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="50.72202829616253" x2="50.72202829616253" y1="566.6628435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="99.21602829616245" y1="566.6628435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="161.5161282961625" y1="566.6628435058594" y2="566.6628435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="161.5161282961625" y1="590.8314435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="99.21602829616245" y1="566.6628435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5161282961625" x2="161.5161282961625" y1="566.6628435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5160282961625" x2="234.1818282961625" y1="566.6628435058594" y2="566.6628435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5160282961625" x2="234.1818282961625" y1="590.8314435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5160282961625" x2="161.5160282961625" y1="566.6628435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1818282961625" x2="234.1818282961625" y1="566.6628435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1817282961625" x2="296.4818282961625" y1="566.6628435058594" y2="566.6628435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1817282961625" x2="296.4818282961625" y1="590.8314435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1817282961625" x2="234.1817282961625" y1="566.6628435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4818282961625" x2="296.4818282961625" y1="566.6628435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4817282961625" x2="358.7818282961625" y1="566.6628435058594" y2="566.6628435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4817282961625" x2="358.7818282961625" y1="590.8314435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4817282961625" x2="296.4817282961625" y1="566.6628435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="358.7818282961625" x2="358.7818282961625" y1="566.6628435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="50.72202829616253" x2="99.21602829616245" y1="590.8314435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="50.72202829616253" x2="99.21602829616245" y1="615.0000435058594" y2="615.0000435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="50.72202829616253" x2="50.72202829616253" y1="590.8314435058594" y2="615.0000435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="99.21602829616245" y1="590.8314435058594" y2="615.0000435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="161.5161282961625" y1="590.8314435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="161.5161282961625" y1="615.0000435058594" y2="615.0000435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="99.21602829616245" y1="590.8314435058594" y2="615.0000435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5161282961625" x2="161.5161282961625" y1="590.8314435058594" y2="615.0000435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5160282961625" x2="234.1818282961625" y1="590.8314435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5160282961625" x2="234.1818282961625" y1="615.0000435058594" y2="615.0000435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5160282961625" x2="161.5160282961625" y1="590.8314435058594" y2="615.0000435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1818282961625" x2="234.1818282961625" y1="590.8314435058594" y2="615.0000435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1817282961625" x2="296.4818282961625" y1="590.8314435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1817282961625" x2="296.4818282961625" y1="615.0000435058594" y2="615.0000435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1817282961625" x2="234.1817282961625" y1="590.8314435058594" y2="615.0000435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4818282961625" x2="296.4818282961625" y1="590.8314435058594" y2="615.0000435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4817282961625" x2="358.7818282961625" y1="590.8314435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4817282961625" x2="358.7818282961625" y1="615.0000435058594" y2="615.0000435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4817282961625" x2="296.4817282961625" y1="590.8314435058594" y2="615.0000435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="358.7818282961625" x2="358.7818282961625" y1="590.8314435058594" y2="615.0000435058594"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,172.149,236.341) scale(1,1) translate(0,0)" writing-mode="lr" x="172.15" xml:space="preserve" y="240.84" zvalue="31">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,277.149,236.341) scale(1,1) translate(0,0)" writing-mode="lr" x="277.15" xml:space="preserve" y="240.84" zvalue="32">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,188.917,476.25) scale(1,1) translate(0,0)" writing-mode="lr" x="188.9166666666665" xml:space="preserve" y="480.7500000000001" zvalue="33">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,70.75,504) scale(1,1) translate(0,5.43454e-14)" writing-mode="lr" x="70.75" xml:space="preserve" y="508.5000000000001" zvalue="34">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,70.75,529.5) scale(1,1) translate(0,0)" writing-mode="lr" x="70.75" xml:space="preserve" y="534" zvalue="35">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,70.75,555) scale(1,1) translate(0,0)" writing-mode="lr" x="70.75" xml:space="preserve" y="559.5" zvalue="36">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,69.75,579.5) scale(1,1) translate(0,0)" writing-mode="lr" x="69.75" xml:space="preserve" y="584" zvalue="37">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,70.75,606) scale(1,1) translate(0,0)" writing-mode="lr" x="70.75" xml:space="preserve" y="610.5" zvalue="38">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.75,175.5) scale(1,1) translate(0,0)" writing-mode="lr" x="52.75" xml:space="preserve" y="181" zvalue="39">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232.75,175.5) scale(1,1) translate(0,0)" writing-mode="lr" x="232.75" xml:space="preserve" y="181" zvalue="40">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" x="125.921875" xml:space="preserve" y="473.265625" zvalue="49">110kV母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="125.921875" xml:space="preserve" y="490.265625" zvalue="49">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,67.0972,199.444) scale(1,1) translate(0,0)" writing-mode="lr" x="67.09999999999999" xml:space="preserve" y="203.94" zvalue="61">110kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1603.92,312.43) scale(1,1) translate(0,0)" writing-mode="lr" x="1603.92" xml:space="preserve" y="316.93" zvalue="64">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1555.16,668.202) scale(1,1) translate(0,0)" writing-mode="lr" x="1555.16" xml:space="preserve" y="672.7" zvalue="65">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1324,49.644) scale(1,1) translate(0,0)" writing-mode="lr" x="1324" xml:space="preserve" y="54.14" zvalue="66">110kV平河光伏电站线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1302.74,241.157) scale(1,1) translate(0,0)" writing-mode="lr" x="1302.74" xml:space="preserve" y="245.66" zvalue="68">152</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1344.37,293.257) scale(1,1) translate(-5.35434e-12,0)" writing-mode="lr" x="1344.37" xml:space="preserve" y="297.76" zvalue="79">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1304.22,301.94) scale(1,1) translate(0,0)" writing-mode="lr" x="1304.22" xml:space="preserve" y="306.44" zvalue="82">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1379.48,207.389) scale(1,1) translate(-3.34697e-12,0)" writing-mode="lr" x="1379.48" xml:space="preserve" y="211.89" zvalue="84">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1283.24,105.12) scale(1,1) translate(1.13225e-12,0)" writing-mode="lr" x="1283.24" xml:space="preserve" y="109.62" zvalue="85">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1344.02,170.727) scale(1,1) translate(2.22659e-12,0)" writing-mode="lr" x="1344.02" xml:space="preserve" y="175.23" zvalue="86">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,886.944,49.644) scale(1,1) translate(0,0)" writing-mode="lr" x="886.9400000000001" xml:space="preserve" y="54.14" zvalue="92">110kV备用</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,871.471,241.157) scale(1,1) translate(0,0)" writing-mode="lr" x="871.47" xml:space="preserve" y="245.66" zvalue="94">151</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,913.104,293.257) scale(1,1) translate(0,0)" writing-mode="lr" x="913.1" xml:space="preserve" y="297.76" zvalue="96">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,872.955,301.94) scale(1,1) translate(0,0)" writing-mode="lr" x="872.95" xml:space="preserve" y="306.44" zvalue="101">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,945.797,211.249) scale(1,1) translate(2.07653e-13,0)" writing-mode="lr" x="945.8" xml:space="preserve" y="215.75" zvalue="103">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,854.744,105.12) scale(1,1) translate(1.87783e-13,0)" writing-mode="lr" x="854.74" xml:space="preserve" y="109.62" zvalue="105">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,911.909,168.797) scale(1,1) translate(-2.01172e-13,0)" writing-mode="lr" x="911.91" xml:space="preserve" y="173.3" zvalue="107">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="106" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1487.8,413.964) scale(1,1) translate(0,0)" writing-mode="lr" x="1487.8" xml:space="preserve" y="418.46" zvalue="113">1901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="109" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1473.12,529.955) scale(1,1) translate(0,0)" writing-mode="lr" x="1473.12" xml:space="preserve" y="534.46" zvalue="115">110kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="114" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1431.74,430.044) scale(1,1) translate(0,0)" writing-mode="lr" x="1431.74" xml:space="preserve" y="434.54" zvalue="119">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1429.49,388.772) scale(1,1) translate(0,0)" writing-mode="lr" x="1429.49" xml:space="preserve" y="393.27" zvalue="121">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1162.95,585.72) scale(1,1) translate(0,-2.56559e-13)" writing-mode="lr" x="1162.95" xml:space="preserve" y="590.22" zvalue="125">1010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="134" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,994.014,386.025) scale(1,1) translate(0,-1.67677e-13)" writing-mode="lr" x="994.01" xml:space="preserve" y="390.53" zvalue="134">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="138" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,990.963,485.54) scale(1,1) translate(0,-2.11871e-13)" writing-mode="lr" x="990.96" xml:space="preserve" y="490.04" zvalue="138">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="142" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,956.682,514.948) scale(1,1) translate(0,0)" writing-mode="lr" x="956.6799999999999" xml:space="preserve" y="519.45" zvalue="141">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="146" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,966.54,386.143) scale(1,1) translate(0,0)" writing-mode="lr" x="966.54" xml:space="preserve" y="390.64" zvalue="143">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="145" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1038.27,483.545) scale(1,1) translate(0,-2.10985e-13)" writing-mode="lr" x="1038.27" xml:space="preserve" y="488.04" zvalue="145">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="152" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,978.641,658.543) scale(1,1) translate(1.07231e-13,-2.887e-13)" writing-mode="lr" x="978.64" xml:space="preserve" y="663.04" zvalue="150">301</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="159" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,912.149,580.586) scale(1,1) translate(0,6.19319e-14)" writing-mode="lr" x="912.1493551583767" xml:space="preserve" y="585.0858739632721" zvalue="156">110kV1号主变(50MVA)</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="166" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1083.2,621.329) scale(1,1) translate(-4.74599e-13,-2.72173e-13)" writing-mode="lr" x="1083.2" xml:space="preserve" y="625.83" zvalue="161">3010</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="11" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,690.001,988.229) scale(1,1) translate(0,0)" writing-mode="lr" x="690" xml:space="preserve" y="991.73" zvalue="164">35kV1号SVG</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,769.48,872.139) scale(1,1) translate(-1.68127e-13,0)" writing-mode="lr" x="769.48" xml:space="preserve" y="876.64" zvalue="168">361</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="107" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,771.168,733.758) scale(1,1) translate(1.68877e-13,0)" writing-mode="lr" x="771.17" xml:space="preserve" y="738.26" zvalue="174">351</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,705.981,789.755) scale(1,1) translate(0,0)" writing-mode="lr" x="705.98" xml:space="preserve" y="794.26" zvalue="178">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="125" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,760.518,820.372) scale(1,1) translate(4.18938e-13,0)" writing-mode="lr" x="760.52" xml:space="preserve" y="824.87" zvalue="181">8</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="170" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,689.485,839.543) scale(1,1) translate(0,0)" writing-mode="lr" x="689.48" xml:space="preserve" y="844.04" zvalue="189">87</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="175" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,995.68,933.719) scale(1,1) translate(0,-2.02462e-13)" writing-mode="lr" x="995.6795306038673" xml:space="preserve" y="938.2191550067216" zvalue="196">35kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="179" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1025.02,734.861) scale(1,1) translate(0,0)" writing-mode="lr" x="1025.02" xml:space="preserve" y="739.36" zvalue="201">3901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="185" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,871.99,922.309) scale(1,1) translate(0,0)" writing-mode="lr" x="871.99" xml:space="preserve" y="926.8099999999999" zvalue="206">35kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="188" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,893.859,733.758) scale(1,1) translate(-1.95585e-13,0)" writing-mode="lr" x="893.86" xml:space="preserve" y="738.26" zvalue="209">352</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="193" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,821.266,799.957) scale(1,1) translate(-1.80414e-13,0)" writing-mode="lr" x="821.27" xml:space="preserve" y="804.46" zvalue="214">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="214" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1145.3,738.144) scale(1,1) translate(-5.028e-13,0)" writing-mode="lr" x="1145.3" xml:space="preserve" y="742.64" zvalue="222">353</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="215" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1105.25,796.098) scale(1,1) translate(0,0)" writing-mode="lr" x="1105.25" xml:space="preserve" y="800.6" zvalue="224">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="204" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1120.43,880.629) scale(1,1) translate(0,0)" writing-mode="lr" x="1120.43" xml:space="preserve" y="885.13" zvalue="226">35kV平河Ⅰ回线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="211" stroke="rgb(255,255,255)" text-anchor="middle" x="1114.9140625" xml:space="preserve" y="900.9080337273991" zvalue="232">(#2、#3、#6、#16方</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="211" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1114.9140625" xml:space="preserve" y="916.9080337273991" zvalue="232">阵)</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="224" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1262.04,738.144) scale(1,1) translate(0,0)" writing-mode="lr" x="1262.04" xml:space="preserve" y="742.64" zvalue="243">354</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="223" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1221.99,796.098) scale(1,1) translate(0,0)" writing-mode="lr" x="1221.99" xml:space="preserve" y="800.6" zvalue="246">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="222" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1237.17,880.629) scale(1,1) translate(0,0)" writing-mode="lr" x="1237.17" xml:space="preserve" y="885.13" zvalue="248">35kV平河Ⅱ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="231" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1240.65,899.15) scale(1,1) translate(-2.65646e-13,0)" writing-mode="lr" x="1240.64900328897" xml:space="preserve" y="903.6503884426722" zvalue="250">(#1、#4、#5、</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="239" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1374.93,738.144) scale(1,1) translate(0,0)" writing-mode="lr" x="1374.93" xml:space="preserve" y="742.64" zvalue="259">355</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="238" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1334.88,796.098) scale(1,1) translate(0,0)" writing-mode="lr" x="1334.88" xml:space="preserve" y="800.6" zvalue="262">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="237" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1366.57,883.716) scale(1,1) translate(4.39455e-13,1.64424e-12)" writing-mode="lr" x="1366.573569041248" xml:space="preserve" y="888.2163754006169" zvalue="264">35kV平河Ⅲ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="250" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1363.78,904.294) scale(1,1) translate(0,6.93276e-13)" writing-mode="lr" x="1363.777751203629" xml:space="preserve" y="908.7936406285016" zvalue="272">(#10、#11、#12、</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="253" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1623.75,738.144) scale(1,1) translate(0,0)" writing-mode="lr" x="1623.75" xml:space="preserve" y="742.64" zvalue="275">357</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="252" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1583.22,795.326) scale(1,1) translate(0,0)" writing-mode="lr" x="1583.22" xml:space="preserve" y="799.83" zvalue="278">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="251" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1610.09,885.312) scale(1,1) translate(0,-1.93762e-13)" writing-mode="lr" x="1610.08845309004" xml:space="preserve" y="889.8122249826582" zvalue="280">35kV储能Ⅱ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="266" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1500.35,738.144) scale(1,1) translate(0,0)" writing-mode="lr" x="1500.35" xml:space="preserve" y="742.64" zvalue="289">356</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="265" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1460.3,796.098) scale(1,1) translate(0,0)" writing-mode="lr" x="1460.3" xml:space="preserve" y="800.6" zvalue="292">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="264" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1487.17,888.198) scale(1,1) translate(0,-1.93933e-13)" writing-mode="lr" x="1487.172529403761" xml:space="preserve" y="892.6981462501191" zvalue="294">35kV储能Ⅰ回线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="277" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1488.39,914.751) scale(1,1) translate(4.82426e-13,1.09649e-12)" writing-mode="lr" x="1488.390593404185" xml:space="preserve" y="919.2510510521859" zvalue="302">(目前为备用)</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="278" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1608.99,914.751) scale(1,1) translate(1.74198e-12,1.09649e-12)" writing-mode="lr" x="1608.990989485699" xml:space="preserve" y="919.2510510521859" zvalue="304">(目前为备用)</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="283" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1372.47,927.761) scale(1,1) translate(-5.82278e-13,-1.82887e-12)" writing-mode="lr" x="1372.467364448349" xml:space="preserve" y="932.2610588777751" zvalue="310">#13、#14、#15方阵)</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="284" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1236.05,921.852) scale(1,1) translate(0,0)" writing-mode="lr" x="1236.051999955981" xml:space="preserve" y="926.3516394697806" zvalue="312">#7、#8、#9方阵)</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="110" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,693.189,889.675) scale(1,1) translate(0,0)" writing-mode="lr" x="693.1900000000001" xml:space="preserve" y="894.1799999999999" zvalue="320">R</text>
  <path d="M 711.237 893.989 L 711.237 916.122 L 746.991 916.122" fill="none" id="169" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="326"/>
  <path d="M 716.344 834.398 L 716.344 818.507 L 745.289 818.507" fill="none" id="173" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="327"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="268" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1030.22,429.776) scale(1,1) translate(0,0)" writing-mode="lr" x="1030.22" xml:space="preserve" y="434.28" zvalue="496">101</text>
  <rect fill="none" height="38" id="293" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,1107,252) scale(1,1) translate(0,0)" width="132" x="1041" y="233" zvalue="523"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="293" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1107,252) scale(1,1) translate(0,0)" writing-mode="lr" x="1107" xml:space="preserve" y="258" zvalue="523">设计容量50MW</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="47.51" y="226.25" zvalue="42"/></g>
  <g href="新能源AVC AGC 控制.svg"><rect fill-opacity="0" height="24" width="72.88" x="47.29" y="282.57" zvalue="540"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="22">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="22" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,158.321,175.25) scale(1,1) translate(0,0)" writing-mode="lr" x="158.47" xml:space="preserve" y="181.57" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127537672196" ObjectName=""/>
   </metadata>
  </g>
  <g id="1035">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="1035" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,340.321,174.25) scale(1,1) translate(0,0)" writing-mode="lr" x="340.47" xml:space="preserve" y="180.57" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127537737732" ObjectName=""/>
   </metadata>
  </g>
  <g id="36">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="36" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,193.321,509.167) scale(1,1) translate(0,0)" writing-mode="lr" x="193.04" xml:space="preserve" y="513.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127520436228" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="37">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="37" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,193.321,530.667) scale(1,1) translate(0,0)" writing-mode="lr" x="193.04" xml:space="preserve" y="535.33" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127520174084" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="38">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="38" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,193.321,553.167) scale(1,1) translate(0,-1.20496e-13)" writing-mode="lr" x="193.04" xml:space="preserve" y="557.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127520239620" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="39">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,193.321,577.667) scale(1,1) translate(0,0)" writing-mode="lr" x="193.04" xml:space="preserve" y="582.33" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127520305156" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="41">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,158.321,200.758) scale(1,1) translate(0,-4.25251e-14)" writing-mode="lr" x="157.96" xml:space="preserve" y="205.4" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127520043012" ObjectName="F"/>
   </metadata>
  </g>
  <g id="52">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="52" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,130.889,507.167) scale(1,1) translate(-2.30926e-14,0)" writing-mode="lr" x="131.01" xml:space="preserve" y="512.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127519911940" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="98">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="98" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,193.321,603.5) scale(1,1) translate(0,0)" writing-mode="lr" x="193.04" xml:space="preserve" y="608.17" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127520632836" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="131">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="131" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,130.889,531.25) scale(1,1) translate(-2.30926e-14,0)" writing-mode="lr" x="131.01" xml:space="preserve" y="536.16" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127519649796" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="132">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="132" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,130.889,555.333) scale(1,1) translate(-2.30926e-14,-1.20977e-13)" writing-mode="lr" x="131.01" xml:space="preserve" y="560.24" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127519715332" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="150">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="150" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,130.889,579.417) scale(1,1) translate(-2.30926e-14,0)" writing-mode="lr" x="131.01" xml:space="preserve" y="584.33" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127519780868" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="157">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="157" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,130.889,603.5) scale(1,1) translate(-2.30926e-14,0)" writing-mode="lr" x="131.01" xml:space="preserve" y="608.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127520108548" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="54">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="54" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1462.97,56.4844) scale(1,1) translate(-3.15203e-13,3.51722e-14)" writing-mode="lr" x="1421.36" xml:space="preserve" y="60.85" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127520698372" ObjectName="P"/>
   </metadata>
  </g>
  <g id="68">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="68" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1462.97,77.0209) scale(1,1) translate(4.72805e-13,5.34123e-14)" writing-mode="lr" x="1421.36" xml:space="preserve" y="81.39" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127520763908" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="69">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="69" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1462.97,98.9357) scale(1,1) translate(-3.15203e-13,7.28765e-14)" writing-mode="lr" x="1421.36" xml:space="preserve" y="103.3" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127520829444" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="128">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="128" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,724.383,316.244) scale(1,1) translate(2.26807e-13,2.65885e-13)" writing-mode="lr" x="682.78" xml:space="preserve" y="320.61" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127519911940" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="177">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="177" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,727.671,680.399) scale(1,1) translate(2.27903e-13,5.8932e-13)" writing-mode="lr" x="686.0599999999999" xml:space="preserve" y="684.77" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127520436228" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="180">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="180" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1127.2,396.796) scale(1,1) translate(-2.38935e-13,-1.68715e-13)" writing-mode="lr" x="1078.2" xml:space="preserve" y="402.66" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127524892676" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="186">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="186" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1127.2,422.846) scale(1,1) translate(-2.38935e-13,0)" writing-mode="lr" x="1078.2" xml:space="preserve" y="428.71" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127524958212" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="190">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="190" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,794.237,595.839) scale(1,1) translate(-1.65002e-13,0)" writing-mode="lr" x="745.23" xml:space="preserve" y="601.7" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127525023748" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="202">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="202" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,794.237,620.816) scale(1,1) translate(-1.65002e-13,0)" writing-mode="lr" x="745.23" xml:space="preserve" y="626.6799999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127525089284" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="205">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="205" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1129.35,456.4) scale(1,1) translate(1.19705e-13,0)" writing-mode="lr" x="1080.34" xml:space="preserve" y="462.27" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127525154820" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="206">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="206" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,792.093,653.298) scale(1,1) translate(-1.64526e-13,2.82625e-13)" writing-mode="lr" x="743.09" xml:space="preserve" y="659.16" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127525482500" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="207">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="207" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,797.213,964.832) scale(1,1) translate(8.36882e-14,8.41947e-13)" writing-mode="lr" x="755.61" xml:space="preserve" y="969.2" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127526924292" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="208">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="208" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,798.936,988.126) scale(1,1) translate(8.38795e-14,8.62636e-13)" writing-mode="lr" x="757.33" xml:space="preserve" y="992.49" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127526989828" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="209">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="209" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1102.73,941.055) scale(1,1) translate(1.17607e-13,8.20828e-13)" writing-mode="lr" x="1061.12" xml:space="preserve" y="945.42" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127530332164" ObjectName="P"/>
   </metadata>
  </g>
  <g id="210">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="210" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1104.48,962.092) scale(1,1) translate(1.17802e-13,8.39514e-13)" writing-mode="lr" x="1062.88" xml:space="preserve" y="966.46" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127530397700" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="213">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="213" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1104.11,990.773) scale(1,1) translate(1.1776e-13,8.64987e-13)" writing-mode="lr" x="1062.5" xml:space="preserve" y="995.14" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127530463236" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="216">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="216" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1224.48,941.055) scale(1,1) translate(-2.62249e-13,8.20828e-13)" writing-mode="lr" x="1182.88" xml:space="preserve" y="945.42" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127531380740" ObjectName="P"/>
   </metadata>
  </g>
  <g id="220">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="220" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1229.62,962.092) scale(1,1) translate(-2.6339e-13,8.39514e-13)" writing-mode="lr" x="1188.01" xml:space="preserve" y="966.46" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127531446276" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="232">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="232" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1229.52,990.773) scale(1,1) translate(-2.63368e-13,8.64987e-13)" writing-mode="lr" x="1187.91" xml:space="preserve" y="995.14" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127531511812" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="241">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="241" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1345.63,941.055) scale(1,1) translate(-2.89151e-13,8.20828e-13)" writing-mode="lr" x="1304.03" xml:space="preserve" y="945.42" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127532953604" ObjectName="P"/>
   </metadata>
  </g>
  <g id="245">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="245" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1346.14,962.092) scale(1,1) translate(-2.89262e-13,8.39514e-13)" writing-mode="lr" x="1304.53" xml:space="preserve" y="966.46" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127533019140" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="255">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="255" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1349.39,990.773) scale(1,1) translate(-2.89985e-13,8.64987e-13)" writing-mode="lr" x="1307.79" xml:space="preserve" y="995.14" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127533084676" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="300">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="300" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1474.32,933.15) scale(1,1) translate(-9.5437e-13,-2.04883e-13)" writing-mode="lr" x="1434.43" xml:space="preserve" y="937.5700000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127534526468" ObjectName="P"/>
   </metadata>
  </g>
  <g id="301">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="301" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1474.32,958.15) scale(1,1) translate(-9.5437e-13,-2.10434e-13)" writing-mode="lr" x="1434.43" xml:space="preserve" y="962.5700000000001" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127534592004" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="302">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="302" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1474.32,983.15) scale(1,1) translate(-9.5437e-13,-2.15985e-13)" writing-mode="lr" x="1434.43" xml:space="preserve" y="987.5700000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127534657540" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="303">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="303" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1600.62,933.15) scale(1,1) translate(-2.07701e-12,-2.04883e-13)" writing-mode="lr" x="1560.73" xml:space="preserve" y="937.5700000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127536099332" ObjectName="P"/>
   </metadata>
  </g>
  <g id="304">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="304" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1597.23,957.378) scale(1,1) translate(2.0725e-12,-2.10263e-13)" writing-mode="lr" x="1557.35" xml:space="preserve" y="961.8" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127536164868" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="305">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="305" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1597.23,982.378) scale(1,1) translate(2.0725e-12,-2.15814e-13)" writing-mode="lr" x="1557.35" xml:space="preserve" y="986.8" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127536230404" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="306">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="306" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,868.631,939.759) scale(1,1) translate(0,-2.0635e-13)" writing-mode="lr" x="828.75" xml:space="preserve" y="944.1799999999999" zvalue="1">P:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127528693764" ObjectName="P"/>
   </metadata>
  </g>
  <g id="307">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="307" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,868.631,964.759) scale(1,1) translate(0,-2.11902e-13)" writing-mode="lr" x="828.75" xml:space="preserve" y="969.1799999999999" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127528759300" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="308">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="308" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,868.631,989.759) scale(1,1) translate(0,-2.17453e-13)" writing-mode="lr" x="828.75" xml:space="preserve" y="994.1799999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127528824836" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="897">
   <use height="30" transform="rotate(0,308.661,235.143) scale(0.708333,0.665547) translate(122.721,113.148)" width="30" x="298.04" xlink:href="#State:红绿圆(方形)_0" y="225.16" zvalue="46"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374895218691" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,308.661,235.143) scale(0.708333,0.665547) translate(122.721,113.148)" width="30" x="298.04" y="225.16"/></g>
  <g id="21">
   <use height="30" transform="rotate(0,213.036,235.143) scale(0.708333,0.665547) translate(83.3456,113.148)" width="30" x="202.41" xlink:href="#State:红绿圆(方形)_0" y="225.16" zvalue="47"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562953506062344" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,213.036,235.143) scale(0.708333,0.665547) translate(83.3456,113.148)" width="30" x="202.41" y="225.16"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="35">
   <path class="kv110" d="M 719.34 342.19 L 1601.95 342.19" stroke-width="4" zvalue="63"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674260418564" ObjectName="110kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674260418564"/></metadata>
  <path d="M 719.34 342.19 L 1601.95 342.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="kv35" d="M 693.02 699.78 L 1649.75 699.78" stroke-width="4" zvalue="64"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674260484100" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674260484100"/></metadata>
  <path d="M 693.02 699.78 L 1649.75 699.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="45">
   <use class="kv110" height="25" transform="rotate(0,1333.41,78.9133) scale(1.39552,1.3698) translate(-372.378,-16.6816)" width="28" x="1313.869760234625" xlink:href="#ACLineSegment:带避雷器线路PT的线路_0" y="61.79079958934443" zvalue="65"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249326616579" ObjectName="110kV平河光伏电站线"/>
   <cge:TPSR_Ref TObjectID="8444249326616579_5066549595734018"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1333.41,78.9133) scale(1.39552,1.3698) translate(-372.378,-16.6816)" width="28" x="1313.869760234625" y="61.79079958934443"/></g>
 </g>
 <g id="BreakerClass">
  <g id="48">
   <use class="kv110" height="20" transform="rotate(0,1326.86,239.228) scale(1.4472,1.30248) translate(-407.78,-52.5326)" width="10" x="1319.62179523766" xlink:href="#Breaker:开关_0" y="226.2030159642017" zvalue="67"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924576673796" ObjectName="110kV平河光伏电站线152断路器"/>
   <cge:TPSR_Ref TObjectID="6473924576673796"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1326.86,239.228) scale(1.4472,1.30248) translate(-407.78,-52.5326)" width="10" x="1319.62179523766" y="226.2030159642017"/></g>
  <g id="103">
   <use class="kv110" height="20" transform="rotate(0,895.591,239.228) scale(1.4472,1.30248) translate(-274.513,-52.5326)" width="10" x="888.354778850165" xlink:href="#Breaker:开关_0" y="226.2030159642017" zvalue="93"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924576739332" ObjectName="备用151断路器"/>
   <cge:TPSR_Ref TObjectID="6473924576739332"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,895.591,239.228) scale(1.4472,1.30248) translate(-274.513,-52.5326)" width="10" x="888.354778850165" y="226.2030159642017"/></g>
  <g id="151">
   <use class="kv35" height="20" transform="rotate(0,1005.51,658.43) scale(1.75856,1.54419) translate(-429.934,-226.596)" width="10" x="996.7128166761785" xlink:href="#Breaker:手车开关_0" y="642.9878793940439" zvalue="149"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924576870404" ObjectName="110kV1号主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473924576870404"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1005.51,658.43) scale(1.75856,1.54419) translate(-429.934,-226.596)" width="10" x="996.7128166761785" y="642.9878793940439"/></g>
  <g id="57">
   <use class="kv35" height="20" transform="rotate(0,748.013,873.104) scale(1.4472,1.30248) translate(-228.909,-199.742)" width="10" x="740.7766780552702" xlink:href="#Breaker:开关_0" y="860.0786977686402" zvalue="167"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924576935940" ObjectName="35kV1号SVG361断路器"/>
   <cge:TPSR_Ref TObjectID="6473924576935940"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,748.013,873.104) scale(1.4472,1.30248) translate(-228.909,-199.742)" width="10" x="740.7766780552702" y="860.0786977686402"/></g>
  <g id="64">
   <use class="kv35" height="20" transform="rotate(0,748.013,734.723) scale(2.12257,2.12257) translate(-389.99,-377.349)" width="10" x="737.3998669649877" xlink:href="#Breaker:小车断路器_0" y="713.4975285424615" zvalue="173"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924577001476" ObjectName="35kV1号SVG351断路器"/>
   <cge:TPSR_Ref TObjectID="6473924577001476"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,748.013,734.723) scale(2.12257,2.12257) translate(-389.99,-377.349)" width="10" x="737.3998669649877" y="713.4975285424615"/></g>
  <g id="187">
   <use class="kv35" height="20" transform="rotate(0,868.292,734.723) scale(2.12257,2.12257) translate(-453.603,-377.349)" width="10" x="857.6793110204225" xlink:href="#Breaker:小车断路器_0" y="713.4975285424614" zvalue="208"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924577067012" ObjectName="35kV1号站用变352断路器"/>
   <cge:TPSR_Ref TObjectID="6473924577067012"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,868.292,734.723) scale(2.12257,2.12257) translate(-453.603,-377.349)" width="10" x="857.6793110204225" y="713.4975285424614"/></g>
  <g id="199">
   <use class="kv35" height="20" transform="rotate(0,1118.79,734.723) scale(2.12257,2.12257) translate(-586.082,-377.349)" width="10" x="1108.172681070995" xlink:href="#Breaker:小车断路器_0" y="713.4975283971812" zvalue="221"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924577132548" ObjectName="35kV平河Ⅰ回线353断路器"/>
   <cge:TPSR_Ref TObjectID="6473924577132548"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1118.79,734.723) scale(2.12257,2.12257) translate(-586.082,-377.349)" width="10" x="1108.172681070995" y="713.4975283971812"/></g>
  <g id="236">
   <use class="kv35" height="20" transform="rotate(0,1235.53,734.723) scale(2.12257,2.12257) translate(-647.823,-377.349)" width="10" x="1224.913864477901" xlink:href="#Breaker:小车断路器_0" y="713.4975283971812" zvalue="242"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924577198084" ObjectName="35kV平河Ⅱ回线354断路器"/>
   <cge:TPSR_Ref TObjectID="6473924577198084"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1235.53,734.723) scale(2.12257,2.12257) translate(-647.823,-377.349)" width="10" x="1224.913864477901" y="713.4975283971812"/></g>
  <g id="249">
   <use class="kv35" height="20" transform="rotate(0,1348.41,734.723) scale(2.12257,2.12257) translate(-707.523,-377.349)" width="10" x="1337.795835210198" xlink:href="#Breaker:小车断路器_0" y="713.4975283971812" zvalue="258"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924577263620" ObjectName="35kV平河Ⅲ回线355断路器"/>
   <cge:TPSR_Ref TObjectID="6473924577263620"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1348.41,734.723) scale(2.12257,2.12257) translate(-707.523,-377.349)" width="10" x="1337.795835210198" y="713.4975283971812"/></g>
  <g id="263">
   <use class="kv35" height="20" transform="rotate(0,1597.23,734.723) scale(2.12257,2.12257) translate(-839.119,-377.349)" width="10" x="1586.619616012578" xlink:href="#Breaker:小车断路器_0" y="713.4975285812028" zvalue="274"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924577394692" ObjectName="35kV储能Ⅱ回线357断路器"/>
   <cge:TPSR_Ref TObjectID="6473924577394692"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1597.23,734.723) scale(2.12257,2.12257) translate(-839.119,-377.349)" width="10" x="1586.619616012578" y="713.4975285812028"/></g>
  <g id="276">
   <use class="kv35" height="20" transform="rotate(0,1473.83,734.723) scale(2.12257,2.12257) translate(-773.857,-377.349)" width="10" x="1463.220247134972" xlink:href="#Breaker:小车断路器_0" y="713.4975283971812" zvalue="288"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924577329156" ObjectName="35kV储能Ⅰ回线356断路器"/>
   <cge:TPSR_Ref TObjectID="6473924577329156"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1473.83,734.723) scale(2.12257,2.12257) translate(-773.857,-377.349)" width="10" x="1463.220247134972" y="713.4975283971812"/></g>
  <g id="55">
   <use class="kv110" height="20" transform="rotate(0,1005.13,428.812) scale(1.4472,1.30248) translate(-308.363,-96.5608)" width="10" x="997.8967225010582" xlink:href="#Breaker:开关_0" y="415.7868386043419" zvalue="495"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924576804868" ObjectName="110kV1号主变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473924576804868"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1005.13,428.812) scale(1.4472,1.30248) translate(-308.363,-96.5608)" width="10" x="997.8967225010582" y="415.7868386043419"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="65">
   <use class="kv110" height="30" transform="rotate(0,1326.87,289.751) scale(0.932643,0.683938) translate(95.3236,129.159)" width="15" x="1319.877611689109" xlink:href="#Disconnector:刀闸_0" y="279.49231097942" zvalue="78"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450182316037" ObjectName="110kV平河光伏电站线1521隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450182316037"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1326.87,289.751) scale(0.932643,0.683938) translate(95.3236,129.159)" width="15" x="1319.877611689109" y="279.49231097942"/></g>
  <g id="76">
   <use class="kv110" height="30" transform="rotate(0,1326.17,168.636) scale(1.1256,0.825443) translate(-147.043,33.0434)" width="15" x="1317.728972909234" xlink:href="#Disconnector:刀闸_0" y="156.2547862369235" zvalue="85"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450182774789" ObjectName="110kV平河光伏电站线1526隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450182774789"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1326.17,168.636) scale(1.1256,0.825443) translate(-147.043,33.0434)" width="15" x="1317.728972909234" y="156.2547862369235"/></g>
  <g id="102">
   <use class="kv110" height="30" transform="rotate(0,895.605,289.751) scale(0.932643,0.683938) translate(64.1768,129.159)" width="15" x="888.6105953016144" xlink:href="#Disconnector:刀闸_0" y="279.49231097942" zvalue="95"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450183299077" ObjectName="备用1511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450183299077"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,895.605,289.751) scale(0.932643,0.683938) translate(64.1768,129.159)" width="15" x="888.6105953016144" y="279.49231097942"/></g>
  <g id="95">
   <use class="kv110" height="30" transform="rotate(0,894.904,167.672) scale(1.1256,0.825443) translate(-98.9184,32.8393)" width="15" x="886.4619565217392" xlink:href="#Disconnector:刀闸_0" y="155.2899830682715" zvalue="106"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450182840325" ObjectName="备用1516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450182840325"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,894.904,167.672) scale(1.1256,0.825443) translate(-98.9184,32.8393)" width="15" x="886.4619565217392" y="155.2899830682715"/></g>
  <g id="105">
   <use class="kv110" height="30" transform="rotate(0,1458.38,413.035) scale(1.53654,1.12679) translate(-505.221,-44.5757)" width="15" x="1446.851796980508" xlink:href="#Disconnector:刀闸_0" y="396.1334407243916" zvalue="112"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450183430149" ObjectName="110kV母线电压互感器1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450183430149"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1458.38,413.035) scale(1.53654,1.12679) translate(-505.221,-44.5757)" width="15" x="1446.851796980508" y="396.1334407243916"/></g>
  <g id="133">
   <use class="kv110" height="30" transform="rotate(0,1006.69,387.082) scale(1.17352,0.86058) translate(-147.549,60.6186)" width="15" x="997.8863349366156" xlink:href="#Disconnector:刀闸_0" y="374.1728946087964" zvalue="133"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450183954437" ObjectName="110kV1号主变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450183954437"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1006.69,387.082) scale(1.17352,0.86058) translate(-147.549,60.6186)" width="15" x="997.8863349366156" y="374.1728946087964"/></g>
  <g id="137">
   <use class="kv110" height="30" transform="rotate(0,1005.4,486.596) scale(1.17352,0.86058) translate(-147.358,76.7406)" width="15" x="996.5954648501348" xlink:href="#Disconnector:刀闸_0" y="473.6872430938672" zvalue="137"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450184019973" ObjectName="110kV1号主变110kV侧1016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450184019973"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1005.4,486.596) scale(1.17352,0.86058) translate(-147.358,76.7406)" width="15" x="996.5954648501348" y="473.6872430938672"/></g>
  <g id="165">
   <use class="kv35" height="30" transform="rotate(0,1057.15,618.865) scale(1.17352,0.86058) translate(-155.01,98.1691)" width="15" x="1048.347620135413" xlink:href="#Disconnector:刀闸_0" y="605.956148629969" zvalue="160"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450184609797" ObjectName="110kV1号主变35kV侧3010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450184609797"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1057.15,618.865) scale(1.17352,0.86058) translate(-155.01,98.1691)" width="15" x="1048.347620135413" y="605.956148629969"/></g>
  <g id="124">
   <use class="kv35" height="30" transform="rotate(0,747.951,818.307) scale(0.699651,0.582507) translate(318.832,580.233)" width="15" x="742.7039090007128" xlink:href="#Disconnector:刀闸_0" y="809.5694652171674" zvalue="180"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450185068549" ObjectName="35kV1号SVG3518隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450185068549"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,747.951,818.307) scale(0.699651,0.582507) translate(318.832,580.233)" width="15" x="742.7039090007128" y="809.5694652171674"/></g>
  <g id="178">
   <use class="kv35" height="36" transform="rotate(0,991.249,734.723) scale(1.24046,1.24046) translate(-190.469,-138.096)" width="14" x="982.5657575521026" xlink:href="#Disconnector:联体小车刀闸2_0" y="712.3948958239403" zvalue="200"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450185396229" ObjectName="35kV母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450185396229"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,991.249,734.723) scale(1.24046,1.24046) translate(-190.469,-138.096)" width="14" x="982.5657575521026" y="712.3948958239403"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="67">
   <path class="kv110" d="M 1326.95 251.67 L 1326.95 279.83" stroke-width="1" zvalue="79"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="48@1" LinkObjectIDznd="65@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1326.95 251.67 L 1326.95 279.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="kv110" d="M 1326.93 299.84 L 1326.93 342.19" stroke-width="1" zvalue="80"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@1" LinkObjectIDznd="35@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1326.93 299.84 L 1326.93 342.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv110" d="M 1308.32 318.39 L 1326.93 318.39" stroke-width="1" zvalue="82"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="71@0" LinkObjectIDznd="70" MaxPinNum="2"/>
   </metadata>
  <path d="M 1308.32 318.39 L 1326.93 318.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv110" d="M 1324.88 93.95 L 1324.88 156.66" stroke-width="1" zvalue="86"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@0" LinkObjectIDznd="76@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1324.88 93.95 L 1324.88 156.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="kv110" d="M 1326.24 180.81 L 1326.24 226.77" stroke-width="1" zvalue="87"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="76@1" LinkObjectIDznd="48@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1326.24 180.81 L 1326.24 226.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="82">
   <path class="kv110" d="M 1298.67 122.53 L 1324.88 122.53" stroke-width="1" zvalue="88"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="75@0" LinkObjectIDznd="78" MaxPinNum="2"/>
   </metadata>
  <path d="M 1298.67 122.53 L 1324.88 122.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="kv110" d="M 1343.54 202.52 L 1326.24 202.52" stroke-width="1" zvalue="89"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="74@0" LinkObjectIDznd="79" MaxPinNum="2"/>
   </metadata>
  <path d="M 1343.54 202.52 L 1326.24 202.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv110" d="M 895.69 251.67 L 895.69 279.83" stroke-width="1" zvalue="97"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="103@1" LinkObjectIDznd="102@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 895.69 251.67 L 895.69 279.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="94">
   <path class="kv110" d="M 893.42 95.66 L 893.42 155.7" stroke-width="1" zvalue="108"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="104@0" LinkObjectIDznd="95@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 893.42 95.66 L 893.42 155.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="kv110" d="M 894.97 179.84 L 894.97 226.77" stroke-width="1" zvalue="109"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="95@1" LinkObjectIDznd="103@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 894.97 179.84 L 894.97 226.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="kv110" d="M 867.41 122.53 L 893.42 122.53" stroke-width="1" zvalue="110"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="96@0" LinkObjectIDznd="94" MaxPinNum="2"/>
   </metadata>
  <path d="M 867.41 122.53 L 893.42 122.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="kv110" d="M 912.27 202.52 L 894.97 202.52" stroke-width="1" zvalue="111"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="97@0" LinkObjectIDznd="93" MaxPinNum="2"/>
   </metadata>
  <path d="M 912.27 202.52 L 894.97 202.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="111">
   <path class="kv110" d="M 1459.86 470.31 L 1459.86 429.65" stroke-width="1" zvalue="116"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@0" LinkObjectIDznd="105@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1459.86 470.31 L 1459.86 429.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="kv110" d="M 1458.51 396.69 L 1458.51 342.19" stroke-width="1" zvalue="117"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="105@0" LinkObjectIDznd="35@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1458.51 396.69 L 1458.51 342.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="kv110" d="M 1438.11 451.43 L 1459.86 451.52" stroke-width="1" zvalue="121"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="113@0" LinkObjectIDznd="111" MaxPinNum="2"/>
   </metadata>
  <path d="M 1438.11 451.43 L 1459.86 451.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="118">
   <path class="kv110" d="M 1437.79 372.1 L 1458.51 372.1" stroke-width="1" zvalue="122"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="115@0" LinkObjectIDznd="112" MaxPinNum="2"/>
   </metadata>
  <path d="M 1437.79 372.1 L 1458.51 372.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="136">
   <path class="kv110" d="M 1006.79 374.6 L 1006.79 342.19" stroke-width="1" zvalue="135"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="133@0" LinkObjectIDznd="35@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1006.79 374.6 L 1006.79 342.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="139">
   <path class="kv110" d="M 1005.47 538.7 L 1005.47 499.28" stroke-width="1" zvalue="138"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@0" LinkObjectIDznd="137@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1005.47 538.7 L 1005.47 499.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="147">
   <path class="kv110" d="M 982.68 362.03 L 1006.79 362.03" stroke-width="1" zvalue="145"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="143@0" LinkObjectIDznd="136" MaxPinNum="2"/>
   </metadata>
  <path d="M 982.68 362.03 L 1006.79 362.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="148">
   <path class="kv110" d="M 1023.31 461.66 L 1005.5 461.8" stroke-width="1" zvalue="146"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="144@0" LinkObjectIDznd="279" MaxPinNum="2"/>
   </metadata>
  <path d="M 1023.31 461.66 L 1005.5 461.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="kv110" d="M 994.76 516.06 L 1005.47 516.06" stroke-width="1" zvalue="147"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="141@0" LinkObjectIDznd="139" MaxPinNum="2"/>
   </metadata>
  <path d="M 994.76 516.06 L 1005.47 516.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="153">
   <path class="kv35" d="M 1005.45 605.42 L 1005.51 644.15" stroke-width="1" zvalue="150"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@1" LinkObjectIDznd="151@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1005.45 605.42 L 1005.51 644.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="154">
   <path class="kv35" d="M 1005.51 672.33 L 1005.51 699.78" stroke-width="1" zvalue="151"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="151@1" LinkObjectIDznd="43@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1005.51 672.33 L 1005.51 699.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="156">
   <path class="kv35" d="M 991.86 629.57 L 1005.49 629.57" stroke-width="1" zvalue="153"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="155@0" LinkObjectIDznd="153" MaxPinNum="2"/>
   </metadata>
  <path d="M 991.86 629.57 L 1005.49 629.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="160">
   <path class="kv110" d="M 1005.5 554.34 L 1125.16 554.34 L 1125.16 572.46" stroke-width="1" zvalue="156"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@2" LinkObjectIDznd="121@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1005.5 554.34 L 1125.16 554.34 L 1125.16 572.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="167">
   <path class="kv35" d="M 1005.45 586.81 L 1057.25 586.81 L 1057.25 606.38" stroke-width="1" zvalue="161"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@3" LinkObjectIDznd="165@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1005.45 586.81 L 1057.25 586.81 L 1057.25 606.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="168">
   <path class="kv35" d="M 1057.15 644.65 L 1057.22 631.55" stroke-width="1" zvalue="162"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@0" LinkObjectIDznd="165@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1057.15 644.65 L 1057.22 631.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="kv35" d="M 747.51 790.37 L 748.01 790.68" stroke-width="1" zvalue="171"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@0" LinkObjectIDznd="127" MaxPinNum="2"/>
   </metadata>
  <path d="M 747.51 790.37 L 748.01 790.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="119">
   <path class="kv35" d="M 748.01 715.09 L 748.01 699.78" stroke-width="1" zvalue="175"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="64@0" LinkObjectIDznd="43@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 748.01 715.09 L 748.01 699.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="120">
   <path class="kv35" d="M 723.9 774.43 L 723.9 763.77 L 748.01 763.77" stroke-width="1" zvalue="178"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60@0" LinkObjectIDznd="127" MaxPinNum="2"/>
   </metadata>
  <path d="M 723.9 774.43 L 723.9 763.77 L 748.01 763.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="123">
   <path class="kv35" d="M 775.38 777.4 L 775.38 763.03 L 748.01 763.03" stroke-width="1" zvalue="179"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="51@0" LinkObjectIDznd="127" MaxPinNum="2"/>
   </metadata>
  <path d="M 775.38 777.4 L 775.38 763.03 L 748.01 763.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="126">
   <path class="kv35" d="M 747.96 860.64 L 747.99 826.9" stroke-width="1" zvalue="181"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="57@0" LinkObjectIDznd="124@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 747.96 860.64 L 747.99 826.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="kv35" d="M 748.01 809.86 L 748.01 753.83" stroke-width="1" zvalue="182"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="124@0" LinkObjectIDznd="64@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 748.01 809.86 L 748.01 753.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="163">
   <path class="kv35" d="M 730.93 836.96 L 747.99 836.96" stroke-width="1" zvalue="189"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="162@0" LinkObjectIDznd="126" MaxPinNum="2"/>
   </metadata>
  <path d="M 730.93 836.96 L 747.99 836.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="171">
   <path class="kv35" d="M 748.11 885.54 L 748.11 933.45" stroke-width="1" zvalue="192"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="57@1" LinkObjectIDznd="47@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 748.11 885.54 L 748.11 933.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="172">
   <path class="kv35" d="M 760.33 901.08 L 748.11 901.08" stroke-width="1" zvalue="193"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@0" LinkObjectIDznd="171" MaxPinNum="2"/>
   </metadata>
  <path d="M 760.33 901.08 L 748.11 901.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="181">
   <path class="kv35" d="M 991.25 846.99 L 991.25 755.81" stroke-width="1" zvalue="202"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="174@0" LinkObjectIDznd="178@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 991.25 846.99 L 991.25 755.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="182">
   <path class="kv35" d="M 991.25 713.64 L 991.25 699.78" stroke-width="1" zvalue="203"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="178@0" LinkObjectIDznd="43@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 991.25 713.64 L 991.25 699.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="183">
   <path class="kv35" d="M 966.06 826.67 L 966.06 800.52 L 991.25 800.52" stroke-width="1" zvalue="204"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="176@0" LinkObjectIDznd="181" MaxPinNum="2"/>
   </metadata>
  <path d="M 966.06 826.67 L 966.06 800.52 L 991.25 800.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="189">
   <path class="kv35" d="M 868.29 844.59 L 868.29 753.83" stroke-width="1" zvalue="209"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="184@0" LinkObjectIDznd="187@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 868.29 844.59 L 868.29 753.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="194">
   <path class="kv35" d="M 840.64 782.15 L 840.64 762.01 L 868.29 762.01" stroke-width="1" zvalue="214"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@0" LinkObjectIDznd="189" MaxPinNum="2"/>
   </metadata>
  <path d="M 840.64 782.15 L 840.64 762.01 L 868.29 762.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="195">
   <path class="kv35" d="M 868.29 761.04 L 895.98 761.04 L 895.98 785.12" stroke-width="1" zvalue="215"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="189" LinkObjectIDznd="192@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 868.29 761.04 L 895.98 761.04 L 895.98 785.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="197">
   <path class="kv35" d="M 868.11 805.81 L 868.11 805.42" stroke-width="1" zvalue="218"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="196@0" LinkObjectIDznd="189" MaxPinNum="2"/>
   </metadata>
  <path d="M 868.11 805.81 L 868.11 805.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="198">
   <path class="kv35" d="M 868.29 715.09 L 868.29 699.78" stroke-width="1" zvalue="219"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="187@0" LinkObjectIDznd="43@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 868.29 715.09 L 868.29 699.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="217">
   <path class="kv35" d="M 1092.45 782.15 L 1092.45 770.74 L 1119.27 770.7" stroke-width="1" zvalue="236"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="200@0" LinkObjectIDznd="219" MaxPinNum="2"/>
   </metadata>
  <path d="M 1092.45 782.15 L 1092.45 770.74 L 1119.27 770.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="218">
   <path class="kv35" d="M 1147.8 785.12 L 1147.8 771.27 L 1119.27 771.23" stroke-width="1" zvalue="237"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="201@0" LinkObjectIDznd="219" MaxPinNum="2"/>
   </metadata>
  <path d="M 1147.8 785.12 L 1147.8 771.27 L 1119.27 771.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="219">
   <path class="kv35" d="M 1119.27 852.75 L 1119.27 753.83" stroke-width="1" zvalue="238"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="203@0" LinkObjectIDznd="199@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1119.27 852.75 L 1119.27 753.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="221">
   <path class="kv35" d="M 1119.06 815.67 L 1119.06 815.27" stroke-width="1" zvalue="240"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="212@0" LinkObjectIDznd="219" MaxPinNum="2"/>
   </metadata>
  <path d="M 1119.06 815.67 L 1119.06 815.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="229">
   <path class="kv35" d="M 1209.19 782.15 L 1209.19 770.74 L 1236.01 770.7" stroke-width="1" zvalue="252"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="234@0" LinkObjectIDznd="227" MaxPinNum="2"/>
   </metadata>
  <path d="M 1209.19 782.15 L 1209.19 770.74 L 1236.01 770.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="228">
   <path class="kv35" d="M 1264.54 785.12 L 1264.54 771.27 L 1236.01 771.23" stroke-width="1" zvalue="253"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="235@0" LinkObjectIDznd="227" MaxPinNum="2"/>
   </metadata>
  <path d="M 1264.54 785.12 L 1264.54 771.27 L 1236.01 771.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="227">
   <path class="kv35" d="M 1236.01 852.75 L 1236.01 753.83" stroke-width="1" zvalue="254"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="233@0" LinkObjectIDznd="236@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1236.01 852.75 L 1236.01 753.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="226">
   <path class="kv35" d="M 1235.53 715.09 L 1235.53 699.78" stroke-width="1" zvalue="255"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="236@0" LinkObjectIDznd="43@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1235.53 715.09 L 1235.53 699.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="225">
   <path class="kv35" d="M 1235.8 815.67 L 1235.8 815.27" stroke-width="1" zvalue="256"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="230@0" LinkObjectIDznd="227" MaxPinNum="2"/>
   </metadata>
  <path d="M 1235.8 815.67 L 1235.8 815.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="244">
   <path class="kv35" d="M 1322.08 782.15 L 1322.08 770.74 L 1348.89 770.7" stroke-width="1" zvalue="266"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="247@0" LinkObjectIDznd="242" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.08 782.15 L 1322.08 770.74 L 1348.89 770.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="243">
   <path class="kv35" d="M 1377.42 785.12 L 1377.42 770.74 L 1322.22 770.74" stroke-width="1" zvalue="267"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="248@0" LinkObjectIDznd="244" MaxPinNum="2"/>
   </metadata>
  <path d="M 1377.42 785.12 L 1377.42 770.74 L 1322.22 770.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="242">
   <path class="kv35" d="M 1348.89 852.75 L 1348.89 753.83" stroke-width="1" zvalue="268"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="246@0" LinkObjectIDznd="249@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1348.89 852.75 L 1348.89 753.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="240">
   <path class="kv35" d="M 1348.95 816.92 L 1348.89 781.73" stroke-width="1" zvalue="270"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="287@0" LinkObjectIDznd="242" MaxPinNum="2"/>
   </metadata>
  <path d="M 1348.95 816.92 L 1348.89 781.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="258">
   <path class="kv35" d="M 1570.42 781.38 L 1570.42 769.96 L 1597.23 769.92" stroke-width="1" zvalue="282"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="261@0" LinkObjectIDznd="256" MaxPinNum="2"/>
   </metadata>
  <path d="M 1570.42 781.38 L 1570.42 769.96 L 1597.23 769.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="257">
   <path class="kv35" d="M 1625.76 784.34 L 1625.76 769.96 L 1570.56 769.96" stroke-width="1" zvalue="283"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="262@0" LinkObjectIDznd="258" MaxPinNum="2"/>
   </metadata>
  <path d="M 1625.76 784.34 L 1625.76 769.96 L 1570.56 769.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="256">
   <path class="kv35" d="M 1597.23 852.46 L 1597.23 753.83" stroke-width="1" zvalue="284"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="260@0" LinkObjectIDznd="263@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1597.23 852.46 L 1597.23 753.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="254">
   <path class="kv35" d="M 1597.02 814.9 L 1597.02 814.82" stroke-width="1" zvalue="286"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="259@0" LinkObjectIDznd="256" MaxPinNum="2"/>
   </metadata>
  <path d="M 1597.02 814.9 L 1597.02 814.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="271">
   <path class="kv35" d="M 1447.5 782.15 L 1447.5 770.74 L 1474.32 770.7" stroke-width="1" zvalue="296"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="274@0" LinkObjectIDznd="269" MaxPinNum="2"/>
   </metadata>
  <path d="M 1447.5 782.15 L 1447.5 770.74 L 1474.32 770.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="270">
   <path class="kv35" d="M 1502.85 785.12 L 1502.85 771.27 L 1474.32 771.23" stroke-width="1" zvalue="297"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="275@0" LinkObjectIDznd="269" MaxPinNum="2"/>
   </metadata>
  <path d="M 1502.85 785.12 L 1502.85 771.27 L 1474.32 771.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="269">
   <path class="kv35" d="M 1474.32 853.23 L 1474.32 753.83" stroke-width="1" zvalue="298"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="273@0" LinkObjectIDznd="276@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1474.32 853.23 L 1474.32 753.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="267">
   <path class="kv35" d="M 1474.11 815.67 L 1474.11 815.6" stroke-width="1" zvalue="300"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@0" LinkObjectIDznd="269" MaxPinNum="2"/>
   </metadata>
  <path d="M 1474.11 815.67 L 1474.11 815.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="280">
   <path class="kv35" d="M 1597.23 715.09 L 1597.23 699.78" stroke-width="1" zvalue="306"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="263@0" LinkObjectIDznd="43@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1597.23 715.09 L 1597.23 699.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="281">
   <path class="kv110" d="M 895.66 299.84 L 895.66 342.19" stroke-width="1" zvalue="307"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@1" LinkObjectIDznd="35@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 895.66 299.84 L 895.66 342.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="282">
   <path class="kv110" d="M 877.06 318.39 L 895.66 318.39" stroke-width="1" zvalue="308"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="281" MaxPinNum="2"/>
   </metadata>
  <path d="M 877.06 318.39 L 895.66 318.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="285">
   <path class="kv35" d="M 1118.79 715.09 L 1118.79 699.78" stroke-width="1" zvalue="313"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="199@0" LinkObjectIDznd="43@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1118.79 715.09 L 1118.79 699.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="kv35" d="M 1348.41 715.09 L 1348.41 699.78" stroke-width="1" zvalue="316"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="249@0" LinkObjectIDznd="43@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1348.41 715.09 L 1348.41 699.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="kv35" d="M 1473.83 715.09 L 1473.83 699.78" stroke-width="1" zvalue="317"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="276@0" LinkObjectIDznd="43@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 1473.83 715.09 L 1473.83 699.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="164">
   <path class="kv35" d="M 711.28 871.67 L 711.28 845.87 L 747.98 845.87" stroke-width="1" zvalue="325"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="100@0" LinkObjectIDznd="126" MaxPinNum="2"/>
   </metadata>
  <path d="M 711.28 871.67 L 711.28 845.87 L 747.98 845.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="279">
   <path class="kv110" d="M 1005.5 474.11 L 1005.5 441.25" stroke-width="1" zvalue="496"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="137@0" LinkObjectIDznd="55@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1005.5 474.11 L 1005.5 441.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="286">
   <path class="kv110" d="M 1005.08 416.35 L 1005.08 399.77" stroke-width="1" zvalue="497"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="55@0" LinkObjectIDznd="133@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1005.08 416.35 L 1005.08 399.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="71">
   <use class="kv110" height="20" transform="rotate(90,1298.92,318.342) scale(0.964803,0.964803) translate(47.2095,11.2614)" width="10" x="1294.091295277257" xlink:href="#GroundDisconnector:地刀_0" y="308.6936868839574" zvalue="81"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450196733957" ObjectName="110kV平河光伏电站线15217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450196733957"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1298.92,318.342) scale(0.964803,0.964803) translate(47.2095,11.2614)" width="10" x="1294.091295277257" y="308.6936868839574"/></g>
  <g id="74">
   <use class="kv110" height="20" transform="rotate(270,1352.94,202.565) scale(0.964803,0.964803) translate(49.1806,7.03779)" width="10" x="1348.120272721775" xlink:href="#GroundDisconnector:地刀_0" y="192.9173066457037" zvalue="83"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450182578181" ObjectName="110kV平河光伏电站线15260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450182578181"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1352.94,202.565) scale(0.964803,0.964803) translate(49.1806,7.03779)" width="10" x="1348.120272721775" y="192.9173066457037"/></g>
  <g id="75">
   <use class="kv110" height="20" transform="rotate(90,1289.27,122.487) scale(0.964803,0.964803) translate(46.8576,4.11645)" width="10" x="1284.443263590736" xlink:href="#GroundDisconnector:地刀_0" y="112.8386436475784" zvalue="84"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450182709253" ObjectName="110kV平河光伏电站线15267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450182709253"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1289.27,122.487) scale(0.964803,0.964803) translate(46.8576,4.11645)" width="10" x="1284.443263590736" y="112.8386436475784"/></g>
  <g id="99">
   <use class="kv110" height="20" transform="rotate(90,867.648,318.342) scale(0.964803,0.964803) translate(31.4766,11.2614)" width="10" x="862.8242788897624" xlink:href="#GroundDisconnector:地刀_0" y="308.6936868839574" zvalue="99"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450183233541" ObjectName="备用15117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450183233541"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,867.648,318.342) scale(0.964803,0.964803) translate(31.4766,11.2614)" width="10" x="862.8242788897624" y="308.6936868839574"/></g>
  <g id="97">
   <use class="kv110" height="20" transform="rotate(270,921.677,202.565) scale(0.964803,0.964803) translate(33.4476,7.03779)" width="10" x="916.8532563342806" xlink:href="#GroundDisconnector:地刀_0" y="192.9173066457037" zvalue="102"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450183102469" ObjectName="备用15160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450183102469"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,921.677,202.565) scale(0.964803,0.964803) translate(33.4476,7.03779)" width="10" x="916.8532563342806" y="192.9173066457037"/></g>
  <g id="96">
   <use class="kv110" height="20" transform="rotate(90,858,122.487) scale(0.964803,0.964803) translate(31.1246,4.11645)" width="10" x="853.1762472032411" xlink:href="#GroundDisconnector:地刀_0" y="112.8386436475784" zvalue="104"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450182971397" ObjectName="备用15167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450182971397"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,858,122.487) scale(0.964803,0.964803) translate(31.1246,4.11645)" width="10" x="853.1762472032411" y="112.8386436475784"/></g>
  <g id="113">
   <use class="kv110" height="20" transform="rotate(90,1427.66,451.377) scale(1.072,1.072) translate(-95.5322,-29.5977)" width="10" x="1422.302916355916" xlink:href="#GroundDisconnector:地刀_0" y="440.6573202851519" zvalue="118"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450183626757" ObjectName="110kV母线电压互感器19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450183626757"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1427.66,451.377) scale(1.072,1.072) translate(-95.5322,-29.5977)" width="10" x="1422.302916355916" y="440.6573202851519"/></g>
  <g id="115">
   <use class="kv110" height="20" transform="rotate(90,1427.34,372.049) scale(1.072,1.072) translate(-95.5106,-24.2695)" width="10" x="1421.981315299698" xlink:href="#GroundDisconnector:地刀_0" y="361.3290597515338" zvalue="120"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450183757829" ObjectName="110kV母线电压互感器19010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450183757829"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1427.34,372.049) scale(1.072,1.072) translate(-95.5106,-24.2695)" width="10" x="1421.981315299698" y="361.3290597515338"/></g>
  <g id="121">
   <use class="kv110" height="40" transform="rotate(0,1122.11,586.776) scale(1.17352,-1.17352) translate(-162.447,-1083.32)" width="40" x="1098.640402670328" xlink:href="#GroundDisconnector:主变中性点接地刀_0" y="563.3058717968779" zvalue="124"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450183888901" ObjectName="110kV1号主变1010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450183888901"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1122.11,586.776) scale(1.17352,-1.17352) translate(-162.447,-1083.32)" width="40" x="1098.640402670328" y="563.3058717968779"/></g>
  <g id="141">
   <use class="kv110" height="20" transform="rotate(90,983.321,516.004) scale(1.17352,1.17352) translate(-144.528,-74.5619)" width="10" x="977.453719620175" xlink:href="#GroundDisconnector:地刀_0" y="504.2685584322836" zvalue="140"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450184151045" ObjectName="110kV1号主变110kV侧10167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450184151045"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,983.321,516.004) scale(1.17352,1.17352) translate(-144.528,-74.5619)" width="10" x="977.453719620175" y="504.2685584322836"/></g>
  <g id="143">
   <use class="kv110" height="20" transform="rotate(90,971.234,361.968) scale(1.17352,1.17352) translate(-142.741,-51.786)" width="10" x="965.3664815376724" xlink:href="#GroundDisconnector:地刀_0" y="350.2331220958785" zvalue="142"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450196865029" ObjectName="110kV1号主变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450196865029"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,971.234,361.968) scale(1.17352,1.17352) translate(-142.741,-51.786)" width="10" x="965.3664815376724" y="350.2331220958785"/></g>
  <g id="144">
   <use class="kv110" height="20" transform="rotate(270,1034.75,461.717) scale(1.17352,1.17352) translate(-152.132,-66.5351)" width="10" x="1028.880614596991" xlink:href="#GroundDisconnector:地刀_0" y="449.9821742330366" zvalue="144"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450184413189" ObjectName="110kV1号主变110kV侧10160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450184413189"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1034.75,461.717) scale(1.17352,1.17352) translate(-152.132,-66.5351)" width="10" x="1028.880614596991" y="449.9821742330366"/></g>
  <g id="60">
   <use class="kv35" height="20" transform="rotate(0,723.821,789.344) scale(1.52939,1.52939) translate(-247.9,-267.934)" width="10" x="716.1741972546412" xlink:href="#GroundDisconnector:地刀_0" y="774.0504152304931" zvalue="177"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450185003013" ObjectName="35kV1号SVG35167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450185003013"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,723.821,789.344) scale(1.52939,1.52939) translate(-247.9,-267.934)" width="10" x="716.1741972546412" y="774.0504152304931"/></g>
  <g id="162">
   <use class="kv35" height="20" transform="rotate(90,717.453,836.891) scale(1.38209,1.38209) translate(-196.433,-227.543)" width="10" x="710.5421822664836" xlink:href="#GroundDisconnector:地刀_0" y="823.0706074987597" zvalue="188"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450185199621" ObjectName="35kV1号SVG35187接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450185199621"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,717.453,836.891) scale(1.38209,1.38209) translate(-196.433,-227.543)" width="10" x="710.5421822664836" y="823.0706074987597"/></g>
  <g id="191">
   <use class="kv35" height="20" transform="rotate(0,840.562,797.063) scale(1.52939,1.52939) translate(-288.31,-270.606)" width="10" x="832.9153806615468" xlink:href="#GroundDisconnector:地刀_0" y="781.7688405797099" zvalue="213"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450185592837" ObjectName="35kV1号站用变35267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450185592837"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,840.562,797.063) scale(1.52939,1.52939) translate(-288.31,-270.606)" width="10" x="832.9153806615468" y="781.7688405797099"/></g>
  <g id="200">
   <use class="kv35" height="20" transform="rotate(0,1092.38,797.063) scale(1.52939,1.52939) translate(-375.474,-270.606)" width="10" x="1084.729007679749" xlink:href="#GroundDisconnector:地刀_0" y="781.7688405797099" zvalue="223"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450185854981" ObjectName="35kV平河Ⅰ回线35367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450185854981"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1092.38,797.063) scale(1.52939,1.52939) translate(-375.474,-270.606)" width="10" x="1084.729007679749" y="781.7688405797099"/></g>
  <g id="234">
   <use class="kv35" height="20" transform="rotate(0,1209.12,797.063) scale(1.52939,1.52939) translate(-415.883,-270.606)" width="10" x="1201.470191086654" xlink:href="#GroundDisconnector:地刀_0" y="781.7688405797099" zvalue="245"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450186313733" ObjectName="35kV平河Ⅱ回线35467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450186313733"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1209.12,797.063) scale(1.52939,1.52939) translate(-415.883,-270.606)" width="10" x="1201.470191086654" y="781.7688405797099"/></g>
  <g id="247">
   <use class="kv35" height="20" transform="rotate(0,1322,797.063) scale(1.52939,1.52939) translate(-454.957,-270.606)" width="10" x="1314.352161818951" xlink:href="#GroundDisconnector:地刀_0" y="781.7688405797099" zvalue="261"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450186575877" ObjectName="35kV平河Ⅲ回线35567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450186575877"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1322,797.063) scale(1.52939,1.52939) translate(-454.957,-270.606)" width="10" x="1314.352161818951" y="781.7688405797099"/></g>
  <g id="261">
   <use class="kv35" height="20" transform="rotate(0,1570.34,796.291) scale(1.52939,1.52939) translate(-540.919,-270.338)" width="10" x="1562.692497430005" xlink:href="#GroundDisconnector:地刀_0" y="780.9969980447885" zvalue="277"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450187231237" ObjectName="35kV储能Ⅱ回线35767接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450187231237"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1570.34,796.291) scale(1.52939,1.52939) translate(-540.919,-270.338)" width="10" x="1562.692497430005" y="780.9969980447885"/></g>
  <g id="274">
   <use class="kv35" height="20" transform="rotate(0,1447.42,797.063) scale(1.52939,1.52939) translate(-498.372,-270.606)" width="10" x="1439.776573743726" xlink:href="#GroundDisconnector:地刀_0" y="781.7688405797099" zvalue="291"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450186903557" ObjectName="35kV储能Ⅰ回线35667接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450186903557"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1447.42,797.063) scale(1.52939,1.52939) translate(-498.372,-270.606)" width="10" x="1439.776573743726" y="781.7688405797099"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="104">
   <use class="kv110" height="45" transform="rotate(0,896.351,78.9133) scale(0.976863,0.761002) translate(20.7671,19.4058)" width="40" x="876.8139248352179" xlink:href="#EnergyConsumer:单绕组PT避雷器_0" y="61.79079958934443" zvalue="91"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450183364613" ObjectName="110kV备用"/>
   <cge:TPSR_Ref TObjectID="6192450183364613"/></metadata>
  <rect fill="white" height="45" opacity="0" stroke="white" transform="rotate(0,896.351,78.9133) scale(0.976863,0.761002) translate(20.7671,19.4058)" width="40" x="876.8139248352179" y="61.79079958934443"/></g>
  <g id="47">
   <use class="kv35" height="38" transform="rotate(0,746.905,974.584) scale(2.89441,2.28506) translate(-470.857,-523.665)" width="19" x="719.4081609413746" xlink:href="#EnergyConsumer:负荷SVG_0" y="931.16771482131" zvalue="163"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450184675333" ObjectName="35kV1号SVG"/>
   <cge:TPSR_Ref TObjectID="6192450184675333"/></metadata>
  <rect fill="white" height="38" opacity="0" stroke="white" transform="rotate(0,746.905,974.584) scale(2.89441,2.28506) translate(-470.857,-523.665)" width="19" x="719.4081609413746" y="931.16771482131"/></g>
  <g id="184">
   <use class="kv35" height="30" transform="rotate(0,868.131,869.899) scale(1.75732,1.75732) translate(-363.52,-363.525)" width="28" x="843.5282155167201" xlink:href="#EnergyConsumer:站用变YD2022_0" y="843.5392148774608" zvalue="205"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450185461765" ObjectName="35kV1号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,868.131,869.899) scale(1.75732,1.75732) translate(-363.52,-363.525)" width="28" x="843.5282155167201" y="843.5392148774608"/></g>
  <g id="260">
   <use class="kv35" height="30" transform="rotate(0,1597.23,857.667) scale(0.225121,-0.385921) translate(5493.12,-3089.27)" width="12" x="1595.881726431638" xlink:href="#EnergyConsumer:负荷_0" y="851.8778708350972" zvalue="279"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450187100165" ObjectName="35kV储能Ⅱ回线"/>
   <cge:TPSR_Ref TObjectID="6192450187100165"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1597.23,857.667) scale(0.225121,-0.385921) translate(5493.12,-3089.27)" width="12" x="1595.881726431638" y="851.8778708350972"/></g>
  <g id="273">
   <use class="kv35" height="30" transform="rotate(0,1474.32,858.439) scale(0.225121,-0.385921) translate(5070.04,-3092.04)" width="12" x="1472.965802745359" xlink:href="#EnergyConsumer:负荷_0" y="852.6497133700188" zvalue="293"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450186772485" ObjectName="35kV储能Ⅰ回线"/>
   <cge:TPSR_Ref TObjectID="6192450186772485"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1474.32,858.439) scale(0.225121,-0.385921) translate(5070.04,-3092.04)" width="12" x="1472.965802745359" y="852.6497133700188"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="108">
   <use class="kv110" height="30" transform="rotate(0,1453.82,489.969) scale(1.5008,-1.5008) translate(-480.118,-808.929)" width="20" x="1438.811770575074" xlink:href="#Accessory:平河光伏_0" y="467.4574083032662" zvalue="114"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450183495685" ObjectName="110kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1453.82,489.969) scale(1.5008,-1.5008) translate(-480.118,-808.929)" width="20" x="1438.811770575074" y="467.4574083032662"/></g>
  <g id="155">
   <use class="kv35" height="26" transform="rotate(90,977.35,629.53) scale(1.17352,1.17352) translate(-143.471,-90.8276)" width="12" x="970.3086558163426" xlink:href="#Accessory:避雷器_0" y="614.2747306942387" zvalue="152"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450184478725" ObjectName="110kV1号主变35kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,977.35,629.53) scale(1.17352,1.17352) translate(-143.471,-90.8276)" width="12" x="970.3086558163426" y="614.2747306942387"/></g>
  <g id="161">
   <use class="kv35" height="26" transform="rotate(0,1057.15,659.026) scale(1.17352,1.17352) translate(-155.271,-95.1889)" width="12" x="1050.107897526069" xlink:href="#Accessory:接地变接地设备_0" y="643.7702317857138" zvalue="157"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450184544261" ObjectName="110kV1号主变接地设备"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1057.15,659.026) scale(1.17352,1.17352) translate(-155.271,-95.1889)" width="12" x="1050.107897526069" y="643.7702317857138"/></g>
  <g id="53">
   <use class="kv35" height="20" transform="rotate(270,778.983,901.083) scale(1.4472,2.13222) translate(-236.244,-467.157)" width="20" x="764.5108360041122" xlink:href="#Accessory:线路PT3_0" y="879.7606824091432" zvalue="165"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450184740869" ObjectName="35kV1号SVG避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,778.983,901.083) scale(1.4472,2.13222) translate(-236.244,-467.157)" width="20" x="764.5108360041122" y="879.7606824091432"/></g>
  <g id="61">
   <use class="kv35" height="21" transform="rotate(0,747.507,790.372) scale(1.07966,1.07966) translate(-54.8347,-57.4796)" width="8" x="743.1886859769004" xlink:href="#Accessory:中间电缆_0" y="779.0352316018625" zvalue="170"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450184806405" ObjectName="电缆"/>
   </metadata>
  <rect fill="white" height="21" opacity="0" stroke="white" transform="rotate(0,747.507,790.372) scale(1.07966,1.07966) translate(-54.8347,-57.4796)" width="8" x="743.1886859769004" y="779.0352316018625"/></g>
  <g id="51">
   <use class="kv35" height="20" transform="rotate(0,775.385,792.345) scale(1.50795,1.7082) translate(-256.108,-321.415)" width="20" x="760.3050088578028" xlink:href="#Accessory:线路PT3_0" y="775.262525982198" zvalue="176"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450184871941" ObjectName="35kV1号SVGPT"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,775.385,792.345) scale(1.50795,1.7082) translate(-256.108,-321.415)" width="20" x="760.3050088578028" y="775.262525982198"/></g>
  <g id="174">
   <use class="kv35" height="35" transform="rotate(0,991.143,878.992) scale(1.6953,-1.8688) translate(-394.333,-1334.14)" width="35" x="961.4754028844409" xlink:href="#Accessory:四绕组PT带熔断器_0" y="846.2876877696718" zvalue="195"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450185265157" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,991.143,878.992) scale(1.6953,-1.8688) translate(-394.333,-1334.14)" width="35" x="961.4754028844409" y="846.2876877696718"/></g>
  <g id="176">
   <use class="kv35" height="20" transform="rotate(0,966.058,846.089) scale(-1.4472,2.21905) translate(-1629.12,-452.614)" width="20" x="951.5861704057568" xlink:href="#Accessory:线路PT3_0" y="823.8985789441858" zvalue="197"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450185330693" ObjectName="35kV母线电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,966.058,846.089) scale(-1.4472,2.21905) translate(-1629.12,-452.614)" width="20" x="951.5861704057568" y="823.8985789441858"/></g>
  <g id="192">
   <use class="kv35" height="20" transform="rotate(0,895.985,800.063) scale(1.50795,1.7082) translate(-296.732,-324.615)" width="20" x="880.9054049393167" xlink:href="#Accessory:线路PT3_0" y="782.980951331415" zvalue="212"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450185658373" ObjectName="35kV1号站用变PT"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,895.985,800.063) scale(1.50795,1.7082) translate(-296.732,-324.615)" width="20" x="880.9054049393167" y="782.980951331415"/></g>
  <g id="196">
   <use class="kv35" height="21" transform="rotate(0,868.108,805.809) scale(1.07966,1.07966) translate(-63.733,-58.6186)" width="8" x="863.7890820584144" xlink:href="#Accessory:中间电缆_0" y="794.4720823002965" zvalue="217"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450185723909" ObjectName="35kV1号站用变电缆"/>
   </metadata>
  <rect fill="white" height="21" opacity="0" stroke="white" transform="rotate(0,868.108,805.809) scale(1.07966,1.07966) translate(-63.733,-58.6186)" width="8" x="863.7890820584144" y="794.4720823002965"/></g>
  <g id="201">
   <use class="kv35" height="20" transform="rotate(0,1147.8,800.063) scale(1.50795,1.7082) translate(-381.555,-324.615)" width="20" x="1132.719031957518" xlink:href="#Accessory:线路PT3_0" y="782.980951331415" zvalue="222"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450185920517" ObjectName="35kV平河Ⅰ回线PT"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1147.8,800.063) scale(1.50795,1.7082) translate(-381.555,-324.615)" width="20" x="1132.719031957518" y="782.980951331415"/></g>
  <g id="212">
   <use class="kv35" height="15" transform="rotate(0,1119.06,823.687) scale(1.14529,1.14529) translate(-140.87,-103.4)" width="15" x="1110.46966605302" xlink:href="#Accessory:放电间隙4_0" y="815.0978330478795" zvalue="233"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450186051589" ObjectName="35kV平河Ⅰ回线电缆"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,1119.06,823.687) scale(1.14529,1.14529) translate(-140.87,-103.4)" width="15" x="1110.46966605302" y="815.0978330478795"/></g>
  <g id="235">
   <use class="kv35" height="20" transform="rotate(0,1264.54,800.063) scale(1.50795,1.7082) translate(-420.879,-324.615)" width="20" x="1249.460215364424" xlink:href="#Accessory:线路PT3_0" y="782.980951331415" zvalue="244"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450186379269" ObjectName="35kV平河Ⅱ回线PT"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1264.54,800.063) scale(1.50795,1.7082) translate(-420.879,-324.615)" width="20" x="1249.460215364424" y="782.980951331415"/></g>
  <g id="230">
   <use class="kv35" height="15" transform="rotate(0,1235.8,823.687) scale(1.14529,1.14529) translate(-155.679,-103.4)" width="15" x="1227.210849459926" xlink:href="#Accessory:放电间隙4_0" y="815.0978330478795" zvalue="251"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450186117125" ObjectName="35kV平河Ⅱ回线电缆"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,1235.8,823.687) scale(1.14529,1.14529) translate(-155.679,-103.4)" width="15" x="1227.210849459926" y="815.0978330478795"/></g>
  <g id="248">
   <use class="kv35" height="20" transform="rotate(0,1377.42,800.063) scale(1.50795,1.7082) translate(-458.903,-324.615)" width="20" x="1362.342186096721" xlink:href="#Accessory:线路PT3_0" y="782.980951331415" zvalue="260"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450186641413" ObjectName="35kV平河Ⅲ回线PT"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1377.42,800.063) scale(1.50795,1.7082) translate(-458.903,-324.615)" width="20" x="1362.342186096721" y="782.980951331415"/></g>
  <g id="262">
   <use class="kv35" height="20" transform="rotate(0,1625.76,799.291) scale(1.50795,1.7082) translate(-542.556,-324.295)" width="20" x="1610.682521707775" xlink:href="#Accessory:线路PT3_0" y="782.2091087964932" zvalue="276"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450187296773" ObjectName="35kV储能Ⅱ回线PT"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1625.76,799.291) scale(1.50795,1.7082) translate(-542.556,-324.295)" width="20" x="1610.682521707775" y="782.2091087964932"/></g>
  <g id="259">
   <use class="kv35" height="15" transform="rotate(0,1597.02,822.916) scale(1.14529,1.14529) translate(-201.503,-103.302)" width="15" x="1588.433155803277" xlink:href="#Accessory:放电间隙4_0" y="814.3259905129578" zvalue="281"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450187034629" ObjectName="35kV储能Ⅱ回线电缆"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,1597.02,822.916) scale(1.14529,1.14529) translate(-201.503,-103.302)" width="15" x="1588.433155803277" y="814.3259905129578"/></g>
  <g id="275">
   <use class="kv35" height="20" transform="rotate(0,1502.85,800.063) scale(1.50795,1.7082) translate(-501.152,-324.615)" width="20" x="1487.766598021496" xlink:href="#Accessory:线路PT3_0" y="782.980951331415" zvalue="290"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450186969093" ObjectName="35kV储能Ⅰ回线PT"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1502.85,800.063) scale(1.50795,1.7082) translate(-501.152,-324.615)" width="20" x="1487.766598021496" y="782.980951331415"/></g>
  <g id="272">
   <use class="kv35" height="15" transform="rotate(0,1474.11,823.687) scale(1.14529,1.14529) translate(-185.91,-103.4)" width="15" x="1465.517232116998" xlink:href="#Accessory:放电间隙4_0" y="815.0978330478795" zvalue="295"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450186706949" ObjectName="35kV储能Ⅰ回线电缆"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,1474.11,823.687) scale(1.14529,1.14529) translate(-185.91,-103.4)" width="15" x="1465.517232116998" y="815.0978330478795"/></g>
  <g id="287">
   <use class="kv35" height="15" transform="rotate(0,1348.95,823.669) scale(0.964803,0.964803) translate(48.9468,29.7842)" width="15" x="1341.711223101443" xlink:href="#Accessory:放电间隙4_0" y="816.4328401391399" zvalue="315"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450187362309" ObjectName="35kV平河Ⅲ回线电缆"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,1348.95,823.669) scale(0.964803,0.964803) translate(48.9468,29.7842)" width="15" x="1341.711223101443" y="816.4328401391399"/></g>
  <g id="100">
   <use class="kv35" height="20" transform="rotate(0,711.35,882.071) scale(1.47558,1.47558) translate(-226.891,-279.536)" width="10" x="703.9722748275703" xlink:href="#Accessory:R_0" y="867.3147215335309" zvalue="319"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450187427845" ObjectName="R"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,711.35,882.071) scale(1.47558,1.47558) translate(-226.891,-279.536)" width="10" x="703.9722748275703" y="867.3147215335309"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="158">
   <g id="1580">
    <use class="kv110" height="30" transform="rotate(0,1016.78,571.894) scale(2.38604,2.38604) translate(-569.162,-311.42)" width="31" x="979.8" xlink:href="#PowerTransformer2:平河光伏1_0" y="536.1" zvalue="155"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874463510531" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1581">
    <use class="kv35" height="30" transform="rotate(0,1016.78,571.894) scale(2.38604,2.38604) translate(-569.162,-311.42)" width="31" x="979.8" xlink:href="#PowerTransformer2:平河光伏1_1" y="536.1" zvalue="155"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874463576067" ObjectName="35"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399463534595" ObjectName="110kV1号主变"/>
   <cge:TPSR_Ref TObjectID="6755399463534595"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1016.78,571.894) scale(2.38604,2.38604) translate(-569.162,-311.42)" width="31" x="979.8" y="536.1"/></g>
 </g>
</svg>