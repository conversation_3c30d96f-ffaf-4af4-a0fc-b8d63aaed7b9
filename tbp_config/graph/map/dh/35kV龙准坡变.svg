<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549583347714" height="1045" id="thSvg" source="NR-PCS9000" viewBox="0 0 1900 1045" width="1900">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀（规范制图）_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000004" y2="0.9166666666666679"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀（规范制图）_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.89694619969018" y2="15.89694619969018"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.73157590710537" y2="17.73157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.48348701738202" y2="19.48348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.05" x2="5.05" y1="3.566666666666666" y2="15.81666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀（规范制图）_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸带避雷器_0" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="11.75" xlink:href="#terminal" y="7.25"/>
   <use terminal-index="1" type="0" x="11.75" xlink:href="#terminal" y="41.75"/>
   <rect fill-opacity="0" height="8" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,25,7.5) scale(1,1) translate(0,0)" width="4" x="23" y="3.5"/>
   <path d="M 24 8.5 L 26 8.5 L 25 5.5 L 24 8.5 L 24 8.5 z" fill="rgb(0,255,127)" fill-opacity="1" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="2.5" y2="3.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24" x2="26" y1="1.75" y2="1.75"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23" x2="27" y1="2.5" y2="2.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="16.5" y2="8.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.5" x2="25.5" y1="0.75" y2="0.75"/>
   <rect height="12.17" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,11.75,25.42) scale(1,1) translate(0,0)" width="6" x="8.75" y="19.33"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="24.975" y1="16.475" y2="16.475"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="11.5" y2="37.41666666666667"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="6.343004115226339" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="6.343004115226339" y1="11.43694745991201" y2="16.24729228749822"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="17.259670781893" y1="11.43694745991201" y2="16.24729228749822"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="17.259670781893" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="6.293004115226339" y1="41.49729228749822" y2="36.68694745991201"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="6.293004115226339" y1="37.49082677025685" y2="32.68048194267064"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="17.209670781893" y1="37.49082677025685" y2="32.68048194267064"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="17.209670781893" y1="41.49729228749822" y2="36.68694745991201"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸带避雷器_1" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="11.75" xlink:href="#terminal" y="7.25"/>
   <use terminal-index="1" type="0" x="11.75" xlink:href="#terminal" y="41.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="2.5" y2="3.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24" x2="26" y1="1.75" y2="1.75"/>
   <rect fill-opacity="0" height="8" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,25,7.5) scale(1,1) translate(0,0)" width="4" x="23" y="3.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23" x2="27" y1="2.5" y2="2.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.5" x2="25.5" y1="0.75" y2="0.75"/>
   <path d="M 24 8.5 L 26 8.5 L 25 5.5 L 24 8.5 L 24 8.5 z" fill="rgb(255,0,0)" fill-opacity="1" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="16.5" y2="8.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="24.975" y1="16.475" y2="16.475"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="7.333333333333334" y2="41.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="6.343004115226339" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="17.259670781893" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="6.293004115226339" y1="41.49729228749822" y2="36.68694745991201"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="17.209670781893" y1="41.49729228749822" y2="36.68694745991201"/>
   <rect height="12.17" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,11.75,25.42) scale(1,1) translate(0,0)" width="6" x="8.75" y="19.33"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸带避雷器_2" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="11.75" xlink:href="#terminal" y="7.25"/>
   <use terminal-index="1" type="0" x="11.75" xlink:href="#terminal" y="41.75"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.75" x2="14.75" y1="17" y2="32"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.65" x2="8.65" y1="17.1" y2="32.1"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="16.5" y2="8.5"/>
   <rect fill-opacity="0" height="8" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,25,7.5) scale(1,1) translate(0,0)" width="4" x="23" y="3.5"/>
   <path d="M 24 8.5 L 26 8.5 L 25 5.5 L 24 8.5 L 24 8.5 z" fill="rgb(0,255,127)" fill-opacity="1" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="2.5" y2="3.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23" x2="27" y1="2.5" y2="2.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24" x2="26" y1="1.75" y2="1.75"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.5" x2="25.5" y1="0.75" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="24.975" y1="16.475" y2="16.475"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="32.35" y2="41.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.71666666666667" x2="11.71666666666667" y1="17.05" y2="7.466666666666663"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="6.343004115226339" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="6.343004115226339" y1="11.43694745991201" y2="16.24729228749822"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="17.259670781893" y1="11.43694745991201" y2="16.24729228749822"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="17.259670781893" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="6.293004115226339" y1="41.49729228749822" y2="36.68694745991201"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="6.293004115226339" y1="37.49082677025685" y2="32.68048194267064"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="17.209670781893" y1="37.49082677025685" y2="32.68048194267064"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="17.209670781893" y1="41.49729228749822" y2="36.68694745991201"/>
  </symbol>
  <symbol id="Accessory:10kV母线PT带消谐装置_0" viewBox="0,0,35,30">
   <use terminal-index="0" type="0" x="17.56245852479325" xlink:href="#terminal" y="28.12373692455963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.61245852479333" x2="34.61245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.86245852479331" x2="30.86245852479331" y1="22.68299618381883" y2="19.84040359122624"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.6124585247933" x2="26.6124585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.61245852479332" x2="34.61245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.16666666666667" x2="31" y1="22.69225544307809" y2="22.69225544307809"/>
   <rect fill-opacity="0" height="3.55" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,30.81,15.38) scale(1,1) translate(0,0)" width="8.92" x="26.34" y="13.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.647721108666" x2="30.647721108666" y1="10.83674821859629" y2="7.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.71438777533266" x2="28.71438777533266" y1="7.51612768565195" y2="7.51612768565195"/>
   <ellipse cx="14.04" cy="22.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.05048569403981" x2="22.05048569403981" y1="34.53084700683308" y2="34.53084700683308"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.7608382776216" x2="28.48402243293775" y1="7.754208141873304" y2="7.754208141873304"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.01083827762157" x2="29.40068909960439" y1="6.50420814187332" y2="6.50420814187332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.51083827762159" x2="30.06735576627107" y1="5.004208141873296" y2="5.004208141873296"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.89410730945214" x2="23.0877735452272" y1="24.15299989806297" y2="21.53208583485582"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58713830216066" x2="22.81883560169719" y1="21.54040359122622" y2="21.54040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.51457106407813" x2="19.32090482830307" y1="24.05654513693459" y2="21.43563107372743"/>
   <ellipse cx="14.04" cy="15.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="21.04" cy="22.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.3644003886642" x2="22.3644003886642" y1="30.19213090500035" y2="30.19213090500035"/>
   <ellipse cx="21.04" cy="15.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.19220382559271" x2="17.19220382559271" y1="23.07076893362116" y2="23.07076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.4422038255927" x2="16.4422038255927" y1="16.82076893362115" y2="16.82076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.92264294445647" x2="20.87303257583197" y1="16.99992879670395" y2="15.47611741162254"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.873032575832" x2="20.37063985073817" y1="15.47611741162252" y2="12.82298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.87303257583199" x2="23.32581493230132" y1="15.47611741162254" y2="16.60543752773795"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.94220382559271" x2="16.94220382559271" y1="16.32076893362115" y2="16.32076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583198" x2="16.07581493230131" y1="22.72611741162255" y2="23.85543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583199" x2="13.12063985073816" y1="22.72611741162255" y2="20.07298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.67264294445646" x2="13.62303257583197" y1="24.24992879670398" y2="22.72611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583199" x2="16.07581493230131" y1="15.47611741162254" y2="16.60543752773796"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583198" x2="13.12063985073816" y1="15.47611741162253" y2="12.82298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.67264294445646" x2="13.62303257583196" y1="16.99992879670396" y2="15.47611741162255"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.59103363843511" x2="17.59103363843511" y1="25.15822158129307" y2="28.16666666666667"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_0" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="3.333333333333332" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="24.08333333333333" y2="29.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4" x2="7" y1="19.5" y2="24.5"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_1" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="0" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="18.25" y2="32.5"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_2" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="4" y1="18.5" y2="25.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4" x2="10" y1="18.5" y2="25.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="0" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.083333333333333" x2="7.083333333333333" y1="25.83333333333333" y2="32.41666666666667"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变1接点_0" viewBox="0,0,26,38">
   <use terminal-index="0" type="0" x="9.116666666666667" xlink:href="#terminal" y="0.2500000000000071"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.45" x2="25.45" y1="30" y2="30"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.45" x2="24.45" y1="31" y2="31"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="9" y1="33" y2="38"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.45" x2="23.45" y1="32" y2="32"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23" x2="23" y1="24" y2="30"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19" x2="19" y1="24" y2="30"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="23" y1="24" y2="24"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="9" y1="2.25" y2="0.25"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19" x2="9" y1="30" y2="37"/>
   <ellipse cx="9.210000000000001" cy="10.97" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="9.201922857336841" y1="6.6" y2="10.70894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="5.216666666666669" y1="10.73394833233988" y2="13.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.22969569739165" x2="13.31666666666667" y1="10.68990500916851" y2="13.5"/>
   <ellipse cx="9.210000000000001" cy="24.47" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="9.201922857336841" y1="20.1" y2="24.20894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="5.216666666666669" y1="24.23394833233988" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.22969569739165" x2="13.31666666666667" y1="24.18990500916851" y2="27"/>
  </symbol>
  <symbol id="Compensator:西郊变电容_0" viewBox="0,0,24,40">
   <use terminal-index="0" type="0" x="12" xlink:href="#terminal" y="2.449999999999999"/>
   <rect fill-opacity="0" height="5.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,12,23) scale(1,1) translate(0,0)" width="3" x="10.5" y="20.17"/>
   <path d="M 12 17.85 L 17.85 17.85 L 17.85 22.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="6.5" y1="33.10833333333333" y2="33.10833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.471296296296289" x2="6.471296296296289" y1="30.88611111111111" y2="33.12685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.50833333333333" x2="6.50833333333333" y1="20.10833333333333" y2="17.90462962962963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="29.60833333333333" y2="33.12685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="22.60833333333333" y2="28.60833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="17.79351851851851" y2="22.525"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.94090594744122" x2="6.94090594744122" y1="36.94166666666666" y2="34.94166666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.416666666666666" x2="12.00833333333334" y1="17.85833333333333" y2="17.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="33.10833333333333" y2="37.10833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.09166666666667" x2="17.09166666666667" y1="34.94166666666666" y2="36.94166666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.758333333333324" x2="2.758333333333324" y1="19.85833333333333" y2="31.60833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.08333333333333" x2="7.000000000000002" y1="37.10833333333333" y2="37.10833333333333"/>
   <path d="M 6.50833 23.7072 A 2.96392 1.81747 180 0 1 6.50833 20.0723" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 6.50833 27.3421 A 2.96392 1.81747 180 0 1 6.50833 23.7072" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 6.50833 30.8771 A 2.96392 1.81747 180 0 1 6.50833 27.2421" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.50833333333333" x2="10.50833333333333" y1="29.60833333333333" y2="29.60833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.50833333333333" x2="10.50833333333333" y1="28.60833333333333" y2="28.60833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12" x2="12" y1="2.25" y2="4.916666666666668"/>
   <path d="M 7.26667 9.85 A 4.91667 4.75 -450 1 0 12.0167 4.93333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 7.33333 9.83333 L 12 9.91667 L 12 18" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="20.56666666666668" y2="21.08015580397416"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.28450340888154" x2="18.47116270499357" y1="26.7156109584975" y2="26.7156109584975"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510778" x2="17.14262023217246" y1="23.75922956383932" y2="22.95550743587977"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.50700202690252" x2="18.32283029297954" y1="27.0857461490052" y2="27.0857461490052"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="25.36667381975841" y2="26.33114037330986"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.98783858485353" x2="18.76782752902158" y1="26.34547576798984" y2="26.34547576798984"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="21.08015580397418" y2="23.73243882624067"/>
   <rect fill-opacity="0" height="4.29" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,17.83,23.22) scale(1,1) translate(0,0)" width="2.33" x="16.67" y="21.08"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510781" x2="18.53565505804314" y1="23.75922956383932" y2="22.95550743587977"/>
  </symbol>
  <symbol id=":线路带避雷器_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15.16666666666667" xlink:href="#terminal" y="39.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.16022336769755" x2="15.16022336769755" y1="39.75" y2="0.8333333333333321"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="28.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="6.5" y1="33.25" y2="33.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="8.5" y1="31.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="7.5" y1="32.25" y2="32.25"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6.04,21.17) scale(1,1) translate(0,0)" width="6.08" x="3" y="14"/>
   <path d="M 15 9.25 L 6 9.25 L 6 21.25 L 6 21.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="7" y1="22" y2="16"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="5" y1="22" y2="16"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV龙准坡变" InitShowingPlane="" fill="rgb(0,0,0)" height="1045" width="1900" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="174" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,167.046,93.6272) scale(1,1) translate(8.02766e-15,-5.67788e-14)" writing-mode="lr" x="167.05" xml:space="preserve" y="98.13" zvalue="179"/>
  <line fill="none" id="156" stroke="rgb(0,39,45)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.22222222222285" x2="318.8097708603564" y1="76.70055797356122" y2="76.70055797356122" zvalue="190"/>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="4" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,96.625,325.25) scale(1,1) translate(0,0)" width="72.88" x="60.19" y="313.25" zvalue="534"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,96.625,325.25) scale(1,1) translate(0,0)" writing-mode="lr" x="96.63" xml:space="preserve" y="329.75" zvalue="534">信号一览</text>
  <image height="60" id="2" preserveAspectRatio="xMidYMid slice" width="273.81" x="43.44" xlink:href="logo.png" y="39.75"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,180.347,69.75) scale(1,1) translate(-9.6466e-15,0)" writing-mode="lr" x="180.35" xml:space="preserve" y="73.25" zvalue="662"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="3" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,173.833,67.8153) scale(1,1) translate(1.85407e-14,0)" writing-mode="lr" x="173.83" xml:space="preserve" y="76.81999999999999" zvalue="663">35kV龙准坡变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,612.076,303.591) scale(1,1) translate(0,0)" writing-mode="lr" x="612.08" xml:space="preserve" y="308.09" zvalue="7">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,583.504,650.75) scale(1,1) translate(0,0)" writing-mode="lr" x="583.5" xml:space="preserve" y="655.25" zvalue="9">10kV母线</text>
  <line fill="none" id="130" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="375" x2="375" y1="49.5" y2="1039.5" zvalue="210"/>
  <line fill="none" id="136" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.47692454998014" x2="318.0881846035993" y1="169.1279458827686" y2="169.1279458827686" zvalue="501"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="113.0114285714285" y1="928.0627909390723" y2="928.0627909390723"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="113.0114285714285" y1="979.4022909390724" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="41.57142857142856" y1="928.0627909390723" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0114285714285" x2="113.0114285714285" y1="928.0627909390723" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="347.5719285714285" y1="928.0627909390723" y2="928.0627909390723"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="347.5719285714285" y1="979.4022909390724" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="113.0119285714286" y1="928.0627909390723" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="347.5719285714285" x2="347.5719285714285" y1="928.0627909390723" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="113.0114285714285" y1="979.4022709390724" y2="979.4022709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="113.0114285714285" y1="1006.879770939072" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="41.57142857142856" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0114285714285" x2="113.0114285714285" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="184.0472285714286" y1="979.4022709390724" y2="979.4022709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="184.0472285714286" y1="1006.879770939072" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="113.0119285714286" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="184.0472285714286" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="265.8093285714285" y1="979.4022709390724" y2="979.4022709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="265.8093285714285" y1="1006.879770939072" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="184.0472285714286" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8093285714285" x2="265.8093285714285" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8092285714285" x2="347.5713285714286" y1="979.4022709390724" y2="979.4022709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8092285714285" x2="347.5713285714286" y1="1006.879770939072" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8092285714285" x2="265.8092285714285" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="347.5713285714286" x2="347.5713285714286" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="113.0114285714285" y1="1006.879690939072" y2="1006.879690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="113.0114285714285" y1="1034.357190939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="41.57142857142856" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0114285714285" x2="113.0114285714285" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="184.0472285714286" y1="1006.879690939072" y2="1006.879690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="184.0472285714286" y1="1034.357190939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="113.0119285714286" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="184.0472285714286" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="265.8093285714285" y1="1006.879690939072" y2="1006.879690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="265.8093285714285" y1="1034.357190939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="184.0472285714286" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8093285714285" x2="265.8093285714285" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8092285714285" x2="347.5713285714286" y1="1006.879690939072" y2="1006.879690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8092285714285" x2="347.5713285714286" y1="1034.357190939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8092285714285" x2="265.8092285714285" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="347.5713285714286" x2="347.5713285714286" y1="1006.879690939072" y2="1034.357190939072"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="134" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,188.624,957.636) scale(1,1) translate(-1.04198e-14,1.05117e-13)" writing-mode="lr" x="46.93" xml:space="preserve" y="963.64" zvalue="503">参考图号       LongZhunPo-01-2017</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="109" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,119.149,995.005) scale(1,1) translate(5.03187e-14,-1.52972e-12)" writing-mode="lr" x="56.65" xml:space="preserve" y="1001" zvalue="504">制图             </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="98" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,262.452,996.005) scale(1,1) translate(1.29063e-13,-1.53127e-12)" writing-mode="lr" x="193.75" xml:space="preserve" y="1002" zvalue="505">绘制日期    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,70.2423,1024.56) scale(1,1) translate(-4.58202e-14,1.01293e-12)" writing-mode="lr" x="70.23999999999999" xml:space="preserve" y="1030.56" zvalue="506">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="96" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,270.746,1022.56) scale(1,1) translate(0,1.12325e-13)" writing-mode="lr" x="193.92" xml:space="preserve" y="1028.56" zvalue="507">更新日期    20210812</text>
  <line fill="none" id="91" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="40.85611355802348" x2="345.4673736116426" y1="623.8945306693694" y2="623.8945306693694" zvalue="508"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.9207,640.936) scale(1,1) translate(2.94653e-15,-1.38164e-13)" writing-mode="lr" x="83.92070806204504" xml:space="preserve" y="645.4363811688671" zvalue="510">危险点(双击编辑）：</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="172.3571428571429" y2="172.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="198.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="2.571428571428442" y1="172.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="172.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="172.3571428571429" y2="172.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="198.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="172.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.5714285714284" x2="364.5714285714284" y1="172.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="198.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="222.6071428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="2.571428571428442" y1="198.3571428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="198.3571428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="198.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="222.6071428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="198.3571428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.5714285714284" x2="364.5714285714284" y1="198.3571428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="222.6071428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="245.3571428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="2.571428571428442" y1="222.6071428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="222.6071428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="222.6071428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="245.3571428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="222.6071428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.5714285714284" x2="364.5714285714284" y1="222.6071428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="245.3571428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="268.1071428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="2.571428571428442" y1="245.3571428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="245.3571428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="245.3571428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="268.1071428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="245.3571428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.5714285714284" x2="364.5714285714284" y1="245.3571428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="268.1071428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="290.8571428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="2.571428571428442" y1="268.1071428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="268.1071428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="268.1071428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="290.8571428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="268.1071428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.5714285714284" x2="364.5714285714284" y1="268.1071428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="452.3571428571429" y2="452.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="53.57142857142856" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="452.3571428571429" y2="452.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="452.3571428571429" y2="452.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9587285714285" x2="216.9587285714285" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="452.3571428571429" y2="452.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="216.9586285714286" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="452.3571428571429" y2="452.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.5714285714286" x2="334.5714285714286" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="53.57142857142856" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9587285714285" x2="216.9587285714285" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="216.9586285714286" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.5714285714286" x2="334.5714285714286" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="53.57142857142856" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9587285714285" x2="216.9587285714285" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="216.9586285714286" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.5714285714286" x2="334.5714285714286" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="564.6776428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="53.57142857142856" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="564.6776428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="564.6776428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9587285714285" x2="216.9587285714285" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="564.6776428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="216.9586285714286" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="564.6776428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.5714285714286" x2="334.5714285714286" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="564.6777428571429" y2="564.6777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="53.57142857142856" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="564.6777428571429" y2="564.6777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="564.6777428571429" y2="564.6777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9587285714285" x2="216.9587285714285" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="564.6777428571429" y2="564.6777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="216.9586285714286" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="564.6777428571429" y2="564.6777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.5714285714286" x2="334.5714285714286" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="614.0365428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="53.57142857142856" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="614.0365428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="614.0365428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9587285714285" x2="216.9587285714285" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="614.0365428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="216.9586285714286" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="614.0365428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.5714285714286" x2="334.5714285714286" y1="589.3571428571429" y2="614.0365428571429"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" x="130.578125" xml:space="preserve" y="467.359375" zvalue="513">35kV     母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="130.578125" xml:space="preserve" y="483.359375" zvalue="513">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,199.97,327.199) scale(1,1) translate(0,0)" writing-mode="lr" x="199.97" xml:space="preserve" y="331.7" zvalue="514">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,304.97,327.199) scale(1,1) translate(0,0)" writing-mode="lr" x="304.97" xml:space="preserve" y="331.7" zvalue="515">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" x="247.90625" xml:space="preserve" y="467.359375" zvalue="516">10kV     母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="247.90625" xml:space="preserve" y="483.359375" zvalue="516">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.5714,502.857) scale(1,1) translate(0,0)" writing-mode="lr" x="78.57142857142856" xml:space="preserve" y="507.3571428571429" zvalue="517">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.5714,528.357) scale(1,1) translate(0,0)" writing-mode="lr" x="78.57142857142856" xml:space="preserve" y="532.8571428571429" zvalue="518">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.5714,553.857) scale(1,1) translate(0,0)" writing-mode="lr" x="78.57142857142856" xml:space="preserve" y="558.3571428571429" zvalue="519">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.5714,579.357) scale(1,1) translate(0,0)" writing-mode="lr" x="78.57142857142856" xml:space="preserve" y="583.8571428571429" zvalue="520">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.5714,604.857) scale(1,1) translate(0,0)" writing-mode="lr" x="78.57142857142856" xml:space="preserve" y="609.3571428571429" zvalue="521">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,40.5714,186.357) scale(1,1) translate(0,0)" writing-mode="lr" x="40.57" xml:space="preserve" y="191.86" zvalue="522">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.821,186.357) scale(1,1) translate(0,0)" writing-mode="lr" x="226.82" xml:space="preserve" y="191.86" zvalue="523">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44.2589,210.607) scale(1,1) translate(0,0)" writing-mode="lr" x="44.26" xml:space="preserve" y="215.11" zvalue="524">35kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47.7589,258.357) scale(1,1) translate(0,0)" writing-mode="lr" x="47.76" xml:space="preserve" y="262.86" zvalue="525">1号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47.7589,281.357) scale(1,1) translate(0,0)" writing-mode="lr" x="47.76" xml:space="preserve" y="285.86" zvalue="526">1号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,45.2589,234.607) scale(1,1) translate(0,0)" writing-mode="lr" x="45.26" xml:space="preserve" y="239.11" zvalue="527">10kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="149" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1168,490.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1168" xml:space="preserve" y="494.75" zvalue="558">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="158" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1170.5,518.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1170.5" xml:space="preserve" y="523" zvalue="565">10000kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="162" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1109,602.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1109" xml:space="preserve" y="607" zvalue="565">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="161" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1108,382.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1108" xml:space="preserve" y="387" zvalue="568">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="172" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1055.75,443.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1055.75" xml:space="preserve" y="448" zvalue="573">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="182" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1019.5,257.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1019.5" xml:space="preserve" y="262" zvalue="579">311</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="197" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,995,133) scale(1,1) translate(0,0)" writing-mode="lr" x="995" xml:space="preserve" y="137.5" zvalue="580">35kV清龙线龙准坡T线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="203" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1308,274.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1308" xml:space="preserve" y="279" zvalue="583">3901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="207" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1277.69,179) scale(1,1) translate(0,0)" writing-mode="lr" x="1277.69" xml:space="preserve" y="183.5" zvalue="584">35kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="211" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1361.87,436.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1361.87" xml:space="preserve" y="441" zvalue="587">35kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="213" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1388,347.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1388" xml:space="preserve" y="352" zvalue="588">3121</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="217" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,783.25,622.5) scale(1,1) translate(0,0)" writing-mode="lr" x="783.25" xml:space="preserve" y="627" zvalue="593">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="216" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,752.688,527) scale(1,1) translate(1.52697e-13,0)" writing-mode="lr" x="752.6900000000001" xml:space="preserve" y="531.5" zvalue="595">10kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="244" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1018,736.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1018" xml:space="preserve" y="741" zvalue="599">012</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="236" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,965.25,797.5) scale(1,1) translate(0,0)" writing-mode="lr" x="965.25" xml:space="preserve" y="802" zvalue="602">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="253" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,996,899.5) scale(1,1) translate(0,0)" writing-mode="lr" x="996" xml:space="preserve" y="904" zvalue="608">10kV新寨隧道线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="258" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1342,738.695) scale(1,1) translate(0,0)" writing-mode="lr" x="1342" xml:space="preserve" y="743.1900000000001" zvalue="612">013</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="257" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1290.25,799.695) scale(1,1) translate(0,0)" writing-mode="lr" x="1290.25" xml:space="preserve" y="804.1900000000001" zvalue="615">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="256" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1320,900.695) scale(1,1) translate(0,0)" writing-mode="lr" x="1320" xml:space="preserve" y="905.1900000000001" zvalue="621">10kV护国河大桥线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="285" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,732,738.695) scale(1,1) translate(0,0)" writing-mode="lr" x="732" xml:space="preserve" y="743.1900000000001" zvalue="638">011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="284" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,678.25,799.695) scale(1,1) translate(0,0)" writing-mode="lr" x="678.25" xml:space="preserve" y="804.1900000000001" zvalue="640">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="292" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,706,899.5) scale(1,1) translate(0,0)" writing-mode="lr" x="706" xml:space="preserve" y="904" zvalue="645">10kV1号电容器</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="60.19" y="313.25" zvalue="534"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="128">
   <path class="kv35" d="M 621.67 316.59 L 1590.29 316.59" stroke-width="6" zvalue="6"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674237808644" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674237808644"/></metadata>
  <path d="M 621.67 316.59 L 1590.29 316.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="166">
   <path class="kv10" d="M 593.1 666.31 L 1618.86 666.31" stroke-width="6" zvalue="8"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674237874181" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674237874181"/></metadata>
  <path d="M 593.1 666.31 L 1618.86 666.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="MeasurementClass">
  <g id="240">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="240" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,613.667,340.591) scale(1,1) translate(0,0)" writing-mode="lr" x="613.24" xml:space="preserve" y="345.29" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124602839044" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="241">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="241" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,583.095,692.305) scale(1,1) translate(0,0)" writing-mode="lr" x="582.67" xml:space="preserve" y="697" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124603363332" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="51">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="51" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,127.571,210.468) scale(1,1) translate(0,0)" writing-mode="lr" x="127.72" xml:space="preserve" y="216.9" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124603035652" ObjectName="F"/>
   </metadata>
  </g>
  <g id="165">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="165" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,127.571,235.357) scale(1,1) translate(0,0)" writing-mode="lr" x="127.72" xml:space="preserve" y="241.79" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124603559943" ObjectName="F"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="8" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,127.571,186.5) scale(1,1) translate(0,0)" writing-mode="lr" x="127.72" xml:space="preserve" y="192.93" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124613128196" ObjectName=""/>
   </metadata>
  </g>
  <g id="13">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="13" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,304.571,186.357) scale(1,1) translate(0,0)" writing-mode="lr" x="304.72" xml:space="preserve" y="192.79" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124613193732" ObjectName=""/>
   </metadata>
  </g>
  <g id="278">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="278" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,142.571,258.552) scale(1,1) translate(-2.00069e-14,0)" writing-mode="lr" x="142.8" xml:space="preserve" y="265.04" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124604608516" ObjectName="油温"/>
   </metadata>
  </g>
  <g id="275">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="275" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,142.571,281.552) scale(1,1) translate(0,0)" writing-mode="lr" x="142.77" xml:space="preserve" y="288.04" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124604215303" ObjectName="Tap"/>
   </metadata>
  </g>
  <g id="144">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="144" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,125.071,528.093) scale(1,1) translate(0,0)" writing-mode="lr" x="125.2" xml:space="preserve" y="533" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124602642436" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="143">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="143" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,125.071,553.218) scale(1,1) translate(0,-1.20508e-13)" writing-mode="lr" x="125.2" xml:space="preserve" y="558.13" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124602707972" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="142">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="142" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,125.071,578.343) scale(1,1) translate(0,-2.52173e-13)" writing-mode="lr" x="125.2" xml:space="preserve" y="583.25" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124602773508" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="141">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="141" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,126.5,502.968) scale(1,1) translate(0,0)" writing-mode="lr" x="126.63" xml:space="preserve" y="507.88" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124602904580" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="139">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="139" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,242.905,526.982) scale(1,1) translate(0,0)" writing-mode="lr" x="243.03" xml:space="preserve" y="531.89" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124603166724" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="138">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="138" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,242.905,552.107) scale(1,1) translate(0,0)" writing-mode="lr" x="243.03" xml:space="preserve" y="557.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124603232262" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="56">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,242.905,577.232) scale(1,1) translate(0,0)" writing-mode="lr" x="243.03" xml:space="preserve" y="582.14" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124603297799" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="55">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="55" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,242.905,501.857) scale(1,1) translate(0,0)" writing-mode="lr" x="243.03" xml:space="preserve" y="506.77" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124603428868" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="148">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="148" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,124.321,603.468) scale(1,1) translate(0,0)" writing-mode="lr" x="124.45" xml:space="preserve" y="608.38" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124603101188" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="140">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="140" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,242.613,602.357) scale(1,1) translate(0,0)" writing-mode="lr" x="242.74" xml:space="preserve" y="607.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124603625478" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="1">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="1" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,995,62) scale(1,1) translate(0,0)" writing-mode="lr" x="994.5599999999999" xml:space="preserve" y="66.67" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124607295492" ObjectName="P"/>
   </metadata>
  </g>
  <g id="2">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="2" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,995,80) scale(1,1) translate(0,0)" writing-mode="lr" x="994.5599999999999" xml:space="preserve" y="84.67" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124607361028" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="3">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="3" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,995,97) scale(1,1) translate(0,0)" writing-mode="lr" x="994.5599999999999" xml:space="preserve" y="101.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124607426564" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="5">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="5" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1202.5,369) scale(1,1) translate(0,0)" writing-mode="lr" x="1201.97" xml:space="preserve" y="375.21" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124603691012" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="6">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="6" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1202.5,398) scale(1,1) translate(0,0)" writing-mode="lr" x="1201.97" xml:space="preserve" y="404.21" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124603756548" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="7" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1202.5,574) scale(1,1) translate(0,0)" writing-mode="lr" x="1201.97" xml:space="preserve" y="580.21" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124603822084" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="9">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="9" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1202.5,603) scale(1,1) translate(0,0)" writing-mode="lr" x="1201.97" xml:space="preserve" y="609.21" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124603887622" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="10">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="10" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1202.5,427) scale(1,1) translate(0,0)" writing-mode="lr" x="1201.97" xml:space="preserve" y="433.21" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124603953159" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="11">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="11" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1202.5,628) scale(1,1) translate(0,0)" writing-mode="lr" x="1201.97" xml:space="preserve" y="634.21" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124604280836" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="12">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="12" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,995,927.5) scale(1,1) translate(0,1.01713e-13)" writing-mode="lr" x="994.53" xml:space="preserve" y="932.1799999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124609458180" ObjectName="P"/>
   </metadata>
  </g>
  <g id="14">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="14" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1320,927.5) scale(1,1) translate(0,1.01713e-13)" writing-mode="lr" x="1319.53" xml:space="preserve" y="932.1799999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124610375684" ObjectName="P"/>
   </metadata>
  </g>
  <g id="15">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="15" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,995,948.5) scale(1,1) translate(0,1.04045e-13)" writing-mode="lr" x="994.53" xml:space="preserve" y="953.1799999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124609523716" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="16">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="16" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1319,948.5) scale(1,1) translate(0,-2.0809e-13)" writing-mode="lr" x="1318.53" xml:space="preserve" y="953.1799999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124610441220" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="17">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="17" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,995,969.5) scale(1,1) translate(0,1.06376e-13)" writing-mode="lr" x="994.53" xml:space="preserve" y="974.1799999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124609589252" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="18">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="18" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1319,969.5) scale(1,1) translate(0,1.06376e-13)" writing-mode="lr" x="1318.53" xml:space="preserve" y="974.1799999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124610506756" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="StateClass">
  <g id="160">
   <use height="30" transform="rotate(0,334.196,327.857) scale(0.708333,0.665547) translate(133.235,159.739)" width="30" x="323.57" xlink:href="#State:红绿圆(方形)_0" y="317.87" zvalue="538"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374889189379" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.196,327.857) scale(0.708333,0.665547) translate(133.235,159.739)" width="30" x="323.57" y="317.87"/></g>
  <g id="54">
   <use height="30" transform="rotate(0,238.571,327.857) scale(0.708333,0.665547) translate(93.8603,159.739)" width="30" x="227.95" xlink:href="#State:红绿圆(方形)_0" y="317.87" zvalue="539"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562951129399301" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,238.571,327.857) scale(0.708333,0.665547) translate(93.8603,159.739)" width="30" x="227.95" y="317.87"/></g>
  <g id="830">
   <use height="30" transform="rotate(0,311.812,141.964) scale(1.27778,1.03333) translate(-55.2853,-4.07948)" width="90" x="254.31" xlink:href="#State:全站检修_0" y="126.46" zvalue="669"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549583347714" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,311.812,141.964) scale(1.27778,1.03333) translate(-55.2853,-4.07948)" width="90" x="254.31" y="126.46"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="147">
   <g id="1470">
    <use class="kv35" height="30" transform="rotate(0,1085,501.5) scale(2.58333,2.6) translate(-646,-284.615)" width="24" x="1054" xlink:href="#PowerTransformer2:可调不带中性点_0" y="462.5" zvalue="557"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874427138052" ObjectName="35"/>
    </metadata>
   </g>
   <g id="1471">
    <use class="kv10" height="30" transform="rotate(0,1085,501.5) scale(2.58333,2.6) translate(-646,-284.615)" width="24" x="1054" xlink:href="#PowerTransformer2:可调不带中性点_1" y="462.5" zvalue="557"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874427203588" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399445774340" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399445774340"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1085,501.5) scale(2.58333,2.6) translate(-646,-284.615)" width="24" x="1054" y="462.5"/></g>
 </g>
 <g id="BreakerClass">
  <g id="157">
   <use class="kv10" height="20" transform="rotate(0,1085,603.5) scale(2.2,2.2) translate(-585.818,-317.182)" width="10" x="1074" xlink:href="#Breaker:小车断路器_0" y="581.5" zvalue="564"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924488855557" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473924488855557"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1085,603.5) scale(2.2,2.2) translate(-585.818,-317.182)" width="10" x="1074" y="581.5"/></g>
  <g id="159">
   <use class="kv35" height="20" transform="rotate(0,1085,383.5) scale(2.2,2.2) translate(-585.818,-197.182)" width="10" x="1074" xlink:href="#Breaker:小车断路器_0" y="361.5" zvalue="567"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924488921093" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473924488921093"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1085,383.5) scale(2.2,2.2) translate(-585.818,-197.182)" width="10" x="1074" y="361.5"/></g>
  <g id="180">
   <use class="kv35" height="20" transform="rotate(0,996,258.5) scale(2.2,2.2) translate(-537.273,-129)" width="10" x="985" xlink:href="#Breaker:小车断路器_0" y="236.5" zvalue="578"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924488986629" ObjectName="35kV清龙线龙准坡T线311断路器"/>
   <cge:TPSR_Ref TObjectID="6473924488986629"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,996,258.5) scale(2.2,2.2) translate(-537.273,-129)" width="10" x="985" y="236.5"/></g>
  <g id="251">
   <use class="kv10" height="20" transform="rotate(0,995,737.5) scale(2.2,2.2) translate(-536.727,-390.273)" width="10" x="984" xlink:href="#Breaker:小车断路器_0" y="715.5" zvalue="598"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924489052165" ObjectName="10kV新寨隧道线012断路器"/>
   <cge:TPSR_Ref TObjectID="6473924489052165"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,995,737.5) scale(2.2,2.2) translate(-536.727,-390.273)" width="10" x="984" y="715.5"/></g>
  <g id="268">
   <use class="kv10" height="20" transform="rotate(0,1319,739.695) scale(2.2,2.2) translate(-713.455,-391.47)" width="10" x="1308" xlink:href="#Breaker:小车断路器_0" y="717.6948051948052" zvalue="611"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924489117701" ObjectName="10kV护国河大桥线013断路器"/>
   <cge:TPSR_Ref TObjectID="6473924489117701"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1319,739.695) scale(2.2,2.2) translate(-713.455,-391.47)" width="10" x="1308" y="717.6948051948052"/></g>
  <g id="290">
   <use class="kv10" height="20" transform="rotate(0,709,739.695) scale(2.2,2.2) translate(-380.727,-391.47)" width="10" x="698" xlink:href="#Breaker:小车断路器_0" y="717.6948051948052" zvalue="637"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924489183237" ObjectName="10kV1号电容器011断路器"/>
   <cge:TPSR_Ref TObjectID="6473924489183237"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,709,739.695) scale(2.2,2.2) translate(-380.727,-391.47)" width="10" x="698" y="717.6948051948052"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="163">
   <path class="kv35" d="M 1085 363.15 L 1085 316.59" stroke-width="1" zvalue="568"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="159@0" LinkObjectIDznd="128@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1085 363.15 L 1085 316.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="164">
   <path class="kv35" d="M 1085 403.3 L 1085.03 465.3" stroke-width="1" zvalue="569"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="159@1" LinkObjectIDznd="147@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1085 403.3 L 1085.03 465.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="167">
   <path class="kv10" d="M 1085 538.02 L 1085 583.15" stroke-width="1" zvalue="570"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="147@1" LinkObjectIDznd="157@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1085 538.02 L 1085 583.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="168">
   <path class="kv10" d="M 1085 623.3 L 1085 666.31" stroke-width="1" zvalue="571"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157@1" LinkObjectIDznd="166@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1085 623.3 L 1085 666.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="176">
   <path class="kv35" d="M 1040.05 437 L 1040.05 424.5 L 1085.01 424.5" stroke-width="1" zvalue="573"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="169@0" LinkObjectIDznd="164" MaxPinNum="2"/>
   </metadata>
  <path d="M 1040.05 437 L 1040.05 424.5 L 1085.01 424.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="kv35" d="M 1134.03 434.13 L 1134.03 424.5 L 1085 424.5" stroke-width="1" zvalue="576"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="178@0" LinkObjectIDznd="176" MaxPinNum="2"/>
   </metadata>
  <path d="M 1134.03 434.13 L 1134.03 424.5 L 1085 424.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="198">
   <path class="kv35" d="M 994.83 186.17 L 994.83 238.15" stroke-width="1" zvalue="580"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="183@0" LinkObjectIDznd="180@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 994.83 186.17 L 994.83 238.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="200">
   <path class="kv35" d="M 996 278.3 L 996 316.59" stroke-width="1" zvalue="581"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="180@1" LinkObjectIDznd="128@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 996 278.3 L 996 316.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="208">
   <path class="kv35" d="M 1277.75 239.79 L 1277.75 255.75" stroke-width="1" zvalue="584"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="204@0" LinkObjectIDznd="202@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1277.75 239.79 L 1277.75 255.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="209">
   <path class="kv35" d="M 1277.75 290.25 L 1277.75 316.59" stroke-width="1" zvalue="585"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="202@0" LinkObjectIDznd="128@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1277.75 290.25 L 1277.75 316.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="214">
   <path class="kv35" d="M 1362 332.5 L 1362 316.59" stroke-width="1" zvalue="588"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="212@1" LinkObjectIDznd="128@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1362 332.5 L 1362 316.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="215">
   <path class="kv35" d="M 1361.99 364.8 L 1361.99 380.75" stroke-width="1" zvalue="589"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="212@0" LinkObjectIDznd="210@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1361.99 364.8 L 1361.99 380.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="220">
   <path class="kv10" d="M 752.75 587.79 L 752.75 603.75" stroke-width="1" zvalue="594"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="221@0" LinkObjectIDznd="222@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 752.75 587.79 L 752.75 603.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="219">
   <path class="kv10" d="M 752.75 638.25 L 752.75 666.31" stroke-width="1" zvalue="596"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="222@0" LinkObjectIDznd="166@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 752.75 638.25 L 752.75 666.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="255">
   <path class="kv10" d="M 995 717.15 L 995 666.31" stroke-width="1" zvalue="609"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="251@0" LinkObjectIDznd="166@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 995 717.15 L 995 666.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="259">
   <path class="kv10" d="M 1319 719.34 L 1319 666.31" stroke-width="1" zvalue="623"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="268@0" LinkObjectIDznd="166@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1319 719.34 L 1319 666.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="274">
   <path class="kv10" d="M 1319 759.49 L 1319 850.04" stroke-width="1" zvalue="629"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="268@1" LinkObjectIDznd="261@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1319 759.49 L 1319 850.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="277">
   <path class="kv10" d="M 1274.05 793.19 L 1274.05 781.5 L 1319 781.5" stroke-width="1" zvalue="630"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="266@0" LinkObjectIDznd="274" MaxPinNum="2"/>
   </metadata>
  <path d="M 1274.05 793.19 L 1274.05 781.5 L 1319 781.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="279">
   <path class="kv10" d="M 1368.03 790.33 L 1368.03 781.5 L 1318 781.5" stroke-width="1" zvalue="631"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="263@0" LinkObjectIDznd="277" MaxPinNum="2"/>
   </metadata>
  <path d="M 1368.03 790.33 L 1368.03 781.5 L 1318 781.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="281">
   <path class="kv10" d="M 995 848.85 L 995 757.3" stroke-width="1" zvalue="633"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="252@0" LinkObjectIDznd="251@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 995 848.85 L 995 757.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="282">
   <path class="kv10" d="M 1044.03 788.13 L 1044.03 779.5 L 995 779.5" stroke-width="1" zvalue="634"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="246@0" LinkObjectIDznd="281" MaxPinNum="2"/>
   </metadata>
  <path d="M 1044.03 788.13 L 1044.03 779.5 L 995 779.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="283">
   <path class="kv10" d="M 950.05 791 L 950.05 781.5 L 995 781.5" stroke-width="1" zvalue="635"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="249@0" LinkObjectIDznd="281" MaxPinNum="2"/>
   </metadata>
  <path d="M 950.05 791 L 950.05 781.5 L 995 781.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="288">
   <path class="kv10" d="M 709 719.34 L 709 666.31" stroke-width="1" zvalue="641"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="290@0" LinkObjectIDznd="166@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 709 719.34 L 709 666.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="287">
   <path class="kv10" d="M 709 836.77 L 709 759.49" stroke-width="1" zvalue="642"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="291@0" LinkObjectIDznd="290@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 709 836.77 L 709 759.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="286">
   <path class="kv10" d="M 664.05 793.19 L 664.05 783.69 L 709 783.69" stroke-width="1" zvalue="643"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="289@0" LinkObjectIDznd="287" MaxPinNum="2"/>
   </metadata>
  <path d="M 664.05 793.19 L 664.05 783.69 L 709 783.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="169">
   <use class="kv35" height="20" transform="rotate(0,1040,446.5) scale(1,1) translate(0,0)" width="10" x="1035" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="436.5" zvalue="572"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449676181510" ObjectName="#1主变35kV侧30167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449676181510"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1040,446.5) scale(1,1) translate(0,0)" width="10" x="1035" y="436.5"/></g>
  <g id="249">
   <use class="kv10" height="20" transform="rotate(0,950,800.5) scale(1,1) translate(0,0)" width="10" x="945" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="790.5" zvalue="601"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449676902405" ObjectName="10kV新寨隧道线01267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449676902405"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,950,800.5) scale(1,1) translate(0,0)" width="10" x="945" y="790.5"/></g>
  <g id="266">
   <use class="kv10" height="20" transform="rotate(0,1274,802.695) scale(1,1) translate(0,0)" width="10" x="1269" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="792.6948051948052" zvalue="614"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449677230086" ObjectName="10kV护国河大桥线01367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449677230086"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1274,802.695) scale(1,1) translate(0,0)" width="10" x="1269" y="792.6948051948052"/></g>
  <g id="289">
   <use class="kv10" height="20" transform="rotate(0,664,802.695) scale(1,1) translate(0,0)" width="10" x="659" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="792.6948051948052" zvalue="639"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449677361158" ObjectName="10kV1号电容器01167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449677361158"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,664,802.695) scale(1,1) translate(0,0)" width="10" x="659" y="792.6948051948052"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="178">
   <use class="kv35" height="26" transform="rotate(0,1134,446.5) scale(1,1) translate(0,0)" width="12" x="1128" xlink:href="#Accessory:避雷器1_0" y="433.5" zvalue="575"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449676247046" ObjectName="#1主变35kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1134,446.5) scale(1,1) translate(0,0)" width="12" x="1128" y="433.5"/></g>
  <g id="204">
   <use class="kv35" height="30" transform="rotate(0,1283.69,219.5) scale(0.857143,1.4) translate(211.448,-56.7143)" width="35" x="1268.687541475207" xlink:href="#Accessory:10kV母线PT带消谐装置_0" y="198.5" zvalue="583"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449676443653" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1283.69,219.5) scale(0.857143,1.4) translate(211.448,-56.7143)" width="35" x="1268.687541475207" y="198.5"/></g>
  <g id="221">
   <use class="kv10" height="30" transform="rotate(0,758.688,567.5) scale(0.857143,1.4) translate(123.948,-156.143)" width="35" x="743.6875414752067" xlink:href="#Accessory:10kV母线PT带消谐装置_0" y="546.5" zvalue="592"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449676640262" ObjectName="10kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,758.688,567.5) scale(0.857143,1.4) translate(123.948,-156.143)" width="35" x="743.6875414752067" y="546.5"/></g>
  <g id="246">
   <use class="kv10" height="26" transform="rotate(0,1044,800.5) scale(1,1) translate(0,0)" width="12" x="1038" xlink:href="#Accessory:避雷器1_0" y="787.5" zvalue="605"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449676771334" ObjectName="10kV新寨隧道线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1044,800.5) scale(1,1) translate(0,0)" width="12" x="1038" y="787.5"/></g>
  <g id="263">
   <use class="kv10" height="26" transform="rotate(0,1368,802.695) scale(1,1) translate(0,0)" width="12" x="1362" xlink:href="#Accessory:避雷器1_0" y="789.6948051948052" zvalue="618"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449677099014" ObjectName="10kV护国河大桥线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1368,802.695) scale(1,1) translate(0,0)" width="12" x="1362" y="789.6948051948052"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="202">
   <use class="kv35" height="42" transform="rotate(0,1281,276.5) scale(1,-1) translate(0,-553)" width="30" x="1266" xlink:href="#Disconnector:手车刀闸带避雷器_0" y="255.5" zvalue="582"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449676378118" ObjectName="35KV母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449676378118"/></metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,1281,276.5) scale(1,-1) translate(0,-553)" width="30" x="1266" y="255.5"/></g>
  <g id="212">
   <use class="kv35" height="33" transform="rotate(0,1362,348.5) scale(1,-1) translate(0,-697)" width="14" x="1355" xlink:href="#Disconnector:手车隔离开关13_0" y="332" zvalue="587"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449676574725" ObjectName="35kV1号站用变3121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449676574725"/></metadata>
  <rect fill="white" height="33" opacity="0" stroke="white" transform="rotate(0,1362,348.5) scale(1,-1) translate(0,-697)" width="14" x="1355" y="332"/></g>
  <g id="222">
   <use class="kv10" height="42" transform="rotate(0,756,624.5) scale(1,-1) translate(0,-1249)" width="30" x="741" xlink:href="#Disconnector:手车刀闸带避雷器_0" y="603.5" zvalue="591"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449676705798" ObjectName="10KV母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449676705798"/></metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,756,624.5) scale(1,-1) translate(0,-1249)" width="30" x="741" y="603.5"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="210">
   <use class="kv35" height="38" transform="rotate(0,1365.87,399.5) scale(1,1) translate(0,0)" width="26" x="1352.87167736084" xlink:href="#EnergyConsumer:站用变1接点_0" y="380.5" zvalue="586"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449676509189" ObjectName="35kV1号站用变"/>
   </metadata>
  <rect fill="white" height="38" opacity="0" stroke="white" transform="rotate(0,1365.87,399.5) scale(1,1) translate(0,0)" width="26" x="1352.87167736084" y="380.5"/></g>
  <g id="252">
   <use class="kv10" height="30" transform="rotate(0,995,865.5) scale(1.25,-1.23333) translate(-197.5,-1563.76)" width="12" x="987.5" xlink:href="#EnergyConsumer:负荷_0" y="847" zvalue="607"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449676967942" ObjectName="10kV新寨隧道线"/>
   <cge:TPSR_Ref TObjectID="6192449676967942"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,995,865.5) scale(1.25,-1.23333) translate(-197.5,-1563.76)" width="12" x="987.5" y="847"/></g>
  <g id="261">
   <use class="kv10" height="30" transform="rotate(0,1319,866.695) scale(1.25,-1.23333) translate(-262.3,-1565.92)" width="12" x="1311.5" xlink:href="#EnergyConsumer:负荷_0" y="848.1948051948052" zvalue="620"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449677033478" ObjectName="10kV护国河大桥线"/>
   <cge:TPSR_Ref TObjectID="6192449677033478"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1319,866.695) scale(1.25,-1.23333) translate(-262.3,-1565.92)" width="12" x="1311.5" y="848.1948051948052"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="291">
   <use class="kv10" height="40" transform="rotate(0,709,860.167) scale(1.33333,1.33333) translate(-173.25,-208.375)" width="24" x="693" xlink:href="#Compensator:西郊变电容_0" y="833.5" zvalue="644"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449677426694" ObjectName="10kV1号电容器"/>
   <cge:TPSR_Ref TObjectID="6192449677426694"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,709,860.167) scale(1.33333,1.33333) translate(-173.25,-208.375)" width="24" x="693" y="833.5"/></g>
 </g>
</svg>