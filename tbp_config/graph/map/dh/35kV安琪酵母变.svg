<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549592522754" height="1045" id="thSvg" source="NR-PCS9000" viewBox="0 0 1900 1045" width="1900">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀（规范制图）_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000004" y2="0.9166666666666679"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀（规范制图）_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.89694619969018" y2="15.89694619969018"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.73157590710537" y2="17.73157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.48348701738202" y2="19.48348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.05" x2="5.05" y1="3.566666666666666" y2="15.81666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀（规范制图）_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_0" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="9.750000000000004" y2="4.166666666666668"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="32"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="1" y1="27" y2="11"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_1" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="27" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_2" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="1.499999999999999" y1="26" y2="9.966666666666669"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.999999999999998" x2="12.41666666666667" y1="26.25" y2="10.05"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="Accessory:pt带容断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <path d="M 15 12.5 L 15 3.08333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15.01,6.21) scale(1,1) translate(0,0)" width="4.92" x="12.55" y="2.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.083333333333332" y2="0.25"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变13_0" viewBox="0,0,32,35">
   <use terminal-index="0" type="0" x="16" xlink:href="#terminal" y="3.5"/>
   <ellipse cx="16.04" cy="11.97" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.97" cy="24.14" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067018" x2="16.03525619067018" y1="20.85" y2="24.95894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067017" x2="10.66666666666667" y1="24.98394833233988" y2="28.66666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.06302903072498" x2="21.16666666666667" y1="24.93990500916851" y2="28.66666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067018" x2="16.03525619067018" y1="7.6" y2="11.70894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067017" x2="12.05" y1="11.73394833233987" y2="14.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.06302903072498" x2="20.15" y1="11.68990500916851" y2="14.5"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="1" y1="23" y2="13"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="0.25" y2="25.75"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV安琪酵母变" InitShowingPlane="" fill="rgb(0,0,0)" height="1045" width="1900" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <line fill="none" id="558" stroke="rgb(0,39,45)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.79365079365175" x2="323.3811994317853" y1="65.414843687847" y2="65.414843687847" zvalue="906"/>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="528" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,101.196,313.964) scale(1,1) translate(0,0)" width="72.88" x="64.76000000000001" y="301.96" zvalue="936"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,101.196,313.964) scale(1,1) translate(0,0)" writing-mode="lr" x="101.2" xml:space="preserve" y="318.46" zvalue="936">信号一览</text>
  <image height="60" id="2" preserveAspectRatio="xMidYMid slice" width="273.81" x="48.02" xlink:href="logo.png" y="38.46"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="372" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,184.919,68.4643) scale(1,1) translate(-1.06617e-14,0)" writing-mode="lr" x="184.92" xml:space="preserve" y="71.95999999999999" zvalue="1166"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="3" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,178.405,66.5296) scale(1,1) translate(2.00633e-14,0)" writing-mode="lr" x="178.4" xml:space="preserve" y="75.53" zvalue="1167">35kV安琪酵母变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="370" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,616.647,292.305) scale(1,1) translate(0,0)" writing-mode="lr" x="616.65" xml:space="preserve" y="296.81" zvalue="903">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="369" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,485.076,633.464) scale(1,1) translate(0,0)" writing-mode="lr" x="485.08" xml:space="preserve" y="637.96" zvalue="905">10kVⅠ段母线</text>
  <line fill="none" id="557" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="379.5714285714289" x2="379.5714285714289" y1="38.21428571428567" y2="1028.214285714286" zvalue="907"/>
  <line fill="none" id="555" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.04835312140904" x2="322.6596131750282" y1="157.8422315970544" y2="157.8422315970544" zvalue="909"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="46.14285714285745" x2="117.5828571428574" y1="916.7770766533581" y2="916.7770766533581"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="46.14285714285745" x2="117.5828571428574" y1="968.1165766533582" y2="968.1165766533582"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="46.14285714285745" x2="46.14285714285745" y1="916.7770766533581" y2="968.1165766533582"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="117.5828571428574" x2="117.5828571428574" y1="916.7770766533581" y2="968.1165766533582"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="117.5833571428575" x2="352.1433571428574" y1="916.7770766533581" y2="916.7770766533581"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="117.5833571428575" x2="352.1433571428574" y1="968.1165766533582" y2="968.1165766533582"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="117.5833571428575" x2="117.5833571428575" y1="916.7770766533581" y2="968.1165766533582"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="352.1433571428574" x2="352.1433571428574" y1="916.7770766533581" y2="968.1165766533582"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="46.14285714285745" x2="117.5828571428574" y1="968.1165566533581" y2="968.1165566533581"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="46.14285714285745" x2="117.5828571428574" y1="995.5940566533582" y2="995.5940566533582"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="46.14285714285745" x2="46.14285714285745" y1="968.1165566533581" y2="995.5940566533582"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="117.5828571428574" x2="117.5828571428574" y1="968.1165566533581" y2="995.5940566533582"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="117.5833571428575" x2="188.6186571428575" y1="968.1165566533581" y2="968.1165566533581"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="117.5833571428575" x2="188.6186571428575" y1="995.5940566533582" y2="995.5940566533582"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="117.5833571428575" x2="117.5833571428575" y1="968.1165566533581" y2="995.5940566533582"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.6186571428575" x2="188.6186571428575" y1="968.1165566533581" y2="995.5940566533582"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.6186571428575" x2="270.3807571428574" y1="968.1165566533581" y2="968.1165566533581"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.6186571428575" x2="270.3807571428574" y1="995.5940566533582" y2="995.5940566533582"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.6186571428575" x2="188.6186571428575" y1="968.1165566533581" y2="995.5940566533582"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="270.3807571428574" x2="270.3807571428574" y1="968.1165566533581" y2="995.5940566533582"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="270.3806571428574" x2="352.1427571428575" y1="968.1165566533581" y2="968.1165566533581"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="270.3806571428574" x2="352.1427571428575" y1="995.5940566533582" y2="995.5940566533582"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="270.3806571428574" x2="270.3806571428574" y1="968.1165566533581" y2="995.5940566533582"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="352.1427571428575" x2="352.1427571428575" y1="968.1165566533581" y2="995.5940566533582"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="46.14285714285745" x2="117.5828571428574" y1="995.5939766533581" y2="995.5939766533581"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="46.14285714285745" x2="117.5828571428574" y1="1023.071476653358" y2="1023.071476653358"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="46.14285714285745" x2="46.14285714285745" y1="995.5939766533581" y2="1023.071476653358"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="117.5828571428574" x2="117.5828571428574" y1="995.5939766533581" y2="1023.071476653358"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="117.5833571428575" x2="188.6186571428575" y1="995.5939766533581" y2="995.5939766533581"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="117.5833571428575" x2="188.6186571428575" y1="1023.071476653358" y2="1023.071476653358"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="117.5833571428575" x2="117.5833571428575" y1="995.5939766533581" y2="1023.071476653358"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.6186571428575" x2="188.6186571428575" y1="995.5939766533581" y2="1023.071476653358"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.6186571428575" x2="270.3807571428574" y1="995.5939766533581" y2="995.5939766533581"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.6186571428575" x2="270.3807571428574" y1="1023.071476653358" y2="1023.071476653358"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.6186571428575" x2="188.6186571428575" y1="995.5939766533581" y2="1023.071476653358"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="270.3807571428574" x2="270.3807571428574" y1="995.5939766533581" y2="1023.071476653358"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="270.3806571428574" x2="352.1427571428575" y1="995.5939766533581" y2="995.5939766533581"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="270.3806571428574" x2="352.1427571428575" y1="1023.071476653358" y2="1023.071476653358"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="270.3806571428574" x2="270.3806571428574" y1="995.5939766533581" y2="1023.071476653358"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="352.1427571428575" x2="352.1427571428575" y1="995.5939766533581" y2="1023.071476653358"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="553" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,193.195,946.351) scale(1,1) translate(-1.14349e-14,1.03864e-13)" writing-mode="lr" x="51.5" xml:space="preserve" y="952.35" zvalue="911">参考图号       LongZhunPo-01-2017</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="552" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,123.72,983.719) scale(1,1) translate(5.4379e-14,-1.51218e-12)" writing-mode="lr" x="61.23" xml:space="preserve" y="989.72" zvalue="912">制图             </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="551" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,267.024,984.719) scale(1,1) translate(1.32108e-13,-1.51373e-12)" writing-mode="lr" x="198.32" xml:space="preserve" y="990.72" zvalue="913">绘制日期    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="550" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,74.8138,1013.28) scale(1,1) translate(-5.08955e-14,-1.55812e-12)" writing-mode="lr" x="74.81" xml:space="preserve" y="1019.28" zvalue="914">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="549" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,275.318,1011.28) scale(1,1) translate(0,1.11072e-13)" writing-mode="lr" x="198.49" xml:space="preserve" y="1017.28" zvalue="915">更新日期    20210812</text>
  <line fill="none" id="548" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="45.42754212945238" x2="350.0388021830715" y1="612.6088163836553" y2="612.6088163836553" zvalue="916"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="546" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,88.4921,629.651) scale(1,1) translate(3.96159e-15,-1.35658e-13)" writing-mode="lr" x="88.49213663347393" xml:space="preserve" y="634.1506668831529" zvalue="918">危险点(双击编辑）：</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.142857142857338" x2="188.1428571428573" y1="161.0714285714287" y2="161.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.142857142857338" x2="188.1428571428573" y1="187.0714285714287" y2="187.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.142857142857338" x2="7.142857142857338" y1="161.0714285714287" y2="187.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.1428571428573" x2="188.1428571428573" y1="161.0714285714287" y2="187.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.1428571428573" x2="369.1428571428573" y1="161.0714285714287" y2="161.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.1428571428573" x2="369.1428571428573" y1="187.0714285714287" y2="187.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.1428571428573" x2="188.1428571428573" y1="161.0714285714287" y2="187.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369.1428571428573" x2="369.1428571428573" y1="161.0714285714287" y2="187.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.142857142857338" x2="188.1428571428573" y1="187.0714285714287" y2="187.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.142857142857338" x2="188.1428571428573" y1="211.3214285714287" y2="211.3214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.142857142857338" x2="7.142857142857338" y1="187.0714285714287" y2="211.3214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.1428571428573" x2="188.1428571428573" y1="187.0714285714287" y2="211.3214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.1428571428573" x2="369.1428571428573" y1="187.0714285714287" y2="187.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.1428571428573" x2="369.1428571428573" y1="211.3214285714287" y2="211.3214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.1428571428573" x2="188.1428571428573" y1="187.0714285714287" y2="211.3214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369.1428571428573" x2="369.1428571428573" y1="187.0714285714287" y2="211.3214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.142857142857338" x2="188.1428571428573" y1="211.3214285714287" y2="211.3214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.142857142857338" x2="188.1428571428573" y1="234.0714285714287" y2="234.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.142857142857338" x2="7.142857142857338" y1="211.3214285714287" y2="234.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.1428571428573" x2="188.1428571428573" y1="211.3214285714287" y2="234.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.1428571428573" x2="369.1428571428573" y1="211.3214285714287" y2="211.3214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.1428571428573" x2="369.1428571428573" y1="234.0714285714287" y2="234.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.1428571428573" x2="188.1428571428573" y1="211.3214285714287" y2="234.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369.1428571428573" x2="369.1428571428573" y1="211.3214285714287" y2="234.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.142857142857338" x2="188.1428571428573" y1="234.0714285714287" y2="234.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.142857142857338" x2="188.1428571428573" y1="256.8214285714287" y2="256.8214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.142857142857338" x2="7.142857142857338" y1="234.0714285714287" y2="256.8214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.1428571428573" x2="188.1428571428573" y1="234.0714285714287" y2="256.8214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.1428571428573" x2="369.1428571428573" y1="234.0714285714287" y2="234.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.1428571428573" x2="369.1428571428573" y1="256.8214285714287" y2="256.8214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.1428571428573" x2="188.1428571428573" y1="234.0714285714287" y2="256.8214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369.1428571428573" x2="369.1428571428573" y1="234.0714285714287" y2="256.8214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.142857142857338" x2="188.1428571428573" y1="256.8214285714287" y2="256.8214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.142857142857338" x2="188.1428571428573" y1="279.5714285714287" y2="279.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.142857142857338" x2="7.142857142857338" y1="256.8214285714287" y2="279.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.1428571428573" x2="188.1428571428573" y1="256.8214285714287" y2="279.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.1428571428573" x2="369.1428571428573" y1="256.8214285714287" y2="256.8214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.1428571428573" x2="369.1428571428573" y1="279.5714285714287" y2="279.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.1428571428573" x2="188.1428571428573" y1="256.8214285714287" y2="279.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369.1428571428573" x2="369.1428571428573" y1="256.8214285714287" y2="279.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.14285714285745" x2="103.9173571428574" y1="441.0714285714287" y2="441.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.14285714285745" x2="103.9173571428574" y1="479.3537285714287" y2="479.3537285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.14285714285745" x2="58.14285714285745" y1="441.0714285714287" y2="479.3537285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9173571428574" x2="103.9173571428574" y1="441.0714285714287" y2="479.3537285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9173571428574" x2="162.7237571428575" y1="441.0714285714287" y2="441.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9173571428574" x2="162.7237571428575" y1="479.3537285714287" y2="479.3537285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9173571428574" x2="103.9173571428574" y1="441.0714285714287" y2="479.3537285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.7237571428575" x2="162.7237571428575" y1="441.0714285714287" y2="479.3537285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.7237571428575" x2="221.5301571428574" y1="441.0714285714287" y2="441.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.7237571428575" x2="221.5301571428574" y1="479.3537285714287" y2="479.3537285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.7237571428575" x2="162.7237571428575" y1="441.0714285714287" y2="479.3537285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.5301571428574" x2="221.5301571428574" y1="441.0714285714287" y2="479.3537285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.5300571428575" x2="280.3364571428574" y1="441.0714285714287" y2="441.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.5300571428575" x2="280.3364571428574" y1="479.3537285714287" y2="479.3537285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.5300571428575" x2="221.5300571428575" y1="441.0714285714287" y2="479.3537285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.3364571428574" x2="280.3364571428574" y1="441.0714285714287" y2="479.3537285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.3364571428574" x2="339.1428571428575" y1="441.0714285714287" y2="441.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.3364571428574" x2="339.1428571428575" y1="479.3537285714287" y2="479.3537285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.3364571428574" x2="280.3364571428574" y1="441.0714285714287" y2="479.3537285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="339.1428571428575" x2="339.1428571428575" y1="441.0714285714287" y2="479.3537285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.14285714285745" x2="103.9173571428574" y1="479.3537285714286" y2="479.3537285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.14285714285745" x2="103.9173571428574" y1="504.0331285714287" y2="504.0331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.14285714285745" x2="58.14285714285745" y1="479.3537285714286" y2="504.0331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9173571428574" x2="103.9173571428574" y1="479.3537285714286" y2="504.0331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9173571428574" x2="162.7237571428575" y1="479.3537285714286" y2="479.3537285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9173571428574" x2="162.7237571428575" y1="504.0331285714287" y2="504.0331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9173571428574" x2="103.9173571428574" y1="479.3537285714286" y2="504.0331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.7237571428575" x2="162.7237571428575" y1="479.3537285714286" y2="504.0331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.7237571428575" x2="221.5301571428574" y1="479.3537285714286" y2="479.3537285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.7237571428575" x2="221.5301571428574" y1="504.0331285714287" y2="504.0331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.7237571428575" x2="162.7237571428575" y1="479.3537285714286" y2="504.0331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.5301571428574" x2="221.5301571428574" y1="479.3537285714286" y2="504.0331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.5300571428575" x2="280.3364571428574" y1="479.3537285714286" y2="479.3537285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.5300571428575" x2="280.3364571428574" y1="504.0331285714287" y2="504.0331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.5300571428575" x2="221.5300571428575" y1="479.3537285714286" y2="504.0331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.3364571428574" x2="280.3364571428574" y1="479.3537285714286" y2="504.0331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.3364571428574" x2="339.1428571428575" y1="479.3537285714286" y2="479.3537285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.3364571428574" x2="339.1428571428575" y1="504.0331285714287" y2="504.0331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.3364571428574" x2="280.3364571428574" y1="479.3537285714286" y2="504.0331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="339.1428571428575" x2="339.1428571428575" y1="479.3537285714286" y2="504.0331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.14285714285745" x2="103.9173571428574" y1="504.0331285714287" y2="504.0331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.14285714285745" x2="103.9173571428574" y1="528.7125285714287" y2="528.7125285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.14285714285745" x2="58.14285714285745" y1="504.0331285714287" y2="528.7125285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9173571428574" x2="103.9173571428574" y1="504.0331285714287" y2="528.7125285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9173571428574" x2="162.7237571428575" y1="504.0331285714287" y2="504.0331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9173571428574" x2="162.7237571428575" y1="528.7125285714287" y2="528.7125285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9173571428574" x2="103.9173571428574" y1="504.0331285714287" y2="528.7125285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.7237571428575" x2="162.7237571428575" y1="504.0331285714287" y2="528.7125285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.7237571428575" x2="221.5301571428574" y1="504.0331285714287" y2="504.0331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.7237571428575" x2="221.5301571428574" y1="528.7125285714287" y2="528.7125285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.7237571428575" x2="162.7237571428575" y1="504.0331285714287" y2="528.7125285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.5301571428574" x2="221.5301571428574" y1="504.0331285714287" y2="528.7125285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.5300571428575" x2="280.3364571428574" y1="504.0331285714287" y2="504.0331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.5300571428575" x2="280.3364571428574" y1="528.7125285714287" y2="528.7125285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.5300571428575" x2="221.5300571428575" y1="504.0331285714287" y2="528.7125285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.3364571428574" x2="280.3364571428574" y1="504.0331285714287" y2="528.7125285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.3364571428574" x2="339.1428571428575" y1="504.0331285714287" y2="504.0331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.3364571428574" x2="339.1428571428575" y1="528.7125285714287" y2="528.7125285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.3364571428574" x2="280.3364571428574" y1="504.0331285714287" y2="528.7125285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="339.1428571428575" x2="339.1428571428575" y1="504.0331285714287" y2="528.7125285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.14285714285745" x2="103.9173571428574" y1="528.7125285714286" y2="528.7125285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.14285714285745" x2="103.9173571428574" y1="553.3919285714287" y2="553.3919285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.14285714285745" x2="58.14285714285745" y1="528.7125285714286" y2="553.3919285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9173571428574" x2="103.9173571428574" y1="528.7125285714286" y2="553.3919285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9173571428574" x2="162.7237571428575" y1="528.7125285714286" y2="528.7125285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9173571428574" x2="162.7237571428575" y1="553.3919285714287" y2="553.3919285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9173571428574" x2="103.9173571428574" y1="528.7125285714286" y2="553.3919285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.7237571428575" x2="162.7237571428575" y1="528.7125285714286" y2="553.3919285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.7237571428575" x2="221.5301571428574" y1="528.7125285714286" y2="528.7125285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.7237571428575" x2="221.5301571428574" y1="553.3919285714287" y2="553.3919285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.7237571428575" x2="162.7237571428575" y1="528.7125285714286" y2="553.3919285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.5301571428574" x2="221.5301571428574" y1="528.7125285714286" y2="553.3919285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.5300571428575" x2="280.3364571428574" y1="528.7125285714286" y2="528.7125285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.5300571428575" x2="280.3364571428574" y1="553.3919285714287" y2="553.3919285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.5300571428575" x2="221.5300571428575" y1="528.7125285714286" y2="553.3919285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.3364571428574" x2="280.3364571428574" y1="528.7125285714286" y2="553.3919285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.3364571428574" x2="339.1428571428575" y1="528.7125285714286" y2="528.7125285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.3364571428574" x2="339.1428571428575" y1="553.3919285714287" y2="553.3919285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.3364571428574" x2="280.3364571428574" y1="528.7125285714286" y2="553.3919285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="339.1428571428575" x2="339.1428571428575" y1="528.7125285714286" y2="553.3919285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.14285714285745" x2="103.9173571428574" y1="553.3920285714287" y2="553.3920285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.14285714285745" x2="103.9173571428574" y1="578.0714285714287" y2="578.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.14285714285745" x2="58.14285714285745" y1="553.3920285714287" y2="578.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9173571428574" x2="103.9173571428574" y1="553.3920285714287" y2="578.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9173571428574" x2="162.7237571428575" y1="553.3920285714287" y2="553.3920285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9173571428574" x2="162.7237571428575" y1="578.0714285714287" y2="578.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9173571428574" x2="103.9173571428574" y1="553.3920285714287" y2="578.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.7237571428575" x2="162.7237571428575" y1="553.3920285714287" y2="578.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.7237571428575" x2="221.5301571428574" y1="553.3920285714287" y2="553.3920285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.7237571428575" x2="221.5301571428574" y1="578.0714285714287" y2="578.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.7237571428575" x2="162.7237571428575" y1="553.3920285714287" y2="578.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.5301571428574" x2="221.5301571428574" y1="553.3920285714287" y2="578.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.5300571428575" x2="280.3364571428574" y1="553.3920285714287" y2="553.3920285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.5300571428575" x2="280.3364571428574" y1="578.0714285714287" y2="578.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.5300571428575" x2="221.5300571428575" y1="553.3920285714287" y2="578.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.3364571428574" x2="280.3364571428574" y1="553.3920285714287" y2="578.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.3364571428574" x2="339.1428571428575" y1="553.3920285714287" y2="553.3920285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.3364571428574" x2="339.1428571428575" y1="578.0714285714287" y2="578.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.3364571428574" x2="280.3364571428574" y1="553.3920285714287" y2="578.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="339.1428571428575" x2="339.1428571428575" y1="553.3920285714287" y2="578.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.14285714285745" x2="103.9173571428574" y1="578.0714285714287" y2="578.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.14285714285745" x2="103.9173571428574" y1="602.7508285714287" y2="602.7508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.14285714285745" x2="58.14285714285745" y1="578.0714285714287" y2="602.7508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9173571428574" x2="103.9173571428574" y1="578.0714285714287" y2="602.7508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9173571428574" x2="162.7237571428575" y1="578.0714285714287" y2="578.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9173571428574" x2="162.7237571428575" y1="602.7508285714287" y2="602.7508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9173571428574" x2="103.9173571428574" y1="578.0714285714287" y2="602.7508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.7237571428575" x2="162.7237571428575" y1="578.0714285714287" y2="602.7508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.7237571428575" x2="221.5301571428574" y1="578.0714285714287" y2="578.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.7237571428575" x2="221.5301571428574" y1="602.7508285714287" y2="602.7508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.7237571428575" x2="162.7237571428575" y1="578.0714285714287" y2="602.7508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.5301571428574" x2="221.5301571428574" y1="578.0714285714287" y2="602.7508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.5300571428575" x2="280.3364571428574" y1="578.0714285714287" y2="578.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.5300571428575" x2="280.3364571428574" y1="602.7508285714287" y2="602.7508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.5300571428575" x2="221.5300571428575" y1="578.0714285714287" y2="602.7508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.3364571428574" x2="280.3364571428574" y1="578.0714285714287" y2="602.7508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.3364571428574" x2="339.1428571428575" y1="578.0714285714287" y2="578.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.3364571428574" x2="339.1428571428575" y1="602.7508285714287" y2="602.7508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.3364571428574" x2="280.3364571428574" y1="578.0714285714287" y2="602.7508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="339.1428571428575" x2="339.1428571428575" y1="578.0714285714287" y2="602.7508285714287"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="543" stroke="rgb(255,255,255)" text-anchor="middle" x="135.15625" xml:space="preserve" y="456.078125" zvalue="921">35kV     母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="543" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="135.15625" xml:space="preserve" y="472.078125" zvalue="921">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="542" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,204.541,315.913) scale(1,1) translate(0,0)" writing-mode="lr" x="204.54" xml:space="preserve" y="320.41" zvalue="922">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="541" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,309.541,315.913) scale(1,1) translate(0,0)" writing-mode="lr" x="309.54" xml:space="preserve" y="320.41" zvalue="923">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="540" stroke="rgb(255,255,255)" text-anchor="middle" x="252.484375" xml:space="preserve" y="456.078125" zvalue="924">10kV     母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="540" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="252.484375" xml:space="preserve" y="472.078125" zvalue="924">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="539" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.1429,491.571) scale(1,1) translate(0,0)" writing-mode="lr" x="83.14285714285745" xml:space="preserve" y="496.0714285714287" zvalue="925">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="538" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.1429,517.071) scale(1,1) translate(0,0)" writing-mode="lr" x="83.14285714285745" xml:space="preserve" y="521.5714285714287" zvalue="926">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="537" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.1429,542.571) scale(1,1) translate(0,5.86277e-14)" writing-mode="lr" x="83.14285714285745" xml:space="preserve" y="547.0714285714286" zvalue="927">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="536" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.1429,568.071) scale(1,1) translate(0,0)" writing-mode="lr" x="83.14285714285745" xml:space="preserve" y="572.5714285714286" zvalue="928">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="535" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.1429,593.571) scale(1,1) translate(0,0)" writing-mode="lr" x="83.14285714285745" xml:space="preserve" y="598.0714285714286" zvalue="929">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="534" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,45.1429,175.071) scale(1,1) translate(0,0)" writing-mode="lr" x="45.14" xml:space="preserve" y="180.57" zvalue="930">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="533" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,231.393,175.071) scale(1,1) translate(0,0)" writing-mode="lr" x="231.39" xml:space="preserve" y="180.57" zvalue="931">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="532" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.8304,199.321) scale(1,1) translate(0,0)" writing-mode="lr" x="48.83" xml:space="preserve" y="203.82" zvalue="932">35kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="531" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.3304,247.071) scale(1,1) translate(0,0)" writing-mode="lr" x="52.33" xml:space="preserve" y="251.57" zvalue="933">1号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="530" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.3304,270.071) scale(1,1) translate(0,3.45819e-13)" writing-mode="lr" x="52.33" xml:space="preserve" y="274.57" zvalue="934">1号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="529" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49.8304,223.321) scale(1,1) translate(0,0)" writing-mode="lr" x="49.83" xml:space="preserve" y="227.82" zvalue="935">10kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="368" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1011.57,478.964) scale(1,1) translate(0,0)" writing-mode="lr" x="1011.57" xml:space="preserve" y="483.46" zvalue="938">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="367" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,952.571,591.214) scale(1,1) translate(0,0)" writing-mode="lr" x="952.5700000000001" xml:space="preserve" y="595.71" zvalue="940">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="525" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1014.07,507.214) scale(1,1) translate(0,0)" writing-mode="lr" x="1014.07" xml:space="preserve" y="511.71" zvalue="941">10000kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="366" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,951.571,371.214) scale(1,1) translate(0,0)" writing-mode="lr" x="951.5700000000001" xml:space="preserve" y="375.71" zvalue="943">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="365" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,897.571,432.214) scale(1,1) translate(0,0)" writing-mode="lr" x="897.5700000000001" xml:space="preserve" y="436.71" zvalue="949">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="364" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1146.82,243.214) scale(1,1) translate(0,0)" writing-mode="lr" x="1146.82" xml:space="preserve" y="247.71" zvalue="952">341</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="363" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1119.57,118.714) scale(1,1) translate(0,0)" writing-mode="lr" x="1119.57" xml:space="preserve" y="123.21" zvalue="954">35kV景安线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="362" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,586.821,609.214) scale(1,1) translate(0,0)" writing-mode="lr" x="586.8200000000001" xml:space="preserve" y="613.71" zvalue="959">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="361" stroke="rgb(255,255,255)" text-anchor="middle" x="552.4140625" xml:space="preserve" y="515.71875" zvalue="961">10kVⅠ段母线电压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="361" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="552.4140625" xml:space="preserve" y="531.71875" zvalue="961">互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="360" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,494.071,727.409) scale(1,1) translate(0,0)" writing-mode="lr" x="494.07" xml:space="preserve" y="731.91" zvalue="964">041</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="359" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,511.321,793.409) scale(1,1) translate(0,0)" writing-mode="lr" x="511.32" xml:space="preserve" y="797.91" zvalue="966">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="358" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1845.98,640.019) scale(1,1) translate(0,0)" writing-mode="lr" x="1845.98" xml:space="preserve" y="644.52" zvalue="970">10kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="357" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1584.57,476.873) scale(1,1) translate(0,0)" writing-mode="lr" x="1584.57" xml:space="preserve" y="481.37" zvalue="972">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="356" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1527.07,590.123) scale(1,1) translate(0,0)" writing-mode="lr" x="1527.07" xml:space="preserve" y="594.62" zvalue="974">002</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="502" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1587.07,505.123) scale(1,1) translate(0,0)" writing-mode="lr" x="1587.07" xml:space="preserve" y="509.62" zvalue="975">10000kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="355" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1524.57,369.123) scale(1,1) translate(0,0)" writing-mode="lr" x="1524.57" xml:space="preserve" y="373.62" zvalue="978">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="354" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1471.82,430.123) scale(1,1) translate(0,0)" writing-mode="lr" x="1471.82" xml:space="preserve" y="434.62" zvalue="983">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="353" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1256.07,361.214) scale(1,1) translate(0,0)" writing-mode="lr" x="1256.07" xml:space="preserve" y="365.71" zvalue="986">3901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="352" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1224.57,457.714) scale(1,1) translate(0,0)" writing-mode="lr" x="1224.57" xml:space="preserve" y="462.21" zvalue="988">35kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="351" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1809.95,610.214) scale(1,1) translate(0,0)" writing-mode="lr" x="1809.95" xml:space="preserve" y="614.71" zvalue="993">0902</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="350" stroke="rgb(255,255,255)" text-anchor="middle" x="1774.3984375" xml:space="preserve" y="516.71875" zvalue="995">10kVⅡ段母线电压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="350" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1774.3984375" xml:space="preserve" y="532.703125" zvalue="995">互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="349" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,468.571,899.714) scale(1,1) translate(0,0)" writing-mode="lr" x="468.57" xml:space="preserve" y="904.21" zvalue="999">#1F</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="484" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,469.071,922.214) scale(1,1) translate(0,0)" writing-mode="lr" x="469.07" xml:space="preserve" y="926.71" zvalue="1000">6000kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="348" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,562.071,730.409) scale(1,1) translate(0,0)" writing-mode="lr" x="562.0700000000001" xml:space="preserve" y="734.91" zvalue="1002">042</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="347" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,579.321,796.409) scale(1,1) translate(0,0)" writing-mode="lr" x="579.3200000000001" xml:space="preserve" y="800.91" zvalue="1004">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="346" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,534.571,896.214) scale(1,1) translate(0,0)" writing-mode="lr" x="534.5700000000001" xml:space="preserve" y="900.71" zvalue="1009">环保车间</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="345" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,626.071,730.409) scale(1,1) translate(0,0)" writing-mode="lr" x="626.0700000000001" xml:space="preserve" y="734.91" zvalue="1011">043</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="344" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,643.321,796.409) scale(1,1) translate(0,0)" writing-mode="lr" x="643.3200000000001" xml:space="preserve" y="800.91" zvalue="1013">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="343" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,603.071,904.464) scale(1,1) translate(0,0)" writing-mode="lr" x="603.0714285714289" xml:space="preserve" y="908.9642857142858" zvalue="1018">2号循环水站</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="342" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,694.071,731.604) scale(1,1) translate(0,0)" writing-mode="lr" x="694.0700000000001" xml:space="preserve" y="736.1" zvalue="1020">044</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="341" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,711.321,797.604) scale(1,1) translate(0,0)" writing-mode="lr" x="711.3200000000001" xml:space="preserve" y="802.1" zvalue="1022">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="340" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,671.071,905.659) scale(1,1) translate(0,0)" writing-mode="lr" x="671.0714285714289" xml:space="preserve" y="910.1590946742466" zvalue="1027">1号循环水站</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="339" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,763.071,732.799) scale(1,1) translate(0,0)" writing-mode="lr" x="763.0700000000001" xml:space="preserve" y="737.3" zvalue="1029">045</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="338" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,780.321,798.799) scale(1,1) translate(0,0)" writing-mode="lr" x="780.3200000000001" xml:space="preserve" y="803.3" zvalue="1031">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="337" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,740.071,906.854) scale(1,1) translate(0,0)" writing-mode="lr" x="740.0714285714289" xml:space="preserve" y="911.3539036342074" zvalue="1036">6号酵母车间</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="336" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,831.071,733.994) scale(1,1) translate(0,0)" writing-mode="lr" x="831.0700000000001" xml:space="preserve" y="738.49" zvalue="1038">046</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="335" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,848.321,799.994) scale(1,1) translate(0,0)" writing-mode="lr" x="848.3200000000001" xml:space="preserve" y="804.49" zvalue="1040">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="334" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,808.071,908.049) scale(1,1) translate(0,0)" writing-mode="lr" x="808.0714285714289" xml:space="preserve" y="912.5487125941685" zvalue="1045">5号酵母车间</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="333" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,900.071,735.188) scale(1,1) translate(0,0)" writing-mode="lr" x="900.0700000000001" xml:space="preserve" y="739.6900000000001" zvalue="1047">047</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="332" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,917.321,801.188) scale(1,1) translate(0,0)" writing-mode="lr" x="917.3200000000001" xml:space="preserve" y="805.6900000000001" zvalue="1049">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="292" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,877.071,909.244) scale(1,1) translate(0,0)" writing-mode="lr" x="877.0714285714289" xml:space="preserve" y="913.7435215541295" zvalue="1054">4号酵母车间</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="291" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,974.071,735.188) scale(1,1) translate(1.06756e-13,0)" writing-mode="lr" x="974.0700000000001" xml:space="preserve" y="739.6900000000001" zvalue="1056">048</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="286" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,991.321,801.188) scale(1,1) translate(0,0)" writing-mode="lr" x="991.3200000000001" xml:space="preserve" y="805.6900000000001" zvalue="1058">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="283" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,951.071,909.244) scale(1,1) translate(0,0)" writing-mode="lr" x="951.0714285714289" xml:space="preserve" y="913.7435215541295" zvalue="1063">3号酵母车间</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="282" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1046.07,735.188) scale(1,1) translate(0,0)" writing-mode="lr" x="1046.07" xml:space="preserve" y="739.6900000000001" zvalue="1065">049</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="281" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1063.32,801.188) scale(1,1) translate(0,0)" writing-mode="lr" x="1063.32" xml:space="preserve" y="805.6900000000001" zvalue="1067">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="255" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1023.07,909.244) scale(1,1) translate(0,0)" writing-mode="lr" x="1023.071428571429" xml:space="preserve" y="913.7435215541295" zvalue="1072">2号酵母车间</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="253" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1122.07,734.188) scale(1,1) translate(0,0)" writing-mode="lr" x="1122.07" xml:space="preserve" y="738.6900000000001" zvalue="1074">051</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="252" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1139.32,800.188) scale(1,1) translate(0,0)" writing-mode="lr" x="1139.32" xml:space="preserve" y="804.6900000000001" zvalue="1076">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="251" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1099.07,908.244) scale(1,1) translate(0,0)" writing-mode="lr" x="1099.071428571429" xml:space="preserve" y="912.7435215541295" zvalue="1081">1号酵母车间</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="249" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1200.82,735.383) scale(1,1) translate(0,0)" writing-mode="lr" x="1200.82" xml:space="preserve" y="739.88" zvalue="1083">053</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="246" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1177.57,858.214) scale(1,1) translate(0,0)" writing-mode="lr" x="1177.57" xml:space="preserve" y="862.71" zvalue="1087">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="244" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1273.82,733.383) scale(1,1) translate(0,0)" writing-mode="lr" x="1273.82" xml:space="preserve" y="737.88" zvalue="1089">054</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="241" stroke="rgb(255,255,255)" text-anchor="middle" x="1245.640625" xml:space="preserve" y="904.453125" zvalue="1092">10kV备用</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="241" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1245.640625" xml:space="preserve" y="920.453125" zvalue="1092">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="240" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1270.57,790.214) scale(1,1) translate(0,0)" writing-mode="lr" x="1270.57" xml:space="preserve" y="794.71" zvalue="1095">0551</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="236" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1284.07,603.214) scale(1,1) translate(0,0)" writing-mode="lr" x="1284.07" xml:space="preserve" y="607.71" zvalue="1098">012</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="215" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1383.07,602.214) scale(1,1) translate(0,0)" writing-mode="lr" x="1383.07" xml:space="preserve" y="606.71" zvalue="1100">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="214" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1412.82,735.383) scale(1,1) translate(0,0)" writing-mode="lr" x="1412.82" xml:space="preserve" y="739.88" zvalue="1107">055</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="213" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1389.57,858.214) scale(1,1) translate(0,0)" writing-mode="lr" x="1389.57" xml:space="preserve" y="862.71" zvalue="1111">#2站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="212" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1482.07,734.188) scale(1,1) translate(0,0)" writing-mode="lr" x="1482.07" xml:space="preserve" y="738.6900000000001" zvalue="1113">056</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="211" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1499.32,800.188) scale(1,1) translate(0,0)" writing-mode="lr" x="1499.32" xml:space="preserve" y="804.6900000000001" zvalue="1115">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="210" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1459.07,908.244) scale(1,1) translate(0,0)" writing-mode="lr" x="1459.071428571429" xml:space="preserve" y="912.7435215541295" zvalue="1120">馈线6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="209" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1554.07,734.188) scale(1,1) translate(0,0)" writing-mode="lr" x="1554.07" xml:space="preserve" y="738.6900000000001" zvalue="1122">057</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="208" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1571.32,800.188) scale(1,1) translate(0,0)" writing-mode="lr" x="1571.32" xml:space="preserve" y="804.6900000000001" zvalue="1124">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="207" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1527.57,908.244) scale(1,1) translate(0,0)" writing-mode="lr" x="1527.571428571429" xml:space="preserve" y="912.7435215541295" zvalue="1129">颗粒肥风机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="204" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1630.07,734.188) scale(1,1) translate(0,0)" writing-mode="lr" x="1630.07" xml:space="preserve" y="738.6900000000001" zvalue="1131">058</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="203" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1647.32,800.188) scale(1,1) translate(0,0)" writing-mode="lr" x="1647.32" xml:space="preserve" y="804.6900000000001" zvalue="1133">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="202" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1611.07,908.244) scale(1,1) translate(0,0)" writing-mode="lr" x="1611.07" xml:space="preserve" y="912.74" zvalue="1138">MVR-TM18</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="200" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1706.07,734.188) scale(1,1) translate(0,0)" writing-mode="lr" x="1706.07" xml:space="preserve" y="738.6900000000001" zvalue="1140">059</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="198" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1723.32,800.188) scale(1,1) translate(0,0)" writing-mode="lr" x="1723.32" xml:space="preserve" y="804.6900000000001" zvalue="1142">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="197" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1687.07,908.244) scale(1,1) translate(0,0)" writing-mode="lr" x="1687.07" xml:space="preserve" y="912.74" zvalue="1147">MVR-TM17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="183" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1786.07,734.188) scale(1,1) translate(0,0)" writing-mode="lr" x="1786.07" xml:space="preserve" y="738.6900000000001" zvalue="1149">061</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="182" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1803.32,800.188) scale(1,1) translate(0,0)" writing-mode="lr" x="1803.32" xml:space="preserve" y="804.6900000000001" zvalue="1151">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="181" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1763.07,908.244) scale(1,1) translate(0,0)" writing-mode="lr" x="1763.071428571429" xml:space="preserve" y="912.7435215541295" zvalue="1156">新干燥#1变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="180" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1862.07,734.188) scale(1,1) translate(0,0)" writing-mode="lr" x="1862.07" xml:space="preserve" y="738.6900000000001" zvalue="1158">062</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="179" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1879.32,800.188) scale(1,1) translate(0,0)" writing-mode="lr" x="1879.32" xml:space="preserve" y="804.6900000000001" zvalue="1160">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="178" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1839.07,908.244) scale(1,1) translate(0,0)" writing-mode="lr" x="1839.071428571429" xml:space="preserve" y="912.7435215541295" zvalue="1165">新干燥#2变</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="64.76000000000001" y="301.96" zvalue="936"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="51">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="51" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,127.571,210.468) scale(1,1) translate(0,0)" writing-mode="lr" x="127.72" xml:space="preserve" y="216.9" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126612500484" ObjectName="F"/>
   </metadata>
  </g>
  <g id="165">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="165" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,127.571,235.357) scale(1,1) translate(0,0)" writing-mode="lr" x="127.72" xml:space="preserve" y="241.79" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="8" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,127.571,186.5) scale(1,1) translate(0,0)" writing-mode="lr" x="127.72" xml:space="preserve" y="192.93" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126648283140" ObjectName=""/>
   </metadata>
  </g>
  <g id="13">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="13" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,304.571,186.357) scale(1,1) translate(0,0)" writing-mode="lr" x="304.72" xml:space="preserve" y="192.79" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126648348676" ObjectName=""/>
   </metadata>
  </g>
  <g id="278">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="278" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,142.571,258.552) scale(1,1) translate(-2.00069e-14,0)" writing-mode="lr" x="142.8" xml:space="preserve" y="265.04" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126620233732" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="275">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="275" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,142.571,281.552) scale(1,1) translate(0,0)" writing-mode="lr" x="142.77" xml:space="preserve" y="288.04" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126620299268" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="144">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="144" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,125.071,528.093) scale(1,1) translate(0,0)" writing-mode="lr" x="125.2" xml:space="preserve" y="533" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126612107268" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="143">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="143" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,125.071,553.218) scale(1,1) translate(0,-1.20508e-13)" writing-mode="lr" x="125.2" xml:space="preserve" y="558.13" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126612172807" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="142">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="142" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,125.071,578.343) scale(1,1) translate(0,-2.52173e-13)" writing-mode="lr" x="125.2" xml:space="preserve" y="583.25" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126612238343" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="141">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="141" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,126.5,502.968) scale(1,1) translate(0,0)" writing-mode="lr" x="126.63" xml:space="preserve" y="507.88" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126612369412" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="139">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="139" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,242.905,526.982) scale(1,1) translate(0,0)" writing-mode="lr" x="243.03" xml:space="preserve" y="531.89" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="138">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="138" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,242.905,552.107) scale(1,1) translate(0,0)" writing-mode="lr" x="243.03" xml:space="preserve" y="557.02" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="56">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,242.905,577.232) scale(1,1) translate(0,0)" writing-mode="lr" x="243.03" xml:space="preserve" y="582.14" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="55">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="55" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,242.905,501.857) scale(1,1) translate(0,0)" writing-mode="lr" x="243.03" xml:space="preserve" y="506.77" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="148">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="148" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,124.321,603.468) scale(1,1) translate(0,0)" writing-mode="lr" x="124.45" xml:space="preserve" y="608.38" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126612566020" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="140">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="140" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,242.613,602.357) scale(1,1) translate(0,0)" writing-mode="lr" x="242.74" xml:space="preserve" y="607.27" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="U0"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="160">
   <use height="30" transform="rotate(0,334.196,327.857) scale(0.708333,0.665547) translate(133.235,159.739)" width="30" x="323.57" xlink:href="#State:红绿圆(方形)_0" y="317.87" zvalue="538"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.196,327.857) scale(0.708333,0.665547) translate(133.235,159.739)" width="30" x="323.57" y="317.87"/></g>
  <g id="54">
   <use height="30" transform="rotate(0,238.571,327.857) scale(0.708333,0.665547) translate(93.8603,159.739)" width="30" x="227.95" xlink:href="#State:红绿圆(方形)_0" y="317.87" zvalue="539"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,238.571,327.857) scale(0.708333,0.665547) translate(93.8603,159.739)" width="30" x="227.95" y="317.87"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="560">
   <path class="kv35" d="M 626.24 305.31 L 1630.57 305.31" stroke-width="6" zvalue="902"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674253799428" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674253799428"/></metadata>
  <path d="M 626.24 305.31 L 1630.57 305.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="559">
   <path class="kv10" d="M 426.57 655.02 L 1288.57 655.02" stroke-width="6" zvalue="904"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674253864964" ObjectName="10kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674253864964"/></metadata>
  <path d="M 426.57 655.02 L 1288.57 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="505">
   <path class="kv10" d="M 1341.57 655.02 L 1883.48 655.02" stroke-width="6" zvalue="969"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674253930500" ObjectName="10kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674253930500"/></metadata>
  <path d="M 1341.57 655.02 L 1883.48 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="PowerTransformer2Class">
  <g id="527">
   <g id="5270">
    <use class="kv35" height="60" transform="rotate(0,928.571,490.214) scale(1.55,1.3) translate(-318.493,-104.126)" width="40" x="897.5700000000001" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="451.21" zvalue="937"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874454401027" ObjectName="35"/>
    </metadata>
   </g>
   <g id="5271">
    <use class="kv10" height="60" transform="rotate(0,928.571,490.214) scale(1.55,1.3) translate(-318.493,-104.126)" width="40" x="897.5700000000001" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="451.21" zvalue="937"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874454466563" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399459078147" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399459078147"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,928.571,490.214) scale(1.55,1.3) translate(-318.493,-104.126)" width="40" x="897.5700000000001" y="451.21"/></g>
  <g id="504">
   <g id="5040">
    <use class="kv35" height="60" transform="rotate(0,1501.57,488.123) scale(1.55,1.3) translate(-521.816,-103.644)" width="40" x="1470.57" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="449.12" zvalue="971"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874454532099" ObjectName="35"/>
    </metadata>
   </g>
   <g id="5041">
    <use class="kv10" height="60" transform="rotate(0,1501.57,488.123) scale(1.55,1.3) translate(-521.816,-103.644)" width="40" x="1470.57" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="449.12" zvalue="971"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874454597635" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399459143683" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399459143683"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1501.57,488.123) scale(1.55,1.3) translate(-521.816,-103.644)" width="40" x="1470.57" y="449.12"/></g>
 </g>
 <g id="BreakerClass">
  <g id="526">
   <use class="kv10" height="20" transform="rotate(0,928.571,592.214) scale(2.2,2.2) translate(-500.494,-311.026)" width="10" x="917.5714285714289" xlink:href="#Breaker:小车断路器_0" y="570.2142857142857" zvalue="939"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924555702277" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473924555702277"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,928.571,592.214) scale(2.2,2.2) translate(-500.494,-311.026)" width="10" x="917.5714285714289" y="570.2142857142857"/></g>
  <g id="524">
   <use class="kv35" height="20" transform="rotate(0,928.571,372.214) scale(2.2,2.2) translate(-500.494,-191.026)" width="10" x="917.5714285714289" xlink:href="#Breaker:小车断路器_0" y="350.2142857142857" zvalue="942"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924555767812" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473924555767812"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,928.571,372.214) scale(2.2,2.2) translate(-500.494,-191.026)" width="10" x="917.5714285714289" y="350.2142857142857"/></g>
  <g id="517">
   <use class="kv35" height="20" transform="rotate(0,1120.57,244.214) scale(2.2,2.2) translate(-605.221,-121.208)" width="10" x="1109.571428571429" xlink:href="#Breaker:小车断路器_0" y="222.2142857142858" zvalue="951"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924555833348" ObjectName="35kV景安线341断路器"/>
   <cge:TPSR_Ref TObjectID="6473924555833348"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1120.57,244.214) scale(2.2,2.2) translate(-605.221,-121.208)" width="10" x="1109.571428571429" y="222.2142857142858"/></g>
  <g id="509">
   <use class="kv10" height="20" transform="rotate(0,469.571,728.409) scale(2.2,2.2) translate(-250.13,-385.314)" width="10" x="458.5714285714289" xlink:href="#Breaker:小车断路器_0" y="706.4090909090909" zvalue="963"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924555898884" ObjectName="#1F041断路器"/>
   <cge:TPSR_Ref TObjectID="6473924555898884"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,469.571,728.409) scale(2.2,2.2) translate(-250.13,-385.314)" width="10" x="458.5714285714289" y="706.4090909090909"/></g>
  <g id="503">
   <use class="kv10" height="20" transform="rotate(0,1501.57,590.123) scale(2.2,2.2) translate(-813.039,-309.885)" width="10" x="1490.571428571429" xlink:href="#Breaker:小车断路器_0" y="568.1233766233767" zvalue="973"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924556029956" ObjectName="#2主变10kV侧002断路器"/>
   <cge:TPSR_Ref TObjectID="6473924556029956"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1501.57,590.123) scale(2.2,2.2) translate(-813.039,-309.885)" width="10" x="1490.571428571429" y="568.1233766233767"/></g>
  <g id="501">
   <use class="kv35" height="20" transform="rotate(0,1501.57,370.123) scale(2.2,2.2) translate(-813.039,-189.885)" width="10" x="1490.571428571429" xlink:href="#Breaker:小车断路器_0" y="348.1233766233767" zvalue="976"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924555964420" ObjectName="#2主变35kV侧302断路器"/>
   <cge:TPSR_Ref TObjectID="6473924555964420"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1501.57,370.123) scale(2.2,2.2) translate(-813.039,-189.885)" width="10" x="1490.571428571429" y="348.1233766233767"/></g>
  <g id="483">
   <use class="kv10" height="20" transform="rotate(0,537.571,731.409) scale(2.2,2.2) translate(-287.221,-386.95)" width="10" x="526.5714285714289" xlink:href="#Breaker:小车断路器_0" y="709.4090909090909" zvalue="1001"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924556095492" ObjectName="环保车间042断路器"/>
   <cge:TPSR_Ref TObjectID="6473924556095492"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,537.571,731.409) scale(2.2,2.2) translate(-287.221,-386.95)" width="10" x="526.5714285714289" y="709.4090909090909"/></g>
  <g id="477">
   <use class="kv10" height="20" transform="rotate(0,601.571,731.409) scale(2.2,2.2) translate(-322.13,-386.95)" width="10" x="590.5714285714289" xlink:href="#Breaker:小车断路器_0" y="709.4090909090909" zvalue="1010"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924556161028" ObjectName="2号循环水站043断路器"/>
   <cge:TPSR_Ref TObjectID="6473924556161028"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,601.571,731.409) scale(2.2,2.2) translate(-322.13,-386.95)" width="10" x="590.5714285714289" y="709.4090909090909"/></g>
  <g id="471">
   <use class="kv10" height="20" transform="rotate(0,669.571,732.604) scale(2.2,2.2) translate(-359.221,-387.602)" width="10" x="658.5714285714289" xlink:href="#Breaker:小车断路器_0" y="710.6038998690519" zvalue="1019"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924556226564" ObjectName="1号循环水站044断路器"/>
   <cge:TPSR_Ref TObjectID="6473924556226564"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,669.571,732.604) scale(2.2,2.2) translate(-359.221,-387.602)" width="10" x="658.5714285714289" y="710.6038998690519"/></g>
  <g id="465">
   <use class="kv10" height="20" transform="rotate(0,738.571,733.799) scale(2.2,2.2) translate(-396.857,-388.254)" width="10" x="727.5714285714289" xlink:href="#Breaker:小车断路器_0" y="711.7987088290129" zvalue="1028"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924556292100" ObjectName="6号酵母车间045断路器"/>
   <cge:TPSR_Ref TObjectID="6473924556292100"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,738.571,733.799) scale(2.2,2.2) translate(-396.857,-388.254)" width="10" x="727.5714285714289" y="711.7987088290129"/></g>
  <g id="459">
   <use class="kv10" height="20" transform="rotate(0,806.571,734.994) scale(2.2,2.2) translate(-433.948,-388.906)" width="10" x="795.5714285714289" xlink:href="#Breaker:小车断路器_0" y="712.9935177889737" zvalue="1037"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924556357636" ObjectName="5号酵母车间046断路器"/>
   <cge:TPSR_Ref TObjectID="6473924556357636"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,806.571,734.994) scale(2.2,2.2) translate(-433.948,-388.906)" width="10" x="795.5714285714289" y="712.9935177889737"/></g>
  <g id="453">
   <use class="kv10" height="20" transform="rotate(0,875.571,736.188) scale(2.2,2.2) translate(-471.584,-389.557)" width="10" x="864.5714285714289" xlink:href="#Breaker:小车断路器_0" y="714.1883267489347" zvalue="1046"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924556423172" ObjectName="4号酵母车间047断路器"/>
   <cge:TPSR_Ref TObjectID="6473924556423172"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,875.571,736.188) scale(2.2,2.2) translate(-471.584,-389.557)" width="10" x="864.5714285714289" y="714.1883267489347"/></g>
  <g id="447">
   <use class="kv10" height="20" transform="rotate(0,949.571,736.188) scale(2.2,2.2) translate(-511.948,-389.557)" width="10" x="938.5714285714289" xlink:href="#Breaker:小车断路器_0" y="714.1883267489347" zvalue="1055"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924556488708" ObjectName="3号酵母车间048断路器"/>
   <cge:TPSR_Ref TObjectID="6473924556488708"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,949.571,736.188) scale(2.2,2.2) translate(-511.948,-389.557)" width="10" x="938.5714285714289" y="714.1883267489347"/></g>
  <g id="441">
   <use class="kv10" height="20" transform="rotate(0,1021.57,736.188) scale(2.2,2.2) translate(-551.221,-389.557)" width="10" x="1010.571428571429" xlink:href="#Breaker:小车断路器_0" y="714.1883267489347" zvalue="1064"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924556554244" ObjectName="2号酵母车间049断路器"/>
   <cge:TPSR_Ref TObjectID="6473924556554244"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1021.57,736.188) scale(2.2,2.2) translate(-551.221,-389.557)" width="10" x="1010.571428571429" y="714.1883267489347"/></g>
  <g id="435">
   <use class="kv10" height="20" transform="rotate(0,1097.57,735.188) scale(2.2,2.2) translate(-592.675,-389.012)" width="10" x="1086.571428571429" xlink:href="#Breaker:小车断路器_0" y="713.1883267489347" zvalue="1073"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924556619780" ObjectName="1号酵母车间051断路器"/>
   <cge:TPSR_Ref TObjectID="6473924556619780"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1097.57,735.188) scale(2.2,2.2) translate(-592.675,-389.012)" width="10" x="1086.571428571429" y="713.1883267489347"/></g>
  <g id="429">
   <use class="kv10" height="20" transform="rotate(0,1174.57,736.383) scale(2.2,2.2) translate(-634.675,-389.664)" width="10" x="1163.571428571429" xlink:href="#Breaker:小车断路器_0" y="714.3831357088956" zvalue="1082"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924556685316" ObjectName="#1站用变053断路器"/>
   <cge:TPSR_Ref TObjectID="6473924556685316"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1174.57,736.383) scale(2.2,2.2) translate(-634.675,-389.664)" width="10" x="1163.571428571429" y="714.3831357088956"/></g>
  <g id="425">
   <use class="kv10" height="20" transform="rotate(0,1247.57,734.383) scale(2.2,2.2) translate(-674.494,-388.573)" width="10" x="1236.571428571429" xlink:href="#Breaker:小车断路器_0" y="712.3831357088956" zvalue="1088"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924556750852" ObjectName="10kV备用线054断路器"/>
   <cge:TPSR_Ref TObjectID="6473924556750852"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1247.57,734.383) scale(2.2,2.2) translate(-674.494,-388.573)" width="10" x="1236.571428571429" y="712.3831357088956"/></g>
  <g id="419">
   <use class="kv10" height="20" transform="rotate(0,1259.57,604.214) scale(2.2,2.2) translate(-681.039,-317.571)" width="10" x="1248.571428571429" xlink:href="#Breaker:小车断路器_0" y="582.2142857142857" zvalue="1097"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924556816388" ObjectName="10kV分段012断路器"/>
   <cge:TPSR_Ref TObjectID="6473924556816388"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1259.57,604.214) scale(2.2,2.2) translate(-681.039,-317.571)" width="10" x="1248.571428571429" y="582.2142857142857"/></g>
  <g id="412">
   <use class="kv10" height="20" transform="rotate(0,1386.57,736.383) scale(2.2,2.2) translate(-750.312,-389.664)" width="10" x="1375.571428571429" xlink:href="#Breaker:小车断路器_0" y="714.3831357088956" zvalue="1106"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924556881924" ObjectName="#2站用变055断路器"/>
   <cge:TPSR_Ref TObjectID="6473924556881924"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1386.57,736.383) scale(2.2,2.2) translate(-750.312,-389.664)" width="10" x="1375.571428571429" y="714.3831357088956"/></g>
  <g id="408">
   <use class="kv10" height="20" transform="rotate(0,1457.57,735.188) scale(2.2,2.2) translate(-789.039,-389.012)" width="10" x="1446.571428571429" xlink:href="#Breaker:小车断路器_0" y="713.1883267489347" zvalue="1112"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924556947460" ObjectName="馈线6056断路器"/>
   <cge:TPSR_Ref TObjectID="6473924556947460"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1457.57,735.188) scale(2.2,2.2) translate(-789.039,-389.012)" width="10" x="1446.571428571429" y="713.1883267489347"/></g>
  <g id="402">
   <use class="kv10" height="20" transform="rotate(0,1529.57,735.188) scale(2.2,2.2) translate(-828.312,-389.012)" width="10" x="1518.571428571429" xlink:href="#Breaker:小车断路器_0" y="713.1883267489347" zvalue="1121"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924557012996" ObjectName="颗粒肥风机057断路器"/>
   <cge:TPSR_Ref TObjectID="6473924557012996"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1529.57,735.188) scale(2.2,2.2) translate(-828.312,-389.012)" width="10" x="1518.571428571429" y="713.1883267489347"/></g>
  <g id="396">
   <use class="kv10" height="20" transform="rotate(0,1605.57,735.188) scale(2.2,2.2) translate(-869.766,-389.012)" width="10" x="1594.571428571429" xlink:href="#Breaker:小车断路器_0" y="713.1883267489347" zvalue="1130"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924557078532" ObjectName="MVR-TM18058断路器"/>
   <cge:TPSR_Ref TObjectID="6473924557078532"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1605.57,735.188) scale(2.2,2.2) translate(-869.766,-389.012)" width="10" x="1594.571428571429" y="713.1883267489347"/></g>
  <g id="390">
   <use class="kv10" height="20" transform="rotate(0,1681.57,735.188) scale(2.2,2.2) translate(-911.221,-389.012)" width="10" x="1670.571428571429" xlink:href="#Breaker:小车断路器_0" y="713.1883267489347" zvalue="1139"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924557144068" ObjectName="MVR-TM17059断路器"/>
   <cge:TPSR_Ref TObjectID="6473924557144068"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1681.57,735.188) scale(2.2,2.2) translate(-911.221,-389.012)" width="10" x="1670.571428571429" y="713.1883267489347"/></g>
  <g id="384">
   <use class="kv10" height="20" transform="rotate(0,1761.57,735.188) scale(2.2,2.2) translate(-954.857,-389.012)" width="10" x="1750.571428571429" xlink:href="#Breaker:小车断路器_0" y="713.1883267489347" zvalue="1148"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924557209604" ObjectName="新干燥#1变061断路器"/>
   <cge:TPSR_Ref TObjectID="6473924557209604"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1761.57,735.188) scale(2.2,2.2) translate(-954.857,-389.012)" width="10" x="1750.571428571429" y="713.1883267489347"/></g>
  <g id="378">
   <use class="kv10" height="20" transform="rotate(0,1837.57,735.188) scale(2.2,2.2) translate(-996.312,-389.012)" width="10" x="1826.571428571429" xlink:href="#Breaker:小车断路器_0" y="713.1883267489347" zvalue="1157"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924557275140" ObjectName="新干燥#2变062断路器"/>
   <cge:TPSR_Ref TObjectID="6473924557275140"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1837.57,735.188) scale(2.2,2.2) translate(-996.312,-389.012)" width="10" x="1826.571428571429" y="713.1883267489347"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="523">
   <path class="kv35" d="M 928.57 351.86 L 928.57 305.31" stroke-width="1" zvalue="944"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="524@0" LinkObjectIDznd="560@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 928.57 351.86 L 928.57 305.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="522">
   <path class="kv35" d="M 928.57 392.01 L 928.63 451.92" stroke-width="1" zvalue="945"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="524@1" LinkObjectIDznd="527@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 928.57 392.01 L 928.63 451.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="521">
   <path class="kv10" d="M 928.57 528.66 L 928.57 571.86" stroke-width="1" zvalue="946"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="527@1" LinkObjectIDznd="526@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 928.57 528.66 L 928.57 571.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="520">
   <path class="kv10" d="M 928.57 612.01 L 928.57 655.02" stroke-width="1" zvalue="947"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="526@1" LinkObjectIDznd="559@14" MaxPinNum="2"/>
   </metadata>
  <path d="M 928.57 612.01 L 928.57 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="518">
   <path class="kv35" d="M 883.62 425.71 L 883.62 413.22 L 928.59 413.22" stroke-width="1" zvalue="950"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="519@0" LinkObjectIDznd="522" MaxPinNum="2"/>
   </metadata>
  <path d="M 883.62 425.71 L 883.62 413.22 L 928.59 413.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="515">
   <path class="kv35" d="M 1119.57 172.01 L 1119.57 223.86" stroke-width="1" zvalue="955"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="516@0" LinkObjectIDznd="517@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1119.57 172.01 L 1119.57 223.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="514">
   <path class="kv35" d="M 1120.57 264.01 L 1120.57 305.31" stroke-width="1" zvalue="956"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="517@1" LinkObjectIDznd="560@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1120.57 264.01 L 1120.57 305.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="511">
   <path class="kv10" d="M 556.21 577.79 L 556.21 594.21" stroke-width="1" zvalue="960"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="512@0" LinkObjectIDznd="513@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 556.21 577.79 L 556.21 594.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="510">
   <path class="kv10" d="M 556.32 628.21 L 556.32 655.02" stroke-width="1" zvalue="962"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="513@0" LinkObjectIDznd="559@13" MaxPinNum="2"/>
   </metadata>
  <path d="M 556.32 628.21 L 556.32 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="507">
   <path class="kv10" d="M 469.57 708.06 L 469.57 655.02" stroke-width="1" zvalue="967"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="509@0" LinkObjectIDznd="559@12" MaxPinNum="2"/>
   </metadata>
  <path d="M 469.57 708.06 L 469.57 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="506">
   <path class="kv10" d="M 469.57 827.09 L 469.57 748.21" stroke-width="1" zvalue="968"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="485@0" LinkObjectIDznd="509@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 469.57 827.09 L 469.57 748.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="500">
   <path class="kv35" d="M 1501.57 349.77 L 1501.57 305.31" stroke-width="1" zvalue="977"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="501@0" LinkObjectIDznd="560@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1501.57 349.77 L 1501.57 305.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="499">
   <path class="kv35" d="M 1501.57 389.92 L 1501.63 449.83" stroke-width="1" zvalue="979"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="501@1" LinkObjectIDznd="504@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1501.57 389.92 L 1501.63 449.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="498">
   <path class="kv10" d="M 1501.57 526.57 L 1501.57 569.77" stroke-width="1" zvalue="980"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="504@1" LinkObjectIDznd="503@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1501.57 526.57 L 1501.57 569.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="497">
   <path class="kv10" d="M 1501.57 609.92 L 1501.57 655.02" stroke-width="1" zvalue="981"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="503@1" LinkObjectIDznd="505@9" MaxPinNum="2"/>
   </metadata>
  <path d="M 1501.57 609.92 L 1501.57 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="495">
   <path class="kv35" d="M 1456.62 423.62 L 1456.62 411.12 L 1501.59 411.12" stroke-width="1" zvalue="984"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="496@0" LinkObjectIDznd="499" MaxPinNum="2"/>
   </metadata>
  <path d="M 1456.62 423.62 L 1456.62 411.12 L 1501.59 411.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="492">
   <path class="kv35" d="M 1219.57 342.21 L 1219.57 305.31" stroke-width="1" zvalue="989"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="494@0" LinkObjectIDznd="560@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1219.57 342.21 L 1219.57 305.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="491">
   <path class="kv35" d="M 1219.57 376.21 L 1219.57 401.6" stroke-width="1" zvalue="990"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="494@1" LinkObjectIDznd="493@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1219.57 376.21 L 1219.57 401.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="488">
   <path class="kv10" d="M 1778.21 578.79 L 1778.21 595.21" stroke-width="1" zvalue="994"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="489@0" LinkObjectIDznd="490@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1778.21 578.79 L 1778.21 595.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="487">
   <path class="kv10" d="M 1778.32 629.21 L 1778.32 655.02" stroke-width="1" zvalue="996"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="490@0" LinkObjectIDznd="505@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 1778.32 629.21 L 1778.32 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="486">
   <path class="kv10" d="M 496.62 786.91 L 496.62 779.21 L 469.57 779.21" stroke-width="1" zvalue="997"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="508@0" LinkObjectIDznd="506" MaxPinNum="2"/>
   </metadata>
  <path d="M 496.62 786.91 L 496.62 779.21 L 469.57 779.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="481">
   <path class="kv10" d="M 537.57 711.06 L 537.57 655.02" stroke-width="1" zvalue="1005"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="483@0" LinkObjectIDznd="559@11" MaxPinNum="2"/>
   </metadata>
  <path d="M 537.57 711.06 L 537.57 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="480">
   <path class="kv10" d="M 537.57 831.96 L 537.57 751.21" stroke-width="1" zvalue="1006"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="478@0" LinkObjectIDznd="483@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 537.57 831.96 L 537.57 751.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="479">
   <path class="kv10" d="M 564.62 789.91 L 564.62 782.21 L 537.57 782.21" stroke-width="1" zvalue="1007"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="482@0" LinkObjectIDznd="480" MaxPinNum="2"/>
   </metadata>
  <path d="M 564.62 789.91 L 564.62 782.21 L 537.57 782.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="475">
   <path class="kv10" d="M 601.57 711.06 L 601.57 655.02" stroke-width="1" zvalue="1014"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="477@0" LinkObjectIDznd="559@10" MaxPinNum="2"/>
   </metadata>
  <path d="M 601.57 711.06 L 601.57 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="474">
   <path class="kv10" d="M 601.57 831.96 L 601.57 751.21" stroke-width="1" zvalue="1015"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="472@0" LinkObjectIDznd="477@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 601.57 831.96 L 601.57 751.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="473">
   <path class="kv10" d="M 628.62 789.91 L 628.62 782.21 L 601.57 782.21" stroke-width="1" zvalue="1016"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="476@0" LinkObjectIDznd="474" MaxPinNum="2"/>
   </metadata>
  <path d="M 628.62 789.91 L 628.62 782.21 L 601.57 782.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="469">
   <path class="kv10" d="M 669.57 712.25 L 669.57 655.02" stroke-width="1" zvalue="1023"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="471@0" LinkObjectIDznd="559@9" MaxPinNum="2"/>
   </metadata>
  <path d="M 669.57 712.25 L 669.57 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="468">
   <path class="kv10" d="M 669.57 833.16 L 669.57 752.4" stroke-width="1" zvalue="1024"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="466@0" LinkObjectIDznd="471@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 669.57 833.16 L 669.57 752.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="467">
   <path class="kv10" d="M 696.62 791.1 L 696.62 783.41 L 669.57 783.41" stroke-width="1" zvalue="1025"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="470@0" LinkObjectIDznd="468" MaxPinNum="2"/>
   </metadata>
  <path d="M 696.62 791.1 L 696.62 783.41 L 669.57 783.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="463">
   <path class="kv10" d="M 738.57 713.45 L 738.57 655.02" stroke-width="1" zvalue="1032"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="465@0" LinkObjectIDznd="559@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 738.57 713.45 L 738.57 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="462">
   <path class="kv10" d="M 738.57 834.35 L 738.57 753.6" stroke-width="1" zvalue="1033"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="460@0" LinkObjectIDznd="465@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 738.57 834.35 L 738.57 753.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="461">
   <path class="kv10" d="M 765.62 792.3 L 765.62 784.6 L 738.57 784.6" stroke-width="1" zvalue="1034"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="464@0" LinkObjectIDznd="462" MaxPinNum="2"/>
   </metadata>
  <path d="M 765.62 792.3 L 765.62 784.6 L 738.57 784.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="457">
   <path class="kv10" d="M 806.57 714.64 L 806.57 655.02" stroke-width="1" zvalue="1041"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="459@0" LinkObjectIDznd="559@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 806.57 714.64 L 806.57 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="456">
   <path class="kv10" d="M 806.57 835.55 L 806.57 754.79" stroke-width="1" zvalue="1042"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="454@0" LinkObjectIDznd="459@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 806.57 835.55 L 806.57 754.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="455">
   <path class="kv10" d="M 833.62 793.49 L 833.62 785.8 L 806.57 785.8" stroke-width="1" zvalue="1043"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="458@0" LinkObjectIDznd="456" MaxPinNum="2"/>
   </metadata>
  <path d="M 833.62 793.49 L 833.62 785.8 L 806.57 785.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="451">
   <path class="kv10" d="M 875.57 715.84 L 875.57 655.02" stroke-width="1" zvalue="1050"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="453@0" LinkObjectIDznd="559@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 875.57 715.84 L 875.57 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="450">
   <path class="kv10" d="M 875.57 836.74 L 875.57 755.99" stroke-width="1" zvalue="1051"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="448@0" LinkObjectIDznd="453@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 875.57 836.74 L 875.57 755.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="449">
   <path class="kv10" d="M 902.62 794.69 L 902.62 786.99 L 875.57 786.99" stroke-width="1" zvalue="1052"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="452@0" LinkObjectIDznd="450" MaxPinNum="2"/>
   </metadata>
  <path d="M 902.62 794.69 L 902.62 786.99 L 875.57 786.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="445">
   <path class="kv10" d="M 949.57 715.84 L 949.57 655.02" stroke-width="1" zvalue="1059"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="447@0" LinkObjectIDznd="559@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 949.57 715.84 L 949.57 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="444">
   <path class="kv10" d="M 949.57 836.74 L 949.57 755.99" stroke-width="1" zvalue="1060"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="442@0" LinkObjectIDznd="447@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 949.57 836.74 L 949.57 755.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="443">
   <path class="kv10" d="M 976.62 794.69 L 976.62 786.99 L 949.57 786.99" stroke-width="1" zvalue="1061"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="446@0" LinkObjectIDznd="444" MaxPinNum="2"/>
   </metadata>
  <path d="M 976.62 794.69 L 976.62 786.99 L 949.57 786.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="439">
   <path class="kv10" d="M 1021.57 715.84 L 1021.57 655.02" stroke-width="1" zvalue="1068"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="441@0" LinkObjectIDznd="559@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1021.57 715.84 L 1021.57 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="438">
   <path class="kv10" d="M 1021.57 836.74 L 1021.57 755.99" stroke-width="1" zvalue="1069"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="436@0" LinkObjectIDznd="441@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1021.57 836.74 L 1021.57 755.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="437">
   <path class="kv10" d="M 1048.62 794.69 L 1048.62 786.99 L 1021.57 786.99" stroke-width="1" zvalue="1070"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="440@0" LinkObjectIDznd="438" MaxPinNum="2"/>
   </metadata>
  <path d="M 1048.62 794.69 L 1048.62 786.99 L 1021.57 786.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="433">
   <path class="kv10" d="M 1097.57 714.84 L 1097.57 655.02" stroke-width="1" zvalue="1077"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="435@0" LinkObjectIDznd="559@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1097.57 714.84 L 1097.57 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="432">
   <path class="kv10" d="M 1097.57 835.74 L 1097.57 754.99" stroke-width="1" zvalue="1078"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="430@0" LinkObjectIDznd="435@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1097.57 835.74 L 1097.57 754.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="431">
   <path class="kv10" d="M 1124.62 793.69 L 1124.62 785.99 L 1097.57 785.99" stroke-width="1" zvalue="1079"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="434@0" LinkObjectIDznd="432" MaxPinNum="2"/>
   </metadata>
  <path d="M 1124.62 793.69 L 1124.62 785.99 L 1097.57 785.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="428">
   <path class="kv10" d="M 1174.57 716.03 L 1174.57 655.02" stroke-width="1" zvalue="1084"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="429@0" LinkObjectIDznd="559@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1174.57 716.03 L 1174.57 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="426">
   <path class="kv10" d="M 1174.57 756.18 L 1174.57 801.2" stroke-width="1" zvalue="1086"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="429@1" LinkObjectIDznd="427@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1174.57 756.18 L 1174.57 801.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="424">
   <path class="kv10" d="M 1247.57 714.03 L 1247.57 655.02" stroke-width="1" zvalue="1090"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="425@0" LinkObjectIDznd="559@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1247.57 714.03 L 1247.57 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="421">
   <path class="kv10" d="M 1247.58 775.29 L 1247.57 754.18" stroke-width="1" zvalue="1094"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="422@1" LinkObjectIDznd="425@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1247.58 775.29 L 1247.57 754.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="420">
   <path class="kv10" d="M 1247.66 801.18 L 1247.66 834.46" stroke-width="1" zvalue="1096"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="422@0" LinkObjectIDznd="423@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1247.66 801.18 L 1247.66 834.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="417">
   <path class="kv10" d="M 1259.57 624.01 L 1259.57 655.02" stroke-width="1" zvalue="1101"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="419@1" LinkObjectIDznd="559@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1259.57 624.01 L 1259.57 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="416">
   <path class="kv10" d="M 1259.57 583.86 L 1259.57 558.21 L 1368.57 558.21 L 1368.57 586.21" stroke-width="1" zvalue="1102"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="419@0" LinkObjectIDznd="418@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1259.57 583.86 L 1259.57 558.21 L 1368.57 558.21 L 1368.57 586.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="415">
   <path class="kv10" d="M 1368.57 620.21 L 1368.57 655.02" stroke-width="1" zvalue="1103"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="418@1" LinkObjectIDznd="505@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1368.57 620.21 L 1368.57 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="413">
   <path class="kv10" d="M 1333.57 585.37 L 1333.57 559.31" stroke-width="1" zvalue="1105"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="414@0" LinkObjectIDznd="416" MaxPinNum="2"/>
   </metadata>
  <path d="M 1333.57 585.37 L 1333.57 559.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="411">
   <path class="kv10" d="M 1386.57 716.03 L 1386.57 655.02" stroke-width="1" zvalue="1108"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="412@0" LinkObjectIDznd="505@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1386.57 716.03 L 1386.57 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="409">
   <path class="kv10" d="M 1386.57 756.18 L 1386.57 801.2" stroke-width="1" zvalue="1110"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="412@1" LinkObjectIDznd="410@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1386.57 756.18 L 1386.57 801.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="406">
   <path class="kv10" d="M 1457.57 714.84 L 1457.57 655.02" stroke-width="1" zvalue="1116"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="408@0" LinkObjectIDznd="505@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1457.57 714.84 L 1457.57 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="405">
   <path class="kv10" d="M 1457.57 835.74 L 1457.57 754.99" stroke-width="1" zvalue="1117"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="403@0" LinkObjectIDznd="408@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1457.57 835.74 L 1457.57 754.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="404">
   <path class="kv10" d="M 1484.62 793.69 L 1484.62 785.99 L 1457.57 785.99" stroke-width="1" zvalue="1118"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="407@0" LinkObjectIDznd="405" MaxPinNum="2"/>
   </metadata>
  <path d="M 1484.62 793.69 L 1484.62 785.99 L 1457.57 785.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="400">
   <path class="kv10" d="M 1529.57 714.84 L 1529.57 655.02" stroke-width="1" zvalue="1125"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="402@0" LinkObjectIDznd="505@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1529.57 714.84 L 1529.57 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="399">
   <path class="kv10" d="M 1529.57 835.74 L 1529.57 754.99" stroke-width="1" zvalue="1126"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="397@0" LinkObjectIDznd="402@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1529.57 835.74 L 1529.57 754.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="398">
   <path class="kv10" d="M 1556.62 793.69 L 1556.62 785.99 L 1529.57 785.99" stroke-width="1" zvalue="1127"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="401@0" LinkObjectIDznd="399" MaxPinNum="2"/>
   </metadata>
  <path d="M 1556.62 793.69 L 1556.62 785.99 L 1529.57 785.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="394">
   <path class="kv10" d="M 1605.57 714.84 L 1605.57 655.02" stroke-width="1" zvalue="1134"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="396@0" LinkObjectIDznd="505@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1605.57 714.84 L 1605.57 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="393">
   <path class="kv10" d="M 1605.57 835.74 L 1605.57 754.99" stroke-width="1" zvalue="1135"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="391@0" LinkObjectIDznd="396@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1605.57 835.74 L 1605.57 754.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="392">
   <path class="kv10" d="M 1632.62 793.69 L 1632.62 785.99 L 1605.57 785.99" stroke-width="1" zvalue="1136"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="395@0" LinkObjectIDznd="393" MaxPinNum="2"/>
   </metadata>
  <path d="M 1632.62 793.69 L 1632.62 785.99 L 1605.57 785.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="388">
   <path class="kv10" d="M 1681.57 714.84 L 1681.57 655.02" stroke-width="1" zvalue="1143"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="390@0" LinkObjectIDznd="505@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1681.57 714.84 L 1681.57 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="387">
   <path class="kv10" d="M 1681.57 835.74 L 1681.57 754.99" stroke-width="1" zvalue="1144"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="385@0" LinkObjectIDznd="390@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1681.57 835.74 L 1681.57 754.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="386">
   <path class="kv10" d="M 1708.62 793.69 L 1708.62 785.99 L 1681.57 785.99" stroke-width="1" zvalue="1145"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="389@0" LinkObjectIDznd="387" MaxPinNum="2"/>
   </metadata>
  <path d="M 1708.62 793.69 L 1708.62 785.99 L 1681.57 785.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="382">
   <path class="kv10" d="M 1761.57 714.84 L 1761.57 655.02" stroke-width="1" zvalue="1152"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="384@0" LinkObjectIDznd="505@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1761.57 714.84 L 1761.57 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="381">
   <path class="kv10" d="M 1761.57 835.74 L 1761.57 754.99" stroke-width="1" zvalue="1153"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="379@0" LinkObjectIDznd="384@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1761.57 835.74 L 1761.57 754.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="380">
   <path class="kv10" d="M 1788.62 793.69 L 1788.62 785.99 L 1761.57 785.99" stroke-width="1" zvalue="1154"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="383@0" LinkObjectIDznd="381" MaxPinNum="2"/>
   </metadata>
  <path d="M 1788.62 793.69 L 1788.62 785.99 L 1761.57 785.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="376">
   <path class="kv10" d="M 1837.57 714.84 L 1837.57 655.02" stroke-width="1" zvalue="1161"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="378@0" LinkObjectIDznd="505@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1837.57 714.84 L 1837.57 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="375">
   <path class="kv10" d="M 1837.57 835.74 L 1837.57 754.99" stroke-width="1" zvalue="1162"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="373@0" LinkObjectIDznd="378@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1837.57 835.74 L 1837.57 754.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="374">
   <path class="kv10" d="M 1864.62 793.69 L 1864.62 785.99 L 1837.57 785.99" stroke-width="1" zvalue="1163"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="377@0" LinkObjectIDznd="375" MaxPinNum="2"/>
   </metadata>
  <path d="M 1864.62 793.69 L 1864.62 785.99 L 1837.57 785.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="519">
   <use class="kv35" height="20" transform="rotate(0,883.571,435.214) scale(1,1) translate(0,0)" width="10" x="878.5714285714289" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="425.2142857142857" zvalue="948"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450013429765" ObjectName="#1主变35kV侧30167断路器"/>
   <cge:TPSR_Ref TObjectID="6192450013429765"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,883.571,435.214) scale(1,1) translate(0,0)" width="10" x="878.5714285714289" y="425.2142857142857"/></g>
  <g id="508">
   <use class="kv10" height="20" transform="rotate(0,496.571,796.409) scale(1,1) translate(0,0)" width="10" x="491.5714285714289" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="786.4090909090909" zvalue="965"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450013757445" ObjectName="#1F04167断路器"/>
   <cge:TPSR_Ref TObjectID="6192450013757445"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,496.571,796.409) scale(1,1) translate(0,0)" width="10" x="491.5714285714289" y="786.4090909090909"/></g>
  <g id="496">
   <use class="kv35" height="20" transform="rotate(0,1456.57,433.123) scale(1,1) translate(0,0)" width="10" x="1451.571428571429" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="423.1233766233767" zvalue="982"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450013888517" ObjectName="#2主变35kV侧30267断路器"/>
   <cge:TPSR_Ref TObjectID="6192450013888517"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1456.57,433.123) scale(1,1) translate(0,0)" width="10" x="1451.571428571429" y="423.1233766233767"/></g>
  <g id="482">
   <use class="kv10" height="20" transform="rotate(0,564.571,799.409) scale(1,1) translate(0,0)" width="10" x="559.5714285714289" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="789.4090909090909" zvalue="1003"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450014412805" ObjectName="环保车间04267断路器"/>
   <cge:TPSR_Ref TObjectID="6192450014412805"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,564.571,799.409) scale(1,1) translate(0,0)" width="10" x="559.5714285714289" y="789.4090909090909"/></g>
  <g id="476">
   <use class="kv10" height="20" transform="rotate(0,628.571,799.409) scale(1,1) translate(0,0)" width="10" x="623.5714285714289" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="789.4090909090909" zvalue="1012"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450014609413" ObjectName="2号循环水站04367断路器"/>
   <cge:TPSR_Ref TObjectID="6192450014609413"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,628.571,799.409) scale(1,1) translate(0,0)" width="10" x="623.5714285714289" y="789.4090909090909"/></g>
  <g id="470">
   <use class="kv10" height="20" transform="rotate(0,696.571,800.604) scale(1,1) translate(0,0)" width="10" x="691.5714285714289" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="790.6038998690519" zvalue="1021"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450014806021" ObjectName="1号循环水站04467断路器"/>
   <cge:TPSR_Ref TObjectID="6192450014806021"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,696.571,800.604) scale(1,1) translate(0,0)" width="10" x="691.5714285714289" y="790.6038998690519"/></g>
  <g id="464">
   <use class="kv10" height="20" transform="rotate(0,765.571,801.799) scale(1,1) translate(0,0)" width="10" x="760.5714285714289" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="791.7987088290129" zvalue="1030"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450015002629" ObjectName="6号酵母车间04567断路器"/>
   <cge:TPSR_Ref TObjectID="6192450015002629"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,765.571,801.799) scale(1,1) translate(0,0)" width="10" x="760.5714285714289" y="791.7987088290129"/></g>
  <g id="458">
   <use class="kv10" height="20" transform="rotate(0,833.571,802.994) scale(1,1) translate(0,0)" width="10" x="828.5714285714289" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="792.9935177889737" zvalue="1039"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450015199237" ObjectName="5号酵母车间04667断路器"/>
   <cge:TPSR_Ref TObjectID="6192450015199237"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,833.571,802.994) scale(1,1) translate(0,0)" width="10" x="828.5714285714289" y="792.9935177889737"/></g>
  <g id="452">
   <use class="kv10" height="20" transform="rotate(0,902.571,804.188) scale(1,1) translate(0,0)" width="10" x="897.5714285714289" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="794.1883267489347" zvalue="1048"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450015395845" ObjectName="4号酵母车间04767断路器"/>
   <cge:TPSR_Ref TObjectID="6192450015395845"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,902.571,804.188) scale(1,1) translate(0,0)" width="10" x="897.5714285714289" y="794.1883267489347"/></g>
  <g id="446">
   <use class="kv10" height="20" transform="rotate(0,976.571,804.188) scale(1,1) translate(0,0)" width="10" x="971.5714285714289" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="794.1883267489347" zvalue="1057"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450015592453" ObjectName="3号酵母车间04867断路器"/>
   <cge:TPSR_Ref TObjectID="6192450015592453"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,976.571,804.188) scale(1,1) translate(0,0)" width="10" x="971.5714285714289" y="794.1883267489347"/></g>
  <g id="440">
   <use class="kv10" height="20" transform="rotate(0,1048.57,804.188) scale(1,1) translate(0,0)" width="10" x="1043.571428571429" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="794.1883267489347" zvalue="1066"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450015789061" ObjectName="2号酵母车间04967断路器"/>
   <cge:TPSR_Ref TObjectID="6192450015789061"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1048.57,804.188) scale(1,1) translate(0,0)" width="10" x="1043.571428571429" y="794.1883267489347"/></g>
  <g id="434">
   <use class="kv10" height="20" transform="rotate(0,1124.57,803.188) scale(1,1) translate(0,0)" width="10" x="1119.571428571429" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="793.1883267489347" zvalue="1075"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450015985669" ObjectName="1号酵母车间05167断路器"/>
   <cge:TPSR_Ref TObjectID="6192450015985669"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1124.57,803.188) scale(1,1) translate(0,0)" width="10" x="1119.571428571429" y="793.1883267489347"/></g>
  <g id="407">
   <use class="kv10" height="20" transform="rotate(0,1484.57,803.188) scale(1,1) translate(0,0)" width="10" x="1479.571428571429" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="793.1883267489347" zvalue="1114"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450016575493" ObjectName="馈线605667断路器"/>
   <cge:TPSR_Ref TObjectID="6192450016575493"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1484.57,803.188) scale(1,1) translate(0,0)" width="10" x="1479.571428571429" y="793.1883267489347"/></g>
  <g id="401">
   <use class="kv10" height="20" transform="rotate(0,1556.57,803.188) scale(1,1) translate(0,0)" width="10" x="1551.571428571429" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="793.1883267489347" zvalue="1123"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450016772101" ObjectName="颗粒肥风机05767断路器"/>
   <cge:TPSR_Ref TObjectID="6192450016772101"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1556.57,803.188) scale(1,1) translate(0,0)" width="10" x="1551.571428571429" y="793.1883267489347"/></g>
  <g id="395">
   <use class="kv10" height="20" transform="rotate(0,1632.57,803.188) scale(1,1) translate(0,0)" width="10" x="1627.571428571429" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="793.1883267489347" zvalue="1132"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450016968709" ObjectName="MVR-TM1805867断路器"/>
   <cge:TPSR_Ref TObjectID="6192450016968709"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1632.57,803.188) scale(1,1) translate(0,0)" width="10" x="1627.571428571429" y="793.1883267489347"/></g>
  <g id="389">
   <use class="kv10" height="20" transform="rotate(0,1708.57,803.188) scale(1,1) translate(0,0)" width="10" x="1703.571428571429" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="793.1883267489347" zvalue="1141"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450017165317" ObjectName="MVR-TM1705967断路器"/>
   <cge:TPSR_Ref TObjectID="6192450017165317"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1708.57,803.188) scale(1,1) translate(0,0)" width="10" x="1703.571428571429" y="793.1883267489347"/></g>
  <g id="383">
   <use class="kv10" height="20" transform="rotate(0,1788.57,803.188) scale(1,1) translate(0,0)" width="10" x="1783.571428571429" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="793.1883267489347" zvalue="1150"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450017361925" ObjectName="新干燥#1变06167断路器"/>
   <cge:TPSR_Ref TObjectID="6192450017361925"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1788.57,803.188) scale(1,1) translate(0,0)" width="10" x="1783.571428571429" y="793.1883267489347"/></g>
  <g id="377">
   <use class="kv10" height="20" transform="rotate(0,1864.57,803.188) scale(1,1) translate(0,0)" width="10" x="1859.571428571429" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="793.1883267489347" zvalue="1159"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450017558533" ObjectName="新干燥#2变06267断路器"/>
   <cge:TPSR_Ref TObjectID="6192450017558533"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1864.57,803.188) scale(1,1) translate(0,0)" width="10" x="1859.571428571429" y="793.1883267489347"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="513">
   <use class="kv10" height="36" transform="rotate(0,556.321,611.214) scale(1,-1) translate(0,-1222.43)" width="14" x="549.3214285714289" xlink:href="#Disconnector:手车刀闸_0" y="593.2142857142857" zvalue="957"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450013626373" ObjectName="10kVⅠ段母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450013626373"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,556.321,611.214) scale(1,-1) translate(0,-1222.43)" width="14" x="549.3214285714289" y="593.2142857142857"/></g>
  <g id="494">
   <use class="kv35" height="36" transform="rotate(0,1219.57,359.214) scale(1,1) translate(0,0)" width="14" x="1212.571428571429" xlink:href="#Disconnector:手车刀闸_0" y="341.2142857142857" zvalue="985"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450013954053" ObjectName="35kV母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450013954053"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1219.57,359.214) scale(1,1) translate(0,0)" width="14" x="1212.571428571429" y="341.2142857142857"/></g>
  <g id="490">
   <use class="kv10" height="36" transform="rotate(0,1778.32,612.214) scale(1,-1) translate(0,-1224.43)" width="14" x="1771.321428571429" xlink:href="#Disconnector:手车刀闸_0" y="594.2142857142857" zvalue="991"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450014150661" ObjectName="10kVⅡ段母线电压互感器0902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450014150661"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1778.32,612.214) scale(1,-1) translate(0,-1224.43)" width="14" x="1771.321428571429" y="594.2142857142857"/></g>
  <g id="422">
   <use class="kv10" height="26" transform="rotate(0,1247.57,788.214) scale(1,1) translate(0,0)" width="12" x="1241.571428571429" xlink:href="#Disconnector:小车隔刀熔断器_0" y="775.2142857142858" zvalue="1093"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450016182277" ObjectName="10kV备用线0551隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450016182277"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1247.57,788.214) scale(1,1) translate(0,0)" width="12" x="1241.571428571429" y="775.2142857142858"/></g>
  <g id="418">
   <use class="kv10" height="36" transform="rotate(0,1368.57,603.214) scale(0.571442,1) translate(1023.37,0)" width="14" x="1364.571382795062" xlink:href="#Disconnector:手车刀闸_0" y="585.2142857142857" zvalue="1099"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450016247813" ObjectName="10kV分段0122隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450016247813"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1368.57,603.214) scale(0.571442,1) translate(1023.37,0)" width="14" x="1364.571382795062" y="585.2142857142857"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="512">
   <use class="kv10" height="30" transform="rotate(0,556.259,560.214) scale(1,-1.2) translate(0,-1024.06)" width="30" x="541.2589700466356" xlink:href="#Accessory:pt带容断器_0" y="542.2142857142857" zvalue="958"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450013560837" ObjectName="10kVⅠ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,556.259,560.214) scale(1,-1.2) translate(0,-1024.06)" width="30" x="541.2589700466356" y="542.2142857142857"/></g>
  <g id="493">
   <use class="kv35" height="18" transform="rotate(0,1223.32,419.714) scale(2.16667,2.16667) translate(-649.962,-215.5)" width="15" x="1207.071428571429" xlink:href="#Accessory:PT8_0" y="400.2142857142857" zvalue="987"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450014019589" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1223.32,419.714) scale(2.16667,2.16667) translate(-649.962,-215.5)" width="15" x="1207.071428571429" y="400.2142857142857"/></g>
  <g id="489">
   <use class="kv10" height="30" transform="rotate(0,1778.26,561.214) scale(1,-1.2) translate(0,-1025.89)" width="30" x="1763.258970046636" xlink:href="#Accessory:pt带容断器_0" y="543.2142857142857" zvalue="992"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450014085125" ObjectName="10kVⅡ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1778.26,561.214) scale(1,-1.2) translate(0,-1025.89)" width="30" x="1763.258970046636" y="543.2142857142857"/></g>
  <g id="414">
   <use class="kv10" height="20" transform="rotate(0,1333.57,595.214) scale(1.25,1.25) translate(-264.214,-116.543)" width="20" x="1321.071428571429" xlink:href="#Accessory:线路PT3_0" y="582.7142857142857" zvalue="1104"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450016313349" ObjectName="10kV分段避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1333.57,595.214) scale(1.25,1.25) translate(-264.214,-116.543)" width="20" x="1321.071428571429" y="582.7142857142857"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="485">
   <use class="kv10" height="30" transform="rotate(0,469.571,849.214) scale(1.5,1.5) translate(-149.024,-275.571)" width="30" x="447.0714285714289" xlink:href="#Generator:发电机_0" y="826.7142857142858" zvalue="998"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450014216197" ObjectName="#1F"/>
   <cge:TPSR_Ref TObjectID="6192450014216197"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,469.571,849.214) scale(1.5,1.5) translate(-149.024,-275.571)" width="30" x="447.0714285714289" y="826.7142857142858"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="478">
   <use class="kv10" height="30" transform="rotate(0,537.571,852.214) scale(3.75,-1.5) translate(-377.719,-1412.86)" width="12" x="515.0714285714289" xlink:href="#EnergyConsumer:负荷_0" y="829.7142857142858" zvalue="1008"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450014281733" ObjectName="环保车间"/>
   <cge:TPSR_Ref TObjectID="6192450014281733"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,537.571,852.214) scale(3.75,-1.5) translate(-377.719,-1412.86)" width="12" x="515.0714285714289" y="829.7142857142858"/></g>
  <g id="472">
   <use class="kv10" height="30" transform="rotate(0,601.571,852.214) scale(3.75,-1.5) translate(-424.652,-1412.86)" width="12" x="579.0714285714289" xlink:href="#EnergyConsumer:负荷_0" y="829.7142857142858" zvalue="1017"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450014478341" ObjectName="2号循环水站"/>
   <cge:TPSR_Ref TObjectID="6192450014478341"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,601.571,852.214) scale(3.75,-1.5) translate(-424.652,-1412.86)" width="12" x="579.0714285714289" y="829.7142857142858"/></g>
  <g id="466">
   <use class="kv10" height="30" transform="rotate(0,669.571,853.409) scale(3.75,-1.5) translate(-474.519,-1414.85)" width="12" x="647.0714285714289" xlink:href="#EnergyConsumer:负荷_0" y="830.9090946742466" zvalue="1026"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450014674949" ObjectName="1号循环水站"/>
   <cge:TPSR_Ref TObjectID="6192450014674949"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,669.571,853.409) scale(3.75,-1.5) translate(-474.519,-1414.85)" width="12" x="647.0714285714289" y="830.9090946742466"/></g>
  <g id="460">
   <use class="kv10" height="30" transform="rotate(0,738.571,854.604) scale(3.75,-1.5) translate(-525.119,-1416.84)" width="12" x="716.0714285714289" xlink:href="#EnergyConsumer:负荷_0" y="832.1039036342074" zvalue="1035"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450014871557" ObjectName="6号酵母车间"/>
   <cge:TPSR_Ref TObjectID="6192450014871557"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,738.571,854.604) scale(3.75,-1.5) translate(-525.119,-1416.84)" width="12" x="716.0714285714289" y="832.1039036342074"/></g>
  <g id="454">
   <use class="kv10" height="30" transform="rotate(0,806.571,855.799) scale(3.75,-1.5) translate(-574.986,-1418.83)" width="12" x="784.0714285714289" xlink:href="#EnergyConsumer:负荷_0" y="833.2987125941685" zvalue="1044"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450015068165" ObjectName="5号酵母车间"/>
   <cge:TPSR_Ref TObjectID="6192450015068165"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,806.571,855.799) scale(3.75,-1.5) translate(-574.986,-1418.83)" width="12" x="784.0714285714289" y="833.2987125941685"/></g>
  <g id="448">
   <use class="kv10" height="30" transform="rotate(0,875.571,856.994) scale(3.75,-1.5) translate(-625.586,-1420.82)" width="12" x="853.0714285714289" xlink:href="#EnergyConsumer:负荷_0" y="834.4935215541295" zvalue="1053"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450015264773" ObjectName="4号酵母车间"/>
   <cge:TPSR_Ref TObjectID="6192450015264773"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,875.571,856.994) scale(3.75,-1.5) translate(-625.586,-1420.82)" width="12" x="853.0714285714289" y="834.4935215541295"/></g>
  <g id="442">
   <use class="kv10" height="30" transform="rotate(0,949.571,856.994) scale(3.75,-1.5) translate(-679.852,-1420.82)" width="12" x="927.0714285714289" xlink:href="#EnergyConsumer:负荷_0" y="834.4935215541295" zvalue="1062"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450015461381" ObjectName="3号酵母车间"/>
   <cge:TPSR_Ref TObjectID="6192450015461381"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,949.571,856.994) scale(3.75,-1.5) translate(-679.852,-1420.82)" width="12" x="927.0714285714289" y="834.4935215541295"/></g>
  <g id="436">
   <use class="kv10" height="30" transform="rotate(0,1021.57,856.994) scale(3.75,-1.5) translate(-732.652,-1420.82)" width="12" x="999.0714285714289" xlink:href="#EnergyConsumer:负荷_0" y="834.4935215541295" zvalue="1071"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450015657989" ObjectName="2号酵母车间"/>
   <cge:TPSR_Ref TObjectID="6192450015657989"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1021.57,856.994) scale(3.75,-1.5) translate(-732.652,-1420.82)" width="12" x="999.0714285714289" y="834.4935215541295"/></g>
  <g id="430">
   <use class="kv10" height="30" transform="rotate(0,1097.57,855.994) scale(3.75,-1.5) translate(-788.386,-1419.16)" width="12" x="1075.071428571429" xlink:href="#EnergyConsumer:负荷_0" y="833.4935215541295" zvalue="1080"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450015854597" ObjectName="1号酵母车间"/>
   <cge:TPSR_Ref TObjectID="6192450015854597"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1097.57,855.994) scale(3.75,-1.5) translate(-788.386,-1419.16)" width="12" x="1075.071428571429" y="833.4935215541295"/></g>
  <g id="427">
   <use class="kv10" height="35" transform="rotate(0,1174.57,819.136) scale(1.28125,1.28125) translate(-253.333,-174.889)" width="32" x="1154.071428571429" xlink:href="#EnergyConsumer:站用变13_0" y="796.7142857142858" zvalue="1085"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450016051205" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1174.57,819.136) scale(1.28125,1.28125) translate(-253.333,-174.889)" width="32" x="1154.071428571429" y="796.7142857142858"/></g>
  <g id="423">
   <use class="kv10" height="30" transform="rotate(0,1247.66,854.714) scale(3.75,-1.5) translate(-898.447,-1417.02)" width="12" x="1225.155495795344" xlink:href="#EnergyConsumer:负荷_0" y="832.2142857142858" zvalue="1091"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450016116741" ObjectName="10kV备用线"/>
   <cge:TPSR_Ref TObjectID="6192450016116741"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1247.66,854.714) scale(3.75,-1.5) translate(-898.447,-1417.02)" width="12" x="1225.155495795344" y="832.2142857142858"/></g>
  <g id="410">
   <use class="kv10" height="35" transform="rotate(0,1386.57,819.136) scale(1.28125,1.28125) translate(-299.869,-174.889)" width="32" x="1366.071428571429" xlink:href="#EnergyConsumer:站用变13_0" y="796.7142857142858" zvalue="1109"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450016378885" ObjectName="#2站用变"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1386.57,819.136) scale(1.28125,1.28125) translate(-299.869,-174.889)" width="32" x="1366.071428571429" y="796.7142857142858"/></g>
  <g id="403">
   <use class="kv10" height="30" transform="rotate(0,1457.57,855.994) scale(3.75,-1.5) translate(-1052.39,-1419.16)" width="12" x="1435.071428571429" xlink:href="#EnergyConsumer:负荷_0" y="833.4935215541295" zvalue="1119"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450016444421" ObjectName="馈线6"/>
   <cge:TPSR_Ref TObjectID="6192450016444421"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1457.57,855.994) scale(3.75,-1.5) translate(-1052.39,-1419.16)" width="12" x="1435.071428571429" y="833.4935215541295"/></g>
  <g id="397">
   <use class="kv10" height="30" transform="rotate(0,1529.57,855.994) scale(3.75,-1.5) translate(-1105.19,-1419.16)" width="12" x="1507.071428571429" xlink:href="#EnergyConsumer:负荷_0" y="833.4935215541295" zvalue="1128"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450016641029" ObjectName="颗粒肥风机"/>
   <cge:TPSR_Ref TObjectID="6192450016641029"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1529.57,855.994) scale(3.75,-1.5) translate(-1105.19,-1419.16)" width="12" x="1507.071428571429" y="833.4935215541295"/></g>
  <g id="391">
   <use class="kv10" height="30" transform="rotate(0,1605.57,855.994) scale(3.75,-1.5) translate(-1160.92,-1419.16)" width="12" x="1583.071428571429" xlink:href="#EnergyConsumer:负荷_0" y="833.4935215541295" zvalue="1137"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450016837637" ObjectName="MVR-TM18"/>
   <cge:TPSR_Ref TObjectID="6192450016837637"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1605.57,855.994) scale(3.75,-1.5) translate(-1160.92,-1419.16)" width="12" x="1583.071428571429" y="833.4935215541295"/></g>
  <g id="385">
   <use class="kv10" height="30" transform="rotate(0,1681.57,855.994) scale(3.75,-1.5) translate(-1216.65,-1419.16)" width="12" x="1659.071428571429" xlink:href="#EnergyConsumer:负荷_0" y="833.4935215541295" zvalue="1146"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450017034245" ObjectName="MVR-TM17"/>
   <cge:TPSR_Ref TObjectID="6192450017034245"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1681.57,855.994) scale(3.75,-1.5) translate(-1216.65,-1419.16)" width="12" x="1659.071428571429" y="833.4935215541295"/></g>
  <g id="379">
   <use class="kv10" height="30" transform="rotate(0,1761.57,855.994) scale(3.75,-1.5) translate(-1275.32,-1419.16)" width="12" x="1739.071428571429" xlink:href="#EnergyConsumer:负荷_0" y="833.4935215541295" zvalue="1155"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450017230853" ObjectName="新干燥#1变"/>
   <cge:TPSR_Ref TObjectID="6192450017230853"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1761.57,855.994) scale(3.75,-1.5) translate(-1275.32,-1419.16)" width="12" x="1739.071428571429" y="833.4935215541295"/></g>
  <g id="373">
   <use class="kv10" height="30" transform="rotate(0,1837.57,855.994) scale(3.75,-1.5) translate(-1331.05,-1419.16)" width="12" x="1815.071428571429" xlink:href="#EnergyConsumer:负荷_0" y="833.4935215541295" zvalue="1164"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450017427461" ObjectName="新干燥#2变"/>
   <cge:TPSR_Ref TObjectID="6192450017427461"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1837.57,855.994) scale(3.75,-1.5) translate(-1331.05,-1419.16)" width="12" x="1815.071428571429" y="833.4935215541295"/></g>
 </g>
</svg>