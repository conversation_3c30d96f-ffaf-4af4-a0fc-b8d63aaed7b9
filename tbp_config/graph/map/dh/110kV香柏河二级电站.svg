<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549580857347" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="14.63292181069959"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="9.583333333333334" y2="14.63582329690123"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00459493611119" x2="10.08333333333333" y1="14.63240401999107" y2="19.41666666666666"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01979386477084" x2="20.08333333333333" y1="14.63518747193215" y2="19.41666666666666"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 10.1667 40.3233 L 19.9167 40.3233 L 15.0833 32.3333 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.499999999999999" x2="5.916666666666666" y1="3.583333333333334" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="4.416666666666668" y2="20.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="4.249999999999998" y2="20.16666666666667"/>
  </symbol>
  <symbol id="Accessory:四卷PT_0" viewBox="0,0,18,18">
   <use terminal-index="0" type="0" x="9" xlink:href="#terminal" y="0.2666666666666639"/>
   <ellipse cx="9.25" cy="5.25" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.25" cy="9.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.08" cy="12.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.82" cy="9.27" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="5" y1="7.13888888888889" y2="9.138888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="11.75" y2="9.138888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="5" y1="7.13888888888889" y2="9.138888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.25" x2="9.25" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.25" x2="9.25" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.25" x2="9.25" y1="7.500000000000001" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="9" y1="10.63888888888889" y2="12.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="9" y1="10.63888888888889" y2="12.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="9" y1="15.25" y2="12.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="12.31481481481481" y1="9.96612466124661" y2="7.499999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.94444444444444" x2="13.62962962962963" y1="9.96612466124661" y2="7.499999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="14.94444444444444" y1="9.966124661246614" y2="9.966124661246614"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.027974625923065" x2="10.46799242340996" y1="19.93170597265525" y2="19.93170597265525"/>
  </symbol>
  <symbol id="Accessory:线路PT11带避雷器_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="20" xlink:href="#terminal" y="0.9166666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.91666666666667" x2="22.91666666666667" y1="23.75" y2="23.75"/>
   <rect fill-opacity="0" height="10" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,20,11) scale(1,1) translate(0,0)" width="6" x="17" y="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32" x2="20" y1="5" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32" x2="32" y1="17" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.13040845230975" x2="32.13040845230975" y1="16.76788570496156" y2="23.53635804601289"/>
   <rect fill-opacity="0" height="6.05" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,32.04,24.56) scale(1,1) translate(0,0)" width="15.34" x="24.37" y="21.53"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.98821300514558" x2="19.98821300514558" y1="1.029523490692871" y2="18.75"/>
   <path d="M 30.0147 23.4481 L 34.2865 23.4481 L 32.115 28.6155 L 30.0147 23.4481" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <ellipse cx="19.76" cy="32.23" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.76" cy="24.17" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.22179295459144" x2="32.22179295459144" y1="32.22550978083666" y2="36.2625308385742"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="29.01788570496155" x2="35.42570020422134" y1="36.23023467011234" y2="36.23023467011234"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="30.26384963537316" x2="34.53572596821302" y1="37.52208140858838" y2="37.52208140858838"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.15382387138148" x2="33.11176719059974" y1="38.49096646244535" y2="38.49096646244535"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.91666666666667" x2="22.91666666666667" y1="32.91666666666667" y2="32.91666666666667"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.08333333333333" x2="9.833333333333332" y1="16.5" y2="18.58333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="14" y1="9.75" y2="16.75"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.75" x2="11" y1="11.58333333333333" y2="9.666666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.75" x2="9.75" y1="11.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8" x2="15.08333333333333" y1="8.416666666666668" y2="25.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_1" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <rect fill-opacity="0" height="8" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,14.96,13) scale(1,1) translate(0,0)" width="4.42" x="12.75" y="9"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="5.416666666666668" y2="25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_2" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="20" y1="6" y2="25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20" x2="10.08333333333333" y1="6.000000000000004" y2="25.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
  </symbol>
  <symbol id="Accessory:PT6_0" viewBox="0,0,15,20">
   <use terminal-index="0" type="0" x="7.560000000000002" xlink:href="#terminal" y="0.42361930658363"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.160000000000005" x2="8.559999999999999" y1="15.48035578286757" y2="14.24281579851547"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.060000000000002" x2="8.459999999999996" y1="16.09912577504353" y2="17.33666575939564"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.459999999999999" x2="5.060000000000007" y1="4.75500925181603" y2="5.992549236168133"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.460000000000005" x2="9.859999999999998" y1="4.75500925181603" y2="5.992549236168133"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.859999999999999" x2="8.859999999999999" y1="14.65532912663279" y2="17.13040909533699"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.460000000000003" x2="7.460000000000003" y1="2.279929283111803" y2="4.755009251816007"/>
   <ellipse cx="7.5" cy="6.51" fill-opacity="0" rx="6" ry="6.06" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.7" cy="13.44" fill-opacity="0" rx="6" ry="6.06" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT789_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
  </symbol>
  <symbol id="Accessory:10kV母线PT带消谐装置_0" viewBox="0,0,35,30">
   <use terminal-index="0" type="0" x="17.56245852479325" xlink:href="#terminal" y="28.12373692455963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.61245852479333" x2="34.61245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.86245852479331" x2="30.86245852479331" y1="22.68299618381883" y2="19.84040359122624"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.6124585247933" x2="26.6124585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.61245852479332" x2="34.61245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.16666666666667" x2="31" y1="22.69225544307809" y2="22.69225544307809"/>
   <rect fill-opacity="0" height="3.55" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,30.81,15.38) scale(1,1) translate(0,0)" width="8.92" x="26.34" y="13.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.647721108666" x2="30.647721108666" y1="10.83674821859629" y2="7.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.71438777533266" x2="28.71438777533266" y1="7.51612768565195" y2="7.51612768565195"/>
   <ellipse cx="14.04" cy="22.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.05048569403981" x2="22.05048569403981" y1="34.53084700683308" y2="34.53084700683308"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.7608382776216" x2="28.48402243293775" y1="7.754208141873304" y2="7.754208141873304"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.01083827762157" x2="29.40068909960439" y1="6.50420814187332" y2="6.50420814187332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.51083827762159" x2="30.06735576627107" y1="5.004208141873296" y2="5.004208141873296"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.89410730945214" x2="23.0877735452272" y1="24.15299989806297" y2="21.53208583485582"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58713830216066" x2="22.81883560169719" y1="21.54040359122622" y2="21.54040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.51457106407813" x2="19.32090482830307" y1="24.05654513693459" y2="21.43563107372743"/>
   <ellipse cx="14.04" cy="15.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="21.04" cy="22.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.3644003886642" x2="22.3644003886642" y1="30.19213090500035" y2="30.19213090500035"/>
   <ellipse cx="21.04" cy="15.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.19220382559271" x2="17.19220382559271" y1="23.07076893362116" y2="23.07076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.4422038255927" x2="16.4422038255927" y1="16.82076893362115" y2="16.82076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.92264294445647" x2="20.87303257583197" y1="16.99992879670395" y2="15.47611741162254"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.873032575832" x2="20.37063985073817" y1="15.47611741162252" y2="12.82298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.87303257583199" x2="23.32581493230132" y1="15.47611741162254" y2="16.60543752773795"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.94220382559271" x2="16.94220382559271" y1="16.32076893362115" y2="16.32076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583198" x2="16.07581493230131" y1="22.72611741162255" y2="23.85543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583199" x2="13.12063985073816" y1="22.72611741162255" y2="20.07298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.67264294445646" x2="13.62303257583197" y1="24.24992879670398" y2="22.72611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583199" x2="16.07581493230131" y1="15.47611741162254" y2="16.60543752773796"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583198" x2="13.12063985073816" y1="15.47611741162253" y2="12.82298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.67264294445646" x2="13.62303257583196" y1="16.99992879670396" y2="15.47611741162255"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.59103363843511" x2="17.59103363843511" y1="25.15822158129307" y2="28.16666666666667"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Disconnector:带融断手车刀闸_0" viewBox="0,0,14,27">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="26.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.05" x2="7.05" y1="1.166666666666664" y2="26.33333333333334"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666668" x2="3.416666666666668" y1="0.8161359979676668" y2="3.698513845372194"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666666" x2="10.66666666666667" y1="0.8161359979676668" y2="3.698513845372194"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125000000000003" x2="3.750000000000002" y1="26.5417774506975" y2="23.30486537657277"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125" x2="10.5" y1="26.5417774506975" y2="23.30486537657277"/>
   <rect fill-opacity="0" height="6.25" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,8.63) scale(1,1) translate(0,0)" width="2.83" x="5.58" y="5.5"/>
  </symbol>
  <symbol id="Disconnector:带融断手车刀闸_1" viewBox="0,0,14,27">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="26.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.05" x2="7.05" y1="2.833333333333334" y2="14.41666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666668" x2="3.416666666666668" y1="0.8161359979676668" y2="3.698513845372194"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666668" x2="3.416666666666668" y1="2.617622152595477" y2="5.500000000000003"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="10.66666666666666" y1="2.617622152595477" y2="5.500000000000003"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666666" x2="10.66666666666667" y1="0.8161359979676668" y2="3.698513845372194"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125000000000003" x2="3.750000000000002" y1="24.65357874079139" y2="21.41666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125000000000003" x2="3.750000000000002" y1="26.5417774506975" y2="23.30486537657277"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125" x2="10.5" y1="24.65357874079139" y2="21.41666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125" x2="10.5" y1="26.5417774506975" y2="23.30486537657277"/>
   <rect fill-opacity="0" height="6.25" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,8.63) scale(1,1) translate(0,0)" width="2.83" x="5.58" y="5.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.25" x2="7.049999999999999" y1="16" y2="18.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.133333333333333" x2="7.133333333333333" y1="18.16666666666667" y2="24.33333333333334"/>
  </symbol>
  <symbol id="Disconnector:带融断手车刀闸_2" viewBox="0,0,14,27">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="26.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.33333333333333" x2="3.333333333333333" y1="9.166666666666668" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.666666666666667" x2="10.83333333333333" y1="9.124593892873616" y2="18.41666666666667"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="0.9166666666666661" y2="5.727011494252875"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="3.923132183908033" y2="8.733477011494239"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="3.923132183908033" y2="8.733477011494239"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666666" x2="12.5" y1="0.9166666666666661" y2="5.727011494252875"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="23.75933908045977" y2="18.94899425287357"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="26.56537356321841" y2="21.7550287356322"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="23.75933908045977" y2="18.94899425287357"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="26.56537356321841" y2="21.7550287356322"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id=":单相三绕组PT带避雷器_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="20" xlink:href="#terminal" y="39"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,11.79,20.83) scale(1,1) translate(0,0)" width="6.08" x="8.75" y="13.67"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.16666666666666" x2="31.16666666666666" y1="19.66666666666667" y2="19.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.25" x2="14.25" y1="30.91666666666666" y2="30.91666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.25" x2="13.25" y1="31.91666666666666" y2="31.91666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.25" x2="12.25" y1="32.91666666666666" y2="32.91666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="27.91666666666666" y2="30.91666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.5" x2="31.5" y1="27.25" y2="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="19.91022336769755" x2="19.91022336769755" y1="39.83333333333334" y2="0.9166666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="12.75" y1="21.66666666666667" y2="15.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="10.75" y1="21.66666666666667" y2="15.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="21.66666666666667" y2="8.999999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.33333333333333" x2="11.75" y1="9.008752830905545" y2="9.008752830905545"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.25545055364641" x2="28.25545055364641" y1="9.041388644475965" y2="15.16666666666666"/>
   <ellipse cx="28.43" cy="19.74" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="28.51" cy="27.08" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="35.07589728904158" x2="35.07589728904158" y1="8.997588122517026" y2="8.997588122517026"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.25000000000001" x2="38.25000000000001" y1="24.41666666666667" y2="24.41666666666667"/>
   <ellipse cx="34.78" cy="24.19" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV香柏河二级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="298" x="40.25" xlink:href="logo.png" y="42.75"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="173" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,189.25,72.75) scale(1,1) translate(0,0)" writing-mode="lr" x="189.25" xml:space="preserve" y="76.25" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,184.5,71.1903) scale(1,1) translate(0,0)" writing-mode="lr" x="184.5" xml:space="preserve" y="80.19" zvalue="3">110kV香柏河二级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="35" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" width="72.88" x="50.19" y="175.25" zvalue="281"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" writing-mode="lr" x="86.63" xml:space="preserve" y="191.75" zvalue="281">信号一览</text>
  <line fill="none" id="171" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="382.25" x2="382.25" y1="10.75" y2="1040.75" zvalue="4"/>
  <line fill="none" id="168" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.250000000000455" x2="375.25" y1="146.6204926140824" y2="146.6204926140824" zvalue="6"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,464.639,352) scale(1,1) translate(0,0)" writing-mode="lr" x="464.64" xml:space="preserve" y="356.5" zvalue="37">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1058.53,391.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1058.53" xml:space="preserve" y="395.72" zvalue="39">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1067.86,471.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1067.86" xml:space="preserve" y="475.72" zvalue="41">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1090.19,448.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1090.19" xml:space="preserve" y="452.72" zvalue="44">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1124.42,520.669) scale(1,1) translate(0,0)" writing-mode="lr" x="1124.42" xml:space="preserve" y="525.17" zvalue="47">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,508.875,644.25) scale(1,1) translate(0,0)" writing-mode="lr" x="508.88" xml:space="preserve" y="648.75" zvalue="57">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,748.306,416.056) scale(1,1) translate(0,0)" writing-mode="lr" x="748.3099999999999" xml:space="preserve" y="420.56" zvalue="59">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,758.333,473.694) scale(1,1) translate(0,0)" writing-mode="lr" x="758.33" xml:space="preserve" y="478.19" zvalue="61">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,765.833,394.75) scale(1,1) translate(0,0)" writing-mode="lr" x="765.83" xml:space="preserve" y="399.25" zvalue="63">10</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,737.875,567.625) scale(1,1) translate(1.4852e-13,0)" writing-mode="lr" x="737.87" xml:space="preserve" y="572.13" zvalue="69">110kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,616.31,735.365) scale(1,1) translate(0,0)" writing-mode="lr" x="616.3099999999999" xml:space="preserve" y="739.87" zvalue="72">021</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" x="593.8359375" xml:space="preserve" y="928.9462240134186" zvalue="76">#1发电机 </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="593.8359375" xml:space="preserve" y="944.9462240134186" zvalue="76">10.5MW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1322.12,899.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1322.13" xml:space="preserve" y="903.75" zvalue="117">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1435.53,583.743) scale(1,1) translate(0,0)" writing-mode="lr" x="1435.53" xml:space="preserve" y="588.24" zvalue="122">0901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1280.72,747.993) scale(1,1) translate(0,0)" writing-mode="lr" x="1280.72" xml:space="preserve" y="752.49" zvalue="130">0231</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1651.88,622.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1651.88" xml:space="preserve" y="627" zvalue="134">#2站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,614.583,271.167) scale(1,1) translate(0,0)" writing-mode="lr" x="614.58" xml:space="preserve" y="275.67" zvalue="153">121</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,605.806,318.722) scale(1,1) translate(0,0)" writing-mode="lr" x="605.8099999999999" xml:space="preserve" y="323.22" zvalue="154">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,607.472,210.722) scale(1,1) translate(0,0)" writing-mode="lr" x="607.47" xml:space="preserve" y="215.22" zvalue="157">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,583.104,98.5) scale(1,1) translate(0,0)" writing-mode="lr" x="583.1" xml:space="preserve" y="103" zvalue="161">110kV香柏河二级线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,642.306,306.167) scale(1,1) translate(0,0)" writing-mode="lr" x="642.3099999999999" xml:space="preserve" y="310.67" zvalue="164">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,642.306,257.389) scale(1,1) translate(0,0)" writing-mode="lr" x="642.3099999999999" xml:space="preserve" y="261.89" zvalue="166">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,642.306,202.944) scale(1,1) translate(0,0)" writing-mode="lr" x="642.3099999999999" xml:space="preserve" y="207.44" zvalue="168">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1335.58,271.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1335.58" xml:space="preserve" y="275.67" zvalue="178">122</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1326.81,318.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1326.81" xml:space="preserve" y="323.22" zvalue="179">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1328.47,210.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1328.47" xml:space="preserve" y="215.22" zvalue="182">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1310.18,100.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1310.18" xml:space="preserve" y="105" zvalue="186">110kV香柏河一二级线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1363.31,306.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1363.31" xml:space="preserve" y="310.67" zvalue="189">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1363.31,257.389) scale(1,1) translate(0,0)" writing-mode="lr" x="1363.31" xml:space="preserve" y="261.89" zvalue="191">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1363.31,202.944) scale(1,1) translate(0,0)" writing-mode="lr" x="1363.31" xml:space="preserve" y="207.44" zvalue="193">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="178" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1194.22,557) scale(1,1) translate(0,-1.21791e-13)" writing-mode="lr" x="1194.22" xml:space="preserve" y="561.5" zvalue="207">1010</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="182" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1400.65,453.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1400.65" xml:space="preserve" y="457.75" zvalue="210">10kV母线PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" glyph-orientation-vertical="0" id="188" letter-spacing="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1688.74,437.861) scale(1,1) translate(0,0)" writing-mode="tb" x="1688.74" xml:space="preserve" y="437.86" zvalue="215">引自支那乡</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,468.75,938.5) scale(1,1) translate(0,0)" writing-mode="lr" x="468.75" xml:space="preserve" y="943" zvalue="218">励磁变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,524,922.5) scale(1,1) translate(0,0)" writing-mode="lr" x="524" xml:space="preserve" y="927" zvalue="219">励磁PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,442.023,813.924) scale(1,1) translate(-9.4813e-14,0)" writing-mode="lr" x="442.02" xml:space="preserve" y="818.42" zvalue="221">0911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="197" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,637.273,821.924) scale(1,1) translate(0,0)" writing-mode="lr" x="637.27" xml:space="preserve" y="826.42" zvalue="226">0912</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,670.897,933.5) scale(1,1) translate(0,0)" writing-mode="lr" x="670.9" xml:space="preserve" y="938" zvalue="227">机组PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="108" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1072.31,735.365) scale(1,1) translate(0,0)" writing-mode="lr" x="1072.31" xml:space="preserve" y="739.87" zvalue="231">022</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="107" stroke="rgb(255,255,255)" text-anchor="middle" x="1048.8203125" xml:space="preserve" y="928.9462240134186" zvalue="235">#2发电机 </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="107" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1048.8203125" xml:space="preserve" y="944.9462240134186" zvalue="235">10.5MW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,924.75,938.5) scale(1,1) translate(0,0)" writing-mode="lr" x="924.75" xml:space="preserve" y="943" zvalue="238">励磁变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,977,930.5) scale(1,1) translate(0,0)" writing-mode="lr" x="977" xml:space="preserve" y="935" zvalue="239">励磁PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,895.511,817.924) scale(1,1) translate(9.76996e-14,0)" writing-mode="lr" x="895.51" xml:space="preserve" y="822.42" zvalue="241">0921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="196" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1092.77,821.924) scale(1,1) translate(0,0)" writing-mode="lr" x="1092.77" xml:space="preserve" y="826.42" zvalue="246">0922</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1127.97,929.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1127.97" xml:space="preserve" y="934" zvalue="248">机组PT</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="390.25" y2="413"/>
  <line fill="none" id="100" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="494.8704926140824" y2="494.8704926140824" zvalue="259"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="1001.0816" y2="1029"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,954) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="960" zvalue="261">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="91" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54,988) scale(1,1) translate(0,0)" writing-mode="lr" x="54" xml:space="preserve" y="994" zvalue="262">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,988) scale(1,1) translate(0,0)" writing-mode="lr" x="236" xml:space="preserve" y="994" zvalue="263">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="1022" zvalue="264">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="235" xml:space="preserve" y="1022" zvalue="265">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5,568.5) scale(1,1) translate(0,0)" writing-mode="lr" x="77.5" xml:space="preserve" y="573" zvalue="267">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="202.4" xml:space="preserve" y="192.34" zvalue="268">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="307.4" xml:space="preserve" y="192.34" zvalue="269">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,59.5,262) scale(1,1) translate(0,0)" writing-mode="lr" x="17" xml:space="preserve" y="266.5" zvalue="270">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240,262) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="266.5" zvalue="271">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.6875,335.25) scale(1,1) translate(0,0)" writing-mode="lr" x="62.69" xml:space="preserve" y="339.75" zvalue="272">110kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,238.125,335.25) scale(1,1) translate(0,0)" writing-mode="lr" x="238.13" xml:space="preserve" y="339.75" zvalue="273">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,288) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="292.5" zvalue="282">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="33" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239,288) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="292.5" zvalue="283">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,381.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="385.75" zvalue="286">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,380.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="384.75" zvalue="288">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,404.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="408.75" zvalue="290">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,403.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="407.75" zvalue="291">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="26" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,311) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="315.5" zvalue="293">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="25" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,238.5,310) scale(1,1) translate(0,0)" writing-mode="lr" x="196" xml:space="preserve" y="314.5" zvalue="295">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="140" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,191.5,954) scale(1,1) translate(0,0)" writing-mode="lr" x="191.5" xml:space="preserve" y="960" zvalue="347">XBH2-02-2009</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="50.19" y="175.25" zvalue="281"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="146">
   <path class="kv110" d="M 513.75 353 L 1563.75 353" stroke-width="6" zvalue="36"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674231779333" ObjectName="110kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674231779333"/></metadata>
  <path d="M 513.75 353 L 1563.75 353" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="kv10" d="M 475 667.75 L 1676.25 667.75" stroke-width="6" zvalue="56"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674231713796" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674231713796"/></metadata>
  <path d="M 475 667.75 L 1676.25 667.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="105">
   <path class="kv10" d="M 1647 417 L 1664 417" stroke-width="6" zvalue="333"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674237743108" ObjectName="10kV支那乡"/>
   <cge:TPSR_Ref TObjectID="9288674237743108"/></metadata>
  <path d="M 1647 417 L 1664 417" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="145">
   <use class="kv110" height="30" transform="rotate(0,1047.08,392.222) scale(1.11111,0.814815) translate(-103.875,86.3636)" width="15" x="1038.750010172526" xlink:href="#Disconnector:刀闸_0" y="380" zvalue="38"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449498251270" ObjectName="#1主变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449498251270"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1047.08,392.222) scale(1.11111,0.814815) translate(-103.875,86.3636)" width="15" x="1038.750010172526" y="380"/></g>
  <g id="125">
   <use class="kv110" height="30" transform="rotate(0,722.972,417.056) scale(-1.11111,-0.814815) translate(-1372.81,-931.674)" width="15" x="714.6388888888888" xlink:href="#Disconnector:刀闸_0" y="404.8333470026652" zvalue="58"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449498054662" ObjectName="110kV母线电压互感器1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449498054662"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,722.972,417.056) scale(-1.11111,-0.814815) translate(-1372.81,-931.674)" width="15" x="714.6388888888888" y="404.8333470026652"/></g>
  <g id="130">
   <use class="kv10" height="27" transform="rotate(0,1397.53,582.243) scale(2.10268,2.01903) translate(-725.169,-280.109)" width="14" x="1382.8125" xlink:href="#Disconnector:带融断手车刀闸_0" y="554.9861111111111" zvalue="121"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449497530374" ObjectName="10kV母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449497530374"/></metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,1397.53,582.243) scale(2.10268,2.01903) translate(-725.169,-280.109)" width="14" x="1382.8125" y="554.9861111111111"/></g>
  <g id="141">
   <use class="kv10" height="27" transform="rotate(0,1326.16,748.993) scale(2.10268,2.01903) translate(-687.739,-364.27)" width="14" x="1311.437105299286" xlink:href="#Disconnector:带融断手车刀闸_0" y="721.7361111111111" zvalue="129"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449497464838" ObjectName="#1站用变0231隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449497464838"/></metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,1326.16,748.993) scale(2.10268,2.01903) translate(-687.739,-364.27)" width="14" x="1311.437105299286" y="721.7361111111111"/></g>
  <g id="154">
   <use class="kv10" height="30" transform="rotate(0,1655.91,450.917) scale(2.34922,1.4055) translate(-930.793,-124.012)" width="30" x="1620.667549252023" xlink:href="#Disconnector:跌落刀闸_0" y="429.834974364315" zvalue="145"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449497333766" ObjectName="#2站用变刀闸"/>
   <cge:TPSR_Ref TObjectID="6192449497333766"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1655.91,450.917) scale(2.34922,1.4055) translate(-930.793,-124.012)" width="30" x="1620.667549252023" y="429.834974364315"/></g>
  <g id="272">
   <use class="kv110" height="30" transform="rotate(0,593.25,319.722) scale(-1.11111,-0.814815) translate(-1126.34,-714.886)" width="15" x="584.9166666666666" xlink:href="#Disconnector:刀闸_0" y="307.5000133514404" zvalue="152"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449497268230" ObjectName="110kV香柏河二级线1211隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449497268230"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,593.25,319.722) scale(-1.11111,-0.814815) translate(-1126.34,-714.886)" width="15" x="584.9166666666666" y="307.5000133514404"/></g>
  <g id="271">
   <use class="kv110" height="30" transform="rotate(0,593.25,211.722) scale(-1.11111,-0.814815) translate(-1126.34,-474.341)" width="15" x="584.9166666931577" xlink:href="#Disconnector:刀闸_0" y="199.4999997880724" zvalue="155"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449497202694" ObjectName="110kV香柏河二级线1216隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449497202694"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,593.25,211.722) scale(-1.11111,-0.814815) translate(-1126.34,-474.341)" width="15" x="584.9166666931577" y="199.4999997880724"/></g>
  <g id="219">
   <use class="kv110" height="30" transform="rotate(0,1314.25,319.722) scale(-1.11111,-0.814815) translate(-2496.24,-714.886)" width="15" x="1305.916666666667" xlink:href="#Disconnector:刀闸_0" y="307.5000133514404" zvalue="177"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449496547334" ObjectName="110kV香柏河一二级线1221隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449496547334"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1314.25,319.722) scale(-1.11111,-0.814815) translate(-2496.24,-714.886)" width="15" x="1305.916666666667" y="307.5000133514404"/></g>
  <g id="218">
   <use class="kv110" height="30" transform="rotate(0,1314.25,211.722) scale(-1.11111,-0.814815) translate(-2496.24,-474.341)" width="15" x="1305.916666693158" xlink:href="#Disconnector:刀闸_0" y="199.4999997880724" zvalue="180"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449496481798" ObjectName="110kV香柏河一二级线1226隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449496481798"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1314.25,211.722) scale(-1.11111,-0.814815) translate(-2496.24,-474.341)" width="15" x="1305.916666693158" y="199.4999997880724"/></g>
  <g id="43">
   <use class="kv10" height="27" transform="rotate(0,469.827,814.924) scale(1.32592,1.27318) translate(-113.205,-171.164)" width="14" x="460.545613714934" xlink:href="#Disconnector:带融断手车刀闸_0" y="797.7361111111111" zvalue="220"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449498775558" ObjectName="#1发电机励磁变0911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449498775558"/></metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,469.827,814.924) scale(1.32592,1.27318) translate(-113.205,-171.164)" width="14" x="460.545613714934" y="797.7361111111111"/></g>
  <g id="70">
   <use class="kv10" height="27" transform="rotate(0,674.827,822.924) scale(1.32592,1.27318) translate(-163.596,-172.88)" width="14" x="665.545613714934" xlink:href="#Disconnector:带融断手车刀闸_0" y="805.7361111111111" zvalue="225"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449498841094" ObjectName="#1发电机机组PT0912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449498841094"/></metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,674.827,822.924) scale(1.32592,1.27318) translate(-163.596,-172.88)" width="14" x="665.545613714934" y="805.7361111111111"/></g>
  <g id="174">
   <use class="kv10" height="27" transform="rotate(0,925.827,818.924) scale(1.32592,1.27318) translate(-225.294,-172.022)" width="14" x="916.545613714934" xlink:href="#Disconnector:带融断手车刀闸_0" y="801.7361111111111" zvalue="240"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449499103238" ObjectName="#2发电机励磁变0921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449499103238"/></metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,925.827,818.924) scale(1.32592,1.27318) translate(-225.294,-172.022)" width="14" x="916.545613714934" y="801.7361111111111"/></g>
  <g id="137">
   <use class="kv10" height="27" transform="rotate(0,1130.83,822.924) scale(1.32592,1.27318) translate(-275.684,-172.88)" width="14" x="1121.545613714934" xlink:href="#Disconnector:带融断手车刀闸_0" y="805.7361111111111" zvalue="245"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449499037702" ObjectName="#2发电机机组PT0922隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449499037702"/></metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,1130.83,822.924) scale(1.32592,1.27318) translate(-275.684,-172.88)" width="14" x="1121.545613714934" y="805.7361111111111"/></g>
 </g>
 <g id="BreakerClass">
  <g id="144">
   <use class="kv110" height="20" transform="rotate(0,1047.08,472.222) scale(1.22222,1.11111) translate(-189.268,-46.1111)" width="10" x="1040.972232407994" xlink:href="#Breaker:开关_0" y="461.1111111111111" zvalue="40"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924465917957" ObjectName="#1主变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473924465917957"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1047.08,472.222) scale(1.22222,1.11111) translate(-189.268,-46.1111)" width="10" x="1040.972232407994" y="461.1111111111111"/></g>
  <g id="341">
   <use class="kv10" height="20" transform="rotate(0,589.297,736.446) scale(2.16108,2.16108) translate(-310.806,-384.059)" width="10" x="578.4912202553933" xlink:href="#Breaker:小车断路器_0" y="714.834974364315" zvalue="71"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924465852421" ObjectName="#1发电机021断路器"/>
   <cge:TPSR_Ref TObjectID="6473924465852421"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,589.297,736.446) scale(2.16108,2.16108) translate(-310.806,-384.059)" width="10" x="578.4912202553933" y="714.834974364315"/></g>
  <g id="273">
   <use class="kv110" height="20" transform="rotate(0,593.25,272.167) scale(1.22222,1.11111) translate(-106.753,-26.1056)" width="10" x="587.1388888888889" xlink:href="#Breaker:开关_0" y="261.0555553436279" zvalue="151"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924465786885" ObjectName="110kV香柏河二级线121断路器"/>
   <cge:TPSR_Ref TObjectID="6473924465786885"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,593.25,272.167) scale(1.22222,1.11111) translate(-106.753,-26.1056)" width="10" x="587.1388888888889" y="261.0555553436279"/></g>
  <g id="220">
   <use class="kv110" height="20" transform="rotate(0,1314.25,272.167) scale(1.22222,1.11111) translate(-237.843,-26.1056)" width="10" x="1308.138888888889" xlink:href="#Breaker:开关_0" y="261.0555553436279" zvalue="176"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924465721349" ObjectName="110kV香柏河一二级线122断路器"/>
   <cge:TPSR_Ref TObjectID="6473924465721349"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1314.25,272.167) scale(1.22222,1.11111) translate(-237.843,-26.1056)" width="10" x="1308.138888888889" y="261.0555553436279"/></g>
  <g id="195">
   <use class="kv10" height="20" transform="rotate(0,1045.3,736.446) scale(2.16108,2.16108) translate(-555.8,-384.059)" width="10" x="1034.491220255393" xlink:href="#Breaker:小车断路器_0" y="714.834974364315" zvalue="230"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924465983493" ObjectName="#2发电机022断路器"/>
   <cge:TPSR_Ref TObjectID="6473924465983493"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1045.3,736.446) scale(2.16108,2.16108) translate(-555.8,-384.059)" width="10" x="1034.491220255393" y="714.834974364315"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="114">
   <path class="kv110" d="M 1047.15 404.24 L 1047.15 461.59" stroke-width="1" zvalue="42"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@1" LinkObjectIDznd="144@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1047.15 404.24 L 1047.15 461.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="kv110" d="M 1047.18 380.4 L 1047.18 353" stroke-width="1" zvalue="45"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@0" LinkObjectIDznd="146@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1047.18 380.4 L 1047.18 353" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="111">
   <path class="kv110" d="M 1047.16 482.83 L 1047.16 512.69" stroke-width="1" zvalue="48"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="144@1" LinkObjectIDznd="138@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1047.16 482.83 L 1047.16 512.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="kv110" d="M 1079.58 431.99 L 1047.15 431.99" stroke-width="1" zvalue="49"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="142@0" LinkObjectIDznd="114" MaxPinNum="2"/>
   </metadata>
  <path d="M 1079.58 431.99 L 1047.15 431.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="106">
   <path class="kv110" d="M 722.9 405.04 L 722.9 353" stroke-width="1" zvalue="64"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@1" LinkObjectIDznd="146@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 722.9 405.04 L 722.9 353" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="103">
   <path class="kv110" d="M 755.22 382.29 L 722.9 382.29" stroke-width="1" zvalue="65"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="123@0" LinkObjectIDznd="106" MaxPinNum="2"/>
   </metadata>
  <path d="M 755.22 382.29 L 722.9 382.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv110" d="M 747.72 457.46 L 722.87 457.53 L 722.87 428.87" stroke-width="1" zvalue="66"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="124@0" LinkObjectIDznd="125@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 747.72 457.46 L 722.87 457.53 L 722.87 428.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="99">
   <path class="kv110" d="M 722.87 496.99 L 722.87 450.36" stroke-width="1" zvalue="67"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="115@0" LinkObjectIDznd="101" MaxPinNum="2"/>
   </metadata>
  <path d="M 722.87 496.99 L 722.87 450.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="kv10" d="M 589.3 716.46 L 589.3 667.75" stroke-width="1" zvalue="73"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="341@0" LinkObjectIDznd="127@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 589.3 716.46 L 589.3 667.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv10" d="M 589.05 846.72 L 589.05 755.9" stroke-width="1" zvalue="74"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="850@0" LinkObjectIDznd="341@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 589.05 846.72 L 589.05 755.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="81">
   <path class="kv10" d="M 1398.43 514.4 L 1398.43 556.5" stroke-width="1" zvalue="126"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="180@0" LinkObjectIDznd="130@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1398.43 514.4 L 1398.43 556.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="kv10" d="M 1326.16 830.64 L 1326.16 775.24" stroke-width="1" zvalue="131"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="205@0" LinkObjectIDznd="141@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1326.16 830.64 L 1326.16 775.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv10" d="M 1326.16 723.25 L 1326.16 667.75" stroke-width="1" zvalue="132"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="141@0" LinkObjectIDznd="127@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1326.16 723.25 L 1326.16 667.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="kv10" d="M 1397.53 608.49 L 1397.53 667.75" stroke-width="1" zvalue="144"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="130@1" LinkObjectIDznd="127@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1397.53 608.49 L 1397.53 667.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="67">
   <path class="kv110" d="M 593.15 331.54 L 593.15 353" stroke-width="1" zvalue="156"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@0" LinkObjectIDznd="146@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 593.15 331.54 L 593.15 353" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv110" d="M 593.18 307.71 L 593.18 282.78" stroke-width="1" zvalue="158"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@1" LinkObjectIDznd="273@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 593.18 307.71 L 593.18 282.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="kv110" d="M 593.21 261.54 L 593.15 223.54" stroke-width="1" zvalue="159"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="273@0" LinkObjectIDznd="271@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 593.21 261.54 L 593.15 223.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="kv110" d="M 593.18 199.71 L 593.18 158.39" stroke-width="1" zvalue="162"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="271@1" LinkObjectIDznd="267@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 593.18 199.71 L 593.18 158.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="kv110" d="M 630.25 188.38 L 593.18 188.38" stroke-width="1" zvalue="169"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="263@0" LinkObjectIDznd="64" MaxPinNum="2"/>
   </metadata>
  <path d="M 630.25 188.38 L 593.18 188.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="kv110" d="M 630.25 242.82 L 593.18 242.82" stroke-width="1" zvalue="170"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="264@0" LinkObjectIDznd="65" MaxPinNum="2"/>
   </metadata>
  <path d="M 630.25 242.82 L 593.18 242.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="kv110" d="M 630.25 291.6 L 593.18 291.6" stroke-width="1" zvalue="171"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="265@0" LinkObjectIDznd="66" MaxPinNum="2"/>
   </metadata>
  <path d="M 630.25 291.6 L 593.18 291.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv110" d="M 1314.15 331.54 L 1314.15 353" stroke-width="1" zvalue="181"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="219@0" LinkObjectIDznd="146@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1314.15 331.54 L 1314.15 353" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="kv110" d="M 1314.18 307.71 L 1314.18 282.78" stroke-width="1" zvalue="183"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="219@1" LinkObjectIDznd="220@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1314.18 307.71 L 1314.18 282.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="kv110" d="M 1314.21 261.54 L 1314.15 223.54" stroke-width="1" zvalue="184"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="220@0" LinkObjectIDznd="218@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1314.21 261.54 L 1314.15 223.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="55">
   <path class="kv110" d="M 1314.18 199.71 L 1314.18 158.39" stroke-width="1" zvalue="187"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="218@1" LinkObjectIDznd="214@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1314.18 199.71 L 1314.18 158.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="54">
   <path class="kv110" d="M 1351.25 188.38 L 1314.18 188.38" stroke-width="1" zvalue="194"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="210@0" LinkObjectIDznd="55" MaxPinNum="2"/>
   </metadata>
  <path d="M 1351.25 188.38 L 1314.18 188.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="53">
   <path class="kv110" d="M 1351.25 242.82 L 1314.18 242.82" stroke-width="1" zvalue="195"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="211@0" LinkObjectIDznd="56" MaxPinNum="2"/>
   </metadata>
  <path d="M 1351.25 242.82 L 1314.18 242.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="kv110" d="M 1351.25 291.6 L 1314.18 291.6" stroke-width="1" zvalue="196"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="212@0" LinkObjectIDznd="57" MaxPinNum="2"/>
   </metadata>
  <path d="M 1351.25 291.6 L 1314.18 291.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="176">
   <path class="kv10" d="M 1047.11 587.46 L 1047.11 667.75" stroke-width="1" zvalue="205"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="138@1" LinkObjectIDznd="127@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1047.11 587.46 L 1047.11 667.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="186">
   <path class="kv10" d="M 1655.91 553.89 L 1655.91 470.59" stroke-width="1" zvalue="212"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="153@0" LinkObjectIDznd="154@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1655.91 553.89 L 1655.91 470.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="187">
   <path class="kv10" d="M 1655.11 417 L 1655.11 381.43" stroke-width="1" zvalue="213"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="105@0" LinkObjectIDznd="183@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1655.11 417 L 1655.11 381.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="kv10" d="M 469.83 798.69 L 469.81 777 L 589.05 777" stroke-width="1" zvalue="222"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="43@0" LinkObjectIDznd="96" MaxPinNum="2"/>
   </metadata>
  <path d="M 469.83 798.69 L 469.81 777 L 589.05 777" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="kv10" d="M 674.83 864.78 L 674.83 839.48" stroke-width="1" zvalue="227"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="71@0" LinkObjectIDznd="70@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 674.83 864.78 L 674.83 839.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="kv10" d="M 674.83 806.69 L 674.81 783 L 589.05 783" stroke-width="1" zvalue="228"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70@0" LinkObjectIDznd="96" MaxPinNum="2"/>
   </metadata>
  <path d="M 674.83 806.69 L 674.81 783 L 589.05 783" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="194">
   <path class="kv10" d="M 1045.3 716.46 L 1045.3 667.75" stroke-width="1" zvalue="232"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="195@0" LinkObjectIDznd="176" MaxPinNum="2"/>
   </metadata>
  <path d="M 1045.3 716.46 L 1045.3 667.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="192">
   <path class="kv10" d="M 1044.05 846.72 L 1044.05 755.9" stroke-width="1" zvalue="233"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@0" LinkObjectIDznd="195@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1044.05 846.72 L 1044.05 755.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="169">
   <path class="kv10" d="M 925.86 887.75 L 925.83 835.48" stroke-width="1" zvalue="242"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="184@0" LinkObjectIDznd="174@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 925.86 887.75 L 925.83 835.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="152">
   <path class="kv10" d="M 925.83 802.37 L 925.83 777 L 1044.05 777" stroke-width="1" zvalue="243"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="174@0" LinkObjectIDznd="192" MaxPinNum="2"/>
   </metadata>
  <path d="M 925.83 802.37 L 925.83 777 L 1044.05 777" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="126">
   <path class="kv10" d="M 1130.83 861.04 L 1130.83 839.48" stroke-width="1" zvalue="247"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="133@0" LinkObjectIDznd="137@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1130.83 861.04 L 1130.83 839.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv10" d="M 1130.83 806.69 L 1130.81 783 L 1044.05 783" stroke-width="1" zvalue="249"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="137@0" LinkObjectIDznd="192" MaxPinNum="2"/>
   </metadata>
  <path d="M 1130.83 806.69 L 1130.81 783 L 1044.05 783" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="217">
   <path class="kv10" d="M 974.44 878.46 L 974.44 855.5 L 925.84 855.5" stroke-width="1" zvalue="321"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="181@0" LinkObjectIDznd="169" MaxPinNum="2"/>
   </metadata>
  <path d="M 974.44 878.46 L 974.44 855.5 L 925.84 855.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="118">
   <path class="kv10" d="M 1655.91 429.83 L 1655.91 417" stroke-width="1" zvalue="334"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="154@0" LinkObjectIDznd="105@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1655.91 429.83 L 1655.91 417" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="119">
   <path class="kv10" d="M 1655.91 417 L 1655.91 429.83" stroke-width="1" zvalue="335"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="105@1" LinkObjectIDznd="118" MaxPinNum="2"/>
   </metadata>
  <path d="M 1655.91 417 L 1655.91 429.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="kv110" d="M 1139.56 544.44 L 1139.56 534.24 L 1047.14 534.24" stroke-width="1" zvalue="336"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="177@0" LinkObjectIDznd="138@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1139.56 544.44 L 1139.56 534.24 L 1047.14 534.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="kv10" d="M 469.83 835.48 L 469.83 887.75" stroke-width="1" zvalue="337"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="43@1" LinkObjectIDznd="16@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 469.83 835.48 L 469.83 887.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="104">
   <path class="kv10" d="M 521.44 870.46 L 469.83 870.46" stroke-width="1" zvalue="339"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="17@0" LinkObjectIDznd="69" MaxPinNum="2"/>
   </metadata>
  <path d="M 521.44 870.46 L 469.83 870.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="142">
   <use class="kv110" height="30" transform="rotate(270,1090.19,432) scale(-0.925926,0.740741) translate(-2268.05,147.311)" width="12" x="1084.638899061415" xlink:href="#GroundDisconnector:地刀12_0" y="420.8888888888889" zvalue="43"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449498185734" ObjectName="#1主变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449498185734"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1090.19,432) scale(-0.925926,0.740741) translate(-2268.05,147.311)" width="12" x="1084.638899061415" y="420.8888888888889"/></g>
  <g id="124">
   <use class="kv110" height="30" transform="rotate(270,758.333,457.472) scale(-0.925926,0.740741) translate(-1577.78,156.226)" width="12" x="752.7777845594618" xlink:href="#GroundDisconnector:地刀12_0" y="446.3611008326212" zvalue="60"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449497989126" ObjectName="110kV母线电压互感器19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449497989126"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,758.333,457.472) scale(-0.925926,0.740741) translate(-1577.78,156.226)" width="12" x="752.7777845594618" y="446.3611008326212"/></g>
  <g id="123">
   <use class="kv110" height="30" transform="rotate(270,765.833,382.306) scale(-0.925926,0.740741) translate(-1593.38,129.918)" width="12" x="760.2777845594618" xlink:href="#GroundDisconnector:地刀12_0" y="371.1944444444444" zvalue="62"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449497858054" ObjectName="110kV母线电压互感器19010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449497858054"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,765.833,382.306) scale(-0.925926,0.740741) translate(-1593.38,129.918)" width="12" x="760.2777845594618" y="371.1944444444444"/></g>
  <g id="265">
   <use class="kv110" height="30" transform="rotate(270,640.861,291.611) scale(-0.925926,0.740741) translate(-1333.44,98.175)" width="12" x="635.3055555555555" xlink:href="#GroundDisconnector:地刀12_0" y="280.5000050862629" zvalue="163"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449497071622" ObjectName="110kV香柏河二级线12117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449497071622"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,640.861,291.611) scale(-0.925926,0.740741) translate(-1333.44,98.175)" width="12" x="635.3055555555555" y="280.5000050862629"/></g>
  <g id="264">
   <use class="kv110" height="30" transform="rotate(270,640.861,242.833) scale(-0.925926,0.740741) translate(-1333.44,81.1028)" width="12" x="635.3055555555557" xlink:href="#GroundDisconnector:地刀12_0" y="231.7222220102947" zvalue="165"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449496940550" ObjectName="110kV香柏河二级线12160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449496940550"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,640.861,242.833) scale(-0.925926,0.740741) translate(-1333.44,81.1028)" width="12" x="635.3055555555557" y="231.7222220102947"/></g>
  <g id="263">
   <use class="kv110" height="30" transform="rotate(270,640.861,188.389) scale(-0.925926,0.740741) translate(-1333.44,62.0472)" width="12" x="635.3055556615193" xlink:href="#GroundDisconnector:地刀12_0" y="177.2777775658501" zvalue="167"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449496809478" ObjectName="110kV香柏河二级线12167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449496809478"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,640.861,188.389) scale(-0.925926,0.740741) translate(-1333.44,62.0472)" width="12" x="635.3055556615193" y="177.2777775658501"/></g>
  <g id="212">
   <use class="kv110" height="30" transform="rotate(270,1361.86,291.611) scale(-0.925926,0.740741) translate(-2833.12,98.175)" width="12" x="1356.305555555556" xlink:href="#GroundDisconnector:地刀12_0" y="280.5000050862629" zvalue="188"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449496350726" ObjectName="110kV香柏河一二级线12217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449496350726"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1361.86,291.611) scale(-0.925926,0.740741) translate(-2833.12,98.175)" width="12" x="1356.305555555556" y="280.5000050862629"/></g>
  <g id="211">
   <use class="kv110" height="30" transform="rotate(270,1361.86,242.833) scale(-0.925926,0.740741) translate(-2833.12,81.1028)" width="12" x="1356.305555555556" xlink:href="#GroundDisconnector:地刀12_0" y="231.7222220102947" zvalue="190"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449496219654" ObjectName="110kV香柏河一二级线12260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449496219654"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1361.86,242.833) scale(-0.925926,0.740741) translate(-2833.12,81.1028)" width="12" x="1356.305555555556" y="231.7222220102947"/></g>
  <g id="210">
   <use class="kv110" height="30" transform="rotate(270,1361.86,188.389) scale(-0.925926,0.740741) translate(-2833.12,62.0472)" width="12" x="1356.305555661519" xlink:href="#GroundDisconnector:地刀12_0" y="177.2777775658501" zvalue="192"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449496088582" ObjectName="110kV香柏河一二级线12267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449496088582"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1361.86,188.389) scale(-0.925926,0.740741) translate(-2833.12,62.0472)" width="12" x="1356.305555661519" y="177.2777775658501"/></g>
  <g id="177">
   <use class="kv110" height="40" transform="rotate(0,1154.44,558) scale(1.11111,-1.11111) translate(-113.222,-1057.98)" width="40" x="1132.222222222222" xlink:href="#GroundDisconnector:中性点地刀12_0" y="535.7777777777777" zvalue="206"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449498447878" ObjectName="#1主变110kV侧1010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449498447878"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1154.44,558) scale(1.11111,-1.11111) translate(-113.222,-1057.98)" width="40" x="1132.222222222222" y="535.7777777777777"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="138">
   <g id="1380">
    <use class="kv110" height="50" transform="rotate(0,1047.11,550) scale(1.66667,1.52) translate(-408.844,-175.158)" width="30" x="1022.11" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="512" zvalue="46"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874418028548" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1381">
    <use class="kv10" height="50" transform="rotate(0,1047.11,550) scale(1.66667,1.52) translate(-408.844,-175.158)" width="30" x="1022.11" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="512" zvalue="46"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874418094084" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399441186820" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399441186820"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1047.11,550) scale(1.66667,1.52) translate(-408.844,-175.158)" width="30" x="1022.11" y="512"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="115">
   <use class="kv110" height="18" transform="rotate(0,722.875,521.25) scale(2.77778,2.77778) translate(-446.64,-317.6)" width="18" x="697.8746921657103" xlink:href="#Accessory:四卷PT_0" y="496.25" zvalue="68"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449497726982" ObjectName="110kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,722.875,521.25) scale(2.77778,2.77778) translate(-446.64,-317.6)" width="18" x="697.8746921657103" y="496.25"/></g>
  <g id="180">
   <use class="kv10" height="18" transform="rotate(0,1398.43,490.139) scale(2.77778,-2.77778) translate(-878.995,-650.589)" width="18" x="1373.430247721266" xlink:href="#Accessory:四卷PT_0" y="465.1388888888889" zvalue="209"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449498513414" ObjectName="10kV母线PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1398.43,490.139) scale(2.77778,-2.77778) translate(-878.995,-650.589)" width="18" x="1373.430247721266" y="465.1388888888889"/></g>
  <g id="183">
   <use class="kv10" height="40" transform="rotate(0,1655.11,360.222) scale(-1.11111,-1.11111) translate(-3142.49,-682.2)" width="40" x="1632.888888888889" xlink:href="#Accessory:线路PT11带避雷器_0" y="338" zvalue="210"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449498578950" ObjectName="#2站用变避雷器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1655.11,360.222) scale(-1.11111,-1.11111) translate(-3142.49,-682.2)" width="40" x="1632.888888888889" y="338"/></g>
  <g id="16">
   <use class="kv10" height="20" transform="rotate(0,469.75,904.667) scale(1.76667,1.76667) translate(-198.104,-384.925)" width="15" x="456.5" xlink:href="#Accessory:PT6_0" y="887" zvalue="217"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449498644486" ObjectName="#1发电机励磁变"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,469.75,904.667) scale(1.76667,1.76667) translate(-198.104,-384.925)" width="15" x="456.5" y="887"/></g>
  <g id="17">
   <use class="kv10" height="30" transform="rotate(0,521.5,889.5) scale(1.3,1.3) translate(-115.846,-200.769)" width="30" x="502" xlink:href="#Accessory:PT789_0" y="870" zvalue="218"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449498710022" ObjectName="#1发电机励磁PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,521.5,889.5) scale(1.3,1.3) translate(-115.846,-200.769)" width="30" x="502" y="870"/></g>
  <g id="71">
   <use class="kv10" height="30" transform="rotate(0,674.77,884.222) scale(0.907032,-1.48148) translate(67.5353,-1473.85)" width="35" x="658.8973568944591" xlink:href="#Accessory:10kV母线PT带消谐装置_0" y="862" zvalue="226"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449498906630" ObjectName="#1发电机机组PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,674.77,884.222) scale(0.907032,-1.48148) translate(67.5353,-1473.85)" width="35" x="658.8973568944591" y="862"/></g>
  <g id="184">
   <use class="kv10" height="20" transform="rotate(0,925.75,904.667) scale(1.76667,1.76667) translate(-395.991,-384.925)" width="15" x="912.5" xlink:href="#Accessory:PT6_0" y="887" zvalue="236"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449499234310" ObjectName="#2发电机励磁变"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,925.75,904.667) scale(1.76667,1.76667) translate(-395.991,-384.925)" width="15" x="912.5" y="887"/></g>
  <g id="181">
   <use class="kv10" height="30" transform="rotate(0,974.5,897.5) scale(1.3,1.3) translate(-220.385,-202.615)" width="30" x="955" xlink:href="#Accessory:PT789_0" y="878" zvalue="237"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449499168774" ObjectName="#2发电机励磁PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,974.5,897.5) scale(1.3,1.3) translate(-220.385,-202.615)" width="30" x="955" y="878"/></g>
  <g id="133">
   <use class="kv10" height="30" transform="rotate(0,1130.77,882.322) scale(0.845943,-1.62148) translate(203.233,-1417.14)" width="35" x="1115.970226381585" xlink:href="#Accessory:10kV母线PT带消谐装置_0" y="858" zvalue="246"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449498972166" ObjectName="#2发电机机组PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1130.77,882.322) scale(0.845943,-1.62148) translate(203.233,-1417.14)" width="35" x="1115.970226381585" y="858"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="kv10" height="30" transform="rotate(0,589.047,874.136) scale(1.85899,1.85899) translate(-259.298,-391.03)" width="30" x="561.1617897505679" xlink:href="#Generator:发电机_0" y="846.2510218856319" zvalue="75"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449497661446" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449497661446"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,589.047,874.136) scale(1.85899,1.85899) translate(-259.298,-391.03)" width="30" x="561.1617897505679" y="846.2510218856319"/></g>
  <g id="191">
   <use class="kv10" height="30" transform="rotate(0,1044.05,874.136) scale(1.85899,1.85899) translate(-469.541,-391.03)" width="30" x="1016.161789750568" xlink:href="#Generator:发电机_0" y="846.2510218856319" zvalue="234"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449499299846" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449499299846"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1044.05,874.136) scale(1.85899,1.85899) translate(-469.541,-391.03)" width="30" x="1016.161789750568" y="846.2510218856319"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="205">
   <use class="kv10" height="30" transform="rotate(0,1326,855.25) scale(1.69643,1.70833) translate(-534.608,-343.991)" width="28" x="1302.25" xlink:href="#EnergyConsumer:站用变DY接地_0" y="829.625" zvalue="116"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449497595910" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1326,855.25) scale(1.69643,1.70833) translate(-534.608,-343.991)" width="28" x="1302.25" y="829.625"/></g>
  <g id="153">
   <use class="kv10" height="30" transform="rotate(0,1655.75,578.5) scale(1.69643,1.70833) translate(-669.979,-229.241)" width="28" x="1632" xlink:href="#EnergyConsumer:站用变DY接地_0" y="552.875" zvalue="133"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449497399302" ObjectName="#2站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1655.75,578.5) scale(1.69643,1.70833) translate(-669.979,-229.241)" width="28" x="1632" y="552.875"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="243">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="243" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,334.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="339.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123776757764" ObjectName="F"/>
   </metadata>
  </g>
  <g id="244">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="244" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,262.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="267.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123788488708" ObjectName="F"/>
   </metadata>
  </g>
  <g id="245">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="245" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,263.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="268.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123788554244" ObjectName="F"/>
   </metadata>
  </g>
  <g id="246">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="246" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,337.222,335.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="340.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123773677572" ObjectName="F"/>
   </metadata>
  </g>
  <g id="256">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="256" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,287.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="292.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123788357636" ObjectName="F"/>
   </metadata>
  </g>
  <g id="257">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="257" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,288.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="293.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123788423172" ObjectName="F"/>
   </metadata>
  </g>
  <g id="234">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="234" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,159.611,311.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="316.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124602511364" ObjectName="装机率用率"/>
   </metadata>
  </g>
  <g id="240">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="240" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,339.611,310.167) scale(1,1) translate(0,0)" writing-mode="lr" x="339.77" xml:space="preserve" y="315.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124602445828" ObjectName="厂用电率"/>
   </metadata>
  </g>
  <g id="113">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="113" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,573.041,28.2259) scale(1,1) translate(0,2.7589e-14)" writing-mode="lr" x="572.5700000000001" xml:space="preserve" y="32.89" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123769352196" ObjectName="P"/>
   </metadata>
  </g>
  <g id="116">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="116" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,573.041,50.375) scale(1,1) translate(0,6.20156e-14)" writing-mode="lr" x="572.5700000000001" xml:space="preserve" y="55.04" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123769417732" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="117">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="117" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,573.041,72.5241) scale(1,1) translate(0,9.64422e-14)" writing-mode="lr" x="572.5700000000001" xml:space="preserve" y="77.19" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123769483268" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="120">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="120" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1312.93,24.8964) scale(1,1) translate(0,4.43164e-15)" writing-mode="lr" x="1312.46" xml:space="preserve" y="29.58" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123767779332" ObjectName="P"/>
   </metadata>
  </g>
  <g id="129">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="129" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1312.93,49.4028) scale(1,1) translate(0,-8.39593e-14)" writing-mode="lr" x="1312.46" xml:space="preserve" y="54.09" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123767844868" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="131">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="131" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1312.93,73.9091) scale(1,1) translate(0,-1.38374e-13)" writing-mode="lr" x="1312.46" xml:space="preserve" y="78.59" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123767910404" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="135">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="135" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,973.36,431.282) scale(1,1) translate(0,4.65183e-14)" writing-mode="lr" x="972.8099999999999" xml:space="preserve" y="435.98" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123773808644" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="185">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="185" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,973.36,457.25) scale(1,1) translate(0,4.94014e-14)" writing-mode="lr" x="972.8099999999999" xml:space="preserve" y="461.94" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123773874180" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="189">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="189" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,973.36,483.218) scale(1,1) translate(0,5.22844e-14)" writing-mode="lr" x="972.8099999999999" xml:space="preserve" y="487.91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123774070788" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="198">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="198" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,589.047,967.951) scale(1,1) translate(0,1.06056e-12)" writing-mode="lr" x="588.49" xml:space="preserve" y="972.65" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123772104708" ObjectName="P"/>
   </metadata>
  </g>
  <g id="204">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="204" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1044.05,967.951) scale(1,1) translate(0,1.06056e-12)" writing-mode="lr" x="1043.49" xml:space="preserve" y="972.65" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123776888836" ObjectName="P"/>
   </metadata>
  </g>
  <g id="206">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="206" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,589.047,994.76) scale(1,1) translate(0,1.09033e-12)" writing-mode="lr" x="588.49" xml:space="preserve" y="999.46" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123772170244" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="207">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="207" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1044.05,994.76) scale(1,1) translate(0,1.09033e-12)" writing-mode="lr" x="1043.49" xml:space="preserve" y="999.46" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123776954372" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="208">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="208" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,589.047,1021.57) scale(1,1) translate(0,1.12009e-12)" writing-mode="lr" x="588.49" xml:space="preserve" y="1026.27" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123772235780" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="209">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="209" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1044.05,1021.57) scale(1,1) translate(0,1.12009e-12)" writing-mode="lr" x="1043.49" xml:space="preserve" y="1026.27" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123777019908" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="15">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="15" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,377.917) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="382.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124568301572" ObjectName="F"/>
   </metadata>
  </g>
  <g id="29">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="29" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,335.611,377.917) scale(1,1) translate(0,0)" writing-mode="lr" x="335.77" xml:space="preserve" y="382.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124568367108" ObjectName="F"/>
   </metadata>
  </g>
  <g id="31">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="31" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,335.611,401.917) scale(1,1) translate(0,2.60736e-13)" writing-mode="lr" x="335.77" xml:space="preserve" y="406.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124568563716" ObjectName="F"/>
   </metadata>
  </g>
  <g id="143">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="143" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,506,684.75) scale(1,1) translate(0,0)" writing-mode="lr" x="505.53" xml:space="preserve" y="689.53" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123773546500" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="147">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="147" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,525.75,387) scale(1,1) translate(0,0)" writing-mode="lr" x="525.28" xml:space="preserve" y="391.78" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123776626692" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="149">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="149" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1492,473.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1491.53" xml:space="preserve" y="478.03" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123773284356" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="150">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="150" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,646.75,467.5) scale(1,1) translate(0,0)" writing-mode="lr" x="646.28" xml:space="preserve" y="472.28" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123776364548" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="155">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="155" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1492,498.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1491.53" xml:space="preserve" y="503.03" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123773349892" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="156">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="156" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,646.75,496.5) scale(1,1) translate(0,0)" writing-mode="lr" x="646.28" xml:space="preserve" y="501.28" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123776430084" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="158">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="158" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1491,519.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1490.53" xml:space="preserve" y="524.03" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123773415428" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="159">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="159" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,645.75,521.5) scale(1,1) translate(0,0)" writing-mode="lr" x="645.28" xml:space="preserve" y="526.28" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123776495620" ObjectName="Uc"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="50">
   <use height="30" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="279"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374883684356" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" y="178.37"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="280"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562950264127496" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" y="178.37"/></g>
 </g>
</svg>