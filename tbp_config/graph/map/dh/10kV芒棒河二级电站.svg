<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549587410946" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV芒棒河二级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="269.25" x="42" xlink:href="logo.png" y="39.5"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,176.625,69.5) scale(1,1) translate(0,0)" writing-mode="lr" x="176.62" xml:space="preserve" y="73" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,178.333,69.1903) scale(1,1) translate(6.66134e-15,0)" writing-mode="lr" x="178.33" xml:space="preserve" y="78.19" zvalue="3">10kV芒棒河二级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="1" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,71.4375,339) scale(1,1) translate(0,0)" width="72.88" x="35" y="327" zvalue="64"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,71.4375,339) scale(1,1) translate(0,0)" writing-mode="lr" x="71.44" xml:space="preserve" y="343.5" zvalue="64">信号一览</text>
  <line fill="none" id="33" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="376" x2="376" y1="7.5" y2="1037.5" zvalue="4"/>
  <line fill="none" id="31" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.000000000000227" x2="368.9999999999998" y1="143.3704926140824" y2="143.3704926140824" zvalue="6"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="184" y1="155.5000000000001" y2="155.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="184" y1="181.5000000000001" y2="181.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="3" y1="155.5000000000001" y2="181.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="184" y1="155.5000000000001" y2="181.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="365" y1="155.5000000000001" y2="155.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="365" y1="181.5000000000001" y2="181.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="184" y1="155.5000000000001" y2="181.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365" x2="365" y1="155.5000000000001" y2="181.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="184" y1="181.5000000000001" y2="181.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="184" y1="205.7500000000001" y2="205.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="3" y1="181.5000000000001" y2="205.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="184" y1="181.5000000000001" y2="205.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="365" y1="181.5000000000001" y2="181.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="365" y1="205.7500000000001" y2="205.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="184" y1="181.5000000000001" y2="205.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365" x2="365" y1="181.5000000000001" y2="205.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="184" y1="205.7500000000001" y2="205.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="184" y1="228.5000000000001" y2="228.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="3" y1="205.7500000000001" y2="228.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="184" y1="205.7500000000001" y2="228.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="365" y1="205.7500000000001" y2="205.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="365" y1="228.5000000000001" y2="228.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="184" y1="205.7500000000001" y2="228.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365" x2="365" y1="205.7500000000001" y2="228.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="184" y1="228.5000000000001" y2="228.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="184" y1="251.2500000000001" y2="251.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="3" y1="228.5000000000001" y2="251.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="184" y1="228.5000000000001" y2="251.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="365" y1="228.5000000000001" y2="228.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="365" y1="251.2500000000001" y2="251.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="184" y1="228.5000000000001" y2="251.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365" x2="365" y1="228.5000000000001" y2="251.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="184" y1="251.2500000000001" y2="251.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="184" y1="274.0000000000001" y2="274.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="3" y1="251.2500000000001" y2="274.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="184" y1="251.2500000000001" y2="274.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="365" y1="251.2500000000001" y2="251.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="365" y1="274.0000000000001" y2="274.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="184" y1="251.2500000000001" y2="274.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365" x2="365" y1="251.2500000000001" y2="274.0000000000001"/>
  <line fill="none" id="29" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.000000000000227" x2="368.9999999999998" y1="613.3704926140824" y2="613.3704926140824" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2" x2="92" y1="928.5000000000002" y2="928.5000000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2" x2="92" y1="967.6633000000002" y2="967.6633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2" x2="2" y1="928.5000000000002" y2="967.6633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92" x2="92" y1="928.5000000000002" y2="967.6633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92" x2="362" y1="928.5000000000002" y2="928.5000000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92" x2="362" y1="967.6633000000002" y2="967.6633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92" x2="92" y1="928.5000000000002" y2="967.6633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="362" x2="362" y1="928.5000000000002" y2="967.6633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2" x2="92" y1="967.6632700000002" y2="967.6632700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2" x2="92" y1="995.5816700000003" y2="995.5816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2" x2="2" y1="967.6632700000002" y2="995.5816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92" x2="92" y1="967.6632700000002" y2="995.5816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92" x2="182" y1="967.6632700000002" y2="967.6632700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92" x2="182" y1="995.5816700000003" y2="995.5816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92" x2="92" y1="967.6632700000002" y2="995.5816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182" x2="182" y1="967.6632700000002" y2="995.5816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.0000000000001" x2="272.0000000000001" y1="967.6632700000002" y2="967.6632700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.0000000000001" x2="272.0000000000001" y1="995.5816700000003" y2="995.5816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.0000000000001" x2="182.0000000000001" y1="967.6632700000002" y2="995.5816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.0000000000001" x2="272.0000000000001" y1="967.6632700000002" y2="995.5816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272" x2="362" y1="967.6632700000002" y2="967.6632700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272" x2="362" y1="995.5816700000003" y2="995.5816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272" x2="272" y1="967.6632700000002" y2="995.5816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="362" x2="362" y1="967.6632700000002" y2="995.5816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2" x2="92" y1="995.5816000000002" y2="995.5816000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2" x2="92" y1="1023.5" y2="1023.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2" x2="2" y1="995.5816000000002" y2="1023.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92" x2="92" y1="995.5816000000002" y2="1023.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92" x2="182" y1="995.5816000000002" y2="995.5816000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92" x2="182" y1="1023.5" y2="1023.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92" x2="92" y1="995.5816000000002" y2="1023.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182" x2="182" y1="995.5816000000002" y2="1023.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.0000000000001" x2="272.0000000000001" y1="995.5816000000002" y2="995.5816000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.0000000000001" x2="272.0000000000001" y1="1023.5" y2="1023.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.0000000000001" x2="182.0000000000001" y1="995.5816000000002" y2="1023.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.0000000000001" x2="272.0000000000001" y1="995.5816000000002" y2="1023.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272" x2="362" y1="995.5816000000002" y2="995.5816000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272" x2="362" y1="1023.5" y2="1023.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272" x2="272" y1="995.5816000000002" y2="1023.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="362" x2="362" y1="995.5816000000002" y2="1023.5"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47,948.5) scale(1,1) translate(0,0)" writing-mode="lr" x="47" xml:space="preserve" y="954.5" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44,982.5) scale(1,1) translate(0,0)" writing-mode="lr" x="44" xml:space="preserve" y="988.5" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226,982.5) scale(1,1) translate(0,0)" writing-mode="lr" x="226" xml:space="preserve" y="988.5" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,43,1010.5) scale(1,1) translate(0,0)" writing-mode="lr" x="43" xml:space="preserve" y="1016.5" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,225,1010.5) scale(1,1) translate(0,0)" writing-mode="lr" x="225" xml:space="preserve" y="1016.5" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,67.5,643) scale(1,1) translate(0,2.07834e-13)" writing-mode="lr" x="67.5" xml:space="preserve" y="647.5000000000001" zvalue="19">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.054,950.5) scale(1,1) translate(0,0)" writing-mode="lr" x="227.05" xml:space="preserve" y="956.5" zvalue="28">MangBangHeErJi-01-2016</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,135.054,1009.5) scale(1,1) translate(0,0)" writing-mode="lr" x="135.05" xml:space="preserve" y="1015.5" zvalue="30">杨立超</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,41,169.5) scale(1,1) translate(0,0)" writing-mode="lr" x="41" xml:space="preserve" y="175" zvalue="31">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,221,169.5) scale(1,1) translate(0,0)" writing-mode="lr" x="221" xml:space="preserve" y="175" zvalue="32">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,230.792,193.083) scale(1,1) translate(0,0)" writing-mode="lr" x="230.79" xml:space="preserve" y="197.58" zvalue="34">0.4kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.1875,241.5) scale(1,1) translate(0,0)" writing-mode="lr" x="48.19" xml:space="preserve" y="246" zvalue="35">10kV#1变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,538.429,652.714) scale(1,1) translate(0,0)" writing-mode="lr" x="538.4299999999999" xml:space="preserve" y="657.21" zvalue="38">0.4kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" x="933.9921875" xml:space="preserve" y="967.4618490134186" zvalue="41">#1发电机      </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="933.9921875" xml:space="preserve" y="983.4618490134186" zvalue="41">500KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,910.721,716.005) scale(1,1) translate(0,0)" writing-mode="lr" x="910.72" xml:space="preserve" y="720.5" zvalue="43">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,955.327,794.426) scale(1,1) translate(0,0)" writing-mode="lr" x="955.33" xml:space="preserve" y="798.9299999999999" zvalue="47">411</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" x="773.46875" xml:space="preserve" y="514.0357142857143" zvalue="50">#1主变          </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="773.46875" xml:space="preserve" y="530.0357142857143" zvalue="50">630KVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,841.294,378.968) scale(1,1) translate(5.52754e-13,0)" writing-mode="lr" x="841.29" xml:space="preserve" y="383.47" zvalue="53">041</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,790.803,276.65) scale(1,1) translate(0,4.46559e-13)" writing-mode="lr" x="790.8" xml:space="preserve" y="281.15" zvalue="56">0416</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,823.714,152.286) scale(1,1) translate(0,0)" writing-mode="lr" x="823.71" xml:space="preserve" y="156.79" zvalue="58">10kV嘎棒线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,187.211,339.591) scale(1,1) translate(0,0)" writing-mode="lr" x="187.21" xml:space="preserve" y="344.09" zvalue="60">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,292.211,339.591) scale(1,1) translate(0,0)" writing-mode="lr" x="292.21" xml:space="preserve" y="344.09" zvalue="61">通道</text>
 </g>
 <g id="ButtonClass">
  <g href="10kV芒棒河二级电站.svg"><rect fill-opacity="0" height="24" width="72.88" x="35" y="327" zvalue="64"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="36">
   <path class="v400" d="M 577.14 655.14 L 1325 655.14" stroke-width="6" zvalue="37"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674244689924" ObjectName="0.4kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674244689924"/></metadata>
  <path d="M 577.14 655.14 L 1325 655.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="v400" height="30" transform="rotate(0,929.205,912.648) scale(1.85899,1.85899) translate(-416.476,-408.826)" width="30" x="901.3199538974909" xlink:href="#Generator:发电机_0" y="884.7629266475365" zvalue="40"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449815707654" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449815707654"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,929.205,912.648) scale(1.85899,1.85899) translate(-416.476,-408.826)" width="30" x="901.3199538974909" y="884.7629266475365"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="165">
   <use class="v400" height="30" transform="rotate(0,929.939,717.005) scale(1.9625,1.2338) translate(-448.866,-132.361)" width="15" x="915.220664146923" xlink:href="#Disconnector:刀闸_0" y="698.4980158730157" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449815642118" ObjectName="#1发电机4111隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449815642118"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,929.939,717.005) scale(1.9625,1.2338) translate(-448.866,-132.361)" width="15" x="915.220664146923" y="698.4980158730157"/></g>
  <g id="49">
   <use class="kv10" height="30" transform="rotate(0,822.522,277.65) scale(1.9625,1.2338) translate(-396.184,-49.1059)" width="15" x="807.8030137594913" xlink:href="#Disconnector:刀闸_0" y="259.1428571428573" zvalue="55"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449815773190" ObjectName="10kV嘎棒线0416隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449815773190"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,822.522,277.65) scale(1.9625,1.2338) translate(-396.184,-49.1059)" width="15" x="807.8030137594913" y="259.1428571428573"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="43">
   <path class="v400" d="M 930.11 699.11 L 930.11 655.14" stroke-width="1" zvalue="44"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="165@0" LinkObjectIDznd="36@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 930.11 699.11 L 930.11 655.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="v400" d="M 929.2 885.23 L 929.2 803.73" stroke-width="1" zvalue="46"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="850@0" LinkObjectIDznd="170@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 929.2 885.23 L 929.2 803.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="v400" d="M 930.83 782.49 L 930.83 735.2" stroke-width="1" zvalue="48"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@0" LinkObjectIDznd="165@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 930.83 782.49 L 930.83 735.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="46">
   <path class="v400" d="M 823.29 562.22 L 823.29 655.14" stroke-width="1" zvalue="50"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44@1" LinkObjectIDznd="36@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 823.29 562.22 L 823.29 655.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="kv10" d="M 822.76 390.58 L 823.34 473.67" stroke-width="1" zvalue="53"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@1" LinkObjectIDznd="44@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 822.76 390.58 L 823.34 473.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="kv10" d="M 822.64 295.84 L 822.64 369.34" stroke-width="1" zvalue="56"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@1" LinkObjectIDznd="47@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 822.64 295.84 L 822.64 369.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="20">
   <path class="kv10" d="M 821.93 186.89 L 821.93 259.75" stroke-width="1" zvalue="65"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="51@0" LinkObjectIDznd="49@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 821.93 186.89 L 821.93 259.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="170">
   <use class="v400" height="20" transform="rotate(0,930.869,793.119) scale(1.22222,1.11111) translate(-168.138,-78.2008)" width="10" x="924.7583097280913" xlink:href="#Breaker:开关_0" y="782.007631257631" zvalue="45"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924515004421" ObjectName="#1发电机411断路器"/>
   <cge:TPSR_Ref TObjectID="6473924515004421"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,930.869,793.119) scale(1.22222,1.11111) translate(-168.138,-78.2008)" width="10" x="924.7583097280913" y="782.007631257631"/></g>
  <g id="47">
   <use class="kv10" height="20" transform="rotate(0,822.683,379.968) scale(1.22222,1.11111) translate(-148.468,-36.8857)" width="10" x="816.5714285714287" xlink:href="#Breaker:开关_0" y="368.8571428571429" zvalue="52"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924515069957" ObjectName="10kV嘎棒线041断路器"/>
   <cge:TPSR_Ref TObjectID="6473924515069957"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,822.683,379.968) scale(1.22222,1.11111) translate(-148.468,-36.8857)" width="10" x="816.5714285714287" y="368.8571428571429"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="44">
   <g id="440">
    <use class="kv10" height="60" transform="rotate(0,823.286,517.857) scale(1.35,1.5) translate(-206.444,-157.619)" width="40" x="796.29" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="472.86" zvalue="49"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874439000068" ObjectName="10"/>
    </metadata>
   </g>
   <g id="441">
    <use class="v400" height="60" transform="rotate(0,823.286,517.857) scale(1.35,1.5) translate(-206.444,-157.619)" width="40" x="796.29" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="472.86" zvalue="49"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874439065604" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399451738115" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399451738115"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,823.286,517.857) scale(1.35,1.5) translate(-206.444,-157.619)" width="40" x="796.29" y="472.86"/></g>
 </g>
 <g id="StateClass">
  <g id="427">
   <use height="30" transform="rotate(0,319.485,340.107) scale(0.708333,0.665547) translate(127.178,165.895)" width="30" x="308.86" xlink:href="#State:红绿圆(方形)_0" y="330.12" zvalue="62"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,319.485,340.107) scale(0.708333,0.665547) translate(127.178,165.895)" width="30" x="308.86" y="330.12"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,223.86,340.107) scale(0.708333,0.665547) translate(87.8027,165.895)" width="30" x="213.24" xlink:href="#State:红绿圆(方形)_0" y="330.12" zvalue="63"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,223.86,340.107) scale(0.708333,0.665547) translate(87.8027,165.895)" width="30" x="213.24" y="330.12"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="11">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="11" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,530.143,553.643) scale(1,1) translate(0,0)" writing-mode="lr" x="529.67" xml:space="preserve" y="558.42" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125369675782" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="12">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="12" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,530.143,590.643) scale(1,1) translate(0,1.27263e-13)" writing-mode="lr" x="529.67" xml:space="preserve" y="595.42" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125369741318" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="13">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="13" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,530.143,627.643) scale(1,1) translate(0,0)" writing-mode="lr" x="529.67" xml:space="preserve" y="632.42" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125369806854" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="14">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="14" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,530.143,617.143) scale(1,1) translate(0,0)" writing-mode="lr" x="529.67" xml:space="preserve" y="621.92" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125369937926" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="15">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="15" prefix="U0:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,951.071,590.643) scale(1,1) translate(0,1.27263e-13)" writing-mode="lr" x="950.6" xml:space="preserve" y="595.42" zvalue="1">U0:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125370134532" ObjectName="U0"/>
   </metadata>
  </g>
 </g>
</svg>