<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549593571330" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.499999999999999" x2="5.916666666666666" y1="3.583333333333334" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="4.416666666666668" y2="20.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="4.249999999999998" y2="20.16666666666667"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Accessory:PT6_0" viewBox="0,0,15,20">
   <use terminal-index="0" type="0" x="7.560000000000002" xlink:href="#terminal" y="0.42361930658363"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.160000000000005" x2="8.559999999999999" y1="15.48035578286757" y2="14.24281579851547"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.060000000000002" x2="8.459999999999996" y1="16.09912577504353" y2="17.33666575939564"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.459999999999999" x2="5.060000000000007" y1="4.75500925181603" y2="5.992549236168133"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.460000000000005" x2="9.859999999999998" y1="4.75500925181603" y2="5.992549236168133"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.859999999999999" x2="8.859999999999999" y1="14.65532912663279" y2="17.13040909533699"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.460000000000003" x2="7.460000000000003" y1="2.279929283111803" y2="4.755009251816007"/>
   <ellipse cx="7.5" cy="6.51" fill-opacity="0" rx="6" ry="6.06" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.7" cy="13.44" fill-opacity="0" rx="6" ry="6.06" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:接地站用变两卷_0" viewBox="0,0,18,30">
   <use terminal-index="0" type="0" x="8.949999999999999" xlink:href="#terminal" y="0.5"/>
   <ellipse cx="9.039999999999999" cy="8.970000000000001" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.970000000000001" cy="21.14" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.035256190670173" x2="9.035256190670173" y1="4.6" y2="8.708948332339869"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.035256190670172" x2="5.05" y1="8.733948332339875" y2="11.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.063029030724982" x2="13.15" y1="8.689905009168513" y2="11.5"/>
   <path d="M 9 19.8333 L 4.08333 25.4167 L 14.0833 25.4167 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.027974625923065" x2="10.46799242340996" y1="19.93170597265525" y2="19.93170597265525"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="12.0194189818494"/>
   <line fill="none" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="5.416666666666668" y2="12.02321291373541"/>
   <line fill="none" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.92126160277786" x2="6.666666666666666" y1="12.10207526123773" y2="18.50000000000001"/>
   <line fill="none" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.18646053143751" x2="24.5" y1="12.18904818695178" y2="19.00000000000001"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 7.33333 43.5833 L 23 43.5833 L 15 31.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:4绕组PT带接地_0" viewBox="0,0,30,35">
   <use terminal-index="0" type="0" x="14.94238998217831" xlink:href="#terminal" y="31.09945819018008"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="13" y1="18.19406992327969" y2="17.19406992327969"/>
   <rect fill-opacity="0" height="14.44" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,4.46,16.81) scale(1,-1) translate(0,-1028.21)" width="5.58" x="1.67" y="9.58"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="13" y1="14.19406992327969" y2="15.19406992327969"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.583333333333325" x2="4.583333333333325" y1="30.69406992327969" y2="16.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.19406992327969" y2="14.19406992327969"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.583333333333325" x2="3.583333333333325" y1="15.69406992327969" y2="20.69406992327969"/>
   <path d="M 20.6667 26.75 L 20.6667 30.75 L 4.66667 30.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.583333333333325" x2="5.583333333333325" y1="15.69406992327969" y2="20.69406992327969"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.5" x2="26.5" y1="16.5" y2="16.5"/>
   <path d="M 20.75 22.8333 L 26.5833 22.8333 L 26.5833 6.83333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.59610027172149" x2="4.59610027172149" y1="9.527403256613018" y2="6.379186935280762"/>
   <ellipse cx="20.73" cy="22.49" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.625884107343747" x2="2.349068262659902" y1="6.31127839976627" y2="6.31127839976627"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.792550774010413" x2="3.182401595993234" y1="5.06127839976627" y2="5.06127839976627"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.209217440677081" x2="3.765734929326568" y1="3.81127839976627" y2="3.81127839976627"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.69906826265991" x2="23.09906826265991" y1="22.70662962336982" y2="23.94416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.69906826265991" x2="18.29906826265991" y1="22.70662962336982" y2="23.94416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.69906826265991" x2="20.69906826265991" y1="20.23154965466559" y2="22.7066296233698"/>
   <ellipse cx="14.48" cy="22.49" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="20.73" cy="16.24" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.44906826265991" x2="16.84906826265991" y1="22.70662962336982" y2="23.94416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.44906826265991" x2="14.44906826265991" y1="20.23154965466559" y2="22.7066296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.69906826265991" x2="18.29906826265991" y1="16.45662962336982" y2="17.69416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.69906826265991" x2="23.09906826265991" y1="16.45662962336982" y2="17.69416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.69906826265991" x2="20.69906826265991" y1="13.9815496546656" y2="16.4566296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.44906826265991" x2="12.04906826265991" y1="22.70662962336982" y2="23.94416960772192"/>
   <ellipse cx="14.48" cy="16.24" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="28.79255077401042" x2="24.51573492932657" y1="6.727945066432934" y2="6.727945066432934"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.95921744067708" x2="25.34906826265991" y1="5.477945066432934" y2="5.477945066432934"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.37588410734375" x2="25.93240159599324" y1="4.227945066432941" y2="4.227945066432941"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="1" y1="23" y2="13"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="6" y1="23" y2="11"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="0" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Accessory:PT232_0" viewBox="0,0,25,35">
   <use terminal-index="0" type="0" x="12.5" xlink:href="#terminal" y="0.2666666666666657"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.5" x2="12.5" y1="15.16666666666667" y2="0.5"/>
   <rect fill-opacity="0" height="10" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,12.5,7.5) scale(1,1) translate(0,0)" width="6" x="9.5" y="2.5"/>
   <ellipse cx="12.75" cy="20" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.75" cy="24.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.67" cy="29.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.82" cy="24.27" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.583333333333336" x2="5.583333333333336" y1="26.75" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333335" x2="5.583333333333334" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="5.5" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.75" x2="12.75" y1="22.25" y2="19.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.75" x2="12.75" y1="17.63888888888889" y2="19.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.75" x2="12.75" y1="17.63888888888889" y2="19.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.58333333333333" x2="12.58333333333333" y1="32" y2="29.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.58333333333333" x2="12.58333333333333" y1="27.38888888888889" y2="29.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.58333333333333" x2="12.58333333333333" y1="27.38888888888889" y2="29.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="19.31481481481482" y1="24.96612466124661" y2="22.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.94444444444444" x2="20.62962962962963" y1="24.96612466124661" y2="22.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="21.94444444444444" y1="24.96612466124661" y2="24.96612466124661"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:PT4_0" viewBox="0,0,18,15">
   <use terminal-index="0" type="0" x="17.15" xlink:href="#terminal" y="9.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.3" x2="5.3" y1="4.9" y2="4.9"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.33333333333334" x2="13.83333333333334" y1="9.25" y2="9.25"/>
   <rect fill-opacity="0" height="2.83" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.96,13.4) scale(1,1) translate(0,0)" width="7.92" x="4" y="11.98"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.499999999999998" x2="2.083333333333331" y1="9.25" y2="9.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="8.449999999999999" y1="7.433333333333334" y2="9.300000000000001"/>
   <path d="M 12 13.35 L 15 13.35 L 15 9.35" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 6 12.45 L 6 14.45 L 8.66667 13.4333 L 6 12.45" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.96666666666667" x2="1.96666666666667" y1="7.75" y2="10.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.716666666666673" x2="4.716666666666673" y1="9.25" y2="9.25"/>
   <ellipse cx="8.5" cy="4.75" fill-opacity="0" rx="2.57" ry="2.57" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="12.41666666666667" x2="4.75" y1="9.25" y2="9.25"/>
   <ellipse cx="4.5" cy="4.75" fill-opacity="0" rx="2.57" ry="2.57" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.5" cy="4.75" fill-opacity="0" rx="2.57" ry="2.57" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.5" cy="3" fill-opacity="0" rx="2.57" ry="2.57" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.71666666666667" x2="4.71666666666667" y1="7.85" y2="10.68333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.300000000000006" x2="1.300000000000006" y1="8.333333333333336" y2="10.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.8000000000000007" x2="0.8000000000000007" y1="8.750000000000004" y2="9.666666666666668"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.55" x2="9.550000000000001" y1="4.9" y2="4.9"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.71666666666667" x2="13.71666666666667" y1="9.25" y2="9.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.5" x2="1.916666666666666" y1="13.48333333333333" y2="13.48333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.933333333333335" x2="1.933333333333335" y1="11.98333333333333" y2="14.98333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="13.71666666666667" x2="13.71666666666667" y1="7.85" y2="10.68333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.46666666666667" x2="3.46666666666667" y1="7.850000000000001" y2="10.68333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.26666666666667" x2="1.26666666666667" y1="12.56666666666667" y2="14.56666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.7666666666666639" x2="0.7666666666666639" y1="12.98333333333334" y2="13.9"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.8" x2="13.8" y1="4.9" y2="4.9"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="12.46666666666667" x2="12.46666666666667" y1="7.85" y2="10.68333333333333"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Accessory:四卷PT_0" viewBox="0,0,18,18">
   <use terminal-index="0" type="0" x="9" xlink:href="#terminal" y="0.2666666666666639"/>
   <ellipse cx="9.25" cy="5.25" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.25" cy="9.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.08" cy="12.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.82" cy="9.27" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="5" y1="7.13888888888889" y2="9.138888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="11.75" y2="9.138888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="5" y1="7.13888888888889" y2="9.138888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.25" x2="9.25" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.25" x2="9.25" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.25" x2="9.25" y1="7.500000000000001" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="9" y1="10.63888888888889" y2="12.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="9" y1="10.63888888888889" y2="12.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="9" y1="15.25" y2="12.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="12.31481481481481" y1="9.96612466124661" y2="7.499999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.94444444444444" x2="13.62962962962963" y1="9.96612466124661" y2="7.499999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="14.94444444444444" y1="9.966124661246614" y2="9.966124661246614"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="220kV大盈江一级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="296.73" x="45.71" xlink:href="logo.png" y="33.57"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="297" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,194.079,63.5714) scale(1,1) translate(-2.03012e-14,0)" writing-mode="lr" x="194.08" xml:space="preserve" y="67.06999999999999" zvalue="4"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,203.079,63.2618) scale(1,1) translate(-2.82948e-14,0)" writing-mode="lr" x="203.08" xml:space="preserve" y="72.26000000000001" zvalue="5">220kV大盈江一级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="69" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" width="72.88" x="50.19" y="175.25" zvalue="732"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" writing-mode="lr" x="86.63" xml:space="preserve" y="191.75" zvalue="732">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,513.31,297.21) scale(1,1) translate(0,0)" writing-mode="lr" x="513.3099999999999" xml:space="preserve" y="301.71" zvalue="3">220kV母线</text>
  <line fill="none" id="295" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="379.7142857142858" x2="379.7142857142858" y1="13.57142857142856" y2="1043.571428571428" zvalue="6"/>
  <line fill="none" id="293" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.714285714286234" x2="372.7142857142858" y1="149.441921185511" y2="149.441921185511" zvalue="8"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="117" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,595.048,238.683) scale(1,1) translate(0,0)" writing-mode="lr" x="595.05" xml:space="preserve" y="243.18" zvalue="36">241</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,586.419,286.238) scale(1,1) translate(0,0)" writing-mode="lr" x="586.42" xml:space="preserve" y="290.74" zvalue="38">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="115" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,587.937,178.238) scale(1,1) translate(0,0)" writing-mode="lr" x="587.9400000000001" xml:space="preserve" y="182.74" zvalue="40">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="114" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,569.646,88.0159) scale(1,1) translate(0,0)" writing-mode="lr" x="569.65" xml:space="preserve" y="92.52" zvalue="42">220kV落盈线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="113" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,628,244.74) scale(1,1) translate(0,0)" writing-mode="lr" x="628" xml:space="preserve" y="249.24" zvalue="47">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="112" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,634,191) scale(1,1) translate(0,0)" writing-mode="lr" x="634" xml:space="preserve" y="195.5" zvalue="49">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="111" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,634,138.373) scale(1,1) translate(0,0)" writing-mode="lr" x="634" xml:space="preserve" y="142.87" zvalue="51">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="107" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1075.99,409.433) scale(1,1) translate(0,0)" writing-mode="lr" x="1075.99" xml:space="preserve" y="413.93" zvalue="67">202</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="105" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1068.94,348.988) scale(1,1) translate(0,0)" writing-mode="lr" x="1068.94" xml:space="preserve" y="353.49" zvalue="71">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="103" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1115,361.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1115" xml:space="preserve" y="366.25" zvalue="77">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="102" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1001.75,482.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1001.75" xml:space="preserve" y="487" zvalue="83">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="101" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1190,477.524) scale(1,1) translate(0,0)" writing-mode="lr" x="1190" xml:space="preserve" y="482.02" zvalue="85">2020</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,593.048,409.433) scale(1,1) translate(0,0)" writing-mode="lr" x="593.05" xml:space="preserve" y="413.93" zvalue="117">201</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,585.993,344.988) scale(1,1) translate(0,0)" writing-mode="lr" x="585.99" xml:space="preserve" y="349.49" zvalue="121">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,632,357.75) scale(1,1) translate(0,0)" writing-mode="lr" x="632" xml:space="preserve" y="362.25" zvalue="127">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,519.75,478.5) scale(1,1) translate(0,0)" writing-mode="lr" x="519.75" xml:space="preserve" y="483" zvalue="133">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,708.5,477.274) scale(1,1) translate(0,0)" writing-mode="lr" x="708.5" xml:space="preserve" y="481.77" zvalue="134">2010</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,478.346,722.75) scale(1,1) translate(0,0)" writing-mode="lr" x="478.35" xml:space="preserve" y="727.25" zvalue="142">#1厂用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="372" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,752.44,835.042) scale(1,1) translate(0,0)" writing-mode="lr" x="752.4400000000001" xml:space="preserve" y="839.54" zvalue="160">励磁变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1566.05,412.433) scale(1,1) translate(0,0)" writing-mode="lr" x="1566.05" xml:space="preserve" y="416.93" zvalue="222">203</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1558.94,351.988) scale(1,1) translate(0,0)" writing-mode="lr" x="1558.94" xml:space="preserve" y="356.49" zvalue="226">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1605,364.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1605" xml:space="preserve" y="369.25" zvalue="232">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1488.25,492.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1488.25" xml:space="preserve" y="496.75" zvalue="238">#3主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1695.5,485.524) scale(1,1) translate(0,0)" writing-mode="lr" x="1695.5" xml:space="preserve" y="490.02" zvalue="239">2030</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1434.94,240.238) scale(1,1) translate(0,0)" writing-mode="lr" x="1434.94" xml:space="preserve" y="244.74" zvalue="275">2901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1469,253) scale(1,1) translate(0,0)" writing-mode="lr" x="1469" xml:space="preserve" y="257.5" zvalue="277">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1469,187.981) scale(1,1) translate(0,0)" writing-mode="lr" x="1469" xml:space="preserve" y="192.48" zvalue="279">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1410.75,106.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1410.75" xml:space="preserve" y="111.25" zvalue="285">220kVⅠ母电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="346" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,655.938,561.25) scale(1,1) translate(0,0)" writing-mode="lr" x="655.9400000000001" xml:space="preserve" y="565.75" zvalue="448">0913</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="371" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,618.125,754) scale(1,1) translate(0,0)" writing-mode="lr" x="618.13" xml:space="preserve" y="758.5" zvalue="460">0911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="370" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,715.625,752.5) scale(1,1) translate(0,0)" writing-mode="lr" x="715.63" xml:space="preserve" y="757" zvalue="462">0912</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="410" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1242.44,830.042) scale(1,1) translate(0,0)" writing-mode="lr" x="1242.44" xml:space="preserve" y="834.54" zvalue="486">励磁变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="392" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1106.12,757) scale(1,1) translate(0,0)" writing-mode="lr" x="1106.13" xml:space="preserve" y="761.5" zvalue="507">0921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="390" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1203.62,755.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1203.63" xml:space="preserve" y="760" zvalue="510">0922</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="526" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1446.6,718.5) scale(1,1) translate(1.41646e-12,0)" writing-mode="lr" x="1446.6" xml:space="preserve" y="723" zvalue="545">#1近区变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="480" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1723.44,856.042) scale(1,1) translate(0,0)" writing-mode="lr" x="1723.44" xml:space="preserve" y="860.54" zvalue="551">励磁变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="474" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1595.12,768) scale(1,1) translate(0,0)" writing-mode="lr" x="1595.13" xml:space="preserve" y="772.5" zvalue="572">0931</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="473" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1692.62,766.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1692.63" xml:space="preserve" y="771" zvalue="575">0932</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="10.5" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.5" x2="372.5" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="10.5" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.5" x2="372.5" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="10.5" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.5" x2="372.5" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="10.5" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.5" x2="372.5" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="10.5" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.5" x2="372.5" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="10.5" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.5" x2="372.5" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="10.5" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.5" x2="372.5" y1="390.25" y2="413"/>
  <line fill="none" id="119" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="494.8704926140824" y2="494.8704926140824" zvalue="708"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="1001.0816" y2="1029"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="109" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,954) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="960" zvalue="710">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="108" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54,988) scale(1,1) translate(0,0)" writing-mode="lr" x="54" xml:space="preserve" y="994" zvalue="711">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,988) scale(1,1) translate(0,0)" writing-mode="lr" x="236" xml:space="preserve" y="994" zvalue="712">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="99" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="1022" zvalue="713">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="98" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="235" xml:space="preserve" y="1022" zvalue="714">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="96" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5,568.5) scale(1,1) translate(0,0)" writing-mode="lr" x="77.5" xml:space="preserve" y="573" zvalue="716">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="202.4" xml:space="preserve" y="192.34" zvalue="717">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="307.4" xml:space="preserve" y="192.34" zvalue="718">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,64.5,262) scale(1,1) translate(0,0)" writing-mode="lr" x="22" xml:space="preserve" y="266.5" zvalue="722">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240,262) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="266.5" zvalue="723">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.6875,335.25) scale(1,1) translate(0,0)" writing-mode="lr" x="62.69" xml:space="preserve" y="339.75" zvalue="724">220kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,288) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="292.5" zvalue="733">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239,288) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="292.5" zvalue="734">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,357.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="361.75" zvalue="737">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,309.75) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="314.25" zvalue="741">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,238.5,310) scale(1,1) translate(0,0)" writing-mode="lr" x="196" xml:space="preserve" y="314.5" zvalue="743">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,242.5,957.5) scale(1,1) translate(0,0)" writing-mode="lr" x="165" xml:space="preserve" y="962" zvalue="749">DaYingJiang1-01-2011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,595.424,567) scale(1,1) translate(0,0)" writing-mode="lr" x="595.42" xml:space="preserve" y="571.5" zvalue="754">011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="125" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,498.367,587) scale(1,1) translate(0,0)" writing-mode="lr" x="498.37" xml:space="preserve" y="591.5" zvalue="759">012</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="138" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,568,861) scale(1,1) translate(0,0)" writing-mode="lr" x="568" xml:space="preserve" y="865.5" zvalue="773">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="142" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1061.55,858) scale(1,1) translate(0,0)" writing-mode="lr" x="1061.55" xml:space="preserve" y="862.5" zvalue="776">#2发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="148" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,963.5,730.941) scale(1,1) translate(0,0)" writing-mode="lr" x="963.5" xml:space="preserve" y="735.4400000000001" zvalue="780">#2厂用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="147" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1138.09,587.441) scale(1,1) translate(0,2.57101e-13)" writing-mode="lr" x="1138.09" xml:space="preserve" y="591.9400000000001" zvalue="782">0923</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="146" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1078.55,632.191) scale(1,1) translate(0,0)" writing-mode="lr" x="1078.55" xml:space="preserve" y="636.6900000000001" zvalue="785">021</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="145" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,982.654,603.191) scale(1,1) translate(-2.15529e-13,0)" writing-mode="lr" x="982.65" xml:space="preserve" y="607.6900000000001" zvalue="787">022</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="164" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1556,874) scale(1,1) translate(0,0)" writing-mode="lr" x="1556" xml:space="preserve" y="878.5" zvalue="798">#3发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="178" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1628.94,573.566) scale(1,1) translate(0,0)" writing-mode="lr" x="1628.94" xml:space="preserve" y="578.0700000000001" zvalue="806">0933</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="177" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1568.39,619.316) scale(1,1) translate(0,0)" writing-mode="lr" x="1568.39" xml:space="preserve" y="623.8200000000001" zvalue="808">031</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="176" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1475.5,593.316) scale(1,1) translate(0,0)" writing-mode="lr" x="1475.5" xml:space="preserve" y="597.8200000000001" zvalue="810">032</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="50.19" y="175.25" zvalue="732"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="362">
   <path class="kv220" d="M 489.62 318.24 L 1796.94 318.24" stroke-width="4" zvalue="2"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674255831044" ObjectName="220kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674255831044"/></metadata>
  <path d="M 489.62 318.24 L 1796.94 318.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BreakerClass">
  <g id="404">
   <use class="kv220" height="20" transform="rotate(0,573.714,239.683) scale(1.22222,1.11111) translate(-103.201,-22.8571)" width="10" x="567.6031746031747" xlink:href="#Breaker:开关_0" y="228.5714285714285" zvalue="35"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924562518021" ObjectName="220kV落盈线241断路器"/>
   <cge:TPSR_Ref TObjectID="6473924562518021"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,573.714,239.683) scale(1.22222,1.11111) translate(-103.201,-22.8571)" width="10" x="567.6031746031747" y="228.5714285714285"/></g>
  <g id="579">
   <use class="kv220" height="20" transform="rotate(0,1054.66,410.433) scale(1.22222,1.11111) translate(-190.645,-39.9321)" width="10" x="1048.546385313894" xlink:href="#Breaker:开关_0" y="399.3214285714285" zvalue="66"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924562452484" ObjectName="#2主变220kV侧202断路器"/>
   <cge:TPSR_Ref TObjectID="6473924562452484"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1054.66,410.433) scale(1.22222,1.11111) translate(-190.645,-39.9321)" width="10" x="1048.546385313894" y="399.3214285714285"/></g>
  <g id="335">
   <use class="kv220" height="20" transform="rotate(0,571.714,410.433) scale(1.22222,1.11111) translate(-102.837,-39.9321)" width="10" x="565.6031746031747" xlink:href="#Breaker:开关_0" y="399.3214285714285" zvalue="116"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924562386948" ObjectName="#1主变220kV侧201断路器"/>
   <cge:TPSR_Ref TObjectID="6473924562386948"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,571.714,410.433) scale(1.22222,1.11111) translate(-102.837,-39.9321)" width="10" x="565.6031746031747" y="399.3214285714285"/></g>
  <g id="546">
   <use class="kv220" height="20" transform="rotate(0,1544.71,413.433) scale(1.22222,1.11111) translate(-279.746,-40.2321)" width="10" x="1538.603174603175" xlink:href="#Breaker:开关_0" y="402.3214285714285" zvalue="221"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924562321412" ObjectName="#3主变220kV侧203断路器"/>
   <cge:TPSR_Ref TObjectID="6473924562321412"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1544.71,413.433) scale(1.22222,1.11111) translate(-279.746,-40.2321)" width="10" x="1538.603174603175" y="402.3214285714285"/></g>
  <g id="77">
   <use class="kv10" height="20" transform="rotate(0,572.424,568) scale(2.2,2.2) translate(-306.231,-297.818)" width="10" x="561.4235449735452" xlink:href="#Breaker:小车断路器_0" y="546" zvalue="753"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924562583557" ObjectName="#1发电机011断路器"/>
   <cge:TPSR_Ref TObjectID="6473924562583557"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,572.424,568) scale(2.2,2.2) translate(-306.231,-297.818)" width="10" x="561.4235449735452" y="546"/></g>
  <g id="122">
   <use class="kv10" height="20" transform="rotate(0,471.867,588) scale(2.2,2.2) translate(-251.382,-308.727)" width="10" x="460.8667556577738" xlink:href="#Breaker:小车断路器_0" y="566" zvalue="758"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924562649093" ObjectName="#1厂用变012断路器"/>
   <cge:TPSR_Ref TObjectID="6473924562649093"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,471.867,588) scale(2.2,2.2) translate(-251.382,-308.727)" width="10" x="460.8667556577738" y="566"/></g>
  <g id="159">
   <use class="kv10" height="20" transform="rotate(0,1055.55,633.191) scale(2.2,2.2) translate(-569.752,-333.377)" width="10" x="1044.545767195767" xlink:href="#Breaker:小车断路器_0" y="611.1907793209876" zvalue="783"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924562780165" ObjectName="#2发电机021断路器"/>
   <cge:TPSR_Ref TObjectID="6473924562780165"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1055.55,633.191) scale(2.2,2.2) translate(-569.752,-333.377)" width="10" x="1044.545767195767" y="611.1907793209876"/></g>
  <g id="157">
   <use class="kv10" height="20" transform="rotate(0,956.154,604.191) scale(2.2,2.2) translate(-515.539,-317.559)" width="10" x="945.1540776755595" xlink:href="#Breaker:小车断路器_0" y="582.1907793209876" zvalue="786"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924562714629" ObjectName="#1厂用变022断路器"/>
   <cge:TPSR_Ref TObjectID="6473924562714629"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,956.154,604.191) scale(2.2,2.2) translate(-515.539,-317.559)" width="10" x="945.1540776755595" y="582.1907793209876"/></g>
  <g id="197">
   <use class="kv10" height="20" transform="rotate(0,1545.39,620.316) scale(2.2,2.2) translate(-836.941,-326.354)" width="10" x="1534.391689520208" xlink:href="#Breaker:小车断路器_0" y="598.3160759687612" zvalue="807"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924562911236" ObjectName="#3发电机031断路器"/>
   <cge:TPSR_Ref TObjectID="6473924562911236"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1545.39,620.316) scale(2.2,2.2) translate(-836.941,-326.354)" width="10" x="1534.391689520208" y="598.3160759687612"/></g>
  <g id="196">
   <use class="kv10" height="20" transform="rotate(0,1445,592.316) scale(2.2,2.2) translate(-782.182,-311.081)" width="10" x="1434" xlink:href="#Breaker:小车断路器_0" y="570.3160759687612" zvalue="809"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924562845701" ObjectName="#1厂用变032断路器"/>
   <cge:TPSR_Ref TObjectID="6473924562845701"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1445,592.316) scale(2.2,2.2) translate(-782.182,-311.081)" width="10" x="1434" y="570.3160759687612"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="403">
   <use class="kv220" height="30" transform="rotate(0,573.864,287.238) scale(-1.11111,-0.814815) translate(-1089.51,-642.535)" width="15" x="565.5303943336626" xlink:href="#Disconnector:刀闸_0" y="275.0158865792409" zvalue="37"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450053341189" ObjectName="220kV落盈线2411隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450053341189"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,573.864,287.238) scale(-1.11111,-0.814815) translate(-1089.51,-642.535)" width="15" x="565.5303943336626" y="275.0158865792409"/></g>
  <g id="402">
   <use class="kv220" height="30" transform="rotate(0,573.714,179.238) scale(-1.11111,-0.814815) translate(-1089.22,-401.99)" width="15" x="565.3809524074436" xlink:href="#Disconnector:刀闸_0" y="167.015873015873" zvalue="39"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450053275653" ObjectName="220kV落盈线2416隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450053275653"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,573.714,179.238) scale(-1.11111,-0.814815) translate(-1089.22,-401.99)" width="15" x="565.3809524074436" y="167.015873015873"/></g>
  <g id="577">
   <use class="kv220" height="30" transform="rotate(0,1054.71,349.988) scale(-1.11111,-0.814815) translate(-2003.12,-782.297)" width="15" x="1046.380952407444" xlink:href="#Disconnector:刀闸_0" y="337.765873015873" zvalue="70"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450052751365" ObjectName="#2主变220kV侧2021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450052751365"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1054.71,349.988) scale(-1.11111,-0.814815) translate(-2003.12,-782.297)" width="15" x="1046.380952407444" y="337.765873015873"/></g>
  <g id="333">
   <use class="kv220" height="30" transform="rotate(0,571.771,345.988) scale(-1.11111,-0.814815) translate(-1085.53,-773.388)" width="15" x="563.4377416967236" xlink:href="#Disconnector:刀闸_0" y="333.765873015873" zvalue="120"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450052423686" ObjectName="#1主变220kV侧2011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450052423686"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,571.771,345.988) scale(-1.11111,-0.814815) translate(-1085.53,-773.388)" width="15" x="563.4377416967236" y="333.765873015873"/></g>
  <g id="544">
   <use class="kv220" height="30" transform="rotate(0,1544.71,352.988) scale(-1.11111,-0.814815) translate(-2934.12,-788.979)" width="15" x="1536.380952407444" xlink:href="#Disconnector:刀闸_0" y="340.765873015873" zvalue="225"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450051964934" ObjectName="#3主变220kV侧2031隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450051964934"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1544.71,352.988) scale(-1.11111,-0.814815) translate(-2934.12,-788.979)" width="15" x="1536.380952407444" y="340.765873015873"/></g>
  <g id="565">
   <use class="kv220" height="30" transform="rotate(0,1408.71,241.238) scale(-1.11111,-0.814815) translate(-2675.72,-540.081)" width="15" x="1400.380952407444" xlink:href="#Disconnector:刀闸_0" y="229.015873015873" zvalue="274"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450051637253" ObjectName="220kV母线电压互感器2901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450051637253"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1408.71,241.238) scale(-1.11111,-0.814815) translate(-2675.72,-540.081)" width="15" x="1400.380952407444" y="229.015873015873"/></g>
  <g id="345">
   <use class="kv10" height="26" transform="rotate(0,686.75,562.25) scale(1.25,1.25) translate(-135.85,-109.2)" width="12" x="679.25" xlink:href="#Disconnector:单手车刀闸1212_0" y="546" zvalue="447"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450053406725" ObjectName="#1发电机0913隔离刀闸"/>
   <cge:TPSR_Ref TObjectID="6192450053406725"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,686.75,562.25) scale(1.25,1.25) translate(-135.85,-109.2)" width="12" x="679.25" y="546"/></g>
  <g id="357">
   <use class="kv10" height="26" transform="rotate(0,644.5,755) scale(1.25,1.25) translate(-127.4,-147.75)" width="12" x="637" xlink:href="#Disconnector:单手车刀闸1212_0" y="738.75" zvalue="459"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450053472261" ObjectName="#1发电机0911隔离刀闸"/>
   <cge:TPSR_Ref TObjectID="6192450053472261"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,644.5,755) scale(1.25,1.25) translate(-127.4,-147.75)" width="12" x="637" y="738.75"/></g>
  <g id="358">
   <use class="kv10" height="26" transform="rotate(0,690,753.5) scale(1.25,1.25) translate(-136.5,-147.45)" width="12" x="682.5" xlink:href="#Disconnector:单手车刀闸1212_0" y="737.25" zvalue="461"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450053537797" ObjectName="#1发电机0912隔离刀闸"/>
   <cge:TPSR_Ref TObjectID="6192450053537797"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,690,753.5) scale(1.25,1.25) translate(-136.5,-147.45)" width="12" x="682.5" y="737.25"/></g>
  <g id="429">
   <use class="kv10" height="26" transform="rotate(0,1132.5,758) scale(1.25,1.25) translate(-225,-148.35)" width="12" x="1125" xlink:href="#Disconnector:单手车刀闸1212_0" y="741.75" zvalue="506"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450053931013" ObjectName="#2发电机0921隔离刀闸"/>
   <cge:TPSR_Ref TObjectID="6192450053931013"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1132.5,758) scale(1.25,1.25) translate(-225,-148.35)" width="12" x="1125" y="741.75"/></g>
  <g id="428">
   <use class="kv10" height="26" transform="rotate(0,1178,756.5) scale(1.25,1.25) translate(-234.1,-148.05)" width="12" x="1170.5" xlink:href="#Disconnector:单手车刀闸1212_0" y="740.25" zvalue="508"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450053865477" ObjectName="#2发电机0922隔离刀闸"/>
   <cge:TPSR_Ref TObjectID="6192450053865477"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1178,756.5) scale(1.25,1.25) translate(-234.1,-148.05)" width="12" x="1170.5" y="740.25"/></g>
  <g id="498">
   <use class="kv10" height="26" transform="rotate(0,1621.5,769) scale(1.25,1.25) translate(-322.8,-150.55)" width="12" x="1614" xlink:href="#Disconnector:单手车刀闸1212_0" y="752.75" zvalue="571"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450054258693" ObjectName="#3发电机0931隔离刀闸"/>
   <cge:TPSR_Ref TObjectID="6192450054258693"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1621.5,769) scale(1.25,1.25) translate(-322.8,-150.55)" width="12" x="1614" y="752.75"/></g>
  <g id="497">
   <use class="kv10" height="26" transform="rotate(0,1667,767.5) scale(1.25,1.25) translate(-331.9,-150.25)" width="12" x="1659.5" xlink:href="#Disconnector:单手车刀闸1212_0" y="751.25" zvalue="573"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450054193157" ObjectName="#3发电机0932隔离刀闸"/>
   <cge:TPSR_Ref TObjectID="6192450054193157"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1667,767.5) scale(1.25,1.25) translate(-331.9,-150.25)" width="12" x="1659.5" y="751.25"/></g>
  <g id="160">
   <use class="kv10" height="26" transform="rotate(0,1168.9,588.441) scale(1.25,1.25) translate(-232.281,-114.438)" width="12" x="1161.40407767556" xlink:href="#Disconnector:单手车刀闸1212_0" y="572.1907793209876" zvalue="781"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450054914053" ObjectName="#1发电机0923隔离刀闸"/>
   <cge:TPSR_Ref TObjectID="6192450054914053"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1168.9,588.441) scale(1.25,1.25) translate(-232.281,-114.438)" width="12" x="1161.40407767556" y="572.1907793209876"/></g>
  <g id="198">
   <use class="kv10" height="26" transform="rotate(0,1659.75,574.566) scale(1.25,1.25) translate(-330.45,-111.663)" width="12" x="1652.25" xlink:href="#Disconnector:单手车刀闸1212_0" y="558.3160759687612" zvalue="805"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450055241733" ObjectName="#3发电机0933隔离刀闸"/>
   <cge:TPSR_Ref TObjectID="6192450055241733"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1659.75,574.566) scale(1.25,1.25) translate(-330.45,-111.663)" width="12" x="1652.25" y="558.3160759687612"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="259">
   <path class="kv220" d="M 573.8 250.29 L 573.8 275.22" stroke-width="1" zvalue="43"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="404@1" LinkObjectIDznd="403@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 573.8 250.29 L 573.8 275.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="256">
   <path class="kv220" d="M 573.62 191.06 L 573.67 229.05" stroke-width="1" zvalue="44"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="402@0" LinkObjectIDznd="404@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 573.62 191.06 L 573.67 229.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="255">
   <path class="kv220" d="M 573.65 142.79 L 573.65 167.22" stroke-width="1" zvalue="45"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="401@0" LinkObjectIDznd="402@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 573.65 142.79 L 573.65 167.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="253">
   <path class="kv220" d="M 617.77 262.76 L 573.8 262.76" stroke-width="1" zvalue="52"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="396@0" LinkObjectIDznd="259" MaxPinNum="2"/>
   </metadata>
  <path d="M 617.77 262.76 L 573.8 262.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="252">
   <path class="kv220" d="M 617.77 211.02 L 573.65 211.02" stroke-width="1" zvalue="53"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="395@0" LinkObjectIDznd="256" MaxPinNum="2"/>
   </metadata>
  <path d="M 617.77 211.02 L 573.65 211.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="250">
   <path class="kv220" d="M 617.77 158.39 L 573.65 158.39" stroke-width="1" zvalue="54"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="394@0" LinkObjectIDznd="255" MaxPinNum="2"/>
   </metadata>
  <path d="M 617.77 158.39 L 573.65 158.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="248">
   <path class="kv220" d="M 573.77 299.06 L 573.77 318.24" stroke-width="1" zvalue="55"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="403@0" LinkObjectIDznd="362@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 573.77 299.06 L 573.77 318.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="242">
   <path class="kv220" d="M 1054.62 361.81 L 1054.62 399.8" stroke-width="1" zvalue="73"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="577@0" LinkObjectIDznd="579@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1054.62 361.81 L 1054.62 399.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="240">
   <path class="kv220" d="M 1098.77 381.77 L 1054.62 381.77" stroke-width="1" zvalue="79"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="573@0" LinkObjectIDznd="242" MaxPinNum="2"/>
   </metadata>
  <path d="M 1098.77 381.77 L 1054.62 381.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="239">
   <path class="kv220" d="M 1054.65 337.97 L 1054.65 318.24" stroke-width="1" zvalue="80"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="577@1" LinkObjectIDznd="362@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1054.65 337.97 L 1054.65 318.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="238">
   <path class="kv220" d="M 1056.52 465.77 L 1113.75 465.77" stroke-width="1" zvalue="84"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="249@2" LinkObjectIDznd="251@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1056.52 465.77 L 1113.75 465.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="225">
   <path class="kv220" d="M 571.67 357.81 L 571.67 399.8" stroke-width="1" zvalue="123"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="333@0" LinkObjectIDznd="335@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 571.67 357.81 L 571.67 399.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="223">
   <path class="kv220" d="M 615.77 377.77 L 571.67 377.77" stroke-width="1" zvalue="129"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="328@0" LinkObjectIDznd="225" MaxPinNum="2"/>
   </metadata>
  <path d="M 615.77 377.77 L 571.67 377.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="221">
   <path class="kv220" d="M 573.52 461.77 L 634.75 461.77" stroke-width="1" zvalue="135"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="323@2" LinkObjectIDznd="322@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 573.52 461.77 L 634.75 461.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="188">
   <path class="kv220" d="M 1544.62 364.81 L 1544.67 402.8" stroke-width="1" zvalue="228"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="544@0" LinkObjectIDznd="546@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1544.62 364.81 L 1544.67 402.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="186">
   <path class="kv220" d="M 1588.77 384.77 L 1544.65 384.77" stroke-width="1" zvalue="234"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="539@0" LinkObjectIDznd="188" MaxPinNum="2"/>
   </metadata>
  <path d="M 1588.77 384.77 L 1544.65 384.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="185">
   <path class="kv220" d="M 1544.65 340.97 L 1544.65 318.24" stroke-width="1" zvalue="235"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="544@1" LinkObjectIDznd="362@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1544.65 340.97 L 1544.65 318.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="184">
   <path class="kv220" d="M 1546.52 468.77 L 1611.75 468.77" stroke-width="1" zvalue="240"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="533@2" LinkObjectIDznd="532@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1546.52 468.77 L 1611.75 468.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="172">
   <path class="kv220" d="M 1544.8 424.04 L 1544.8 454.31" stroke-width="1" zvalue="271"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="546@1" LinkObjectIDznd="533@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1544.8 424.04 L 1544.8 454.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="171">
   <path class="kv220" d="M 1452.77 273.02 L 1408.62 273.02" stroke-width="1" zvalue="280"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="561@0" LinkObjectIDznd="168" MaxPinNum="2"/>
   </metadata>
  <path d="M 1452.77 273.02 L 1408.62 273.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="170">
   <path class="kv220" d="M 1452.77 212 L 1408.65 212" stroke-width="1" zvalue="281"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="560@0" LinkObjectIDznd="169" MaxPinNum="2"/>
   </metadata>
  <path d="M 1452.77 212 L 1408.65 212" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="169">
   <path class="kv220" d="M 1408.65 229.22 L 1408.65 182.72" stroke-width="1" zvalue="282"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="565@1" LinkObjectIDznd="553@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1408.65 229.22 L 1408.65 182.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="168">
   <path class="kv220" d="M 1408.62 253.06 L 1408.62 318.24" stroke-width="1" zvalue="283"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="565@0" LinkObjectIDznd="362@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1408.62 253.06 L 1408.62 318.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="301">
   <path class="kv220" d="M 539.59 158.39 L 573.65 158.39" stroke-width="1" zvalue="423"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@0" LinkObjectIDznd="255" MaxPinNum="2"/>
   </metadata>
  <path d="M 539.59 158.39 L 573.65 158.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="344">
   <path class="kv220" d="M 571.7 333.97 L 571.7 318.24" stroke-width="1" zvalue="446"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="333@1" LinkObjectIDznd="362@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 571.7 333.97 L 571.7 318.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="364">
   <path class="kv10" d="M 644.61 771.21 L 644.61 795.96" stroke-width="1" zvalue="467"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="357@0" LinkObjectIDznd="360@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 644.61 771.21 L 644.61 795.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="369">
   <path class="kv10" d="M 690.11 795.96 L 690.11 769.71" stroke-width="1" zvalue="469"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="359@0" LinkObjectIDznd="358@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 690.11 795.96 L 690.11 769.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="422">
   <path class="kv10" d="M 1132.61 774.21 L 1132.61 798.96" stroke-width="1" zvalue="514"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="429@0" LinkObjectIDznd="425@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1132.61 774.21 L 1132.61 798.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="418">
   <path class="kv10" d="M 1178.11 798.96 L 1178.11 772.71" stroke-width="1" zvalue="516"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="427@0" LinkObjectIDznd="428@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1178.11 798.96 L 1178.11 772.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="466">
   <path class="kv10" d="M 1132.5 743.34 L 1132.5 717.5" stroke-width="1" zvalue="539"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="429@1" LinkObjectIDznd="175" MaxPinNum="2"/>
   </metadata>
  <path d="M 1132.5 743.34 L 1132.5 717.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="467">
   <path class="kv10" d="M 1178 741.84 L 1178 717.5" stroke-width="1" zvalue="540"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="428@1" LinkObjectIDznd="175" MaxPinNum="2"/>
   </metadata>
  <path d="M 1178 741.84 L 1178 717.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="493">
   <path class="kv10" d="M 1621.5 751.97 L 1621.5 711.13" stroke-width="1" zvalue="578"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="498@1" LinkObjectIDznd="167" MaxPinNum="2"/>
   </metadata>
  <path d="M 1621.5 751.97 L 1621.5 711.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="492">
   <path class="kv10" d="M 1621.61 785.21 L 1621.61 809.96" stroke-width="1" zvalue="579"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="498@0" LinkObjectIDznd="495@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1621.61 785.21 L 1621.61 809.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="491">
   <path class="kv10" d="M 1667 750.47 L 1667 711.13" stroke-width="1" zvalue="580"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="497@1" LinkObjectIDznd="167" MaxPinNum="2"/>
   </metadata>
  <path d="M 1667 750.47 L 1667 711.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="490">
   <path class="kv10" d="M 1667.11 809.96 L 1667.11 783.71" stroke-width="1" zvalue="581"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="496@0" LinkObjectIDznd="497@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1667.11 809.96 L 1667.11 783.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="611">
   <path class="kv10" d="M 644.5 738.84 L 644.5 705" stroke-width="1" zvalue="649"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="357@1" LinkObjectIDznd="140" MaxPinNum="2"/>
   </metadata>
  <path d="M 644.5 738.84 L 644.5 705" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="614">
   <path class="kv10" d="M 690 737.34 L 690 705" stroke-width="1" zvalue="650"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="358@1" LinkObjectIDznd="140" MaxPinNum="2"/>
   </metadata>
  <path d="M 690 737.34 L 690 705" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="54">
   <path class="kv220" d="M 1054.74 421.04 L 1054.74 451.31" stroke-width="1" zvalue="750"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="579@1" LinkObjectIDznd="249@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1054.74 421.04 L 1054.74 451.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="kv220" d="M 571.8 421.04 L 571.8 447.31" stroke-width="1" zvalue="751"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="335@1" LinkObjectIDznd="323@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 571.8 421.04 L 571.8 447.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="84">
   <path class="kv10" d="M 573.5 508.81 L 573.5 547.65" stroke-width="1" zvalue="754"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="323@1" LinkObjectIDznd="77@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 573.5 508.81 L 573.5 547.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="121">
   <path class="kv10" d="M 572.42 587.8 L 572.42 798.88" stroke-width="1" zvalue="756"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="77@1" LinkObjectIDznd="137@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 572.42 587.8 L 572.42 798.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="129">
   <path class="kv10" d="M 471.87 647.29 L 471.87 607.8" stroke-width="1" zvalue="764"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="317@0" LinkObjectIDznd="122@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 471.87 647.29 L 471.87 607.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="133">
   <path class="kv10" d="M 723.03 603.63 L 723.03 529 L 573.5 529" stroke-width="1" zvalue="768"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="131@0" LinkObjectIDznd="84" MaxPinNum="2"/>
   </metadata>
  <path d="M 723.03 603.63 L 723.03 529 L 573.5 529" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="135">
   <path class="kv10" d="M 686.86 599.36 L 686.86 578.46" stroke-width="1" zvalue="770"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="132@0" LinkObjectIDznd="345@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 686.86 599.36 L 686.86 578.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="136">
   <path class="kv10" d="M 686.75 546.09 L 686.75 529" stroke-width="1" zvalue="771"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="345@1" LinkObjectIDznd="133" MaxPinNum="2"/>
   </metadata>
  <path d="M 686.75 546.09 L 686.75 529" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="140">
   <path class="kv10" d="M 752.53 791.78 L 752.53 705 L 572.42 705" stroke-width="1" zvalue="774"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="304@0" LinkObjectIDznd="121" MaxPinNum="2"/>
   </metadata>
  <path d="M 752.53 791.78 L 752.53 705 L 572.42 705" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="151">
   <path class="kv10" d="M 1205.19 629.82 L 1205.19 554.17 L 1056.5 554.17" stroke-width="1" zvalue="793"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="153@0" LinkObjectIDznd="173" MaxPinNum="2"/>
   </metadata>
  <path d="M 1205.19 629.82 L 1205.19 554.17 L 1056.5 554.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="150">
   <path class="kv10" d="M 1169.01 625.55 L 1169.01 604.65" stroke-width="1" zvalue="794"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="152@0" LinkObjectIDznd="160@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1169.01 625.55 L 1169.01 604.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="kv10" d="M 1168.91 572.28 L 1168.91 555.19" stroke-width="1" zvalue="795"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="160@1" LinkObjectIDznd="151" MaxPinNum="2"/>
   </metadata>
  <path d="M 1168.91 572.28 L 1168.91 555.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="167">
   <path class="kv10" d="M 1723.53 812.78 L 1723.6 712 L 1545.39 712" stroke-width="1" zvalue="800"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="520@0" LinkObjectIDznd="201" MaxPinNum="2"/>
   </metadata>
  <path d="M 1723.53 812.78 L 1723.6 712 L 1545.39 712" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="173">
   <path class="kv10" d="M 1056.5 512.81 L 1056.5 612.84" stroke-width="1" zvalue="801"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="249@1" LinkObjectIDznd="159@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1056.5 512.81 L 1056.5 612.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="174">
   <path class="kv10" d="M 1055.55 652.99 L 1055.55 794.88" stroke-width="1" zvalue="802"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="159@1" LinkObjectIDznd="141@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1055.55 652.99 L 1055.55 794.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="175">
   <path class="kv10" d="M 1242.53 786.78 L 1242.53 716 L 1055.55 716" stroke-width="1" zvalue="803"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="444@0" LinkObjectIDznd="174" MaxPinNum="2"/>
   </metadata>
  <path d="M 1242.53 786.78 L 1242.53 716 L 1055.55 716" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="194">
   <path class="kv10" d="M 1445.87 634.29 L 1445.87 612.12" stroke-width="1" zvalue="812"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="525@0" LinkObjectIDznd="196@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1445.87 634.29 L 1445.87 612.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="193">
   <path class="kv10" d="M 1445 571.97 L 1445 540 L 1546.5 540" stroke-width="1" zvalue="813"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="196@0" LinkObjectIDznd="200" MaxPinNum="2"/>
   </metadata>
  <path d="M 1445 571.97 L 1445 540 L 1546.5 540" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="180">
   <path class="kv10" d="M 1658.86 612.67 L 1658.86 590.78" stroke-width="1" zvalue="817"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="182@0" LinkObjectIDznd="198@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1658.86 612.67 L 1658.86 590.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="kv10" d="M 1659.75 558.41 L 1659.75 538" stroke-width="1" zvalue="818"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="198@1" LinkObjectIDznd="215" MaxPinNum="2"/>
   </metadata>
  <path d="M 1659.75 558.41 L 1659.75 538" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="200">
   <path class="kv10" d="M 1546.5 515.81 L 1546.5 599.97" stroke-width="1" zvalue="820"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="533@1" LinkObjectIDznd="197@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1546.5 515.81 L 1546.5 599.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="201">
   <path class="kv10" d="M 1545.39 640.12 L 1545.39 812.88" stroke-width="1" zvalue="821"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="197@1" LinkObjectIDznd="163@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1545.39 640.12 L 1545.39 812.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="203">
   <path class="kv10" d="M 471.87 567.65 L 471.87 535 L 573.5 535" stroke-width="1" zvalue="823"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="122@0" LinkObjectIDznd="84" MaxPinNum="2"/>
   </metadata>
  <path d="M 471.87 567.65 L 471.87 535 L 573.5 535" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="211">
   <path class="kv10" d="M 957.02 655.48 L 957.02 623.99" stroke-width="1" zvalue="825"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@0" LinkObjectIDznd="157@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 957.02 655.48 L 957.02 623.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="212">
   <path class="kv10" d="M 956.15 583.84 L 956.15 557 L 1056.5 557" stroke-width="1" zvalue="826"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157@0" LinkObjectIDznd="173" MaxPinNum="2"/>
   </metadata>
  <path d="M 956.15 583.84 L 956.15 557 L 1056.5 557" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="215">
   <path class="kv10" d="M 1695.03 616.95 L 1695.03 538 L 1554 538 L 1546.5 538" stroke-width="1" zvalue="827"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="183@0" LinkObjectIDznd="200" MaxPinNum="2"/>
   </metadata>
  <path d="M 1695.03 616.95 L 1695.03 538 L 1554 538 L 1546.5 538" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="396">
   <use class="kv220" height="30" transform="rotate(270,634,262.74) scale(1.66667,1.13333) translate(-249.6,-28.9106)" width="12" x="624" xlink:href="#GroundDisconnector:地刀12_0" y="245.7401555714285" zvalue="46"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450053144581" ObjectName="220kV落盈线24117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450053144581"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,634,262.74) scale(1.66667,1.13333) translate(-249.6,-28.9106)" width="12" x="624" y="245.7401555714285"/></g>
  <g id="395">
   <use class="kv220" height="30" transform="rotate(270,634,211) scale(1.66667,1.13333) translate(-249.6,-22.8235)" width="12" x="624" xlink:href="#GroundDisconnector:地刀12_0" y="194" zvalue="48"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450053013509" ObjectName="220kV落盈线24160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450053013509"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,634,211) scale(1.66667,1.13333) translate(-249.6,-22.8235)" width="12" x="624" y="194"/></g>
  <g id="394">
   <use class="kv220" height="30" transform="rotate(270,634,158.373) scale(1.66667,1.13333) translate(-249.6,-16.6321)" width="12" x="624" xlink:href="#GroundDisconnector:地刀12_0" y="141.3725549345024" zvalue="50"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450052882437" ObjectName="220kV落盈线24167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450052882437"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,634,158.373) scale(1.66667,1.13333) translate(-249.6,-16.6321)" width="12" x="624" y="141.3725549345024"/></g>
  <g id="573">
   <use class="kv220" height="30" transform="rotate(270,1115,381.75) scale(1.66667,1.13333) translate(-442,-42.9118)" width="12" x="1105" xlink:href="#GroundDisconnector:地刀12_0" y="364.75" zvalue="76"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450052685829" ObjectName="#2主变220kV侧20217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450052685829"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1115,381.75) scale(1.66667,1.13333) translate(-442,-42.9118)" width="12" x="1105" y="364.75"/></g>
  <g id="251">
   <use class="kv220" height="40" transform="rotate(0,1130.5,481.024) scale(1.25,-1.25) translate(-221.1,-860.844)" width="40" x="1105.5" xlink:href="#GroundDisconnector:中性点地刀12_0" y="456.0242737273118" zvalue="82"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450052554757" ObjectName="#2主变220kV侧2020中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450052554757"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1130.5,481.024) scale(1.25,-1.25) translate(-221.1,-860.844)" width="40" x="1105.5" y="456.0242737273118"/></g>
  <g id="328">
   <use class="kv220" height="30" transform="rotate(270,632,377.75) scale(1.66667,1.13333) translate(-248.8,-42.4412)" width="12" x="622" xlink:href="#GroundDisconnector:地刀12_0" y="360.75" zvalue="126"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450052358149" ObjectName="#1主变220kV侧20117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450052358149"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,632,377.75) scale(1.66667,1.13333) translate(-248.8,-42.4412)" width="12" x="622" y="360.75"/></g>
  <g id="322">
   <use class="kv220" height="40" transform="rotate(0,651.5,477.024) scale(1.25,-1.25) translate(-125.3,-853.644)" width="40" x="626.5" xlink:href="#GroundDisconnector:中性点地刀12_0" y="452.0242737273118" zvalue="132"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450052227077" ObjectName="#1主变220kV侧2010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450052227077"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,651.5,477.024) scale(1.25,-1.25) translate(-125.3,-853.644)" width="40" x="626.5" y="452.0242737273118"/></g>
  <g id="539">
   <use class="kv220" height="30" transform="rotate(270,1605,384.75) scale(1.66667,1.13333) translate(-638,-43.2647)" width="12" x="1595" xlink:href="#GroundDisconnector:地刀12_0" y="367.75" zvalue="231"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450051899398" ObjectName="#3主变220kV侧20317接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450051899398"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1605,384.75) scale(1.66667,1.13333) translate(-638,-43.2647)" width="12" x="1595" y="367.75"/></g>
  <g id="532">
   <use class="kv220" height="40" transform="rotate(0,1628.5,484.024) scale(1.25,-1.25) translate(-320.7,-866.244)" width="40" x="1603.5" xlink:href="#GroundDisconnector:中性点地刀12_0" y="459.0242737273118" zvalue="237"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450051768325" ObjectName="#3主变220kV侧2030中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450051768325"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1628.5,484.024) scale(1.25,-1.25) translate(-320.7,-866.244)" width="40" x="1603.5" y="459.0242737273118"/></g>
  <g id="561">
   <use class="kv220" height="30" transform="rotate(270,1469,273) scale(1.66667,1.13333) translate(-583.6,-30.1176)" width="12" x="1459" xlink:href="#GroundDisconnector:地刀12_0" y="256" zvalue="276"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450051571717" ObjectName="220kV母线电压互感器29010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450051571717"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1469,273) scale(1.66667,1.13333) translate(-583.6,-30.1176)" width="12" x="1459" y="256"/></g>
  <g id="560">
   <use class="kv220" height="30" transform="rotate(270,1469,211.981) scale(1.66667,1.13333) translate(-583.6,-22.9389)" width="12" x="1459" xlink:href="#GroundDisconnector:地刀12_0" y="194.9808882678358" zvalue="278"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450051440645" ObjectName="220kV母线电压互感器29017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450051440645"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1469,211.981) scale(1.66667,1.13333) translate(-583.6,-22.9389)" width="12" x="1459" y="194.9808882678358"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="249">
   <g id="2490">
    <use class="kv220" height="50" transform="rotate(0,1056.5,482) scale(1.25,1.25) translate(-207.55,-90.15)" width="30" x="1037.75" xlink:href="#PowerTransformer2:Y-D_0" y="450.75" zvalue="81"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874458202115" ObjectName="220"/>
    </metadata>
   </g>
   <g id="2491">
    <use class="kv10" height="50" transform="rotate(0,1056.5,482) scale(1.25,1.25) translate(-207.55,-90.15)" width="30" x="1037.75" xlink:href="#PowerTransformer2:Y-D_1" y="450.75" zvalue="81"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874458267651" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399460978691" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399460978691"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1056.5,482) scale(1.25,1.25) translate(-207.55,-90.15)" width="30" x="1037.75" y="450.75"/></g>
  <g id="323">
   <g id="3230">
    <use class="kv220" height="50" transform="rotate(0,573.5,478) scale(1.25,1.25) translate(-110.95,-89.35)" width="30" x="554.75" xlink:href="#PowerTransformer2:Y-D_0" y="446.75" zvalue="131"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874458071043" ObjectName="220"/>
    </metadata>
   </g>
   <g id="3231">
    <use class="kv10" height="50" transform="rotate(0,573.5,478) scale(1.25,1.25) translate(-110.95,-89.35)" width="30" x="554.75" xlink:href="#PowerTransformer2:Y-D_1" y="446.75" zvalue="131"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874458136579" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399460913155" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399460913155"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,573.5,478) scale(1.25,1.25) translate(-110.95,-89.35)" width="30" x="554.75" y="446.75"/></g>
  <g id="533">
   <g id="5330">
    <use class="kv220" height="50" transform="rotate(0,1546.5,485) scale(1.25,1.25) translate(-305.55,-90.75)" width="30" x="1527.75" xlink:href="#PowerTransformer2:Y-D_0" y="453.75" zvalue="236"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874457939971" ObjectName="220"/>
    </metadata>
   </g>
   <g id="5331">
    <use class="kv10" height="50" transform="rotate(0,1546.5,485) scale(1.25,1.25) translate(-305.55,-90.75)" width="30" x="1527.75" xlink:href="#PowerTransformer2:Y-D_1" y="453.75" zvalue="236"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874458005507" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399460847619" ObjectName="#3主变"/>
   <cge:TPSR_Ref TObjectID="6755399460847619"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1546.5,485) scale(1.25,1.25) translate(-305.55,-90.75)" width="30" x="1527.75" y="453.75"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="317">
   <use class="kv10" height="30" transform="rotate(0,471.971,677.5) scale(2.08333,2.08333) translate(-235.675,-336.05)" width="18" x="453.2209223244405" xlink:href="#EnergyConsumer:接地站用变两卷_0" y="646.25" zvalue="141"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450052096005" ObjectName="#1厂用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,471.971,677.5) scale(2.08333,2.08333) translate(-235.675,-336.05)" width="18" x="453.2209223244405" y="646.25"/></g>
  <g id="525">
   <use class="kv10" height="30" transform="rotate(0,1445.97,664.5) scale(2.08333,2.08333) translate(-742.155,-329.29)" width="18" x="1427.22092232444" xlink:href="#EnergyConsumer:接地站用变两卷_0" y="633.25" zvalue="544"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450054389765" ObjectName="#1近区变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1445.97,664.5) scale(2.08333,2.08333) translate(-742.155,-329.29)" width="18" x="1427.22092232444" y="633.25"/></g>
  <g id="161">
   <use class="kv10" height="30" transform="rotate(0,957.125,685.691) scale(2.08333,2.08333) translate(-487.955,-340.309)" width="18" x="938.375" xlink:href="#EnergyConsumer:接地站用变两卷_0" y="654.4407793209875" zvalue="779"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450054979589" ObjectName="#2厂用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,957.125,685.691) scale(2.08333,2.08333) translate(-487.955,-340.309)" width="18" x="938.375" y="654.4407793209875"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="304">
   <use class="kv10" height="20" transform="rotate(0,752.44,805.58) scale(1.44131,1.44131) translate(-227.077,-242.245)" width="15" x="741.6303554228138" xlink:href="#Accessory:PT6_0" y="791.1666666666669" zvalue="159"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450052030469" ObjectName="#1发电机励磁变"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,752.44,805.58) scale(1.44131,1.44131) translate(-227.077,-242.245)" width="15" x="741.6303554228138" y="791.1666666666669"/></g>
  <g id="553">
   <use class="kv220" height="35" transform="rotate(0,1409,161.469) scale(2.08333,1.5625) translate(-716.43,-48.285)" width="30" x="1377.75" xlink:href="#Accessory:4绕组PT带接地_0" y="134.125" zvalue="284"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450051309573" ObjectName="220kVⅠ母电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1409,161.469) scale(2.08333,1.5625) translate(-716.43,-48.285)" width="30" x="1377.75" y="134.125"/></g>
  <g id="359">
   <use class="kv10" height="35" transform="rotate(0,690.105,817.5) scale(1.25,1.25) translate(-134.896,-159.125)" width="25" x="674.4800840298944" xlink:href="#Accessory:PT232_0" y="795.625" zvalue="462"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450053603333" ObjectName="#1发电机PT2"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,690.105,817.5) scale(1.25,1.25) translate(-134.896,-159.125)" width="25" x="674.4800840298944" y="795.625"/></g>
  <g id="360">
   <use class="kv10" height="35" transform="rotate(0,644.25,817.5) scale(1.25,1.25) translate(-125.725,-159.125)" width="25" x="628.625" xlink:href="#Accessory:PT232_0" y="795.625" zvalue="464"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450053668869" ObjectName="#1发电机PT3"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,644.25,817.5) scale(1.25,1.25) translate(-125.725,-159.125)" width="25" x="628.625" y="795.625"/></g>
  <g id="444">
   <use class="kv10" height="20" transform="rotate(0,1242.44,800.58) scale(1.44131,1.44131) translate(-377.109,-240.714)" width="15" x="1231.630355422814" xlink:href="#Accessory:PT6_0" y="786.1666666666669" zvalue="485"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450053996549" ObjectName="#2发电机励磁变"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1242.44,800.58) scale(1.44131,1.44131) translate(-377.109,-240.714)" width="15" x="1231.630355422814" y="786.1666666666669"/></g>
  <g id="427">
   <use class="kv10" height="35" transform="rotate(0,1178.11,820.5) scale(1.25,1.25) translate(-232.496,-159.725)" width="25" x="1162.480084029894" xlink:href="#Accessory:PT232_0" y="798.625" zvalue="509"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450053799941" ObjectName="#2发电机PT2"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1178.11,820.5) scale(1.25,1.25) translate(-232.496,-159.725)" width="25" x="1162.480084029894" y="798.625"/></g>
  <g id="425">
   <use class="kv10" height="35" transform="rotate(0,1132.25,820.5) scale(1.25,1.25) translate(-223.325,-159.725)" width="25" x="1116.625" xlink:href="#Accessory:PT232_0" y="798.625" zvalue="511"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450053734405" ObjectName="#2发电机PT3"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1132.25,820.5) scale(1.25,1.25) translate(-223.325,-159.725)" width="25" x="1116.625" y="798.625"/></g>
  <g id="520">
   <use class="kv10" height="20" transform="rotate(0,1723.44,826.58) scale(1.44131,1.44131) translate(-524.384,-248.675)" width="15" x="1712.630355422814" xlink:href="#Accessory:PT6_0" y="812.1666666666669" zvalue="550"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450054324229" ObjectName="#3发电机励磁变"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1723.44,826.58) scale(1.44131,1.44131) translate(-524.384,-248.675)" width="15" x="1712.630355422814" y="812.1666666666669"/></g>
  <g id="496">
   <use class="kv10" height="35" transform="rotate(0,1667.11,831.5) scale(1.25,1.25) translate(-330.296,-161.925)" width="25" x="1651.480084029894" xlink:href="#Accessory:PT232_0" y="809.625" zvalue="574"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450054127621" ObjectName="#3发电机PT2"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1667.11,831.5) scale(1.25,1.25) translate(-330.296,-161.925)" width="25" x="1651.480084029894" y="809.625"/></g>
  <g id="495">
   <use class="kv10" height="35" transform="rotate(0,1621.25,831.5) scale(1.25,1.25) translate(-321.125,-161.925)" width="25" x="1605.625" xlink:href="#Accessory:PT232_0" y="809.625" zvalue="576"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450054062085" ObjectName="#3发电机PT3"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1621.25,831.5) scale(1.25,1.25) translate(-321.125,-161.925)" width="25" x="1605.625" y="809.625"/></g>
  <g id="49">
   <use class="kv220" height="15" transform="rotate(0,520.3,154.25) scale(2.36667,2.36667) translate(-288.155,-78.8239)" width="18" x="499" xlink:href="#Accessory:PT4_0" y="136.5" zvalue="747"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450054455301" ObjectName="220kV落盈线PT"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,520.3,154.25) scale(2.36667,2.36667) translate(-288.155,-78.8239)" width="18" x="499" y="136.5"/></g>
  <g id="131">
   <use class="kv10" height="26" transform="rotate(0,723,616) scale(1,1) translate(0,0)" width="12" x="717" xlink:href="#Accessory:避雷器1_0" y="603" zvalue="766"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450054520837" ObjectName="#1发电机避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,723,616) scale(1,1) translate(0,0)" width="12" x="717" y="603"/></g>
  <g id="132">
   <use class="kv10" height="18" transform="rotate(0,686.855,611) scale(1.33333,1.33333) translate(-168.714,-149.75)" width="18" x="674.8550840298944" xlink:href="#Accessory:四卷PT_0" y="599" zvalue="767"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450054586373" ObjectName="#1发电机PT1"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,686.855,611) scale(1.33333,1.33333) translate(-168.714,-149.75)" width="18" x="674.8550840298944" y="599"/></g>
  <g id="153">
   <use class="kv10" height="26" transform="rotate(0,1205.15,642.191) scale(1,1) translate(0,0)" width="12" x="1199.15407767556" xlink:href="#Accessory:避雷器1_0" y="629.1907793209876" zvalue="791"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450054848517" ObjectName="#2发电机避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1205.15,642.191) scale(1,1) translate(0,0)" width="12" x="1199.15407767556" y="629.1907793209876"/></g>
  <g id="152">
   <use class="kv10" height="18" transform="rotate(0,1169.01,637.191) scale(1.33333,1.33333) translate(-289.252,-156.298)" width="18" x="1157.009161705454" xlink:href="#Accessory:四卷PT_0" y="625.1907793209876" zvalue="792"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450054782981" ObjectName="#2发电机PT1"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1169.01,637.191) scale(1.33333,1.33333) translate(-289.252,-156.298)" width="18" x="1157.009161705454" y="625.1907793209876"/></g>
  <g id="183">
   <use class="kv10" height="26" transform="rotate(0,1695,629.316) scale(1,1) translate(0,0)" width="12" x="1689" xlink:href="#Accessory:避雷器1_0" y="616.3160759687612" zvalue="814"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450055176197" ObjectName="#3发电机避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1695,629.316) scale(1,1) translate(0,0)" width="12" x="1689" y="616.3160759687612"/></g>
  <g id="182">
   <use class="kv10" height="18" transform="rotate(0,1658.86,624.316) scale(1.33333,1.33333) translate(-411.714,-153.079)" width="18" x="1646.855084029894" xlink:href="#Accessory:四卷PT_0" y="612.3160759687612" zvalue="815"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450055110661" ObjectName="#3发电机PT1"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1658.86,624.316) scale(1.33333,1.33333) translate(-411.714,-153.079)" width="18" x="1646.855084029894" y="612.3160759687612"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="1">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="1" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,571.424,30.3968) scale(1,1) translate(1.76115e-13,2.03717e-15)" writing-mode="lr" x="570.98" xml:space="preserve" y="35.09" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126847184900" ObjectName="P"/>
   </metadata>
  </g>
  <g id="4">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="4" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,571.424,47.7857) scale(1,1) translate(0,3.98623e-15)" writing-mode="lr" x="570.96" xml:space="preserve" y="52.47" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126847250436" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="5">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="5" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,571.424,65.1746) scale(1,1) translate(0,0)" writing-mode="lr" x="570.96" xml:space="preserve" y="69.86" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126847315972" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="7" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1452.39,414.319) scale(1,1) translate(0,-8.92064e-14)" writing-mode="lr" x="1451.84" xml:space="preserve" y="419.02" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126838927364" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="9">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="9" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,438.167,356.542) scale(1,1) translate(0,-2.29132e-13)" writing-mode="lr" x="437.61" xml:space="preserve" y="361.24" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126842073092" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="11">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="11" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,941.611,388.653) scale(1,1) translate(0,-1.66768e-13)" writing-mode="lr" x="941.0599999999999" xml:space="preserve" y="393.36" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126844628996" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="12">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="12" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1452.39,432.819) scale(1,1) translate(7.76817e-13,-1.87443e-13)" writing-mode="lr" x="1451.84" xml:space="preserve" y="437.49" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126838992900" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="14">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="14" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,438.167,375.597) scale(1,1) translate(0,4.03227e-14)" writing-mode="lr" x="437.61" xml:space="preserve" y="380.29" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126842138628" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="15">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="15" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,941.611,413.819) scale(1,1) translate(0,0)" writing-mode="lr" x="941.0599999999999" xml:space="preserve" y="418.53" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126844694532" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="16">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="16" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1384.83,524.847) scale(1,1) translate(0,0)" writing-mode="lr" x="1384.28" xml:space="preserve" y="529.54" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126839058436" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="18">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="18" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,447.611,479.847) scale(1,1) translate(0,0)" writing-mode="lr" x="447.06" xml:space="preserve" y="484.57" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126842204164" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="19" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,921.5,512.403) scale(1,1) translate(0,0)" writing-mode="lr" x="920.95" xml:space="preserve" y="517.11" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126844760068" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="20">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="20" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1385.83,539.014) scale(1,1) translate(0,0)" writing-mode="lr" x="1385.28" xml:space="preserve" y="543.7" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126839123972" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="22">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="22" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,452.611,502.125) scale(1,1) translate(-1.77463e-13,0)" writing-mode="lr" x="452.06" xml:space="preserve" y="506.81" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126842269700" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="23">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="23" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,921.5,534.236) scale(1,1) translate(0,5.80431e-14)" writing-mode="lr" x="920.95" xml:space="preserve" y="538.92" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126844825604" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="24">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="24" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1452.39,452.986) scale(1,1) translate(0,-9.82362e-14)" writing-mode="lr" x="1451.84" xml:space="preserve" y="457.65" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126839189508" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="26">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="26" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,440.167,395.764) scale(1,1) translate(0,-8.49136e-14)" writing-mode="lr" x="439.61" xml:space="preserve" y="400.48" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126842335236" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="27">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="27" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,941.611,437.319) scale(1,1) translate(0,-1.88775e-13)" writing-mode="lr" x="941.0599999999999" xml:space="preserve" y="442.01" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126844891140" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="28">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="28" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1385.83,561.403) scale(1,1) translate(0,6.08926e-14)" writing-mode="lr" x="1385.28" xml:space="preserve" y="566.11" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126839517188" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="30">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="30" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,448.611,520.069) scale(1,1) translate(-1.75687e-13,-1.13101e-13)" writing-mode="lr" x="448.06" xml:space="preserve" y="524.74" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126842662916" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="31">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="31" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,921.5,555.625) scale(1,1) translate(0,6.03129e-14)" writing-mode="lr" x="920.95" xml:space="preserve" y="560.3200000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126845218820" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="214">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="214" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,334.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="339.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126849150980" ObjectName="F"/>
   </metadata>
  </g>
  <g id="244">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="244" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,262.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="267.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126859636740" ObjectName="F"/>
   </metadata>
  </g>
  <g id="245">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="245" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,263.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="268.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126859702276" ObjectName="F"/>
   </metadata>
  </g>
  <g id="66">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="66" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,287.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="292.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126859505668" ObjectName="F"/>
   </metadata>
  </g>
  <g id="65">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,288.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="293.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126859571204" ObjectName="F"/>
   </metadata>
  </g>
  <g id="234">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="234" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,159.611,311.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="316.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126859505668" ObjectName="F"/>
   </metadata>
  </g>
  <g id="51">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,339.611,310.167) scale(1,1) translate(0,0)" writing-mode="lr" x="339.77" xml:space="preserve" y="315.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126859505668" ObjectName="F"/>
   </metadata>
  </g>
  <g id="2">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="2" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,572.5,882) scale(1,1) translate(0,0)" writing-mode="lr" x="571.92" xml:space="preserve" y="888.28" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126850920452" ObjectName="P"/>
   </metadata>
  </g>
  <g id="3">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="3" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,574.5,900) scale(1,1) translate(0,0)" writing-mode="lr" x="573.92" xml:space="preserve" y="906.28" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126850985988" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="8" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,577.5,920) scale(1,1) translate(0,0)" writing-mode="lr" x="576.92" xml:space="preserve" y="926.28" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126851051524" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="10">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="10" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,965.046,846) scale(1,1) translate(0,0)" writing-mode="lr" x="964.47" xml:space="preserve" y="852.28" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126851575812" ObjectName="P"/>
   </metadata>
  </g>
  <g id="13">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="13" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,965.046,872) scale(1,1) translate(0,0)" writing-mode="lr" x="964.47" xml:space="preserve" y="878.28" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126851641348" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="17">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="17" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,966.046,902) scale(1,1) translate(0,0)" writing-mode="lr" x="965.47" xml:space="preserve" y="908.28" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126851706884" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="21">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="21" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1464.5,827) scale(1,1) translate(0,0)" writing-mode="lr" x="1463.92" xml:space="preserve" y="833.28" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126853869572" ObjectName="P"/>
   </metadata>
  </g>
  <g id="25">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="25" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1464.5,860) scale(1,1) translate(0,0)" writing-mode="lr" x="1463.92" xml:space="preserve" y="866.28" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126853935108" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="29">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="29" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1467.5,890) scale(1,1) translate(0,0)" writing-mode="lr" x="1466.92" xml:space="preserve" y="896.28" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126854000644" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="32">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="32" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,442.624,280.238) scale(1,1) translate(0,0)" writing-mode="lr" x="442.16" xml:space="preserve" y="285.02" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126849019910" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="213">
   <use height="30" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="730"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374892269571" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" y="178.37"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="731"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562952117420036" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" y="178.37"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="137">
   <use class="kv10" height="30" transform="rotate(0,574,821) scale(1.5,1.5) translate(-183.833,-266.167)" width="30" x="551.5" xlink:href="#Generator:发电机_0" y="798.5" zvalue="772"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450054651909" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192450054651909"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,574,821) scale(1.5,1.5) translate(-183.833,-266.167)" width="30" x="551.5" y="798.5"/></g>
  <g id="141">
   <use class="kv10" height="30" transform="rotate(0,1055.55,817) scale(1.5,1.5) translate(-344.349,-264.833)" width="30" x="1033.045767195767" xlink:href="#Generator:发电机_0" y="794.5" zvalue="775"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450054717445" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192450054717445"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1055.55,817) scale(1.5,1.5) translate(-344.349,-264.833)" width="30" x="1033.045767195767" y="794.5"/></g>
  <g id="163">
   <use class="kv10" height="30" transform="rotate(0,1547,835) scale(1.5,1.5) translate(-508.167,-270.833)" width="30" x="1524.5" xlink:href="#Generator:发电机_0" y="812.5" zvalue="797"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450055045125" ObjectName="#3发电机"/>
   <cge:TPSR_Ref TObjectID="6192450055045125"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1547,835) scale(1.5,1.5) translate(-508.167,-270.833)" width="30" x="1524.5" y="812.5"/></g>
 </g>
</svg>