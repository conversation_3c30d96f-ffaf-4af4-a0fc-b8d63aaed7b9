<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549587607554" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀（规范制图）_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000004" y2="0.9166666666666679"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀（规范制图）_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.89694619969018" y2="15.89694619969018"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.73157590710537" y2="17.73157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.48348701738202" y2="19.48348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.05" x2="5.05" y1="3.566666666666666" y2="15.81666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀（规范制图）_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Accessory:腊撒线路PT_0" viewBox="0,0,12,32">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="0.6666666666666661"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="3" y1="27" y2="25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="27" y2="30"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="13.66666666666667" y2="1"/>
   <path d="M 6 15 L 3 20 L 9 20 L 6 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="9" y1="27" y2="25"/>
   <rect fill-opacity="0" height="6" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,8) scale(1,1) translate(0,0)" width="4" x="4" y="5"/>
   <ellipse cx="5.9" cy="18.6" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.9" cy="26.28" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-y不带中性点_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="1" x="10" xlink:href="#terminal" y="2.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.02838478538902" x2="10.02838478538902" y1="6.047799292079558" y2="8.646787392096492"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.434837295917547" x2="10.028384785389" y1="11.24577549211341" y2="8.646787392096474"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.62193227486047" x2="10.02838478538902" y1="11.24577549211341" y2="8.646787392096474"/>
   <ellipse cx="10.03" cy="9.050000000000001" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-y不带中性点_1" viewBox="0,0,20,30">
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.434837295917547" x2="10.028384785389" y1="22.74577549211341" y2="20.14678739209647"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.02838478538902" x2="10.02838478538902" y1="17.54779929207956" y2="20.14678739209649"/>
   <use terminal-index="1" type="1" x="10.08333333333333" xlink:href="#terminal" y="26.16666666666667"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.62193227486047" x2="10.02838478538902" y1="22.74577549211341" y2="20.14678739209647"/>
   <ellipse cx="10.03" cy="19.8" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV大湾电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="269.25" x="43.29" xlink:href="logo.png" y="50.86"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,177.911,80.8571) scale(1,1) translate(0,0)" writing-mode="lr" x="177.91" xml:space="preserve" y="84.36" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,179.619,80.5475) scale(1,1) translate(6.80408e-15,0)" writing-mode="lr" x="179.62" xml:space="preserve" y="89.55" zvalue="3">10kV大湾电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="6" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,73.4375,361) scale(1,1) translate(0,0)" width="72.88" x="37" y="349" zvalue="113"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,73.4375,361) scale(1,1) translate(0,0)" writing-mode="lr" x="73.44" xml:space="preserve" y="365.5" zvalue="113">信号一览</text>
  <line fill="none" id="30" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="377.2857142857144" x2="377.2857142857144" y1="18.85714285714289" y2="1048.857142857143" zvalue="4"/>
  <line fill="none" id="28" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.285714285714675" x2="370.2857142857142" y1="154.7276354712253" y2="154.7276354712253" zvalue="6"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="185.2857142857144" y1="166.8571428571429" y2="166.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="185.2857142857144" y1="192.8571428571429" y2="192.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="4.285714285714448" y1="166.8571428571429" y2="192.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="185.2857142857144" y1="166.8571428571429" y2="192.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="366.2857142857144" y1="166.8571428571429" y2="166.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="366.2857142857144" y1="192.8571428571429" y2="192.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="185.2857142857144" y1="166.8571428571429" y2="192.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.2857142857144" x2="366.2857142857144" y1="166.8571428571429" y2="192.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="185.2857142857144" y1="192.8571428571429" y2="192.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="185.2857142857144" y1="217.1071428571429" y2="217.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="4.285714285714448" y1="192.8571428571429" y2="217.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="185.2857142857144" y1="192.8571428571429" y2="217.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="366.2857142857144" y1="192.8571428571429" y2="192.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="366.2857142857144" y1="217.1071428571429" y2="217.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="185.2857142857144" y1="192.8571428571429" y2="217.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.2857142857144" x2="366.2857142857144" y1="192.8571428571429" y2="217.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="185.2857142857144" y1="217.1071428571429" y2="217.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="185.2857142857144" y1="239.8571428571429" y2="239.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="4.285714285714448" y1="217.1071428571429" y2="239.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="185.2857142857144" y1="217.1071428571429" y2="239.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="366.2857142857144" y1="217.1071428571429" y2="217.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="366.2857142857144" y1="239.8571428571429" y2="239.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="185.2857142857144" y1="217.1071428571429" y2="239.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.2857142857144" x2="366.2857142857144" y1="217.1071428571429" y2="239.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="185.2857142857144" y1="239.8571428571429" y2="239.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="185.2857142857144" y1="262.6071428571429" y2="262.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="4.285714285714448" y1="239.8571428571429" y2="262.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="185.2857142857144" y1="239.8571428571429" y2="262.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="366.2857142857144" y1="239.8571428571429" y2="239.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="366.2857142857144" y1="262.6071428571429" y2="262.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="185.2857142857144" y1="239.8571428571429" y2="262.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.2857142857144" x2="366.2857142857144" y1="239.8571428571429" y2="262.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="185.2857142857144" y1="262.6071428571429" y2="262.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="185.2857142857144" y1="285.3571428571429" y2="285.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="4.285714285714448" y1="262.6071428571429" y2="285.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="185.2857142857144" y1="262.6071428571429" y2="285.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="366.2857142857144" y1="262.6071428571429" y2="262.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="366.2857142857144" y1="285.3571428571429" y2="285.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="185.2857142857144" y1="262.6071428571429" y2="285.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.2857142857144" x2="366.2857142857144" y1="262.6071428571429" y2="285.3571428571429"/>
  <line fill="none" id="26" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.285714285714675" x2="370.2857142857142" y1="624.7276354712253" y2="624.7276354712253" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.285714285714448" x2="93.28571428571445" y1="939.8571428571431" y2="939.8571428571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.285714285714448" x2="93.28571428571445" y1="979.0204428571431" y2="979.0204428571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.285714285714448" x2="3.285714285714448" y1="939.8571428571431" y2="979.0204428571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.28571428571445" x2="93.28571428571445" y1="939.8571428571431" y2="979.0204428571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.28571428571445" x2="363.2857142857144" y1="939.8571428571431" y2="939.8571428571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.28571428571445" x2="363.2857142857144" y1="979.0204428571431" y2="979.0204428571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.28571428571445" x2="93.28571428571445" y1="939.8571428571431" y2="979.0204428571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.2857142857144" x2="363.2857142857144" y1="939.8571428571431" y2="979.0204428571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.285714285714448" x2="93.28571428571445" y1="979.0204128571431" y2="979.0204128571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.285714285714448" x2="93.28571428571445" y1="1006.938812857143" y2="1006.938812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.285714285714448" x2="3.285714285714448" y1="979.0204128571431" y2="1006.938812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.28571428571445" x2="93.28571428571445" y1="979.0204128571431" y2="1006.938812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.28571428571445" x2="183.2857142857144" y1="979.0204128571431" y2="979.0204128571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.28571428571445" x2="183.2857142857144" y1="1006.938812857143" y2="1006.938812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.28571428571445" x2="93.28571428571445" y1="979.0204128571431" y2="1006.938812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.2857142857144" x2="183.2857142857144" y1="979.0204128571431" y2="1006.938812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.2857142857146" x2="273.2857142857146" y1="979.0204128571431" y2="979.0204128571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.2857142857146" x2="273.2857142857146" y1="1006.938812857143" y2="1006.938812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.2857142857146" x2="183.2857142857146" y1="979.0204128571431" y2="1006.938812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.2857142857146" x2="273.2857142857146" y1="979.0204128571431" y2="1006.938812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.2857142857144" x2="363.2857142857144" y1="979.0204128571431" y2="979.0204128571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.2857142857144" x2="363.2857142857144" y1="1006.938812857143" y2="1006.938812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.2857142857144" x2="273.2857142857144" y1="979.0204128571431" y2="1006.938812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.2857142857144" x2="363.2857142857144" y1="979.0204128571431" y2="1006.938812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.285714285714448" x2="93.28571428571445" y1="1006.938742857143" y2="1006.938742857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.285714285714448" x2="93.28571428571445" y1="1034.857142857143" y2="1034.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.285714285714448" x2="3.285714285714448" y1="1006.938742857143" y2="1034.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.28571428571445" x2="93.28571428571445" y1="1006.938742857143" y2="1034.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.28571428571445" x2="183.2857142857144" y1="1006.938742857143" y2="1006.938742857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.28571428571445" x2="183.2857142857144" y1="1034.857142857143" y2="1034.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.28571428571445" x2="93.28571428571445" y1="1006.938742857143" y2="1034.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.2857142857144" x2="183.2857142857144" y1="1006.938742857143" y2="1034.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.2857142857146" x2="273.2857142857146" y1="1006.938742857143" y2="1006.938742857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.2857142857146" x2="273.2857142857146" y1="1034.857142857143" y2="1034.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.2857142857146" x2="183.2857142857146" y1="1006.938742857143" y2="1034.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.2857142857146" x2="273.2857142857146" y1="1006.938742857143" y2="1034.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.2857142857144" x2="363.2857142857144" y1="1006.938742857143" y2="1006.938742857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.2857142857144" x2="363.2857142857144" y1="1034.857142857143" y2="1034.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.2857142857144" x2="273.2857142857144" y1="1006.938742857143" y2="1034.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.2857142857144" x2="363.2857142857144" y1="1006.938742857143" y2="1034.857142857143"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.2857,959.857) scale(1,1) translate(0,0)" writing-mode="lr" x="48.29" xml:space="preserve" y="965.86" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,45.2857,993.857) scale(1,1) translate(0,0)" writing-mode="lr" x="45.29" xml:space="preserve" y="999.86" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.286,993.857) scale(1,1) translate(0,0)" writing-mode="lr" x="227.29" xml:space="preserve" y="999.86" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44.2857,1021.86) scale(1,1) translate(0,0)" writing-mode="lr" x="44.29" xml:space="preserve" y="1027.86" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.286,1021.86) scale(1,1) translate(0,0)" writing-mode="lr" x="226.29" xml:space="preserve" y="1027.86" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,68.7857,654.357) scale(1,1) translate(0,-2.82155e-13)" writing-mode="lr" x="68.78571428571445" xml:space="preserve" y="658.8571428571429" zvalue="18">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,228.34,961.857) scale(1,1) translate(0,0)" writing-mode="lr" x="228.34" xml:space="preserve" y="967.86" zvalue="26">DaWan-01-2014</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,138.34,1022.86) scale(1,1) translate(0,0)" writing-mode="lr" x="138.34" xml:space="preserve" y="1028.86" zvalue="27">杨立超</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,42.2857,180.857) scale(1,1) translate(0,0)" writing-mode="lr" x="42.29" xml:space="preserve" y="186.36" zvalue="29">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,222.286,180.857) scale(1,1) translate(0,0)" writing-mode="lr" x="222.29" xml:space="preserve" y="186.36" zvalue="30">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49.4732,252.857) scale(1,1) translate(0,0)" writing-mode="lr" x="49.47" xml:space="preserve" y="257.36" zvalue="31">10kV#1变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.369,207.552) scale(1,1) translate(0,0)" writing-mode="lr" x="234.37" xml:space="preserve" y="212.05" zvalue="32">0.4kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="105" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,511.286,684.143) scale(1,1) translate(0,0)" writing-mode="lr" x="511.29" xml:space="preserve" y="688.64" zvalue="35">0.4kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="108" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,781.429,21.4286) scale(1,1) translate(0,0)" writing-mode="lr" x="781.4299999999999" xml:space="preserve" y="25.93" zvalue="36">10kV街桥线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,708.571,1044.29) scale(1,1) translate(0,0)" writing-mode="lr" x="708.5700000000001" xml:space="preserve" y="1048.79" zvalue="38">#1发电机     500KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,672.286,774.143) scale(1,1) translate(0,0)" writing-mode="lr" x="672.29" xml:space="preserve" y="778.64" zvalue="40">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,731.286,868.429) scale(1,1) translate(0,0)" writing-mode="lr" x="731.29" xml:space="preserve" y="872.9299999999999" zvalue="43">401</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="109" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,810.571,322.714) scale(1,1) translate(0,0)" writing-mode="lr" x="810.5700000000001" xml:space="preserve" y="327.21" zvalue="46">051</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="110" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,763.589,224.143) scale(1,1) translate(0,0)" writing-mode="lr" x="763.59" xml:space="preserve" y="228.64" zvalue="49">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="106" stroke="rgb(255,255,255)" text-anchor="middle" x="728.3984375" xml:space="preserve" y="501.2614087301587" zvalue="56">#1主变        </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="106" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="728.3984375" xml:space="preserve" y="517.2614087301588" zvalue="56">1250KVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="111" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,964.429,188.429) scale(1,1) translate(0,0)" writing-mode="lr" x="964.4299999999999" xml:space="preserve" y="192.93" zvalue="62">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="112" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1080.71,177) scale(1,1) translate(0,0)" writing-mode="lr" x="1080.71" xml:space="preserve" y="181.5" zvalue="65">97</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1102.57,1046) scale(1,1) translate(0,0)" writing-mode="lr" x="1102.57" xml:space="preserve" y="1050.5" zvalue="77">#2发电机           500KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1066.29,775.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1066.29" xml:space="preserve" y="780.36" zvalue="79">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1125.29,870.143) scale(1,1) translate(0,0)" writing-mode="lr" x="1125.29" xml:space="preserve" y="874.64" zvalue="82">402</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" x="1638.765625" xml:space="preserve" y="1002.583580123053" zvalue="91">#3发电机          </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1638.765625" xml:space="preserve" y="1018.583580123053" zvalue="91">500KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1603.71,727) scale(1,1) translate(0,0)" writing-mode="lr" x="1603.71" xml:space="preserve" y="731.5" zvalue="93">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1662.71,821.286) scale(1,1) translate(0,0)" writing-mode="lr" x="1662.71" xml:space="preserve" y="825.79" zvalue="96">403</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="107" stroke="rgb(255,255,255)" text-anchor="middle" x="1590.25" xml:space="preserve" y="489.9563492063492" zvalue="105">#2主变        </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="107" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1590.25" xml:space="preserve" y="505.9563492063492" zvalue="105">1250KVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,189.211,361.591) scale(1,1) translate(0,0)" writing-mode="lr" x="189.21" xml:space="preserve" y="366.09" zvalue="109">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,294.211,361.591) scale(1,1) translate(0,0)" writing-mode="lr" x="294.21" xml:space="preserve" y="366.09" zvalue="110">通道</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="37" y="349" zvalue="113"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="33">
   <path class="v400" d="M 555.71 689.43 L 1294.29 689.43" stroke-width="6" zvalue="34"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674245083140" ObjectName="0.4kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674245083140"/></metadata>
  <path d="M 555.71 689.43 L 1294.29 689.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="36">
   <use class="v400" height="30" transform="rotate(0,705.714,1000.86) scale(2.14286,2.14286) translate(-359.238,-516.648)" width="30" x="673.5714285714284" xlink:href="#Generator:发电机_0" y="968.7142857142856" zvalue="37"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449817739270" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449817739270"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,705.714,1000.86) scale(2.14286,2.14286) translate(-359.238,-516.648)" width="30" x="673.5714285714284" y="968.7142857142856"/></g>
  <g id="87">
   <use class="v400" height="30" transform="rotate(0,1099.71,1002.57) scale(2.14286,2.14286) translate(-569.371,-517.562)" width="30" x="1067.571428571428" xlink:href="#Generator:发电机_0" y="970.4285714285712" zvalue="76"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449818656773" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449818656773"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1099.71,1002.57) scale(2.14286,2.14286) translate(-569.371,-517.562)" width="30" x="1067.571428571428" y="970.4285714285712"/></g>
  <g id="100">
   <use class="v400" height="30" transform="rotate(0,1637.14,953.714) scale(2.14286,2.14286) translate(-856,-491.505)" width="30" x="1605" xlink:href="#Generator:发电机_0" y="921.5714285714284" zvalue="90"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449818918917" ObjectName="#3发电机"/>
   <cge:TPSR_Ref TObjectID="6192449818918917"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1637.14,953.714) scale(2.14286,2.14286) translate(-856,-491.505)" width="30" x="1605" y="921.5714285714284"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="38">
   <use class="v400" height="30" transform="rotate(0,704.286,775.143) scale(1.42857,1.04762) translate(-208.071,-34.5195)" width="15" x="693.5714285714286" xlink:href="#Disconnector:刀闸_0" y="759.4285714285713" zvalue="39"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449817804806" ObjectName="#1发电机4011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449817804806"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,704.286,775.143) scale(1.42857,1.04762) translate(-208.071,-34.5195)" width="15" x="693.5714285714286" y="759.4285714285713"/></g>
  <g id="47">
   <use class="kv10" height="30" transform="rotate(0,781.303,225.143) scale(1.42857,1.04762) translate(-231.177,-9.51948)" width="15" x="770.5888899273417" xlink:href="#Disconnector:刀闸_0" y="209.4285714285715" zvalue="48"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449817870342" ObjectName="10kV街桥线0516"/>
   <cge:TPSR_Ref TObjectID="6192449817870342"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,781.303,225.143) scale(1.42857,1.04762) translate(-231.177,-9.51948)" width="15" x="770.5888899273417" y="209.4285714285715"/></g>
  <g id="59">
   <use class="kv10" height="30" transform="rotate(0,982.143,189.429) scale(1.42857,1.04762) translate(-291.429,-7.8961)" width="15" x="971.4285714285714" xlink:href="#Disconnector:刀闸_0" y="173.7142857142858" zvalue="61"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449818001414" ObjectName="10kV电压互感器0519隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449818001414"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,982.143,189.429) scale(1.42857,1.04762) translate(-291.429,-7.8961)" width="15" x="971.4285714285714" y="173.7142857142858"/></g>
  <g id="86">
   <use class="v400" height="30" transform="rotate(0,1098.29,776.857) scale(1.42857,1.04762) translate(-326.271,-34.5974)" width="15" x="1087.571428571428" xlink:href="#Disconnector:刀闸_0" y="761.1428571428571" zvalue="78"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449818591238" ObjectName="#2发电机4021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449818591238"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1098.29,776.857) scale(1.42857,1.04762) translate(-326.271,-34.5974)" width="15" x="1087.571428571428" y="761.1428571428571"/></g>
  <g id="99">
   <use class="v400" height="30" transform="rotate(0,1635.71,728) scale(1.42857,1.04762) translate(-487.5,-32.3766)" width="15" x="1625" xlink:href="#Disconnector:刀闸_0" y="712.2857142857142" zvalue="92"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449818853381" ObjectName="#3发电机4031隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449818853381"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1635.71,728) scale(1.42857,1.04762) translate(-487.5,-32.3766)" width="15" x="1625" y="712.2857142857142"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="40">
   <path class="v400" d="M 704.41 759.95 L 704.41 689.43" stroke-width="1" zvalue="41"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@0" LinkObjectIDznd="33@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 704.41 759.95 L 704.41 689.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="v400" d="M 705.71 969.25 L 705.71 887.85" stroke-width="1" zvalue="43"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="36@0" LinkObjectIDznd="41@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 705.71 969.25 L 705.71 887.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="v400" d="M 704.21 850.98 L 704.21 790.59" stroke-width="1" zvalue="44"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41@0" LinkObjectIDznd="38@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 704.21 850.98 L 704.21 790.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="kv10" d="M 781.43 98.45 L 781.43 209.95" stroke-width="1" zvalue="49"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="34@0" LinkObjectIDznd="47@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 781.43 98.45 L 781.43 209.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="kv10" d="M 781.39 240.59 L 781.39 305.26" stroke-width="1" zvalue="50"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@1" LinkObjectIDznd="44@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 781.39 240.59 L 781.39 305.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="55">
   <path class="v400" d="M 784.46 533.16 L 784.46 689.43" stroke-width="1" zvalue="56"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="54@1" LinkObjectIDznd="33@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 784.46 533.16 L 784.46 689.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="kv10" d="M 784.29 477.4 L 784.29 342.13" stroke-width="1" zvalue="57"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="54@0" LinkObjectIDznd="44@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 784.29 477.4 L 784.29 342.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="kv10" d="M 984.18 247.43 L 984.18 204.87" stroke-width="1" zvalue="62"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="57@0" LinkObjectIDznd="59@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 984.18 247.43 L 984.18 204.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="kv10" d="M 982.27 174.23 L 982.27 166.57 L 781.43 166.57" stroke-width="1" zvalue="63"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="59@0" LinkObjectIDznd="48" MaxPinNum="2"/>
   </metadata>
  <path d="M 982.27 174.23 L 982.27 166.57 L 781.43 166.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="kv10" d="M 1063.07 226.48 L 984.18 226.48" stroke-width="1" zvalue="65"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="62@0" LinkObjectIDznd="60" MaxPinNum="2"/>
   </metadata>
  <path d="M 1063.07 226.48 L 984.18 226.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="v400" d="M 1136.52 597.91 L 1136.52 689.43" stroke-width="1" zvalue="67"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="64@0" LinkObjectIDznd="33@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1136.52 597.91 L 1136.52 689.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="67">
   <path class="v400" d="M 1202.91 590.28 L 1202.91 633.71 L 1136.52 633.71" stroke-width="1" zvalue="69"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="66@0" LinkObjectIDznd="65" MaxPinNum="2"/>
   </metadata>
  <path d="M 1202.91 590.28 L 1202.91 633.71 L 1136.52 633.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="v400" d="M 544.03 952.18 L 544.03 912.29 L 705.71 912.29" stroke-width="1" zvalue="71"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="68@0" LinkObjectIDznd="42" MaxPinNum="2"/>
   </metadata>
  <path d="M 544.03 952.18 L 544.03 912.29 L 705.71 912.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="v400" d="M 817.95 939.52 L 817.95 910.86 L 705.71 910.86" stroke-width="1" zvalue="74"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70@0" LinkObjectIDznd="42" MaxPinNum="2"/>
   </metadata>
  <path d="M 817.95 939.52 L 817.95 910.86 L 705.71 910.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="v400" d="M 1098.41 761.66 L 1098.41 689.43" stroke-width="1" zvalue="80"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="86@0" LinkObjectIDznd="33@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1098.41 761.66 L 1098.41 689.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="v400" d="M 1099.71 970.96 L 1099.71 889.56" stroke-width="1" zvalue="83"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="87@0" LinkObjectIDznd="84@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1099.71 970.96 L 1099.71 889.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="82">
   <path class="v400" d="M 1098.21 852.69 L 1098.21 792.3" stroke-width="1" zvalue="84"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="84@0" LinkObjectIDznd="86@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1098.21 852.69 L 1098.21 792.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="v400" d="M 938.03 953.89 L 938.03 914 L 1099.71 914" stroke-width="1" zvalue="86"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="81@0" LinkObjectIDznd="83" MaxPinNum="2"/>
   </metadata>
  <path d="M 938.03 953.89 L 938.03 914 L 1099.71 914" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="v400" d="M 1211.95 941.23 L 1211.95 912.57 L 1099.71 912.57" stroke-width="1" zvalue="88"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="79@0" LinkObjectIDznd="83" MaxPinNum="2"/>
   </metadata>
  <path d="M 1211.95 941.23 L 1211.95 912.57 L 1099.71 912.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="v400" d="M 1635.84 712.81 L 1635.84 519.44" stroke-width="1" zvalue="94"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="101@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1635.84 712.81 L 1635.84 519.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="v400" d="M 1637.14 922.11 L 1637.14 840.7" stroke-width="1" zvalue="97"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="100@0" LinkObjectIDznd="97@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1637.14 922.11 L 1637.14 840.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="v400" d="M 1635.64 803.84 L 1635.64 743.45" stroke-width="1" zvalue="98"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="97@0" LinkObjectIDznd="99@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1635.64 803.84 L 1635.64 743.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="v400" d="M 1475.45 905.04 L 1475.45 865.14 L 1637.14 865.14" stroke-width="1" zvalue="100"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="94@0" LinkObjectIDznd="96" MaxPinNum="2"/>
   </metadata>
  <path d="M 1475.45 905.04 L 1475.45 865.14 L 1637.14 865.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="v400" d="M 1749.38 892.37 L 1749.38 863.71 L 1637.14 863.71" stroke-width="1" zvalue="102"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92@0" LinkObjectIDznd="96" MaxPinNum="2"/>
   </metadata>
  <path d="M 1749.38 892.37 L 1749.38 863.71 L 1637.14 863.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="104">
   <path class="kv10" d="M 1635.82 463.69 L 1635.82 428 L 784.29 428" stroke-width="1" zvalue="107"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="101@0" LinkObjectIDznd="56" MaxPinNum="2"/>
   </metadata>
  <path d="M 1635.82 463.69 L 1635.82 428 L 784.29 428" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="41">
   <use class="v400" height="20" transform="rotate(0,704.286,869.429) scale(2.14286,1.92857) translate(-369.905,-409.328)" width="10" x="693.5714285714287" xlink:href="#Breaker:开关_0" y="850.1428571428571" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924515594245" ObjectName="#1发电机401断路器"/>
   <cge:TPSR_Ref TObjectID="6473924515594245"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,704.286,869.429) scale(2.14286,1.92857) translate(-369.905,-409.328)" width="10" x="693.5714285714287" y="850.1428571428571"/></g>
  <g id="44">
   <use class="kv10" height="20" transform="rotate(0,782.857,323.714) scale(2.14286,1.92857) translate(-411.81,-146.577)" width="10" x="772.1428571428571" xlink:href="#Breaker:开关_0" y="304.4285714285716" zvalue="45"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924515659781" ObjectName="10kV街桥线051断路器"/>
   <cge:TPSR_Ref TObjectID="6473924515659781"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,782.857,323.714) scale(2.14286,1.92857) translate(-411.81,-146.577)" width="10" x="772.1428571428571" y="304.4285714285716"/></g>
  <g id="84">
   <use class="v400" height="20" transform="rotate(0,1098.29,871.143) scale(2.14286,1.92857) translate(-580.038,-410.153)" width="10" x="1087.571428571429" xlink:href="#Breaker:开关_0" y="851.8571428571429" zvalue="81"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924515725317" ObjectName="#2发电机402断路器"/>
   <cge:TPSR_Ref TObjectID="6473924515725317"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1098.29,871.143) scale(2.14286,1.92857) translate(-580.038,-410.153)" width="10" x="1087.571428571429" y="851.8571428571429"/></g>
  <g id="97">
   <use class="v400" height="20" transform="rotate(0,1635.71,822.286) scale(2.14286,1.92857) translate(-866.667,-386.63)" width="10" x="1625" xlink:href="#Breaker:开关_0" y="803" zvalue="95"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924515790853" ObjectName="#3发电机403断路器"/>
   <cge:TPSR_Ref TObjectID="6473924515790853"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1635.71,822.286) scale(2.14286,1.92857) translate(-866.667,-386.63)" width="10" x="1625" y="803"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="54">
   <g id="540">
    <use class="kv10" height="30" transform="rotate(0,784.286,506.571) scale(2.14286,2.38095) translate(-406.857,-273.097)" width="20" x="762.86" xlink:href="#PowerTransformer2:Y-y不带中性点_0" y="470.86" zvalue="55"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874439524356" ObjectName="10"/>
    </metadata>
   </g>
   <g id="541">
    <use class="v400" height="30" transform="rotate(0,784.286,506.571) scale(2.14286,2.38095) translate(-406.857,-273.097)" width="20" x="762.86" xlink:href="#PowerTransformer2:Y-y不带中性点_1" y="470.86" zvalue="55"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874439589892" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399452000259" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399452000259"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,784.286,506.571) scale(2.14286,2.38095) translate(-406.857,-273.097)" width="20" x="762.86" y="470.86"/></g>
  <g id="101">
   <g id="1010">
    <use class="kv10" height="30" transform="rotate(0,1635.82,492.857) scale(2.14286,2.38095) translate(-861.009,-265.143)" width="20" x="1614.39" xlink:href="#PowerTransformer2:Y-y不带中性点_0" y="457.14" zvalue="104"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874439655428" ObjectName="10"/>
    </metadata>
   </g>
   <g id="1011">
    <use class="v400" height="30" transform="rotate(0,1635.82,492.857) scale(2.14286,2.38095) translate(-861.009,-265.143)" width="20" x="1614.39" xlink:href="#PowerTransformer2:Y-y不带中性点_1" y="457.14" zvalue="104"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874439720964" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399452065795" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399452065795"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1635.82,492.857) scale(2.14286,2.38095) translate(-861.009,-265.143)" width="20" x="1614.39" y="457.14"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="57">
   <use class="kv10" height="18" transform="rotate(0,987.5,277.286) scale(3.57143,3.57143) translate(-691.714,-176.503)" width="15" x="960.7142857142858" xlink:href="#Accessory:PT8_0" y="245.1428571428572" zvalue="58"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449817935878" ObjectName="10kV电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,987.5,277.286) scale(3.57143,3.57143) translate(-691.714,-176.503)" width="15" x="960.7142857142858" y="245.1428571428572"/></g>
  <g id="64">
   <use class="v400" height="32" transform="rotate(0,1136.52,563) scale(2.27679,-2.27679) translate(-629.681,-789.85)" width="12" x="1122.857142857143" xlink:href="#Accessory:腊撒线路PT_0" y="526.5714285714287" zvalue="66"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449818198022" ObjectName="0.4kV母线PT"/>
   </metadata>
  <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(0,1136.52,563) scale(2.27679,-2.27679) translate(-629.681,-789.85)" width="12" x="1122.857142857143" y="526.5714285714287"/></g>
  <g id="66">
   <use class="v400" height="26" transform="rotate(0,1202.86,569.667) scale(1.66667,-1.66667) translate(-477.143,-902.8)" width="12" x="1192.857142857143" xlink:href="#Accessory:避雷器1_0" y="548" zvalue="68"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449818263558" ObjectName="0.4kV母线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1202.86,569.667) scale(1.66667,-1.66667) translate(-477.143,-902.8)" width="12" x="1192.857142857143" y="548"/></g>
  <g id="68">
   <use class="v400" height="26" transform="rotate(0,543.956,978) scale(2.08791,2.08791) translate(-276.902,-495.447)" width="12" x="531.4285714285713" xlink:href="#Accessory:避雷器1_0" y="950.8571428571428" zvalue="70"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449818329094" ObjectName="#1发电机避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,543.956,978) scale(2.08791,2.08791) translate(-276.902,-495.447)" width="12" x="531.4285714285713" y="950.8571428571428"/></g>
  <g id="70">
   <use class="v400" height="32" transform="rotate(0,817.946,974.429) scale(2.27679,2.27679) translate(-451.031,-526.016)" width="12" x="804.2857142857143" xlink:href="#Accessory:腊撒线路PT_0" y="937.9999999999999" zvalue="73"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449818394630" ObjectName="#1发电机避雷器2"/>
   </metadata>
  <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(0,817.946,974.429) scale(2.27679,2.27679) translate(-451.031,-526.016)" width="12" x="804.2857142857143" y="937.9999999999999"/></g>
  <g id="81">
   <use class="v400" height="26" transform="rotate(0,937.956,979.714) scale(2.08791,2.08791) translate(-482.197,-496.34)" width="12" x="925.4285714285714" xlink:href="#Accessory:避雷器1_0" y="952.5714285714286" zvalue="85"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449818525702" ObjectName="#2发电机避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,937.956,979.714) scale(2.08791,2.08791) translate(-482.197,-496.34)" width="12" x="925.4285714285714" y="952.5714285714286"/></g>
  <g id="79">
   <use class="v400" height="32" transform="rotate(0,1211.95,976.143) scale(2.27679,2.27679) translate(-671.98,-526.977)" width="12" x="1198.285714285714" xlink:href="#Accessory:腊撒线路PT_0" y="939.7142857142856" zvalue="87"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449818460166" ObjectName="#2发电机避雷器2"/>
   </metadata>
  <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(0,1211.95,976.143) scale(2.27679,2.27679) translate(-671.98,-526.977)" width="12" x="1198.285714285714" y="939.7142857142856"/></g>
  <g id="94">
   <use class="v400" height="26" transform="rotate(0,1475.38,930.857) scale(2.08791,2.08791) translate(-762.226,-470.883)" width="12" x="1462.857142857143" xlink:href="#Accessory:避雷器1_0" y="903.7142857142857" zvalue="99"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449818787845" ObjectName="#3发电机避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1475.38,930.857) scale(2.08791,2.08791) translate(-762.226,-470.883)" width="12" x="1462.857142857143" y="903.7142857142857"/></g>
  <g id="92">
   <use class="v400" height="32" transform="rotate(0,1749.38,927.286) scale(2.27679,2.27679) translate(-973.361,-499.579)" width="12" x="1735.714285714286" xlink:href="#Accessory:腊撒线路PT_0" y="890.8571428571429" zvalue="101"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449818722309" ObjectName="#3发电机避雷器2"/>
   </metadata>
  <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(0,1749.38,927.286) scale(2.27679,2.27679) translate(-973.361,-499.579)" width="12" x="1735.714285714286" y="890.8571428571429"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="62">
   <use class="kv10" height="20" transform="rotate(270,1080.71,226.571) scale(1.85714,1.85714) translate(-494.505,-96)" width="10" x="1071.428571428572" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="208.0000000000002" zvalue="64"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449818132486" ObjectName="10kV电压互感器05197接地刀闸"/>
   <cge:TPSR_Ref TObjectID="6192449818132486"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1080.71,226.571) scale(1.85714,1.85714) translate(-494.505,-96)" width="10" x="1071.428571428572" y="208.0000000000002"/></g>
 </g>
 <g id="StateClass">
  <g id="427">
   <use height="30" transform="rotate(0,321.485,362.107) scale(0.708333,0.665547) translate(128.001,176.951)" width="30" x="310.86" xlink:href="#State:红绿圆(方形)_0" y="352.12" zvalue="111"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,321.485,362.107) scale(0.708333,0.665547) translate(128.001,176.951)" width="30" x="310.86" y="352.12"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,225.86,362.107) scale(0.708333,0.665547) translate(88.6262,176.951)" width="30" x="215.24" xlink:href="#State:红绿圆(方形)_0" y="352.12" zvalue="112"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,225.86,362.107) scale(0.708333,0.665547) translate(88.6262,176.951)" width="30" x="215.24" y="352.12"/></g>
 </g>
</svg>