<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549591670786" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT7_0" viewBox="0,0,14,18">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1.416666666666664"/>
   <ellipse cx="7.15" cy="6.35" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.15" cy="12.03" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变带负荷1_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.98980556533989" x2="12.83661970177291" y1="29.68333333333333" y2="27.75505857643124"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.9898055653399" x2="15.14299142890688" y1="29.68333333333333" y2="27.75505857643124"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.83661970177294" x2="15.1429914289069" y1="27.75505857643125" y2="27.75505857643125"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV邦外电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="64" id="1" preserveAspectRatio="xMidYMid slice" width="298" x="46.71" xlink:href="logo.png" y="41.43"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,195.714,73.4286) scale(1,1) translate(0,0)" writing-mode="lr" x="195.71" xml:space="preserve" y="76.93000000000001" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,198.214,71.1189) scale(1,1) translate(0,0)" writing-mode="lr" x="198.21" xml:space="preserve" y="80.12" zvalue="3">35kV邦外电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="9" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,83.125,181.107) scale(1,1) translate(0,0)" width="72.88" x="46.69" y="169.11" zvalue="25"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.125,181.107) scale(1,1) translate(0,0)" writing-mode="lr" x="83.12" xml:space="preserve" y="185.61" zvalue="25">信号一览</text>
  <line fill="none" id="30" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="380.7142857142851" x2="380.7142857142851" y1="9.428571428571558" y2="1039.428571428572" zvalue="4"/>
  <line fill="none" id="28" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.714285714285779" x2="373.7142857142853" y1="145.2990640426538" y2="145.2990640426538" zvalue="6"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.499999999999773" x2="190.4999999999998" y1="242.8571428571429" y2="242.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.499999999999773" x2="190.4999999999998" y1="268.8571428571429" y2="268.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.499999999999773" x2="9.499999999999773" y1="242.8571428571429" y2="268.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.4999999999998" x2="190.4999999999998" y1="242.8571428571429" y2="268.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.4999999999998" x2="371.4999999999998" y1="242.8571428571429" y2="242.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.4999999999998" x2="371.4999999999998" y1="268.8571428571429" y2="268.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.4999999999998" x2="190.4999999999998" y1="242.8571428571429" y2="268.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371.4999999999998" x2="371.4999999999998" y1="242.8571428571429" y2="268.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.499999999999773" x2="190.4999999999998" y1="268.8571428571429" y2="268.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.499999999999773" x2="190.4999999999998" y1="293.1071428571429" y2="293.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.499999999999773" x2="9.499999999999773" y1="268.8571428571429" y2="293.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.4999999999998" x2="190.4999999999998" y1="268.8571428571429" y2="293.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.4999999999998" x2="371.4999999999998" y1="268.8571428571429" y2="268.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.4999999999998" x2="371.4999999999998" y1="293.1071428571429" y2="293.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.4999999999998" x2="190.4999999999998" y1="268.8571428571429" y2="293.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371.4999999999998" x2="371.4999999999998" y1="268.8571428571429" y2="293.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.499999999999773" x2="190.4999999999998" y1="293.1071428571429" y2="293.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.499999999999773" x2="190.4999999999998" y1="315.8571428571429" y2="315.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.499999999999773" x2="9.499999999999773" y1="293.1071428571429" y2="315.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.4999999999998" x2="190.4999999999998" y1="293.1071428571429" y2="315.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.4999999999998" x2="371.4999999999998" y1="293.1071428571429" y2="293.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.4999999999998" x2="371.4999999999998" y1="315.8571428571429" y2="315.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.4999999999998" x2="190.4999999999998" y1="293.1071428571429" y2="315.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371.4999999999998" x2="371.4999999999998" y1="293.1071428571429" y2="315.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.499999999999773" x2="190.4999999999998" y1="315.8571428571429" y2="315.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.499999999999773" x2="190.4999999999998" y1="338.6071428571429" y2="338.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.499999999999773" x2="9.499999999999773" y1="315.8571428571429" y2="338.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.4999999999998" x2="190.4999999999998" y1="315.8571428571429" y2="338.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.4999999999998" x2="371.4999999999998" y1="315.8571428571429" y2="315.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.4999999999998" x2="371.4999999999998" y1="338.6071428571429" y2="338.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.4999999999998" x2="190.4999999999998" y1="315.8571428571429" y2="338.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371.4999999999998" x2="371.4999999999998" y1="315.8571428571429" y2="338.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.499999999999773" x2="190.4999999999998" y1="338.6071428571429" y2="338.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.499999999999773" x2="190.4999999999998" y1="361.3571428571429" y2="361.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.499999999999773" x2="9.499999999999773" y1="338.6071428571429" y2="361.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.4999999999998" x2="190.4999999999998" y1="338.6071428571429" y2="361.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.4999999999998" x2="371.4999999999998" y1="338.6071428571429" y2="338.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.4999999999998" x2="371.4999999999998" y1="361.3571428571429" y2="361.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.4999999999998" x2="190.4999999999998" y1="338.6071428571429" y2="361.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371.4999999999998" x2="371.4999999999998" y1="338.6071428571429" y2="361.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.499999999999773" x2="190.4999999999998" y1="361.3571428571429" y2="361.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.499999999999773" x2="190.4999999999998" y1="384.1071428571429" y2="384.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.499999999999773" x2="9.499999999999773" y1="361.3571428571429" y2="384.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.4999999999998" x2="190.4999999999998" y1="361.3571428571429" y2="384.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.4999999999998" x2="371.4999999999998" y1="361.3571428571429" y2="361.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.4999999999998" x2="371.4999999999998" y1="384.1071428571429" y2="384.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.4999999999998" x2="190.4999999999998" y1="361.3571428571429" y2="384.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371.4999999999998" x2="371.4999999999998" y1="361.3571428571429" y2="384.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.499999999999773" x2="190.4999999999998" y1="384.1071428571429" y2="384.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.499999999999773" x2="190.4999999999998" y1="406.8571428571429" y2="406.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.499999999999773" x2="9.499999999999773" y1="384.1071428571429" y2="406.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.4999999999998" x2="190.4999999999998" y1="384.1071428571429" y2="406.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.4999999999998" x2="371.4999999999998" y1="384.1071428571429" y2="384.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.4999999999998" x2="371.4999999999998" y1="406.8571428571429" y2="406.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.4999999999998" x2="190.4999999999998" y1="384.1071428571429" y2="406.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371.4999999999998" x2="371.4999999999998" y1="384.1071428571429" y2="406.8571428571429"/>
  <line fill="none" id="26" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="375.4999999999995" y1="488.7276354712253" y2="488.7276354712253" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8.499999999999773" x2="98.49999999999977" y1="927.8571428571428" y2="927.8571428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8.499999999999773" x2="98.49999999999977" y1="967.0204428571428" y2="967.0204428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8.499999999999773" x2="8.499999999999773" y1="927.8571428571428" y2="967.0204428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98.49999999999977" x2="98.49999999999977" y1="927.8571428571428" y2="967.0204428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98.49999999999977" x2="368.4999999999998" y1="927.8571428571428" y2="927.8571428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98.49999999999977" x2="368.4999999999998" y1="967.0204428571428" y2="967.0204428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98.49999999999977" x2="98.49999999999977" y1="927.8571428571428" y2="967.0204428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368.4999999999998" x2="368.4999999999998" y1="927.8571428571428" y2="967.0204428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8.499999999999773" x2="98.49999999999977" y1="967.0204128571428" y2="967.0204128571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8.499999999999773" x2="98.49999999999977" y1="994.9388128571427" y2="994.9388128571427"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8.499999999999773" x2="8.499999999999773" y1="967.0204128571428" y2="994.9388128571427"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98.49999999999977" x2="98.49999999999977" y1="967.0204128571428" y2="994.9388128571427"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98.49999999999977" x2="188.4999999999998" y1="967.0204128571428" y2="967.0204128571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98.49999999999977" x2="188.4999999999998" y1="994.9388128571427" y2="994.9388128571427"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98.49999999999977" x2="98.49999999999977" y1="967.0204128571428" y2="994.9388128571427"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.4999999999998" x2="188.4999999999998" y1="967.0204128571428" y2="994.9388128571427"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.4999999999999" x2="278.4999999999999" y1="967.0204128571428" y2="967.0204128571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.4999999999999" x2="278.4999999999999" y1="994.9388128571427" y2="994.9388128571427"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.4999999999999" x2="188.4999999999999" y1="967.0204128571428" y2="994.9388128571427"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.4999999999999" x2="278.4999999999999" y1="967.0204128571428" y2="994.9388128571427"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.4999999999998" x2="368.4999999999998" y1="967.0204128571428" y2="967.0204128571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.4999999999998" x2="368.4999999999998" y1="994.9388128571427" y2="994.9388128571427"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.4999999999998" x2="278.4999999999998" y1="967.0204128571428" y2="994.9388128571427"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368.4999999999998" x2="368.4999999999998" y1="967.0204128571428" y2="994.9388128571427"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8.499999999999773" x2="98.49999999999977" y1="994.9387428571428" y2="994.9387428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8.499999999999773" x2="98.49999999999977" y1="1022.857142857143" y2="1022.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8.499999999999773" x2="8.499999999999773" y1="994.9387428571428" y2="1022.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98.49999999999977" x2="98.49999999999977" y1="994.9387428571428" y2="1022.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98.49999999999977" x2="188.4999999999998" y1="994.9387428571428" y2="994.9387428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98.49999999999977" x2="188.4999999999998" y1="1022.857142857143" y2="1022.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98.49999999999977" x2="98.49999999999977" y1="994.9387428571428" y2="1022.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.4999999999998" x2="188.4999999999998" y1="994.9387428571428" y2="1022.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.4999999999999" x2="278.4999999999999" y1="994.9387428571428" y2="994.9387428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.4999999999999" x2="278.4999999999999" y1="1022.857142857143" y2="1022.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.4999999999999" x2="188.4999999999999" y1="994.9387428571428" y2="1022.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.4999999999999" x2="278.4999999999999" y1="994.9387428571428" y2="1022.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.4999999999998" x2="368.4999999999998" y1="994.9387428571428" y2="994.9387428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.4999999999998" x2="368.4999999999998" y1="1022.857142857143" y2="1022.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.4999999999998" x2="278.4999999999998" y1="994.9387428571428" y2="1022.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368.4999999999998" x2="368.4999999999998" y1="994.9387428571428" y2="1022.857142857143"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53.5,947.857) scale(1,1) translate(0,0)" writing-mode="lr" x="53.5" xml:space="preserve" y="953.86" zvalue="10">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,50.5,981.857) scale(1,1) translate(0,0)" writing-mode="lr" x="50.5" xml:space="preserve" y="987.86" zvalue="11">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232.5,981.857) scale(1,1) translate(0,0)" writing-mode="lr" x="232.5" xml:space="preserve" y="987.86" zvalue="12">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49.5,1009.86) scale(1,1) translate(0,0)" writing-mode="lr" x="49.5" xml:space="preserve" y="1015.86" zvalue="13">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,231.5,1009.86) scale(1,1) translate(0,0)" writing-mode="lr" x="231.5" xml:space="preserve" y="1015.86" zvalue="14">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,74,562.357) scale(1,1) translate(0,0)" writing-mode="lr" x="73.99999999999977" xml:space="preserve" y="566.8571428571429" zvalue="16">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,198.899,181.699) scale(1,1) translate(0,0)" writing-mode="lr" x="198.9" xml:space="preserve" y="186.2" zvalue="17">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,303.899,181.699) scale(1,1) translate(0,0)" writing-mode="lr" x="303.9" xml:space="preserve" y="186.2" zvalue="18">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.554,949.857) scale(1,1) translate(0,0)" writing-mode="lr" x="233.55" xml:space="preserve" y="955.86" zvalue="19">BangWai-01-2023</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,143.554,1009.86) scale(1,1) translate(0,0)" writing-mode="lr" x="143.55" xml:space="preserve" y="1015.86" zvalue="20">段勇</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,323.554,1009.86) scale(1,1) translate(0,0)" writing-mode="lr" x="323.55" xml:space="preserve" y="1015.86" zvalue="21">20230626</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,56,255.857) scale(1,1) translate(0,0)" writing-mode="lr" x="13.5" xml:space="preserve" y="260.36" zvalue="22">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,236.5,255.857) scale(1,1) translate(0,0)" writing-mode="lr" x="194" xml:space="preserve" y="260.36" zvalue="23">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.7361,330.218) scale(1,1) translate(0,0)" writing-mode="lr" x="59.74" xml:space="preserve" y="334.72" zvalue="24">6.3kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,55,281.857) scale(1,1) translate(0,3.60854e-13)" writing-mode="lr" x="12.5" xml:space="preserve" y="286.36" zvalue="26">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,235.5,281.857) scale(1,1) translate(0,3.60854e-13)" writing-mode="lr" x="193" xml:space="preserve" y="286.36" zvalue="27">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.1875,375.107) scale(1,1) translate(0,0)" writing-mode="lr" x="56.19" xml:space="preserve" y="379.61" zvalue="28">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,224.187,374.107) scale(1,1) translate(0,0)" writing-mode="lr" x="224.19" xml:space="preserve" y="378.61" zvalue="29">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.1875,398.107) scale(1,1) translate(0,0)" writing-mode="lr" x="56.19" xml:space="preserve" y="402.61" zvalue="30">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,224.187,397.107) scale(1,1) translate(0,0)" writing-mode="lr" x="224.19" xml:space="preserve" y="401.61" zvalue="31">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,55,304.857) scale(1,1) translate(0,0)" writing-mode="lr" x="12.5" xml:space="preserve" y="309.36" zvalue="32">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,235,303.857) scale(1,1) translate(0,0)" writing-mode="lr" x="192.5" xml:space="preserve" y="308.36" zvalue="33">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="128" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,730.619,509.053) scale(1,1) translate(0,0)" writing-mode="lr" x="730.62" xml:space="preserve" y="513.55" zvalue="36">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,816.5,616.286) scale(1,1) translate(0,0)" writing-mode="lr" x="816.5" xml:space="preserve" y="620.79" zvalue="40">601</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,755.444,672.159) scale(1,1) translate(0,0)" writing-mode="lr" x="755.4400000000001" xml:space="preserve" y="676.66" zvalue="43">6011</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="129" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1267.33,517.339) scale(1,1) translate(0,0)" writing-mode="lr" x="1267.33" xml:space="preserve" y="521.84" zvalue="50">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1353.21,624.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1353.21" xml:space="preserve" y="629.0700000000001" zvalue="51">602</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1293.16,689.929) scale(1,1) translate(0,0)" writing-mode="lr" x="1293.16" xml:space="preserve" y="694.4299999999999" zvalue="53">6021</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="131" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,814.54,408.286) scale(1,1) translate(0,0)" writing-mode="lr" x="814.54" xml:space="preserve" y="412.79" zvalue="58">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="133" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1350.43,421.286) scale(1,1) translate(0,0)" writing-mode="lr" x="1350.43" xml:space="preserve" y="425.79" zvalue="60">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="130" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,771.778,338.964) scale(1,1) translate(0,0)" writing-mode="lr" x="771.78" xml:space="preserve" y="343.46" zvalue="64">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="132" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1306.98,341.464) scale(1,1) translate(0,0)" writing-mode="lr" x="1306.98" xml:space="preserve" y="345.96" zvalue="67">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,517,715.75) scale(1,1) translate(0,0)" writing-mode="lr" x="517" xml:space="preserve" y="720.25" zvalue="69">6.3kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="121" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,555.125,272) scale(1,1) translate(0,0)" writing-mode="lr" x="555.13" xml:space="preserve" y="276.5" zvalue="70">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="136" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,660.393,832) scale(1,1) translate(0,0)" writing-mode="lr" x="660.39" xml:space="preserve" y="836.5" zvalue="72">641</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,662.107,782.714) scale(1,1) translate(0,1.37617e-12)" writing-mode="lr" x="662.11" xml:space="preserve" y="787.21" zvalue="74">6411</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="33" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,693.393,977.143) scale(1,1) translate(0,0)" writing-mode="lr" x="693.39" xml:space="preserve" y="981.64" zvalue="78">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="137" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,740.5,843.964) scale(1,1) translate(0,0)" writing-mode="lr" x="740.5" xml:space="preserve" y="848.46" zvalue="90">6911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="138" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,966.286,840.036) scale(1,1) translate(0,0)" writing-mode="lr" x="966.29" xml:space="preserve" y="844.54" zvalue="95">642</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,970.143,785.036) scale(1,1) translate(0,0)" writing-mode="lr" x="970.14" xml:space="preserve" y="789.54" zvalue="97">6421</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,997.143,980.893) scale(1,1) translate(0,0)" writing-mode="lr" x="997.14" xml:space="preserve" y="985.39" zvalue="101">#2发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="139" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1044.25,847.714) scale(1,1) translate(0,0)" writing-mode="lr" x="1044.25" xml:space="preserve" y="852.21" zvalue="105">6921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="140" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1257.89,835.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1257.89" xml:space="preserve" y="840.25" zvalue="110">643</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1260.32,783.607) scale(1,1) translate(0,-1.37776e-12)" writing-mode="lr" x="1260.32" xml:space="preserve" y="788.11" zvalue="112">6431</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1285.89,980.893) scale(1,1) translate(0,0)" writing-mode="lr" x="1285.89" xml:space="preserve" y="985.39" zvalue="116">#3发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="141" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1333,847.714) scale(1,1) translate(0,0)" writing-mode="lr" x="1333" xml:space="preserve" y="852.21" zvalue="120">6931</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="134" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,552.643,945.616) scale(1,1) translate(0,0)" writing-mode="lr" x="552.64" xml:space="preserve" y="950.12" zvalue="124">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="135" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,514.786,801.464) scale(1,1) translate(0,0)" writing-mode="lr" x="514.79" xml:space="preserve" y="805.96" zvalue="126">6441</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="126" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1441.58,81.0327) scale(1,1) translate(0,0)" writing-mode="lr" x="1441.58" xml:space="preserve" y="85.53" zvalue="130">#2站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="127" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1413,211.464) scale(1,1) translate(0,0)" writing-mode="lr" x="1413" xml:space="preserve" y="215.96" zvalue="131">3031</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="142" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1565,959.196) scale(1,1) translate(0,0)" writing-mode="lr" x="1565" xml:space="preserve" y="963.7" zvalue="140">6.3kV邦马线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,700,25.3571) scale(1,1) translate(0,0)" writing-mode="lr" x="700" xml:space="preserve" y="29.86" zvalue="142">35kV邦河线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="124" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,674.667,237.714) scale(1,1) translate(0,0)" writing-mode="lr" x="674.67" xml:space="preserve" y="242.21" zvalue="145">3416</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="125" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,775.083,149.917) scale(1,1) translate(0,0)" writing-mode="lr" x="775.08" xml:space="preserve" y="154.42" zvalue="148">34167</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,728,534) scale(1,1) translate(0,0)" writing-mode="lr" x="728" xml:space="preserve" y="538.5" zvalue="152">6300kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="101" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1266,540) scale(1,1) translate(0,0)" writing-mode="lr" x="1266" xml:space="preserve" y="544.5" zvalue="154">6300kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="113" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,691,997.5) scale(1,1) translate(0,0)" writing-mode="lr" x="691" xml:space="preserve" y="1002" zvalue="155">1600kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="143" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,996,997.5) scale(1,1) translate(0,0)" writing-mode="lr" x="996" xml:space="preserve" y="1002" zvalue="157">1600kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="144" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1284,996.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1284" xml:space="preserve" y="1001" zvalue="159">1600kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="148" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1535,786.464) scale(1,1) translate(0,0)" writing-mode="lr" x="1535" xml:space="preserve" y="790.96" zvalue="162">6451</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="150" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1587.43,838.607) scale(1,1) translate(0,0)" writing-mode="lr" x="1587.43" xml:space="preserve" y="843.11" zvalue="166">645</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="46.69" y="169.11" zvalue="25"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="PowerTransformer2Class">
  <g id="414">
   <g id="4140">
    <use class="kv35" height="60" transform="rotate(0,786.536,510.053) scale(1.6125,1.54462) translate(-286.512,-163.502)" width="40" x="754.29" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="463.71" zvalue="35"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874451386371" ObjectName="35"/>
    </metadata>
   </g>
   <g id="4141">
    <use class="v6300" height="60" transform="rotate(0,786.536,510.053) scale(1.6125,1.54462) translate(-286.512,-163.502)" width="40" x="754.29" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="463.71" zvalue="35"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874451451907" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399457636355" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399457636355"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,786.536,510.053) scale(1.6125,1.54462) translate(-286.512,-163.502)" width="40" x="754.29" y="463.71"/></g>
  <g id="48">
   <g id="480">
    <use class="kv35" height="60" transform="rotate(0,1323.25,518.339) scale(1.6125,1.54462) translate(-490.38,-166.424)" width="40" x="1291" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="472" zvalue="49"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874451517443" ObjectName="35"/>
    </metadata>
   </g>
   <g id="481">
    <use class="v6300" height="60" transform="rotate(0,1323.25,518.339) scale(1.6125,1.54462) translate(-490.38,-166.424)" width="40" x="1291" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="472" zvalue="49"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874451582979" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399457701891" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399457701891"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1323.25,518.339) scale(1.6125,1.54462) translate(-490.38,-166.424)" width="40" x="1291" y="472"/></g>
 </g>
 <g id="BreakerClass">
  <g id="42">
   <use class="v6300" height="20" transform="rotate(0,785.571,614.714) scale(2.14286,1.92857) translate(-413.257,-286.688)" width="10" x="774.8571427890233" xlink:href="#Breaker:开关_0" y="595.4285714285714" zvalue="39"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924550000645" ObjectName="#1主变6.3kV侧601断路器"/>
   <cge:TPSR_Ref TObjectID="6473924550000645"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,785.571,614.714) scale(2.14286,1.92857) translate(-413.257,-286.688)" width="10" x="774.8571427890233" y="595.4285714285714"/></g>
  <g id="47">
   <use class="v6300" height="20" transform="rotate(0,1322.29,623) scale(2.14286,1.92857) translate(-699.505,-290.677)" width="10" x="1311.571428503309" xlink:href="#Breaker:开关_0" y="603.7142857142858" zvalue="50"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924550066181" ObjectName="#2主变6.3kV侧602断路器"/>
   <cge:TPSR_Ref TObjectID="6473924550066181"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1322.29,623) scale(2.14286,1.92857) translate(-699.505,-290.677)" width="10" x="1311.571428503309" y="603.7142857142858"/></g>
  <g id="49">
   <use class="kv35" height="20" transform="rotate(0,786.714,409.286) scale(2.14286,1.92857) translate(-413.867,-187.778)" width="10" x="776" xlink:href="#Breaker:开关_0" y="390" zvalue="57"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924550131717" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473924550131717"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,786.714,409.286) scale(2.14286,1.92857) translate(-413.867,-187.778)" width="10" x="776" y="390"/></g>
  <g id="50">
   <use class="kv35" height="20" transform="rotate(0,1322.71,422.286) scale(2.14286,1.92857) translate(-699.733,-194.037)" width="10" x="1312" xlink:href="#Breaker:开关_0" y="403" zvalue="59"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924550197252" ObjectName="#2主变35kV侧302断路器"/>
   <cge:TPSR_Ref TObjectID="6473924550197252"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1322.71,422.286) scale(2.14286,1.92857) translate(-699.733,-194.037)" width="10" x="1312" y="403"/></g>
  <g id="60">
   <use class="v6300" height="20" transform="rotate(0,692.679,835.857) scale(2.14286,1.92857) translate(-363.714,-393.164)" width="10" x="681.9642857142858" xlink:href="#Breaker:开关_0" y="816.5714307512552" zvalue="71"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924550262788" ObjectName="#1发电机641断路器"/>
   <cge:TPSR_Ref TObjectID="6473924550262788"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,692.679,835.857) scale(2.14286,1.92857) translate(-363.714,-393.164)" width="10" x="681.9642857142858" y="816.5714307512552"/></g>
  <g id="82">
   <use class="v6300" height="20" transform="rotate(0,996.429,839.607) scale(2.14286,1.92857) translate(-525.714,-394.97)" width="10" x="985.7142857142859" xlink:href="#Breaker:开关_0" y="820.3214307512552" zvalue="94"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924550328324" ObjectName="#2发电机642断路器"/>
   <cge:TPSR_Ref TObjectID="6473924550328324"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,996.429,839.607) scale(2.14286,1.92857) translate(-525.714,-394.97)" width="10" x="985.7142857142859" y="820.3214307512552"/></g>
  <g id="99">
   <use class="v6300" height="20" transform="rotate(0,1285.18,839.607) scale(2.14286,1.92857) translate(-679.714,-394.97)" width="10" x="1274.464285714286" xlink:href="#Breaker:开关_0" y="820.3214416503905" zvalue="109"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924550393860" ObjectName="#3发电机643断路器"/>
   <cge:TPSR_Ref TObjectID="6473924550393860"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1285.18,839.607) scale(2.14286,1.92857) translate(-679.714,-394.97)" width="10" x="1274.464285714286" y="820.3214416503905"/></g>
  <g id="149">
   <use class="v6300" height="20" transform="rotate(0,1564.71,839.607) scale(2.14286,1.92857) translate(-828.8,-394.97)" width="10" x="1554" xlink:href="#Breaker:开关_0" y="820.3214416503905" zvalue="165"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924591353860" ObjectName="6.3kV邦马线645断路器"/>
   <cge:TPSR_Ref TObjectID="6473924591353860"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1564.71,839.607) scale(2.14286,1.92857) translate(-828.8,-394.97)" width="10" x="1554" y="820.3214416503905"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="89">
   <use class="v6300" height="30" transform="rotate(0,785.857,678.714) scale(1.42857,1.04762) translate(-232.543,-30.1364)" width="15" x="775.1428570747375" xlink:href="#Disconnector:刀闸_0" y="663" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449986428933" ObjectName="#1主变6.3kV侧6011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449986428933"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,785.857,678.714) scale(1.42857,1.04762) translate(-232.543,-30.1364)" width="15" x="775.1428570747375" y="663"/></g>
  <g id="46">
   <use class="v6300" height="30" transform="rotate(0,1322.3,689.5) scale(1.42857,1.04762) translate(-393.477,-30.6266)" width="15" x="1311.588889859222" xlink:href="#Disconnector:刀闸_0" y="673.7857142857142" zvalue="52"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449986494469" ObjectName="#2主变6.3kV侧6021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449986494469"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1322.3,689.5) scale(1.42857,1.04762) translate(-393.477,-30.6266)" width="15" x="1311.588889859222" y="673.7857142857142"/></g>
  <g id="53">
   <use class="kv35" height="30" transform="rotate(0,786.714,339.964) scale(1.42857,1.04762) translate(-232.8,-14.7386)" width="15" x="776" xlink:href="#Disconnector:刀闸_0" y="324.25" zvalue="63"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449986560005" ObjectName="#1主变35kV侧3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449986560005"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,786.714,339.964) scale(1.42857,1.04762) translate(-232.8,-14.7386)" width="15" x="776" y="324.25"/></g>
  <g id="55">
   <use class="kv35" height="30" transform="rotate(0,1322.56,342.464) scale(1.42857,1.04762) translate(-393.552,-14.8523)" width="15" x="1311.841193679849" xlink:href="#Disconnector:刀闸_0" y="326.75" zvalue="66"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449986625541" ObjectName="#2主变35kV侧3021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449986625541"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1322.56,342.464) scale(1.42857,1.04762) translate(-393.552,-14.8523)" width="15" x="1311.841193679849" y="326.75"/></g>
  <g id="94">
   <use class="v6300" height="30" transform="rotate(0,692.679,783.714) scale(1.42857,1.04762) translate(-204.589,-34.9091)" width="15" x="681.964285714286" xlink:href="#Disconnector:刀闸_0" y="768.0000021798267" zvalue="72"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449986756613" ObjectName="#1发电机6411隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449986756613"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,692.679,783.714) scale(1.42857,1.04762) translate(-204.589,-34.9091)" width="15" x="681.964285714286" y="768.0000021798267"/></g>
  <g id="65">
   <use class="v6300" height="30" transform="rotate(0,773.214,844.964) scale(1.42857,1.04762) translate(-228.75,-37.6932)" width="15" x="762.5" xlink:href="#Disconnector:刀闸_0" y="829.25" zvalue="89"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449986953221" ObjectName="#1发电机6911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449986953221"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,773.214,844.964) scale(1.42857,1.04762) translate(-228.75,-37.6932)" width="15" x="762.5" y="829.25"/></g>
  <g id="81">
   <use class="v6300" height="30" transform="rotate(0,996.429,787.464) scale(1.42857,1.04762) translate(-295.714,-35.0795)" width="15" x="985.714285714286" xlink:href="#Disconnector:刀闸_0" y="771.7500021798267" zvalue="95"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449987280901" ObjectName="#2发电机6421隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449987280901"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,996.429,787.464) scale(1.42857,1.04762) translate(-295.714,-35.0795)" width="15" x="985.714285714286" y="771.7500021798267"/></g>
  <g id="74">
   <use class="v6300" height="30" transform="rotate(0,1076.96,848.714) scale(1.42857,1.04762) translate(-319.875,-37.8636)" width="15" x="1066.25" xlink:href="#Disconnector:刀闸_0" y="833" zvalue="104"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449987018757" ObjectName="#2发电机6921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449987018757"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1076.96,848.714) scale(1.42857,1.04762) translate(-319.875,-37.8636)" width="15" x="1066.25" y="833"/></g>
  <g id="98">
   <use class="v6300" height="30" transform="rotate(0,1285.18,787.464) scale(1.42857,1.04762) translate(-382.339,-35.0795)" width="15" x="1274.464285714286" xlink:href="#Disconnector:刀闸_0" y="771.7499869210376" zvalue="110"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449987608581" ObjectName="#3发电机6431隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449987608581"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1285.18,787.464) scale(1.42857,1.04762) translate(-382.339,-35.0795)" width="15" x="1274.464285714286" y="771.7499869210376"/></g>
  <g id="88">
   <use class="v6300" height="30" transform="rotate(0,1365.71,848.714) scale(1.42857,1.04762) translate(-406.5,-37.8636)" width="15" x="1355" xlink:href="#Disconnector:刀闸_0" y="833" zvalue="119"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449987346437" ObjectName="#3发电机6931隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449987346437"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1365.71,848.714) scale(1.42857,1.04762) translate(-406.5,-37.8636)" width="15" x="1355" y="833"/></g>
  <g id="62">
   <use class="v6300" height="30" transform="rotate(0,548.214,802.464) scale(1.42857,1.04762) translate(-161.25,-35.7614)" width="15" x="537.5" xlink:href="#Disconnector:刀闸_0" y="786.75" zvalue="125"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449987739653" ObjectName="#1站用变6441隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449987739653"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,548.214,802.464) scale(1.42857,1.04762) translate(-161.25,-35.7614)" width="15" x="537.5" y="786.75"/></g>
  <g id="102">
   <use class="kv35" height="30" transform="rotate(0,1436.96,212.464) scale(1.42857,1.04762) translate(-427.875,-8.94318)" width="15" x="1426.25" xlink:href="#Disconnector:刀闸_0" y="196.7500000000001" zvalue="130"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449987805189" ObjectName="#2站用变3031隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449987805189"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1436.96,212.464) scale(1.42857,1.04762) translate(-427.875,-8.94318)" width="15" x="1426.25" y="196.7500000000001"/></g>
  <g id="114">
   <use class="kv35" height="30" transform="rotate(0,699.464,238.714) scale(1.42857,1.04762) translate(-206.625,-10.1364)" width="15" x="688.75" xlink:href="#Disconnector:刀闸_0" y="223.0000000000001" zvalue="144"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449988067333" ObjectName="35kV邦河线3416隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449988067333"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,699.464,238.714) scale(1.42857,1.04762) translate(-206.625,-10.1364)" width="15" x="688.75" y="223.0000000000001"/></g>
  <g id="145">
   <use class="v6300" height="30" transform="rotate(0,1564.71,787.464) scale(1.42857,1.04762) translate(-466.2,-35.0795)" width="15" x="1554" xlink:href="#Disconnector:刀闸_0" y="771.7499871253967" zvalue="161"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450285928453" ObjectName="6.3kV邦马线6451隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450285928453"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1564.71,787.464) scale(1.42857,1.04762) translate(-466.2,-35.0795)" width="15" x="1554" y="771.7499871253967"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="39">
   <path class="v6300" d="M 785.5 596.26 L 786.54 555.74" stroke-width="1" zvalue="46"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="42@0" LinkObjectIDznd="414@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 785.5 596.26 L 786.54 555.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="40">
   <path class="v6300" d="M 785.71 633.13 L 785.71 663.52" stroke-width="1" zvalue="47"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="42@1" LinkObjectIDznd="89@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 785.71 633.13 L 785.71 663.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="v6300" d="M 1322.21 604.55 L 1323.25 564.02" stroke-width="1" zvalue="54"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@0" LinkObjectIDznd="48@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.21 604.55 L 1323.25 564.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="v6300" d="M 1322.43 641.42 L 1322.43 674.31" stroke-width="1" zvalue="55"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@1" LinkObjectIDznd="46@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.43 641.42 L 1322.43 674.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="kv35" d="M 786.86 427.7 L 786.6 464.55" stroke-width="1" zvalue="60"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@1" LinkObjectIDznd="414@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 786.86 427.7 L 786.6 464.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="kv35" d="M 1322.86 440.7 L 1323.31 472.84" stroke-width="1" zvalue="61"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="50@1" LinkObjectIDznd="48@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.86 440.7 L 1323.31 472.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="54">
   <path class="kv35" d="M 786.8 355.41 L 786.8 390.84" stroke-width="1" zvalue="64"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@1" LinkObjectIDznd="49@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 786.8 355.41 L 786.8 390.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="kv35" d="M 1322.64 357.91 L 1322.64 403.84" stroke-width="1" zvalue="67"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="55@1" LinkObjectIDznd="50@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.64 357.91 L 1322.64 403.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="v6300" d="M 692.61 817.41 L 692.61 799.16" stroke-width="1" zvalue="73"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60@0" LinkObjectIDznd="94@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 692.61 817.41 L 692.61 799.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="38">
   <path class="v6300" d="M 692.8 768.52 L 692.8 734.25" stroke-width="1" zvalue="75"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="94@0" LinkObjectIDznd="57@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 692.8 768.52 L 692.8 734.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="37">
   <path class="v6300" d="M 690.89 907.7 L 690.89 854.28" stroke-width="1" zvalue="77"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="97@0" LinkObjectIDznd="60@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 690.89 907.7 L 690.89 854.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="v6300" d="M 773.34 829.77 L 773.34 809.25 L 692.61 809.25" stroke-width="1" zvalue="90"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@0" LinkObjectIDznd="59" MaxPinNum="2"/>
   </metadata>
  <path d="M 773.34 829.77 L 773.34 809.25 L 692.61 809.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="67">
   <path class="v6300" d="M 773.3 948.13 L 773.3 860.41" stroke-width="1" zvalue="91"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="152@0" LinkObjectIDznd="65@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 773.3 948.13 L 773.3 860.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="v6300" d="M 825.13 950.88 L 825.13 879.25 L 773.3 879.25" stroke-width="1" zvalue="92"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="155@0" LinkObjectIDznd="67" MaxPinNum="2"/>
   </metadata>
  <path d="M 825.13 950.88 L 825.13 879.25 L 773.3 879.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="v6300" d="M 996.36 821.16 L 996.36 802.91" stroke-width="1" zvalue="96"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="82@0" LinkObjectIDznd="81@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 996.36 821.16 L 996.36 802.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="v6300" d="M 996.55 772.27 L 996.55 734.25" stroke-width="1" zvalue="98"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="81@0" LinkObjectIDznd="57@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 996.55 772.27 L 996.55 734.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="v6300" d="M 994.64 911.45 L 994.64 858.03" stroke-width="1" zvalue="100"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="78@0" LinkObjectIDznd="82@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 994.64 911.45 L 994.64 858.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="v6300" d="M 1077.09 833.52 L 1077.09 813 L 996.36 813" stroke-width="1" zvalue="105"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="74@0" LinkObjectIDznd="80" MaxPinNum="2"/>
   </metadata>
  <path d="M 1077.09 833.52 L 1077.09 813 L 996.36 813" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="v6300" d="M 1077.05 951.88 L 1077.05 864.16" stroke-width="1" zvalue="106"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="76@0" LinkObjectIDznd="74@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1077.05 951.88 L 1077.05 864.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="v6300" d="M 1128.88 954.63 L 1128.88 883 L 1077.05 883" stroke-width="1" zvalue="107"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="75@0" LinkObjectIDznd="72" MaxPinNum="2"/>
   </metadata>
  <path d="M 1128.88 954.63 L 1128.88 883 L 1077.05 883" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="v6300" d="M 1285.11 821.16 L 1285.11 802.91" stroke-width="1" zvalue="111"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="98@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1285.11 821.16 L 1285.11 802.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="v6300" d="M 1285.3 772.27 L 1285.3 734.25" stroke-width="1" zvalue="113"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="98@0" LinkObjectIDznd="57@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1285.3 772.27 L 1285.3 734.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="v6300" d="M 1283.39 911.45 L 1283.39 858.03" stroke-width="1" zvalue="115"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="93@0" LinkObjectIDznd="99@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1283.39 911.45 L 1283.39 858.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="87">
   <path class="v6300" d="M 1365.84 833.52 L 1365.84 813 L 1285.11 813" stroke-width="1" zvalue="120"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="88@0" LinkObjectIDznd="96" MaxPinNum="2"/>
   </metadata>
  <path d="M 1365.84 833.52 L 1365.84 813 L 1285.11 813" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="86">
   <path class="v6300" d="M 1365.8 951.88 L 1365.8 864.16" stroke-width="1" zvalue="121"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="91@0" LinkObjectIDznd="88@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1365.8 951.88 L 1365.8 864.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="v6300" d="M 1417.63 954.63 L 1417.63 883 L 1365.8 883" stroke-width="1" zvalue="122"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="90@0" LinkObjectIDznd="86" MaxPinNum="2"/>
   </metadata>
  <path d="M 1417.63 954.63 L 1417.63 883 L 1365.8 883" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="v6300" d="M 548.34 787.27 L 548.34 734.25" stroke-width="1" zvalue="126"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="62@0" LinkObjectIDznd="57@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 548.34 787.27 L 548.34 734.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="v6300" d="M 548.3 817.91 L 548.3 881.54" stroke-width="1" zvalue="127"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="62@1" LinkObjectIDznd="61@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 548.3 817.91 L 548.3 881.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="104">
   <path class="kv35" d="M 1437.05 227.91 L 1437.05 293" stroke-width="1" zvalue="133"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@1" LinkObjectIDznd="58@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1437.05 227.91 L 1437.05 293" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="105">
   <path class="kv35" d="M 1435.78 147.94 L 1435.78 197.27" stroke-width="1" zvalue="134"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="103@0" LinkObjectIDznd="102@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1435.78 147.94 L 1435.78 197.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="106">
   <path class="kv35" d="M 786.84 324.77 L 786.84 293" stroke-width="1" zvalue="135"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@0" LinkObjectIDznd="58@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 786.84 324.77 L 786.84 293" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="107">
   <path class="v6300" d="M 785.94 694.16 L 785.94 734.25" stroke-width="1" zvalue="136"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="89@1" LinkObjectIDznd="57@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 785.94 694.16 L 785.94 734.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv35" d="M 1322.68 327.27 L 1322.68 293" stroke-width="1" zvalue="137"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="55@0" LinkObjectIDznd="58@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.68 327.27 L 1322.68 293" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="v6300" d="M 1322.39 704.95 L 1322.39 734.25" stroke-width="1" zvalue="138"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@1" LinkObjectIDznd="57@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.39 704.95 L 1322.39 734.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="115">
   <path class="kv35" d="M 700 85.89 L 700 223.52" stroke-width="1" zvalue="145"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@0" LinkObjectIDznd="114@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 700 85.89 L 700 223.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="116">
   <path class="kv35" d="M 699.55 254.16 L 699.55 293" stroke-width="1" zvalue="146"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@1" LinkObjectIDznd="58@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 699.55 254.16 L 699.55 293" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="118">
   <path class="kv35" d="M 762.06 162.94 L 700 162.94" stroke-width="1" zvalue="148"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="117@0" LinkObjectIDznd="115" MaxPinNum="2"/>
   </metadata>
  <path d="M 762.06 162.94 L 700 162.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="120">
   <path class="kv35" d="M 652.59 141.13 L 700 141.13" stroke-width="1" zvalue="151"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="119@0" LinkObjectIDznd="115" MaxPinNum="2"/>
   </metadata>
  <path d="M 652.59 141.13 L 700 141.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="147">
   <path class="v6300" d="M 1564.84 772.27 L 1564.84 734.25" stroke-width="1" zvalue="163"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@0" LinkObjectIDznd="57@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1564.84 772.27 L 1564.84 734.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="151">
   <path class="v6300" d="M 1565 897.61 L 1565 858.03" stroke-width="1" zvalue="166"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="110@0" LinkObjectIDznd="149@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1565 897.61 L 1565 858.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="153">
   <path class="v6300" d="M 1564.64 821.16 L 1564.64 802.91" stroke-width="1" zvalue="167"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="149@0" LinkObjectIDznd="145@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1564.64 821.16 L 1564.64 802.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="57">
   <path class="v6300" d="M 493.75 734.25 L 1688.75 734.25" stroke-width="6" zvalue="68"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674252423172" ObjectName="6.3kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674252423172"/></metadata>
  <path d="M 493.75 734.25 L 1688.75 734.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv35" d="M 525 293 L 1598.75 293" stroke-width="6" zvalue="69"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674252488708" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674252488708"/></metadata>
  <path d="M 525 293 L 1598.75 293" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="97">
   <use class="v6300" height="30" transform="rotate(0,690.893,931.929) scale(1.64286,1.64286) translate(-260.707,-355.025)" width="30" x="666.25" xlink:href="#Generator:发电机_0" y="907.2857164655411" zvalue="76"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449986691077" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449986691077"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,690.893,931.929) scale(1.64286,1.64286) translate(-260.707,-355.025)" width="30" x="666.25" y="907.2857164655411"/></g>
  <g id="78">
   <use class="v6300" height="30" transform="rotate(0,994.643,935.679) scale(1.64286,1.64286) translate(-379.565,-356.492)" width="30" x="970" xlink:href="#Generator:发电机_0" y="911.0357164655411" zvalue="99"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449987215365" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449987215365"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,994.643,935.679) scale(1.64286,1.64286) translate(-379.565,-356.492)" width="30" x="970" y="911.0357164655411"/></g>
  <g id="93">
   <use class="v6300" height="30" transform="rotate(0,1283.39,935.679) scale(1.64286,1.64286) translate(-492.554,-356.492)" width="30" x="1258.75" xlink:href="#Generator:发电机_0" y="911.0357164655411" zvalue="114"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449987543045" ObjectName="#3发电机"/>
   <cge:TPSR_Ref TObjectID="6192449987543045"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1283.39,935.679) scale(1.64286,1.64286) translate(-492.554,-356.492)" width="30" x="1258.75" y="911.0357164655411"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="152">
   <use class="v6300" height="18" transform="rotate(0,773.302,962.346) scale(1.875,1.875) translate(-354.749,-441.22)" width="14" x="760.1766634630083" xlink:href="#Accessory:PT7_0" y="945.4708065271998" zvalue="83"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449986887685" ObjectName="#1发电机PT2"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,773.302,962.346) scale(1.875,1.875) translate(-354.749,-441.22)" width="14" x="760.1766634630083" y="945.4708065271998"/></g>
  <g id="155">
   <use class="v6300" height="18" transform="rotate(0,825.125,965.096) scale(1.875,1.875) translate(-378.933,-442.503)" width="14" x="812" xlink:href="#Accessory:PT7_0" y="948.2208065271998" zvalue="86"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449986822149" ObjectName="#1发电机PT3"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,825.125,965.096) scale(1.875,1.875) translate(-378.933,-442.503)" width="14" x="812" y="948.2208065271998"/></g>
  <g id="76">
   <use class="v6300" height="18" transform="rotate(0,1077.05,966.096) scale(1.875,1.875) translate(-496.499,-442.97)" width="14" x="1063.926663463008" xlink:href="#Accessory:PT7_0" y="949.2208065271998" zvalue="102"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449987149829" ObjectName="#2发电机PT2"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1077.05,966.096) scale(1.875,1.875) translate(-496.499,-442.97)" width="14" x="1063.926663463008" y="949.2208065271998"/></g>
  <g id="75">
   <use class="v6300" height="18" transform="rotate(0,1128.88,968.846) scale(1.875,1.875) translate(-520.683,-444.253)" width="14" x="1115.75" xlink:href="#Accessory:PT7_0" y="951.9708065271998" zvalue="103"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449987084293" ObjectName="#2发电机PT3"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1128.88,968.846) scale(1.875,1.875) translate(-520.683,-444.253)" width="14" x="1115.75" y="951.9708065271998"/></g>
  <g id="91">
   <use class="v6300" height="18" transform="rotate(0,1365.8,966.096) scale(1.875,1.875) translate(-631.249,-442.97)" width="14" x="1352.676663463008" xlink:href="#Accessory:PT7_0" y="949.2208065271998" zvalue="117"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449987477509" ObjectName="#3发电机PT2"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1365.8,966.096) scale(1.875,1.875) translate(-631.249,-442.97)" width="14" x="1352.676663463008" y="949.2208065271998"/></g>
  <g id="90">
   <use class="v6300" height="18" transform="rotate(0,1417.62,968.846) scale(1.875,1.875) translate(-655.433,-444.253)" width="14" x="1404.5" xlink:href="#Accessory:PT7_0" y="951.9708065271998" zvalue="118"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449987411973" ObjectName="#3发电机PT3"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1417.62,968.846) scale(1.875,1.875) translate(-655.433,-444.253)" width="14" x="1404.5" y="951.9708065271998"/></g>
  <g id="119">
   <use class="kv35" height="18" transform="rotate(90,638.375,141.125) scale(1.875,1.875) translate(-291.783,-57.9833)" width="14" x="625.25" xlink:href="#Accessory:PT7_0" y="124.25" zvalue="150"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449988263941" ObjectName="35kV邦河线PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(90,638.375,141.125) scale(1.875,1.875) translate(-291.783,-57.9833)" width="14" x="625.25" y="124.25"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="61">
   <use class="v6300" height="30" transform="rotate(0,546.875,906.616) scale(1.74107,1.74107) translate(-222.397,-374.777)" width="28" x="522.5" xlink:href="#EnergyConsumer:站用变带负荷1_0" y="880.5" zvalue="123"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449987674117" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,546.875,906.616) scale(1.74107,1.74107) translate(-222.397,-374.777)" width="28" x="522.5" y="880.5"/></g>
  <g id="103">
   <use class="kv35" height="30" transform="rotate(0,1435.62,122.866) scale(1.74107,-1.74107) translate(-600.686,-182.319)" width="28" x="1411.25" xlink:href="#EnergyConsumer:站用变带负荷1_0" y="96.75" zvalue="129"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449987870725" ObjectName="#2站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1435.62,122.866) scale(1.74107,-1.74107) translate(-600.686,-182.319)" width="28" x="1411.25" y="96.75"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="117">
   <use class="kv35" height="20" transform="rotate(270,774.25,163) scale(1.25,1.25) translate(-153.6,-30.1)" width="10" x="768" xlink:href="#GroundDisconnector:地刀_0" y="150.5" zvalue="147"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449988198405" ObjectName="35kV邦河线34167接地刀闸"/>
   <cge:TPSR_Ref TObjectID="6192449988198405"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,774.25,163) scale(1.25,1.25) translate(-153.6,-30.1)" width="10" x="768" y="150.5"/></g>
 </g>
</svg>