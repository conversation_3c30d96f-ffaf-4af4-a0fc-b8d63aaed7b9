<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549674901505" height="1200" id="thSvg" source="NR-PCS9000" viewBox="0 0 1900 1200" width="1900">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Accessory:避雷器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000003" y2="8.666666666666668"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="12.6" y2="18.6"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="2" type="2" x="12.00516117969822" xlink:href="#terminal" y="7.908504801097395"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:母联开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(180,5.08,10.04) scale(1,1) translate(0,0)" width="9.17" x="0.5" y="0.5"/>
  </symbol>
  <symbol id="Breaker:母联开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <rect fill="rgb(170,0,0)" fill-opacity="1" height="19.08" rx="0" ry="0" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-opacity="0" stroke-width="1" transform="rotate(180,5.08,10.04) scale(1,1) translate(0,0)" width="9.17" x="0.5" y="0.5"/>
  </symbol>
  <symbol id="Breaker:母联开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.333333333333333" x2="9.75" y1="0.4166666666666696" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.666666666666668" x2="0.4166666666666661" y1="0.4999999999999982" y2="19.66666666666667"/>
   <rect fill-opacity="0" height="19.58" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.04,10.04) scale(1,1) translate(0,0)" width="9.58" x="0.25" y="0.25"/>
  </symbol>
  <symbol id="Accessory:PT1_0" viewBox="0,0,18,18">
   <use terminal-index="0" type="0" x="9.15" xlink:href="#terminal" y="1.15"/>
   <ellipse cx="9.23" cy="6.18" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="6.12" cy="11.68" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="11.9" cy="11.78" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <path d="M 6.025 3.025 L 3.05 10 L 6.025 7.85833 L 9.025 10.05 L 6.025 3.025" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="7.877914951989029" y2="28.48902606310013"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV培训变" InitShowingPlane="" fill="rgb(0,0,0)" height="1200" width="1900" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass">
  <g href="220kV潞西变.svg"><rect fill-opacity="0" height="37.86" width="196.67" x="630.1" y="14.71" zvalue="495"/></g>
 </g>
 <g id="OtherClass">
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="37.86" id="3" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,728.429,33.6429) scale(1,1) translate(6.99546e-14,4.08403e-14)" width="196.67" x="630.1" y="14.71" zvalue="495"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="21" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,728.429,33.6429) scale(1,1) translate(6.99546e-14,4.08403e-14)" writing-mode="lr" x="728.4299999999999" xml:space="preserve" y="41.14" zvalue="495">110培训变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57.25,304) scale(1,1) translate(0,0)" writing-mode="lr" x="57.25" xml:space="preserve" y="308.5" zvalue="2">110kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1414.22,302.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1414.22" xml:space="preserve" y="306.83" zvalue="7">110kVⅡ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,80.7778,165.111) scale(1,1) translate(0,0)" writing-mode="lr" x="80.78" xml:space="preserve" y="169.61" zvalue="24">1119</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,147.278,273.222) scale(1,1) translate(0,0)" writing-mode="lr" x="147.28" xml:space="preserve" y="277.72" zvalue="27">1111</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,61.5,214) scale(1,1) translate(0,0)" writing-mode="lr" x="61.5" xml:space="preserve" y="218.5" zvalue="28">111</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,304,266) scale(1,1) translate(0,0)" writing-mode="lr" x="304" xml:space="preserve" y="270.5" zvalue="33">11110</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,278.111,220.556) scale(1,1) translate(0,0)" writing-mode="lr" x="278.11" xml:space="preserve" y="225.06" zvalue="40">11190</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,525.778,118.667) scale(1,1) translate(0,0)" writing-mode="lr" x="525.78" xml:space="preserve" y="123.17" zvalue="68">110kV测试二线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,500.778,178.111) scale(1,1) translate(0,0)" writing-mode="lr" x="500.78" xml:space="preserve" y="182.61" zvalue="70">1129</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,503.278,273.222) scale(1,1) translate(0,0)" writing-mode="lr" x="503.28" xml:space="preserve" y="277.72" zvalue="73">1121</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,508.5,231) scale(1,1) translate(0,0)" writing-mode="lr" x="508.5" xml:space="preserve" y="235.5" zvalue="74">112</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,553,263) scale(1,1) translate(0,0)" writing-mode="lr" x="553" xml:space="preserve" y="267.5" zvalue="77">11210</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,553,217) scale(1,1) translate(0,0)" writing-mode="lr" x="553" xml:space="preserve" y="221.5" zvalue="83">11290</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="128" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,312.948,487.5) scale(1,1) translate(0,0)" writing-mode="lr" x="312.95" xml:space="preserve" y="492" zvalue="129">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="129" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,339.278,417.222) scale(1,1) translate(0,0)" writing-mode="lr" x="339.28" xml:space="preserve" y="421.72" zvalue="131">1013</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="130" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,343.5,376.25) scale(1,1) translate(0,0)" writing-mode="lr" x="343.5" xml:space="preserve" y="380.75" zvalue="133">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="133" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,339.278,332.222) scale(1,1) translate(0,0)" writing-mode="lr" x="339.28" xml:space="preserve" y="336.72" zvalue="136">1011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="141" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,339.278,645.222) scale(1,1) translate(0,0)" writing-mode="lr" x="339.28" xml:space="preserve" y="649.72" zvalue="143">0013</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="140" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,341.5,607) scale(1,1) translate(0,0)" writing-mode="lr" x="341.5" xml:space="preserve" y="611.5" zvalue="145">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="139" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,339.278,564.222) scale(1,1) translate(0,0)" writing-mode="lr" x="339.28" xml:space="preserve" y="568.72" zvalue="147">0011</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="149" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.25,676) scale(1,1) translate(0,0)" writing-mode="lr" x="59.25" xml:space="preserve" y="680.5" zvalue="153">10kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="148" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1404.73,676) scale(1,1) translate(0,0)" writing-mode="lr" x="1404.73" xml:space="preserve" y="680.5" zvalue="155">10kVⅡ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="159" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1061,487.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1061" xml:space="preserve" y="492" zvalue="159">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="158" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1087.28,417.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1087.28" xml:space="preserve" y="421.72" zvalue="161">1023</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="157" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1091.5,375) scale(1,1) translate(0,0)" writing-mode="lr" x="1091.5" xml:space="preserve" y="379.5" zvalue="163">102</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="156" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1087.28,332.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1087.28" xml:space="preserve" y="336.72" zvalue="165">1021</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="155" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1087.28,645.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1087.28" xml:space="preserve" y="649.72" zvalue="171">0023</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="154" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1089.5,607) scale(1,1) translate(0,0)" writing-mode="lr" x="1089.5" xml:space="preserve" y="611.5" zvalue="173">002</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="153" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1087.28,564.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1087.28" xml:space="preserve" y="568.72" zvalue="175">0021</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="187" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,901.778,118.667) scale(1,1) translate(0,0)" writing-mode="lr" x="901.78" xml:space="preserve" y="123.17" zvalue="183">110kV测试三线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="186" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,876.778,178.111) scale(1,1) translate(0,0)" writing-mode="lr" x="876.78" xml:space="preserve" y="182.61" zvalue="185">1139</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="185" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,879.278,273.222) scale(1,1) translate(0,0)" writing-mode="lr" x="879.28" xml:space="preserve" y="277.72" zvalue="188">1131</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="184" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,884.5,231) scale(1,1) translate(0,0)" writing-mode="lr" x="884.5" xml:space="preserve" y="235.5" zvalue="189">113</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="183" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,929,263) scale(1,1) translate(0,0)" writing-mode="lr" x="929" xml:space="preserve" y="267.5" zvalue="192">11310</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="182" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,929,217) scale(1,1) translate(0,0)" writing-mode="lr" x="929" xml:space="preserve" y="221.5" zvalue="198">11390</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="181" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1257.78,118.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1257.78" xml:space="preserve" y="123.17" zvalue="203">110kV测试四线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="180" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1232.78,178.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1232.78" xml:space="preserve" y="182.61" zvalue="205">1149</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="179" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1235.28,273.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1235.28" xml:space="preserve" y="277.72" zvalue="208">1141</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="178" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1240.5,231) scale(1,1) translate(0,0)" writing-mode="lr" x="1240.5" xml:space="preserve" y="235.5" zvalue="209">114</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="177" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1285,263) scale(1,1) translate(0,0)" writing-mode="lr" x="1285" xml:space="preserve" y="267.5" zvalue="212">11410</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="176" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1285,217) scale(1,1) translate(0,0)" writing-mode="lr" x="1285" xml:space="preserve" y="221.5" zvalue="218">11490</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="217" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,733,231) scale(1,1) translate(0,0)" writing-mode="lr" x="733" xml:space="preserve" y="235.5" zvalue="223">110</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="218" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,663.278,273.222) scale(1,1) translate(0,0)" writing-mode="lr" x="663.28" xml:space="preserve" y="277.72" zvalue="225">1101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="220" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,759.278,273.222) scale(1,1) translate(0,0)" writing-mode="lr" x="759.28" xml:space="preserve" y="277.72" zvalue="228">1102</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="229" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,678,209) scale(1,1) translate(0,0)" writing-mode="lr" x="678" xml:space="preserve" y="213.5" zvalue="238">11010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="232" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,789.389,209) scale(1,1) translate(0,0)" writing-mode="lr" x="789.39" xml:space="preserve" y="213.5" zvalue="242">11020</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="235" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,631.278,340.222) scale(1,1) translate(0,0)" writing-mode="lr" x="631.28" xml:space="preserve" y="344.72" zvalue="246">1091</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="241" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,803.278,340.222) scale(1,1) translate(0,0)" writing-mode="lr" x="803.28" xml:space="preserve" y="344.72" zvalue="252">1092</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="256" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,141.278,707.222) scale(1,1) translate(0,0)" writing-mode="lr" x="141.28" xml:space="preserve" y="711.72" zvalue="258">0119</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="257" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,142.278,794.222) scale(1,1) translate(0,0)" writing-mode="lr" x="142.28" xml:space="preserve" y="798.72" zvalue="259">0111</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="254" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,148.5,757) scale(1,1) translate(0,0)" writing-mode="lr" x="148.5" xml:space="preserve" y="761.5" zvalue="260">011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="255" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,193,744) scale(1,1) translate(0,0)" writing-mode="lr" x="193" xml:space="preserve" y="748.5" zvalue="261">01190</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="260" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,165,860.5) scale(1,1) translate(0,0)" writing-mode="lr" x="165" xml:space="preserve" y="865" zvalue="266">10kV备用一线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="265" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,337.278,707.222) scale(1,1) translate(0,0)" writing-mode="lr" x="337.28" xml:space="preserve" y="711.72" zvalue="270">0129</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="264" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,338.278,794.222) scale(1,1) translate(0,0)" writing-mode="lr" x="338.28" xml:space="preserve" y="798.72" zvalue="271">0121</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="263" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,344.5,757) scale(1,1) translate(0,0)" writing-mode="lr" x="344.5" xml:space="preserve" y="761.5" zvalue="273">012</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="262" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,389,744) scale(1,1) translate(0,0)" writing-mode="lr" x="389" xml:space="preserve" y="748.5" zvalue="276">01290</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="261" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,361,860.5) scale(1,1) translate(0,0)" writing-mode="lr" x="361" xml:space="preserve" y="865" zvalue="282">10kV备用二线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="280" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,593.278,707.222) scale(1,1) translate(0,0)" writing-mode="lr" x="593.28" xml:space="preserve" y="711.72" zvalue="286">0139</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="279" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,594.278,794.222) scale(1,1) translate(0,0)" writing-mode="lr" x="594.28" xml:space="preserve" y="798.72" zvalue="287">0131</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="278" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,600.5,757) scale(1,1) translate(0,0)" writing-mode="lr" x="600.5" xml:space="preserve" y="761.5" zvalue="289">013</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="277" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,645,744) scale(1,1) translate(0,0)" writing-mode="lr" x="645" xml:space="preserve" y="748.5" zvalue="292">01390</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="276" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,617,860.5) scale(1,1) translate(0,0)" writing-mode="lr" x="617" xml:space="preserve" y="865" zvalue="298">10kV备用三线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="351" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,822.278,707.222) scale(1,1) translate(0,0)" writing-mode="lr" x="822.28" xml:space="preserve" y="711.72" zvalue="349">0149</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="350" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,823.278,794.222) scale(1,1) translate(0,0)" writing-mode="lr" x="823.28" xml:space="preserve" y="798.72" zvalue="350">0141</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="349" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,829.5,757) scale(1,1) translate(0,0)" writing-mode="lr" x="829.5" xml:space="preserve" y="761.5" zvalue="352">014</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="348" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,874,744) scale(1,1) translate(0,0)" writing-mode="lr" x="874" xml:space="preserve" y="748.5" zvalue="354">01490</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="347" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,846,860.5) scale(1,1) translate(0,0)" writing-mode="lr" x="846" xml:space="preserve" y="865" zvalue="360">10kV备用四线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="346" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1086.28,707.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1086.28" xml:space="preserve" y="711.72" zvalue="363">0159</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="345" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1087.28,794.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1087.28" xml:space="preserve" y="798.72" zvalue="364">0151</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="344" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1093.5,757) scale(1,1) translate(0,0)" writing-mode="lr" x="1093.5" xml:space="preserve" y="761.5" zvalue="366">015</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="343" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1138,744) scale(1,1) translate(0,0)" writing-mode="lr" x="1138" xml:space="preserve" y="748.5" zvalue="368">01590</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="342" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1110,860.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1110" xml:space="preserve" y="865" zvalue="374">10kV备用五线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="341" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1326.28,707.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1326.28" xml:space="preserve" y="711.72" zvalue="377">0169</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="340" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1327.28,794.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1327.28" xml:space="preserve" y="798.72" zvalue="378">0161</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="339" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1333.5,757) scale(1,1) translate(0,0)" writing-mode="lr" x="1333.5" xml:space="preserve" y="761.5" zvalue="380">016</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="338" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1378,744) scale(1,1) translate(0,0)" writing-mode="lr" x="1378" xml:space="preserve" y="748.5" zvalue="382">01690</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="337" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1350,860.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1350" xml:space="preserve" y="865" zvalue="388">10kV备用六线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="386" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,733,599) scale(1,1) translate(0,0)" writing-mode="lr" x="733" xml:space="preserve" y="603.5" zvalue="394">010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="385" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,663.278,641.222) scale(1,1) translate(0,0)" writing-mode="lr" x="663.28" xml:space="preserve" y="645.72" zvalue="396">0101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="384" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,759.278,641.222) scale(1,1) translate(0,0)" writing-mode="lr" x="759.28" xml:space="preserve" y="645.72" zvalue="398">0102</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="383" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,686,577) scale(1,1) translate(0,0)" writing-mode="lr" x="686" xml:space="preserve" y="581.5" zvalue="404">01010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="382" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,781.389,577) scale(1,1) translate(0,0)" writing-mode="lr" x="781.39" xml:space="preserve" y="581.5" zvalue="407">01020</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="398" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,143.456,644.222) scale(1,1) translate(0,0)" writing-mode="lr" x="143.46" xml:space="preserve" y="648.72" zvalue="411">0091</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="405" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1327.46,644.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1327.46" xml:space="preserve" y="648.72" zvalue="419">0092</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,361.875,116.875) scale(1,1) translate(0,0)" writing-mode="lr" x="361.88" xml:space="preserve" y="121.38" zvalue="499">Hello World</text>
 </g>
 <g id="BusbarSectionClass">
  <g id="1">
   <path class="kv110" d="M 90.82 305 L 701.93 305" stroke-width="6" zvalue="1"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674391490563" ObjectName="110kVⅠ母"/>
   </metadata>
  <path d="M 90.82 305 L 701.93 305" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="4">
   <path class="kv110" d="M 756 303.33 L 1380.44 303.33" stroke-width="6" zvalue="6"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674391556099" ObjectName="110kVⅡ母"/>
   </metadata>
  <path d="M 756 303.33 L 1380.44 303.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="151">
   <path class="kv10" d="M 90.82 677 L 701.93 677" stroke-width="6" zvalue="152"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674391687171" ObjectName="10kVⅠ母"/>
   </metadata>
  <path d="M 90.82 677 L 701.93 677" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="150">
   <path class="kv10" d="M 758 677 L 1382.44 677" stroke-width="6" zvalue="154"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674391621635" ObjectName="10kVⅡ母"/>
   </metadata>
  <path d="M 758 677 L 1382.44 677" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="20">
   <use class="kv110" height="30" transform="rotate(0,169.389,180.222) scale(1.01481,0.814815) translate(-2.36172,38.1818)" width="15" x="161.7777777777778" xlink:href="#Disconnector:刀闸_0" y="167.9999999999999" zvalue="23"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453456560130" ObjectName="1119"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,169.389,180.222) scale(1.01481,0.814815) translate(-2.36172,38.1818)" width="15" x="161.7777777777778" y="167.9999999999999"/></g>
  <g id="23">
   <use class="kv110" height="30" transform="rotate(0,169.389,274.222) scale(1.01481,0.814815) translate(-2.36172,59.5455)" width="15" x="161.7777777777778" xlink:href="#Disconnector:刀闸_0" y="261.9999999999999" zvalue="26"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453456625666" ObjectName="1111"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,169.389,274.222) scale(1.01481,0.814815) translate(-2.36172,59.5455)" width="15" x="161.7777777777778" y="261.9999999999999"/></g>
  <g id="85">
   <use class="kv110" height="30" transform="rotate(0,525.389,180.222) scale(1.01481,0.814815) translate(-7.5588,38.1818)" width="15" x="517.7777777777778" xlink:href="#Disconnector:刀闸_0" y="167.9999999999999" zvalue="69"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453457412098" ObjectName="1129"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,525.389,180.222) scale(1.01481,0.814815) translate(-7.5588,38.1818)" width="15" x="517.7777777777778" y="167.9999999999999"/></g>
  <g id="84">
   <use class="kv110" height="30" transform="rotate(0,525.389,274.222) scale(1.01481,0.814815) translate(-7.5588,59.5455)" width="15" x="517.7777777777778" xlink:href="#Disconnector:刀闸_0" y="261.9999999999999" zvalue="71"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453457346562" ObjectName="1121"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,525.389,274.222) scale(1.01481,0.814815) translate(-7.5588,59.5455)" width="15" x="517.7777777777778" y="261.9999999999999"/></g>
  <g id="131">
   <use class="kv110" height="30" transform="rotate(0,361.389,418.222) scale(1.01481,0.814815) translate(-5.16464,92.2727)" width="15" x="353.7777777777778" xlink:href="#Disconnector:刀闸_0" y="406" zvalue="130"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453457543170" ObjectName="1013"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,361.389,418.222) scale(1.01481,0.814815) translate(-5.16464,92.2727)" width="15" x="353.7777777777778" y="406"/></g>
  <g id="134">
   <use class="kv110" height="30" transform="rotate(0,361.389,333.222) scale(1.01481,0.814815) translate(-5.16464,72.9545)" width="15" x="353.7777777777778" xlink:href="#Disconnector:刀闸_0" y="320.9999999999999" zvalue="135"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453457608706" ObjectName="1011"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,361.389,333.222) scale(1.01481,0.814815) translate(-5.16464,72.9545)" width="15" x="353.7777777777778" y="320.9999999999999"/></g>
  <g id="146">
   <use class="kv10" height="30" transform="rotate(0,361.389,646.222) scale(1.01481,0.814815) translate(-5.16464,144.091)" width="15" x="353.7777777777778" xlink:href="#Disconnector:刀闸_0" y="634" zvalue="142"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453457739780" ObjectName="0013"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,361.389,646.222) scale(1.01481,0.814815) translate(-5.16464,144.091)" width="15" x="353.7777777777778" y="634"/></g>
  <g id="144">
   <use class="kv10" height="30" transform="rotate(0,361.389,565.222) scale(1.01481,0.814815) translate(-5.16464,125.682)" width="15" x="353.7777777777778" xlink:href="#Disconnector:刀闸_0" y="553" zvalue="146"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453457674242" ObjectName="0011"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,361.389,565.222) scale(1.01481,0.814815) translate(-5.16464,125.682)" width="15" x="353.7777777777778" y="553"/></g>
  <g id="173">
   <use class="kv110" height="30" transform="rotate(0,1109.39,418.222) scale(1.01481,0.814815) translate(-16.0843,92.2727)" width="15" x="1101.777777777778" xlink:href="#Disconnector:刀闸_0" y="406" zvalue="160"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453458001922" ObjectName="1023"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1109.39,418.222) scale(1.01481,0.814815) translate(-16.0843,92.2727)" width="15" x="1101.777777777778" y="406"/></g>
  <g id="171">
   <use class="kv110" height="30" transform="rotate(0,1109.39,333.222) scale(1.01481,0.814815) translate(-16.0843,72.9545)" width="15" x="1101.777777777778" xlink:href="#Disconnector:刀闸_0" y="320.9999999999999" zvalue="164"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453457936386" ObjectName="1021"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1109.39,333.222) scale(1.01481,0.814815) translate(-16.0843,72.9545)" width="15" x="1101.777777777778" y="320.9999999999999"/></g>
  <g id="166">
   <use class="kv10" height="30" transform="rotate(0,1109.39,646.222) scale(1.01481,0.814815) translate(-16.0843,144.091)" width="15" x="1101.777777777778" xlink:href="#Disconnector:刀闸_0" y="634" zvalue="170"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453457870850" ObjectName="0023"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1109.39,646.222) scale(1.01481,0.814815) translate(-16.0843,144.091)" width="15" x="1101.777777777778" y="634"/></g>
  <g id="164">
   <use class="kv10" height="30" transform="rotate(0,1109.39,565.222) scale(1.01481,0.814815) translate(-16.0843,125.682)" width="15" x="1101.777777777778" xlink:href="#Disconnector:刀闸_0" y="553" zvalue="174"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453457805315" ObjectName="0021"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1109.39,565.222) scale(1.01481,0.814815) translate(-16.0843,125.682)" width="15" x="1101.777777777778" y="553"/></g>
  <g id="214">
   <use class="kv110" height="30" transform="rotate(0,901.389,180.222) scale(1.01481,0.814815) translate(-13.0479,38.1818)" width="15" x="893.7777777777778" xlink:href="#Disconnector:刀闸_0" y="167.9999999999999" zvalue="184"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453458984962" ObjectName="1139"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,901.389,180.222) scale(1.01481,0.814815) translate(-13.0479,38.1818)" width="15" x="893.7777777777778" y="167.9999999999999"/></g>
  <g id="213">
   <use class="kv110" height="30" transform="rotate(0,901.389,274.222) scale(1.01481,0.814815) translate(-13.0479,59.5455)" width="15" x="893.7777777777778" xlink:href="#Disconnector:刀闸_0" y="261.9999999999999" zvalue="186"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453458919426" ObjectName="1131"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,901.389,274.222) scale(1.01481,0.814815) translate(-13.0479,59.5455)" width="15" x="893.7777777777778" y="261.9999999999999"/></g>
  <g id="200">
   <use class="kv110" height="30" transform="rotate(0,1257.39,180.222) scale(1.01481,0.814815) translate(-18.2449,38.1818)" width="15" x="1249.777777777778" xlink:href="#Disconnector:刀闸_0" y="167.9999999999999" zvalue="204"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453458460674" ObjectName="1149"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1257.39,180.222) scale(1.01481,0.814815) translate(-18.2449,38.1818)" width="15" x="1249.777777777778" y="167.9999999999999"/></g>
  <g id="199">
   <use class="kv110" height="30" transform="rotate(0,1257.39,274.222) scale(1.01481,0.814815) translate(-18.2449,59.5455)" width="15" x="1249.777777777778" xlink:href="#Disconnector:刀闸_0" y="261.9999999999999" zvalue="206"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453458395139" ObjectName="1141"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1257.39,274.222) scale(1.01481,0.814815) translate(-18.2449,59.5455)" width="15" x="1249.777777777778" y="261.9999999999999"/></g>
  <g id="219">
   <use class="kv110" height="30" transform="rotate(0,685.389,274.222) scale(1.01481,0.814815) translate(-9.89457,59.5455)" width="15" x="677.7777777777778" xlink:href="#Disconnector:刀闸_0" y="261.9999999999999" zvalue="224"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453459116035" ObjectName="1101"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,685.389,274.222) scale(1.01481,0.814815) translate(-9.89457,59.5455)" width="15" x="677.7777777777778" y="261.9999999999999"/></g>
  <g id="221">
   <use class="kv110" height="30" transform="rotate(0,781.389,274.222) scale(1.01481,0.814815) translate(-11.296,59.5455)" width="15" x="773.7777743869358" xlink:href="#Disconnector:刀闸_0" y="261.9999999999999" zvalue="227"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453459181571" ObjectName="1102"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,781.389,274.222) scale(1.01481,0.814815) translate(-11.296,59.5455)" width="15" x="773.7777743869358" y="261.9999999999999"/></g>
  <g id="236">
   <use class="kv110" height="30" transform="rotate(0,653.389,341.222) scale(1.01481,0.814815) translate(-9.42741,74.7727)" width="15" x="645.7777777777778" xlink:href="#Disconnector:刀闸_0" y="328.9999999999999" zvalue="245"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453459509250" ObjectName="1091"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,653.389,341.222) scale(1.01481,0.814815) translate(-9.42741,74.7727)" width="15" x="645.7777777777778" y="328.9999999999999"/></g>
  <g id="244">
   <use class="kv110" height="30" transform="rotate(0,825.389,341.222) scale(1.01481,0.814815) translate(-11.9384,74.7727)" width="15" x="817.7777777777778" xlink:href="#Disconnector:刀闸_0" y="328.9999999999999" zvalue="251"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453459705858" ObjectName="1092"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,825.389,341.222) scale(1.01481,0.814815) translate(-11.9384,74.7727)" width="15" x="817.7777777777778" y="328.9999999999999"/></g>
  <g id="246">
   <use class="kv10" height="30" transform="rotate(0,165.389,708.222) scale(1.01481,0.814815) translate(-2.30333,158.182)" width="15" x="157.7777777777778" xlink:href="#Disconnector:刀闸_0" y="696" zvalue="257"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453459771395" ObjectName="0119"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,165.389,708.222) scale(1.01481,0.814815) translate(-2.30333,158.182)" width="15" x="157.7777777777778" y="696"/></g>
  <g id="248">
   <use class="kv10" height="30" transform="rotate(0,165.389,795.222) scale(1.01481,0.814815) translate(-2.30333,177.955)" width="15" x="157.7777777777778" xlink:href="#Disconnector:刀闸_0" y="783" zvalue="258"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453459836931" ObjectName="0111"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,165.389,795.222) scale(1.01481,0.814815) translate(-2.30333,177.955)" width="15" x="157.7777777777778" y="783"/></g>
  <g id="275">
   <use class="kv10" height="30" transform="rotate(0,361.389,708.222) scale(1.01481,0.814815) translate(-5.16464,158.182)" width="15" x="353.7777777777778" xlink:href="#Disconnector:刀闸_0" y="696" zvalue="268"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453460361218" ObjectName="0129"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,361.389,708.222) scale(1.01481,0.814815) translate(-5.16464,158.182)" width="15" x="353.7777777777778" y="696"/></g>
  <g id="274">
   <use class="kv10" height="30" transform="rotate(0,361.389,795.222) scale(1.01481,0.814815) translate(-5.16464,177.955)" width="15" x="353.7777777777778" xlink:href="#Disconnector:刀闸_0" y="783" zvalue="269"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453460295682" ObjectName="0121"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,361.389,795.222) scale(1.01481,0.814815) translate(-5.16464,177.955)" width="15" x="353.7777777777778" y="783"/></g>
  <g id="290">
   <use class="kv10" height="30" transform="rotate(0,617.389,708.222) scale(1.01481,0.814815) translate(-8.90187,158.182)" width="15" x="609.7777777777778" xlink:href="#Disconnector:刀闸_0" y="696" zvalue="284"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453460688899" ObjectName="0139"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,617.389,708.222) scale(1.01481,0.814815) translate(-8.90187,158.182)" width="15" x="609.7777777777778" y="696"/></g>
  <g id="289">
   <use class="kv10" height="30" transform="rotate(0,617.389,795.222) scale(1.01481,0.814815) translate(-8.90187,177.955)" width="15" x="609.7777777777778" xlink:href="#Disconnector:刀闸_0" y="783" zvalue="285"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453460623363" ObjectName="0131"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,617.389,795.222) scale(1.01481,0.814815) translate(-8.90187,177.955)" width="15" x="609.7777777777778" y="783"/></g>
  <g id="378">
   <use class="kv10" height="30" transform="rotate(0,846.389,708.222) scale(1.01481,0.814815) translate(-12.2449,158.182)" width="15" x="838.7777777777778" xlink:href="#Disconnector:刀闸_0" y="696" zvalue="347"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453461671938" ObjectName="0149"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,846.389,708.222) scale(1.01481,0.814815) translate(-12.2449,158.182)" width="15" x="838.7777777777778" y="696"/></g>
  <g id="377">
   <use class="kv10" height="30" transform="rotate(0,846.389,795.222) scale(1.01481,0.814815) translate(-12.2449,177.955)" width="15" x="838.7777777777778" xlink:href="#Disconnector:刀闸_0" y="783" zvalue="348"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453461606402" ObjectName="0141"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,846.389,795.222) scale(1.01481,0.814815) translate(-12.2449,177.955)" width="15" x="838.7777777777778" y="783"/></g>
  <g id="369">
   <use class="kv10" height="30" transform="rotate(0,1110.39,708.222) scale(1.01481,0.814815) translate(-16.0989,158.182)" width="15" x="1102.777777777778" xlink:href="#Disconnector:刀闸_0" y="696" zvalue="361"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453461344258" ObjectName="0159"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1110.39,708.222) scale(1.01481,0.814815) translate(-16.0989,158.182)" width="15" x="1102.777777777778" y="696"/></g>
  <g id="368">
   <use class="kv10" height="30" transform="rotate(0,1110.39,795.222) scale(1.01481,0.814815) translate(-16.0989,177.955)" width="15" x="1102.777777777778" xlink:href="#Disconnector:刀闸_0" y="783" zvalue="362"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453461278723" ObjectName="0151"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1110.39,795.222) scale(1.01481,0.814815) translate(-16.0989,177.955)" width="15" x="1102.777777777778" y="783"/></g>
  <g id="360">
   <use class="kv10" height="30" transform="rotate(0,1350.39,708.222) scale(1.01481,0.814815) translate(-19.6026,158.182)" width="15" x="1342.777777777778" xlink:href="#Disconnector:刀闸_0" y="696" zvalue="375"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453461016578" ObjectName="0169"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1350.39,708.222) scale(1.01481,0.814815) translate(-19.6026,158.182)" width="15" x="1342.777777777778" y="696"/></g>
  <g id="359">
   <use class="kv10" height="30" transform="rotate(0,1350.39,795.222) scale(1.01481,0.814815) translate(-19.6026,177.955)" width="15" x="1342.777777777778" xlink:href="#Disconnector:刀闸_0" y="783" zvalue="376"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453460951042" ObjectName="0161"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1350.39,795.222) scale(1.01481,0.814815) translate(-19.6026,177.955)" width="15" x="1342.777777777778" y="783"/></g>
  <g id="396">
   <use class="kv10" height="30" transform="rotate(0,685.389,642.222) scale(1.01481,0.814815) translate(-9.89457,143.182)" width="15" x="677.7777777777778" xlink:href="#Disconnector:刀闸_0" y="630" zvalue="395"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453462065155" ObjectName="0101"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,685.389,642.222) scale(1.01481,0.814815) translate(-9.89457,143.182)" width="15" x="677.7777777777778" y="630"/></g>
  <g id="395">
   <use class="kv10" height="30" transform="rotate(0,781.389,642.222) scale(1.01481,0.814815) translate(-11.296,143.182)" width="15" x="773.7777743869358" xlink:href="#Disconnector:刀闸_0" y="630" zvalue="397"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453461999618" ObjectName="0102"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,781.389,642.222) scale(1.01481,0.814815) translate(-11.296,143.182)" width="15" x="773.7777743869358" y="630"/></g>
  <g id="402">
   <use class="kv10" height="30" transform="rotate(180,165.567,645.222) scale(1.01481,0.814815) translate(-2.30593,143.864)" width="15" x="157.9559326810063" xlink:href="#Disconnector:刀闸_0" y="633" zvalue="410"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453462196226" ObjectName="0091"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,165.567,645.222) scale(1.01481,0.814815) translate(-2.30593,143.864)" width="15" x="157.9559326810063" y="633"/></g>
  <g id="409">
   <use class="kv10" height="30" transform="rotate(180,1349.57,645.222) scale(1.01481,0.814815) translate(-19.5906,143.864)" width="15" x="1341.955932681006" xlink:href="#Disconnector:刀闸_0" y="633" zvalue="418"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453462327298" ObjectName="0092"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1349.57,645.222) scale(1.01481,0.814815) translate(-19.5906,143.864)" width="15" x="1341.955932681006" y="633"/></g>
 </g>
 <g id="BreakerClass">
  <g id="25">
   <use class="kv110" height="20" transform="rotate(0,170,231) scale(1.1,1) translate(-14.9545,0)" width="10" x="164.5" xlink:href="#Breaker:开关_0" y="221" zvalue="27"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925053382660" ObjectName="111"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,170,231) scale(1.1,1) translate(-14.9545,0)" width="10" x="164.5" y="221"/></g>
  <g id="83">
   <use class="kv110" height="20" transform="rotate(0,526,231) scale(1.1,1) translate(-47.3182,0)" width="10" x="520.5" xlink:href="#Breaker:开关_0" y="221" zvalue="72"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925053448196" ObjectName="112"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,526,231) scale(1.1,1) translate(-47.3182,0)" width="10" x="520.5" y="221"/></g>
  <g id="132">
   <use class="kv110" height="20" transform="rotate(0,361,376.25) scale(1.1,1) translate(-32.3182,0)" width="10" x="355.5" xlink:href="#Breaker:开关_0" y="366.25" zvalue="132"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925053513732" ObjectName="101"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,361,376.25) scale(1.1,1) translate(-32.3182,0)" width="10" x="355.5" y="366.25"/></g>
  <g id="145">
   <use class="kv10" height="20" transform="rotate(0,361,607) scale(1.1,1) translate(-32.3182,0)" width="10" x="355.5" xlink:href="#Breaker:开关_0" y="597" zvalue="144"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925053579268" ObjectName="001"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,361,607) scale(1.1,1) translate(-32.3182,0)" width="10" x="355.5" y="597"/></g>
  <g id="172">
   <use class="kv110" height="20" transform="rotate(0,1109,375) scale(1.1,1) translate(-100.318,0)" width="10" x="1103.5" xlink:href="#Breaker:开关_0" y="365" zvalue="162"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925053710341" ObjectName="102"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1109,375) scale(1.1,1) translate(-100.318,0)" width="10" x="1103.5" y="365"/></g>
  <g id="165">
   <use class="kv10" height="20" transform="rotate(0,1109,607) scale(1.1,1) translate(-100.318,0)" width="10" x="1103.5" xlink:href="#Breaker:开关_0" y="597" zvalue="172"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925053644804" ObjectName="002"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1109,607) scale(1.1,1) translate(-100.318,0)" width="10" x="1103.5" y="597"/></g>
  <g id="212">
   <use class="kv110" height="20" transform="rotate(0,902,231) scale(1.1,1) translate(-81.5,0)" width="10" x="896.5" xlink:href="#Breaker:开关_0" y="221" zvalue="187"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925053841413" ObjectName="113"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,902,231) scale(1.1,1) translate(-81.5,0)" width="10" x="896.5" y="221"/></g>
  <g id="198">
   <use class="kv110" height="20" transform="rotate(0,1258,231) scale(1.1,1) translate(-113.864,0)" width="10" x="1252.5" xlink:href="#Breaker:开关_0" y="221" zvalue="207"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925053775877" ObjectName="114"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1258,231) scale(1.1,1) translate(-113.864,0)" width="10" x="1252.5" y="221"/></g>
  <g id="216">
   <use class="kv110" height="20" transform="rotate(90,733,249.5) scale(1.1,1) translate(-66.1364,0)" width="10" x="727.5" xlink:href="#Breaker:母联开关_0" y="239.5" zvalue="222"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925053906948" ObjectName="110"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,733,249.5) scale(1.1,1) translate(-66.1364,0)" width="10" x="727.5" y="239.5"/></g>
  <g id="247">
   <use class="kv10" height="20" transform="rotate(0,166,758) scale(1.1,1) translate(-14.5909,0)" width="10" x="160.5" xlink:href="#Breaker:开关_0" y="748" zvalue="259"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925053972484" ObjectName="011"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,166,758) scale(1.1,1) translate(-14.5909,0)" width="10" x="160.5" y="748"/></g>
  <g id="273">
   <use class="kv10" height="20" transform="rotate(0,362,758) scale(1.1,1) translate(-32.4091,0)" width="10" x="356.5" xlink:href="#Breaker:开关_0" y="748" zvalue="272"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925054038020" ObjectName="012"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,362,758) scale(1.1,1) translate(-32.4091,0)" width="10" x="356.5" y="748"/></g>
  <g id="288">
   <use class="kv10" height="20" transform="rotate(0,618,758) scale(1.1,1) translate(-55.6818,0)" width="10" x="612.5" xlink:href="#Breaker:开关_0" y="748" zvalue="288"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925054103556" ObjectName="013"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,618,758) scale(1.1,1) translate(-55.6818,0)" width="10" x="612.5" y="748"/></g>
  <g id="376">
   <use class="kv10" height="20" transform="rotate(0,847,758) scale(1.1,1) translate(-76.5,0)" width="10" x="841.5" xlink:href="#Breaker:开关_0" y="748" zvalue="351"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925054300164" ObjectName="014"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,847,758) scale(1.1,1) translate(-76.5,0)" width="10" x="841.5" y="748"/></g>
  <g id="367">
   <use class="kv10" height="20" transform="rotate(0,1111,758) scale(1.1,1) translate(-100.5,0)" width="10" x="1105.5" xlink:href="#Breaker:开关_0" y="748" zvalue="365"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925054234628" ObjectName="015"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1111,758) scale(1.1,1) translate(-100.5,0)" width="10" x="1105.5" y="748"/></g>
  <g id="358">
   <use class="kv10" height="20" transform="rotate(0,1351,758) scale(1.1,1) translate(-122.318,0)" width="10" x="1345.5" xlink:href="#Breaker:开关_0" y="748" zvalue="379"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925054169092" ObjectName="016"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1351,758) scale(1.1,1) translate(-122.318,0)" width="10" x="1345.5" y="748"/></g>
  <g id="397">
   <use class="kv10" height="20" transform="rotate(90,733,617.5) scale(1.1,1) translate(-66.1364,0)" width="10" x="727.5" xlink:href="#Breaker:母联开关_0" y="607.5" zvalue="393"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925054365700" ObjectName="010"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,733,617.5) scale(1.1,1) translate(-66.1364,0)" width="10" x="727.5" y="607.5"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="33">
   <use class="kv110" height="20" transform="rotate(90,197,252) scale(1,-1) translate(0,-504)" width="10" x="192" xlink:href="#GroundDisconnector:地刀_0" y="242" zvalue="32"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453456756738" ObjectName="11110"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,197,252) scale(1,-1) translate(0,-504)" width="10" x="192" y="242"/></g>
  <g id="40">
   <use class="kv110" height="20" transform="rotate(90,198.111,206.556) scale(1,-1) translate(0,-413.111)" width="10" x="193.1111111111111" xlink:href="#GroundDisconnector:地刀_0" y="196.5555555555555" zvalue="39"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453456887810" ObjectName="11190"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,198.111,206.556) scale(1,-1) translate(0,-413.111)" width="10" x="193.1111111111111" y="196.5555555555555"/></g>
  <g id="82">
   <use class="kv110" height="20" transform="rotate(90,553,252) scale(1,-1) translate(0,-504)" width="10" x="548" xlink:href="#GroundDisconnector:地刀_0" y="242" zvalue="75"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453457281026" ObjectName="11210"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,553,252) scale(1,-1) translate(0,-504)" width="10" x="548" y="242"/></g>
  <g id="77">
   <use class="kv110" height="20" transform="rotate(90,553,206) scale(1,-1) translate(0,-412)" width="10" x="548" xlink:href="#GroundDisconnector:地刀_0" y="196" zvalue="81"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453457149954" ObjectName="11290"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,553,206) scale(1,-1) translate(0,-412)" width="10" x="548" y="196"/></g>
  <g id="211">
   <use class="kv110" height="20" transform="rotate(90,929,252) scale(1,-1) translate(0,-504)" width="10" x="924" xlink:href="#GroundDisconnector:地刀_0" y="242" zvalue="190"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453458853890" ObjectName="11310"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,929,252) scale(1,-1) translate(0,-504)" width="10" x="924" y="242"/></g>
  <g id="206">
   <use class="kv110" height="20" transform="rotate(90,929,206) scale(1,-1) translate(0,-412)" width="10" x="924" xlink:href="#GroundDisconnector:地刀_0" y="196" zvalue="196"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453458722818" ObjectName="11390"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,929,206) scale(1,-1) translate(0,-412)" width="10" x="924" y="196"/></g>
  <g id="197">
   <use class="kv110" height="20" transform="rotate(90,1285,252) scale(1,-1) translate(0,-504)" width="10" x="1280" xlink:href="#GroundDisconnector:地刀_0" y="242" zvalue="210"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453458329603" ObjectName="11410"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1285,252) scale(1,-1) translate(0,-504)" width="10" x="1280" y="242"/></g>
  <g id="192">
   <use class="kv110" height="20" transform="rotate(90,1285,206) scale(1,-1) translate(0,-412)" width="10" x="1280" xlink:href="#GroundDisconnector:地刀_0" y="196" zvalue="216"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453458198530" ObjectName="11490"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1285,206) scale(1,-1) translate(0,-412)" width="10" x="1280" y="196"/></g>
  <g id="230">
   <use class="kv110" height="20" transform="rotate(0,686,226) scale(1,-1) translate(0,-452)" width="10" x="681" xlink:href="#GroundDisconnector:地刀_0" y="216" zvalue="237"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453459312643" ObjectName="11010"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,686,226) scale(1,-1) translate(0,-452)" width="10" x="681" y="216"/></g>
  <g id="233">
   <use class="kv110" height="20" transform="rotate(0,781.389,226) scale(1,-1) translate(0,-452)" width="10" x="776.3888854980469" xlink:href="#GroundDisconnector:地刀_0" y="216" zvalue="241"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453459443714" ObjectName="11020"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,781.389,226) scale(1,-1) translate(0,-452)" width="10" x="776.3888854980469" y="216"/></g>
  <g id="249">
   <use class="kv10" height="20" transform="rotate(90,193,734) scale(1,-1) translate(0,-1468)" width="10" x="188" xlink:href="#GroundDisconnector:地刀_0" y="724" zvalue="260"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453459968002" ObjectName="01190"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,193,734) scale(1,-1) translate(0,-1468)" width="10" x="188" y="724"/></g>
  <g id="272">
   <use class="kv10" height="20" transform="rotate(90,389,734) scale(1,-1) translate(0,-1468)" width="10" x="384" xlink:href="#GroundDisconnector:地刀_0" y="724" zvalue="274"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453460230146" ObjectName="01290"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,389,734) scale(1,-1) translate(0,-1468)" width="10" x="384" y="724"/></g>
  <g id="287">
   <use class="kv10" height="20" transform="rotate(90,645,734) scale(1,-1) translate(0,-1468)" width="10" x="640" xlink:href="#GroundDisconnector:地刀_0" y="724" zvalue="290"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453460557827" ObjectName="01390"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,645,734) scale(1,-1) translate(0,-1468)" width="10" x="640" y="724"/></g>
  <g id="375">
   <use class="kv10" height="20" transform="rotate(90,874,734) scale(1,-1) translate(0,-1468)" width="10" x="869" xlink:href="#GroundDisconnector:地刀_0" y="724" zvalue="353"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453461540867" ObjectName="01490"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,874,734) scale(1,-1) translate(0,-1468)" width="10" x="869" y="724"/></g>
  <g id="366">
   <use class="kv10" height="20" transform="rotate(90,1138,734) scale(1,-1) translate(0,-1468)" width="10" x="1133" xlink:href="#GroundDisconnector:地刀_0" y="724" zvalue="367"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453461213187" ObjectName="01590"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1138,734) scale(1,-1) translate(0,-1468)" width="10" x="1133" y="724"/></g>
  <g id="357">
   <use class="kv10" height="20" transform="rotate(90,1378,734) scale(1,-1) translate(0,-1468)" width="10" x="1373" xlink:href="#GroundDisconnector:地刀_0" y="724" zvalue="381"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453460885506" ObjectName="01690"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1378,734) scale(1,-1) translate(0,-1468)" width="10" x="1373" y="724"/></g>
  <g id="390">
   <use class="kv10" height="20" transform="rotate(0,686,594) scale(1,-1) translate(0,-1188)" width="10" x="681" xlink:href="#GroundDisconnector:地刀_0" y="584" zvalue="403"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453461934082" ObjectName="01010"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,686,594) scale(1,-1) translate(0,-1188)" width="10" x="681" y="584"/></g>
  <g id="388">
   <use class="kv10" height="20" transform="rotate(0,781.389,594) scale(1,-1) translate(0,-1188)" width="10" x="776.3888854980469" xlink:href="#GroundDisconnector:地刀_0" y="584" zvalue="406"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453461803010" ObjectName="01020"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,781.389,594) scale(1,-1) translate(0,-1188)" width="10" x="776.3888854980469" y="584"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="45">
   <use class="kv110" height="26" transform="rotate(90,142,152) scale(1,1) translate(0,0)" width="12" x="136" xlink:href="#Accessory:避雷器_0" y="139" zvalue="43"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453456953346" ObjectName="110kV潞帕Ⅰ回线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,142,152) scale(1,1) translate(0,0)" width="12" x="136" y="139"/></g>
  <g id="74">
   <use class="kv110" height="26" transform="rotate(90,497,161) scale(1,1) translate(0,0)" width="12" x="491" xlink:href="#Accessory:避雷器_0" y="148" zvalue="85"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453457018882" ObjectName="110kV测试二线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,497,161) scale(1,1) translate(0,0)" width="12" x="491" y="148"/></g>
  <g id="203">
   <use class="kv110" height="26" transform="rotate(90,873,161) scale(1,1) translate(0,0)" width="12" x="867" xlink:href="#Accessory:避雷器_0" y="148" zvalue="200"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453458591746" ObjectName="110kV测试三线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,873,161) scale(1,1) translate(0,0)" width="12" x="867" y="148"/></g>
  <g id="189">
   <use class="kv110" height="26" transform="rotate(90,1229,161) scale(1,1) translate(0,0)" width="12" x="1223" xlink:href="#Accessory:避雷器_0" y="148" zvalue="220"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453458067458" ObjectName="110kV测试四线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1229,161) scale(1,1) translate(0,0)" width="12" x="1223" y="148"/></g>
  <g id="237">
   <use class="kv110" height="18" transform="rotate(0,653.177,381.82) scale(1.8254,1.75779) translate(-287.921,-157.784)" width="18" x="636.7485785002302" xlink:href="#Accessory:PT1_0" y="366" zvalue="247"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453459574786" ObjectName="110kVⅠ母PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,653.177,381.82) scale(1.8254,1.75779) translate(-287.921,-157.784)" width="18" x="636.7485785002302" y="366"/></g>
  <g id="243">
   <use class="kv110" height="18" transform="rotate(0,825.5,381.82) scale(1.8254,1.75779) translate(-365.841,-157.784)" width="18" x="809.0714285714286" xlink:href="#Accessory:PT1_0" y="365.9999999709861" zvalue="253"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453459640322" ObjectName="110kVⅡ母PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,825.5,381.82) scale(1.8254,1.75779) translate(-365.841,-157.784)" width="18" x="809.0714285714286" y="365.9999999709861"/></g>
  <g id="401">
   <use class="kv10" height="18" transform="rotate(180,166.722,606) scale(1.5,1.44444) translate(-51.0741,-182.462)" width="18" x="153.2222222222222" xlink:href="#Accessory:PT1_0" y="593" zvalue="412"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453462130691" ObjectName="10kVⅠ母PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(180,166.722,606) scale(1.5,1.44444) translate(-51.0741,-182.462)" width="18" x="153.2222222222222" y="593"/></g>
  <g id="408">
   <use class="kv10" height="18" transform="rotate(180,1350.72,606) scale(1.5,1.44444) translate(-445.741,-182.462)" width="18" x="1337.222222222222" xlink:href="#Accessory:PT1_0" y="593" zvalue="420"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453462261763" ObjectName="10kVⅡ母PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(180,1350.72,606) scale(1.5,1.44444) translate(-445.741,-182.462)" width="18" x="1337.222222222222" y="593"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="86">
   <use class="kv110" height="30" transform="rotate(0,525.778,143.25) scale(3.09524,0.583333) translate(-348.578,96.0714)" width="7" x="514.9444444444445" xlink:href="#ACLineSegment:线路_0" y="134.5" zvalue="67"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453457477634" ObjectName="110kV测试二线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,525.778,143.25) scale(3.09524,0.583333) translate(-348.578,96.0714)" width="7" x="514.9444444444445" y="134.5"/></g>
  <g id="215">
   <use class="kv110" height="30" transform="rotate(0,901.778,143.25) scale(3.09524,0.583333) translate(-603.101,96.0714)" width="7" x="890.9444444444445" xlink:href="#ACLineSegment:线路_0" y="134.5" zvalue="182"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453459050498" ObjectName="110kV测试三线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,901.778,143.25) scale(3.09524,0.583333) translate(-603.101,96.0714)" width="7" x="890.9444444444445" y="134.5"/></g>
  <g id="201">
   <use class="kv110" height="30" transform="rotate(0,1257.78,143.25) scale(3.09524,0.583333) translate(-844.085,96.0714)" width="7" x="1246.944444444444" xlink:href="#ACLineSegment:线路_0" y="134.5" zvalue="202"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453458526210" ObjectName="110kV测试四线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1257.78,143.25) scale(3.09524,0.583333) translate(-844.085,96.0714)" width="7" x="1246.944444444444" y="134.5"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="81">
   <path class="kv110" d="M 525.45 286.24 L 525.45 305" stroke-width="1" zvalue="76"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="84@1" LinkObjectIDznd="1@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 525.45 286.24 L 525.45 305" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="kv110" d="M 525.48 262.4 L 525.48 240.55" stroke-width="1" zvalue="78"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="84@0" LinkObjectIDznd="83@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 525.48 262.4 L 525.48 240.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="kv110" d="M 525.96 221.43 L 525.96 192.24" stroke-width="1" zvalue="79"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="83@0" LinkObjectIDznd="85@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 525.96 221.43 L 525.96 192.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv110" d="M 543.25 252.05 L 525.48 252.05" stroke-width="1" zvalue="80"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="82@0" LinkObjectIDznd="80" MaxPinNum="2"/>
   </metadata>
  <path d="M 543.25 252.05 L 525.48 252.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="kv110" d="M 543.25 206.05 L 525.96 206.05" stroke-width="1" zvalue="82"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="77@0" LinkObjectIDznd="79" MaxPinNum="2"/>
   </metadata>
  <path d="M 543.25 206.05 L 525.96 206.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="75">
   <path class="kv110" d="M 525.78 151.91 L 525.78 168.4" stroke-width="1" zvalue="84"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="86@0" LinkObjectIDznd="85@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 525.78 151.91 L 525.78 168.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv110" d="M 509.37 161.03 L 525.78 161.03" stroke-width="1" zvalue="86"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="74@0" LinkObjectIDznd="75" MaxPinNum="2"/>
   </metadata>
  <path d="M 509.37 161.03 L 525.78 161.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="136">
   <path class="kv110" d="M 361.48 406.4 L 361.48 385.8" stroke-width="1" zvalue="138"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="131@0" LinkObjectIDznd="132@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 361.48 406.4 L 361.48 385.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="137">
   <path class="kv110" d="M 361.45 345.24 L 361.45 366.68" stroke-width="1" zvalue="139"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="134@1" LinkObjectIDznd="132@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 361.45 345.24 L 361.45 366.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="138">
   <path class="kv110" d="M 361.48 321.4 L 361.48 305" stroke-width="1" zvalue="140"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="134@0" LinkObjectIDznd="1@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 361.48 321.4 L 361.48 305" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="143">
   <path class="kv10" d="M 361.48 634.4 L 361.48 616.55" stroke-width="1" zvalue="148"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="146@0" LinkObjectIDznd="145@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 361.48 634.4 L 361.48 616.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="142">
   <path class="kv10" d="M 361.45 577.24 L 361.45 597.43" stroke-width="1" zvalue="149"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="144@1" LinkObjectIDznd="145@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 361.45 577.24 L 361.45 597.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="147">
   <path class="kv10" d="M 361.48 553.4 L 361.43 520.12" stroke-width="1" zvalue="150"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="144@0" LinkObjectIDznd="127@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 361.48 553.4 L 361.43 520.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="152">
   <path class="kv10" d="M 361.45 658.24 L 361.45 677" stroke-width="1" zvalue="156"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="146@1" LinkObjectIDznd="151@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 361.45 658.24 L 361.45 677" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="170">
   <path class="kv110" d="M 1109.5 456.45 L 1109.45 430.24" stroke-width="1" zvalue="166"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="174@0" LinkObjectIDznd="173@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1109.5 456.45 L 1109.45 430.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="169">
   <path class="kv110" d="M 1109.48 406.4 L 1109.48 384.55" stroke-width="1" zvalue="167"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="173@0" LinkObjectIDznd="172@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1109.48 406.4 L 1109.48 384.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="168">
   <path class="kv110" d="M 1109.45 345.24 L 1109.45 365.43" stroke-width="1" zvalue="168"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="171@1" LinkObjectIDznd="172@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1109.45 345.24 L 1109.45 365.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="167">
   <path class="kv110" d="M 1109.48 321.4 L 1109.48 303.33" stroke-width="1" zvalue="169"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="171@0" LinkObjectIDznd="4@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1109.48 321.4 L 1109.48 303.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="163">
   <path class="kv10" d="M 1109.48 634.4 L 1109.48 616.55" stroke-width="1" zvalue="176"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="166@0" LinkObjectIDznd="165@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1109.48 634.4 L 1109.48 616.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="162">
   <path class="kv10" d="M 1109.45 577.24 L 1109.45 597.43" stroke-width="1" zvalue="177"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="164@1" LinkObjectIDznd="165@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1109.45 577.24 L 1109.45 597.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="161">
   <path class="kv10" d="M 1109.48 553.4 L 1109.48 520.12" stroke-width="1" zvalue="178"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="164@0" LinkObjectIDznd="174@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1109.48 553.4 L 1109.48 520.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="160">
   <path class="kv10" d="M 1109.45 658.24 L 1109.45 677" stroke-width="1" zvalue="179"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="166@1" LinkObjectIDznd="150@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1109.45 658.24 L 1109.45 677" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="210">
   <path class="kv110" d="M 901.45 286.24 L 901.45 303.33" stroke-width="1" zvalue="191"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="213@1" LinkObjectIDznd="4@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 901.45 286.24 L 901.45 303.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="209">
   <path class="kv110" d="M 901.48 262.4 L 901.48 240.55" stroke-width="1" zvalue="193"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="213@0" LinkObjectIDznd="212@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 901.48 262.4 L 901.48 240.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="208">
   <path class="kv110" d="M 901.96 221.43 L 901.96 192.24" stroke-width="1" zvalue="194"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="212@0" LinkObjectIDznd="214@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 901.96 221.43 L 901.96 192.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="207">
   <path class="kv110" d="M 919.25 252.07 L 901.48 252.07" stroke-width="1" zvalue="195"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="211@0" LinkObjectIDznd="209" MaxPinNum="2"/>
   </metadata>
  <path d="M 919.25 252.07 L 901.48 252.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="205">
   <path class="kv110" d="M 919.25 206.07 L 901.96 206.07" stroke-width="1" zvalue="197"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="206@0" LinkObjectIDznd="208" MaxPinNum="2"/>
   </metadata>
  <path d="M 919.25 206.07 L 901.96 206.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="204">
   <path class="kv110" d="M 901.78 151.91 L 901.78 168.4" stroke-width="1" zvalue="199"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="215@0" LinkObjectIDznd="214@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 901.78 151.91 L 901.78 168.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="202">
   <path class="kv110" d="M 885.37 161.03 L 901.78 161.03" stroke-width="1" zvalue="201"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="203@0" LinkObjectIDznd="204" MaxPinNum="2"/>
   </metadata>
  <path d="M 885.37 161.03 L 901.78 161.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="196">
   <path class="kv110" d="M 1257.45 286.24 L 1257.45 303.33" stroke-width="1" zvalue="211"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="199@1" LinkObjectIDznd="4@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1257.45 286.24 L 1257.45 303.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="195">
   <path class="kv110" d="M 1257.48 262.4 L 1257.48 240.55" stroke-width="1" zvalue="213"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="199@0" LinkObjectIDznd="198@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1257.48 262.4 L 1257.48 240.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="194">
   <path class="kv110" d="M 1257.96 221.43 L 1257.96 192.24" stroke-width="1" zvalue="214"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="198@0" LinkObjectIDznd="200@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1257.96 221.43 L 1257.96 192.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="193">
   <path class="kv110" d="M 1275.25 252.05 L 1257.48 252.05" stroke-width="1" zvalue="215"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="197@0" LinkObjectIDznd="195" MaxPinNum="2"/>
   </metadata>
  <path d="M 1275.25 252.05 L 1257.48 252.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="191">
   <path class="kv110" d="M 1275.25 206.05 L 1257.96 206.05" stroke-width="1" zvalue="217"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="192@0" LinkObjectIDznd="194" MaxPinNum="2"/>
   </metadata>
  <path d="M 1275.25 206.05 L 1257.96 206.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="190">
   <path class="kv110" d="M 1257.78 151.91 L 1257.78 168.4" stroke-width="1" zvalue="219"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="201@0" LinkObjectIDznd="200@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1257.78 151.91 L 1257.78 168.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="188">
   <path class="kv110" d="M 1241.37 161.03 L 1257.78 161.03" stroke-width="1" zvalue="221"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="189@0" LinkObjectIDznd="190" MaxPinNum="2"/>
   </metadata>
  <path d="M 1241.37 161.03 L 1257.78 161.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="222">
   <path class="kv110" d="M 685.45 286.24 L 685.45 305" stroke-width="1" zvalue="229"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="219@1" LinkObjectIDznd="1@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 685.45 286.24 L 685.45 305" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="226">
   <path class="kv110" d="M 723.1 249.61 L 685.48 249.61 L 685.48 262.4" stroke-width="1" zvalue="233"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="216@0" LinkObjectIDznd="219@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 723.1 249.61 L 685.48 249.61 L 685.48 262.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="227">
   <path class="kv110" d="M 781.48 262.4 L 781.48 249.56 L 742.82 249.56" stroke-width="1" zvalue="234"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="221@0" LinkObjectIDznd="216@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 781.48 262.4 L 781.48 249.56 L 742.82 249.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="228">
   <path class="kv110" d="M 781.45 286.24 L 781.45 303.33" stroke-width="1" zvalue="235"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="221@1" LinkObjectIDznd="4@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 781.45 286.24 L 781.45 303.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="231">
   <path class="kv110" d="M 686.07 235.75 L 686.07 251" stroke-width="1" zvalue="239"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="230@0" LinkObjectIDznd="226" MaxPinNum="2"/>
   </metadata>
  <path d="M 686.07 235.75 L 686.07 251" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="242">
   <path class="kv110" d="M 825.45 353.24 L 825.45 368.02" stroke-width="1" zvalue="254"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="244@1" LinkObjectIDznd="243@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 825.45 353.24 L 825.45 368.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="245">
   <path class="kv110" d="M 825.48 329.4 L 825.48 303.33" stroke-width="1" zvalue="255"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="244@0" LinkObjectIDznd="4@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 825.48 329.4 L 825.48 303.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="250">
   <path class="kv10" d="M 165.48 696.4 L 165.48 677" stroke-width="1" zvalue="261"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="246@0" LinkObjectIDznd="151@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 165.48 696.4 L 165.48 677" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="251">
   <path class="kv10" d="M 165.45 720.24 L 165.45 748.43" stroke-width="1" zvalue="262"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="246@1" LinkObjectIDznd="247@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 165.45 720.24 L 165.45 748.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="252">
   <path class="kv10" d="M 165.48 783.4 L 165.48 767.55" stroke-width="1" zvalue="263"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="248@0" LinkObjectIDznd="247@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 165.48 783.4 L 165.48 767.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="253">
   <path class="kv10" d="M 183.25 734.05 L 165.45 734.05" stroke-width="1" zvalue="264"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="249@0" LinkObjectIDznd="251" MaxPinNum="2"/>
   </metadata>
  <path d="M 183.25 734.05 L 165.45 734.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="259">
   <path class="kv10" d="M 165 826.2 L 165 807.24" stroke-width="1" zvalue="266"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="258@0" LinkObjectIDznd="248@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 165 826.2 L 165 807.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="271">
   <path class="kv10" d="M 361.48 696.4 L 361.45 677" stroke-width="1" zvalue="275"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="275@0" LinkObjectIDznd="152" MaxPinNum="2"/>
   </metadata>
  <path d="M 361.48 696.4 L 361.45 677" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="270">
   <path class="kv10" d="M 361.45 720.24 L 361.45 748.43" stroke-width="1" zvalue="277"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="275@1" LinkObjectIDznd="273@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 361.45 720.24 L 361.45 748.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="269">
   <path class="kv10" d="M 361.48 783.4 L 361.48 767.55" stroke-width="1" zvalue="278"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="274@0" LinkObjectIDznd="273@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 361.48 783.4 L 361.48 767.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="268">
   <path class="kv10" d="M 379.25 734.05 L 361.45 734.05" stroke-width="1" zvalue="279"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@0" LinkObjectIDznd="270" MaxPinNum="2"/>
   </metadata>
  <path d="M 379.25 734.05 L 361.45 734.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="266">
   <path class="kv10" d="M 361 826.2 L 361 807.24" stroke-width="1" zvalue="281"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="267@0" LinkObjectIDznd="274@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 361 826.2 L 361 807.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="286">
   <path class="kv10" d="M 617.48 696.4 L 617.45 677" stroke-width="1" zvalue="291"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="290@0" LinkObjectIDznd="151@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 617.48 696.4 L 617.45 677" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="285">
   <path class="kv10" d="M 617.45 720.24 L 617.45 748.43" stroke-width="1" zvalue="293"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="290@1" LinkObjectIDznd="288@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 617.45 720.24 L 617.45 748.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="284">
   <path class="kv10" d="M 617.48 783.4 L 617.48 767.55" stroke-width="1" zvalue="294"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="289@0" LinkObjectIDznd="288@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 617.48 783.4 L 617.48 767.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="283">
   <path class="kv10" d="M 635.25 734.07 L 617.45 734.07" stroke-width="1" zvalue="295"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="287@0" LinkObjectIDznd="285" MaxPinNum="2"/>
   </metadata>
  <path d="M 635.25 734.07 L 617.45 734.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="281">
   <path class="kv10" d="M 617 826.2 L 617 807.24" stroke-width="1" zvalue="297"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="282@0" LinkObjectIDznd="289@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 617 826.2 L 617 807.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="374">
   <path class="kv10" d="M 846.45 720.24 L 846.45 748.43" stroke-width="1" zvalue="355"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="378@1" LinkObjectIDznd="376@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 846.45 720.24 L 846.45 748.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="373">
   <path class="kv10" d="M 846.48 783.4 L 846.48 767.55" stroke-width="1" zvalue="356"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="377@0" LinkObjectIDznd="376@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 846.48 783.4 L 846.48 767.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="372">
   <path class="kv10" d="M 864.25 734.05 L 846.45 734.05" stroke-width="1" zvalue="357"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="375@0" LinkObjectIDznd="374" MaxPinNum="2"/>
   </metadata>
  <path d="M 864.25 734.05 L 846.45 734.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="370">
   <path class="kv10" d="M 846 826.2 L 846 807.24" stroke-width="1" zvalue="359"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="371@0" LinkObjectIDznd="377@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 846 826.2 L 846 807.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="365">
   <path class="kv10" d="M 1110.45 720.24 L 1110.45 748.43" stroke-width="1" zvalue="369"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="369@1" LinkObjectIDznd="367@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1110.45 720.24 L 1110.45 748.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="364">
   <path class="kv10" d="M 1110.48 783.4 L 1110.48 767.55" stroke-width="1" zvalue="370"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="368@0" LinkObjectIDznd="367@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1110.48 783.4 L 1110.48 767.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="363">
   <path class="kv10" d="M 1128.25 734.05 L 1110.45 734.05" stroke-width="1" zvalue="371"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="366@0" LinkObjectIDznd="365" MaxPinNum="2"/>
   </metadata>
  <path d="M 1128.25 734.05 L 1110.45 734.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="361">
   <path class="kv10" d="M 1110 826.2 L 1110 807.24" stroke-width="1" zvalue="373"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="362@0" LinkObjectIDznd="368@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1110 826.2 L 1110 807.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="356">
   <path class="kv10" d="M 1350.45 720.24 L 1350.45 748.43" stroke-width="1" zvalue="383"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="360@1" LinkObjectIDznd="358@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1350.45 720.24 L 1350.45 748.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="355">
   <path class="kv10" d="M 1350.48 783.4 L 1350.48 767.55" stroke-width="1" zvalue="384"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="359@0" LinkObjectIDznd="358@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1350.48 783.4 L 1350.48 767.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="354">
   <path class="kv10" d="M 1368.25 734.05 L 1350.45 734.05" stroke-width="1" zvalue="385"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="357@0" LinkObjectIDznd="356" MaxPinNum="2"/>
   </metadata>
  <path d="M 1368.25 734.05 L 1350.45 734.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="352">
   <path class="kv10" d="M 1350 826.2 L 1350 807.24" stroke-width="1" zvalue="387"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="353@0" LinkObjectIDznd="359@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1350 826.2 L 1350 807.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="379">
   <path class="kv10" d="M 846.48 696.4 L 846.48 677" stroke-width="1" zvalue="389"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="378@0" LinkObjectIDznd="150@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 846.48 696.4 L 846.48 677" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="380">
   <path class="kv10" d="M 1110.48 696.4 L 1110.48 677" stroke-width="1" zvalue="390"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="369@0" LinkObjectIDznd="160" MaxPinNum="2"/>
   </metadata>
  <path d="M 1110.48 696.4 L 1110.48 677" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="381">
   <path class="kv10" d="M 1350.48 696.4 L 1350.48 677" stroke-width="1" zvalue="391"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="360@0" LinkObjectIDznd="150@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1350.48 696.4 L 1350.48 677" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="394">
   <path class="kv10" d="M 685.45 654.24 L 685.45 677" stroke-width="1" zvalue="399"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="396@1" LinkObjectIDznd="151@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 685.45 654.24 L 685.45 677" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="393">
   <path class="kv10" d="M 723.1 617.61 L 685.48 617.61 L 685.48 630.4" stroke-width="1" zvalue="400"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="397@0" LinkObjectIDznd="396@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 723.1 617.61 L 685.48 617.61 L 685.48 630.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="392">
   <path class="kv10" d="M 781.48 630.4 L 781.48 617.56 L 742.82 617.56" stroke-width="1" zvalue="401"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="395@0" LinkObjectIDznd="397@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 781.48 630.4 L 781.48 617.56 L 742.82 617.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="391">
   <path class="kv10" d="M 781.45 654.24 L 781.45 677" stroke-width="1" zvalue="402"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="395@1" LinkObjectIDznd="150@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 781.45 654.24 L 781.45 677" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="389">
   <path class="kv10" d="M 686.05 603.75 L 686.05 619" stroke-width="1" zvalue="405"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="390@0" LinkObjectIDznd="393" MaxPinNum="2"/>
   </metadata>
  <path d="M 686.05 603.75 L 686.05 619" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="387">
   <path class="kv10" d="M 781.44 603.75 L 781.48 618" stroke-width="1" zvalue="408"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="388@0" LinkObjectIDznd="392" MaxPinNum="2"/>
   </metadata>
  <path d="M 781.44 603.75 L 781.48 618" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="403">
   <path class="kv10" d="M 165.48 657.04 L 165.48 677" stroke-width="1" zvalue="415"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="402@0" LinkObjectIDznd="250" MaxPinNum="2"/>
   </metadata>
  <path d="M 165.48 657.04 L 165.48 677" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="404">
   <path class="kv10" d="M 165.5 633.21 L 165.5 617.34" stroke-width="1" zvalue="416"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="402@1" LinkObjectIDznd="401@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 165.5 633.21 L 165.5 617.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="407">
   <path class="kv10" d="M 1349.48 657.04 L 1349.48 677" stroke-width="1" zvalue="421"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="409@0" LinkObjectIDznd="381" MaxPinNum="2"/>
   </metadata>
  <path d="M 1349.48 657.04 L 1349.48 677" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="406">
   <path class="kv10" d="M 1349.5 633.21 L 1349.5 617.34" stroke-width="1" zvalue="422"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="409@1" LinkObjectIDznd="408@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1349.5 633.21 L 1349.5 617.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="412">
   <path class="kv110" d="M 361.45 430.24 L 361.45 456.45" stroke-width="1" zvalue="425"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="131@1" LinkObjectIDznd="127@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 361.45 430.24 L 361.45 456.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="413">
   <path class="kv110" d="M 653.45 368.02 L 653.45 353.24" stroke-width="1" zvalue="426"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="237@0" LinkObjectIDznd="236@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 653.45 368.02 L 653.45 353.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="kv110" d="M 781.44 235.75 L 781.44 249.56" stroke-width="1" zvalue="492"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="233@0" LinkObjectIDznd="227" MaxPinNum="2"/>
   </metadata>
  <path d="M 781.44 235.75 L 781.44 249.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="7">
   <path class="kv110" d="M 653.48 329.4 L 653.48 305" stroke-width="1" zvalue="497"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="236@0" LinkObjectIDznd="1@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 653.48 329.4 L 653.48 305" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="2">
   <path class="kv110" d="M 169.48 168.4 L 169.48 151.91" stroke-width="1" zvalue="498"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="20@0" LinkObjectIDznd="" MaxPinNum="2"/>
   </metadata>
  <path d="M 169.48 168.4 L 169.48 151.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="127">
   <g id="1270">
    <use class="kv110" height="30" transform="rotate(0,361.426,488.145) scale(2.27083,2.27631) translate(-187.016,-254.554)" width="24" x="334.18" xlink:href="#PowerTransformer2:可调两卷变_0" y="454" zvalue="128"/>
    <metadata>
     <cge:PSR_Ref ObjectID="null" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1271">
    <use class="kv10" height="30" transform="rotate(0,361.426,488.145) scale(2.27083,2.27631) translate(-187.016,-254.554)" width="24" x="334.18" xlink:href="#PowerTransformer2:可调两卷变_1" y="454" zvalue="128"/>
    <metadata>
     <cge:PSR_Ref ObjectID="null" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399520157698" ObjectName="#1主变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,361.426,488.145) scale(2.27083,2.27631) translate(-187.016,-254.554)" width="24" x="334.18" y="454"/></g>
  <g id="174">
   <g id="1740">
    <use class="kv110" height="30" transform="rotate(0,1109.48,488.145) scale(2.27083,2.27631) translate(-605.651,-254.554)" width="24" x="1082.23" xlink:href="#PowerTransformer2:可调两卷变_0" y="454" zvalue="158"/>
    <metadata>
     <cge:PSR_Ref ObjectID="null" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1741">
    <use class="kv10" height="30" transform="rotate(0,1109.48,488.145) scale(2.27083,2.27631) translate(-605.651,-254.554)" width="24" x="1082.23" xlink:href="#PowerTransformer2:可调两卷变_1" y="454" zvalue="158"/>
    <metadata>
     <cge:PSR_Ref ObjectID="null" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399520223234" ObjectName="#2主变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1109.48,488.145) scale(2.27083,2.27631) translate(-605.651,-254.554)" width="24" x="1082.23" y="454"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="258">
   <use class="kv10" height="30" transform="rotate(180,165,837) scale(1,0.8) translate(0,206.25)" width="12" x="159" xlink:href="#EnergyConsumer:负荷_0" y="825" zvalue="265"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453460033538" ObjectName="10kV备用一线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,165,837) scale(1,0.8) translate(0,206.25)" width="12" x="159" y="825"/></g>
  <g id="267">
   <use class="kv10" height="30" transform="rotate(180,361,837) scale(1,0.8) translate(0,206.25)" width="12" x="355" xlink:href="#EnergyConsumer:负荷_0" y="825" zvalue="280"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453460099074" ObjectName="10kV备用二线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,361,837) scale(1,0.8) translate(0,206.25)" width="12" x="355" y="825"/></g>
  <g id="282">
   <use class="kv10" height="30" transform="rotate(180,617,837) scale(1,0.8) translate(0,206.25)" width="12" x="611" xlink:href="#EnergyConsumer:负荷_0" y="825" zvalue="296"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453460426754" ObjectName="10kV备用三线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,617,837) scale(1,0.8) translate(0,206.25)" width="12" x="611" y="825"/></g>
  <g id="371">
   <use class="kv10" height="30" transform="rotate(180,846,837) scale(1,0.8) translate(0,206.25)" width="12" x="840" xlink:href="#EnergyConsumer:负荷_0" y="825" zvalue="358"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453461409795" ObjectName="10kV备用四线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,846,837) scale(1,0.8) translate(0,206.25)" width="12" x="840" y="825"/></g>
  <g id="362">
   <use class="kv10" height="30" transform="rotate(180,1110,837) scale(1,0.8) translate(0,206.25)" width="12" x="1104" xlink:href="#EnergyConsumer:负荷_0" y="825" zvalue="372"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453461082115" ObjectName="10kV备用五线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1110,837) scale(1,0.8) translate(0,206.25)" width="12" x="1104" y="825"/></g>
  <g id="353">
   <use class="kv10" height="30" transform="rotate(180,1350,837) scale(1,0.8) translate(0,206.25)" width="12" x="1344" xlink:href="#EnergyConsumer:负荷_0" y="825" zvalue="386"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453460754435" ObjectName="10kV备用六线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1350,837) scale(1,0.8) translate(0,206.25)" width="12" x="1344" y="825"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="13">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="13" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,525.778,52.5) scale(1,1) translate(0,0)" writing-mode="lr" x="525.9299999999999" xml:space="preserve" y="58.39" zvalue="1">P:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="14">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="14" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1257.78,52.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1257.93" xml:space="preserve" y="58.39" zvalue="1">P:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="15">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="15" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,901.778,52.5) scale(1,1) translate(0,0)" writing-mode="lr" x="901.9299999999999" xml:space="preserve" y="58.39" zvalue="1">P:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="17">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="17" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,525.778,74.5) scale(1,1) translate(0,0)" writing-mode="lr" x="525.9299999999999" xml:space="preserve" y="80.39" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="22">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="22" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1257.78,74.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1257.93" xml:space="preserve" y="80.39" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="27">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="27" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,901.778,74.5) scale(1,1) translate(0,0)" writing-mode="lr" x="901.9299999999999" xml:space="preserve" y="80.39" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="29">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="29" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,525.778,96.5) scale(1,1) translate(0,0)" writing-mode="lr" x="525.9299999999999" xml:space="preserve" y="102.39" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="30">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="30" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1257.78,96.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1257.93" xml:space="preserve" y="102.39" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="31">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="31" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,901.778,96.5) scale(1,1) translate(0,0)" writing-mode="lr" x="901.9299999999999" xml:space="preserve" y="102.39" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="32">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="32" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,733,178) scale(1,1) translate(0,0)" writing-mode="lr" x="733.15" xml:space="preserve" y="183.89" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="38">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="38" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,733,538) scale(1,1) translate(0,0)" writing-mode="lr" x="733.15" xml:space="preserve" y="543.89" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="43">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="43" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,733,196) scale(1,1) translate(0,0)" writing-mode="lr" x="733.15" xml:space="preserve" y="201.89" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="47">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="47" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,733,560) scale(1,1) translate(0,0)" writing-mode="lr" x="733.15" xml:space="preserve" y="565.89" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="48">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="48" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,733,214) scale(1,1) translate(0,0)" writing-mode="lr" x="733.15" xml:space="preserve" y="219.89" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="49">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="49" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,728,578) scale(1,1) translate(0,0)" writing-mode="lr" x="728.15" xml:space="preserve" y="583.89" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="50">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="50" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,61.6071,284) scale(1,1) translate(1.03808e-15,0)" writing-mode="lr" x="61.83" xml:space="preserve" y="289.89" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="51">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="51" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1401.68,282.333) scale(1,1) translate(1.49816e-13,0)" writing-mode="lr" x="1401.9" xml:space="preserve" y="288.22" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="52">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="52" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1419.97,654.571) scale(1,1) translate(1.51846e-13,0)" writing-mode="lr" x="1420.19" xml:space="preserve" y="660.46" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="53">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="53" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,61.3214,657.429) scale(1,1) translate(1.00636e-15,0)" writing-mode="lr" x="61.55" xml:space="preserve" y="663.3200000000001" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="54">
   <text Format="f5.2" Plane="0" SignFlag="down" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="54" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,409.426,376) scale(1,1) translate(0,0)" writing-mode="lr" x="409.6" xml:space="preserve" y="381.89" zvalue="1">P:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="55">
   <text Format="f5.2" Plane="0" SignFlag="down" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="55" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1157.48,376) scale(1,1) translate(0,0)" writing-mode="lr" x="1157.66" xml:space="preserve" y="381.89" zvalue="1">P:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="56">
   <text Format="f5.2" Plane="0" SignFlag="down" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="56" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,409.426,398) scale(1,1) translate(0,0)" writing-mode="lr" x="409.6" xml:space="preserve" y="403.89" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="57">
   <text Format="f5.2" Plane="0" SignFlag="down" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="57" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1157.48,398) scale(1,1) translate(0,0)" writing-mode="lr" x="1157.66" xml:space="preserve" y="403.89" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="58">
   <text Format="f5.2" Plane="0" SignFlag="up" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="58" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,409.426,566.289) scale(1,1) translate(0,0)" writing-mode="lr" x="409.6" xml:space="preserve" y="572.1799999999999" zvalue="1">P:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="59">
   <text Format="f5.2" Plane="0" SignFlag="up" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="59" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1157.48,566.289) scale(1,1) translate(0,0)" writing-mode="lr" x="1157.66" xml:space="preserve" y="572.1799999999999" zvalue="1">P:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="60">
   <text Format="f5.2" Plane="0" SignFlag="up" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="60" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,409.426,588.289) scale(1,1) translate(0,0)" writing-mode="lr" x="409.6" xml:space="preserve" y="594.1799999999999" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="61">
   <text Format="f5.2" Plane="0" SignFlag="up" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="61" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1157.48,588.289) scale(1,1) translate(0,0)" writing-mode="lr" x="1157.66" xml:space="preserve" y="594.1799999999999" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="62">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="62" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,409.426,420) scale(1,1) translate(0,0)" writing-mode="lr" x="409.6" xml:space="preserve" y="425.89" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="63">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="63" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1157.48,420) scale(1,1) translate(0,0)" writing-mode="lr" x="1157.66" xml:space="preserve" y="425.89" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="64">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" id="64" prefix="油温：" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,449.562,485.895) scale(1,1) translate(0,0)" writing-mode="lr" x="449.72" xml:space="preserve" y="491.79" zvalue="1">油温：dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="65">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" id="65" prefix="油温：" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1176.73,485.895) scale(1,1) translate(0,0)" writing-mode="lr" x="1176.88" xml:space="preserve" y="491.79" zvalue="1">油温：dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="66">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" id="66" prefix="档位：" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,449.187,465.145) scale(1,1) translate(0,0)" writing-mode="lr" x="449.34" xml:space="preserve" y="471.04" zvalue="1">档位：ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="87">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" id="87" prefix="档位：" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1204.23,465.145) scale(1,1) translate(0,0)" writing-mode="lr" x="1204.38" xml:space="preserve" y="471.04" zvalue="1">档位：ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="88">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="88" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,409.426,610.289) scale(1,1) translate(0,0)" writing-mode="lr" x="409.6" xml:space="preserve" y="616.1799999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="89">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="89" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1157.48,610.289) scale(1,1) translate(0,0)" writing-mode="lr" x="1157.66" xml:space="preserve" y="616.1799999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="92">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="92" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,165,878.25) scale(1,1) translate(0,0)" writing-mode="lr" x="165.15" xml:space="preserve" y="884.14" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="93">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="93" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,361,878.25) scale(1,1) translate(0,0)" writing-mode="lr" x="361.15" xml:space="preserve" y="884.14" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="94">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="94" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,617,878.25) scale(1,1) translate(0,0)" writing-mode="lr" x="617.15" xml:space="preserve" y="884.14" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="95">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="95" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1350,878.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1350.15" xml:space="preserve" y="884.14" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="96">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="96" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1110,878.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1110.15" xml:space="preserve" y="884.14" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="97">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="97" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,846,878.25) scale(1,1) translate(0,0)" writing-mode="lr" x="846.15" xml:space="preserve" y="884.14" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="98">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="98" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,165,900.25) scale(1,1) translate(0,0)" writing-mode="lr" x="165.15" xml:space="preserve" y="906.14" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="99">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="99" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,361,900.25) scale(1,1) translate(0,0)" writing-mode="lr" x="361.15" xml:space="preserve" y="906.14" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="100">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="100" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,617,900.25) scale(1,1) translate(0,0)" writing-mode="lr" x="617.15" xml:space="preserve" y="906.14" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="101">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="101" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1350,900.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1350.15" xml:space="preserve" y="906.14" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="102">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="102" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1110,900.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1110.15" xml:space="preserve" y="906.14" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="103">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="103" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,846,900.25) scale(1,1) translate(0,0)" writing-mode="lr" x="846.15" xml:space="preserve" y="906.14" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="104">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="104" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,165,922.25) scale(1,1) translate(0,0)" writing-mode="lr" x="165.15" xml:space="preserve" y="928.14" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="105">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="105" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,361,922.25) scale(1,1) translate(0,0)" writing-mode="lr" x="361.15" xml:space="preserve" y="928.14" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="106">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="106" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,617,922.25) scale(1,1) translate(0,0)" writing-mode="lr" x="617.15" xml:space="preserve" y="928.14" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="107">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="107" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1350,922.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1350.15" xml:space="preserve" y="928.14" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="108">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="108" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1110,922.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1110.15" xml:space="preserve" y="928.14" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="109">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="109" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,846,922.25) scale(1,1) translate(0,0)" writing-mode="lr" x="846.15" xml:space="preserve" y="928.14" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
</svg>