<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549685714945" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="Accessory:PT6_0" viewBox="0,0,15,20">
   <use terminal-index="0" type="0" x="7.560000000000002" xlink:href="#terminal" y="0.42361930658363"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.160000000000005" x2="8.559999999999999" y1="15.48035578286757" y2="14.24281579851547"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.060000000000002" x2="8.459999999999996" y1="16.09912577504353" y2="17.33666575939564"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.459999999999999" x2="5.060000000000007" y1="4.75500925181603" y2="5.992549236168133"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.460000000000005" x2="9.859999999999998" y1="4.75500925181603" y2="5.992549236168133"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.859999999999999" x2="8.859999999999999" y1="14.65532912663279" y2="17.13040909533699"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.460000000000003" x2="7.460000000000003" y1="2.279929283111803" y2="4.755009251816007"/>
   <ellipse cx="7.5" cy="6.51" fill-opacity="0" rx="6" ry="6.06" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.7" cy="13.44" fill-opacity="0" rx="6" ry="6.06" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:D-Y_0" viewBox="0,0,30,50">
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01666666666667" x2="15.01666666666667" y1="37.50000000000001" y2="32.91666666666667"/>
   <use terminal-index="1" type="1" x="15.00325153685922" xlink:href="#terminal" y="49.86055225321343"/>
   <use terminal-index="2" type="2" x="15" xlink:href="#terminal" y="37.75085860895189"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.25" x2="15" y1="43.08333333333334" y2="37.75000000000001"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15" x2="20" y1="37.74761215261902" y2="42.91666666666667"/>
   <ellipse cx="15" cy="35.25" fill-opacity="0" rx="14.75" ry="14.75" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:D-Y_1" viewBox="0,0,30,50">
   <path d="M 10 16 L 20.1667 16 L 15 7.83333 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.75" ry="14.75" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="0" type="1" x="15.00731595793324" xlink:href="#terminal" y="0.3902606310013752"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Accessory:线路PT11带避雷器_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="20" xlink:href="#terminal" y="0.9166666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.91666666666667" x2="22.91666666666667" y1="23.75" y2="23.75"/>
   <rect fill-opacity="0" height="10" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,20,11) scale(1,1) translate(0,0)" width="6" x="17" y="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32" x2="20" y1="5" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32" x2="32" y1="17" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.13040845230975" x2="32.13040845230975" y1="16.76788570496156" y2="23.53635804601289"/>
   <rect fill-opacity="0" height="6.05" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,32.04,24.56) scale(1,1) translate(0,0)" width="15.34" x="24.37" y="21.53"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.98821300514558" x2="19.98821300514558" y1="1.029523490692871" y2="18.75"/>
   <path d="M 30.0147 23.4481 L 34.2865 23.4481 L 32.115 28.6155 L 30.0147 23.4481" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <ellipse cx="19.76" cy="32.23" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.76" cy="24.17" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.22179295459144" x2="32.22179295459144" y1="32.22550978083666" y2="36.2625308385742"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="29.01788570496155" x2="35.42570020422134" y1="36.23023467011234" y2="36.23023467011234"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="30.26384963537316" x2="34.53572596821302" y1="37.52208140858838" y2="37.52208140858838"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.15382387138148" x2="33.11176719059974" y1="38.49096646244535" y2="38.49096646244535"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.91666666666667" x2="22.91666666666667" y1="32.91666666666667" y2="32.91666666666667"/>
  </symbol>
  <symbol id="Disconnector:联体小车刀闸2_0" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="9.583333333333332" y2="1.050000000000001"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.083333333333333" x2="7.083333333333333" y1="26.41666666666667" y2="34.83333333333334"/>
   <rect fill-opacity="0" height="17" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,18) scale(1,1) translate(0,0)" width="8" x="3" y="9.5"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
  </symbol>
  <symbol id="Disconnector:联体小车刀闸2_1" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="35" y2="1.166666666666664"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <rect fill-opacity="0" height="17" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,18) scale(1,1) translate(0,0)" width="8" x="3" y="9.5"/>
  </symbol>
  <symbol id="Disconnector:联体小车刀闸2_2" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.083333333333333" x2="7.083333333333333" y1="9.999999999999995" y2="1.299999999999994"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="1.499999999999999" y1="26" y2="9.966666666666669"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.999999999999998" x2="12.41666666666667" y1="26.25" y2="10.05"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.499999999999999" x2="5.916666666666666" y1="3.583333333333334" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="4.416666666666668" y2="20.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="4.249999999999998" y2="20.16666666666667"/>
  </symbol>
  <symbol id="Ground:大地_0" viewBox="0,0,12,18">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6" x2="6" y1="13.08333333333334" y2="0.1666666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.5833333333333313" x2="11.25" y1="12.99453511141348" y2="12.99453511141348"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.083333333333333" x2="8" y1="17.63116790988687" y2="17.63116790988687"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666667" x2="10.33333333333333" y1="15.40451817731685" y2="15.40451817731685"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV高河一级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="269.25" x="43.75" xlink:href="logo.png" y="45.25"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,178.375,75.25) scale(1,1) translate(0,0)" writing-mode="lr" x="178.37" xml:space="preserve" y="78.75" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,180.083,74.9403) scale(1,1) translate(6.85563e-15,0)" writing-mode="lr" x="180.08" xml:space="preserve" y="83.94" zvalue="3">110kV高河一级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="37" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,84.4375,190) scale(1,1) translate(0,0)" width="72.88" x="48" y="178" zvalue="272"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,84.4375,190) scale(1,1) translate(0,0)" writing-mode="lr" x="84.44" xml:space="preserve" y="194.5" zvalue="272">信号一览</text>
  <line fill="none" id="32" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="377.75" x2="377.75" y1="13.25" y2="1043.25" zvalue="4"/>
  <line fill="none" id="30" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.750000000000227" x2="370.7499999999998" y1="149.1204926140824" y2="149.1204926140824" zvalue="6"/>
  <line fill="none" id="28" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.750000000000227" x2="370.7499999999998" y1="619.1204926140825" y2="619.1204926140825" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="934.2500000000002" y2="934.2500000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="973.4133000000002" y2="973.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="3.75" y1="934.2500000000002" y2="973.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="934.2500000000002" y2="973.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="363.75" y1="934.2500000000002" y2="934.2500000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="363.75" y1="973.4133000000002" y2="973.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="934.2500000000002" y2="973.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.75" x2="363.75" y1="934.2500000000002" y2="973.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="973.4132700000002" y2="973.4132700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="1001.33167" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="3.75" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="183.75" y1="973.4132700000002" y2="973.4132700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="183.75" y1="1001.33167" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.75" x2="183.75" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="273.7500000000001" y1="973.4132700000002" y2="973.4132700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="273.7500000000001" y1="1001.33167" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="183.7500000000001" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.7500000000001" x2="273.7500000000001" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="363.75" y1="973.4132700000002" y2="973.4132700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="363.75" y1="1001.33167" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="273.75" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.75" x2="363.75" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="1001.3316" y2="1001.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="1029.25" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="3.75" y1="1001.3316" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="1001.3316" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="183.75" y1="1001.3316" y2="1001.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="183.75" y1="1029.25" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="1001.3316" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.75" x2="183.75" y1="1001.3316" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="273.7500000000001" y1="1001.3316" y2="1001.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="273.7500000000001" y1="1029.25" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="183.7500000000001" y1="1001.3316" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.7500000000001" x2="273.7500000000001" y1="1001.3316" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="363.75" y1="1001.3316" y2="1001.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="363.75" y1="1029.25" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="273.75" y1="1001.3316" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.75" x2="363.75" y1="1001.3316" y2="1029.25"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.75,954.25) scale(1,1) translate(0,0)" writing-mode="lr" x="48.75" xml:space="preserve" y="960.25" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,45.75,988.25) scale(1,1) translate(0,0)" writing-mode="lr" x="45.75" xml:space="preserve" y="994.25" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.75,988.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.75" xml:space="preserve" y="994.25" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44.75,1016.25) scale(1,1) translate(0,0)" writing-mode="lr" x="44.75" xml:space="preserve" y="1022.25" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.75,1016.25) scale(1,1) translate(0,0)" writing-mode="lr" x="226.75" xml:space="preserve" y="1022.25" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,69.25,648.75) scale(1,1) translate(0,2.09749e-13)" writing-mode="lr" x="69.25" xml:space="preserve" y="653.2500000000001" zvalue="19">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,190.149,186.091) scale(1,1) translate(0,0)" writing-mode="lr" x="190.15" xml:space="preserve" y="190.59" zvalue="20">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,295.149,186.091) scale(1,1) translate(0,0)" writing-mode="lr" x="295.15" xml:space="preserve" y="190.59" zvalue="21">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,228.804,956.25) scale(1,1) translate(0,0)" writing-mode="lr" x="228.8" xml:space="preserve" y="962.25" zvalue="28">GH1-01-2022</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,138.804,988.25) scale(1,1) translate(0,0)" writing-mode="lr" x="138.8" xml:space="preserve" y="994.25" zvalue="29">杨立超</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,318.804,988.25) scale(1,1) translate(0,0)" writing-mode="lr" x="318.8" xml:space="preserve" y="994.25" zvalue="30">20210218</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,571.533,761.449) scale(1,1) translate(0,0)" writing-mode="lr" x="571.53" xml:space="preserve" y="765.95" zvalue="38">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,790.692,812.835) scale(1,1) translate(-1.7217e-13,0)" writing-mode="lr" x="790.6900000000001" xml:space="preserve" y="817.34" zvalue="40">081</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,822.668,1011.63) scale(1,1) translate(0,-6.62384e-13)" writing-mode="lr" x="822.6684752281722" xml:space="preserve" y="1016.127752122349" zvalue="43">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1181.16,791.933) scale(1,1) translate(0,0)" writing-mode="lr" x="1181.16" xml:space="preserve" y="796.4299999999999" zvalue="49">082</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1208.95,1005.3) scale(1,1) translate(5.23781e-13,0)" writing-mode="lr" x="1208.95" xml:space="preserve" y="1009.8" zvalue="52">#2发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1468.81,961.19) scale(1,1) translate(0,0)" writing-mode="lr" x="1468.81" xml:space="preserve" y="965.6900000000001" zvalue="71">1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,914.645,848.376) scale(1,1) translate(0,0)" writing-mode="lr" x="914.65" xml:space="preserve" y="852.88" zvalue="77">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1259.92,831.648) scale(1,1) translate(0,0)" writing-mode="lr" x="1259.92" xml:space="preserve" y="836.15" zvalue="79">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,916.25,1021.06) scale(1,1) translate(-1.98896e-13,0)" writing-mode="lr" x="916.25" xml:space="preserve" y="1025.56" zvalue="87">机组PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,731.016,1017.53) scale(1,1) translate(0,0)" writing-mode="lr" x="731.02" xml:space="preserve" y="1022.03" zvalue="89">励磁变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,773.071,1019.67) scale(1,1) translate(0,0)" writing-mode="lr" x="773.0700000000001" xml:space="preserve" y="1024.17" zvalue="92">励磁PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1100.08,1013.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1100.08" xml:space="preserve" y="1017.75" zvalue="97">励磁变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1148.72,1015.39) scale(1,1) translate(0,0)" writing-mode="lr" x="1148.72" xml:space="preserve" y="1019.89" zvalue="99">励磁PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="91" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1285.49,1020.35) scale(1,1) translate(0,0)" writing-mode="lr" x="1285.49" xml:space="preserve" y="1024.85" zvalue="105">机组PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1530.9,835.998) scale(1,1) translate(0,0)" writing-mode="lr" x="1530.9" xml:space="preserve" y="840.5" zvalue="111">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="114" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,731.622,696.836) scale(1,1) translate(0,0)" writing-mode="lr" x="731.62" xml:space="preserve" y="701.34" zvalue="137">085</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="113" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,678.689,594.399) scale(1,1) translate(0,0)" writing-mode="lr" x="678.6900000000001" xml:space="preserve" y="598.9" zvalue="139">施工变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="112" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,764.658,630.681) scale(1,1) translate(0,0)" writing-mode="lr" x="764.66" xml:space="preserve" y="635.1799999999999" zvalue="142">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1026.97,696.836) scale(1,1) translate(0,0)" writing-mode="lr" x="1026.97" xml:space="preserve" y="701.34" zvalue="149">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="132" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1065.46,671.476) scale(1,1) translate(0,0)" writing-mode="lr" x="1065.46" xml:space="preserve" y="675.98" zvalue="151">00167</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="128" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,941.435,583.231) scale(1,1) translate(0,0)" writing-mode="lr" x="941.4299999999999" xml:space="preserve" y="587.73" zvalue="153">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1029.89,517.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1029.89" xml:space="preserve" y="521.9400000000001" zvalue="158">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1024.5,427.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1024.5" xml:space="preserve" y="431.72" zvalue="161">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="115" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1070.29,466.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1070.29" xml:space="preserve" y="471.17" zvalue="164">10117</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="133" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,586.111,383) scale(1,1) translate(0,0)" writing-mode="lr" x="586.11" xml:space="preserve" y="387.5" zvalue="166">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="142" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,734.222,299.889) scale(1,1) translate(0,0)" writing-mode="lr" x="734.22" xml:space="preserve" y="304.39" zvalue="169">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="145" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,740.739,253.889) scale(1,1) translate(0,0)" writing-mode="lr" x="740.74" xml:space="preserve" y="258.39" zvalue="171">182</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="147" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,733.852,193.667) scale(1,1) translate(0,0)" writing-mode="lr" x="733.85" xml:space="preserve" y="198.17" zvalue="173">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="149" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,772.063,207) scale(1,1) translate(0,0)" writing-mode="lr" x="772.0599999999999" xml:space="preserve" y="211.5" zvalue="175">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="152" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,769.286,137.889) scale(1,1) translate(0,0)" writing-mode="lr" x="769.29" xml:space="preserve" y="142.39" zvalue="177">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="141" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,720.95,82) scale(1,1) translate(0,0)" writing-mode="lr" x="720.95" xml:space="preserve" y="86.5" zvalue="178">110kV挖苦河三级电站线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="162" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1395.7,298.817) scale(1,1) translate(0,0)" writing-mode="lr" x="1395.7" xml:space="preserve" y="303.32" zvalue="188">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="160" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1402.22,252.817) scale(1,1) translate(0,0)" writing-mode="lr" x="1402.22" xml:space="preserve" y="257.32" zvalue="190">181</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="158" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1395.33,192.595) scale(1,1) translate(0,0)" writing-mode="lr" x="1395.33" xml:space="preserve" y="197.1" zvalue="192">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="156" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1430.76,136.817) scale(1,1) translate(0,0)" writing-mode="lr" x="1430.76" xml:space="preserve" y="141.32" zvalue="196">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="155" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1382.43,80.9286) scale(1,1) translate(0,0)" writing-mode="lr" x="1382.43" xml:space="preserve" y="85.43000000000001" zvalue="199">110kV高河一级电站线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="179" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,851.444,413.222) scale(1,1) translate(0,0)" writing-mode="lr" x="851.4400000000001" xml:space="preserve" y="417.72" zvalue="209">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="184" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,886.952,372) scale(1,1) translate(0,0)" writing-mode="lr" x="886.95" xml:space="preserve" y="376.5" zvalue="215">0</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="187" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,887.397,440.889) scale(1,1) translate(0,0)" writing-mode="lr" x="887.4" xml:space="preserve" y="445.39" zvalue="218">7</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="198" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1448,815.297) scale(1,1) translate(0,0)" writing-mode="lr" x="1448" xml:space="preserve" y="819.8" zvalue="227">0831</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="210" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1084.67,927.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1084.67" xml:space="preserve" y="932.17" zvalue="239">0921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="216" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1260.38,928.381) scale(1,1) translate(0,0)" writing-mode="lr" x="1260.38" xml:space="preserve" y="932.88" zvalue="245">0922</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="222" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,718,927.667) scale(1,1) translate(0,0)" writing-mode="lr" x="718" xml:space="preserve" y="932.17" zvalue="251">0911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="228" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,889.667,927.667) scale(1,1) translate(0,0)" writing-mode="lr" x="889.67" xml:space="preserve" y="932.17" zvalue="257">0912</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="238" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1370.78,681.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1370.78" xml:space="preserve" y="685.9400000000001" zvalue="267">0901</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.999999999999545" x2="186.9999999999995" y1="287.6586151123047" y2="287.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.999999999999545" x2="186.9999999999995" y1="313.6586151123047" y2="313.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.999999999999545" x2="5.999999999999545" y1="287.6586151123047" y2="313.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.9999999999995" x2="186.9999999999995" y1="287.6586151123047" y2="313.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.9999999999995" x2="367.9999999999995" y1="287.6586151123047" y2="287.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.9999999999995" x2="367.9999999999995" y1="313.6586151123047" y2="313.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.9999999999995" x2="186.9999999999995" y1="287.6586151123047" y2="313.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="367.9999999999995" x2="367.9999999999995" y1="287.6586151123047" y2="313.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.999999999999545" x2="186.9999999999995" y1="313.6586151123047" y2="313.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.999999999999545" x2="186.9999999999995" y1="337.9086151123047" y2="337.9086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.999999999999545" x2="5.999999999999545" y1="313.6586151123047" y2="337.9086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.9999999999995" x2="186.9999999999995" y1="313.6586151123047" y2="337.9086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.9999999999995" x2="367.9999999999995" y1="313.6586151123047" y2="313.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.9999999999995" x2="367.9999999999995" y1="337.9086151123047" y2="337.9086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.9999999999995" x2="186.9999999999995" y1="313.6586151123047" y2="337.9086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="367.9999999999995" x2="367.9999999999995" y1="313.6586151123047" y2="337.9086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.999999999999545" x2="186.9999999999995" y1="337.9086151123047" y2="337.9086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.999999999999545" x2="186.9999999999995" y1="360.6586151123047" y2="360.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.999999999999545" x2="5.999999999999545" y1="337.9086151123047" y2="360.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.9999999999995" x2="186.9999999999995" y1="337.9086151123047" y2="360.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.9999999999995" x2="367.9999999999995" y1="337.9086151123047" y2="337.9086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.9999999999995" x2="367.9999999999995" y1="360.6586151123047" y2="360.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.9999999999995" x2="186.9999999999995" y1="337.9086151123047" y2="360.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="367.9999999999995" x2="367.9999999999995" y1="337.9086151123047" y2="360.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.999999999999545" x2="186.9999999999995" y1="360.6586151123047" y2="360.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.999999999999545" x2="186.9999999999995" y1="383.4086151123047" y2="383.4086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.999999999999545" x2="5.999999999999545" y1="360.6586151123047" y2="383.4086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.9999999999995" x2="186.9999999999995" y1="360.6586151123047" y2="383.4086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.9999999999995" x2="367.9999999999995" y1="360.6586151123047" y2="360.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.9999999999995" x2="367.9999999999995" y1="383.4086151123047" y2="383.4086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.9999999999995" x2="186.9999999999995" y1="360.6586151123047" y2="383.4086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="367.9999999999995" x2="367.9999999999995" y1="360.6586151123047" y2="383.4086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.999999999999545" x2="186.9999999999995" y1="383.4086151123047" y2="383.4086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.999999999999545" x2="186.9999999999995" y1="406.1586151123047" y2="406.1586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.999999999999545" x2="5.999999999999545" y1="383.4086151123047" y2="406.1586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.9999999999995" x2="186.9999999999995" y1="383.4086151123047" y2="406.1586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.9999999999995" x2="367.9999999999995" y1="383.4086151123047" y2="383.4086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.9999999999995" x2="367.9999999999995" y1="406.1586151123047" y2="406.1586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.9999999999995" x2="186.9999999999995" y1="383.4086151123047" y2="406.1586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="367.9999999999995" x2="367.9999999999995" y1="383.4086151123047" y2="406.1586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.999999999999545" x2="186.9999999999995" y1="406.1586151123047" y2="406.1586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.999999999999545" x2="186.9999999999995" y1="428.9086151123047" y2="428.9086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.999999999999545" x2="5.999999999999545" y1="406.1586151123047" y2="428.9086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.9999999999995" x2="186.9999999999995" y1="406.1586151123047" y2="428.9086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.9999999999995" x2="367.9999999999995" y1="406.1586151123047" y2="406.1586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.9999999999995" x2="367.9999999999995" y1="428.9086151123047" y2="428.9086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.9999999999995" x2="186.9999999999995" y1="406.1586151123047" y2="428.9086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="367.9999999999995" x2="367.9999999999995" y1="406.1586151123047" y2="428.9086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.999999999999545" x2="186.9999999999995" y1="428.9086151123047" y2="428.9086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.999999999999545" x2="186.9999999999995" y1="451.6586151123047" y2="451.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.999999999999545" x2="5.999999999999545" y1="428.9086151123047" y2="451.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.9999999999995" x2="186.9999999999995" y1="428.9086151123047" y2="451.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.9999999999995" x2="367.9999999999995" y1="428.9086151123047" y2="428.9086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.9999999999995" x2="367.9999999999995" y1="451.6586151123047" y2="451.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.9999999999995" x2="186.9999999999995" y1="428.9086151123047" y2="451.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="367.9999999999995" x2="367.9999999999995" y1="428.9086151123047" y2="451.6586151123047"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,52.5,300.659) scale(1,1) translate(0,0)" writing-mode="lr" x="10" xml:space="preserve" y="305.16" zvalue="278">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,233,300.659) scale(1,1) translate(0,0)" writing-mode="lr" x="190.5" xml:space="preserve" y="305.16" zvalue="279">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.6875,373.909) scale(1,1) translate(0,0)" writing-mode="lr" x="55.69" xml:space="preserve" y="378.41" zvalue="280">110kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.625,372.659) scale(1,1) translate(0,0)" writing-mode="lr" x="234.62" xml:space="preserve" y="377.16" zvalue="281">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,51.5,326.659) scale(1,1) translate(0,0)" writing-mode="lr" x="9" xml:space="preserve" y="331.16" zvalue="288">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,232,326.659) scale(1,1) translate(0,0)" writing-mode="lr" x="189.5" xml:space="preserve" y="331.16" zvalue="289">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.6875,419.909) scale(1,1) translate(0,0)" writing-mode="lr" x="52.69" xml:space="preserve" y="424.41" zvalue="292">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,220.687,418.909) scale(1,1) translate(0,0)" writing-mode="lr" x="220.69" xml:space="preserve" y="423.41" zvalue="294">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.6875,442.909) scale(1,1) translate(0,0)" writing-mode="lr" x="52.69" xml:space="preserve" y="447.41" zvalue="295">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,220.687,441.909) scale(1,1) translate(0,0)" writing-mode="lr" x="220.69" xml:space="preserve" y="446.41" zvalue="296">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,51.5,349.659) scale(1,1) translate(0,0)" writing-mode="lr" x="9" xml:space="preserve" y="354.16" zvalue="297">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,231.5,348.659) scale(1,1) translate(0,0)" writing-mode="lr" x="189" xml:space="preserve" y="353.16" zvalue="299">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,827,1035) scale(1,1) translate(0,0)" writing-mode="lr" x="827" xml:space="preserve" y="1039.5" zvalue="338">10.5MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1208,1030) scale(1,1) translate(0,0)" writing-mode="lr" x="1208" xml:space="preserve" y="1034.5" zvalue="340">10.5MW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,710,548.125) scale(1,1) translate(0,0)" writing-mode="lr" x="710" xml:space="preserve" y="552.63" zvalue="342">10kV高挖线</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="48" y="178" zvalue="272"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="253">
   <path class="kv10" d="M 576.25 739.43 L 1709.11 739.43" stroke-width="6" zvalue="37"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674424782851" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674424782851"/></metadata>
  <path d="M 576.25 739.43 L 1709.11 739.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="kv110" d="M 578.89 353.56 L 1706.67 353.56" stroke-width="6" zvalue="165"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674424848387" ObjectName="110kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674424848387"/></metadata>
  <path d="M 578.89 353.56 L 1706.67 353.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="195">
   <use class="kv10" height="20" transform="rotate(0,821.669,816.297) scale(2.06048,2.06048) translate(-417.591,-409.524)" width="10" x="811.3663072734206" xlink:href="#Breaker:小车断路器_0" y="795.6923039475148" zvalue="39"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925228888067" ObjectName="#1发电机081断路器"/>
   <cge:TPSR_Ref TObjectID="6473925228888067"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,821.669,816.297) scale(2.06048,2.06048) translate(-417.591,-409.524)" width="10" x="811.3663072734206" y="795.6923039475148"/></g>
  <g id="161">
   <use class="kv10" height="20" transform="rotate(0,1207.95,800.297) scale(2.06048,2.06048) translate(-616.4,-401.289)" width="10" x="1197.647650789404" xlink:href="#Breaker:小车断路器_0" y="779.6923036958933" zvalue="48"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925228822531" ObjectName="#2发电机082断路器"/>
   <cge:TPSR_Ref TObjectID="6473925228822531"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1207.95,800.297) scale(2.06048,2.06048) translate(-616.4,-401.289)" width="10" x="1197.647650789404" y="779.6923036958933"/></g>
  <g id="119">
   <use class="kv10" height="20" transform="rotate(0,711.105,697.836) scale(2.06048,2.06048) translate(-360.686,-348.554)" width="10" x="700.8029182631758" xlink:href="#Breaker:小车断路器_0" y="677.2307711175686" zvalue="136"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925229019139" ObjectName="施工变085断路器"/>
   <cge:TPSR_Ref TObjectID="6473925229019139"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,711.105,697.836) scale(2.06048,2.06048) translate(-360.686,-348.554)" width="10" x="700.8029182631758" y="677.2307711175686"/></g>
  <g id="126">
   <use class="kv10" height="20" transform="rotate(0,1006.46,697.836) scale(2.06048,2.06048) translate(-512.696,-348.554)" width="10" x="996.1538461538462" xlink:href="#Breaker:小车断路器_0" y="677.2307710333187" zvalue="148"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925229084675" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473925229084675"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1006.46,697.836) scale(2.06048,2.06048) translate(-512.696,-348.554)" width="10" x="996.1538461538462" y="677.2307710333187"/></g>
  <g id="36">
   <use class="kv110" height="20" transform="rotate(0,1005.44,514.444) scale(1.22222,1.11111) translate(-181.697,-50.3333)" width="10" x="999.3333333333333" xlink:href="#Breaker:开关_0" y="503.3333333333333" zvalue="157"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925229150211" ObjectName="#1主变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473925229150211"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1005.44,514.444) scale(1.22222,1.11111) translate(-181.697,-50.3333)" width="10" x="999.3333333333333" y="503.3333333333333"/></g>
  <g id="136">
   <use class="kv110" height="20" transform="rotate(0,721.517,254.889) scale(1.22222,1.11111) translate(-130.074,-24.3778)" width="10" x="715.4061136028454" xlink:href="#Breaker:开关_0" y="243.7777777777778" zvalue="170"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925229215747" ObjectName="110kV挖苦河三级电站线182断路器"/>
   <cge:TPSR_Ref TObjectID="6473925229215747"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,721.517,254.889) scale(1.22222,1.11111) translate(-130.074,-24.3778)" width="10" x="715.4061136028454" y="243.7777777777778"/></g>
  <g id="175">
   <use class="kv110" height="20" transform="rotate(0,1383,253.817) scale(1.22222,1.11111) translate(-250.343,-24.2706)" width="10" x="1376.884187028473" xlink:href="#Breaker:开关_0" y="242.7063443320138" zvalue="189"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925229281283" ObjectName="110kV高河一级电站线181断路器"/>
   <cge:TPSR_Ref TObjectID="6473925229281283"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1383,253.817) scale(1.22222,1.11111) translate(-250.343,-24.2706)" width="10" x="1376.884187028473" y="242.7063443320138"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="63">
   <path class="kv10" d="M 821.64 941.19 L 821.67 834.84" stroke-width="1" zvalue="41"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="193@0" LinkObjectIDznd="195@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 821.64 941.19 L 821.67 834.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="kv10" d="M 881.15 849.39 L 821.66 849.39" stroke-width="1" zvalue="46"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="64@0" LinkObjectIDznd="63" MaxPinNum="2"/>
   </metadata>
  <path d="M 881.15 849.39 L 821.66 849.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="kv10" d="M 1207.95 941.19 L 1207.95 818.84" stroke-width="1" zvalue="50"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="159@0" LinkObjectIDznd="161@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1207.95 941.19 L 1207.95 818.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="kv10" d="M 1249.01 852.08 L 1207.95 852.08" stroke-width="1" zvalue="56"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="66@0" LinkObjectIDznd="59" MaxPinNum="2"/>
   </metadata>
  <path d="M 1249.01 852.08 L 1207.95 852.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="120">
   <path class="kv10" d="M 711.11 716.38 L 711.11 739.43" stroke-width="1" zvalue="144"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="119@1" LinkObjectIDznd="253@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 711.11 716.38 L 711.11 739.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="121">
   <path class="kv10" d="M 711.11 608.52 L 711.11 678.78" stroke-width="1" zvalue="145"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="118@0" LinkObjectIDznd="119@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 711.11 608.52 L 711.11 678.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="122">
   <path class="kv10" d="M 751.02 650.76 L 711.11 650.76" stroke-width="1" zvalue="146"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="116@0" LinkObjectIDznd="121" MaxPinNum="2"/>
   </metadata>
  <path d="M 751.02 650.76 L 711.11 650.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="129">
   <path class="kv10" d="M 1006.46 623.8 L 1006.46 678.78" stroke-width="1" zvalue="153"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="127@0" LinkObjectIDznd="126@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1006.46 623.8 L 1006.46 678.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="130">
   <path class="kv10" d="M 1006.46 716.38 L 1006.46 739.43" stroke-width="1" zvalue="154"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="126@1" LinkObjectIDznd="253@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1006.46 716.38 L 1006.46 739.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="131">
   <path class="kv10" d="M 1043.6 650.76 L 1006.46 650.76" stroke-width="1" zvalue="155"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@0" LinkObjectIDznd="129" MaxPinNum="2"/>
   </metadata>
  <path d="M 1043.6 650.76 L 1006.46 650.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="kv110" d="M 1005.53 525.06 L 1005.53 556.19" stroke-width="1" zvalue="158"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="36@1" LinkObjectIDznd="127@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1005.53 525.06 L 1005.53 556.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="kv110" d="M 1005.4 440.04 L 1005.4 503.81" stroke-width="1" zvalue="161"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60@0" LinkObjectIDznd="36@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1005.4 440.04 L 1005.4 503.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="kv110" d="M 1053.98 482.49 L 1005.4 482.49" stroke-width="1" zvalue="164"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="97@0" LinkObjectIDznd="83" MaxPinNum="2"/>
   </metadata>
  <path d="M 1053.98 482.49 L 1005.4 482.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="134">
   <path class="kv110" d="M 1005.43 416.21 L 1005.43 353.56" stroke-width="1" zvalue="166"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60@1" LinkObjectIDznd="124@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1005.43 416.21 L 1005.43 353.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="143">
   <path class="kv110" d="M 721.57 312.71 L 721.57 353.56" stroke-width="1" zvalue="178"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="135@0" LinkObjectIDznd="124@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 721.57 312.71 L 721.57 353.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="144">
   <path class="kv110" d="M 721.6 265.5 L 721.6 288.88" stroke-width="1" zvalue="179"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="136@1" LinkObjectIDznd="135@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 721.6 265.5 L 721.6 288.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="146">
   <path class="kv110" d="M 721.48 206.48 L 721.48 244.26" stroke-width="1" zvalue="180"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="137@0" LinkObjectIDznd="136@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 721.48 206.48 L 721.48 244.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="148">
   <path class="kv110" d="M 758.42 222.93 L 721.48 222.93" stroke-width="1" zvalue="181"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="138@0" LinkObjectIDznd="146" MaxPinNum="2"/>
   </metadata>
  <path d="M 758.42 222.93 L 721.48 222.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="150">
   <path class="kv110" d="M 721.51 136.5 L 721.51 182.65" stroke-width="1" zvalue="182"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="140@0" LinkObjectIDznd="137@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 721.51 136.5 L 721.51 182.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="151">
   <path class="kv110" d="M 758.42 154.93 L 721.51 154.93" stroke-width="1" zvalue="183"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="139@0" LinkObjectIDznd="150" MaxPinNum="2"/>
   </metadata>
  <path d="M 758.42 154.93 L 721.51 154.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="154">
   <path class="kv110" d="M 688.73 154.31 L 721.51 154.31" stroke-width="1" zvalue="185"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="153@0" LinkObjectIDznd="150" MaxPinNum="2"/>
   </metadata>
  <path d="M 688.73 154.31 L 721.51 154.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="170">
   <path class="kv110" d="M 1383.05 311.64 L 1383.05 353.56" stroke-width="1" zvalue="198"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="176@0" LinkObjectIDznd="124@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1383.05 311.64 L 1383.05 353.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="169">
   <path class="kv110" d="M 1383.08 264.43 L 1383.08 287.8" stroke-width="1" zvalue="200"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="175@1" LinkObjectIDznd="176@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1383.08 264.43 L 1383.08 287.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="168">
   <path class="kv110" d="M 1382.95 205.41 L 1382.95 243.19" stroke-width="1" zvalue="201"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="174@0" LinkObjectIDznd="175@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1382.95 205.41 L 1382.95 243.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="166">
   <path class="kv110" d="M 1382.98 135.43 L 1382.98 181.58" stroke-width="1" zvalue="203"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="171@0" LinkObjectIDznd="174@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1382.98 135.43 L 1382.98 181.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="165">
   <path class="kv110" d="M 1419.9 153.86 L 1382.98 153.86" stroke-width="1" zvalue="204"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="172@0" LinkObjectIDznd="166" MaxPinNum="2"/>
   </metadata>
  <path d="M 1419.9 153.86 L 1382.98 153.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="163">
   <path class="kv110" d="M 1349.58 153.86 L 1382.98 153.86" stroke-width="1" zvalue="206"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="164@0" LinkObjectIDznd="166" MaxPinNum="2"/>
   </metadata>
  <path d="M 1349.58 153.86 L 1382.98 153.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="180">
   <path class="kv110" d="M 826.04 402.21 L 826.04 353.56" stroke-width="1" zvalue="211"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="177@1" LinkObjectIDznd="124@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 826.04 402.21 L 826.04 353.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="181">
   <path class="kv110" d="M 826.01 481.14 L 826.01 426.04" stroke-width="1" zvalue="212"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="178@0" LinkObjectIDznd="177@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 826.01 481.14 L 826.01 426.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="183">
   <path class="kv110" d="M 873.31 385.16 L 826.04 385.16" stroke-width="1" zvalue="215"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="182@0" LinkObjectIDznd="180" MaxPinNum="2"/>
   </metadata>
  <path d="M 873.31 385.16 L 826.04 385.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="186">
   <path class="kv110" d="M 872.64 458.49 L 826.01 458.49" stroke-width="1" zvalue="218"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="185@0" LinkObjectIDznd="181" MaxPinNum="2"/>
   </metadata>
  <path d="M 872.64 458.49 L 826.01 458.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="199">
   <path class="kv10" d="M 1473.33 797.41 L 1473.33 739.43" stroke-width="1" zvalue="227"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="197@0" LinkObjectIDznd="253@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1473.33 797.41 L 1473.33 739.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="201">
   <path class="kv10" d="M 1474.08 916.21 L 1473.33 835.19" stroke-width="1" zvalue="229"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@0" LinkObjectIDznd="197@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1474.08 916.21 L 1473.33 835.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="211">
   <path class="kv10" d="M 1110 909.78 L 1110 880.22 L 1207.95 880.22" stroke-width="1" zvalue="239"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="209@0" LinkObjectIDznd="59" MaxPinNum="2"/>
   </metadata>
  <path d="M 1110 909.78 L 1110 880.22 L 1207.95 880.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="212">
   <path class="kv10" d="M 1109.42 986.31 L 1110 947.56" stroke-width="1" zvalue="240"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="76@0" LinkObjectIDznd="209@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1109.42 986.31 L 1110 947.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="213">
   <path class="kv10" d="M 1145.92 986.31 L 1145.92 964.68 L 1109.75 964.68" stroke-width="1" zvalue="241"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="74@0" LinkObjectIDznd="212" MaxPinNum="2"/>
   </metadata>
  <path d="M 1145.92 986.31 L 1145.92 964.68 L 1109.75 964.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="217">
   <path class="kv10" d="M 1286.27 910.49 L 1286.27 880.22 L 1207.77 880.22" stroke-width="1" zvalue="245"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="215@0" LinkObjectIDznd="211" MaxPinNum="2"/>
   </metadata>
  <path d="M 1286.27 910.49 L 1286.27 880.22 L 1207.77 880.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="218">
   <path class="kv10" d="M 1286.3 983.9 L 1286.27 948.27" stroke-width="1" zvalue="246"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="81@0" LinkObjectIDznd="215@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1286.3 983.9 L 1286.27 948.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="225">
   <path class="kv10" d="M 742.5 986.31 L 742.5 947.56" stroke-width="1" zvalue="253"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@0" LinkObjectIDznd="221@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 742.5 986.31 L 742.5 947.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="226">
   <path class="kv10" d="M 774.56 986.31 L 774.56 958 L 742.5 958" stroke-width="1" zvalue="254"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="55@0" LinkObjectIDznd="225" MaxPinNum="2"/>
   </metadata>
  <path d="M 774.56 986.31 L 774.56 958 L 742.5 958" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="229">
   <path class="kv10" d="M 915.56 909.78 L 915.56 878.1 L 821.66 878.1" stroke-width="1" zvalue="257"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="227@0" LinkObjectIDznd="63" MaxPinNum="2"/>
   </metadata>
  <path d="M 915.56 909.78 L 915.56 878.1 L 821.66 878.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="230">
   <path class="kv10" d="M 914.92 983.9 L 915.56 947.56" stroke-width="1" zvalue="258"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="50@0" LinkObjectIDznd="227@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 914.92 983.9 L 915.56 947.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="232">
   <path class="kv10" d="M 1207.95 781.24 L 1207.95 739.43" stroke-width="1" zvalue="260"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@0" LinkObjectIDznd="253@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1207.95 781.24 L 1207.95 739.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="233">
   <path class="kv10" d="M 821.67 797.24 L 821.67 739.43" stroke-width="1" zvalue="261"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="195@0" LinkObjectIDznd="253@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 821.67 797.24 L 821.67 739.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="234">
   <path class="kv10" d="M 1517.26 856.08 L 1473.53 856.08" stroke-width="1" zvalue="262"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="86@0" LinkObjectIDznd="201" MaxPinNum="2"/>
   </metadata>
  <path d="M 1517.26 856.08 L 1473.53 856.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="235">
   <path class="kv10" d="M 743.33 909.78 L 743.33 874.67 L 821.66 874.67" stroke-width="1" zvalue="263"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="221@0" LinkObjectIDznd="63" MaxPinNum="2"/>
   </metadata>
  <path d="M 743.33 909.78 L 743.33 874.67 L 821.66 874.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="239">
   <path class="kv10" d="M 1396.67 701.33 L 1396.67 739.43" stroke-width="1" zvalue="267"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="237@1" LinkObjectIDznd="253@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1396.67 701.33 L 1396.67 739.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="240">
   <path class="kv10" d="M 1396.07 624.56 L 1396.67 663.56" stroke-width="1" zvalue="268"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="189@0" LinkObjectIDznd="237@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1396.07 624.56 L 1396.67 663.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="242">
   <path class="kv110" d="M 1055.56 592.5 L 1055.56 572.74 L 1006.45 572.74" stroke-width="1" zvalue="270"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="241@0" LinkObjectIDznd="127@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1055.56 592.5 L 1055.56 572.74 L 1006.45 572.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="193">
   <use class="kv10" height="30" transform="rotate(0,821.638,963.979) scale(1.54536,1.54536) translate(-281.777,-332.009)" width="30" x="798.4578529557438" xlink:href="#Generator:发电机_0" y="940.7988039849256" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454893895682" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192454893895682"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,821.638,963.979) scale(1.54536,1.54536) translate(-281.777,-332.009)" width="30" x="798.4578529557438" y="940.7988039849256"/></g>
  <g id="159">
   <use class="kv10" height="30" transform="rotate(0,1207.95,963.979) scale(1.54536,1.54536) translate(-418.107,-332.009)" width="30" x="1184.769660218963" xlink:href="#Generator:发电机_0" y="940.7988025779817" zvalue="51"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454893830146" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192454893830146"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1207.95,963.979) scale(1.54536,1.54536) translate(-418.107,-332.009)" width="30" x="1184.769660218963" y="940.7988025779817"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="47">
   <use class="kv10" height="30" transform="rotate(0,1474,929.333) scale(0.904762,0.911111) translate(153.825,89.3333)" width="28" x="1461.333333333333" xlink:href="#EnergyConsumer:站用变DY接地_0" y="915.6666768391931" zvalue="70"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454893764610" ObjectName="1号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1474,929.333) scale(0.904762,0.911111) translate(153.825,89.3333)" width="28" x="1461.333333333333" y="915.6666768391931"/></g>
  <g id="118">
   <use class="kv10" height="30" transform="rotate(0,711.022,595.399) scale(0.904762,-0.911111) translate(73.5111,-1250.22)" width="28" x="698.3555212265758" xlink:href="#EnergyConsumer:站用变DY接地_0" y="581.7326024373372" zvalue="138"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454895403010" ObjectName="施工变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,711.022,595.399) scale(0.904762,-0.911111) translate(73.5111,-1250.22)" width="28" x="698.3555212265758" y="581.7326024373372"/></g>
  <g id="13">
   <use class="kv0" height="30" transform="rotate(0,711.25,573) scale(0.78125,0.770833) translate(197.838,166.914)" width="12" x="706.5625" xlink:href="#EnergyConsumer:负荷_0" y="561.4375" zvalue="341"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV高挖线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,711.25,573) scale(0.78125,0.770833) translate(197.838,166.914)" width="12" x="706.5625" y="561.4375"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="64">
   <use class="kv10" height="30" transform="rotate(270,894.788,849.376) scale(1.19048,0.952381) translate(-142.023,41.7545)" width="12" x="887.6450216450216" xlink:href="#GroundDisconnector:地刀12_0" y="835.0903441354465" zvalue="76"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454894026754" ObjectName="#1发电机08167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454894026754"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,894.788,849.376) scale(1.19048,0.952381) translate(-142.023,41.7545)" width="12" x="887.6450216450216" y="835.0903441354465"/></g>
  <g id="66">
   <use class="kv10" height="30" transform="rotate(270,1262.65,852.064) scale(1.19048,0.952381) translate(-200.881,41.8889)" width="12" x="1255.506493506493" xlink:href="#GroundDisconnector:地刀12_0" y="837.7777926890881" zvalue="78"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454894157826" ObjectName="#2发电机08267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454894157826"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1262.65,852.064) scale(1.19048,0.952381) translate(-200.881,41.8889)" width="12" x="1255.506493506493" y="837.7777926890881"/></g>
  <g id="86">
   <use class="kv10" height="30" transform="rotate(270,1530.9,856.064) scale(1.19048,0.952381) translate(-243.801,42.0889)" width="12" x="1523.758241758242" xlink:href="#GroundDisconnector:地刀12_0" y="841.7777927341042" zvalue="110"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454894682114" ObjectName="#1站用变83167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454894682114"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1530.9,856.064) scale(1.19048,0.952381) translate(-243.801,42.0889)" width="12" x="1523.758241758242" y="841.7777927341042"/></g>
  <g id="116">
   <use class="kv10" height="30" transform="rotate(270,764.658,650.747) scale(1.19048,0.952381) translate(-121.202,31.8231)" width="12" x="757.514651378869" xlink:href="#GroundDisconnector:地刀12_0" y="636.4615391322545" zvalue="141"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454895337474" ObjectName="施工变08567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454895337474"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,764.658,650.747) scale(1.19048,0.952381) translate(-121.202,31.8231)" width="12" x="757.514651378869" y="636.4615391322545"/></g>
  <g id="125">
   <use class="kv10" height="30" transform="rotate(270,1057.24,650.747) scale(1.19048,0.952381) translate(-168.015,31.8231)" width="12" x="1050.096348500309" xlink:href="#GroundDisconnector:地刀12_0" y="636.4615393470931" zvalue="150"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454895534082" ObjectName="#1主变10kV侧00167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454895534082"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1057.24,650.747) scale(1.19048,0.952381) translate(-168.015,31.8231)" width="12" x="1050.096348500309" y="636.4615393470931"/></g>
  <g id="97">
   <use class="kv110" height="30" transform="rotate(270,1067.62,482.476) scale(1.19048,0.952381) translate(-169.676,23.4095)" width="12" x="1060.476190476191" xlink:href="#GroundDisconnector:地刀12_0" y="468.1904761904761" zvalue="163"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454895730690" ObjectName="#1主变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454895730690"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1067.62,482.476) scale(1.19048,0.952381) translate(-169.676,23.4095)" width="12" x="1060.476190476191" y="468.1904761904761"/></g>
  <g id="138">
   <use class="kv110" height="30" transform="rotate(270,772.063,222.921) scale(1.19048,0.952381) translate(-122.387,10.4317)" width="12" x="764.9206349206349" xlink:href="#GroundDisconnector:地刀12_0" y="208.6349206349205" zvalue="174"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454895992834" ObjectName="110kV挖苦河三级电站线18260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454895992834"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,772.063,222.921) scale(1.19048,0.952381) translate(-122.387,10.4317)" width="12" x="764.9206349206349" y="208.6349206349205"/></g>
  <g id="139">
   <use class="kv110" height="30" transform="rotate(270,772.063,154.921) scale(1.19048,0.952381) translate(-122.387,7.03175)" width="12" x="764.9206349206349" xlink:href="#GroundDisconnector:地刀12_0" y="140.6349206349205" zvalue="176"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454896123906" ObjectName="110kV挖苦河三级电站线18267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454896123906"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,772.063,154.921) scale(1.19048,0.952381) translate(-122.387,7.03175)" width="12" x="764.9206349206349" y="140.6349206349205"/></g>
  <g id="172">
   <use class="kv110" height="30" transform="rotate(270,1433.54,153.849) scale(1.19048,0.952381) translate(-228.224,6.97817)" width="12" x="1426.398708346263" xlink:href="#GroundDisconnector:地刀12_0" y="139.5634871891565" zvalue="195"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454896517122" ObjectName="110kV高河一级电站线18167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454896517122"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1433.54,153.849) scale(1.19048,0.952381) translate(-228.224,6.97817)" width="12" x="1426.398708346263" y="139.5634871891565"/></g>
  <g id="182">
   <use class="kv110" height="30" transform="rotate(270,886.952,385.143) scale(1.19048,0.952381) translate(-140.77,18.5429)" width="12" x="879.8095238095239" xlink:href="#GroundDisconnector:地刀12_0" y="370.8571428571427" zvalue="214"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454896910338" ObjectName="110kV母线电压互感器19010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454896910338"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,886.952,385.143) scale(1.19048,0.952381) translate(-140.77,18.5429)" width="12" x="879.8095238095239" y="370.8571428571427"/></g>
  <g id="185">
   <use class="kv110" height="30" transform="rotate(270,886.286,458.476) scale(1.19048,0.952381) translate(-140.663,22.2095)" width="12" x="879.1428571428571" xlink:href="#GroundDisconnector:地刀12_0" y="444.1904761904761" zvalue="217"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454897041410" ObjectName="110kV母线电压互感器19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454897041410"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,886.286,458.476) scale(1.19048,0.952381) translate(-140.663,22.2095)" width="12" x="879.1428571428571" y="444.1904761904761"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="50">
   <use class="kv10" height="18" transform="rotate(0,916.25,995.889) scale(1.43333,1.43333) translate(-273.756,-297.183)" width="15" x="905.5" xlink:href="#Accessory:PT8_0" y="982.9888854026794" zvalue="86"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454894223362" ObjectName="#1发电机机组PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,916.25,995.889) scale(1.43333,1.43333) translate(-273.756,-297.183)" width="15" x="905.5" y="982.9888854026794"/></g>
  <g id="53">
   <use class="kv10" height="20" transform="rotate(0,742.444,995.889) scale(1,1) translate(0,0)" width="15" x="734.9444444444446" xlink:href="#Accessory:PT6_0" y="985.8888854980469" zvalue="88"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454894288898" ObjectName="#1发电机励磁变"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,742.444,995.889) scale(1,1) translate(0,0)" width="15" x="734.9444444444446" y="985.8888854980469"/></g>
  <g id="55">
   <use class="kv10" height="20" transform="rotate(0,774.5,995.889) scale(1,1) translate(0,0)" width="15" x="767" xlink:href="#Accessory:PT6_0" y="985.8888854980469" zvalue="91"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454894354434" ObjectName="#1发电机励磁PT"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,774.5,995.889) scale(1,1) translate(0,0)" width="15" x="767" y="985.8888854980469"/></g>
  <g id="76">
   <use class="kv10" height="20" transform="rotate(0,1109.36,995.889) scale(1,1) translate(0,0)" width="15" x="1101.863636363636" xlink:href="#Accessory:PT6_0" y="985.8888854452368" zvalue="96"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454894485506" ObjectName="#2发电机励磁变"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1109.36,995.889) scale(1,1) translate(0,0)" width="15" x="1101.863636363636" y="985.8888854452368"/></g>
  <g id="74">
   <use class="kv10" height="20" transform="rotate(0,1145.86,995.889) scale(1,1) translate(0,0)" width="15" x="1138.363636363636" xlink:href="#Accessory:PT6_0" y="985.8888854452368" zvalue="98"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454894419970" ObjectName="#2发电机励磁PT"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1145.86,995.889) scale(1,1) translate(0,0)" width="15" x="1138.363636363636" y="985.8888854452368"/></g>
  <g id="81">
   <use class="kv10" height="18" transform="rotate(0,1287.64,995.889) scale(1.43333,1.43333) translate(-386.035,-297.183)" width="15" x="1276.885273237733" xlink:href="#Accessory:PT8_0" y="982.9888855882876" zvalue="104"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454894551042" ObjectName="#2发电机机组PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1287.64,995.889) scale(1.43333,1.43333) translate(-386.035,-297.183)" width="15" x="1276.885273237733" y="982.9888855882876"/></g>
  <g id="153">
   <use class="kv110" height="40" transform="rotate(270,678.125,154.307) scale(0.555556,-0.555556) translate(533.611,-440.948)" width="40" x="667.013888888889" xlink:href="#Accessory:线路PT11带避雷器_0" y="143.1958671453717" zvalue="184"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454896254978" ObjectName="110kV挖苦河三级电站线线路PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(270,678.125,154.307) scale(0.555556,-0.555556) translate(533.611,-440.948)" width="40" x="667.013888888889" y="143.1958671453717"/></g>
  <g id="164">
   <use class="kv110" height="40" transform="rotate(270,1338.98,153.861) scale(0.555556,-0.555556) translate(1062.29,-439.698)" width="40" x="1327.866962314517" xlink:href="#Accessory:线路PT11带避雷器_0" y="142.7494336996076" zvalue="205"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454896320514" ObjectName="110kV高河一级电站线线路PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(270,1338.98,153.861) scale(0.555556,-0.555556) translate(1062.29,-439.698)" width="40" x="1327.866962314517" y="142.7494336996076"/></g>
  <g id="178">
   <use class="kv110" height="40" transform="rotate(0,826.014,500.222) scale(1,1) translate(1.78971e-13,-1.06631e-12)" width="40" x="806.0135810545991" xlink:href="#Accessory:线路PT11带避雷器_0" y="480.2222222222223" zvalue="210"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454896779266" ObjectName="110kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,826.014,500.222) scale(1,1) translate(1.78971e-13,-1.06631e-12)" width="40" x="806.0135810545991" y="480.2222222222223"/></g>
  <g id="189">
   <use class="kv10" height="18" transform="rotate(0,1398.43,603.399) scale(2.53086,-2.53086) translate(-834.396,-828.038)" width="15" x="1379.444444444444" xlink:href="#Accessory:PT8_0" y="580.6214916441176" zvalue="221"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454897106946" ObjectName="10kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1398.43,603.399) scale(2.53086,-2.53086) translate(-834.396,-828.038)" width="15" x="1379.444444444444" y="580.6214916441176"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="127">
   <g id="1270">
    <use class="kv110" height="50" transform="rotate(0,1006.45,590.167) scale(1.36667,-1.36667) translate(-264.522,-1012.83)" width="30" x="985.95" xlink:href="#PowerTransformer2:D-Y_0" y="556" zvalue="152"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874597269506" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1271">
    <use class="kv10" height="50" transform="rotate(0,1006.45,590.167) scale(1.36667,-1.36667) translate(-264.522,-1012.83)" width="30" x="985.95" xlink:href="#PowerTransformer2:D-Y_1" y="556" zvalue="152"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874469212163" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399535624194" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399535624194"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1006.45,590.167) scale(1.36667,-1.36667) translate(-264.522,-1012.83)" width="30" x="985.95" y="556"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="60">
   <use class="kv110" height="30" transform="rotate(0,1005.5,428.222) scale(-1.11111,-0.814815) translate(-1909.62,-956.545)" width="15" x="997.1679004268823" xlink:href="#Disconnector:刀闸_0" y="416" zvalue="160"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454895599618" ObjectName="#1主变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454895599618"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1005.5,428.222) scale(-1.11111,-0.814815) translate(-1909.62,-956.545)" width="15" x="997.1679004268823" y="416"/></g>
  <g id="135">
   <use class="kv110" height="30" transform="rotate(0,721.667,300.889) scale(-1.11111,-0.814815) translate(-1370.33,-672.939)" width="15" x="713.3333333333334" xlink:href="#Disconnector:刀闸_0" y="288.6666666666667" zvalue="168"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454895796226" ObjectName="110kV挖苦河三级电站线1821隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454895796226"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,721.667,300.889) scale(-1.11111,-0.814815) translate(-1370.33,-672.939)" width="15" x="713.3333333333334" y="288.6666666666667"/></g>
  <g id="137">
   <use class="kv110" height="30" transform="rotate(0,721.574,194.667) scale(-1.11111,-0.814815) translate(-1370.16,-436.354)" width="15" x="713.2406806963943" xlink:href="#Disconnector:刀闸_0" y="182.4444444444443" zvalue="172"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454895861762" ObjectName="110kV挖苦河三级电站线1826隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454895861762"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,721.574,194.667) scale(-1.11111,-0.814815) translate(-1370.16,-436.354)" width="15" x="713.2406806963943" y="182.4444444444443"/></g>
  <g id="176">
   <use class="kv110" height="30" transform="rotate(0,1383.14,299.817) scale(-1.11111,-0.814815) translate(-2627.14,-670.553)" width="15" x="1374.811406758961" xlink:href="#Disconnector:刀闸_0" y="287.5952332209027" zvalue="187"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454896648194" ObjectName="110kV高河一级电站线1811隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454896648194"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1383.14,299.817) scale(-1.11111,-0.814815) translate(-2627.14,-670.553)" width="15" x="1374.811406758961" y="287.5952332209027"/></g>
  <g id="174">
   <use class="kv110" height="30" transform="rotate(0,1383.05,193.595) scale(-1.11111,-0.814815) translate(-2626.97,-433.967)" width="15" x="1374.718754122022" xlink:href="#Disconnector:刀闸_0" y="181.3730109986804" zvalue="191"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454896582658" ObjectName="110kV高河一级电站线1816隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454896582658"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1383.05,193.595) scale(-1.11111,-0.814815) translate(-2626.97,-433.967)" width="15" x="1374.718754122022" y="181.3730109986804"/></g>
  <g id="177">
   <use class="kv110" height="30" transform="rotate(0,826.111,414.222) scale(-1.11111,-0.814815) translate(-1568.78,-925.364)" width="15" x="817.7777777777777" xlink:href="#Disconnector:刀闸_0" y="402" zvalue="208"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454896713730" ObjectName="110kV母线电压互感器1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454896713730"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,826.111,414.222) scale(-1.11111,-0.814815) translate(-1568.78,-925.364)" width="15" x="817.7777777777777" y="402"/></g>
  <g id="197">
   <use class="kv10" height="36" transform="rotate(0,1473.33,816.297) scale(1.11111,1.11111) translate(-146.556,-79.6297)" width="14" x="1465.555555555555" xlink:href="#Disconnector:联体小车刀闸2_0" y="796.2970886230469" zvalue="226"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454897172482" ObjectName="#1站用变0831接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454897172482"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1473.33,816.297) scale(1.11111,1.11111) translate(-146.556,-79.6297)" width="14" x="1465.555555555555" y="796.2970886230469"/></g>
  <g id="209">
   <use class="kv10" height="36" transform="rotate(0,1110,928.667) scale(1.11111,1.11111) translate(-110.222,-90.8667)" width="14" x="1102.222222222222" xlink:href="#Disconnector:联体小车刀闸2_0" y="908.6666563881768" zvalue="238"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454897303554" ObjectName="#2发电机0921接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454897303554"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1110,928.667) scale(1.11111,1.11111) translate(-110.222,-90.8667)" width="14" x="1102.222222222222" y="908.6666563881768"/></g>
  <g id="215">
   <use class="kv10" height="36" transform="rotate(0,1286.27,929.381) scale(1.11111,1.11111) translate(-127.849,-90.9381)" width="14" x="1278.492063492064" xlink:href="#Disconnector:联体小车刀闸2_0" y="909.380942195181" zvalue="244"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454897369090" ObjectName="#2发电机0922接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454897369090"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1286.27,929.381) scale(1.11111,1.11111) translate(-127.849,-90.9381)" width="14" x="1278.492063492064" y="909.380942195181"/></g>
  <g id="221">
   <use class="kv10" height="36" transform="rotate(0,743.333,928.667) scale(1.11111,1.11111) translate(-73.5556,-90.8667)" width="14" x="735.5555555555555" xlink:href="#Disconnector:联体小车刀闸2_0" y="908.6666564941403" zvalue="250"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454897434626" ObjectName="#1发电机0911接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454897434626"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,743.333,928.667) scale(1.11111,1.11111) translate(-73.5556,-90.8667)" width="14" x="735.5555555555555" y="908.6666564941403"/></g>
  <g id="227">
   <use class="kv10" height="36" transform="rotate(0,915.556,928.667) scale(1.11111,1.11111) translate(-90.7778,-90.8667)" width="14" x="907.7777777777777" xlink:href="#Disconnector:联体小车刀闸2_0" y="908.6666565471226" zvalue="256"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454897500162" ObjectName="#1发电机0912接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454897500162"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,915.556,928.667) scale(1.11111,1.11111) translate(-90.7778,-90.8667)" width="14" x="907.7777777777777" y="908.6666565471226"/></g>
  <g id="237">
   <use class="kv10" height="36" transform="rotate(0,1396.67,682.444) scale(1.11111,1.11111) translate(-138.889,-66.2444)" width="14" x="1388.888888888889" xlink:href="#Disconnector:联体小车刀闸2_0" y="662.4444444444443" zvalue="266"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454897565698" ObjectName="10kV母线电压互感器0901接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454897565698"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1396.67,682.444) scale(1.11111,1.11111) translate(-138.889,-66.2444)" width="14" x="1388.888888888889" y="662.4444444444443"/></g>
 </g>
 <g id="GroundClass">
  <g id="241">
   <use class="kv110" height="18" transform="rotate(0,1055.56,602.222) scale(1.11111,1.11111) translate(-104.889,-59.2222)" width="12" x="1048.888888888889" xlink:href="#Ground:大地_0" y="592.2222222222222" zvalue="269"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454897631234" ObjectName="#1主变110kV侧中性点接地"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1055.56,602.222) scale(1.11111,1.11111) translate(-104.889,-59.2222)" width="12" x="1048.888888888889" y="592.2222222222222"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="71">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="71" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,152.611,372.825) scale(1,1) translate(0,0)" writing-mode="lr" x="152.77" xml:space="preserve" y="377.74" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136738729985" ObjectName="F"/>
   </metadata>
  </g>
  <g id="279">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="279" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,152.611,300.825) scale(1,1) translate(0,0)" writing-mode="lr" x="152.77" xml:space="preserve" y="305.74" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127231881220" ObjectName="高河一级上网有功"/>
   </metadata>
  </g>
  <g id="70">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="70" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,330.222,301.825) scale(1,1) translate(0,0)" writing-mode="lr" x="330.38" xml:space="preserve" y="306.74" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136745611265" ObjectName="GEN_QSUM"/>
   </metadata>
  </g>
  <g id="69">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="69" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,330.222,373.825) scale(1,1) translate(0,0)" writing-mode="lr" x="330.38" xml:space="preserve" y="378.74" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136732307457" ObjectName="F"/>
   </metadata>
  </g>
  <g id="256">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="256" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,152.611,325.825) scale(1,1) translate(0,0)" writing-mode="lr" x="152.77" xml:space="preserve" y="330.74" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136745414657" ObjectName="F"/>
   </metadata>
  </g>
  <g id="58">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="58" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,330.222,326.825) scale(1,1) translate(0,0)" writing-mode="lr" x="330.38" xml:space="preserve" y="331.74" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136745480193" ObjectName="F"/>
   </metadata>
  </g>
  <g id="264">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="264" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,152.611,418.048) scale(1,1) translate(0,0)" writing-mode="lr" x="152.77" xml:space="preserve" y="422.96" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136745938945" ObjectName="坝前水位实测值"/>
   </metadata>
  </g>
  <g id="247">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="247" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,152.611,349.825) scale(1,1) translate(0,0)" writing-mode="lr" x="152.77" xml:space="preserve" y="354.74" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127182204935" ObjectName="装机利用率"/>
   </metadata>
  </g>
  <g id="45">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,330.222,348.825) scale(1,1) translate(0,0)" writing-mode="lr" x="330.38" xml:space="preserve" y="353.74" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127182139400" ObjectName="厂用电率"/>
   </metadata>
  </g>
  <g id="44">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="44" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,328.611,418.048) scale(1,1) translate(0,0)" writing-mode="lr" x="328.77" xml:space="preserve" y="422.96" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136746397697" ObjectName="高河一级坝上雨量实测值"/>
   </metadata>
  </g>
  <g id="1">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="12" id="1" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,594.506,47.8333) scale(1,1) translate(0,0)" writing-mode="lr" x="594.04" xml:space="preserve" y="52.11" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136739385345" ObjectName="P"/>
   </metadata>
  </g>
  <g id="2">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="12" id="2" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,594.506,71.8333) scale(1,1) translate(0,0)" writing-mode="lr" x="594.04" xml:space="preserve" y="76.11" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136739450881" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="3">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="3" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,594.506,98.8333) scale(1,1) translate(0,0)" writing-mode="lr" x="594.04" xml:space="preserve" y="103.11" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136739516417" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="5">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="5" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1256.98,33.7619) scale(1,1) translate(0,0)" writing-mode="lr" x="1256.52" xml:space="preserve" y="38.54" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136740302849" ObjectName="P"/>
   </metadata>
  </g>
  <g id="9">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="9" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1256.98,56.7619) scale(1,1) translate(0,0)" writing-mode="lr" x="1256.52" xml:space="preserve" y="61.54" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136740368385" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="10">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="10" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1256.98,79.7619) scale(1,1) translate(0,0)" writing-mode="lr" x="1256.52" xml:space="preserve" y="84.54000000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136740433921" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="12" id="19" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1177.45,465.5) scale(1,1) translate(-2.51454e-13,0)" writing-mode="lr" x="1176.98" xml:space="preserve" y="469.78" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="26">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="12" id="26" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1177.45,491.5) scale(1,1) translate(-2.51454e-13,0)" writing-mode="lr" x="1176.98" xml:space="preserve" y="495.78" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="27">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="12" id="27" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1177.45,648.833) scale(1,1) translate(-2.51454e-13,1.40184e-13)" writing-mode="lr" x="1176.98" xml:space="preserve" y="653.11" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136735911937" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="29">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="12" id="29" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1178.45,674.833) scale(1,1) translate(-2.51676e-13,0)" writing-mode="lr" x="1177.98" xml:space="preserve" y="679.11" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136735977473" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="78">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="78" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1180.45,519.5) scale(1,1) translate(-2.5212e-13,0)" writing-mode="lr" x="1179.98" xml:space="preserve" y="523.78" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="80">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="80" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1180.45,703.833) scale(1,1) translate(-2.5212e-13,0)" writing-mode="lr" x="1179.98" xml:space="preserve" y="708.11" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136736370689" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="93">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="93" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,969.138,966.66) scale(1,1) translate(0,0)" writing-mode="lr" x="968.59" xml:space="preserve" y="970.9400000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136730734593" ObjectName="P"/>
   </metadata>
  </g>
  <g id="95">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="95" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,969.138,996.16) scale(1,1) translate(0,-1.52114e-12)" writing-mode="lr" x="968.59" xml:space="preserve" y="1000.44" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136730800129" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="98">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="98" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,970.388,1024.41) scale(1,1) translate(0,0)" writing-mode="lr" x="969.84" xml:space="preserve" y="1028.69" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136730865665" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="157">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="157" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1354.2,960.41) scale(1,1) translate(0,0)" writing-mode="lr" x="1353.65" xml:space="preserve" y="964.6900000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136729554945" ObjectName="P"/>
   </metadata>
  </g>
  <g id="167">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="167" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1352.95,988.66) scale(1,1) translate(0,0)" writing-mode="lr" x="1352.4" xml:space="preserve" y="992.9400000000001" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136729620481" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="173">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="173" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1354.2,1016.91) scale(1,1) translate(0,0)" writing-mode="lr" x="1353.65" xml:space="preserve" y="1021.19" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136729686017" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="4">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="4" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,578.254,705.427) scale(1,1) translate(0,0)" writing-mode="lr" x="577.79" xml:space="preserve" y="710.21" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136732176385" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="188">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="188" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,593.889,332.556) scale(1,1) translate(0,0)" writing-mode="lr" x="593.42" xml:space="preserve" y="337.33" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136738598913" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="190">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="190" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1413.25,489.927) scale(1,1) translate(0,0)" writing-mode="lr" x="1412.79" xml:space="preserve" y="494.71" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136731914241" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="191">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="191" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,828.889,529.056) scale(1,1) translate(0,0)" writing-mode="lr" x="828.42" xml:space="preserve" y="533.83" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136738336769" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="192">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="192" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1413.25,518.927) scale(1,1) translate(0,0)" writing-mode="lr" x="1412.79" xml:space="preserve" y="523.71" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136731979777" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="194">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="194" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,828.889,558.056) scale(1,1) translate(0,0)" writing-mode="lr" x="828.42" xml:space="preserve" y="562.83" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136738402305" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="196">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="196" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1413.25,544.927) scale(1,1) translate(0,0)" writing-mode="lr" x="1412.79" xml:space="preserve" y="549.71" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136732045313" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="200">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="200" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,828.889,584.056) scale(1,1) translate(0,-1.25801e-13)" writing-mode="lr" x="828.42" xml:space="preserve" y="588.83" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136738467841" ObjectName="Uc"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="85">
   <use height="30" transform="rotate(0,323.673,187.016) scale(0.708333,0.665547) translate(128.902,88.9631)" width="30" x="313.05" xlink:href="#State:红绿圆(方形)_0" y="177.03" zvalue="286"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374930083841" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,323.673,187.016) scale(0.708333,0.665547) translate(128.902,88.9631)" width="30" x="313.05" y="177.03"/></g>
  <g id="260">
   <use height="30" transform="rotate(0,231.048,188.016) scale(0.708333,0.665547) translate(90.7623,89.4657)" width="30" x="220.42" xlink:href="#State:红绿圆(方形)_0" y="178.03" zvalue="287"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562962215993345" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,231.048,188.016) scale(0.708333,0.665547) translate(90.7623,89.4657)" width="30" x="220.42" y="178.03"/></g>
 </g>
</svg>