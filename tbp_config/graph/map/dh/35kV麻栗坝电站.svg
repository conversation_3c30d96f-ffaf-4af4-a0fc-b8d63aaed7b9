<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549584723970" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="Accessory:带熔断器四卷PT_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12" y2="1"/>
   <ellipse cx="15.25" cy="16.75" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.08" cy="24.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="11.25" cy="20.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="18.82" cy="20.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="11" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="11" y1="23.25" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.25" x2="15.25" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.25" x2="15.25" y1="19" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.25" x2="15.25" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17" x2="15" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="26.75" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="15" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17" x2="20.94444444444444" y1="21.46612466124661" y2="21.46612466124661"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17" x2="18.31481481481482" y1="21.46612466124661" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.94444444444444" x2="19.62962962962963" y1="21.46612466124661" y2="19"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15.01,6.21) scale(1,1) translate(0,0)" width="4.92" x="12.55" y="2.5"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
  <symbol id="Accessory:PT232_0" viewBox="0,0,25,35">
   <use terminal-index="0" type="0" x="12.5" xlink:href="#terminal" y="0.2666666666666657"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.5" x2="12.5" y1="15.16666666666667" y2="0.5"/>
   <rect fill-opacity="0" height="10" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,12.5,7.5) scale(1,1) translate(0,0)" width="6" x="9.5" y="2.5"/>
   <ellipse cx="12.75" cy="20" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.75" cy="24.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.67" cy="29.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.82" cy="24.27" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.583333333333336" x2="5.583333333333336" y1="26.75" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333335" x2="5.583333333333334" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="5.5" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.75" x2="12.75" y1="22.25" y2="19.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.75" x2="12.75" y1="17.63888888888889" y2="19.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.75" x2="12.75" y1="17.63888888888889" y2="19.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.58333333333333" x2="12.58333333333333" y1="32" y2="29.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.58333333333333" x2="12.58333333333333" y1="27.38888888888889" y2="29.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.58333333333333" x2="12.58333333333333" y1="27.38888888888889" y2="29.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="19.31481481481482" y1="24.96612466124661" y2="22.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.94444444444444" x2="20.62962962962963" y1="24.96612466124661" y2="22.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="21.94444444444444" y1="24.96612466124661" y2="24.96612466124661"/>
  </symbol>
  <symbol id="Accessory:避雷器PT带熔断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="18" xlink:href="#terminal" y="1.066666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.24166666666667" x2="10.86666666666667" y1="1" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.26666666666667" x2="25.26666666666667" y1="6.583333333333332" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="12" y2="1"/>
   <ellipse cx="10.62" cy="16.75" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.62" cy="20.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.78" cy="24.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.05" cy="20.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.86666666666667" x2="14.86666666666667" y1="23.25" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.61666666666667" x2="10.61666666666667" y1="19" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.616666666666671" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.61666666666667" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.866666666666671" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="26.75" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067752" x2="5.636382113821139" y1="22.23028455284553" y2="20.91546973803071"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.10250677506775" x2="5.63638211382114" y1="18.28584010840109" y2="19.60065492321591"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067754" x2="8.102506775067756" y1="18.28584010840108" y2="22.23028455284553"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,10.86,6.21) scale(-1,1) translate(-1746.33,0)" width="4.92" x="8.4" y="2.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.53333333333333" x2="27.36666666666667" y1="18.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="6.583333333333337" y2="12.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="14.83333333333334" y2="18.43333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="26.95" y1="12.5" y2="9.166666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.23333333333333" x2="23.65000000000001" y1="12.53333333333333" y2="9.283333333333333"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,25.36,10.96) scale(-1,1) translate(-2326.33,0)" width="4.92" x="22.9" y="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.93333333333334" x2="27.01666666666667" y1="19.75" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.28333333333333" x2="26.41666666666666" y1="21" y2="21"/>
  </symbol>
  <symbol id="EnergyConsumer:回贤变站用变_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.8499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="17.5" y1="27" y2="21.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="9" y1="24" y2="25.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="25.75" y2="25.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.16666666666667" x2="11.25" y1="24" y2="25.83333333333333"/>
   <rect fill-opacity="0" height="4.32" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.06,5.25) scale(1,1) translate(0,0)" width="3.43" x="8.34" y="3.09"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.0235180220435" x2="10.0235180220435" y1="0.5833333333333091" y2="9.290410445610181"/>
   <ellipse cx="9.880000000000001" cy="13.47" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.94" cy="19.46" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="18.1368007916835" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.6" x2="15.39982126899018" y1="25.06619780557639" y2="25.06619780557639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75996425379804" x2="16.23985701519214" y1="25.90207720835499" y2="25.90207720835499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.91992850759607" x2="17.07989276139411" y1="26.73795661113357" y2="26.73795661113357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="17.5" y1="19.80855959724066" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.49991063449509" x2="17.49991063449509" y1="19.83333333333333" y2="24.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.10311503267975" x2="10.10311503267975" y1="29.16666666666667" y2="23.64357429718876"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="10.8868007916835" y2="12.55855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="14.23031840279781" y2="12.55855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="14.23031840279781" y2="12.55855959724065"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV麻栗坝电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="256" x="48" xlink:href="logo.png" y="43"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,176,73) scale(1,1) translate(0,0)" writing-mode="lr" x="176" xml:space="preserve" y="76.5" zvalue="54"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,176.5,72.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="176.5" xml:space="preserve" y="81.69" zvalue="55">35kV麻栗坝电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="202" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,82.625,185.25) scale(1,1) translate(0,0)" width="72.88" x="46.19" y="173.25" zvalue="1618"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,82.625,185.25) scale(1,1) translate(0,0)" writing-mode="lr" x="82.63" xml:space="preserve" y="189.75" zvalue="1618">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,868.704,612.698) scale(1,1) translate(0,0)" writing-mode="lr" x="868.7" xml:space="preserve" y="617.2" zvalue="16">6.3kV母线</text>
  <line fill="none" id="67" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="382" x2="382" y1="11" y2="1041" zvalue="58"/>
  <line fill="none" id="666" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.000000000000455" x2="375" y1="146.8704926140824" y2="146.8704926140824" zvalue="1056"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="932" y2="932"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="971.1632999999999" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="368" y1="932" y2="932"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="368" y1="971.1632999999999" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188" x2="188" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="188.0000000000001" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.0000000000001" x2="278.0000000000001" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="278" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188" x2="188" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="188.0000000000001" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.0000000000001" x2="278.0000000000001" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="278" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="999.0816" y2="1027"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="609" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,952) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="958" zvalue="1062">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="608" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,50,986) scale(1,1) translate(0,0)" writing-mode="lr" x="50" xml:space="preserve" y="992" zvalue="1063">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="607" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232,986) scale(1,1) translate(0,0)" writing-mode="lr" x="232" xml:space="preserve" y="992" zvalue="1064">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="606" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49,1014) scale(1,1) translate(0,0)" writing-mode="lr" x="49" xml:space="preserve" y="1020" zvalue="1065">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="605" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,231,1014) scale(1,1) translate(0,0)" writing-mode="lr" x="231" xml:space="preserve" y="1020" zvalue="1066">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="602" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,73.5,646.5) scale(1,1) translate(0,0)" writing-mode="lr" x="73.5" xml:space="preserve" y="651" zvalue="1069">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="851" stroke="rgb(255,255,255)" text-anchor="middle" x="882.140625" xml:space="preserve" y="882.890625" zvalue="1261">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="851" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="882.140625" xml:space="preserve" y="898.890625" zvalue="1261">1500kW</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="247" y2="247"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="273" y2="273"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="247" y2="273"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="247" y2="273"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="247" y2="247"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="273" y2="273"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="247" y2="273"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="247" y2="273"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="273" y2="273"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="297.25" y2="297.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="273" y2="297.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="273" y2="297.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="273" y2="273"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="297.25" y2="297.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="273" y2="297.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="273" y2="297.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="297.25" y2="297.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="320" y2="320"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="297.25" y2="320"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="297.25" y2="320"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="297.25" y2="297.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="320" y2="320"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="297.25" y2="320"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="297.25" y2="320"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="320" y2="320"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="342.75" y2="342.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="320" y2="342.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="320" y2="342.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="320" y2="320"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="342.75" y2="342.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="320" y2="342.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="320" y2="342.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="342.75" y2="342.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="365.5" y2="365.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="342.75" y2="365.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="342.75" y2="365.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="342.75" y2="342.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="365.5" y2="365.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="342.75" y2="365.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="342.75" y2="365.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="365.5" y2="365.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="388.25" y2="388.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="365.5" y2="388.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="365.5" y2="388.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="365.5" y2="365.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="388.25" y2="388.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="365.5" y2="388.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="365.5" y2="388.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="388.25" y2="388.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="411" y2="411"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="388.25" y2="411"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="388.25" y2="411"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="388.25" y2="388.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="411" y2="411"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="388.25" y2="411"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="388.25" y2="411"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="212" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,198.399,185.841) scale(1,1) translate(0,0)" writing-mode="lr" x="198.4" xml:space="preserve" y="190.34" zvalue="1606">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="211" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,303.399,185.841) scale(1,1) translate(0,0)" writing-mode="lr" x="303.4" xml:space="preserve" y="190.34" zvalue="1607">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="210" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,55.5,260) scale(1,1) translate(0,0)" writing-mode="lr" x="13" xml:space="preserve" y="264.5" zvalue="1608">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="209" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,236,260) scale(1,1) translate(0,0)" writing-mode="lr" x="193.5" xml:space="preserve" y="264.5" zvalue="1609">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="208" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,58.6875,333.25) scale(1,1) translate(0,0)" writing-mode="lr" x="58.69" xml:space="preserve" y="337.75" zvalue="1610">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="207" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.875,355) scale(1,1) translate(0,0)" writing-mode="lr" x="59.88" xml:space="preserve" y="359.5" zvalue="1611">0.6kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="201" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,54.5,286) scale(1,1) translate(0,0)" writing-mode="lr" x="12" xml:space="preserve" y="290.5" zvalue="1619">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="200" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,235,286) scale(1,1) translate(0,0)" writing-mode="lr" x="192.5" xml:space="preserve" y="290.5" zvalue="1620">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="184" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.6875,379.25) scale(1,1) translate(0,0)" writing-mode="lr" x="55.69" xml:space="preserve" y="383.75" zvalue="1623">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="167" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.6875,402.25) scale(1,1) translate(0,0)" writing-mode="lr" x="55.69" xml:space="preserve" y="406.75" zvalue="1627">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="159" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,54.5,309) scale(1,1) translate(0,0)" writing-mode="lr" x="12" xml:space="preserve" y="313.5" zvalue="1629">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="109" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,234.5,308) scale(1,1) translate(0,0)" writing-mode="lr" x="192" xml:space="preserve" y="312.5" zvalue="1631">厂用电率</text>
  <line fill="none" id="214" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.000000000000455" x2="375" y1="492.8704926140824" y2="492.8704926140824" zvalue="1636"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,244.125,950) scale(1,1) translate(0,0)" writing-mode="lr" x="244.13" xml:space="preserve" y="956" zvalue="1653">MaLiBa-01-2023</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="148" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1176,493.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1176" xml:space="preserve" y="498.07" zvalue="1655">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="170" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1141.04,378.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1141.04" xml:space="preserve" y="383.07" zvalue="1656">351</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="189" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1130.93,300.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1130.93" xml:space="preserve" y="305.07" zvalue="1657">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="193" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1117,171.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1117" xml:space="preserve" y="176.07" zvalue="1658">35kV麻城线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="232" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1169,282.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1169" xml:space="preserve" y="287.07" zvalue="1663">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="236" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1066.5,265.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1066.5" xml:space="preserve" y="270.07" zvalue="1666">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="238" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1021.25,304.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1021.25" xml:space="preserve" y="309.07" zvalue="1668">97</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="244" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,903.04,753.643) scale(1,1) translate(0,0)" writing-mode="lr" x="903.04" xml:space="preserve" y="758.14" zvalue="1674">651</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="247" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,889.929,692.143) scale(1,1) translate(0,0)" writing-mode="lr" x="889.9299999999999" xml:space="preserve" y="696.64" zvalue="1676">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="271" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,820.929,799.143) scale(1,1) translate(0,0)" writing-mode="lr" x="820.9299999999999" xml:space="preserve" y="803.64" zvalue="1683">6911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="273" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,972.429,798.143) scale(1,1) translate(0,0)" writing-mode="lr" x="972.4299999999999" xml:space="preserve" y="802.64" zvalue="1685">6912</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="283" stroke="rgb(255,255,255)" text-anchor="middle" x="1324.703125" xml:space="preserve" y="885.421875" zvalue="1692">#2发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="283" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1324.703125" xml:space="preserve" y="901.421875" zvalue="1692">1500kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="282" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1346.81,756.183) scale(1,1) translate(0,0)" writing-mode="lr" x="1346.81" xml:space="preserve" y="760.6799999999999" zvalue="1694">652</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="281" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1332.5,694.683) scale(1,1) translate(0,0)" writing-mode="lr" x="1332.5" xml:space="preserve" y="699.1799999999999" zvalue="1696">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="280" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1265,801.683) scale(1,1) translate(0,0)" writing-mode="lr" x="1265" xml:space="preserve" y="806.1799999999999" zvalue="1702">6921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="279" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1415.75,800.683) scale(1,1) translate(0,0)" writing-mode="lr" x="1415.75" xml:space="preserve" y="805.1799999999999" zvalue="1705">6922</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="303" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1119.43,700) scale(1,1) translate(0,0)" writing-mode="lr" x="1119.43" xml:space="preserve" y="704.5" zvalue="1713">6811</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="358" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1107.75,872.321) scale(1,1) translate(0,0)" writing-mode="lr" x="1107.75" xml:space="preserve" y="876.8200000000001" zvalue="1762">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1288,428.071) scale(1,1) translate(0,0)" writing-mode="lr" x="1288" xml:space="preserve" y="432.57" zvalue="1786">6.3kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1268,548.683) scale(1,1) translate(1.94289e-12,0)" writing-mode="lr" x="1268" xml:space="preserve" y="553.1799999999999" zvalue="1788">6901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1182,515.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1182" xml:space="preserve" y="520.0700000000001" zvalue="1794">4000kVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="6" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,146.714,1013.14) scale(1,1) translate(0,-2.21791e-13)" writing-mode="lr" x="126" xml:space="preserve" y="1019.14" zvalue="1797">段勇</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="7" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,322.429,1014.57) scale(1,1) translate(0,-2.22108e-13)" writing-mode="lr" x="280.29" xml:space="preserve" y="1020.57" zvalue="1799">20230926</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="46.19" y="173.25" zvalue="1618"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="15">
   <path class="v6300" d="M 798.86 628.03 L 1401.57 628.03" stroke-width="4" zvalue="15"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674240561156" ObjectName="6.3kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674240561156"/></metadata>
  <path d="M 798.86 628.03 L 1401.57 628.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="v6300" height="30" transform="rotate(0,881.14,839.143) scale(1.5,1.5) translate(-286.213,-272.214)" width="30" x="858.6397358526772" xlink:href="#Generator:发电机_0" y="816.6428571428571" zvalue="1260"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449753382918" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449753382918"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,881.14,839.143) scale(1.5,1.5) translate(-286.213,-272.214)" width="30" x="858.6397358526772" y="816.6428571428571"/></g>
  <g id="299">
   <use class="v6300" height="30" transform="rotate(0,1323.71,841.683) scale(1.5,1.5) translate(-433.737,-273.061)" width="30" x="1301.211164424106" xlink:href="#Generator:发电机_0" y="819.1825430733817" zvalue="1691"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449754628102" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449754628102"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1323.71,841.683) scale(1.5,1.5) translate(-433.737,-273.061)" width="30" x="1301.211164424106" y="819.1825430733817"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="205">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="205" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,155.611,260.167) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="265.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124948672517" ObjectName="F"/>
   </metadata>
  </g>
  <g id="221">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="221" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,333.222,261.167) scale(1,1) translate(0,0)" writing-mode="lr" x="333.38" xml:space="preserve" y="266.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124948738053" ObjectName="F"/>
   </metadata>
  </g>
  <g id="204">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="204" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,156.222,356.167) scale(1,1) translate(0,0)" writing-mode="lr" x="156.38" xml:space="preserve" y="361.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124923899909" ObjectName="F"/>
   </metadata>
  </g>
  <g id="199">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="199" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,155.611,285.167) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="290.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124948541445" ObjectName="F"/>
   </metadata>
  </g>
  <g id="190">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="190" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,333.222,286.167) scale(1,1) translate(0,0)" writing-mode="lr" x="333.38" xml:space="preserve" y="291.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124948606981" ObjectName="F"/>
   </metadata>
  </g>
  <g id="120">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="120" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,155.611,309.167) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="314.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127187316741" ObjectName="装机利用率"/>
   </metadata>
  </g>
  <g id="104">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="104" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,335.611,308.167) scale(1,1) translate(0,0)" writing-mode="lr" x="335.77" xml:space="preserve" y="313.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127187251205" ObjectName="厂用电率"/>
   </metadata>
  </g>
  <g id="57">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="57" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,155.611,333.5) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="338.41" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="14">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="14" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1112,95.3214) scale(1,1) translate(0,0)" writing-mode="lr" x="1111.59" xml:space="preserve" y="99.98999999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124927766533" ObjectName="P"/>
   </metadata>
  </g>
  <g id="17">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="17" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1112,119.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1111.59" xml:space="preserve" y="124.24" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124927832069" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="18">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="18" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1112,138.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1111.59" xml:space="preserve" y="143.24" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124927897605" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="19" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,872.784,921.143) scale(1,1) translate(0,0)" writing-mode="lr" x="872.29" xml:space="preserve" y="927.36" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124924030984" ObjectName="P"/>
   </metadata>
  </g>
  <g id="20">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="20" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1323.71,922.571) scale(1,1) translate(-8.50224e-13,0)" writing-mode="lr" x="1323.22" xml:space="preserve" y="928.79" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124929732613" ObjectName="P"/>
   </metadata>
  </g>
  <g id="21">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="21" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,872.784,941.268) scale(1,1) translate(0,0)" writing-mode="lr" x="872.29" xml:space="preserve" y="947.49" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124924096519" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="22">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="22" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1323.71,942.696) scale(1,1) translate(-8.50224e-13,0)" writing-mode="lr" x="1323.22" xml:space="preserve" y="948.91" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124929798149" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="23">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="23" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,872.784,961.393) scale(1,1) translate(0,0)" writing-mode="lr" x="872.29" xml:space="preserve" y="967.61" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124924162055" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="24">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="24" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1323.71,962.821) scale(1,1) translate(-8.50224e-13,0)" writing-mode="lr" x="1323.22" xml:space="preserve" y="969.04" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124929863685" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="203">
   <use height="30" transform="rotate(0,330.673,186.357) scale(0.708333,0.665547) translate(131.784,88.6322)" width="30" x="320.05" xlink:href="#State:红绿圆(方形)_0" y="176.37" zvalue="1616"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374889451523" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,330.673,186.357) scale(0.708333,0.665547) translate(131.784,88.6322)" width="30" x="320.05" y="176.37"/></g>
  <g id="215">
   <use height="30" transform="rotate(0,235.048,186.357) scale(0.708333,0.665547) translate(92.4093,88.6322)" width="30" x="224.42" xlink:href="#State:红绿圆(方形)_0" y="176.37" zvalue="1617"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562951080509445" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,235.048,186.357) scale(0.708333,0.665547) translate(92.4093,88.6322)" width="30" x="224.42" y="176.37"/></g>
  <g id="830">
   <use height="30" transform="rotate(0,322.812,124.464) scale(1.27778,1.03333) translate(-57.6766,-3.51496)" width="90" x="265.31" xlink:href="#State:全站检修_0" y="108.96" zvalue="1796"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549584723970" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,322.812,124.464) scale(1.27778,1.03333) translate(-57.6766,-3.51496)" width="90" x="265.31" y="108.96"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="62">
   <g id="620">
    <use class="kv35" height="60" transform="rotate(0,1114.83,491.071) scale(1.44155,1.48333) translate(-332.644,-145.512)" width="40" x="1086" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="446.57" zvalue="1654"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874431332356" ObjectName="35"/>
    </metadata>
   </g>
   <g id="621">
    <use class="v6300" height="60" transform="rotate(0,1114.83,491.071) scale(1.44155,1.48333) translate(-332.644,-145.512)" width="40" x="1086" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="446.57" zvalue="1654"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874431397892" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399447937028" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399447937028"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1114.83,491.071) scale(1.44155,1.48333) translate(-332.644,-145.512)" width="40" x="1086" y="446.57"/></g>
 </g>
 <g id="BreakerClass">
  <g id="166">
   <use class="kv35" height="20" transform="rotate(0,1114.79,377.571) scale(1.5,1.35) translate(-369.096,-94.3889)" width="10" x="1107.287936013301" xlink:href="#Breaker:开关_0" y="364.0714285714286" zvalue="1655"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924500520965" ObjectName="35kV麻城线351断路器"/>
   <cge:TPSR_Ref TObjectID="6473924500520965"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1114.79,377.571) scale(1.5,1.35) translate(-369.096,-94.3889)" width="10" x="1107.287936013301" y="364.0714285714286"/></g>
  <g id="243">
   <use class="v6300" height="20" transform="rotate(0,881.04,750.643) scale(1.5,1.35) translate(-291.18,-191.111)" width="10" x="873.5397358526773" xlink:href="#Breaker:开关_0" y="737.1428571428571" zvalue="1673"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924500586501" ObjectName="#1发电机651断路器"/>
   <cge:TPSR_Ref TObjectID="6473924500586501"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,881.04,750.643) scale(1.5,1.35) translate(-291.18,-191.111)" width="10" x="873.5397358526773" y="737.1428571428571"/></g>
  <g id="298">
   <use class="v6300" height="20" transform="rotate(0,1323.61,753.183) scale(1.5,1.35) translate(-438.704,-191.77)" width="10" x="1316.111164424106" xlink:href="#Breaker:开关_0" y="739.6825430733817" zvalue="1693"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924500652037" ObjectName="#2发电机652断路器"/>
   <cge:TPSR_Ref TObjectID="6473924500652037"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1323.61,753.183) scale(1.5,1.35) translate(-438.704,-191.77)" width="10" x="1316.111164424106" y="739.6825430733817"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="175">
   <use class="kv35" height="30" transform="rotate(0,1114.68,301.571) scale(1,0.733333) translate(0,105.662)" width="15" x="1107.176771589195" xlink:href="#Disconnector:刀闸_0" y="290.5714285714286" zvalue="1656"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449753448454" ObjectName="35kV麻城线3516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449753448454"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1114.68,301.571) scale(1,0.733333) translate(0,105.662)" width="15" x="1107.176771589195" y="290.5714285714286"/></g>
  <g id="234">
   <use class="kv35" height="30" transform="rotate(0,1054.5,266.571) scale(1,0.733333) translate(0,92.9351)" width="15" x="1047" xlink:href="#Disconnector:刀闸_0" y="255.5714285714286" zvalue="1665"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449753710597" ObjectName="35kV麻城线3519隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449753710597"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1054.5,266.571) scale(1,0.733333) translate(0,92.9351)" width="15" x="1047" y="255.5714285714286"/></g>
  <g id="245">
   <use class="v6300" height="30" transform="rotate(0,880.929,693.143) scale(1,0.733333) translate(0,248.052)" width="15" x="873.4285714285714" xlink:href="#Disconnector:刀闸_0" y="682.1428571428571" zvalue="1675"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449753972742" ObjectName="#1发电机6511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449753972742"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,880.929,693.143) scale(1,0.733333) translate(0,248.052)" width="15" x="873.4285714285714" y="682.1428571428571"/></g>
  <g id="270">
   <use class="v6300" height="30" transform="rotate(0,802.929,797.143) scale(1,0.733333) translate(0,285.87)" width="15" x="795.4285714285713" xlink:href="#Disconnector:刀闸_0" y="786.1428571428571" zvalue="1682"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449754103814" ObjectName="#1发电机6911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449754103814"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,802.929,797.143) scale(1,0.733333) translate(0,285.87)" width="15" x="795.4285714285713" y="786.1428571428571"/></g>
  <g id="272">
   <use class="v6300" height="30" transform="rotate(0,949.929,797.143) scale(1,0.733333) translate(0,285.87)" width="15" x="942.4285714285714" xlink:href="#Disconnector:刀闸_0" y="786.1428571428571" zvalue="1684"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449754169350" ObjectName="#1发电机6912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449754169350"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,949.929,797.143) scale(1,0.733333) translate(0,285.87)" width="15" x="942.4285714285714" y="786.1428571428571"/></g>
  <g id="297">
   <use class="v6300" height="30" transform="rotate(0,1323.5,695.683) scale(1,0.733333) translate(0,248.975)" width="15" x="1316" xlink:href="#Disconnector:刀闸_0" y="684.6825430733817" zvalue="1695"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449754562566" ObjectName="#2发电机6521隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449754562566"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1323.5,695.683) scale(1,0.733333) translate(0,248.975)" width="15" x="1316" y="684.6825430733817"/></g>
  <g id="290">
   <use class="v6300" height="30" transform="rotate(0,1245.5,799.683) scale(1,0.733333) translate(0,286.794)" width="15" x="1238" xlink:href="#Disconnector:刀闸_0" y="788.6825430733817" zvalue="1701"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449754431494" ObjectName="#2发电机6921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449754431494"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1245.5,799.683) scale(1,0.733333) translate(0,286.794)" width="15" x="1238" y="788.6825430733817"/></g>
  <g id="289">
   <use class="v6300" height="30" transform="rotate(0,1392.5,799.683) scale(1,0.733333) translate(0,286.794)" width="15" x="1385" xlink:href="#Disconnector:刀闸_0" y="788.6825430733817" zvalue="1703"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449754365958" ObjectName="#2发电机6922隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449754365958"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1392.5,799.683) scale(1,0.733333) translate(0,286.794)" width="15" x="1385" y="788.6825430733817"/></g>
  <g id="302">
   <use class="v6300" height="30" transform="rotate(0,1096.93,699) scale(1,0.733333) translate(0,250.182)" width="15" x="1089.428571428571" xlink:href="#Disconnector:刀闸_0" y="688" zvalue="1712"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449754693638" ObjectName="#1站用变6811隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449754693638"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1096.93,699) scale(1,0.733333) translate(0,250.182)" width="15" x="1089.428571428571" y="688"/></g>
  <g id="3">
   <use class="v6300" height="30" transform="rotate(0,1295.5,549.683) scale(1,0.733333) translate(0,195.885)" width="15" x="1288" xlink:href="#Disconnector:刀闸_0" y="538.6825430733817" zvalue="1787"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450020835333" ObjectName="6.3kV母线电压互感器6901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450020835333"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1295.5,549.683) scale(1,0.733333) translate(0,195.885)" width="15" x="1288" y="538.6825430733817"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="195">
   <path class="v6300" d="M 1114.83 534.94 L 1114.83 628.03" stroke-width="1" zvalue="1658"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="62@1" LinkObjectIDznd="15@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1114.83 534.94 L 1114.83 628.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="206">
   <path class="kv35" d="M 1114.89 447.38 L 1114.89 390.46" stroke-width="1" zvalue="1659"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="62@0" LinkObjectIDznd="166@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1114.89 447.38 L 1114.89 390.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="226">
   <path class="kv35" d="M 1114.74 364.66 L 1114.74 312.38" stroke-width="1" zvalue="1660"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="166@0" LinkObjectIDznd="175@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1114.74 364.66 L 1114.74 312.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="227">
   <path class="kv35" d="M 1116 234.89 L 1116 290.94" stroke-width="1" zvalue="1661"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@0" LinkObjectIDznd="175@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1116 234.89 L 1116 290.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="233">
   <path class="kv35" d="M 1158.25 267.52 L 1116 267.52" stroke-width="1" zvalue="1663"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="231@0" LinkObjectIDznd="227" MaxPinNum="2"/>
   </metadata>
  <path d="M 1158.25 267.52 L 1116 267.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="240">
   <path class="kv35" d="M 1054.59 255.94 L 1054.59 246.57 L 1116 246.57" stroke-width="1" zvalue="1669"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="234@0" LinkObjectIDznd="227" MaxPinNum="2"/>
   </metadata>
  <path d="M 1054.59 255.94 L 1054.59 246.57 L 1116 246.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="241">
   <path class="kv35" d="M 1054.56 277.38 L 1054.56 304.85" stroke-width="1" zvalue="1670"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="234@1" LinkObjectIDznd="239@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1054.56 277.38 L 1054.56 304.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="242">
   <path class="kv35" d="M 1030.75 290.52 L 1054.56 290.52" stroke-width="1" zvalue="1671"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="237@0" LinkObjectIDznd="241" MaxPinNum="2"/>
   </metadata>
  <path d="M 1030.75 290.52 L 1054.56 290.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="248">
   <path class="v6300" d="M 881.02 682.51 L 881.02 628.03" stroke-width="1" zvalue="1676"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="245@0" LinkObjectIDznd="15@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 881.02 682.51 L 881.02 628.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="253">
   <path class="v6300" d="M 880.99 703.95 L 880.99 737.73" stroke-width="1" zvalue="1677"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="245@1" LinkObjectIDznd="243@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 880.99 703.95 L 880.99 737.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="254">
   <path class="v6300" d="M 881.14 817.02 L 881.14 763.54" stroke-width="1" zvalue="1678"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="850@0" LinkObjectIDznd="243@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 881.14 817.02 L 881.14 763.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="275">
   <path class="v6300" d="M 803.02 786.51 L 803.02 775.14 L 881.14 775.14" stroke-width="1" zvalue="1686"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="270@0" LinkObjectIDznd="254" MaxPinNum="2"/>
   </metadata>
  <path d="M 803.02 786.51 L 803.02 775.14 L 881.14 775.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="276">
   <path class="v6300" d="M 802.99 807.95 L 802.99 817.21 L 802.93 817.21 L 802.93 826.46" stroke-width="1" zvalue="1687"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="270@1" LinkObjectIDznd="255@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 802.99 807.95 L 802.99 817.21 L 802.93 817.21 L 802.93 826.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="277">
   <path class="v6300" d="M 881.14 775.14 L 950.02 775.14 L 950.02 786.51" stroke-width="1" zvalue="1688"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="275" LinkObjectIDznd="272@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 881.14 775.14 L 950.02 775.14 L 950.02 786.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="278">
   <path class="v6300" d="M 949.99 807.95 L 949.99 830.14" stroke-width="1" zvalue="1689"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@1" LinkObjectIDznd="274@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 949.99 807.95 L 949.99 830.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="294">
   <path class="v6300" d="M 1323.59 685.05 L 1323.59 628.03" stroke-width="1" zvalue="1697"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="297@0" LinkObjectIDznd="15@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1323.59 685.05 L 1323.59 628.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="293">
   <path class="v6300" d="M 1323.56 706.49 L 1323.56 740.27" stroke-width="1" zvalue="1698"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="297@1" LinkObjectIDznd="298@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1323.56 706.49 L 1323.56 740.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="292">
   <path class="v6300" d="M 1323.71 819.56 L 1323.71 766.08" stroke-width="1" zvalue="1699"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="299@0" LinkObjectIDznd="298@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1323.71 819.56 L 1323.71 766.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="287">
   <path class="v6300" d="M 1245.59 789.05 L 1245.59 777.68 L 1323.71 777.68" stroke-width="1" zvalue="1706"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="290@0" LinkObjectIDznd="292" MaxPinNum="2"/>
   </metadata>
  <path d="M 1245.59 789.05 L 1245.59 777.68 L 1323.71 777.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="286">
   <path class="v6300" d="M 1245.56 810.49 L 1245.51 829" stroke-width="1" zvalue="1707"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="290@1" LinkObjectIDznd="291@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1245.56 810.49 L 1245.51 829" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="285">
   <path class="v6300" d="M 1323.71 777.68 L 1392.59 777.68 L 1392.59 789.05" stroke-width="1" zvalue="1708"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="287" LinkObjectIDznd="289@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1323.71 777.68 L 1392.59 777.68 L 1392.59 789.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="284">
   <path class="v6300" d="M 1392.56 810.49 L 1392.56 832.68" stroke-width="1" zvalue="1709"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="289@1" LinkObjectIDznd="288@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1392.56 810.49 L 1392.56 832.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="304">
   <path class="v6300" d="M 1097.02 688.36 L 1097.02 628.03" stroke-width="1" zvalue="1713"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="302@0" LinkObjectIDznd="15@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1097.02 688.36 L 1097.02 628.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="305">
   <path class="v6300" d="M 1096.99 709.81 L 1096.99 747.63" stroke-width="1" zvalue="1714"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="302@1" LinkObjectIDznd="356@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1096.99 709.81 L 1096.99 747.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="5">
   <path class="v6300" d="M 1295.59 539.05 L 1295.59 501.29" stroke-width="1" zvalue="1788"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="3@0" LinkObjectIDznd="1@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1295.59 539.05 L 1295.59 501.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="8">
   <path class="v6300" d="M 1295.56 560.49 L 1295.56 628.03" stroke-width="1" zvalue="1789"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="3@1" LinkObjectIDznd="15@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1295.56 560.49 L 1295.56 628.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="231">
   <use class="kv35" height="20" transform="rotate(270,1168,267.571) scale(1,1) translate(0,0)" width="10" x="1163" xlink:href="#GroundDisconnector:地刀_0" y="257.5714285714286" zvalue="1662"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449753645061" ObjectName="35kV麻城线35167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449753645061"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1168,267.571) scale(1,1) translate(0,0)" width="10" x="1163" y="257.5714285714286"/></g>
  <g id="237">
   <use class="kv35" height="20" transform="rotate(90,1021,290.571) scale(-1,1) translate(-2042,0)" width="10" x="1016" xlink:href="#GroundDisconnector:地刀_0" y="280.5714285714286" zvalue="1667"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449753841670" ObjectName="35kV麻城线35197接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449753841670"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1021,290.571) scale(-1,1) translate(-2042,0)" width="10" x="1016" y="280.5714285714286"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="239">
   <use class="kv35" height="35" transform="rotate(0,1054.56,323.071) scale(1.23333,1.05714) translate(-196.595,-16.4633)" width="25" x="1039.144497757439" xlink:href="#Accessory:PT232_0" y="304.5714285714286" zvalue="1668"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449753907206" ObjectName="35kV麻城线PT"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1054.56,323.071) scale(1.23333,1.05714) translate(-196.595,-16.4633)" width="25" x="1039.144497757439" y="304.5714285714286"/></g>
  <g id="255">
   <use class="v6300" height="18" transform="rotate(0,804.845,843.643) scale(2.05556,2.05556) translate(-405.382,-423.722)" width="15" x="789.4285714285716" xlink:href="#Accessory:PT8_0" y="825.1428571428571" zvalue="1680"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449754038278" ObjectName="#1发电机PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,804.845,843.643) scale(2.05556,2.05556) translate(-405.382,-423.722)" width="15" x="789.4285714285716" y="825.1428571428571"/></g>
  <g id="274">
   <use class="v6300" height="30" transform="rotate(0,949.99,844.143) scale(1,1) translate(0,0)" width="30" x="934.9897358526772" xlink:href="#Accessory:带熔断器四卷PT_0" y="829.1428571428571" zvalue="1685"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449754234886" ObjectName="#1发电机PT1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,949.99,844.143) scale(1,1) translate(0,0)" width="30" x="934.9897358526772" y="829.1428571428571"/></g>
  <g id="291">
   <use class="v6300" height="18" transform="rotate(0,1247.42,846.183) scale(2.05556,2.05556) translate(-632.649,-425.026)" width="15" x="1232" xlink:href="#Accessory:PT8_0" y="827.6825430733817" zvalue="1700"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449754497030" ObjectName="#2发电机PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1247.42,846.183) scale(2.05556,2.05556) translate(-632.649,-425.026)" width="15" x="1232" y="827.6825430733817"/></g>
  <g id="288">
   <use class="v6300" height="30" transform="rotate(0,1392.56,846.683) scale(1,1) translate(0,0)" width="30" x="1377.561164424106" xlink:href="#Accessory:带熔断器四卷PT_0" y="831.6825430733817" zvalue="1704"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449754300422" ObjectName="#2发电机PT1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1392.56,846.683) scale(1,1) translate(0,0)" width="30" x="1377.561164424106" y="831.6825430733817"/></g>
  <g id="1">
   <use class="v6300" height="30" transform="rotate(0,1293.25,479.271) scale(1.31667,-1.58) translate(-306.285,-773.908)" width="30" x="1273.5" xlink:href="#Accessory:避雷器PT带熔断器_0" y="455.5714285714286" zvalue="1785"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450020769797" ObjectName="6.3kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1293.25,479.271) scale(1.31667,-1.58) translate(-306.285,-773.908)" width="30" x="1273.5" y="455.5714285714286"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="356">
   <use class="v6300" height="30" transform="rotate(0,1096.99,799.731) scale(3.65973,3.68203) translate(-770.647,-542.302)" width="20" x="1060.392394885042" xlink:href="#EnergyConsumer:回贤变站用变_0" y="744.5000000000001" zvalue="1761"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449755152390" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1096.99,799.731) scale(3.65973,3.68203) translate(-770.647,-542.302)" width="20" x="1060.392394885042" y="744.5000000000001"/></g>
 </g>
</svg>