<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549681061889" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:母联小车开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.9499999999999993"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.998992968246015" x2="4.998992968246015" y1="14.41666666666666" y2="17.66666666666666"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:母联小车开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.9499999999999993"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="1.08333333333333" y2="5.749999999999999"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:母联小车开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.9499999999999993"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="7" y1="6" y2="14"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="7" y1="14" y2="6"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_0" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="9.750000000000004" y2="4.166666666666668"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="32"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="1" y1="27" y2="11"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_1" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="27" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_2" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="1.499999999999999" y1="26" y2="9.966666666666669"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.999999999999998" x2="12.41666666666667" y1="26.25" y2="10.05"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Accessory:PT象达_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15.5" xlink:href="#terminal" y="7.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.25" x2="18.25" y1="12.08333333333333" y2="12.08333333333333"/>
   <ellipse cx="15.65" cy="12.68" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.9" cy="18.28" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.25" x2="18.25" y1="18.33333333333333" y2="18.33333333333333"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:带熔断器35kVPT11_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="1.1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="26.25" y2="23.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.75" x2="11.75" y1="21.63888888888889" y2="23.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.75" x2="18.06481481481482" y1="24.96612466124661" y2="22.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.75" x2="20.69444444444444" y1="24.96612466124661" y2="24.96612466124661"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.75" x2="11.75" y1="21.63888888888889" y2="23.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.69444444444444" x2="19.37962962962963" y1="24.96612466124661" y2="22.5"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15.01,5.71) scale(1,1) translate(0,0)" width="4.92" x="12.55" y="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="13" y2="1"/>
   <ellipse cx="15.03" cy="18.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="11.67" cy="23.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="18.5" cy="23.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="20.5" y2="17.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="15" y1="15.88888888888889" y2="17.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17" x2="15" y1="15.88888888888889" y2="17.88888888888889"/>
  </symbol>
  <symbol id="Accessory:传输线_0" viewBox="0,0,12,22">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="1"/>
   <path d="M 1.16667 1 L 10.8333 1 L 6 7.63889 L 1.16667 1 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="14.27777777777778" y2="7.638888888888888"/>
   <path d="M 1.16667 20.9167 L 10.8333 20.9167 L 6 14.2778 L 1.16667 20.9167 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸带避雷器_0" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="11.75" xlink:href="#terminal" y="7.25"/>
   <use terminal-index="1" type="0" x="11.75" xlink:href="#terminal" y="41.75"/>
   <rect fill-opacity="0" height="8" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,25,7.5) scale(1,1) translate(0,0)" width="4" x="23" y="3.5"/>
   <path d="M 24 8.5 L 26 8.5 L 25 5.5 L 24 8.5 L 24 8.5 z" fill="rgb(0,255,127)" fill-opacity="1" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="2.5" y2="3.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24" x2="26" y1="1.75" y2="1.75"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23" x2="27" y1="2.5" y2="2.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="16.5" y2="8.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.5" x2="25.5" y1="0.75" y2="0.75"/>
   <rect height="12.17" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,11.75,25.42) scale(1,1) translate(0,0)" width="6" x="8.75" y="19.33"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="24.975" y1="16.475" y2="16.475"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="16.33333333333333" y2="31.58333333333333"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="6.343004115226339" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="6.343004115226339" y1="11.43694745991201" y2="16.24729228749822"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="17.259670781893" y1="11.43694745991201" y2="16.24729228749822"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="17.259670781893" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="6.293004115226339" y1="41.49729228749822" y2="36.68694745991201"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="6.293004115226339" y1="37.49082677025685" y2="32.68048194267064"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="17.209670781893" y1="37.49082677025685" y2="32.68048194267064"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="17.209670781893" y1="41.49729228749822" y2="36.68694745991201"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸带避雷器_1" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="11.75" xlink:href="#terminal" y="7.25"/>
   <use terminal-index="1" type="0" x="11.75" xlink:href="#terminal" y="41.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="2.5" y2="3.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24" x2="26" y1="1.75" y2="1.75"/>
   <rect fill-opacity="0" height="8" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,25,7.5) scale(1,1) translate(0,0)" width="4" x="23" y="3.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23" x2="27" y1="2.5" y2="2.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.5" x2="25.5" y1="0.75" y2="0.75"/>
   <path d="M 24 8.5 L 26 8.5 L 25 5.5 L 24 8.5 L 24 8.5 z" fill="rgb(255,0,0)" fill-opacity="1" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="16.5" y2="8.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="24.975" y1="16.475" y2="16.475"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="7.333333333333334" y2="41.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="6.343004115226339" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="17.259670781893" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="6.293004115226339" y1="41.49729228749822" y2="36.68694745991201"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="17.209670781893" y1="41.49729228749822" y2="36.68694745991201"/>
   <rect height="12.17" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,11.75,25.42) scale(1,1) translate(0,0)" width="6" x="8.75" y="19.33"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸带避雷器_2" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="11.75" xlink:href="#terminal" y="7.25"/>
   <use terminal-index="1" type="0" x="11.75" xlink:href="#terminal" y="41.75"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.75" x2="14.75" y1="17" y2="32"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.65" x2="8.65" y1="17.1" y2="32.1"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="16.5" y2="8.5"/>
   <rect fill-opacity="0" height="8" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,25,7.5) scale(1,1) translate(0,0)" width="4" x="23" y="3.5"/>
   <path d="M 24 8.5 L 26 8.5 L 25 5.5 L 24 8.5 L 24 8.5 z" fill="rgb(0,255,127)" fill-opacity="1" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="2.5" y2="3.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23" x2="27" y1="2.5" y2="2.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24" x2="26" y1="1.75" y2="1.75"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.5" x2="25.5" y1="0.75" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="24.975" y1="16.475" y2="16.475"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="32.35" y2="41.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.71666666666667" x2="11.71666666666667" y1="17.05" y2="7.466666666666663"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="6.343004115226339" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="6.343004115226339" y1="11.43694745991201" y2="16.24729228749822"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="17.259670781893" y1="11.43694745991201" y2="16.24729228749822"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="17.259670781893" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="6.293004115226339" y1="41.49729228749822" y2="36.68694745991201"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="6.293004115226339" y1="37.49082677025685" y2="32.68048194267064"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="17.209670781893" y1="37.49082677025685" y2="32.68048194267064"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="17.209670781893" y1="41.49729228749822" y2="36.68694745991201"/>
  </symbol>
  <symbol id="Accessory:PT1_0" viewBox="0,0,18,18">
   <use terminal-index="0" type="0" x="9.15" xlink:href="#terminal" y="1.15"/>
   <ellipse cx="9.23" cy="6.18" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="6.12" cy="11.68" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="11.9" cy="11.78" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT带保险_0" viewBox="0,0,11,29">
   <use terminal-index="0" type="0" x="5.5" xlink:href="#terminal" y="0.1666666666666661"/>
   <rect fill-opacity="0" height="6" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.5,7.5) scale(1,1) translate(0,0)" width="4" x="3.5" y="4.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="13.16666666666667" y2="0.5"/>
   <ellipse cx="5.4" cy="18.1" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.4" cy="23.78" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Compensator:西郊变电容_0" viewBox="0,0,24,40">
   <use terminal-index="0" type="0" x="12" xlink:href="#terminal" y="2.449999999999999"/>
   <rect fill-opacity="0" height="5.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,12,23) scale(1,1) translate(0,0)" width="3" x="10.5" y="20.17"/>
   <path d="M 12 17.85 L 17.85 17.85 L 17.85 22.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="6.5" y1="33.10833333333333" y2="33.10833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.471296296296289" x2="6.471296296296289" y1="30.88611111111111" y2="33.12685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.50833333333333" x2="6.50833333333333" y1="20.10833333333333" y2="17.90462962962963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="29.60833333333333" y2="33.12685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="22.60833333333333" y2="28.60833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="17.79351851851851" y2="22.525"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.94090594744122" x2="6.94090594744122" y1="36.94166666666666" y2="34.94166666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.416666666666666" x2="12.00833333333334" y1="17.85833333333333" y2="17.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="33.10833333333333" y2="37.10833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.09166666666667" x2="17.09166666666667" y1="34.94166666666666" y2="36.94166666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.758333333333324" x2="2.758333333333324" y1="19.85833333333333" y2="31.60833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.08333333333333" x2="7.000000000000002" y1="37.10833333333333" y2="37.10833333333333"/>
   <path d="M 6.50833 23.7072 A 2.96392 1.81747 180 0 1 6.50833 20.0723" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 6.50833 27.3421 A 2.96392 1.81747 180 0 1 6.50833 23.7072" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 6.50833 30.8771 A 2.96392 1.81747 180 0 1 6.50833 27.2421" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.50833333333333" x2="10.50833333333333" y1="29.60833333333333" y2="29.60833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.50833333333333" x2="10.50833333333333" y1="28.60833333333333" y2="28.60833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12" x2="12" y1="2.25" y2="4.916666666666668"/>
   <path d="M 7.26667 9.85 A 4.91667 4.75 -450 1 0 12.0167 4.93333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 7.33333 9.83333 L 12 9.91667 L 12 18" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="20.56666666666668" y2="21.08015580397416"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.28450340888154" x2="18.47116270499357" y1="26.7156109584975" y2="26.7156109584975"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510778" x2="17.14262023217246" y1="23.75922956383932" y2="22.95550743587977"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.50700202690252" x2="18.32283029297954" y1="27.0857461490052" y2="27.0857461490052"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="25.36667381975841" y2="26.33114037330986"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.98783858485353" x2="18.76782752902158" y1="26.34547576798984" y2="26.34547576798984"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="21.08015580397418" y2="23.73243882624067"/>
   <rect fill-opacity="0" height="4.29" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,17.83,23.22) scale(1,1) translate(0,0)" width="2.33" x="16.67" y="21.08"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510781" x2="18.53565505804314" y1="23.75922956383932" y2="22.95550743587977"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变无融断_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.8499999999999996"/>
   <path d="M 10 9 L 10 0" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="17.5" y1="27" y2="21.65"/>
   <ellipse cx="9.880000000000001" cy="13.47" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.94" cy="19.46" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="18.1368007916835" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.6" x2="15.39982126899018" y1="25.06619780557639" y2="25.06619780557639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75996425379804" x2="16.23985701519214" y1="25.90207720835499" y2="25.90207720835499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.91992850759607" x2="17.07989276139411" y1="26.73795661113357" y2="26.73795661113357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="17.5" y1="19.80855959724066" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.49991063449509" x2="17.49991063449509" y1="19.83333333333333" y2="24.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.10311503267975" x2="10.10311503267975" y1="26.83333333333333" y2="23.64357429718876"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="10.8868007916835" y2="12.55855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="14.23031840279781" y2="12.55855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="14.23031840279781" y2="12.55855959724065"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV畹町变" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="82.56999999999999" id="1" preserveAspectRatio="xMidYMid slice" width="249.69" x="85.88" xlink:href="logo.png" y="34.28"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="300" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,210.725,75.5707) scale(1,1) translate(-1.90688e-14,0)" writing-mode="lr" x="210.72" xml:space="preserve" y="79.06999999999999" zvalue="1279"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,239.571,75.5475) scale(1,1) translate(0,0)" writing-mode="lr" x="239.57" xml:space="preserve" y="84.55" zvalue="1280">35kV畹町变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="171" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,332.438,405.25) scale(1,1) translate(0,-2.61957e-13)" width="72.88" x="296" y="393.25" zvalue="1422"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,332.438,405.25) scale(1,1) translate(0,-2.61957e-13)" writing-mode="lr" x="332.44" xml:space="preserve" y="409.75" zvalue="1422">小电流接地</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="167" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,128.625,324.25) scale(1,1) translate(0,0)" width="72.88" x="92.19" y="312.25" zvalue="1423"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,128.625,324.25) scale(1,1) translate(0,0)" writing-mode="lr" x="128.63" xml:space="preserve" y="328.75" zvalue="1423">全站公用</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="170" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,230.531,405.25) scale(1,1) translate(0,0)" width="72.88" x="194.09" y="393.25" zvalue="1424"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,230.531,405.25) scale(1,1) translate(0,0)" writing-mode="lr" x="230.53" xml:space="preserve" y="409.75" zvalue="1424">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="169" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,128.625,405.25) scale(1,1) translate(0,0)" width="72.88" x="92.19" y="393.25" zvalue="1425"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,128.625,405.25) scale(1,1) translate(0,0)" writing-mode="lr" x="128.63" xml:space="preserve" y="409.75" zvalue="1425">AVC功能</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="168" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,128.625,364.75) scale(1,1) translate(0,0)" width="72.88" x="92.19" y="352.75" zvalue="1426"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,128.625,364.75) scale(1,1) translate(0,0)" writing-mode="lr" x="128.63" xml:space="preserve" y="369.25" zvalue="1426">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="115" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,563.344,343.361) scale(1,1) translate(0,0)" writing-mode="lr" x="563.34" xml:space="preserve" y="347.86" zvalue="19">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="109" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,745.125,290.722) scale(1,1) translate(0,0)" writing-mode="lr" x="745.13" xml:space="preserve" y="295.22" zvalue="34">3901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="108" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1075.64,669.917) scale(1,1) translate(0,0)" writing-mode="lr" x="1075.64" xml:space="preserve" y="674.42" zvalue="39">10kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="107" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1822.49,672.306) scale(1,1) translate(0,0)" writing-mode="lr" x="1822.49" xml:space="preserve" y="676.8099999999999" zvalue="40">10kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="311" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1238.32,575.681) scale(1,1) translate(0,-1.23426e-13)" writing-mode="lr" x="1238.31746031746" xml:space="preserve" y="580.1805555555555" zvalue="43">10kV分段 012</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="103" stroke="rgb(255,255,255)" text-anchor="middle" x="747.3671875" xml:space="preserve" y="499.1961811913385" zvalue="81">#1主变   </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="103" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="747.3671875" xml:space="preserve" y="515.1961811913385" zvalue="81">5000KVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1147.6,655.933) scale(1,1) translate(0,0)" writing-mode="lr" x="1147.6" xml:space="preserve" y="660.4299999999999" zvalue="99">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,784.475,447.667) scale(1,1) translate(0,0)" writing-mode="lr" x="784.47" xml:space="preserve" y="452.17" zvalue="131">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,792.391,389.694) scale(1,1) translate(0,0)" writing-mode="lr" x="792.39" xml:space="preserve" y="394.19" zvalue="134">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" x="1371.765625" xml:space="preserve" y="499.4635425143772" zvalue="147">#2主变     </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1371.765625" xml:space="preserve" y="515.4635425143772" zvalue="147">5000kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1407.13,447.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1407.13" xml:space="preserve" y="452.17" zvalue="150">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1415.72,387.694) scale(1,1) translate(0,0)" writing-mode="lr" x="1415.72" xml:space="preserve" y="392.19" zvalue="153">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="407" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,836.392,642.62) scale(1,1) translate(0,0)" writing-mode="lr" x="836.39" xml:space="preserve" y="647.12" zvalue="456">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="412" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1460.77,646.12) scale(1,1) translate(3.702e-12,5.66764e-13)" writing-mode="lr" x="1460.77" xml:space="preserve" y="650.62" zvalue="460">002</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="414" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,766.134,167.625) scale(1,1) translate(0,0)" writing-mode="lr" x="766.13" xml:space="preserve" y="172.12" zvalue="463">35kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="420" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,721.75,317.778) scale(1,1) translate(0,0)" writing-mode="lr" x="721.75" xml:space="preserve" y="322.28" zvalue="467">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="421" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,721.75,262.778) scale(1,1) translate(0,0)" writing-mode="lr" x="721.75" xml:space="preserve" y="267.28" zvalue="471">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="427" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,750.25,420.278) scale(1,1) translate(0,0)" writing-mode="lr" x="750.25" xml:space="preserve" y="424.78" zvalue="475">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="428" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1372.75,414.419) scale(1,1) translate(0,0)" writing-mode="lr" x="1372.75" xml:space="preserve" y="418.92" zvalue="478">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="435" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1434.75,188.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1434.75" xml:space="preserve" y="192.75" zvalue="483">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="443" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1035.88,95.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1035.88" xml:space="preserve" y="100.13" zvalue="489">35kV芒畹线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="447" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1018.72,263.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1018.72" xml:space="preserve" y="267.67" zvalue="492">321</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="452" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1019,312.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1019" xml:space="preserve" y="316.94" zvalue="496">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="456" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1020.99,211.194) scale(1,1) translate(0,0)" writing-mode="lr" x="1020.99" xml:space="preserve" y="215.69" zvalue="500">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="459" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1090.36,288.528) scale(1,1) translate(0,0)" writing-mode="lr" x="1090.36" xml:space="preserve" y="293.03" zvalue="504">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="463" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1090.36,235.917) scale(1,1) translate(0,0)" writing-mode="lr" x="1090.36" xml:space="preserve" y="240.42" zvalue="508">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="467" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1088.86,187.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1088.86" xml:space="preserve" y="192.17" zvalue="512">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="476" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,600.883,547.819) scale(1,1) translate(0,0)" writing-mode="lr" x="600.88" xml:space="preserve" y="552.3200000000001" zvalue="521">10kVⅠ段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="482" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,560.667,645.901) scale(1,1) translate(0,0)" writing-mode="lr" x="560.67" xml:space="preserve" y="650.4" zvalue="525">0901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,475.541,731.626) scale(1,1) translate(0,0)" writing-mode="lr" x="475.54" xml:space="preserve" y="736.13" zvalue="646">021</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,479.25,781.028) scale(1,1) translate(0,0)" writing-mode="lr" x="479.25" xml:space="preserve" y="785.53" zvalue="652">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,520.387,820.515) scale(1,1) translate(0,0)" writing-mode="lr" x="520.39" xml:space="preserve" y="825.02" zvalue="655">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,502.111,950.986) scale(1,1) translate(0,0)" writing-mode="lr" x="502.11" xml:space="preserve" y="955.49" zvalue="658">10kV1号电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,480.111,860.111) scale(1,1) translate(0,0)" writing-mode="lr" x="480.11" xml:space="preserve" y="864.61" zvalue="670">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="194" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1440.28,202.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1440.28" xml:space="preserve" y="207.33" zvalue="803">50kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,702.746,821.515) scale(1,1) translate(0,0)" writing-mode="lr" x="702.75" xml:space="preserve" y="826.02" zvalue="994">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,687.444,935.917) scale(1,1) translate(0,0)" writing-mode="lr" x="687.4400000000001" xml:space="preserve" y="940.42" zvalue="997">10kV城区一回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,662.33,729.537) scale(1,1) translate(0,0)" writing-mode="lr" x="662.33" xml:space="preserve" y="734.04" zvalue="1000">022</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,711.535,782.028) scale(1,1) translate(0,0)" writing-mode="lr" x="711.54" xml:space="preserve" y="786.53" zvalue="1003">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,659.674,873.028) scale(1,1) translate(0,0)" writing-mode="lr" x="659.67" xml:space="preserve" y="877.53" zvalue="1007">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,848.746,821.515) scale(1,1) translate(0,0)" writing-mode="lr" x="848.75" xml:space="preserve" y="826.02" zvalue="1020">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,833.444,935.917) scale(1,1) translate(-1.74404e-13,0)" writing-mode="lr" x="833.4400000000001" xml:space="preserve" y="940.42" zvalue="1023">10kV城区二回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,808.33,729.537) scale(1,1) translate(0,0)" writing-mode="lr" x="808.33" xml:space="preserve" y="734.04" zvalue="1026">023</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,857.535,782.028) scale(1,1) translate(0,0)" writing-mode="lr" x="857.54" xml:space="preserve" y="786.53" zvalue="1029">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,805.674,873.028) scale(1,1) translate(0,0)" writing-mode="lr" x="805.67" xml:space="preserve" y="877.53" zvalue="1033">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="110" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,978.746,821.515) scale(1,1) translate(0,0)" writing-mode="lr" x="978.75" xml:space="preserve" y="826.02" zvalue="1043">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="106" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,963.444,935.917) scale(1,1) translate(0,0)" writing-mode="lr" x="963.4400000000001" xml:space="preserve" y="940.42" zvalue="1046">10kV备用线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="105" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,938.33,729.537) scale(1,1) translate(0,0)" writing-mode="lr" x="938.33" xml:space="preserve" y="734.04" zvalue="1049">024</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="104" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,987.535,782.028) scale(1,1) translate(2.17612e-13,0)" writing-mode="lr" x="987.54" xml:space="preserve" y="786.53" zvalue="1051">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,935.674,873.028) scale(1,1) translate(0,0)" writing-mode="lr" x="935.67" xml:space="preserve" y="877.53" zvalue="1055">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="432" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1108.75,821.515) scale(1,1) translate(0,0)" writing-mode="lr" x="1108.75" xml:space="preserve" y="826.02" zvalue="1065">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="391" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1093.44,935.917) scale(1,1) translate(0,0)" writing-mode="lr" x="1093.44" xml:space="preserve" y="940.42" zvalue="1068">10kV畹两线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="388" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1068.33,729.537) scale(1,1) translate(0,0)" writing-mode="lr" x="1068.33" xml:space="preserve" y="734.04" zvalue="1071">025</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="387" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1117.54,782.028) scale(1,1) translate(0,0)" writing-mode="lr" x="1117.54" xml:space="preserve" y="786.53" zvalue="1073">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="386" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1065.67,873.028) scale(1,1) translate(0,0)" writing-mode="lr" x="1065.67" xml:space="preserve" y="877.53" zvalue="1077">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="509" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1692.78,548.833) scale(1,1) translate(0,1.19756e-13)" writing-mode="lr" x="1692.78" xml:space="preserve" y="553.33" zvalue="1087">10kVⅡ段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="507" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1652.56,646.915) scale(1,1) translate(0,5.67471e-13)" writing-mode="lr" x="1652.56" xml:space="preserve" y="651.42" zvalue="1089">0902</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="521" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1354.75,821.515) scale(1,1) translate(0,0)" writing-mode="lr" x="1354.75" xml:space="preserve" y="826.02" zvalue="1094">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="520" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1339.44,935.917) scale(1,1) translate(0,0)" writing-mode="lr" x="1339.44" xml:space="preserve" y="940.42" zvalue="1097">10kV畹莫线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="519" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1314.33,729.537) scale(1,1) translate(0,0)" writing-mode="lr" x="1314.33" xml:space="preserve" y="734.04" zvalue="1100">026</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="518" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1363.54,782.028) scale(1,1) translate(0,0)" writing-mode="lr" x="1363.54" xml:space="preserve" y="786.53" zvalue="1102">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="516" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1375.67,837.028) scale(1,1) translate(0,0)" writing-mode="lr" x="1375.67" xml:space="preserve" y="841.53" zvalue="1106">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="565" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1484.75,821.515) scale(1,1) translate(0,0)" writing-mode="lr" x="1484.75" xml:space="preserve" y="826.02" zvalue="1138">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="564" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1469.44,935.917) scale(1,1) translate(0,0)" writing-mode="lr" x="1469.44" xml:space="preserve" y="940.42" zvalue="1141">10kV备用线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="563" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1444.33,729.537) scale(1,1) translate(0,0)" writing-mode="lr" x="1444.33" xml:space="preserve" y="734.04" zvalue="1144">027</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="562" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1493.54,782.028) scale(1,1) translate(0,0)" writing-mode="lr" x="1493.54" xml:space="preserve" y="786.53" zvalue="1146">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="561" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1441.67,873.028) scale(1,1) translate(0,0)" writing-mode="lr" x="1441.67" xml:space="preserve" y="877.53" zvalue="1150">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="588" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1614.75,821.515) scale(1,1) translate(0,0)" writing-mode="lr" x="1614.75" xml:space="preserve" y="826.02" zvalue="1160">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="587" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1599.44,935.917) scale(1,1) translate(0,0)" writing-mode="lr" x="1599.44" xml:space="preserve" y="940.42" zvalue="1163">10kV城东线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="586" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1574.33,729.537) scale(1,1) translate(0,0)" writing-mode="lr" x="1574.33" xml:space="preserve" y="734.04" zvalue="1166">028</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="585" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1623.54,782.028) scale(1,1) translate(0,0)" writing-mode="lr" x="1623.54" xml:space="preserve" y="786.53" zvalue="1168">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="584" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1571.67,873.028) scale(1,1) translate(0,0)" writing-mode="lr" x="1571.67" xml:space="preserve" y="877.53" zvalue="1172">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="609" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1744.75,821.515) scale(1,1) translate(0,0)" writing-mode="lr" x="1744.75" xml:space="preserve" y="826.02" zvalue="1182">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="608" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1729.44,935.917) scale(1,1) translate(0,0)" writing-mode="lr" x="1729.44" xml:space="preserve" y="940.42" zvalue="1185">10kV岭畹线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="607" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1704.33,729.537) scale(1,1) translate(0,0)" writing-mode="lr" x="1704.33" xml:space="preserve" y="734.04" zvalue="1188">029</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="606" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1753.54,782.028) scale(1,1) translate(0,0)" writing-mode="lr" x="1753.54" xml:space="preserve" y="786.53" zvalue="1190">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="605" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1701.67,873.028) scale(1,1) translate(0,0)" writing-mode="lr" x="1701.67" xml:space="preserve" y="877.53" zvalue="1194">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="629" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1277.89,959.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1277.89" xml:space="preserve" y="963.9400000000001" zvalue="1205">#2站用变</text>
  <line fill="none" id="297" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.57142857142912" x2="402.5714285714287" y1="160.7276354712253" y2="160.7276354712253" zvalue="1282"/>
  <line fill="none" id="278" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="403.5714285714287" x2="403.5714285714287" y1="18.85714285714289" y2="1048.857142857143" zvalue="1283"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.57142857142867" x2="217.5714285714287" y1="172.8571428571429" y2="172.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.57142857142867" x2="217.5714285714287" y1="198.8571428571429" y2="198.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.57142857142867" x2="36.57142857142867" y1="172.8571428571429" y2="198.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.5714285714287" x2="217.5714285714287" y1="172.8571428571429" y2="198.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.5714285714287" x2="398.5714285714287" y1="172.8571428571429" y2="172.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.5714285714287" x2="398.5714285714287" y1="198.8571428571429" y2="198.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.5714285714287" x2="217.5714285714287" y1="172.8571428571429" y2="198.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="398.5714285714287" x2="398.5714285714287" y1="172.8571428571429" y2="198.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.57142857142867" x2="217.5714285714287" y1="198.8571428571429" y2="198.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.57142857142867" x2="217.5714285714287" y1="223.1071428571429" y2="223.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.57142857142867" x2="36.57142857142867" y1="198.8571428571429" y2="223.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.5714285714287" x2="217.5714285714287" y1="198.8571428571429" y2="223.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.5714285714287" x2="398.5714285714287" y1="198.8571428571429" y2="198.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.5714285714287" x2="398.5714285714287" y1="223.1071428571429" y2="223.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.5714285714287" x2="217.5714285714287" y1="198.8571428571429" y2="223.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="398.5714285714287" x2="398.5714285714287" y1="198.8571428571429" y2="223.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.57142857142867" x2="217.5714285714287" y1="223.1071428571429" y2="223.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.57142857142867" x2="217.5714285714287" y1="245.8571428571429" y2="245.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.57142857142867" x2="36.57142857142867" y1="223.1071428571429" y2="245.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.5714285714287" x2="217.5714285714287" y1="223.1071428571429" y2="245.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.5714285714287" x2="398.5714285714287" y1="223.1071428571429" y2="223.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.5714285714287" x2="398.5714285714287" y1="245.8571428571429" y2="245.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.5714285714287" x2="217.5714285714287" y1="223.1071428571429" y2="245.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="398.5714285714287" x2="398.5714285714287" y1="223.1071428571429" y2="245.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.57142857142867" x2="217.5714285714287" y1="245.8571428571429" y2="245.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.57142857142867" x2="217.5714285714287" y1="268.6071428571429" y2="268.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.57142857142867" x2="36.57142857142867" y1="245.8571428571429" y2="268.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.5714285714287" x2="217.5714285714287" y1="245.8571428571429" y2="268.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.5714285714287" x2="398.5714285714287" y1="245.8571428571429" y2="245.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.5714285714287" x2="398.5714285714287" y1="268.6071428571429" y2="268.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.5714285714287" x2="217.5714285714287" y1="245.8571428571429" y2="268.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="398.5714285714287" x2="398.5714285714287" y1="245.8571428571429" y2="268.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.57142857142867" x2="217.5714285714287" y1="268.6071428571429" y2="268.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.57142857142867" x2="217.5714285714287" y1="291.3571428571429" y2="291.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.57142857142867" x2="36.57142857142867" y1="268.6071428571429" y2="291.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.5714285714287" x2="217.5714285714287" y1="268.6071428571429" y2="291.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.5714285714287" x2="398.5714285714287" y1="268.6071428571429" y2="268.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.5714285714287" x2="398.5714285714287" y1="291.3571428571429" y2="291.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.5714285714287" x2="217.5714285714287" y1="268.6071428571429" y2="291.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="398.5714285714287" x2="398.5714285714287" y1="268.6071428571429" y2="291.3571428571429"/>
  <line fill="none" id="274" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.57142857142912" x2="402.5714285714287" y1="630.7276354712253" y2="630.7276354712253" zvalue="1285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="87.57142857142867" x2="133.3459285714287" y1="452.8571428571429" y2="452.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="87.57142857142867" x2="133.3459285714287" y1="491.1394428571429" y2="491.1394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="87.57142857142867" x2="87.57142857142867" y1="452.8571428571429" y2="491.1394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="133.3459285714287" x2="133.3459285714287" y1="452.8571428571429" y2="491.1394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="133.3459285714287" x2="192.1523285714287" y1="452.8571428571429" y2="452.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="133.3459285714287" x2="192.1523285714287" y1="491.1394428571429" y2="491.1394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="133.3459285714287" x2="133.3459285714287" y1="452.8571428571429" y2="491.1394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.1523285714287" x2="192.1523285714287" y1="452.8571428571429" y2="491.1394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.1523285714287" x2="250.9587285714287" y1="452.8571428571429" y2="452.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.1523285714287" x2="250.9587285714287" y1="491.1394428571429" y2="491.1394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.1523285714287" x2="192.1523285714287" y1="452.8571428571429" y2="491.1394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="250.9587285714287" x2="250.9587285714287" y1="452.8571428571429" y2="491.1394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="250.9586285714287" x2="309.7650285714287" y1="452.8571428571429" y2="452.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="250.9586285714287" x2="309.7650285714287" y1="491.1394428571429" y2="491.1394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="250.9586285714287" x2="250.9586285714287" y1="452.8571428571429" y2="491.1394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="309.7650285714287" x2="309.7650285714287" y1="452.8571428571429" y2="491.1394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="309.7650285714287" x2="368.5714285714287" y1="452.8571428571429" y2="452.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="309.7650285714287" x2="368.5714285714287" y1="491.1394428571429" y2="491.1394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="309.7650285714287" x2="309.7650285714287" y1="452.8571428571429" y2="491.1394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.5714285714287" x2="368.5714285714287" y1="452.8571428571429" y2="491.1394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="87.57142857142867" x2="133.3459285714287" y1="491.1394428571429" y2="491.1394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="87.57142857142867" x2="133.3459285714287" y1="515.8188428571428" y2="515.8188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="87.57142857142867" x2="87.57142857142867" y1="491.1394428571429" y2="515.8188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="133.3459285714287" x2="133.3459285714287" y1="491.1394428571429" y2="515.8188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="133.3459285714287" x2="192.1523285714287" y1="491.1394428571429" y2="491.1394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="133.3459285714287" x2="192.1523285714287" y1="515.8188428571428" y2="515.8188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="133.3459285714287" x2="133.3459285714287" y1="491.1394428571429" y2="515.8188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.1523285714287" x2="192.1523285714287" y1="491.1394428571429" y2="515.8188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.1523285714287" x2="250.9587285714287" y1="491.1394428571429" y2="491.1394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.1523285714287" x2="250.9587285714287" y1="515.8188428571428" y2="515.8188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.1523285714287" x2="192.1523285714287" y1="491.1394428571429" y2="515.8188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="250.9587285714287" x2="250.9587285714287" y1="491.1394428571429" y2="515.8188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="250.9586285714287" x2="309.7650285714287" y1="491.1394428571429" y2="491.1394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="250.9586285714287" x2="309.7650285714287" y1="515.8188428571428" y2="515.8188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="250.9586285714287" x2="250.9586285714287" y1="491.1394428571429" y2="515.8188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="309.7650285714287" x2="309.7650285714287" y1="491.1394428571429" y2="515.8188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="309.7650285714287" x2="368.5714285714287" y1="491.1394428571429" y2="491.1394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="309.7650285714287" x2="368.5714285714287" y1="515.8188428571428" y2="515.8188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="309.7650285714287" x2="309.7650285714287" y1="491.1394428571429" y2="515.8188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.5714285714287" x2="368.5714285714287" y1="491.1394428571429" y2="515.8188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="87.57142857142867" x2="133.3459285714287" y1="515.8188428571428" y2="515.8188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="87.57142857142867" x2="133.3459285714287" y1="540.4982428571429" y2="540.4982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="87.57142857142867" x2="87.57142857142867" y1="515.8188428571428" y2="540.4982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="133.3459285714287" x2="133.3459285714287" y1="515.8188428571428" y2="540.4982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="133.3459285714287" x2="192.1523285714287" y1="515.8188428571428" y2="515.8188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="133.3459285714287" x2="192.1523285714287" y1="540.4982428571429" y2="540.4982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="133.3459285714287" x2="133.3459285714287" y1="515.8188428571428" y2="540.4982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.1523285714287" x2="192.1523285714287" y1="515.8188428571428" y2="540.4982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.1523285714287" x2="250.9587285714287" y1="515.8188428571428" y2="515.8188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.1523285714287" x2="250.9587285714287" y1="540.4982428571429" y2="540.4982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.1523285714287" x2="192.1523285714287" y1="515.8188428571428" y2="540.4982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="250.9587285714287" x2="250.9587285714287" y1="515.8188428571428" y2="540.4982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="250.9586285714287" x2="309.7650285714287" y1="515.8188428571428" y2="515.8188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="250.9586285714287" x2="309.7650285714287" y1="540.4982428571429" y2="540.4982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="250.9586285714287" x2="250.9586285714287" y1="515.8188428571428" y2="540.4982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="309.7650285714287" x2="309.7650285714287" y1="515.8188428571428" y2="540.4982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="309.7650285714287" x2="368.5714285714287" y1="515.8188428571428" y2="515.8188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="309.7650285714287" x2="368.5714285714287" y1="540.4982428571429" y2="540.4982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="309.7650285714287" x2="309.7650285714287" y1="515.8188428571428" y2="540.4982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.5714285714287" x2="368.5714285714287" y1="515.8188428571428" y2="540.4982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="87.57142857142867" x2="133.3459285714287" y1="540.4982428571429" y2="540.4982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="87.57142857142867" x2="133.3459285714287" y1="565.1776428571429" y2="565.1776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="87.57142857142867" x2="87.57142857142867" y1="540.4982428571429" y2="565.1776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="133.3459285714287" x2="133.3459285714287" y1="540.4982428571429" y2="565.1776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="133.3459285714287" x2="192.1523285714287" y1="540.4982428571429" y2="540.4982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="133.3459285714287" x2="192.1523285714287" y1="565.1776428571429" y2="565.1776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="133.3459285714287" x2="133.3459285714287" y1="540.4982428571429" y2="565.1776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.1523285714287" x2="192.1523285714287" y1="540.4982428571429" y2="565.1776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.1523285714287" x2="250.9587285714287" y1="540.4982428571429" y2="540.4982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.1523285714287" x2="250.9587285714287" y1="565.1776428571429" y2="565.1776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.1523285714287" x2="192.1523285714287" y1="540.4982428571429" y2="565.1776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="250.9587285714287" x2="250.9587285714287" y1="540.4982428571429" y2="565.1776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="250.9586285714287" x2="309.7650285714287" y1="540.4982428571429" y2="540.4982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="250.9586285714287" x2="309.7650285714287" y1="565.1776428571429" y2="565.1776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="250.9586285714287" x2="250.9586285714287" y1="540.4982428571429" y2="565.1776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="309.7650285714287" x2="309.7650285714287" y1="540.4982428571429" y2="565.1776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="309.7650285714287" x2="368.5714285714287" y1="540.4982428571429" y2="540.4982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="309.7650285714287" x2="368.5714285714287" y1="565.1776428571429" y2="565.1776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="309.7650285714287" x2="309.7650285714287" y1="540.4982428571429" y2="565.1776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.5714285714287" x2="368.5714285714287" y1="540.4982428571429" y2="565.1776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="87.57142857142867" x2="133.3459285714287" y1="565.1777428571429" y2="565.1777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="87.57142857142867" x2="133.3459285714287" y1="589.8571428571429" y2="589.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="87.57142857142867" x2="87.57142857142867" y1="565.1777428571429" y2="589.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="133.3459285714287" x2="133.3459285714287" y1="565.1777428571429" y2="589.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="133.3459285714287" x2="192.1523285714287" y1="565.1777428571429" y2="565.1777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="133.3459285714287" x2="192.1523285714287" y1="589.8571428571429" y2="589.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="133.3459285714287" x2="133.3459285714287" y1="565.1777428571429" y2="589.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.1523285714287" x2="192.1523285714287" y1="565.1777428571429" y2="589.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.1523285714287" x2="250.9587285714287" y1="565.1777428571429" y2="565.1777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.1523285714287" x2="250.9587285714287" y1="589.8571428571429" y2="589.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.1523285714287" x2="192.1523285714287" y1="565.1777428571429" y2="589.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="250.9587285714287" x2="250.9587285714287" y1="565.1777428571429" y2="589.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="250.9586285714287" x2="309.7650285714287" y1="565.1777428571429" y2="565.1777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="250.9586285714287" x2="309.7650285714287" y1="589.8571428571429" y2="589.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="250.9586285714287" x2="250.9586285714287" y1="565.1777428571429" y2="589.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="309.7650285714287" x2="309.7650285714287" y1="565.1777428571429" y2="589.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="309.7650285714287" x2="368.5714285714287" y1="565.1777428571429" y2="565.1777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="309.7650285714287" x2="368.5714285714287" y1="589.8571428571429" y2="589.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="309.7650285714287" x2="309.7650285714287" y1="565.1777428571429" y2="589.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.5714285714287" x2="368.5714285714287" y1="565.1777428571429" y2="589.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="87.57142857142867" x2="133.3459285714287" y1="589.8571428571429" y2="589.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="87.57142857142867" x2="133.3459285714287" y1="614.5365428571429" y2="614.5365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="87.57142857142867" x2="87.57142857142867" y1="589.8571428571429" y2="614.5365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="133.3459285714287" x2="133.3459285714287" y1="589.8571428571429" y2="614.5365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="133.3459285714287" x2="192.1523285714287" y1="589.8571428571429" y2="589.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="133.3459285714287" x2="192.1523285714287" y1="614.5365428571429" y2="614.5365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="133.3459285714287" x2="133.3459285714287" y1="589.8571428571429" y2="614.5365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.1523285714287" x2="192.1523285714287" y1="589.8571428571429" y2="614.5365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.1523285714287" x2="250.9587285714287" y1="589.8571428571429" y2="589.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.1523285714287" x2="250.9587285714287" y1="614.5365428571429" y2="614.5365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.1523285714287" x2="192.1523285714287" y1="589.8571428571429" y2="614.5365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="250.9587285714287" x2="250.9587285714287" y1="589.8571428571429" y2="614.5365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="250.9586285714287" x2="309.7650285714287" y1="589.8571428571429" y2="589.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="250.9586285714287" x2="309.7650285714287" y1="614.5365428571429" y2="614.5365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="250.9586285714287" x2="250.9586285714287" y1="589.8571428571429" y2="614.5365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="309.7650285714287" x2="309.7650285714287" y1="589.8571428571429" y2="614.5365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="309.7650285714287" x2="368.5714285714287" y1="589.8571428571429" y2="589.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="309.7650285714287" x2="368.5714285714287" y1="614.5365428571429" y2="614.5365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="309.7650285714287" x2="309.7650285714287" y1="589.8571428571429" y2="614.5365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.5714285714287" x2="368.5714285714287" y1="589.8571428571429" y2="614.5365428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="35.57142857142867" x2="125.5714285714287" y1="945.8571428571429" y2="945.8571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="35.57142857142867" x2="125.5714285714287" y1="985.0204428571428" y2="985.0204428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="35.57142857142867" x2="35.57142857142867" y1="945.8571428571429" y2="985.0204428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="125.5714285714287" x2="125.5714285714287" y1="945.8571428571429" y2="985.0204428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="125.5714285714287" x2="395.5714285714287" y1="945.8571428571429" y2="945.8571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="125.5714285714287" x2="395.5714285714287" y1="985.0204428571428" y2="985.0204428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="125.5714285714287" x2="125.5714285714287" y1="945.8571428571429" y2="985.0204428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="395.5714285714287" x2="395.5714285714287" y1="945.8571428571429" y2="985.0204428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="35.57142857142867" x2="125.5714285714287" y1="985.0204128571429" y2="985.0204128571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="35.57142857142867" x2="125.5714285714287" y1="1012.938812857143" y2="1012.938812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="35.57142857142867" x2="35.57142857142867" y1="985.0204128571429" y2="1012.938812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="125.5714285714287" x2="125.5714285714287" y1="985.0204128571429" y2="1012.938812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="125.5714285714287" x2="215.5714285714287" y1="985.0204128571429" y2="985.0204128571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="125.5714285714287" x2="215.5714285714287" y1="1012.938812857143" y2="1012.938812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="125.5714285714287" x2="125.5714285714287" y1="985.0204128571429" y2="1012.938812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="215.5714285714287" x2="215.5714285714287" y1="985.0204128571429" y2="1012.938812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="215.5714285714288" x2="305.5714285714288" y1="985.0204128571429" y2="985.0204128571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="215.5714285714288" x2="305.5714285714288" y1="1012.938812857143" y2="1012.938812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="215.5714285714288" x2="215.5714285714288" y1="985.0204128571429" y2="1012.938812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="305.5714285714288" x2="305.5714285714288" y1="985.0204128571429" y2="1012.938812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="305.5714285714287" x2="395.5714285714287" y1="985.0204128571429" y2="985.0204128571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="305.5714285714287" x2="395.5714285714287" y1="1012.938812857143" y2="1012.938812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="305.5714285714287" x2="305.5714285714287" y1="985.0204128571429" y2="1012.938812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="395.5714285714287" x2="395.5714285714287" y1="985.0204128571429" y2="1012.938812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="35.57142857142867" x2="125.5714285714287" y1="1012.938742857143" y2="1012.938742857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="35.57142857142867" x2="125.5714285714287" y1="1040.857142857143" y2="1040.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="35.57142857142867" x2="35.57142857142867" y1="1012.938742857143" y2="1040.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="125.5714285714287" x2="125.5714285714287" y1="1012.938742857143" y2="1040.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="125.5714285714287" x2="215.5714285714287" y1="1012.938742857143" y2="1012.938742857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="125.5714285714287" x2="215.5714285714287" y1="1040.857142857143" y2="1040.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="125.5714285714287" x2="125.5714285714287" y1="1012.938742857143" y2="1040.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="215.5714285714287" x2="215.5714285714287" y1="1012.938742857143" y2="1040.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="215.5714285714288" x2="305.5714285714288" y1="1012.938742857143" y2="1012.938742857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="215.5714285714288" x2="305.5714285714288" y1="1040.857142857143" y2="1040.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="215.5714285714288" x2="215.5714285714288" y1="1012.938742857143" y2="1040.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="305.5714285714288" x2="305.5714285714288" y1="1012.938742857143" y2="1040.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="305.5714285714287" x2="395.5714285714287" y1="1012.938742857143" y2="1012.938742857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="305.5714285714287" x2="395.5714285714287" y1="1040.857142857143" y2="1040.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="305.5714285714287" x2="305.5714285714287" y1="1012.938742857143" y2="1040.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="395.5714285714287" x2="395.5714285714287" y1="1012.938742857143" y2="1040.857142857143"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="270" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,80.5714,965.857) scale(1,1) translate(0,0)" writing-mode="lr" x="80.56999999999999" xml:space="preserve" y="971.86" zvalue="1289">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="269" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5714,999.857) scale(1,1) translate(0,0)" writing-mode="lr" x="77.56999999999999" xml:space="preserve" y="1005.86" zvalue="1290">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="268" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,259.571,999.857) scale(1,1) translate(0,0)" writing-mode="lr" x="259.57" xml:space="preserve" y="1005.86" zvalue="1291">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="267" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,76.5714,1027.86) scale(1,1) translate(0,0)" writing-mode="lr" x="76.56999999999999" xml:space="preserve" y="1033.86" zvalue="1292">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="266" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,258.571,1027.86) scale(1,1) translate(0,0)" writing-mode="lr" x="258.57" xml:space="preserve" y="1033.86" zvalue="1293">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="265" stroke="rgb(255,255,255)" text-anchor="middle" x="162.5546875" xml:space="preserve" y="469.2586805555555" zvalue="1294">35kV Ⅰ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="265" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="162.5546875" xml:space="preserve" y="486.2586805555555" zvalue="1294">母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="262" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,101.071,660.357) scale(1,1) translate(0,0)" writing-mode="lr" x="101.0714285714287" xml:space="preserve" y="664.8571428571429" zvalue="1296">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="261" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.97,327.699) scale(1,1) translate(0,0)" writing-mode="lr" x="233.97" xml:space="preserve" y="332.2" zvalue="1297">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="260" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,338.97,327.699) scale(1,1) translate(0,0)" writing-mode="lr" x="338.97" xml:space="preserve" y="332.2" zvalue="1298">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="259" stroke="rgb(255,255,255)" text-anchor="middle" x="279.2578125" xml:space="preserve" y="468.6961805555555" zvalue="1299">10kV Ⅰ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="259" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="279.2578125" xml:space="preserve" y="485.6961805555555" zvalue="1299">母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="258" stroke="rgb(255,255,255)" text-anchor="middle" x="339.7890625" xml:space="preserve" y="468.6961805555555" zvalue="1300">10kV Ⅱ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="258" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="339.7890625" xml:space="preserve" y="485.6961805555555" zvalue="1300">母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="257" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,112.571,503.607) scale(1,1) translate(0,0)" writing-mode="lr" x="112.5714285714287" xml:space="preserve" y="508.1071428571429" zvalue="1301">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="256" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,112.571,529.107) scale(1,1) translate(0,0)" writing-mode="lr" x="112.5714285714287" xml:space="preserve" y="533.6071428571429" zvalue="1302">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="255" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,112.571,552.107) scale(1,1) translate(0,0)" writing-mode="lr" x="112.5714285714287" xml:space="preserve" y="556.6071428571429" zvalue="1303">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="254" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,112.571,575.107) scale(1,1) translate(0,0)" writing-mode="lr" x="112.5714285714287" xml:space="preserve" y="579.6071428571429" zvalue="1304">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="253" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,112.571,602.107) scale(1,1) translate(0,0)" writing-mode="lr" x="112.5714285714287" xml:space="preserve" y="606.6071428571429" zvalue="1305">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="249" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,260.626,967.857) scale(1,1) translate(0,0)" writing-mode="lr" x="260.63" xml:space="preserve" y="973.86" zvalue="1306">WangDing-01-2020</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="247" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,350.626,999.857) scale(1,1) translate(0,0)" writing-mode="lr" x="350.63" xml:space="preserve" y="1005.86" zvalue="1308">20200901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="246" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,74.5714,186.857) scale(1,1) translate(0,0)" writing-mode="lr" x="74.56999999999999" xml:space="preserve" y="192.36" zvalue="1309">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="242" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,254.571,186.857) scale(1,1) translate(0,0)" writing-mode="lr" x="254.57" xml:space="preserve" y="192.36" zvalue="1310">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="241" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.2589,211.107) scale(1,1) translate(0,0)" writing-mode="lr" x="78.26000000000001" xml:space="preserve" y="215.61" zvalue="1311">35kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="240" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,81.7589,258.857) scale(1,1) translate(0,0)" writing-mode="lr" x="81.76000000000001" xml:space="preserve" y="264.36" zvalue="1312">1号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="239" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,263.321,258.357) scale(1,1) translate(0,0)" writing-mode="lr" x="263.32" xml:space="preserve" y="263.86" zvalue="1313">2号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="238" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,81.7589,281.857) scale(1,1) translate(0,0)" writing-mode="lr" x="81.76000000000001" xml:space="preserve" y="287.36" zvalue="1314">1号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="237" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,263.321,281.357) scale(1,1) translate(0,0)" writing-mode="lr" x="263.32" xml:space="preserve" y="286.86" zvalue="1315">2号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="236" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,79.2589,235.107) scale(1,1) translate(0,0)" writing-mode="lr" x="79.26000000000001" xml:space="preserve" y="239.61" zvalue="1316">10kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="235" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,262.321,234.857) scale(1,1) translate(0,0)" writing-mode="lr" x="262.32" xml:space="preserve" y="239.36" zvalue="1317">10kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,171.626,999.857) scale(1,1) translate(0,0)" writing-mode="lr" x="171.63" xml:space="preserve" y="1005.86" zvalue="1428">唐涛</text>
 </g>
 <g id="ButtonClass">
  <g href="小电流装置20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="296" y="393.25" zvalue="1422"/></g>
  <g href="全站公用20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="92.19" y="312.25" zvalue="1423"/></g>
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="194.09" y="393.25" zvalue="1424"/></g>
  <g href="单厂站信息-20210617.svg"><rect fill-opacity="0" height="24" width="72.88" x="92.19" y="393.25" zvalue="1425"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="92.19" y="352.75" zvalue="1426"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="508">
   <path class="kv35" d="M 606 344.61 L 1582.59 344.61" stroke-width="6" zvalue="18"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674406760451" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674406760451"/></metadata>
  <path d="M 606 344.61 L 1582.59 344.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="429">
   <path class="kv10" d="M 462.33 690.5 L 1209.75 690.5" stroke-width="6" zvalue="37"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674406694915" ObjectName="10kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674406694915"/></metadata>
  <path d="M 462.33 690.5 L 1209.75 690.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="426">
   <path class="kv10" d="M 1261 691.75 L 1849.33 691.75" stroke-width="6" zvalue="38"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674406825987" ObjectName="10kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674406825987"/></metadata>
  <path d="M 1261 691.75 L 1849.33 691.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="449">
   <use class="kv35" height="30" transform="rotate(0,773.75,289.944) scale(0.833333,0.833333) translate(153.5,55.4889)" width="15" x="767.5" xlink:href="#Disconnector:刀闸_0" y="277.444442987442" zvalue="33"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454124109827" ObjectName="35kV母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454124109827"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,773.75,289.944) scale(0.833333,0.833333) translate(153.5,55.4889)" width="15" x="767.5" y="277.444442987442"/></g>
  <g id="574">
   <use class="kv10" height="36" transform="rotate(0,1168.98,643.679) scale(1.30867,1.22026) translate(-273.564,-112.22)" width="14" x="1159.821428571429" xlink:href="#Disconnector:手车刀闸_0" y="621.7142857142857" zvalue="98"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454124044291" ObjectName="10kV分段0121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454124044291"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1168.98,643.679) scale(1.30867,1.22026) translate(-273.564,-112.22)" width="14" x="1159.821428571429" y="621.7142857142857"/></g>
  <g id="633">
   <use class="kv35" height="30" transform="rotate(0,810.863,388.694) scale(0.833333,0.833333) translate(160.923,75.2389)" width="15" x="804.6131037206526" xlink:href="#Disconnector:刀闸_0" y="376.1944428814782" zvalue="132"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454123978755" ObjectName="#1主变35kV侧3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454123978755"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,810.863,388.694) scale(0.833333,0.833333) translate(160.923,75.2389)" width="15" x="804.6131037206526" y="376.1944428814782"/></g>
  <g id="664">
   <use class="kv35" height="30" transform="rotate(0,1434.19,388.694) scale(0.833333,0.833333) translate(285.589,75.2389)" width="15" x="1427.944444444445" xlink:href="#Disconnector:刀闸_0" y="376.1944427490234" zvalue="151"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454123913219" ObjectName="#2主变35kV侧3021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454123913219"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1434.19,388.694) scale(0.833333,0.833333) translate(285.589,75.2389)" width="15" x="1427.944444444445" y="376.1944427490234"/></g>
  <g id="438">
   <use class="kv35" height="30" transform="rotate(0,1435.75,299) scale(1.25,1.25) translate(-285.275,-56.05)" width="15" x="1426.375" xlink:href="#Disconnector:令克_0" y="280.25" zvalue="484"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454124830723" ObjectName="35kV1号站用变隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454124830723"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1435.75,299) scale(1.25,1.25) translate(-285.275,-56.05)" width="15" x="1426.375" y="280.25"/></g>
  <g id="448">
   <use class="kv35" height="30" transform="rotate(0,1037.75,313.444) scale(0.833333,0.833333) translate(206.3,60.1889)" width="15" x="1031.5" xlink:href="#Disconnector:刀闸_0" y="300.9444428814782" zvalue="495"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454124961795" ObjectName="35kV芒畹线3211隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454124961795"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1037.75,313.444) scale(0.833333,0.833333) translate(206.3,60.1889)" width="15" x="1031.5" y="300.9444428814782"/></g>
  <g id="453">
   <use class="kv35" height="30" transform="rotate(0,1037.61,212.194) scale(0.833333,0.833333) translate(206.273,39.9389)" width="15" x="1031.363103720653" xlink:href="#Disconnector:刀闸_0" y="199.6944428814782" zvalue="499"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454125027331" ObjectName="35kV芒畹线3216隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454125027331"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1037.61,212.194) scale(0.833333,0.833333) translate(206.273,39.9389)" width="15" x="1031.363103720653" y="199.6944428814782"/></g>
  <g id="470">
   <use class="kv35" height="30" transform="rotate(270,1073.69,159.667) scale(-0.833333,-0.833333) translate(-2363.36,-353.767)" width="15" x="1067.436251263037" xlink:href="#Disconnector:令克_0" y="147.1666666666665" zvalue="515"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454126010371" ObjectName="35kV芒畹线3219隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454126010371"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1073.69,159.667) scale(-0.833333,-0.833333) translate(-2363.36,-353.767)" width="15" x="1067.436251263037" y="147.1666666666665"/></g>
  <g id="479">
   <use class="kv10" height="42" transform="rotate(0,604.618,640.102) scale(1.33658,1.32209) translate(-147.207,-149.178)" width="30" x="584.5692638282546" xlink:href="#Disconnector:手车刀闸带避雷器_0" y="612.3380811390925" zvalue="524"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454125551619" ObjectName="10kVⅠ段母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454125551619"/></metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,604.618,640.102) scale(1.33658,1.32209) translate(-147.207,-149.178)" width="30" x="584.5692638282546" y="612.3380811390925"/></g>
  <g id="58">
   <use class="kv10" height="30" transform="rotate(0,507.762,821.515) scale(0.833333,-0.833333) translate(100.302,-1809.83)" width="15" x="501.5123410938806" xlink:href="#Disconnector:刀闸_0" y="809.0153503417969" zvalue="653"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454125682691" ObjectName="10kV1号电容器0216隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454125682691"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,507.762,821.515) scale(0.833333,-0.833333) translate(100.302,-1809.83)" width="15" x="501.5123410938806" y="809.0153503417969"/></g>
  <g id="34">
   <use class="kv10" height="30" transform="rotate(0,687.371,821.515) scale(0.833333,-0.833333) translate(136.224,-1809.83)" width="15" x="681.1212969020604" xlink:href="#Disconnector:刀闸_0" y="809.0153501387952" zvalue="993"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454126993411" ObjectName="10kV城区一回线0226隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454126993411"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,687.371,821.515) scale(0.833333,-0.833333) translate(136.224,-1809.83)" width="15" x="681.1212969020604" y="809.0153501387952"/></g>
  <g id="98">
   <use class="kv10" height="30" transform="rotate(0,833.371,821.515) scale(0.833333,-0.833333) translate(165.424,-1809.83)" width="15" x="827.1212969020604" xlink:href="#Disconnector:刀闸_0" y="809.0153501387952" zvalue="1019"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454127714307" ObjectName="10kV城区二回线0236隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454127714307"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,833.371,821.515) scale(0.833333,-0.833333) translate(165.424,-1809.83)" width="15" x="827.1212969020604" y="809.0153501387952"/></g>
  <g id="385">
   <use class="kv10" height="30" transform="rotate(0,963.371,821.515) scale(0.833333,-0.833333) translate(191.424,-1809.83)" width="15" x="957.1212969020604" xlink:href="#Disconnector:刀闸_0" y="809.0153501387952" zvalue="1042"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454128304131" ObjectName="10kV备用线0246隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454128304131"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,963.371,821.515) scale(0.833333,-0.833333) translate(191.424,-1809.83)" width="15" x="957.1212969020604" y="809.0153501387952"/></g>
  <g id="505">
   <use class="kv10" height="30" transform="rotate(0,1093.37,821.515) scale(0.833333,-0.833333) translate(217.424,-1809.83)" width="15" x="1087.12129690206" xlink:href="#Disconnector:刀闸_0" y="809.0153501387952" zvalue="1064"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454128893955" ObjectName="10kV畹两线0256隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454128893955"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1093.37,821.515) scale(0.833333,-0.833333) translate(217.424,-1809.83)" width="15" x="1087.12129690206" y="809.0153501387952"/></g>
  <g id="514">
   <use class="kv10" height="42" transform="rotate(0,1696.51,641.116) scale(1.33658,1.32209) translate(-422.17,-149.425)" width="30" x="1676.464376022381" xlink:href="#Disconnector:手车刀闸带避雷器_0" y="613.3519700279815" zvalue="1088"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454128959491" ObjectName="10kVⅡ段母线电压互感器0902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454128959491"/></metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,1696.51,641.116) scale(1.33658,1.32209) translate(-422.17,-149.425)" width="30" x="1676.464376022381" y="613.3519700279815"/></g>
  <g id="538">
   <use class="kv10" height="30" transform="rotate(0,1339.37,821.515) scale(0.833333,-0.833333) translate(266.624,-1809.83)" width="15" x="1333.12129690206" xlink:href="#Disconnector:刀闸_0" y="809.0153501387952" zvalue="1093"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454129614851" ObjectName="10kV畹莫线0266隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454129614851"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1339.37,821.515) scale(0.833333,-0.833333) translate(266.624,-1809.83)" width="15" x="1333.12129690206" y="809.0153501387952"/></g>
  <g id="583">
   <use class="kv10" height="30" transform="rotate(0,1469.37,821.515) scale(0.833333,-0.833333) translate(292.624,-1809.83)" width="15" x="1463.12129690206" xlink:href="#Disconnector:刀闸_0" y="809.0153501387952" zvalue="1137"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454130204675" ObjectName="10kV备用2线0276隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454130204675"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1469.37,821.515) scale(0.833333,-0.833333) translate(292.624,-1809.83)" width="15" x="1463.12129690206" y="809.0153501387952"/></g>
  <g id="604">
   <use class="kv10" height="30" transform="rotate(0,1599.37,821.515) scale(0.833333,-0.833333) translate(318.624,-1809.83)" width="15" x="1593.12129690206" xlink:href="#Disconnector:刀闸_0" y="809.0153501387952" zvalue="1159"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454130794499" ObjectName="10kV城东线0286隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454130794499"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1599.37,821.515) scale(0.833333,-0.833333) translate(318.624,-1809.83)" width="15" x="1593.12129690206" y="809.0153501387952"/></g>
  <g id="625">
   <use class="kv10" height="30" transform="rotate(0,1729.37,821.515) scale(0.833333,-0.833333) translate(344.624,-1809.83)" width="15" x="1723.12129690206" xlink:href="#Disconnector:刀闸_0" y="809.0153501387952" zvalue="1181"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454131384323" ObjectName="10kV岭畹线0296隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454131384323"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1729.37,821.515) scale(0.833333,-0.833333) translate(344.624,-1809.83)" width="15" x="1723.12129690206" y="809.0153501387952"/></g>
  <g id="631">
   <use class="kv10" height="30" transform="rotate(0,1282.04,885.861) scale(1.25,1.25) translate(-254.533,-173.422)" width="15" x="1272.666666666667" xlink:href="#Disconnector:令克_0" y="867.1111111111113" zvalue="1206"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454131515395" ObjectName="#2站用变隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454131515395"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1282.04,885.861) scale(1.25,1.25) translate(-254.533,-173.422)" width="15" x="1272.666666666667" y="867.1111111111113"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="309">
   <path class="kv10" d="M 1168.98 664.42 L 1168.98 690.5" stroke-width="1" zvalue="44"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="574@1" LinkObjectIDznd="429@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1168.98 664.42 L 1168.98 690.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="264">
   <path class="kv10" d="M 1168.98 622.93 L 1168.98 605.09 L 1213.64 605.09" stroke-width="1" zvalue="101"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="574@0" LinkObjectIDznd="576@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1168.98 622.93 L 1168.98 605.09 L 1213.64 605.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="252">
   <path class="kv35" d="M 810.09 493.99 L 810.09 468.4" stroke-width="1" zvalue="130"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="549@0" LinkObjectIDznd="630@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 810.09 493.99 L 810.09 468.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="251">
   <path class="kv35" d="M 810.11 441.16 L 810.11 400.98" stroke-width="1" zvalue="133"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="630@0" LinkObjectIDznd="633@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 810.11 441.16 L 810.11 400.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="250">
   <path class="kv35" d="M 810.94 376.61 L 810.94 344.61" stroke-width="1" zvalue="135"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="633@0" LinkObjectIDznd="508@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 810.94 376.61 L 810.94 344.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="245">
   <path class="kv35" d="M 1433.36 493.99 L 1433.36 468.37" stroke-width="1" zvalue="149"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="667@0" LinkObjectIDznd="666@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1433.36 493.99 L 1433.36 468.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="244">
   <path class="kv35" d="M 1433.86 440.26 L 1433.86 400.98" stroke-width="1" zvalue="152"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="666@0" LinkObjectIDznd="664@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1433.86 440.26 L 1433.86 400.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="243">
   <path class="kv35" d="M 1434.27 376.61 L 1434.27 344.61" stroke-width="1" zvalue="154"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="664@0" LinkObjectIDznd="508@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1434.27 376.61 L 1434.27 344.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="283">
   <path class="kv10" d="M 1259.35 605.22 L 1310.44 605.22 L 1310.44 691.75" stroke-width="1" zvalue="400"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="576@1" LinkObjectIDznd="426@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1259.35 605.22 L 1310.44 605.22 L 1310.44 691.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="405">
   <path class="kv10" d="M 810.06 566.71 L 810.06 625.29" stroke-width="1" zvalue="456"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="549@1" LinkObjectIDznd="403@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 810.06 566.71 L 810.06 625.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="406">
   <path class="kv10" d="M 810.06 662.46 L 810.06 690.5" stroke-width="1" zvalue="457"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="403@0" LinkObjectIDznd="429@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 810.06 662.46 L 810.06 690.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="410">
   <path class="kv10" d="M 1433.33 566.71 L 1433.33 628.79" stroke-width="1" zvalue="460"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="667@1" LinkObjectIDznd="409@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1433.33 566.71 L 1433.33 628.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="411">
   <path class="kv10" d="M 1433.81 665.96 L 1433.81 691.75" stroke-width="1" zvalue="461"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="409@0" LinkObjectIDznd="426@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1433.81 665.96 L 1433.81 691.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="415">
   <path class="kv35" d="M 773.8 302.23 L 773.8 344.61" stroke-width="1" zvalue="463"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="449@1" LinkObjectIDznd="508@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 773.8 302.23 L 773.8 344.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="416">
   <path class="kv35" d="M 773.82 277.86 L 773.82 247.95" stroke-width="1" zvalue="464"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="449@0" LinkObjectIDznd="413@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 773.82 277.86 L 773.82 247.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="419">
   <path class="kv35" d="M 758.83 316.92 L 773.8 316.92" stroke-width="1" zvalue="468"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="417@0" LinkObjectIDznd="415" MaxPinNum="2"/>
   </metadata>
  <path d="M 758.83 316.92 L 773.8 316.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="423">
   <path class="kv35" d="M 758.83 261.92 L 773.82 261.92" stroke-width="1" zvalue="472"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="422@0" LinkObjectIDznd="416" MaxPinNum="2"/>
   </metadata>
  <path d="M 758.83 261.92 L 773.82 261.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="425">
   <path class="kv35" d="M 792.33 419.42 L 810.11 419.42" stroke-width="1" zvalue="475"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="424@0" LinkObjectIDznd="251" MaxPinNum="2"/>
   </metadata>
  <path d="M 792.33 419.42 L 810.11 419.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="431">
   <path class="kv35" d="M 1414.83 413.56 L 1433.86 413.56" stroke-width="1" zvalue="479"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="430@0" LinkObjectIDznd="244" MaxPinNum="2"/>
   </metadata>
  <path d="M 1414.83 413.56 L 1433.86 413.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="439">
   <path class="kv35" d="M 1435.38 267.65 L 1435.38 282.44" stroke-width="1" zvalue="485"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="434@0" LinkObjectIDznd="438@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1435.38 267.65 L 1435.38 282.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="440">
   <path class="kv35" d="M 1435.65 314.31 L 1435.65 344.61" stroke-width="1" zvalue="486"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="438@1" LinkObjectIDznd="243" MaxPinNum="2"/>
   </metadata>
  <path d="M 1435.65 314.31 L 1435.65 344.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="450">
   <path class="kv35" d="M 1037.82 271.37 L 1037.82 301.36" stroke-width="1" zvalue="496"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="444@1" LinkObjectIDznd="448@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1037.82 271.37 L 1037.82 301.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="454">
   <path class="kv35" d="M 1037.69 144.12 L 1037.69 200.11" stroke-width="1" zvalue="500"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="441@0" LinkObjectIDznd="453@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1037.69 144.12 L 1037.69 200.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="455">
   <path class="kv35" d="M 1037.66 224.48 L 1037.66 244.41" stroke-width="1" zvalue="501"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="453@1" LinkObjectIDznd="444@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1037.66 224.48 L 1037.66 244.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="461">
   <path class="kv35" d="M 1053.28 287.67 L 1037.82 287.67" stroke-width="1" zvalue="505"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="460@0" LinkObjectIDznd="450" MaxPinNum="2"/>
   </metadata>
  <path d="M 1053.28 287.67 L 1037.82 287.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="465">
   <path class="kv35" d="M 1053.28 233.67 L 1037.66 233.67" stroke-width="1" zvalue="509"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="462@0" LinkObjectIDznd="455" MaxPinNum="2"/>
   </metadata>
  <path d="M 1053.28 233.67 L 1037.66 233.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="468">
   <path class="kv35" d="M 1054.53 186.67 L 1037.69 186.67" stroke-width="1" zvalue="512"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="466@0" LinkObjectIDznd="454" MaxPinNum="2"/>
   </metadata>
  <path d="M 1054.53 186.67 L 1037.69 186.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="481">
   <path class="kv10" d="M 600.27 667.54 L 600.27 690.5" stroke-width="1" zvalue="526"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="479@1" LinkObjectIDznd="429@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 600.27 667.54 L 600.27 690.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="kv10" d="M 507.81 715.79 L 507.81 690.5" stroke-width="1" zvalue="650"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="63@1" LinkObjectIDznd="429@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 507.81 715.79 L 507.81 690.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="kv10" d="M 507.55 866.69 L 507.55 833.6" stroke-width="1" zvalue="654"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="55@0" LinkObjectIDznd="58@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 507.55 866.69 L 507.55 833.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="kv10" d="M 507.81 809.23 L 507.81 752.96" stroke-width="1" zvalue="656"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="58@1" LinkObjectIDznd="63@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 507.81 809.23 L 507.81 752.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="kv10" d="M 490.08 765.42 L 507.81 765.42" stroke-width="1" zvalue="662"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="59@0" LinkObjectIDznd="56" MaxPinNum="2"/>
   </metadata>
  <path d="M 490.08 765.42 L 507.81 765.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="kv10" d="M 490.94 839.5 L 507.55 839.5" stroke-width="1" zvalue="670"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@0" LinkObjectIDznd="57" MaxPinNum="2"/>
   </metadata>
  <path d="M 490.94 839.5 L 507.55 839.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="35">
   <path class="kv35" d="M 1084.73 159.74 L 1113.58 159.74" stroke-width="1" zvalue="759"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="470@0" LinkObjectIDznd="469@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1084.73 159.74 L 1113.58 159.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="155">
   <path class="kv35" d="M 1015.71 159.72 L 1037.69 159.72" stroke-width="1" zvalue="764"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="154@0" LinkObjectIDznd="454" MaxPinNum="2"/>
   </metadata>
  <path d="M 1015.71 159.72 L 1037.69 159.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="156">
   <path class="kv35" d="M 1063.48 159.6 L 1037.69 159.6" stroke-width="1" zvalue="765"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="470@1" LinkObjectIDznd="155" MaxPinNum="2"/>
   </metadata>
  <path d="M 1063.48 159.6 L 1037.69 159.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="187">
   <path class="kv35" d="M 1037.8 325.73 L 1037.8 344.61" stroke-width="1" zvalue="795"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="448@1" LinkObjectIDznd="508@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1037.8 325.73 L 1037.8 344.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="3">
   <path class="kv35" d="M 786.79 261.92 L 773.82 261.92" stroke-width="1" zvalue="973"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="2@0" LinkObjectIDznd="416" MaxPinNum="2"/>
   </metadata>
  <path d="M 786.79 261.92 L 773.82 261.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="5">
   <path class="kv10" d="M 826.79 612.03 L 810.06 612.03" stroke-width="1" zvalue="976"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="4@0" LinkObjectIDznd="405" MaxPinNum="2"/>
   </metadata>
  <path d="M 826.79 612.03 L 810.06 612.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="7">
   <path class="kv10" d="M 1451.24 617.59 L 1433.33 617.59" stroke-width="1" zvalue="979"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="6@0" LinkObjectIDznd="410" MaxPinNum="2"/>
   </metadata>
  <path d="M 1451.24 617.59 L 1433.33 617.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="kv10" d="M 809.44 582.67 L 810.06 582.67" stroke-width="1" zvalue="982"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="8@0" LinkObjectIDznd="405" MaxPinNum="2"/>
   </metadata>
  <path d="M 809.44 582.67 L 810.06 582.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="14">
   <path class="kv10" d="M 507.11 773.78 L 507.81 773.78" stroke-width="1" zvalue="987"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="13@0" LinkObjectIDznd="56" MaxPinNum="2"/>
   </metadata>
  <path d="M 507.11 773.78 L 507.81 773.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="16">
   <path class="kv10" d="M 1432.67 588.22 L 1433.33 588.22" stroke-width="1" zvalue="990"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="15@0" LinkObjectIDznd="410" MaxPinNum="2"/>
   </metadata>
  <path d="M 1432.67 588.22 L 1433.33 588.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="17">
   <path class="kv10" d="M 600.72 602.02 L 600.72 621.92" stroke-width="1" zvalue="991"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="475@0" LinkObjectIDznd="479@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 600.72 602.02 L 600.72 621.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="33">
   <path class="kv10" d="M 687.44 883.57 L 687.44 833.6" stroke-width="1" zvalue="995"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="32@0" LinkObjectIDznd="34@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 687.44 883.57 L 687.44 833.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="kv10" d="M 687.42 809.23 L 687.42 749.38" stroke-width="1" zvalue="999"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="34@1" LinkObjectIDznd="31@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 687.42 809.23 L 687.42 749.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="29">
   <path class="kv10" d="M 686.63 712.2 L 686.63 690.5" stroke-width="1" zvalue="1001"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="31@1" LinkObjectIDznd="429@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 686.63 712.2 L 686.63 690.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="27">
   <path class="kv10" d="M 701.48 764.08 L 687.42 764.08" stroke-width="1" zvalue="1004"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="28@0" LinkObjectIDznd="30" MaxPinNum="2"/>
   </metadata>
  <path d="M 701.48 764.08 L 687.42 764.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="25">
   <path class="kv10" d="M 670.51 852.42 L 687.44 852.42" stroke-width="1" zvalue="1006"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="26@0" LinkObjectIDznd="33" MaxPinNum="2"/>
   </metadata>
  <path d="M 670.51 852.42 L 687.44 852.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="23">
   <path class="kv10" d="M 715.1 852.42 L 687.44 852.42" stroke-width="1" zvalue="1009"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="24@0" LinkObjectIDznd="33" MaxPinNum="2"/>
   </metadata>
  <path d="M 715.1 852.42 L 687.44 852.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="38">
   <path class="kv10" d="M 663.12 774.57 L 663.12 764.08 L 688 764.08" stroke-width="1" zvalue="1011"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="37@0" LinkObjectIDznd="27" MaxPinNum="2"/>
   </metadata>
  <path d="M 663.12 774.57 L 663.12 764.08 L 688 764.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="kv10" d="M 686.89 779.33 L 687.42 779.33" stroke-width="1" zvalue="1017"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="43@0" LinkObjectIDznd="30" MaxPinNum="2"/>
   </metadata>
  <path d="M 686.89 779.33 L 687.42 779.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv10" d="M 833.44 883.57 L 833.44 833.6" stroke-width="1" zvalue="1021"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="93@0" LinkObjectIDznd="98@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 833.44 883.57 L 833.44 833.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="kv10" d="M 833.42 809.23 L 833.42 749.38" stroke-width="1" zvalue="1025"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="98@1" LinkObjectIDznd="92@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 833.42 809.23 L 833.42 749.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="kv10" d="M 847.48 764.08 L 833.42 764.08" stroke-width="1" zvalue="1030"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="84@0" LinkObjectIDznd="91" MaxPinNum="2"/>
   </metadata>
  <path d="M 847.48 764.08 L 833.42 764.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="kv10" d="M 816.51 852.42 L 833.44 852.42" stroke-width="1" zvalue="1032"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="77@0" LinkObjectIDznd="96" MaxPinNum="2"/>
   </metadata>
  <path d="M 816.51 852.42 L 833.44 852.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="67">
   <path class="kv10" d="M 861.1 852.42 L 833.44 852.42" stroke-width="1" zvalue="1035"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="68@0" LinkObjectIDznd="96" MaxPinNum="2"/>
   </metadata>
  <path d="M 861.1 852.42 L 833.44 852.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="kv10" d="M 809.12 774.57 L 809.12 764.08 L 834 764.08" stroke-width="1" zvalue="1037"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="66@0" LinkObjectIDznd="80" MaxPinNum="2"/>
   </metadata>
  <path d="M 809.12 774.57 L 809.12 764.08 L 834 764.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="kv10" d="M 832.89 779.33 L 833.42 779.33" stroke-width="1" zvalue="1039"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="62@0" LinkObjectIDznd="91" MaxPinNum="2"/>
   </metadata>
  <path d="M 832.89 779.33 L 833.42 779.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="99">
   <path class="kv10" d="M 832.63 712.2 L 832.63 690.5" stroke-width="1" zvalue="1040"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92@1" LinkObjectIDznd="429@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 832.63 712.2 L 832.63 690.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="384">
   <path class="kv10" d="M 963.44 883.57 L 963.44 833.6" stroke-width="1" zvalue="1044"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="383@0" LinkObjectIDznd="385@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 963.44 883.57 L 963.44 833.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="381">
   <path class="kv10" d="M 963.42 809.23 L 963.42 749.38" stroke-width="1" zvalue="1048"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="385@1" LinkObjectIDznd="382@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 963.42 809.23 L 963.42 749.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="374">
   <path class="kv10" d="M 977.48 764.08 L 963.42 764.08" stroke-width="1" zvalue="1052"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="380@0" LinkObjectIDznd="381" MaxPinNum="2"/>
   </metadata>
  <path d="M 977.48 764.08 L 963.42 764.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="371">
   <path class="kv10" d="M 946.51 852.42 L 963.44 852.42" stroke-width="1" zvalue="1054"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="373@0" LinkObjectIDznd="384" MaxPinNum="2"/>
   </metadata>
  <path d="M 946.51 852.42 L 963.44 852.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="367">
   <path class="kv10" d="M 991.1 852.42 L 963.44 852.42" stroke-width="1" zvalue="1057"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="368@0" LinkObjectIDznd="384" MaxPinNum="2"/>
   </metadata>
  <path d="M 991.1 852.42 L 963.44 852.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="364">
   <path class="kv10" d="M 939.12 774.57 L 939.12 764.08 L 964 764.08" stroke-width="1" zvalue="1059"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="366@0" LinkObjectIDznd="374" MaxPinNum="2"/>
   </metadata>
  <path d="M 939.12 774.57 L 939.12 764.08 L 964 764.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="152">
   <path class="kv10" d="M 962.89 779.33 L 963.42 779.33" stroke-width="1" zvalue="1061"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="189@0" LinkObjectIDznd="381" MaxPinNum="2"/>
   </metadata>
  <path d="M 962.89 779.33 L 963.42 779.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="132">
   <path class="kv10" d="M 962.63 712.2 L 962.63 690.5" stroke-width="1" zvalue="1062"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="382@1" LinkObjectIDznd="429@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 962.63 712.2 L 962.63 690.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="504">
   <path class="kv10" d="M 1093.44 883.57 L 1093.44 833.6" stroke-width="1" zvalue="1066"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="503@0" LinkObjectIDznd="505@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1093.44 883.57 L 1093.44 833.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="500">
   <path class="kv10" d="M 1093.42 809.23 L 1093.42 749.38" stroke-width="1" zvalue="1070"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="505@1" LinkObjectIDznd="501@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1093.42 809.23 L 1093.42 749.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="498">
   <path class="kv10" d="M 1107.48 764.08 L 1093.42 764.08" stroke-width="1" zvalue="1074"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="499@0" LinkObjectIDznd="500" MaxPinNum="2"/>
   </metadata>
  <path d="M 1107.48 764.08 L 1093.42 764.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="494">
   <path class="kv10" d="M 1076.51 852.42 L 1093.44 852.42" stroke-width="1" zvalue="1076"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="497@0" LinkObjectIDznd="504" MaxPinNum="2"/>
   </metadata>
  <path d="M 1076.51 852.42 L 1093.44 852.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="491">
   <path class="kv10" d="M 1121.1 852.42 L 1093.44 852.42" stroke-width="1" zvalue="1079"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="493@0" LinkObjectIDznd="504" MaxPinNum="2"/>
   </metadata>
  <path d="M 1121.1 852.42 L 1093.44 852.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="473">
   <path class="kv10" d="M 1069.12 774.57 L 1069.12 764.08 L 1094 764.08" stroke-width="1" zvalue="1081"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="490@0" LinkObjectIDznd="498" MaxPinNum="2"/>
   </metadata>
  <path d="M 1069.12 774.57 L 1069.12 764.08 L 1094 764.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="451">
   <path class="kv10" d="M 1092.89 779.33 L 1093.42 779.33" stroke-width="1" zvalue="1083"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="472@0" LinkObjectIDznd="500" MaxPinNum="2"/>
   </metadata>
  <path d="M 1092.89 779.33 L 1093.42 779.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="433">
   <path class="kv10" d="M 1092.63 712.2 L 1092.63 690.5" stroke-width="1" zvalue="1084"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="501@1" LinkObjectIDznd="429@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1092.63 712.2 L 1092.63 690.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="513">
   <path class="kv10" d="M 1692.17 668.55 L 1692.17 691.75" stroke-width="1" zvalue="1090"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="514@1" LinkObjectIDznd="426@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1692.17 668.55 L 1692.17 691.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="512">
   <path class="kv10" d="M 1692.62 603.03 L 1692.62 622.94" stroke-width="1" zvalue="1091"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="515@0" LinkObjectIDznd="514@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1692.62 603.03 L 1692.62 622.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="537">
   <path class="kv10" d="M 1339.44 883.57 L 1339.44 833.6" stroke-width="1" zvalue="1095"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="536@0" LinkObjectIDznd="538@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1339.44 883.57 L 1339.44 833.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="534">
   <path class="kv10" d="M 1339.42 809.23 L 1339.42 749.38" stroke-width="1" zvalue="1099"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="538@1" LinkObjectIDznd="535@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1339.42 809.23 L 1339.42 749.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="532">
   <path class="kv10" d="M 1353.48 764.08 L 1339.42 764.08" stroke-width="1" zvalue="1103"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="533@0" LinkObjectIDznd="534" MaxPinNum="2"/>
   </metadata>
  <path d="M 1353.48 764.08 L 1339.42 764.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="527">
   <path class="kv10" d="M 1367.1 872.42 L 1339.44 872.42" stroke-width="1" zvalue="1108"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="529@0" LinkObjectIDznd="537" MaxPinNum="2"/>
   </metadata>
  <path d="M 1367.1 872.42 L 1339.44 872.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="525">
   <path class="kv10" d="M 1315.12 774.57 L 1315.12 764.08 L 1340 764.08" stroke-width="1" zvalue="1110"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="526@0" LinkObjectIDznd="532" MaxPinNum="2"/>
   </metadata>
  <path d="M 1315.12 774.57 L 1315.12 764.08 L 1340 764.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="523">
   <path class="kv10" d="M 1338.89 779.33 L 1339.42 779.33" stroke-width="1" zvalue="1112"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="524@0" LinkObjectIDznd="534" MaxPinNum="2"/>
   </metadata>
  <path d="M 1338.89 779.33 L 1339.42 779.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="522">
   <path class="kv10" d="M 1338.63 712.2 L 1338.63 691.75" stroke-width="1" zvalue="1113"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="535@1" LinkObjectIDznd="426@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1338.63 712.2 L 1338.63 691.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="582">
   <path class="kv10" d="M 1469.44 883.57 L 1469.44 833.6" stroke-width="1" zvalue="1139"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="581@0" LinkObjectIDznd="583@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1469.44 883.57 L 1469.44 833.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="579">
   <path class="kv10" d="M 1469.42 809.23 L 1469.42 749.38" stroke-width="1" zvalue="1143"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="583@1" LinkObjectIDznd="580@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1469.42 809.23 L 1469.42 749.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="577">
   <path class="kv10" d="M 1483.48 764.08 L 1469.42 764.08" stroke-width="1" zvalue="1147"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="578@0" LinkObjectIDznd="579" MaxPinNum="2"/>
   </metadata>
  <path d="M 1483.48 764.08 L 1469.42 764.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="573">
   <path class="kv10" d="M 1452.51 852.42 L 1469.44 852.42" stroke-width="1" zvalue="1149"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="575@0" LinkObjectIDznd="582" MaxPinNum="2"/>
   </metadata>
  <path d="M 1452.51 852.42 L 1469.44 852.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="571">
   <path class="kv10" d="M 1497.1 852.42 L 1469.44 852.42" stroke-width="1" zvalue="1152"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="572@0" LinkObjectIDznd="582" MaxPinNum="2"/>
   </metadata>
  <path d="M 1497.1 852.42 L 1469.44 852.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="569">
   <path class="kv10" d="M 1445.12 774.57 L 1445.12 764.08 L 1470 764.08" stroke-width="1" zvalue="1154"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="570@0" LinkObjectIDznd="577" MaxPinNum="2"/>
   </metadata>
  <path d="M 1445.12 774.57 L 1445.12 764.08 L 1470 764.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="567">
   <path class="kv10" d="M 1468.89 779.33 L 1469.42 779.33" stroke-width="1" zvalue="1156"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="568@0" LinkObjectIDznd="579" MaxPinNum="2"/>
   </metadata>
  <path d="M 1468.89 779.33 L 1469.42 779.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="566">
   <path class="kv10" d="M 1468.63 712.2 L 1468.63 691.75" stroke-width="1" zvalue="1157"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="580@1" LinkObjectIDznd="426@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1468.63 712.2 L 1468.63 691.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="603">
   <path class="kv10" d="M 1599.44 883.57 L 1599.44 833.6" stroke-width="1" zvalue="1161"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="602@0" LinkObjectIDznd="604@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1599.44 883.57 L 1599.44 833.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="600">
   <path class="kv10" d="M 1599.42 809.23 L 1599.42 749.38" stroke-width="1" zvalue="1165"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="604@1" LinkObjectIDznd="601@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1599.42 809.23 L 1599.42 749.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="598">
   <path class="kv10" d="M 1613.48 764.08 L 1599.42 764.08" stroke-width="1" zvalue="1169"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="599@0" LinkObjectIDznd="600" MaxPinNum="2"/>
   </metadata>
  <path d="M 1613.48 764.08 L 1599.42 764.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="596">
   <path class="kv10" d="M 1582.51 852.42 L 1599.44 852.42" stroke-width="1" zvalue="1171"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="597@0" LinkObjectIDznd="603" MaxPinNum="2"/>
   </metadata>
  <path d="M 1582.51 852.42 L 1599.44 852.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="594">
   <path class="kv10" d="M 1627.1 852.42 L 1599.44 852.42" stroke-width="1" zvalue="1174"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="595@0" LinkObjectIDznd="603" MaxPinNum="2"/>
   </metadata>
  <path d="M 1627.1 852.42 L 1599.44 852.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="592">
   <path class="kv10" d="M 1575.12 774.57 L 1575.12 764.08 L 1600 764.08" stroke-width="1" zvalue="1176"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="593@0" LinkObjectIDznd="598" MaxPinNum="2"/>
   </metadata>
  <path d="M 1575.12 774.57 L 1575.12 764.08 L 1600 764.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="590">
   <path class="kv10" d="M 1598.89 779.33 L 1599.42 779.33" stroke-width="1" zvalue="1178"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="591@0" LinkObjectIDznd="600" MaxPinNum="2"/>
   </metadata>
  <path d="M 1598.89 779.33 L 1599.42 779.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="589">
   <path class="kv10" d="M 1598.63 712.2 L 1598.63 691.75" stroke-width="1" zvalue="1179"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="601@1" LinkObjectIDznd="426@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1598.63 712.2 L 1598.63 691.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="624">
   <path class="kv10" d="M 1729.44 883.57 L 1729.44 833.6" stroke-width="1" zvalue="1183"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="623@0" LinkObjectIDznd="625@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1729.44 883.57 L 1729.44 833.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="621">
   <path class="kv10" d="M 1729.42 809.23 L 1729.42 749.38" stroke-width="1" zvalue="1187"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="625@1" LinkObjectIDznd="622@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1729.42 809.23 L 1729.42 749.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="619">
   <path class="kv10" d="M 1743.48 764.08 L 1729.42 764.08" stroke-width="1" zvalue="1191"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="620@0" LinkObjectIDznd="621" MaxPinNum="2"/>
   </metadata>
  <path d="M 1743.48 764.08 L 1729.42 764.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="617">
   <path class="kv10" d="M 1712.51 852.42 L 1729.44 852.42" stroke-width="1" zvalue="1193"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="618@0" LinkObjectIDznd="624" MaxPinNum="2"/>
   </metadata>
  <path d="M 1712.51 852.42 L 1729.44 852.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="615">
   <path class="kv10" d="M 1757.1 852.42 L 1729.44 852.42" stroke-width="1" zvalue="1196"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="616@0" LinkObjectIDznd="624" MaxPinNum="2"/>
   </metadata>
  <path d="M 1757.1 852.42 L 1729.44 852.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="613">
   <path class="kv10" d="M 1705.12 774.57 L 1705.12 764.08 L 1730 764.08" stroke-width="1" zvalue="1198"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="614@0" LinkObjectIDznd="619" MaxPinNum="2"/>
   </metadata>
  <path d="M 1705.12 774.57 L 1705.12 764.08 L 1730 764.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="611">
   <path class="kv10" d="M 1728.89 779.33 L 1729.42 779.33" stroke-width="1" zvalue="1200"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="612@0" LinkObjectIDznd="621" MaxPinNum="2"/>
   </metadata>
  <path d="M 1728.89 779.33 L 1729.42 779.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="610">
   <path class="kv10" d="M 1728.63 712.2 L 1728.63 691.75" stroke-width="1" zvalue="1201"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="622@1" LinkObjectIDznd="426@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1728.63 712.2 L 1728.63 691.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="626">
   <path class="kv10" d="M 1364.84 852.53 L 1339.44 852.53" stroke-width="1" zvalue="1202"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="531@0" LinkObjectIDznd="537" MaxPinNum="2"/>
   </metadata>
  <path d="M 1364.84 852.53 L 1339.44 852.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="632">
   <path class="kv10" d="M 1281.94 901.17 L 1281.94 906.35" stroke-width="1" zvalue="1207"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="631@1" LinkObjectIDznd="628@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1281.94 901.17 L 1281.94 906.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="634">
   <path class="kv10" d="M 1282.15 869.3 L 1282.15 861.56 L 1339.44 861.56" stroke-width="1" zvalue="1208"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="631@0" LinkObjectIDznd="537" MaxPinNum="2"/>
   </metadata>
  <path d="M 1282.15 869.3 L 1282.15 861.56 L 1339.44 861.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="636">
   <path class="kv10" d="M 1317.11 861.56 L 1317.11 861.56" stroke-width="1" zvalue="1211"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="635@0" LinkObjectIDznd="634" MaxPinNum="2"/>
   </metadata>
  <path d="M 1317.11 861.56 L 1317.11 861.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="549">
   <g id="5490">
    <use class="kv35" height="30" transform="rotate(0,810.063,530.194) scale(2.58333,2.6) translate(-477.49,-302.274)" width="24" x="779.0599999999999" xlink:href="#PowerTransformer2:可调不带中性点_0" y="491.19" zvalue="80"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874575773698" ObjectName="35"/>
    </metadata>
   </g>
   <g id="5491">
    <use class="kv10" height="30" transform="rotate(0,810.063,530.194) scale(2.58333,2.6) translate(-477.49,-302.274)" width="24" x="779.0599999999999" xlink:href="#PowerTransformer2:可调不带中性点_1" y="491.19" zvalue="80"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874575839234" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399527235586" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399527235586"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,810.063,530.194) scale(2.58333,2.6) translate(-477.49,-302.274)" width="24" x="779.0599999999999" y="491.19"/></g>
  <g id="667">
   <g id="6670">
    <use class="kv35" height="30" transform="rotate(0,1433.33,530.194) scale(2.58333,2.6) translate(-859.495,-302.274)" width="24" x="1402.33" xlink:href="#PowerTransformer2:可调不带中性点_0" y="491.19" zvalue="146"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874575642626" ObjectName="35"/>
    </metadata>
   </g>
   <g id="6671">
    <use class="kv10" height="30" transform="rotate(0,1433.33,530.194) scale(2.58333,2.6) translate(-859.495,-302.274)" width="24" x="1402.33" xlink:href="#PowerTransformer2:可调不带中性点_1" y="491.19" zvalue="146"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874575708162" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399527170050" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399527170050"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1433.33,530.194) scale(2.58333,2.6) translate(-859.495,-302.274)" width="24" x="1402.33" y="491.19"/></g>
 </g>
 <g id="BreakerClass">
  <g id="576">
   <use class="kv10" height="20" transform="rotate(270,1236.56,605.222) scale(2.72698,2.5322) translate(-774.472,-350.89)" width="10" x="1222.925170068027" xlink:href="#Breaker:母联小车开关_0" y="579.9002267573696" zvalue="100"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925135892483" ObjectName="10kV分段012断路器"/>
   <cge:TPSR_Ref TObjectID="6473925135892483"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1236.56,605.222) scale(2.72698,2.5322) translate(-774.472,-350.89)" width="10" x="1222.925170068027" y="579.9002267573696"/></g>
  <g id="630">
   <use class="kv35" height="20" transform="rotate(0,810.164,454.788) scale(1.5673,1.42482) translate(-290.41,-131.35)" width="10" x="802.3270073427254" xlink:href="#Breaker:开关_0" y="440.5402636805579" zvalue="129"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925135826947" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473925135826947"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,810.164,454.788) scale(1.5673,1.42482) translate(-290.41,-131.35)" width="10" x="802.3270073427254" y="440.5402636805579"/></g>
  <g id="666">
   <use class="kv35" height="20" transform="rotate(0,1433.91,454.331) scale(1.61761,1.47055) translate(-544.385,-140.673)" width="10" x="1425.823903622073" xlink:href="#Breaker:开关_0" y="439.6255296171045" zvalue="148"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925135761411" ObjectName="#2主变35kV侧302断路器"/>
   <cge:TPSR_Ref TObjectID="6473925135761411"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1433.91,454.331) scale(1.61761,1.47055) translate(-544.385,-140.673)" width="10" x="1425.823903622073" y="439.6255296171045"/></g>
  <g id="403">
   <use class="kv10" height="20" transform="rotate(0,810.063,643.62) scale(2.24069,-2.03699) translate(-442.336,-949.215)" width="10" x="798.8598412698416" xlink:href="#Breaker:小车断路器_0" y="623.25" zvalue="455"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925135958019" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473925135958019"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,810.063,643.62) scale(2.24069,-2.03699) translate(-442.336,-949.215)" width="10" x="798.8598412698416" y="623.25"/></g>
  <g id="409">
   <use class="kv10" height="20" transform="rotate(0,1433.81,647.12) scale(2.24069,-2.03699) translate(-787.713,-954.434)" width="10" x="1422.609841269842" xlink:href="#Breaker:小车断路器_0" y="626.75" zvalue="459"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925136023555" ObjectName="#2主变10kV侧002断路器"/>
   <cge:TPSR_Ref TObjectID="6473925136023555"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1433.81,647.12) scale(2.24069,-2.03699) translate(-787.713,-954.434)" width="10" x="1422.609841269842" y="626.75"/></g>
  <g id="444">
   <use class="kv35" height="20" transform="rotate(0,1037.72,257.898) scale(1.55118,1.41016) translate(-365.975,-70.9113)" width="10" x="1029.95987833038" xlink:href="#Breaker:开关_0" y="243.7967208894656" zvalue="491"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925136089091" ObjectName="35kV芒畹线321断路器"/>
   <cge:TPSR_Ref TObjectID="6473925136089091"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1037.72,257.898) scale(1.55118,1.41016) translate(-365.975,-70.9113)" width="10" x="1029.95987833038" y="243.7967208894656"/></g>
  <g id="63">
   <use class="kv10" height="20" transform="rotate(0,507.813,734.12) scale(2.24069,-2.03699) translate(-274.978,-1084.14)" width="10" x="496.6098412698415" xlink:href="#Breaker:小车断路器_0" y="713.75" zvalue="645"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925136154627" ObjectName="10kV1号电容器021断路器"/>
   <cge:TPSR_Ref TObjectID="6473925136154627"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,507.813,734.12) scale(2.24069,-2.03699) translate(-274.978,-1084.14)" width="10" x="496.6098412698415" y="713.75"/></g>
  <g id="31">
   <use class="kv10" height="20" transform="rotate(0,686.626,730.537) scale(2.24069,-2.03699) translate(-373.988,-1078.8)" width="10" x="675.4229371137008" xlink:href="#Breaker:小车断路器_0" y="710.1666666666667" zvalue="998"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925136220163" ObjectName="10kV城区一回线022断路器"/>
   <cge:TPSR_Ref TObjectID="6473925136220163"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,686.626,730.537) scale(2.24069,-2.03699) translate(-373.988,-1078.8)" width="10" x="675.4229371137008" y="710.1666666666667"/></g>
  <g id="92">
   <use class="kv10" height="20" transform="rotate(0,832.626,730.537) scale(2.24069,-2.03699) translate(-454.83,-1078.8)" width="10" x="821.4229371137008" xlink:href="#Breaker:小车断路器_0" y="710.1666666666667" zvalue="1024"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925136285699" ObjectName="10kV城区二回线023断路器"/>
   <cge:TPSR_Ref TObjectID="6473925136285699"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,832.626,730.537) scale(2.24069,-2.03699) translate(-454.83,-1078.8)" width="10" x="821.4229371137008" y="710.1666666666667"/></g>
  <g id="382">
   <use class="kv10" height="20" transform="rotate(0,962.626,730.537) scale(2.24069,-2.03699) translate(-526.812,-1078.8)" width="10" x="951.4229371137008" xlink:href="#Breaker:小车断路器_0" y="710.1666666666667" zvalue="1047"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925136351235" ObjectName="10kV备用线024断路器"/>
   <cge:TPSR_Ref TObjectID="6473925136351235"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,962.626,730.537) scale(2.24069,-2.03699) translate(-526.812,-1078.8)" width="10" x="951.4229371137008" y="710.1666666666667"/></g>
  <g id="501">
   <use class="kv10" height="20" transform="rotate(0,1092.63,730.537) scale(2.24069,-2.03699) translate(-598.794,-1078.8)" width="10" x="1081.422937113701" xlink:href="#Breaker:小车断路器_0" y="710.1666666666667" zvalue="1069"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925136416771" ObjectName="10kV畹两线025断路器"/>
   <cge:TPSR_Ref TObjectID="6473925136416771"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1092.63,730.537) scale(2.24069,-2.03699) translate(-598.794,-1078.8)" width="10" x="1081.422937113701" y="710.1666666666667"/></g>
  <g id="535">
   <use class="kv10" height="20" transform="rotate(0,1338.63,730.537) scale(2.24069,-2.03699) translate(-735.007,-1078.8)" width="10" x="1327.422937113701" xlink:href="#Breaker:小车断路器_0" y="710.1666666666667" zvalue="1098"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925136482307" ObjectName="10kV畹莫线026断路器"/>
   <cge:TPSR_Ref TObjectID="6473925136482307"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1338.63,730.537) scale(2.24069,-2.03699) translate(-735.007,-1078.8)" width="10" x="1327.422937113701" y="710.1666666666667"/></g>
  <g id="580">
   <use class="kv10" height="20" transform="rotate(0,1468.63,730.537) scale(2.24069,-2.03699) translate(-806.989,-1078.8)" width="10" x="1457.422937113701" xlink:href="#Breaker:小车断路器_0" y="710.1666666666667" zvalue="1142"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925136547843" ObjectName="10kV备用2线027断路器"/>
   <cge:TPSR_Ref TObjectID="6473925136547843"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1468.63,730.537) scale(2.24069,-2.03699) translate(-806.989,-1078.8)" width="10" x="1457.422937113701" y="710.1666666666667"/></g>
  <g id="601">
   <use class="kv10" height="20" transform="rotate(0,1598.63,730.537) scale(2.24069,-2.03699) translate(-878.971,-1078.8)" width="10" x="1587.422937113701" xlink:href="#Breaker:小车断路器_0" y="710.1666666666667" zvalue="1164"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925136613379" ObjectName="10kV城东线028断路器"/>
   <cge:TPSR_Ref TObjectID="6473925136613379"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1598.63,730.537) scale(2.24069,-2.03699) translate(-878.971,-1078.8)" width="10" x="1587.422937113701" y="710.1666666666667"/></g>
  <g id="622">
   <use class="kv10" height="20" transform="rotate(0,1728.63,730.537) scale(2.24069,-2.03699) translate(-950.954,-1078.8)" width="10" x="1717.422937113701" xlink:href="#Breaker:小车断路器_0" y="710.1666666666667" zvalue="1186"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925136678915" ObjectName="10kV岭畹线029断路器"/>
   <cge:TPSR_Ref TObjectID="6473925136678915"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1728.63,730.537) scale(2.24069,-2.03699) translate(-950.954,-1078.8)" width="10" x="1717.422937113701" y="710.1666666666667"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="413">
   <use class="kv35" height="30" transform="rotate(180,773.72,216.472) scale(2.05575,2.26481) translate(-381.515,-101.919)" width="30" x="742.884159447146" xlink:href="#Accessory:带熔断器35kVPT11_0" y="182.4999999999999" zvalue="462"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454124175363" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,773.72,216.472) scale(2.05575,2.26481) translate(-381.515,-101.919)" width="30" x="742.884159447146" y="182.4999999999999"/></g>
  <g id="469">
   <use class="kv35" height="30" transform="rotate(90,1124.95,159.044) scale(1.39167,-1.5512) translate(-310.729,-253.306)" width="30" x="1104.079236742629" xlink:href="#Accessory:PT象达_0" y="135.7759953961245" zvalue="513"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454126075907" ObjectName="35kV芒畹线线路PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1124.95,159.044) scale(1.39167,-1.5512) translate(-310.729,-253.306)" width="30" x="1104.079236742629" y="135.7759953961245"/></g>
  <g id="475">
   <use class="kv10" height="18" transform="rotate(0,600.397,585.444) scale(2.15916,-2.11111) translate(-311.895,-852.76)" width="18" x="580.9641954061907" xlink:href="#Accessory:PT1_0" y="566.4444444444445" zvalue="520"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454125486083" ObjectName="10kVⅠ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,600.397,585.444) scale(2.15916,-2.11111) translate(-311.895,-852.76)" width="18" x="580.9641954061907" y="566.4444444444445"/></g>
  <g id="154">
   <use class="kv35" height="26" transform="rotate(90,1000.25,159.686) scale(0.9375,1.25) translate(66.3083,-28.6873)" width="12" x="994.625" xlink:href="#Accessory:避雷器1_0" y="143.4363870200881" zvalue="763"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454126141443" ObjectName="35kV芒畹线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1000.25,159.686) scale(0.9375,1.25) translate(66.3083,-28.6873)" width="12" x="994.625" y="143.4363870200881"/></g>
  <g id="2">
   <use class="kv35" height="26" transform="rotate(270,802.25,261.883) scale(-1,1.25) translate(-1604.5,-49.1267)" width="12" x="796.25" xlink:href="#Accessory:避雷器1_0" y="245.6333333333332" zvalue="972"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454126206979" ObjectName="35kV母线电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,802.25,261.883) scale(-1,1.25) translate(-1604.5,-49.1267)" width="12" x="796.25" y="245.6333333333332"/></g>
  <g id="4">
   <use class="kv10" height="26" transform="rotate(270,842.25,612) scale(-1,1.25) translate(-1684.5,-119.15)" width="12" x="836.25" xlink:href="#Accessory:避雷器1_0" y="595.75" zvalue="975"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454126272515" ObjectName="#1主变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,842.25,612) scale(-1,1.25) translate(-1684.5,-119.15)" width="12" x="836.25" y="595.75"/></g>
  <g id="6">
   <use class="kv10" height="26" transform="rotate(270,1466.69,617.556) scale(-1,1.25) translate(-2933.39,-120.261)" width="12" x="1460.694444444445" xlink:href="#Accessory:避雷器1_0" y="601.3055555555555" zvalue="978"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454126338051" ObjectName="#2主变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1466.69,617.556) scale(-1,1.25) translate(-2933.39,-120.261)" width="12" x="1460.694444444445" y="601.3055555555555"/></g>
  <g id="8">
   <use class="kv10" height="22" transform="rotate(0,809.444,593.778) scale(1.11111,1.11111) translate(-80.2778,-58.1556)" width="12" x="802.7777777777778" xlink:href="#Accessory:传输线_0" y="581.5555555555555" zvalue="980"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454126403587" ObjectName="#1主变线缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,809.444,593.778) scale(1.11111,1.11111) translate(-80.2778,-58.1556)" width="12" x="802.7777777777778" y="581.5555555555555"/></g>
  <g id="13">
   <use class="kv10" height="22" transform="rotate(0,507.111,784.889) scale(1.11111,1.11111) translate(-50.0444,-77.2667)" width="12" x="500.4444444444443" xlink:href="#Accessory:传输线_0" y="772.6666666666667" zvalue="986"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454126469123" ObjectName="无功补偿装置线缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,507.111,784.889) scale(1.11111,1.11111) translate(-50.0444,-77.2667)" width="12" x="500.4444444444443" y="772.6666666666667"/></g>
  <g id="15">
   <use class="kv10" height="22" transform="rotate(0,1432.67,599.333) scale(1.11111,1.11111) translate(-142.6,-58.7111)" width="12" x="1426" xlink:href="#Accessory:传输线_0" y="587.1111111111111" zvalue="989"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454126534659" ObjectName="#2主变线缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1432.67,599.333) scale(1.11111,1.11111) translate(-142.6,-58.7111)" width="12" x="1426" y="587.1111111111111"/></g>
  <g id="24">
   <use class="kv10" height="26" transform="rotate(270,730.563,852.384) scale(-1,1.25) translate(-1461.13,-167.227)" width="12" x="724.5630958438593" xlink:href="#Accessory:避雷器1_0" y="836.1338333540858" zvalue="1008"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454126600195" ObjectName="10kV城区一回线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,730.563,852.384) scale(-1,1.25) translate(-1461.13,-167.227)" width="12" x="724.5630958438593" y="836.1338333540858"/></g>
  <g id="37">
   <use class="kv10" height="29" transform="rotate(0,663.125,795.167) scale(1.43678,1.43678) translate(-199.188,-235.397)" width="11" x="655.2222222222222" xlink:href="#Accessory:PT带保险_0" y="774.3333333333335" zvalue="1010"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454127058947" ObjectName="10kV城区一回线PT"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,663.125,795.167) scale(1.43678,1.43678) translate(-199.188,-235.397)" width="11" x="655.2222222222222" y="774.3333333333335"/></g>
  <g id="43">
   <use class="kv10" height="22" transform="rotate(0,686.889,790.444) scale(1.11111,1.11111) translate(-68.0222,-77.8222)" width="12" x="680.2222222222222" xlink:href="#Accessory:传输线_0" y="778.2222222222223" zvalue="1016"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454127124483" ObjectName="10kV城区一回线线缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,686.889,790.444) scale(1.11111,1.11111) translate(-68.0222,-77.8222)" width="12" x="680.2222222222222" y="778.2222222222223"/></g>
  <g id="68">
   <use class="kv10" height="26" transform="rotate(270,876.563,852.384) scale(-1,1.25) translate(-1753.13,-167.227)" width="12" x="870.5630958438593" xlink:href="#Accessory:避雷器1_0" y="836.1338333540858" zvalue="1034"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454127321091" ObjectName="10kV城区二回线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,876.563,852.384) scale(-1,1.25) translate(-1753.13,-167.227)" width="12" x="870.5630958438593" y="836.1338333540858"/></g>
  <g id="66">
   <use class="kv10" height="29" transform="rotate(0,809.125,795.167) scale(1.43678,1.43678) translate(-243.572,-235.397)" width="11" x="801.2222222222222" xlink:href="#Accessory:PT带保险_0" y="774.3333333333335" zvalue="1036"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454127255555" ObjectName="10kV城区二回线PT"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,809.125,795.167) scale(1.43678,1.43678) translate(-243.572,-235.397)" width="11" x="801.2222222222222" y="774.3333333333335"/></g>
  <g id="62">
   <use class="kv10" height="22" transform="rotate(0,832.889,790.444) scale(1.11111,1.11111) translate(-82.6222,-77.8222)" width="12" x="826.2222222222222" xlink:href="#Accessory:传输线_0" y="778.2222222222223" zvalue="1038"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454127190019" ObjectName="10kV城区二回线线缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,832.889,790.444) scale(1.11111,1.11111) translate(-82.6222,-77.8222)" width="12" x="826.2222222222222" y="778.2222222222223"/></g>
  <g id="368">
   <use class="kv10" height="26" transform="rotate(270,1006.56,852.384) scale(-1,1.25) translate(-2013.13,-167.227)" width="12" x="1000.563095843859" xlink:href="#Accessory:避雷器1_0" y="836.1338333540858" zvalue="1056"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454127910915" ObjectName="10kV备用线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1006.56,852.384) scale(-1,1.25) translate(-2013.13,-167.227)" width="12" x="1000.563095843859" y="836.1338333540858"/></g>
  <g id="366">
   <use class="kv10" height="29" transform="rotate(0,939.125,795.167) scale(1.43678,1.43678) translate(-283.092,-235.397)" width="11" x="931.2222222222222" xlink:href="#Accessory:PT带保险_0" y="774.3333333333335" zvalue="1058"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454127845379" ObjectName="10kV备用线PT"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,939.125,795.167) scale(1.43678,1.43678) translate(-283.092,-235.397)" width="11" x="931.2222222222222" y="774.3333333333335"/></g>
  <g id="189">
   <use class="kv10" height="22" transform="rotate(0,962.889,790.444) scale(1.11111,1.11111) translate(-95.6222,-77.8222)" width="12" x="956.2222222222222" xlink:href="#Accessory:传输线_0" y="778.2222222222223" zvalue="1060"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454127779843" ObjectName="10kV备用线线缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,962.889,790.444) scale(1.11111,1.11111) translate(-95.6222,-77.8222)" width="12" x="956.2222222222222" y="778.2222222222223"/></g>
  <g id="493">
   <use class="kv10" height="26" transform="rotate(270,1136.56,852.384) scale(-1,1.25) translate(-2273.13,-167.227)" width="12" x="1130.563095843859" xlink:href="#Accessory:避雷器1_0" y="836.1338333540858" zvalue="1078"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454128500739" ObjectName="10kV畹两线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1136.56,852.384) scale(-1,1.25) translate(-2273.13,-167.227)" width="12" x="1130.563095843859" y="836.1338333540858"/></g>
  <g id="490">
   <use class="kv10" height="29" transform="rotate(0,1069.12,795.167) scale(1.43678,1.43678) translate(-322.612,-235.397)" width="11" x="1061.222222222222" xlink:href="#Accessory:PT带保险_0" y="774.3333333333335" zvalue="1080"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454128435203" ObjectName="10kV畹两线PT"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1069.12,795.167) scale(1.43678,1.43678) translate(-322.612,-235.397)" width="11" x="1061.222222222222" y="774.3333333333335"/></g>
  <g id="472">
   <use class="kv10" height="22" transform="rotate(0,1092.89,790.444) scale(1.11111,1.11111) translate(-108.622,-77.8222)" width="12" x="1086.222222222222" xlink:href="#Accessory:传输线_0" y="778.2222222222223" zvalue="1082"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454128369667" ObjectName="10kV畹两线线缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1092.89,790.444) scale(1.11111,1.11111) translate(-108.622,-77.8222)" width="12" x="1086.222222222222" y="778.2222222222223"/></g>
  <g id="515">
   <use class="kv10" height="18" transform="rotate(0,1692.29,586.458) scale(2.15916,-2.11111) translate(-898.086,-854.254)" width="18" x="1672.859307600317" xlink:href="#Accessory:PT1_0" y="567.4583333333334" zvalue="1086"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454129025027" ObjectName="10kVⅡ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1692.29,586.458) scale(2.15916,-2.11111) translate(-898.086,-854.254)" width="18" x="1672.859307600317" y="567.4583333333334"/></g>
  <g id="529">
   <use class="kv10" height="26" transform="rotate(270,1382.56,872.384) scale(-1,1.25) translate(-2765.13,-171.227)" width="12" x="1376.563095843859" xlink:href="#Accessory:避雷器1_0" y="856.1338333540858" zvalue="1107"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454129221635" ObjectName="10kV畹莫线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1382.56,872.384) scale(-1,1.25) translate(-2765.13,-171.227)" width="12" x="1376.563095843859" y="856.1338333540858"/></g>
  <g id="526">
   <use class="kv10" height="29" transform="rotate(0,1315.12,795.167) scale(1.43678,1.43678) translate(-397.396,-235.397)" width="11" x="1307.222222222222" xlink:href="#Accessory:PT带保险_0" y="774.3333333333335" zvalue="1109"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454129156099" ObjectName="10kV畹莫线PT"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1315.12,795.167) scale(1.43678,1.43678) translate(-397.396,-235.397)" width="11" x="1307.222222222222" y="774.3333333333335"/></g>
  <g id="524">
   <use class="kv10" height="22" transform="rotate(0,1338.89,790.444) scale(1.11111,1.11111) translate(-133.222,-77.8222)" width="12" x="1332.222222222222" xlink:href="#Accessory:传输线_0" y="778.2222222222223" zvalue="1111"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454129090563" ObjectName="10kV畹莫线线缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1338.89,790.444) scale(1.11111,1.11111) translate(-133.222,-77.8222)" width="12" x="1332.222222222222" y="778.2222222222223"/></g>
  <g id="572">
   <use class="kv10" height="26" transform="rotate(270,1512.56,852.384) scale(-1,1.25) translate(-3025.13,-167.227)" width="12" x="1506.563095843859" xlink:href="#Accessory:避雷器1_0" y="836.1338333540858" zvalue="1151"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454129811459" ObjectName="10kV备用2线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1512.56,852.384) scale(-1,1.25) translate(-3025.13,-167.227)" width="12" x="1506.563095843859" y="836.1338333540858"/></g>
  <g id="570">
   <use class="kv10" height="29" transform="rotate(0,1445.12,795.167) scale(1.43678,1.43678) translate(-436.916,-235.397)" width="11" x="1437.222222222222" xlink:href="#Accessory:PT带保险_0" y="774.3333333333335" zvalue="1153"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454129745923" ObjectName="10kV备用2线PT"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1445.12,795.167) scale(1.43678,1.43678) translate(-436.916,-235.397)" width="11" x="1437.222222222222" y="774.3333333333335"/></g>
  <g id="568">
   <use class="kv10" height="22" transform="rotate(0,1468.89,790.444) scale(1.11111,1.11111) translate(-146.222,-77.8222)" width="12" x="1462.222222222222" xlink:href="#Accessory:传输线_0" y="778.2222222222223" zvalue="1155"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454129680387" ObjectName="10kV备用2线线缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1468.89,790.444) scale(1.11111,1.11111) translate(-146.222,-77.8222)" width="12" x="1462.222222222222" y="778.2222222222223"/></g>
  <g id="595">
   <use class="kv10" height="26" transform="rotate(270,1642.56,852.384) scale(-1,1.25) translate(-3285.13,-167.227)" width="12" x="1636.563095843859" xlink:href="#Accessory:避雷器1_0" y="836.1338333540858" zvalue="1173"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454130401283" ObjectName="10kV城东线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1642.56,852.384) scale(-1,1.25) translate(-3285.13,-167.227)" width="12" x="1636.563095843859" y="836.1338333540858"/></g>
  <g id="593">
   <use class="kv10" height="29" transform="rotate(0,1575.12,795.167) scale(1.43678,1.43678) translate(-476.436,-235.397)" width="11" x="1567.222222222222" xlink:href="#Accessory:PT带保险_0" y="774.3333333333335" zvalue="1175"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454130335747" ObjectName="10kV城东线PT"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1575.12,795.167) scale(1.43678,1.43678) translate(-476.436,-235.397)" width="11" x="1567.222222222222" y="774.3333333333335"/></g>
  <g id="591">
   <use class="kv10" height="22" transform="rotate(0,1598.89,790.444) scale(1.11111,1.11111) translate(-159.222,-77.8222)" width="12" x="1592.222222222222" xlink:href="#Accessory:传输线_0" y="778.2222222222223" zvalue="1177"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454130270211" ObjectName="10kV城东线线缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1598.89,790.444) scale(1.11111,1.11111) translate(-159.222,-77.8222)" width="12" x="1592.222222222222" y="778.2222222222223"/></g>
  <g id="616">
   <use class="kv10" height="26" transform="rotate(270,1772.56,852.384) scale(-1,1.25) translate(-3545.13,-167.227)" width="12" x="1766.563095843859" xlink:href="#Accessory:避雷器1_0" y="836.1338333540858" zvalue="1195"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454130991107" ObjectName="10kV岭畹线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1772.56,852.384) scale(-1,1.25) translate(-3545.13,-167.227)" width="12" x="1766.563095843859" y="836.1338333540858"/></g>
  <g id="614">
   <use class="kv10" height="29" transform="rotate(0,1705.12,795.167) scale(1.43678,1.43678) translate(-515.956,-235.397)" width="11" x="1697.222222222222" xlink:href="#Accessory:PT带保险_0" y="774.3333333333335" zvalue="1197"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454130925571" ObjectName="10kV岭畹线PT"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1705.12,795.167) scale(1.43678,1.43678) translate(-515.956,-235.397)" width="11" x="1697.222222222222" y="774.3333333333335"/></g>
  <g id="612">
   <use class="kv10" height="22" transform="rotate(0,1728.89,790.444) scale(1.11111,1.11111) translate(-172.222,-77.8222)" width="12" x="1722.222222222222" xlink:href="#Accessory:传输线_0" y="778.2222222222223" zvalue="1199"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454130860035" ObjectName="10kV岭畹线线缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1728.89,790.444) scale(1.11111,1.11111) translate(-172.222,-77.8222)" width="12" x="1722.222222222222" y="778.2222222222223"/></g>
  <g id="635">
   <use class="kv10" height="22" transform="rotate(90,1306,861.556) scale(1.11111,1.11111) translate(-129.933,-84.9333)" width="12" x="1299.333333333333" xlink:href="#Accessory:传输线_0" y="849.3333333333335" zvalue="1210"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454131580931" ObjectName="#2站用变线缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(90,1306,861.556) scale(1.11111,1.11111) translate(-129.933,-84.9333)" width="12" x="1299.333333333333" y="849.3333333333335"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="417">
   <use class="kv35" height="20" transform="rotate(90,748,316.972) scale(-1.11111,1.11111) translate(-1420.64,-30.5861)" width="10" x="742.4444444444445" xlink:href="#GroundDisconnector:地刀_0" y="305.8611111111111" zvalue="466"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454124306435" ObjectName="35kV母线电压互感器39010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454124306435"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,748,316.972) scale(-1.11111,1.11111) translate(-1420.64,-30.5861)" width="10" x="742.4444444444445" y="305.8611111111111"/></g>
  <g id="422">
   <use class="kv35" height="20" transform="rotate(90,748,261.972) scale(-1.11111,1.11111) translate(-1420.64,-25.0861)" width="10" x="742.4444444444445" xlink:href="#GroundDisconnector:地刀_0" y="250.8611111111111" zvalue="470"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454124437507" ObjectName="35kV母线电压互感器39017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454124437507"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,748,261.972) scale(-1.11111,1.11111) translate(-1420.64,-25.0861)" width="10" x="742.4444444444445" y="250.8611111111111"/></g>
  <g id="424">
   <use class="kv35" height="20" transform="rotate(90,781.5,419.472) scale(-1.11111,1.11111) translate(-1484.29,-40.8361)" width="10" x="775.9444444444445" xlink:href="#GroundDisconnector:地刀_0" y="408.3611111111111" zvalue="474"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454124568579" ObjectName="#1主变35kV侧30117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454124568579"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,781.5,419.472) scale(-1.11111,1.11111) translate(-1484.29,-40.8361)" width="10" x="775.9444444444445" y="408.3611111111111"/></g>
  <g id="430">
   <use class="kv35" height="20" transform="rotate(90,1404,413.613) scale(-1.11111,1.11111) translate(-2667.04,-40.2502)" width="10" x="1398.444444444444" xlink:href="#GroundDisconnector:地刀_0" y="402.5018867479586" zvalue="477"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454124699651" ObjectName="#2主变35kV侧30217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454124699651"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1404,413.613) scale(-1.11111,1.11111) translate(-2667.04,-40.2502)" width="10" x="1398.444444444444" y="402.5018867479586"/></g>
  <g id="460">
   <use class="kv35" height="20" transform="rotate(270,1064.11,287.722) scale(1.11111,1.11111) translate(-105.856,-27.6611)" width="10" x="1058.555555555556" xlink:href="#GroundDisconnector:地刀_0" y="276.6111111111111" zvalue="503"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454125158403" ObjectName="35kV芒畹线32117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454125158403"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1064.11,287.722) scale(1.11111,1.11111) translate(-105.856,-27.6611)" width="10" x="1058.555555555556" y="276.6111111111111"/></g>
  <g id="462">
   <use class="kv35" height="20" transform="rotate(270,1064.11,233.722) scale(1.11111,1.11111) translate(-105.856,-22.2611)" width="10" x="1058.555555555556" xlink:href="#GroundDisconnector:地刀_0" y="222.6111111111111" zvalue="507"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454125289475" ObjectName="35kV芒畹线32160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454125289475"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1064.11,233.722) scale(1.11111,1.11111) translate(-105.856,-22.2611)" width="10" x="1058.555555555556" y="222.6111111111111"/></g>
  <g id="466">
   <use class="kv35" height="20" transform="rotate(270,1065.36,186.722) scale(1.11111,1.11111) translate(-105.981,-17.5611)" width="10" x="1059.805555555556" xlink:href="#GroundDisconnector:地刀_0" y="175.6111111111111" zvalue="511"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454125420547" ObjectName="35kV芒畹线32167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454125420547"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1065.36,186.722) scale(1.11111,1.11111) translate(-105.981,-17.5611)" width="10" x="1059.805555555556" y="175.6111111111111"/></g>
  <g id="59">
   <use class="kv10" height="20" transform="rotate(90,479.25,765.472) scale(-1.11111,1.11111) translate(-910.019,-75.4361)" width="10" x="473.6944444444445" xlink:href="#GroundDisconnector:地刀_0" y="754.3611111111111" zvalue="651"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454125813763" ObjectName="10kV1号电容器02160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454125813763"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,479.25,765.472) scale(-1.11111,1.11111) translate(-910.019,-75.4361)" width="10" x="473.6944444444445" y="754.3611111111111"/></g>
  <g id="69">
   <use class="kv10" height="20" transform="rotate(90,480.111,839.556) scale(-1.11111,1.11111) translate(-911.656,-82.8444)" width="10" x="474.5555555555555" xlink:href="#GroundDisconnector:地刀_0" y="828.4444444444443" zvalue="669"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454125944835" ObjectName="10kV1号电容器02167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454125944835"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,480.111,839.556) scale(-1.11111,1.11111) translate(-911.656,-82.8444)" width="10" x="474.5555555555555" y="828.4444444444443"/></g>
  <g id="28">
   <use class="kv10" height="20" transform="rotate(270,712.313,764.139) scale(1.11111,1.11111) translate(-70.6758,-75.3028)" width="10" x="706.7575402883037" xlink:href="#GroundDisconnector:地刀_0" y="753.0277777777778" zvalue="1002"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454126862339" ObjectName="10kV城区一回线02260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454126862339"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,712.313,764.139) scale(1.11111,1.11111) translate(-70.6758,-75.3028)" width="10" x="706.7575402883037" y="753.0277777777778"/></g>
  <g id="26">
   <use class="kv10" height="20" transform="rotate(90,659.674,852.472) scale(-1.11111,1.11111) translate(-1252.83,-84.1361)" width="10" x="654.1186513994146" xlink:href="#GroundDisconnector:地刀_0" y="841.3611111111111" zvalue="1005"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454126731267" ObjectName="10kV城区一回线02267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454126731267"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,659.674,852.472) scale(-1.11111,1.11111) translate(-1252.83,-84.1361)" width="10" x="654.1186513994146" y="841.3611111111111"/></g>
  <g id="84">
   <use class="kv10" height="20" transform="rotate(270,858.313,764.139) scale(1.11111,1.11111) translate(-85.2758,-75.3028)" width="10" x="852.7575402883037" xlink:href="#GroundDisconnector:地刀_0" y="753.0277777777778" zvalue="1028"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454127583235" ObjectName="10kV城区二回线02360接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454127583235"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,858.313,764.139) scale(1.11111,1.11111) translate(-85.2758,-75.3028)" width="10" x="852.7575402883037" y="753.0277777777778"/></g>
  <g id="77">
   <use class="kv10" height="20" transform="rotate(90,805.674,852.472) scale(-1.11111,1.11111) translate(-1530.23,-84.1361)" width="10" x="800.1186513994146" xlink:href="#GroundDisconnector:地刀_0" y="841.3611111111111" zvalue="1031"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454127452163" ObjectName="10kV城区二回线02367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454127452163"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,805.674,852.472) scale(-1.11111,1.11111) translate(-1530.23,-84.1361)" width="10" x="800.1186513994146" y="841.3611111111111"/></g>
  <g id="380">
   <use class="kv10" height="20" transform="rotate(270,988.313,764.139) scale(1.11111,1.11111) translate(-98.2758,-75.3028)" width="10" x="982.7575402883037" xlink:href="#GroundDisconnector:地刀_0" y="753.0277777777778" zvalue="1050"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454128173059" ObjectName="10kV备用线02460接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454128173059"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,988.313,764.139) scale(1.11111,1.11111) translate(-98.2758,-75.3028)" width="10" x="982.7575402883037" y="753.0277777777778"/></g>
  <g id="373">
   <use class="kv10" height="20" transform="rotate(90,935.674,852.472) scale(-1.11111,1.11111) translate(-1777.23,-84.1361)" width="10" x="930.1186513994146" xlink:href="#GroundDisconnector:地刀_0" y="841.3611111111111" zvalue="1053"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454128041987" ObjectName="10kV备用线02467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454128041987"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,935.674,852.472) scale(-1.11111,1.11111) translate(-1777.23,-84.1361)" width="10" x="930.1186513994146" y="841.3611111111111"/></g>
  <g id="499">
   <use class="kv10" height="20" transform="rotate(270,1118.31,764.139) scale(1.11111,1.11111) translate(-111.276,-75.3028)" width="10" x="1112.757540288304" xlink:href="#GroundDisconnector:地刀_0" y="753.0277777777778" zvalue="1072"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454128762883" ObjectName="10kV畹两线02560接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454128762883"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1118.31,764.139) scale(1.11111,1.11111) translate(-111.276,-75.3028)" width="10" x="1112.757540288304" y="753.0277777777778"/></g>
  <g id="497">
   <use class="kv10" height="20" transform="rotate(90,1065.67,852.472) scale(-1.11111,1.11111) translate(-2024.23,-84.1361)" width="10" x="1060.118651399415" xlink:href="#GroundDisconnector:地刀_0" y="841.3611111111111" zvalue="1075"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454128631811" ObjectName="10kV畹两线02567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454128631811"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1065.67,852.472) scale(-1.11111,1.11111) translate(-2024.23,-84.1361)" width="10" x="1060.118651399415" y="841.3611111111111"/></g>
  <g id="533">
   <use class="kv10" height="20" transform="rotate(270,1364.31,764.139) scale(1.11111,1.11111) translate(-135.876,-75.3028)" width="10" x="1358.757540288304" xlink:href="#GroundDisconnector:地刀_0" y="753.0277777777778" zvalue="1101"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454129483779" ObjectName="10kV畹莫线02660接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454129483779"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1364.31,764.139) scale(1.11111,1.11111) translate(-135.876,-75.3028)" width="10" x="1358.757540288304" y="753.0277777777778"/></g>
  <g id="531">
   <use class="kv10" height="20" transform="rotate(90,1375.67,852.472) scale(1.11111,-1.11111) translate(-137.012,-1618.59)" width="10" x="1370.118651399415" xlink:href="#GroundDisconnector:地刀_0" y="841.3611111111111" zvalue="1104"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454129352707" ObjectName="10kV畹莫线02667接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454129352707"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1375.67,852.472) scale(1.11111,-1.11111) translate(-137.012,-1618.59)" width="10" x="1370.118651399415" y="841.3611111111111"/></g>
  <g id="578">
   <use class="kv10" height="20" transform="rotate(270,1494.31,764.139) scale(1.11111,1.11111) translate(-148.876,-75.3028)" width="10" x="1488.757540288304" xlink:href="#GroundDisconnector:地刀_0" y="753.0277777777778" zvalue="1145"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454130073603" ObjectName="10kV备用2线02760接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454130073603"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1494.31,764.139) scale(1.11111,1.11111) translate(-148.876,-75.3028)" width="10" x="1488.757540288304" y="753.0277777777778"/></g>
  <g id="575">
   <use class="kv10" height="20" transform="rotate(90,1441.67,852.472) scale(-1.11111,1.11111) translate(-2738.63,-84.1361)" width="10" x="1436.118651399415" xlink:href="#GroundDisconnector:地刀_0" y="841.3611111111111" zvalue="1148"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454129942531" ObjectName="10kV备用2线02767接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454129942531"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1441.67,852.472) scale(-1.11111,1.11111) translate(-2738.63,-84.1361)" width="10" x="1436.118651399415" y="841.3611111111111"/></g>
  <g id="599">
   <use class="kv10" height="20" transform="rotate(270,1624.31,764.139) scale(1.11111,1.11111) translate(-161.876,-75.3028)" width="10" x="1618.757540288304" xlink:href="#GroundDisconnector:地刀_0" y="753.0277777777778" zvalue="1167"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454130663427" ObjectName="10kV城东线02860接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454130663427"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1624.31,764.139) scale(1.11111,1.11111) translate(-161.876,-75.3028)" width="10" x="1618.757540288304" y="753.0277777777778"/></g>
  <g id="597">
   <use class="kv10" height="20" transform="rotate(90,1571.67,852.472) scale(-1.11111,1.11111) translate(-2985.63,-84.1361)" width="10" x="1566.118651399415" xlink:href="#GroundDisconnector:地刀_0" y="841.3611111111111" zvalue="1170"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454130532355" ObjectName="10kV城东线02867接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454130532355"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1571.67,852.472) scale(-1.11111,1.11111) translate(-2985.63,-84.1361)" width="10" x="1566.118651399415" y="841.3611111111111"/></g>
  <g id="620">
   <use class="kv10" height="20" transform="rotate(270,1754.31,764.139) scale(1.11111,1.11111) translate(-174.876,-75.3028)" width="10" x="1748.757540288304" xlink:href="#GroundDisconnector:地刀_0" y="753.0277777777778" zvalue="1189"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454131253251" ObjectName="10kV岭畹线02960接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454131253251"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1754.31,764.139) scale(1.11111,1.11111) translate(-174.876,-75.3028)" width="10" x="1748.757540288304" y="753.0277777777778"/></g>
  <g id="618">
   <use class="kv10" height="20" transform="rotate(90,1701.67,852.472) scale(-1.11111,1.11111) translate(-3232.63,-84.1361)" width="10" x="1696.118651399415" xlink:href="#GroundDisconnector:地刀_0" y="841.3611111111111" zvalue="1192"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454131122179" ObjectName="10kV岭畹线02967接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454131122179"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1701.67,852.472) scale(-1.11111,1.11111) translate(-3232.63,-84.1361)" width="10" x="1696.118651399415" y="841.3611111111111"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="434">
   <use class="kv35" height="30" transform="rotate(0,1435.38,238.906) scale(2.4375,-2.03125) translate(-832.128,-341.053)" width="20" x="1411" xlink:href="#EnergyConsumer:站用变无融断_0" y="208.4375" zvalue="482"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454124765187" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1435.38,238.906) scale(2.4375,-2.03125) translate(-832.128,-341.053)" width="20" x="1411" y="208.4375"/></g>
  <g id="32">
   <use class="kv10" height="30" transform="rotate(0,687.444,902.917) scale(1.79167,-1.43333) translate(-299.005,-1526.36)" width="12" x="676.6944444444446" xlink:href="#EnergyConsumer:负荷_0" y="881.4166666666667" zvalue="996"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454126927875" ObjectName="10kV城区一回线"/>
   <cge:TPSR_Ref TObjectID="6192454126927875"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,687.444,902.917) scale(1.79167,-1.43333) translate(-299.005,-1526.36)" width="12" x="676.6944444444446" y="881.4166666666667"/></g>
  <g id="93">
   <use class="kv10" height="30" transform="rotate(0,833.444,902.917) scale(1.79167,-1.43333) translate(-363.516,-1526.36)" width="12" x="822.6944444444446" xlink:href="#EnergyConsumer:负荷_0" y="881.4166666666667" zvalue="1022"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454127648771" ObjectName="10kV城区二回线"/>
   <cge:TPSR_Ref TObjectID="6192454127648771"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,833.444,902.917) scale(1.79167,-1.43333) translate(-363.516,-1526.36)" width="12" x="822.6944444444446" y="881.4166666666667"/></g>
  <g id="383">
   <use class="kv10" height="30" transform="rotate(0,963.444,902.917) scale(1.79167,-1.43333) translate(-420.958,-1526.36)" width="12" x="952.6944444444445" xlink:href="#EnergyConsumer:负荷_0" y="881.4166666666667" zvalue="1045"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454128238595" ObjectName="10kV备用线"/>
   <cge:TPSR_Ref TObjectID="6192454128238595"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,963.444,902.917) scale(1.79167,-1.43333) translate(-420.958,-1526.36)" width="12" x="952.6944444444445" y="881.4166666666667"/></g>
  <g id="503">
   <use class="kv10" height="30" transform="rotate(0,1093.44,902.917) scale(1.79167,-1.43333) translate(-478.4,-1526.36)" width="12" x="1082.694444444445" xlink:href="#EnergyConsumer:负荷_0" y="881.4166666666667" zvalue="1067"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454128828419" ObjectName="10kV畹两线"/>
   <cge:TPSR_Ref TObjectID="6192454128828419"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1093.44,902.917) scale(1.79167,-1.43333) translate(-478.4,-1526.36)" width="12" x="1082.694444444445" y="881.4166666666667"/></g>
  <g id="536">
   <use class="kv10" height="30" transform="rotate(0,1339.44,902.917) scale(1.79167,-1.43333) translate(-587.098,-1526.36)" width="12" x="1328.694444444445" xlink:href="#EnergyConsumer:负荷_0" y="881.4166666666667" zvalue="1096"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454129549315" ObjectName="10kV畹莫线"/>
   <cge:TPSR_Ref TObjectID="6192454129549315"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1339.44,902.917) scale(1.79167,-1.43333) translate(-587.098,-1526.36)" width="12" x="1328.694444444445" y="881.4166666666667"/></g>
  <g id="581">
   <use class="kv10" height="30" transform="rotate(0,1469.44,902.917) scale(1.79167,-1.43333) translate(-644.539,-1526.36)" width="12" x="1458.694444444444" xlink:href="#EnergyConsumer:负荷_0" y="881.4166666666667" zvalue="1140"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454130139139" ObjectName="10kV备用2线"/>
   <cge:TPSR_Ref TObjectID="6192454130139139"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1469.44,902.917) scale(1.79167,-1.43333) translate(-644.539,-1526.36)" width="12" x="1458.694444444444" y="881.4166666666667"/></g>
  <g id="602">
   <use class="kv10" height="30" transform="rotate(0,1599.44,902.917) scale(1.79167,-1.43333) translate(-701.981,-1526.36)" width="12" x="1588.694444444444" xlink:href="#EnergyConsumer:负荷_0" y="881.4166666666667" zvalue="1162"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454130728963" ObjectName="10kV城东线"/>
   <cge:TPSR_Ref TObjectID="6192454130728963"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1599.44,902.917) scale(1.79167,-1.43333) translate(-701.981,-1526.36)" width="12" x="1588.694444444444" y="881.4166666666667"/></g>
  <g id="623">
   <use class="kv10" height="30" transform="rotate(0,1729.44,902.917) scale(1.79167,-1.43333) translate(-759.423,-1526.36)" width="12" x="1718.694444444444" xlink:href="#EnergyConsumer:负荷_0" y="881.4166666666667" zvalue="1184"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454131318787" ObjectName="10kV岭畹线"/>
   <cge:TPSR_Ref TObjectID="6192454131318787"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1729.44,902.917) scale(1.79167,-1.43333) translate(-759.423,-1526.36)" width="12" x="1718.694444444444" y="881.4166666666667"/></g>
  <g id="628">
   <use class="kv10" height="30" transform="rotate(0,1280.44,928.222) scale(1.50794,1.51852) translate(-424.196,-309.176)" width="28" x="1259.333333333333" xlink:href="#EnergyConsumer:站用变DY接地_0" y="905.4444444444446" zvalue="1204"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454131449859" ObjectName="#2站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1280.44,928.222) scale(1.50794,1.51852) translate(-424.196,-309.176)" width="28" x="1259.333333333333" y="905.4444444444446"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="441">
   <use class="kv35" height="30" transform="rotate(0,1037.69,131.458) scale(4.08692,0.852778) translate(-772.978,20.4864)" width="7" x="1023.382018458804" xlink:href="#ACLineSegment:线路_0" y="118.6666666666666" zvalue="488"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249323405316" ObjectName="35kV芒畹线"/>
   <cge:TPSR_Ref TObjectID="8444249323405316_5066549681061889"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1037.69,131.458) scale(4.08692,0.852778) translate(-772.978,20.4864)" width="7" x="1023.382018458804" y="118.6666666666666"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="55">
   <use class="kv10" height="40" transform="rotate(0,507.549,901.264) scale(1.90104,1.97014) translate(-229.752,-424.399)" width="24" x="484.7361111111111" xlink:href="#Compensator:西郊变电容_0" y="861.8611111111112" zvalue="657"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454125617155" ObjectName="10kV1号电容器"/>
   <cge:TPSR_Ref TObjectID="6192454125617155"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,507.549,901.264) scale(1.90104,1.97014) translate(-229.752,-424.399)" width="24" x="484.7361111111111" y="861.8611111111112"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="72">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="72" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1037.69,16.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1037.88" xml:space="preserve" y="21.41" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134267760642" ObjectName="P"/>
   </metadata>
  </g>
  <g id="73">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="73" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1037.69,39.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1037.88" xml:space="preserve" y="44.41" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134267826178" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="74">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="74" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1037.69,62.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1037.88" xml:space="preserve" y="67.41" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134267891714" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="78">
   <text Format="f4.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="78" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,509.049,998.861) scale(1,1) translate(0,0)" writing-mode="lr" x="509.24" xml:space="preserve" y="1003.91" zvalue="1">Q:dd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134269333506" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="85">
   <text Format="f4.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="85" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,508.049,1023.86) scale(1,1) translate(0,0)" writing-mode="lr" x="508.24" xml:space="preserve" y="1028.91" zvalue="1">Ia:dd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134269399042" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="89">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="89" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1435.38,115.938) scale(1,1) translate(0,0)" writing-mode="lr" x="1435.57" xml:space="preserve" y="120.85" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134266843138" ObjectName="P"/>
   </metadata>
  </g>
  <g id="95">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="95" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1435.38,138.938) scale(1,1) translate(0,0)" writing-mode="lr" x="1435.57" xml:space="preserve" y="143.85" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134266908674" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="102">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="102" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1435.38,161.938) scale(1,1) translate(0,0)" writing-mode="lr" x="1435.57" xml:space="preserve" y="166.85" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134266974210" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="133">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="133" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,888.563,378.694) scale(1,1) translate(0,0)" writing-mode="lr" x="888.76" xml:space="preserve" y="383.6" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134262190082" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="134">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="134" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,888.563,405.694) scale(1,1) translate(0,-2.63252e-13)" writing-mode="lr" x="888.76" xml:space="preserve" y="410.6" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134262255618" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="135">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="135" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,888.563,432.694) scale(1,1) translate(0,0)" writing-mode="lr" x="888.76" xml:space="preserve" y="437.6" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134262452226" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="136">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="136" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,903.563,602.694) scale(1,1) translate(0,0)" writing-mode="lr" x="903.76" xml:space="preserve" y="607.6" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134262321154" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="137">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="137" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,903.563,629.694) scale(1,1) translate(0,0)" writing-mode="lr" x="903.76" xml:space="preserve" y="634.6" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134262386690" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="138">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="138" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,903.563,656.694) scale(1,1) translate(0,0)" writing-mode="lr" x="903.76" xml:space="preserve" y="661.6" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134262779906" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="141">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="141" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1509.83,388.694) scale(1,1) translate(0,0)" writing-mode="lr" x="1510.03" xml:space="preserve" y="393.6" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134258716674" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="142">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="142" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1509.83,415.694) scale(1,1) translate(0,0)" writing-mode="lr" x="1510.03" xml:space="preserve" y="420.6" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134258782210" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="143">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="143" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1509.83,442.694) scale(1,1) translate(0,0)" writing-mode="lr" x="1510.03" xml:space="preserve" y="447.6" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134258978818" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="144">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="144" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1526.83,599.694) scale(1,1) translate(0,0)" writing-mode="lr" x="1527.03" xml:space="preserve" y="604.6" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134258847746" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="145">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="145" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1526.83,626.694) scale(1,1) translate(0,0)" writing-mode="lr" x="1527.03" xml:space="preserve" y="631.6" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134258913282" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="146">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="146" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1526.83,653.694) scale(1,1) translate(0,-4.28454e-13)" writing-mode="lr" x="1527.03" xml:space="preserve" y="658.6" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134259306498" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="234">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="234" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,161.21,529.107) scale(1,1) translate(1.50823e-14,0)" writing-mode="lr" x="161.32" xml:space="preserve" y="533.88" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134264745986" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="233">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="233" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,162.21,551.107) scale(1,1) translate(1.51933e-14,1.20816e-13)" writing-mode="lr" x="162.32" xml:space="preserve" y="555.88" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134264811522" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="232">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="232" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,161.21,575.107) scale(1,1) translate(1.50823e-14,-1.26145e-13)" writing-mode="lr" x="161.32" xml:space="preserve" y="579.88" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134264877058" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="231">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="231" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,161.21,503.607) scale(1,1) translate(1.50823e-14,5.51345e-14)" writing-mode="lr" x="161.32" xml:space="preserve" y="508.38" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134265008130" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="230">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="230" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,165.571,210.968) scale(1,1) translate(0,0)" writing-mode="lr" x="165.72" xml:space="preserve" y="217.24" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134265139202" ObjectName="F"/>
   </metadata>
  </g>
  <g id="229">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="229" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,280.613,529.107) scale(1,1) translate(2.83387e-14,0)" writing-mode="lr" x="280.72" xml:space="preserve" y="533.88" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134264221698" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="228">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="228" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,281.613,551.107) scale(1,1) translate(2.84497e-14,1.20816e-13)" writing-mode="lr" x="281.72" xml:space="preserve" y="555.88" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134264287234" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="227">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="227" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,280.613,575.107) scale(1,1) translate(2.83387e-14,-1.26145e-13)" writing-mode="lr" x="280.72" xml:space="preserve" y="579.88" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134264352770" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="226">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="226" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,280.613,503.607) scale(1,1) translate(2.83387e-14,5.51345e-14)" writing-mode="lr" x="280.72" xml:space="preserve" y="508.38" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134264483842" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="225">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="225" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,165.571,235.857) scale(1,1) translate(0,0)" writing-mode="lr" x="165.72" xml:space="preserve" y="242.13" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134264614914" ObjectName="F"/>
   </metadata>
  </g>
  <g id="224">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="224" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,338.571,529.107) scale(1,1) translate(3.47733e-14,0)" writing-mode="lr" x="338.68" xml:space="preserve" y="533.88" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134265270274" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="223">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="223" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,339.571,551.107) scale(1,1) translate(3.48844e-14,-1.20816e-13)" writing-mode="lr" x="339.68" xml:space="preserve" y="555.88" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134265335810" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="222">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="222" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,338.571,575.107) scale(1,1) translate(3.47733e-14,-1.26145e-13)" writing-mode="lr" x="338.68" xml:space="preserve" y="579.88" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134265401346" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="221">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="221" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,342.571,235.107) scale(1,1) translate(0,0)" writing-mode="lr" x="342.72" xml:space="preserve" y="241.38" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134265663490" ObjectName="F"/>
   </metadata>
  </g>
  <g id="220">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="220" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,338.571,503.607) scale(1,1) translate(3.47733e-14,-1.10269e-13)" writing-mode="lr" x="338.68" xml:space="preserve" y="508.38" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134265532418" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="219">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="219" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,165.571,258.052) scale(1,1) translate(0,0)" writing-mode="lr" x="165.72" xml:space="preserve" y="264.33" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134262648834" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="218">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="218" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,165.571,281.052) scale(1,1) translate(0,3.95536e-13)" writing-mode="lr" x="165.72" xml:space="preserve" y="287.33" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134262714370" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="217">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="217" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,342.571,257.052) scale(1,1) translate(0,0)" writing-mode="lr" x="342.72" xml:space="preserve" y="263.33" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134259175426" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="216">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="216" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,342.571,280.052) scale(1,1) translate(0,2.12204e-13)" writing-mode="lr" x="342.72" xml:space="preserve" y="286.33" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134259240962" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="213">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="213" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,161.21,602.107) scale(1,1) translate(1.50823e-14,4.62491e-13)" writing-mode="lr" x="161.32" xml:space="preserve" y="606.88" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134265204738" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="212">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="212" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,280.613,602.107) scale(1,1) translate(2.83387e-14,4.62491e-13)" writing-mode="lr" x="280.72" xml:space="preserve" y="606.88" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134264680450" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="211">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="211" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,338.571,602.107) scale(1,1) translate(3.47733e-14,4.62491e-13)" writing-mode="lr" x="338.68" xml:space="preserve" y="606.88" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134265729026" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="210">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="210" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,165.571,186.357) scale(1,1) translate(0,0)" writing-mode="lr" x="165.72" xml:space="preserve" y="192.69" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134282702850" ObjectName=""/>
   </metadata>
  </g>
  <g id="209">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="209" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,341.571,186.357) scale(1,1) translate(0,0)" writing-mode="lr" x="341.72" xml:space="preserve" y="192.71" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134282768386" ObjectName=""/>
   </metadata>
  </g>
  <g id="304">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="304" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,687.444,972.917) scale(1,1) translate(0,0)" writing-mode="lr" x="687.64" xml:space="preserve" y="977.83" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134271102978" ObjectName="P"/>
   </metadata>
  </g>
  <g id="306">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="306" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,833.444,972.917) scale(1,1) translate(0,0)" writing-mode="lr" x="833.64" xml:space="preserve" y="977.83" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134272544770" ObjectName="P"/>
   </metadata>
  </g>
  <g id="307">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="307" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,963.444,974.345) scale(1,1) translate(0,0)" writing-mode="lr" x="963.64" xml:space="preserve" y="979.26" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134273986562" ObjectName="P"/>
   </metadata>
  </g>
  <g id="308">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="308" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1093.44,974.345) scale(1,1) translate(0,0)" writing-mode="lr" x="1093.64" xml:space="preserve" y="979.26" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134275428354" ObjectName="P"/>
   </metadata>
  </g>
  <g id="310">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="310" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1339.44,974.345) scale(1,1) translate(0,0)" writing-mode="lr" x="1339.64" xml:space="preserve" y="979.26" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134276870146" ObjectName="P"/>
   </metadata>
  </g>
  <g id="312">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="312" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1469.44,974.345) scale(1,1) translate(0,0)" writing-mode="lr" x="1469.64" xml:space="preserve" y="979.26" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134278311938" ObjectName="P"/>
   </metadata>
  </g>
  <g id="313">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="313" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1599.44,974.345) scale(1,1) translate(0,0)" writing-mode="lr" x="1599.64" xml:space="preserve" y="979.26" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134279753730" ObjectName="P"/>
   </metadata>
  </g>
  <g id="314">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="314" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1729.44,974.345) scale(1,1) translate(0,0)" writing-mode="lr" x="1729.64" xml:space="preserve" y="979.26" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134281195522" ObjectName="P"/>
   </metadata>
  </g>
  <g id="315">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="315" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,687.444,995.917) scale(1,1) translate(0,0)" writing-mode="lr" x="687.64" xml:space="preserve" y="1000.83" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134271168514" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="316">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="316" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,833.444,997.345) scale(1,1) translate(0,0)" writing-mode="lr" x="833.64" xml:space="preserve" y="1002.26" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134272610306" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="317">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="317" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,963.444,997.345) scale(1,1) translate(0,0)" writing-mode="lr" x="963.64" xml:space="preserve" y="1002.26" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134274052098" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="318">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="318" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1093.44,997.345) scale(1,1) translate(0,0)" writing-mode="lr" x="1093.64" xml:space="preserve" y="1002.26" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134275493890" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="335">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="335" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1339.44,997.345) scale(1,1) translate(0,0)" writing-mode="lr" x="1339.64" xml:space="preserve" y="1002.26" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134276935682" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="336">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="336" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1469.44,997.345) scale(1,1) translate(0,0)" writing-mode="lr" x="1469.64" xml:space="preserve" y="1002.26" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134278377474" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="337">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="337" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1599.44,997.345) scale(1,1) translate(0,0)" writing-mode="lr" x="1599.64" xml:space="preserve" y="1002.26" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134279819266" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="338">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="338" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1729.44,997.345) scale(1,1) translate(0,0)" writing-mode="lr" x="1729.64" xml:space="preserve" y="1002.26" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134281261058" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="339">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="339" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,687.444,1018.92) scale(1,1) translate(0,0)" writing-mode="lr" x="687.64" xml:space="preserve" y="1023.83" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134271234050" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="340">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="340" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,833.444,1020.35) scale(1,1) translate(0,0)" writing-mode="lr" x="833.64" xml:space="preserve" y="1025.26" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134272675842" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="341">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="341" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,963.444,1020.35) scale(1,1) translate(0,0)" writing-mode="lr" x="963.64" xml:space="preserve" y="1025.26" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134274117634" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="342">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="342" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1093.44,1020.35) scale(1,1) translate(0,0)" writing-mode="lr" x="1093.64" xml:space="preserve" y="1025.26" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134275559426" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="343">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="343" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1339.44,1020.35) scale(1,1) translate(0,0)" writing-mode="lr" x="1339.64" xml:space="preserve" y="1025.26" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134277001218" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="344">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="344" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1469.44,1020.35) scale(1,1) translate(0,0)" writing-mode="lr" x="1469.64" xml:space="preserve" y="1025.26" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134278443010" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="345">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="345" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1599.44,1020.35) scale(1,1) translate(0,0)" writing-mode="lr" x="1599.64" xml:space="preserve" y="1025.26" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134279884802" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="346">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="346" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1729.44,1020.35) scale(1,1) translate(0,0)" writing-mode="lr" x="1729.64" xml:space="preserve" y="1025.26" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134281326594" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="117">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="117" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,771.361,146) scale(1,1) translate(8.28226e-14,1.08025e-13)" writing-mode="lr" x="771.47" xml:space="preserve" y="150.77" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134265008130" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="127">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="127" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,600.361,526) scale(1,1) translate(6.38378e-14,0)" writing-mode="lr" x="600.47" xml:space="preserve" y="530.77" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134264483842" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="130">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="130" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1690.36,528) scale(1,1) translate(1.84852e-13,0)" writing-mode="lr" x="1690.47" xml:space="preserve" y="532.77" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134265532418" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="StateClass">
  <g id="215">
   <use height="30" transform="rotate(0,372.196,328.357) scale(0.708333,0.665547) translate(148.882,159.991)" width="30" x="361.57" xlink:href="#State:红绿圆(方形)_0" y="318.37" zvalue="1337"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374924185601" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,372.196,328.357) scale(0.708333,0.665547) translate(148.882,159.991)" width="30" x="361.57" y="318.37"/></g>
  <g id="214">
   <use height="30" transform="rotate(0,276.571,328.357) scale(0.708333,0.665547) translate(109.507,159.991)" width="30" x="265.95" xlink:href="#State:红绿圆(方形)_0" y="318.37" zvalue="1338"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562957105430535" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,276.571,328.357) scale(0.708333,0.665547) translate(109.507,159.991)" width="30" x="265.95" y="318.37"/></g>
 </g>
</svg>