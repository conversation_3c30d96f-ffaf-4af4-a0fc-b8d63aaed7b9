<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549587542018" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:手车开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:手车开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="Breaker:手车开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.45181617405996" x2="1.714850492606708" y1="5.276370517142858" y2="14.05696281619048"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.583333333333333" x2="8.583333333333334" y1="5.166666666666667" y2="14.16666666666667"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="12.0194189818494"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="5.416666666666668" y2="12.02321291373541"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.92126160277786" x2="6.666666666666666" y1="12.10207526123773" y2="18.50000000000001"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.18646053143751" x2="24.5" y1="12.18904818695178" y2="19.00000000000001"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 7.33333 43.5833 L 23 43.5833 L 15 31.5 z" fill-opacity="0" stroke="rgb(122,122,122)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(122,122,122)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="1" y1="23" y2="13"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="6" y1="23" y2="11"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="0" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV勐板河二级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="269.25" x="41" xlink:href="logo.png" y="46.67"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,175.625,76.6667) scale(1,1) translate(0,0)" writing-mode="lr" x="175.63" xml:space="preserve" y="80.17" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,177.333,76.357) scale(1,1) translate(6.55032e-15,0)" writing-mode="lr" x="177.33" xml:space="preserve" y="85.36" zvalue="3">10kV勐板河二级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="8" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,77.4375,320) scale(1,1) translate(0,0)" width="72.88" x="41" y="308" zvalue="100"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.4375,320) scale(1,1) translate(0,0)" writing-mode="lr" x="77.44" xml:space="preserve" y="324.5" zvalue="100">信号一览</text>
  <line fill="none" id="32" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="375.0000000000001" x2="375.0000000000001" y1="14.66666666666674" y2="1044.666666666667" zvalue="4"/>
  <line fill="none" id="30" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.410605131648481e-13" x2="367.9999999999999" y1="150.5371592807491" y2="150.5371592807491" zvalue="6"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="183.0000000000001" y1="162.6666666666668" y2="162.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="183.0000000000001" y1="188.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="2.000000000000114" y1="162.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="162.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="364.0000000000001" y1="162.6666666666668" y2="162.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="364.0000000000001" y1="188.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="162.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.0000000000001" x2="364.0000000000001" y1="162.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="183.0000000000001" y1="188.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="183.0000000000001" y1="212.9166666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="2.000000000000114" y1="188.6666666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="188.6666666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="364.0000000000001" y1="188.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="364.0000000000001" y1="212.9166666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="188.6666666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.0000000000001" x2="364.0000000000001" y1="188.6666666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="183.0000000000001" y1="212.9166666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="183.0000000000001" y1="235.6666666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="2.000000000000114" y1="212.9166666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="212.9166666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="364.0000000000001" y1="212.9166666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="364.0000000000001" y1="235.6666666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="212.9166666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.0000000000001" x2="364.0000000000001" y1="212.9166666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="183.0000000000001" y1="235.6666666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="183.0000000000001" y1="258.4166666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="2.000000000000114" y1="235.6666666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="235.6666666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="364.0000000000001" y1="235.6666666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="364.0000000000001" y1="258.4166666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="235.6666666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.0000000000001" x2="364.0000000000001" y1="235.6666666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="183.0000000000001" y1="258.4166666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="183.0000000000001" y1="281.1666666666668" y2="281.1666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="2.000000000000114" y1="258.4166666666668" y2="281.1666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="258.4166666666668" y2="281.1666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="364.0000000000001" y1="258.4166666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="364.0000000000001" y1="281.1666666666668" y2="281.1666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="258.4166666666668" y2="281.1666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.0000000000001" x2="364.0000000000001" y1="258.4166666666668" y2="281.1666666666668"/>
  <line fill="none" id="28" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.410605131648481e-13" x2="367.9999999999999" y1="620.5371592807492" y2="620.5371592807492" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1.000000000000114" x2="91.00000000000011" y1="935.666666666667" y2="935.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1.000000000000114" x2="91.00000000000011" y1="974.829966666667" y2="974.829966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1.000000000000114" x2="1.000000000000114" y1="935.666666666667" y2="974.829966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.00000000000011" x2="91.00000000000011" y1="935.666666666667" y2="974.829966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.00000000000011" x2="361.0000000000001" y1="935.666666666667" y2="935.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.00000000000011" x2="361.0000000000001" y1="974.829966666667" y2="974.829966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.00000000000011" x2="91.00000000000011" y1="935.666666666667" y2="974.829966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361.0000000000001" x2="361.0000000000001" y1="935.666666666667" y2="974.829966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1.000000000000114" x2="91.00000000000011" y1="974.829936666667" y2="974.829936666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1.000000000000114" x2="91.00000000000011" y1="1002.748336666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1.000000000000114" x2="1.000000000000114" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.00000000000011" x2="91.00000000000011" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.00000000000011" x2="181.0000000000001" y1="974.829936666667" y2="974.829936666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.00000000000011" x2="181.0000000000001" y1="1002.748336666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.00000000000011" x2="91.00000000000011" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="181.0000000000001" x2="181.0000000000001" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="181.0000000000002" x2="271.0000000000002" y1="974.829936666667" y2="974.829936666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="181.0000000000002" x2="271.0000000000002" y1="1002.748336666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="181.0000000000002" x2="181.0000000000002" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271.0000000000002" x2="271.0000000000002" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271.0000000000001" x2="361.0000000000001" y1="974.829936666667" y2="974.829936666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271.0000000000001" x2="361.0000000000001" y1="1002.748336666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271.0000000000001" x2="271.0000000000001" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361.0000000000001" x2="361.0000000000001" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1.000000000000114" x2="91.00000000000011" y1="1002.748266666667" y2="1002.748266666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1.000000000000114" x2="91.00000000000011" y1="1030.666666666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1.000000000000114" x2="1.000000000000114" y1="1002.748266666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.00000000000011" x2="91.00000000000011" y1="1002.748266666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.00000000000011" x2="181.0000000000001" y1="1002.748266666667" y2="1002.748266666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.00000000000011" x2="181.0000000000001" y1="1030.666666666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.00000000000011" x2="91.00000000000011" y1="1002.748266666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="181.0000000000001" x2="181.0000000000001" y1="1002.748266666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="181.0000000000002" x2="271.0000000000002" y1="1002.748266666667" y2="1002.748266666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="181.0000000000002" x2="271.0000000000002" y1="1030.666666666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="181.0000000000002" x2="181.0000000000002" y1="1002.748266666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271.0000000000002" x2="271.0000000000002" y1="1002.748266666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271.0000000000001" x2="361.0000000000001" y1="1002.748266666667" y2="1002.748266666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271.0000000000001" x2="361.0000000000001" y1="1030.666666666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271.0000000000001" x2="271.0000000000001" y1="1002.748266666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361.0000000000001" x2="361.0000000000001" y1="1002.748266666667" y2="1030.666666666667"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,46,955.667) scale(1,1) translate(0,0)" writing-mode="lr" x="46" xml:space="preserve" y="961.67" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,43,989.667) scale(1,1) translate(0,0)" writing-mode="lr" x="43" xml:space="preserve" y="995.67" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,225,989.667) scale(1,1) translate(0,0)" writing-mode="lr" x="225" xml:space="preserve" y="995.67" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,42,1017.67) scale(1,1) translate(0,0)" writing-mode="lr" x="42" xml:space="preserve" y="1023.67" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,224,1017.67) scale(1,1) translate(0,0)" writing-mode="lr" x="224" xml:space="preserve" y="1023.67" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,66.5,650.167) scale(1,1) translate(0,0)" writing-mode="lr" x="66.50000000000011" xml:space="preserve" y="654.6666666666667" zvalue="18">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.054,957.667) scale(1,1) translate(0,0)" writing-mode="lr" x="226.05" xml:space="preserve" y="963.67" zvalue="26">MengBanHeErJi-01-2014</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,135.054,1017.67) scale(1,1) translate(0,0)" writing-mode="lr" x="135.05" xml:space="preserve" y="1023.67" zvalue="27">杨立超</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,40,176.667) scale(1,1) translate(0,0)" writing-mode="lr" x="40" xml:space="preserve" y="182.17" zvalue="29">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,220,176.667) scale(1,1) translate(0,0)" writing-mode="lr" x="220" xml:space="preserve" y="182.17" zvalue="30">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47.1875,248.667) scale(1,1) translate(0,0)" writing-mode="lr" x="47.19" xml:space="preserve" y="253.17" zvalue="31">10kV#1变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232.083,203.361) scale(1,1) translate(0,0)" writing-mode="lr" x="232.08" xml:space="preserve" y="207.86" zvalue="32">0.4kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.5,201.5) scale(1,1) translate(0,0)" writing-mode="lr" x="48.5" xml:space="preserve" y="206" zvalue="34">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,473.667,682) scale(1,1) translate(-3.94055e-13,0)" writing-mode="lr" x="473.67" xml:space="preserve" y="686.5" zvalue="37">6.3kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1824.5,690.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1824.5" xml:space="preserve" y="694.83" zvalue="38">0.4kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,586.167,290.333) scale(1,1) translate(0,0)" writing-mode="lr" x="586.17" xml:space="preserve" y="294.83" zvalue="39">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" x="1597.1328125" xml:space="preserve" y="1006.914974013419" zvalue="42">#2发电机    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1597.1328125" xml:space="preserve" y="1022.914974013419" zvalue="42">320KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1573.86,755.457) scale(1,1) translate(0,0)" writing-mode="lr" x="1573.86" xml:space="preserve" y="759.96" zvalue="44">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1618.47,833.879) scale(1,1) translate(0,0)" writing-mode="lr" x="1618.47" xml:space="preserve" y="838.38" zvalue="48">402</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,712.885,984.5) scale(1,1) translate(-1.46302e-13,0)" writing-mode="lr" x="712.88" xml:space="preserve" y="989" zvalue="52">#1发电机   100KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,748.885,803) scale(1,1) translate(0,0)" writing-mode="lr" x="748.88" xml:space="preserve" y="807.5" zvalue="53">601</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" x="753.5" xml:space="preserve" y="552.875" zvalue="56">#1主变            </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="753.5" xml:space="preserve" y="568.875" zvalue="56">1250KVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,723.275,440.556) scale(1,1) translate(0,0)" writing-mode="lr" x="723.28" xml:space="preserve" y="445.06" zvalue="59">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,797,377.546) scale(1,1) translate(0,0)" writing-mode="lr" x="797" xml:space="preserve" y="382.05" zvalue="62">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,681,349.507) scale(1,1) translate(0,0)" writing-mode="lr" x="681" xml:space="preserve" y="354.01" zvalue="64">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" x="1491.25" xml:space="preserve" y="515.625" zvalue="69">#2主变           </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1491.25" xml:space="preserve" y="531.625" zvalue="69">400KVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1527,382) scale(1,1) translate(0,0)" writing-mode="lr" x="1527" xml:space="preserve" y="386.5" zvalue="71">0021</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,960,38) scale(1,1) translate(0,0)" writing-mode="lr" x="960" xml:space="preserve" y="42.5" zvalue="74">10kV州水泥厂线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1383.5,212.753) scale(1,1) translate(0,0)" writing-mode="lr" x="1383.5" xml:space="preserve" y="217.25" zvalue="79">0901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1526.25,255) scale(1,1) translate(0,0)" writing-mode="lr" x="1526.25" xml:space="preserve" y="259.5" zvalue="84">0</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1524.5,176) scale(1,1) translate(0,0)" writing-mode="lr" x="1524.5" xml:space="preserve" y="180.5" zvalue="87">7</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.5,225.5) scale(1,1) translate(0,0)" writing-mode="lr" x="52.5" xml:space="preserve" y="230" zvalue="89">6.3kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227,247.5) scale(1,1) translate(0,0)" writing-mode="lr" x="227" xml:space="preserve" y="252" zvalue="91">10kV#2变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,193.211,320.591) scale(1,1) translate(0,0)" writing-mode="lr" x="193.21" xml:space="preserve" y="325.09" zvalue="96">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,298.211,320.591) scale(1,1) translate(0,0)" writing-mode="lr" x="298.21" xml:space="preserve" y="325.09" zvalue="97">通道</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="41" y="308" zvalue="100"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="35">
   <path class="v6300" d="M 510 683 L 1001.67 683" stroke-width="6" zvalue="36"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674244886532" ObjectName="6.3kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674244886532"/></metadata>
  <path d="M 510 683 L 1001.67 683" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="36">
   <path class="v400" d="M 1336.67 689.67 L 1786.67 689.67" stroke-width="6" zvalue="37"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674244952068" ObjectName="0.4kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674244952068"/></metadata>
  <path d="M 1336.67 689.67 L 1786.67 689.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="37">
   <path class="kv10" d="M 621.67 293 L 1668.33 293" stroke-width="6" zvalue="38"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674245017604" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674245017604"/></metadata>
  <path d="M 621.67 293 L 1668.33 293" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="v400" height="30" transform="rotate(0,1592.35,952.1) scale(1.85899,1.85899) translate(-722.897,-427.055)" width="30" x="1564.462811040348" xlink:href="#Generator:发电机_0" y="924.2153075999174" zvalue="41"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449816821765" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449816821765"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1592.35,952.1) scale(1.85899,1.85899) translate(-722.897,-427.055)" width="30" x="1564.462811040348" y="924.2153075999174"/></g>
  <g id="47">
   <use class="v6300" height="30" transform="rotate(0,711.885,945.885) scale(1.85899,1.85899) translate(-316.058,-424.184)" width="30" x="684" xlink:href="#Generator:发电机_0" y="918" zvalue="51"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449816887301" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449816887301"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,711.885,945.885) scale(1.85899,1.85899) translate(-316.058,-424.184)" width="30" x="684" y="918"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="165">
   <use class="v400" height="30" transform="rotate(0,1593.08,756.457) scale(1.9625,1.2338) translate(-774.102,-139.837)" width="15" x="1578.36352128978" xlink:href="#Disconnector:刀闸_0" y="737.9503968253965" zvalue="43"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449816756229" ObjectName="#2发电机4021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449816756229"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1593.08,756.457) scale(1.9625,1.2338) translate(-774.102,-139.837)" width="15" x="1578.36352128978" y="737.9503968253965"/></g>
  <g id="59">
   <use class="kv10" height="30" transform="rotate(0,699.719,350.507) scale(1.9625,1.2338) translate(-335.955,-62.9118)" width="15" x="685" xlink:href="#Disconnector:刀闸_0" y="332.0000000000001" zvalue="63"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449817083909" ObjectName="#1主变10kV侧0011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449817083909"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,699.719,350.507) scale(1.9625,1.2338) translate(-335.955,-62.9118)" width="15" x="685" y="332.0000000000001"/></g>
  <g id="68">
   <use class="kv10" height="26" transform="rotate(0,1549,383) scale(1,1) translate(0,0)" width="12" x="1543" xlink:href="#Disconnector:单手车刀闸1212_0" y="370" zvalue="70"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449817149445" ObjectName="#2主变0021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449817149445"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1549,383) scale(1,1) translate(0,0)" width="12" x="1543" y="370"/></g>
  <g id="76">
   <use class="kv10" height="30" transform="rotate(0,1416.72,209.507) scale(1.9625,1.2338) translate(-687.605,-36.1932)" width="15" x="1402" xlink:href="#Disconnector:刀闸_0" y="191.0000000000001" zvalue="78"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449817280517" ObjectName="10kV母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449817280517"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1416.72,209.507) scale(1.9625,1.2338) translate(-687.605,-36.1932)" width="15" x="1402" y="191.0000000000001"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="46">
   <path class="v400" d="M 1593.25 738.56 L 1593.25 689.67" stroke-width="1" zvalue="45"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="165@0" LinkObjectIDznd="36@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1593.25 738.56 L 1593.25 689.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="v400" d="M 1592.35 924.68 L 1592.35 843.18" stroke-width="1" zvalue="47"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="850@0" LinkObjectIDznd="170@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1592.35 924.68 L 1592.35 843.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="v400" d="M 1593.97 821.94 L 1593.97 774.65" stroke-width="1" zvalue="49"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@0" LinkObjectIDznd="165@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1593.97 821.94 L 1593.97 774.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="v6300" d="M 711.88 836.11 L 711.88 918.46" stroke-width="1" zvalue="53"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@1" LinkObjectIDznd="47@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 711.88 836.11 L 711.88 918.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="v6300" d="M 711.88 783.46 L 711.88 683" stroke-width="1" zvalue="54"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@0" LinkObjectIDznd="35@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 711.88 783.46 L 711.88 683" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="55">
   <path class="v6300" d="M 697.8 599.39 L 697.8 683" stroke-width="1" zvalue="56"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@1" LinkObjectIDznd="35@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 697.8 599.39 L 697.8 683" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv10" d="M 699.86 448.72 L 699.86 514.78" stroke-width="1" zvalue="59"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="56@1" LinkObjectIDznd="53@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 699.86 448.72 L 699.86 514.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="kv10" d="M 699.84 368.7 L 699.73 427.48" stroke-width="1" zvalue="64"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="59@1" LinkObjectIDznd="56@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 699.84 368.7 L 699.73 427.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="kv10" d="M 699.89 332.61 L 699.89 293" stroke-width="1" zvalue="65"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="59@0" LinkObjectIDznd="37@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 699.89 332.61 L 699.89 293" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="kv10" d="M 784.35 396.62 L 699.79 396.62" stroke-width="1" zvalue="66"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="137@0" LinkObjectIDznd="60" MaxPinNum="2"/>
   </metadata>
  <path d="M 784.35 396.62 L 699.79 396.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv0" d="M 1548.8 560.39 L 1548.8 689.67" stroke-width="1" zvalue="69"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@1" LinkObjectIDznd="" MaxPinNum="2"/>
   </metadata>
  <path d="M 1548.8 560.39 L 1548.8 689.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="kv10" d="M 1549.08 395.97 L 1548.86 475.78" stroke-width="1" zvalue="71"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="68@0" LinkObjectIDznd="65@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1549.08 395.97 L 1548.86 475.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="kv10" d="M 1549 370.08 L 1549 293" stroke-width="1" zvalue="72"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="68@1" LinkObjectIDznd="37@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1549 370.08 L 1549 293" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="kv10" d="M 962.91 97.79 L 962.91 293" stroke-width="1" zvalue="74"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="72@0" LinkObjectIDznd="37@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 962.91 97.79 L 962.91 293" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="kv10" d="M 1416.84 227.7 L 1416.84 293" stroke-width="1" zvalue="79"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="76@1" LinkObjectIDznd="37@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1416.84 227.7 L 1416.84 293" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="kv10" d="M 1415.47 144.54 L 1416.89 191.61" stroke-width="1" zvalue="81"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="78@0" LinkObjectIDznd="76@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1415.47 144.54 L 1416.89 191.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="kv10" d="M 1487.35 256.07 L 1416.84 256.07" stroke-width="1" zvalue="84"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="82@0" LinkObjectIDznd="77" MaxPinNum="2"/>
   </metadata>
  <path d="M 1487.35 256.07 L 1416.84 256.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="kv10" d="M 1485.35 176.07 L 1416.42 176.07" stroke-width="1" zvalue="94"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="84@0" LinkObjectIDznd="80" MaxPinNum="2"/>
   </metadata>
  <path d="M 1485.35 176.07 L 1416.42 176.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="170">
   <use class="v400" height="20" transform="rotate(0,1594.01,832.571) scale(1.22222,1.11111) translate(-288.709,-82.146)" width="10" x="1587.901166870948" xlink:href="#Breaker:开关_0" y="821.460012210012" zvalue="46"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924515397637" ObjectName="#2发电机402断路器"/>
   <cge:TPSR_Ref TObjectID="6473924515397637"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1594.01,832.571) scale(1.22222,1.11111) translate(-288.709,-82.146)" width="10" x="1587.901166870948" y="821.460012210012"/></g>
  <g id="49">
   <use class="v6300" height="20" transform="rotate(0,711.885,810.149) scale(1.75,2.88514) translate(-301.344,-510.496)" width="10" x="703.1348531523532" xlink:href="#Breaker:手车开关_0" y="781.2972972972975" zvalue="52"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924515463173" ObjectName="#1发电机601断路器"/>
   <cge:TPSR_Ref TObjectID="6473924515463173"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,711.885,810.149) scale(1.75,2.88514) translate(-301.344,-510.496)" width="10" x="703.1348531523532" y="781.2972972972975"/></g>
  <g id="56">
   <use class="kv10" height="20" transform="rotate(0,699.775,438.111) scale(1.22222,1.11111) translate(-126.121,-42.7)" width="10" x="693.6640329218105" xlink:href="#Breaker:开关_0" y="427" zvalue="58"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924515528709" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473924515528709"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,699.775,438.111) scale(1.22222,1.11111) translate(-126.121,-42.7)" width="10" x="693.6640329218105" y="427"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="53">
   <g id="530">
    <use class="kv10" height="50" transform="rotate(0,697.8,557) scale(1.72,1.72) translate(-281.302,-215.163)" width="30" x="672" xlink:href="#PowerTransformer2:Y-D_0" y="514" zvalue="55"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874439262212" ObjectName="10"/>
    </metadata>
   </g>
   <g id="531">
    <use class="v6300" height="50" transform="rotate(0,697.8,557) scale(1.72,1.72) translate(-281.302,-215.163)" width="30" x="672" xlink:href="#PowerTransformer2:Y-D_1" y="514" zvalue="55"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874439327748" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399451869187" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399451869187"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,697.8,557) scale(1.72,1.72) translate(-281.302,-215.163)" width="30" x="672" y="514"/></g>
  <g id="65">
   <g id="650">
    <use class="kv10" height="50" transform="rotate(0,1548.8,518) scale(1.72,1.72) translate(-637.535,-198.837)" width="30" x="1523" xlink:href="#PowerTransformer2:Y-D_0" y="475" zvalue="68"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874439393284" ObjectName="10"/>
    </metadata>
   </g>
   <g id="651">
    <use class="kv0" height="50" transform="rotate(0,1548.8,518) scale(1.72,1.72) translate(-637.535,-198.837)" width="30" x="1523" xlink:href="#PowerTransformer2:Y-D_1" y="475" zvalue="68"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874439458820" ObjectName="0"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399451934723" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399451934723"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1548.8,518) scale(1.72,1.72) translate(-637.535,-198.837)" width="30" x="1523" y="475"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="137">
   <use class="kv10" height="20" transform="rotate(270,798,396.546) scale(-1.4,1.4) translate(-1366,-109.299)" width="10" x="791" xlink:href="#GroundDisconnector:地刀_0" y="382.5456889702591" zvalue="61"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449817018373" ObjectName="#1主变10kV侧00117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449817018373"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,798,396.546) scale(-1.4,1.4) translate(-1366,-109.299)" width="10" x="791" y="382.5456889702591"/></g>
  <g id="82">
   <use class="kv10" height="20" transform="rotate(270,1501,256) scale(-1.4,1.4) translate(-2571.14,-69.1429)" width="10" x="1494" xlink:href="#GroundDisconnector:地刀_0" y="241.9999999999999" zvalue="83"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449817477125" ObjectName="10kV母线电压互感器09010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449817477125"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1501,256) scale(-1.4,1.4) translate(-2571.14,-69.1429)" width="10" x="1494" y="241.9999999999999"/></g>
  <g id="84">
   <use class="kv10" height="20" transform="rotate(270,1499,176) scale(-1.4,1.4) translate(-2567.71,-46.2857)" width="10" x="1492" xlink:href="#GroundDisconnector:地刀_0" y="161.9999999999999" zvalue="86"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449817608197" ObjectName="10kV母线电压互感器09017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449817608197"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1499,176) scale(-1.4,1.4) translate(-2567.71,-46.2857)" width="10" x="1492" y="161.9999999999999"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="78">
   <use class="kv10" height="18" transform="rotate(0,1417.58,125.5) scale(2.27778,-2.27778) translate(-785.646,-169.098)" width="15" x="1400.5" xlink:href="#Accessory:PT8_0" y="105" zvalue="80"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449817346053" ObjectName="10kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1417.58,125.5) scale(2.27778,-2.27778) translate(-785.646,-169.098)" width="15" x="1400.5" y="105"/></g>
 </g>
 <g id="StateClass">
  <g id="427">
   <use height="30" transform="rotate(0,325.485,321.107) scale(0.708333,0.665547) translate(129.648,156.347)" width="30" x="314.86" xlink:href="#State:红绿圆(方形)_0" y="311.12" zvalue="98"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,325.485,321.107) scale(0.708333,0.665547) translate(129.648,156.347)" width="30" x="314.86" y="311.12"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,229.86,321.107) scale(0.708333,0.665547) translate(90.2733,156.347)" width="30" x="219.24" xlink:href="#State:红绿圆(方形)_0" y="311.12" zvalue="99"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,229.86,321.107) scale(0.708333,0.665547) translate(90.2733,156.347)" width="30" x="219.24" y="311.12"/></g>
 </g>
</svg>