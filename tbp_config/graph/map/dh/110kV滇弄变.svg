<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549677916161" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="ACLineSegment:220kV线路_0" viewBox="0,0,35,40">
   <use terminal-index="0" type="0" x="14.20833333333333" xlink:href="#terminal" y="39.00547521122963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.5" x2="30.5" y1="30.75" y2="28.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.5" x2="27.5" y1="25.75" y2="30.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="21.50545055364641" x2="21.50545055364641" y1="13.04138864447597" y2="19.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.5" x2="24.5" y1="23.75" y2="25.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="5.5" y1="37" y2="37"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="18.91094883543335" x2="24.38823024054981" y1="12.92541949757221" y2="12.92541949757221"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.5" x2="18.5" y1="23.75" y2="25.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.5" x2="30.5" y1="25.75" y2="27.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="6.5" y1="36" y2="36"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="7.5" y1="35" y2="35"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.04,24.92) scale(1,1) translate(0,0)" width="6.08" x="2" y="17.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="32" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="4" y1="25.75" y2="19.75"/>
   <path d="M 14 13 L 5 13 L 5 25 L 5 25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="6" y1="25.75" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.5" x2="21.5" y1="20.75" y2="23.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.46058610156545" x2="14.15436235204273" y1="12.92541949757221" y2="12.92541949757221"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.16022336769755" x2="14.16022336769755" y1="38.83333333333334" y2="0.8333333333333321"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.46383161512025" x2="31.41666666666666" y1="12.99758812251703" y2="12.99758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.35940244368076" x2="24.35940244368076" y1="12.99758812251703" y2="12.99758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.36598892707138" x2="31.36598892707138" y1="9.749999999999996" y2="16.24517624503405"/>
   <ellipse cx="21.51" cy="23.82" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="28.55" cy="28.08" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="21.76" cy="32.08" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="24.35940244368077" x2="24.35940244368077" y1="10.48325293741726" y2="15.58409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.51910080183274" x2="32.51910080183274" y1="11.01295093653441" y2="15.34306843322376"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="33.38393470790376" x2="33.38393470790376" y1="11.91505874834466" y2="13.89969593432727"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.57589728904158" x2="16.57589728904158" y1="12.99758812251703" y2="12.99758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="26.52148720885832" x2="26.52148720885832" y1="10.48325293741726" y2="15.5840919325617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="16.49256395570825" x2="16.49256395570825" y1="10.33333333333333" y2="15.43417232847779"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="18.73798205421914" x2="18.73798205421914" y1="10.48325293741726" y2="15.58409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.58333333333333" x2="18.58333333333333" y1="32.5" y2="34.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.58333333333334" x2="24.58333333333334" y1="32.5" y2="34.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.58333333333333" x2="21.58333333333333" y1="29.5" y2="32.5"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="2" type="2" x="12.00516117969822" xlink:href="#terminal" y="7.908504801097395"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:5卷PT带壁雷器_0" viewBox="0,0,30,35">
   <use terminal-index="0" type="0" x="25.27572331551165" xlink:href="#terminal" y="34.09945819018008"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.25" x2="25.0990682626599" y1="27.2315496546656" y2="27.2315496546656"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.16666666666666" x2="25.16666666666666" y1="34.02740325661302" y2="14.61073658994635"/>
   <path d="M 10.5 13.9441 L 4.5 13.9441 L 4.5 6.94407" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="15.5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,25.25,14.36) scale(1,-1) translate(0,-926.43)" width="7" x="21.75" y="6.61"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="7.5" y1="22.19406992327969" y2="18.19406992327969"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.16666666666666" x2="26.16666666666666" y1="13.77740325661302" y2="18.77740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="5.5" y1="18.19406992327969" y2="19.19406992327969"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.16666666666666" x2="24.16666666666666" y1="13.77740325661302" y2="18.77740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="5.5" y1="22.19406992327969" y2="21.19406992327969"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.17943360505483" x2="25.17943360505483" y1="6.610736589946352" y2="3.462520268614096"/>
   <ellipse cx="13.98" cy="27.24" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.39" cy="13.83" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.20921744067709" x2="22.93240159599324" y1="3.3946117330996" y2="3.3946117330996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.37588410734375" x2="23.76573492932657" y1="2.144611733099605" y2="2.144611733099605"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.79255077401042" x2="24.34906826265991" y1="0.8946117330996053" y2="0.8946117330996053"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.94906826265991" x2="16.34906826265991" y1="27.45662962336982" y2="28.69416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.94906826265991" x2="13.94906826265991" y1="24.98154965466559" y2="27.4566296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.94906826265991" x2="11.54906826265991" y1="27.45662962336982" y2="28.69416960772192"/>
   <ellipse cx="13.98" cy="20.24" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="6.98" cy="27.24" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.94906826265991" x2="6.94906826265991" y1="24.98154965466559" y2="27.4566296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.949068262659907" x2="4.549068262659912" y1="27.45662962336982" y2="28.69416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.94906826265991" x2="16.34906826265991" y1="20.45662962336982" y2="21.69416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.94906826265991" x2="11.54906826265991" y1="20.45662962336982" y2="21.69416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.949068262659912" x2="9.349068262659905" y1="27.45662962336982" y2="28.69416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.94906826265991" x2="13.94906826265991" y1="17.9815496546656" y2="20.4566296233698"/>
   <ellipse cx="6.98" cy="20.24" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.48240159599324" x2="12.88240159599324" y1="13.92329629003649" y2="15.16083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.48240159599324" x2="8.082401595993243" y1="13.92329629003649" y2="15.16083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.48240159599324" x2="10.48240159599324" y1="11.44821632133226" y2="13.92329629003646"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.542550774010419" x2="2.265734929326571" y1="6.8946117330996" y2="6.8946117330996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.709217440677083" x2="3.099068262659904" y1="5.644611733099605" y2="5.644611733099605"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.125884107343751" x2="3.682401595993237" y1="4.394611733099605" y2="4.394611733099605"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="Disconnector:双联接地刀_0" viewBox="0,0,25,30">
   <use terminal-index="0" type="0" x="20.75" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="20.75" xlink:href="#terminal" y="26.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.91666666666667" x2="9.000000000000002" y1="2.725" y2="2.725"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.758333333333335" x2="10.15833333333333" y1="4.25" y2="4.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.750000000000002" x2="8.166666666666668" y1="1.199999999999999" y2="1.199999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="7.5" y1="6.916666666666668" y2="4.250000000000004"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.5" x2="4.5" y1="18.75" y2="6.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.75" x2="4.5" y1="12.75" y2="12.75"/>
   <path d="M 20.8333 23.6667 L 10.25 23.6667 L 10.25 18.6667" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="20.75" x2="20.75" y1="18.58333333333333" y2="26.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="19.21666666666667" x2="22.18333333333333" y1="6.755662181544974" y2="6.755662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.75817339434921" x2="20.75817339434921" y1="6.833333333333332" y2="0.6887951642647465"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.25" x2="10.25" y1="18.91666666666667" y2="6.916666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.091666666666667" x2="11.58333333333333" y1="6.833333333333336" y2="6.833333333333336"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5081733943492" x2="4.5081733943492" y1="18.75" y2="29.83333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.716666666666665" x2="11.68333333333333" y1="18.65546183093142" y2="18.65546183093142"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="20.68849075415782" x2="13.97817591250885" y1="18.55755876866358" y2="8.609107898003083"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.883333333333331" x2="5.849999999999997" y1="18.65546183093142" y2="18.65546183093142"/>
  </symbol>
  <symbol id="Disconnector:双联接地刀_1" viewBox="0,0,25,30">
   <use terminal-index="0" type="0" x="20.75" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="20.75" xlink:href="#terminal" y="26.75"/>
   <path d="M 20.8333 23.6667 L 10.25 23.6667 L 10.25 18.6667" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.4803124927236215" x2="8.353020840609709" y1="17.27825748133663" y2="8.221742518663367"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.758333333333335" x2="10.15833333333333" y1="4.25" y2="4.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.66666666666667" x2="4.499999999999998" y1="12.75" y2="12.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.91666666666667" x2="9.000000000000002" y1="2.725" y2="2.725"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.750000000000002" x2="8.166666666666668" y1="1.199999999999999" y2="1.199999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="7.5" y1="6.916666666666668" y2="4.250000000000004"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.75" x2="20.75" y1="18.58333333333333" y2="26.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.75817339434921" x2="20.75817339434921" y1="6.750000000000002" y2="0.6054618309314179"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="19.21666666666667" x2="22.18333333333333" y1="6.755662181544974" y2="6.755662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="20.75" x2="20.75" y1="18.75" y2="6.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.091666666666667" x2="11.58333333333333" y1="6.833333333333336" y2="6.833333333333336"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5081733943492" x2="4.5081733943492" y1="18.75" y2="30"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.716666666666665" x2="11.68333333333333" y1="18.65546183093142" y2="18.65546183093142"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.883333333333331" x2="5.849999999999997" y1="18.65546183093142" y2="18.65546183093142"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.230312492723622" x2="14.10302084060971" y1="17.27825748133663" y2="8.221742518663367"/>
  </symbol>
  <symbol id="Accessory:接地变中性点_0" viewBox="0,0,12,20">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.166666666666667" x2="9" y1="0.9166666666666643" y2="0.9166666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.25" x2="3.25" y1="7.166666666666666" y2="11"/>
   <ellipse cx="9" cy="12.75" fill-opacity="0" rx="1" ry="1" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="9" y1="1" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="9" y1="8.75" y2="11.75"/>
   <path d="M 9 6 A 1 0.5 0 0 0 9 5" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.83333333333333" x2="8" y1="8.416666666666668" y2="5.583333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.25" x2="5.25" y1="17" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.250000000000001" x2="4.333333333333334" y1="11" y2="7.083333333333334"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,3.29,9.5) scale(1,1) translate(0,0)" width="4.75" x="0.92" y="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.25" x2="3.25" y1="1" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.25" x2="3.25" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.25" x2="4.25" y1="19" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.75" x2="4.75" y1="18" y2="18"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="10.5" y1="17.91666666666667" y2="17.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="11" y1="16.91666666666667" y2="16.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="10" y1="18.91666666666667" y2="18.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="9" y1="13.75" y2="16.91666666666667"/>
   <path d="M 9 7.91667 A 1 0.5 0 0 0 9 6.91667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 9 6.91667 A 1 0.5 0 0 0 9 5.91667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 9 8.83333 A 1 0.5 0 0 0 9 7.83333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:4卷PT带容断器_0" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="5.47912519145992" xlink:href="#terminal" y="41.37373692455962"/>
   <path d="M 16.75 6.75 L 23.75 6.75 L 23.75 9.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,23.86,15.09) scale(1,1) translate(0,0)" width="11" x="18.36" y="12.59"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.86245852479333" x2="27.86245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.86245852479332" x2="27.86245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.8624585247933" x2="19.8624585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.54,25.5) scale(1,1) translate(0,0)" width="4.58" x="3.25" y="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="36"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="16.5" y1="35" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.89772110866599" x2="23.89772110866599" y1="20.41666666666667" y2="23.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.96438777533266" x2="21.96438777533266" y1="24.15816200815096" y2="24.15816200815096"/>
   <ellipse cx="5.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.0108382776216" x2="21.73402243293775" y1="23.92008155192961" y2="23.92008155192961"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.26083827762157" x2="22.6506890996044" y1="25.17008155192959" y2="25.17008155192959"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.76083827762159" x2="23.31735576627107" y1="26.67008155192962" y2="26.67008155192962"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.08713830216066" x2="14.31883560169719" y1="12.79040359122621" y2="12.79040359122621"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.01457106407813" x2="10.82090482830307" y1="15.30654513693459" y2="12.68563107372743"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.39410730945214" x2="14.5877735452272" y1="15.40299989806297" y2="12.78208583485582"/>
   <ellipse cx="5.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.942203825592701" x2="7.942203825592701" y1="8.070768933621144" y2="8.070768933621144"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.692203825592705" x2="8.692203825592705" y1="14.32076893362116" y2="14.32076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="11.87063985073817" y1="6.726117411622521" y2="4.07298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.42264294445647" x2="12.37303257583197" y1="8.249928796703951" y2="6.726117411622536"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="14.82581493230132" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.442203825592705" x2="8.442203825592705" y1="7.570768933621149" y2="7.570768933621149"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831989" x2="4.620639850738163" y1="13.97611741162255" y2="11.32298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456463" x2="5.123032575831967" y1="15.49992879670398" y2="13.97611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831976" x2="7.575814932301304" y1="13.97611741162255" y2="15.10543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456459" x2="5.123032575831962" y1="8.249928796703964" y2="6.726117411622548"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831987" x2="7.575814932301315" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831981" x2="4.620639850738158" y1="6.726117411622528" y2="4.072985910425693"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.507700305101775" x2="5.507700305101775" y1="17.83333333333333" y2="41.49610160569022"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-180,17.7,35.01) scale(-1,1) translate(-2019.83,0)" width="11" x="12.2" y="32.51"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.2207229126482" x2="26.7207229126482" y1="34.70975002257848" y2="34.70975002257848"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.96221825413249" x2="26.96221825413249" y1="36.64308335591183" y2="36.64308335591183"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.72413779791114" x2="26.72413779791114" y1="32.59663285362289" y2="36.87344869830673"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.97413779791113" x2="27.97413779791113" y1="33.34663285362291" y2="35.95678203164009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="29.47413779791115" x2="29.47413779791115" y1="33.8466328536229" y2="35.29011536497342"/>
  </symbol>
  <symbol id="Compensator:电容20200722_0" viewBox="0,0,25,50">
   <use terminal-index="0" type="0" x="15.16666666666667" xlink:href="#terminal" y="0.4166666666666643"/>
   <path d="M 15.3333 45.75 L 15.3333 48.75 L 1.33333 48.75 L 1.33333 0.0833333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.25" x2="22.25" y1="37.91666666666667" y2="35.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.25" x2="23.25" y1="32.25" y2="29.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.75" x2="23.75" y1="37.91666666666667" y2="37.91666666666667"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,22.25,31.42) scale(1,1) translate(0,0)" width="4" x="20.25" y="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.75" x2="22.75" y1="39.91666666666667" y2="39.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.25" x2="21.25" y1="32.25" y2="29.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.25" x2="23.25" y1="38.91666666666667" y2="38.91666666666667"/>
   <rect fill-opacity="0" height="4" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15.33,32.33) scale(1,1) translate(0,0)" width="2" x="14.33" y="30.33"/>
   <path d="M 15.25 23.75 L 22.25 23.75 L 22.25 32.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.35833333333333" x2="15.35833333333333" y1="40.10833333333333" y2="43.62685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.321296296296296" x2="9.321296296296296" y1="41.38611111111112" y2="43.62685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.10833333333333" x2="7.290905947441217" y1="46.02500000000001" y2="46.02500000000001"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.25" x2="9.333333333333332" y1="43.60833333333333" y2="43.60833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.35833333333333" x2="15.35833333333333" y1="43.69166666666667" y2="46.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.374239280774555" x2="7.374239280774555" y1="46.10833333333333" y2="44.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.19166666666666" x2="23.19166666666666" y1="44.66666666666667" y2="46.10833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.358333333333334" x2="9.358333333333334" y1="30.60833333333334" y2="28.40462962962963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.35833333333333" x2="15.35833333333333" y1="21.5" y2="39.10833333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333332" x2="15.35833333333334" y1="28.35833333333333" y2="28.35833333333333"/>
   <path d="M 9.35833 34.2072 A 2.96392 1.81747 -180 0 1 9.35833 30.5723" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 9.35833 37.8421 A 2.96392 1.81747 -180 0 1 9.35833 34.2072" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 9.35833 41.3771 A 2.96392 1.81747 -180 0 1 9.35833 37.7421" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.85833333333333" x2="13.85833333333334" y1="40.10833333333333" y2="40.10833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.85833333333333" x2="13.85833333333334" y1="39.10833333333333" y2="39.10833333333333"/>
   <path d="M 22.5 14.7417 A 6.84167 7.10597 -270 1 0 15.394 21.5833" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 22.4547 14.749 L 15.3041 14.749 L 15.3041 0.416667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_0" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="9.750000000000004" y2="4.166666666666668"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="32"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="1" y1="27" y2="11"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_1" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="27" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_2" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="1.499999999999999" y1="26" y2="9.966666666666669"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.999999999999998" x2="12.41666666666667" y1="26.25" y2="10.05"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:小车母联_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="18.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="17.58333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车母联_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="18.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="0.8333333333333304" y2="5.749999999999999"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车母联_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="18.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="7" y1="14" y2="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.666666666666666" x2="7.25" y1="5.833333333333333" y2="14.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:母联开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(180,5.08,10.04) scale(1,1) translate(0,0)" width="9.17" x="0.5" y="0.5"/>
  </symbol>
  <symbol id="Breaker:母联开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <rect fill="rgb(170,0,0)" fill-opacity="1" height="19.08" rx="0" ry="0" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-opacity="0" stroke-width="1" transform="rotate(180,5.08,10.04) scale(1,1) translate(0,0)" width="9.17" x="0.5" y="0.5"/>
  </symbol>
  <symbol id="Breaker:母联开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.333333333333333" x2="9.75" y1="0.4166666666666696" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.666666666666668" x2="0.4166666666666661" y1="0.4999999999999982" y2="19.66666666666667"/>
   <rect fill-opacity="0" height="19.58" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.04,10.04) scale(1,1) translate(0,0)" width="9.58" x="0.25" y="0.25"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
  <symbol id="State:间隔模板_0" viewBox="0,0,80,30">
   <rect Plane="0" fill="none" fill-opacity="0" height="28.8" stroke="rgb(85,170,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,15) scale(1,1) translate(0,0)" width="79.2" x="0.4" y="0.6"/>
  </symbol>
  <symbol id="State:间隔模板_1" viewBox="0,0,80,30">
   <rect Plane="0" fill="none" fill-opacity="0" height="28.8" stroke="rgb(185,185,185)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,15) scale(1,1) translate(0,0)" width="79.2" x="0.4" y="0.6"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV滇弄变" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="82.56999999999999" id="1" preserveAspectRatio="xMidYMid slice" width="249.69" x="42.31" xlink:href="logo.png" y="34.43"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,167.153,75.7136) scale(1,1) translate(-9.39397e-15,0)" writing-mode="lr" x="167.15" xml:space="preserve" y="79.20999999999999" zvalue="19"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,196,75.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="196" xml:space="preserve" y="84.69" zvalue="20">110kV滇弄变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="632" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,192.531,404.25) scale(1,1) translate(0,0)" width="72.88" x="156.09" y="392.25" zvalue="981"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,192.531,404.25) scale(1,1) translate(0,0)" writing-mode="lr" x="192.53" xml:space="preserve" y="408.75" zvalue="981">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="631" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,90.625,404.25) scale(1,1) translate(0,0)" width="72.88" x="54.19" y="392.25" zvalue="982"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,90.625,404.25) scale(1,1) translate(0,0)" writing-mode="lr" x="90.63" xml:space="preserve" y="408.75" zvalue="982">AVC功能</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="630" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,90.625,363.75) scale(1,1) translate(0,0)" width="72.88" x="54.19" y="351.75" zvalue="983"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,90.625,363.75) scale(1,1) translate(0,0)" writing-mode="lr" x="90.63" xml:space="preserve" y="368.25" zvalue="983">信号一览</text>
  <line fill="none" id="13" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.00000000000045" x2="391" y1="152.8704926140824" y2="152.8704926140824" zvalue="22"/>
  <line fill="none" id="12" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="392" x2="392" y1="11" y2="1041" zvalue="23"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="25" x2="206" y1="165" y2="165"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="25" x2="206" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="25" x2="25" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="206" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="387" y1="165" y2="165"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="387" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="206" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="387" x2="387" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="25" x2="206" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="25" x2="206" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="25" x2="25" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="206" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="387" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="387" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="206" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="387" x2="387" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="25" x2="206" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="25" x2="206" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="25" x2="25" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="206" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="387" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="387" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="206" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="387" x2="387" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="25" x2="206" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="25" x2="206" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="25" x2="25" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="206" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="387" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="387" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="206" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="387" x2="387" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="25" x2="206" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="25" x2="206" y1="283.5" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="25" x2="25" y1="260.75" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="206" y1="260.75" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="387" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="387" y1="283.5" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="206" y1="260.75" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="387" x2="387" y1="260.75" y2="283.5"/>
  <line fill="none" id="10" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.00000000000045" x2="391" y1="622.8704926140824" y2="622.8704926140824" zvalue="25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56" x2="101.7745" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56" x2="101.7745" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56" x2="56" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.7745" x2="101.7745" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.7745" x2="160.5809" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.7745" x2="160.5809" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.7745" x2="101.7745" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5809" x2="160.5809" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5809" x2="219.3873" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5809" x2="219.3873" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5809" x2="160.5809" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.3873" x2="219.3873" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.3872" x2="278.1936000000001" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.3872" x2="278.1936000000001" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.3872" x2="219.3872" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="278.1936000000001" x2="278.1936000000001" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="278.1936000000001" x2="337" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="278.1936000000001" x2="337" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="278.1936000000001" x2="278.1936000000001" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="337" x2="337" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56" x2="101.7745" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56" x2="101.7745" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56" x2="56" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.7745" x2="101.7745" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.7745" x2="160.5809" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.7745" x2="160.5809" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.7745" x2="101.7745" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5809" x2="160.5809" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5809" x2="219.3873" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5809" x2="219.3873" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5809" x2="160.5809" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.3873" x2="219.3873" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.3872" x2="278.1936000000001" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.3872" x2="278.1936000000001" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.3872" x2="219.3872" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="278.1936000000001" x2="278.1936000000001" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="278.1936000000001" x2="337" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="278.1936000000001" x2="337" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="278.1936000000001" x2="278.1936000000001" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="337" x2="337" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56" x2="101.7745" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56" x2="101.7745" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56" x2="56" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.7745" x2="101.7745" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.7745" x2="160.5809" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.7745" x2="160.5809" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.7745" x2="101.7745" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5809" x2="160.5809" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5809" x2="219.3873" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5809" x2="219.3873" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5809" x2="160.5809" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.3873" x2="219.3873" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.3872" x2="278.1936000000001" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.3872" x2="278.1936000000001" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.3872" x2="219.3872" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="278.1936000000001" x2="278.1936000000001" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="278.1936000000001" x2="337" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="278.1936000000001" x2="337" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="278.1936000000001" x2="278.1936000000001" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="337" x2="337" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56" x2="101.7745" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56" x2="101.7745" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56" x2="56" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.7745" x2="101.7745" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.7745" x2="160.5809" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.7745" x2="160.5809" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.7745" x2="101.7745" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5809" x2="160.5809" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5809" x2="219.3873" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5809" x2="219.3873" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5809" x2="160.5809" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.3873" x2="219.3873" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.3872" x2="278.1936000000001" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.3872" x2="278.1936000000001" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.3872" x2="219.3872" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="278.1936000000001" x2="278.1936000000001" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="278.1936000000001" x2="337" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="278.1936000000001" x2="337" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="278.1936000000001" x2="278.1936000000001" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="337" x2="337" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56" x2="101.7745" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56" x2="101.7745" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56" x2="56" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.7745" x2="101.7745" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.7745" x2="160.5809" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.7745" x2="160.5809" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.7745" x2="101.7745" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5809" x2="160.5809" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5809" x2="219.3873" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5809" x2="219.3873" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5809" x2="160.5809" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.3873" x2="219.3873" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.3872" x2="278.1936000000001" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.3872" x2="278.1936000000001" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.3872" x2="219.3872" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="278.1936000000001" x2="278.1936000000001" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="278.1936000000001" x2="337" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="278.1936000000001" x2="337" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="278.1936000000001" x2="278.1936000000001" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="337" x2="337" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56" x2="101.7745" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56" x2="101.7745" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56" x2="56" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.7745" x2="101.7745" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.7745" x2="160.5809" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.7745" x2="160.5809" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.7745" x2="101.7745" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5809" x2="160.5809" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5809" x2="219.3873" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5809" x2="219.3873" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5809" x2="160.5809" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.3873" x2="219.3873" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.3872" x2="278.1936000000001" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.3872" x2="278.1936000000001" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.3872" x2="219.3872" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="278.1936000000001" x2="278.1936000000001" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="278.1936000000001" x2="337" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="278.1936000000001" x2="337" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="278.1936000000001" x2="278.1936000000001" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="337" x2="337" y1="582" y2="606.6794"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="24" x2="114" y1="938" y2="938"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="24" x2="114" y1="977.1632999999999" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="24" x2="24" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="114" x2="114" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="114" x2="384" y1="938" y2="938"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="114" x2="384" y1="977.1632999999999" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="114" x2="114" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="384" x2="384" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="24" x2="114" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="24" x2="114" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="24" x2="24" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="114" x2="114" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="114" x2="204" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="114" x2="204" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="114" x2="114" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="204" x2="204" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="204.0000000000001" x2="294.0000000000001" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="204.0000000000001" x2="294.0000000000001" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="204.0000000000001" x2="204.0000000000001" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="294.0000000000001" x2="294.0000000000001" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="294" x2="384" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="294" x2="384" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="294" x2="294" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="384" x2="384" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="24" x2="114" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="24" x2="114" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="24" x2="24" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="114" x2="114" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="114" x2="204" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="114" x2="204" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="114" x2="114" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="204" x2="204" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="204.0000000000001" x2="294.0000000000001" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="204.0000000000001" x2="294.0000000000001" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="204.0000000000001" x2="204.0000000000001" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="294.0000000000001" x2="294.0000000000001" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="294" x2="384" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="294" x2="384" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="294" x2="294" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="384" x2="384" y1="1005.0816" y2="1033"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,69,958) scale(1,1) translate(0,0)" writing-mode="lr" x="69" xml:space="preserve" y="964" zvalue="29">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,66,992) scale(1,1) translate(0,0)" writing-mode="lr" x="66" xml:space="preserve" y="998" zvalue="30">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,248,992) scale(1,1) translate(0,0)" writing-mode="lr" x="248" xml:space="preserve" y="998" zvalue="31">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,65,1020) scale(1,1) translate(0,0)" writing-mode="lr" x="65" xml:space="preserve" y="1026" zvalue="32">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,247,1020) scale(1,1) translate(0,0)" writing-mode="lr" x="247" xml:space="preserve" y="1026" zvalue="33">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" x="132.5" xml:space="preserve" y="460" zvalue="34">110kV Ⅰ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="132.5" xml:space="preserve" y="476" zvalue="34">母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,683.75,304.25) scale(1,1) translate(0,0)" writing-mode="lr" x="683.75" xml:space="preserve" y="308.75" zvalue="36">110kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1623.5,308) scale(1,1) translate(0,0)" writing-mode="lr" x="1623.5" xml:space="preserve" y="312.5" zvalue="38">110kVⅡ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,939.5,78.625) scale(1,1) translate(0,0)" writing-mode="lr" x="939.5" xml:space="preserve" y="83.13" zvalue="40">110kV汉滇线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="33" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,914,278.5) scale(1,1) translate(0,0)" writing-mode="lr" x="914" xml:space="preserve" y="283" zvalue="49">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,917.875,175.25) scale(1,1) translate(0,0)" writing-mode="lr" x="917.88" xml:space="preserve" y="179.75" zvalue="53">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,991.25,202.5) scale(1,1) translate(0,0)" writing-mode="lr" x="991.25" xml:space="preserve" y="207" zvalue="56">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,990.25,254) scale(1,1) translate(0,0)" writing-mode="lr" x="990.25" xml:space="preserve" y="258.5" zvalue="58">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,991.25,153.75) scale(1,1) translate(0,0)" writing-mode="lr" x="991.25" xml:space="preserve" y="158.25" zvalue="60">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,795.25,278.5) scale(1,1) translate(0,0)" writing-mode="lr" x="795.25" xml:space="preserve" y="283" zvalue="67">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,799.125,175.25) scale(1,1) translate(0,0)" writing-mode="lr" x="799.13" xml:space="preserve" y="179.75" zvalue="71">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,762.5,201.5) scale(1,1) translate(0,0)" writing-mode="lr" x="762.5" xml:space="preserve" y="206" zvalue="74">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,762.5,253) scale(1,1) translate(0,0)" writing-mode="lr" x="762.5" xml:space="preserve" y="257.5" zvalue="76">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,762.5,153.75) scale(1,1) translate(0,0)" writing-mode="lr" x="762.5" xml:space="preserve" y="158.25" zvalue="78">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1350,78.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1350" xml:space="preserve" y="83.13" zvalue="84">110kV瑞滇线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1324.5,278.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1324.5" xml:space="preserve" y="283" zvalue="88">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1328.38,175.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1328.38" xml:space="preserve" y="179.75" zvalue="92">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1401.75,202.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1401.75" xml:space="preserve" y="207" zvalue="96">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1400.75,254) scale(1,1) translate(0,0)" writing-mode="lr" x="1400.75" xml:space="preserve" y="258.5" zvalue="98">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1401.75,153.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1401.75" xml:space="preserve" y="158.25" zvalue="100">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1468.5,278.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1468.5" xml:space="preserve" y="283" zvalue="108">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="91" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1472.38,175.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1472.38" xml:space="preserve" y="179.75" zvalue="112">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1545.75,202.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1545.75" xml:space="preserve" y="207" zvalue="115">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1544.75,258) scale(1,1) translate(0,0)" writing-mode="lr" x="1544.75" xml:space="preserve" y="262.5" zvalue="117">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1545.75,153.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1545.75" xml:space="preserve" y="158.25" zvalue="119">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="107" stroke="rgb(255,255,255)" text-anchor="middle" x="929.2578125" xml:space="preserve" y="416.9914772727273" zvalue="124">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="107" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="929.2578125" xml:space="preserve" y="432.9914772727273" zvalue="124">SZ11-50000</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="110" stroke="rgb(255,255,255)" text-anchor="middle" x="1384.15625" xml:space="preserve" y="425.5965909090909" zvalue="127">#3主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="110" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1384.15625" xml:space="preserve" y="441.5965909090909" zvalue="127">SZ11-50000</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="119" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1063.12,278.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1063.13" xml:space="preserve" y="282.75" zvalue="136">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="121" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1196.88,278.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1196.88" xml:space="preserve" y="282.75" zvalue="138">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="130" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1063.06,222) scale(1,1) translate(0,0)" writing-mode="lr" x="1063.06" xml:space="preserve" y="226.5" zvalue="147">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="131" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1237.06,220.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1237.06" xml:space="preserve" y="225.25" zvalue="149">27</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="136" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1046.25,474.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1046.25" xml:space="preserve" y="478.75" zvalue="153">110kVⅠ母电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="140" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1021.38,372) scale(1,1) translate(0,0)" writing-mode="lr" x="1021.38" xml:space="preserve" y="376.5" zvalue="156">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="139" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1097.5,400.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1097.5" xml:space="preserve" y="405" zvalue="159">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="138" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1097.5,343.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1097.5" xml:space="preserve" y="348.25" zvalue="161">10</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="154" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1260,473.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1260" xml:space="preserve" y="477.75" zvalue="170">110kVⅡ母电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="153" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1235.12,372) scale(1,1) translate(0,0)" writing-mode="lr" x="1235.13" xml:space="preserve" y="376.5" zvalue="172">1902</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="152" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1311.25,400.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1311.25" xml:space="preserve" y="405" zvalue="174">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="151" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1311.25,343.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1311.25" xml:space="preserve" y="348.25" zvalue="176">20</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="165" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1108.81,719.917) scale(1,1) translate(0,0)" writing-mode="lr" x="1108.81" xml:space="preserve" y="724.42" zvalue="183">10kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="166" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1185.07,721.944) scale(1,1) translate(0,0)" writing-mode="lr" x="1185.07" xml:space="preserve" y="726.4400000000001" zvalue="184">10kVⅢ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,853.5,691) scale(1,1) translate(0,0)" writing-mode="lr" x="853.5" xml:space="preserve" y="695.5" zvalue="196">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="141" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1405,691) scale(1,1) translate(0,0)" writing-mode="lr" x="1405" xml:space="preserve" y="695.5" zvalue="200">003</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="172" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,951,536) scale(1,1) translate(0,0)" writing-mode="lr" x="951" xml:space="preserve" y="540.5" zvalue="208">1010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="174" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1347,533) scale(1,1) translate(0,0)" writing-mode="lr" x="1347" xml:space="preserve" y="537.5" zvalue="210">1030</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="178" stroke="rgb(255,255,255)" text-anchor="middle" x="1080.75" xml:space="preserve" y="962.5" zvalue="213">10kV团结Ⅰ回</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="178" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1080.75" xml:space="preserve" y="978.5" zvalue="213">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="181" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1105.5,780) scale(1,1) translate(0,0)" writing-mode="lr" x="1105.5" xml:space="preserve" y="784.5" zvalue="216">059</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="185" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1097.5,883) scale(1,1) translate(0,0)" writing-mode="lr" x="1097.5" xml:space="preserve" y="887.5" zvalue="220">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="201" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,713.5,775) scale(1,1) translate(0,0)" writing-mode="lr" x="713.5" xml:space="preserve" y="779.5" zvalue="235">053</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="200" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,722.5,824) scale(1,1) translate(0,0)" writing-mode="lr" x="722.5" xml:space="preserve" y="828.5" zvalue="237">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="210" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,705.045,852) scale(1,1) translate(1.6298e-12,0)" writing-mode="lr" x="705.04" xml:space="preserve" y="856.5" zvalue="244">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="216" stroke="rgb(255,255,255)" text-anchor="middle" x="1014.75" xml:space="preserve" y="963.5" zvalue="249">10kV一</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="216" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1014.75" xml:space="preserve" y="979.5" zvalue="249">号线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="215" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1044.5,779) scale(1,1) translate(0,0)" writing-mode="lr" x="1044.5" xml:space="preserve" y="783.5" zvalue="251">058</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="214" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1032.5,881) scale(1,1) translate(0,0)" writing-mode="lr" x="1032.5" xml:space="preserve" y="885.5" zvalue="254">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="227" stroke="rgb(255,255,255)" text-anchor="middle" x="953.75" xml:space="preserve" y="963.5" zvalue="261">10kV四</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="227" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="953.75" xml:space="preserve" y="979.5" zvalue="261">号线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="226" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,984,779) scale(1,1) translate(0,0)" writing-mode="lr" x="984" xml:space="preserve" y="783.5" zvalue="263">057</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="225" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,970.5,881) scale(1,1) translate(0,0)" writing-mode="lr" x="970.5" xml:space="preserve" y="885.5" zvalue="266">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="238" stroke="rgb(255,255,255)" text-anchor="middle" x="894.25" xml:space="preserve" y="964.5" zvalue="273">10kV三</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="238" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="894.25" xml:space="preserve" y="980.5" zvalue="273">号线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="237" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,922,778) scale(1,1) translate(0,0)" writing-mode="lr" x="922" xml:space="preserve" y="782.5" zvalue="275">056</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="236" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,908.5,880) scale(1,1) translate(0,0)" writing-mode="lr" x="908.5" xml:space="preserve" y="884.5" zvalue="278">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="249" stroke="rgb(255,255,255)" text-anchor="middle" x="830.25" xml:space="preserve" y="965.5" zvalue="285">10kV二</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="249" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="830.25" xml:space="preserve" y="981.5" zvalue="285">号线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="248" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,862,777) scale(1,1) translate(0,0)" writing-mode="lr" x="862" xml:space="preserve" y="781.5" zvalue="287">055</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="247" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,848.5,879) scale(1,1) translate(0,0)" writing-mode="lr" x="848.5" xml:space="preserve" y="883.5" zvalue="290">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="260" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,798,775) scale(1,1) translate(0,0)" writing-mode="lr" x="798" xml:space="preserve" y="779.5" zvalue="299">054</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="259" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,804.5,824) scale(1,1) translate(0,0)" writing-mode="lr" x="804.5" xml:space="preserve" y="828.5" zvalue="301">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="258" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,783.045,852) scale(1,1) translate(0,0)" writing-mode="lr" x="783.04" xml:space="preserve" y="856.5" zvalue="306">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="276" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,621.306,780) scale(1,1) translate(0,0)" writing-mode="lr" x="621.3099999999999" xml:space="preserve" y="784.5" zvalue="313">052</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="275" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,613.5,883) scale(1,1) translate(0,0)" writing-mode="lr" x="613.5" xml:space="preserve" y="887.5" zvalue="316">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="274" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,541.5,779) scale(1,1) translate(0,0)" writing-mode="lr" x="541.5" xml:space="preserve" y="783.5" zvalue="321">051</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="273" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,528.5,881) scale(1,1) translate(0,0)" writing-mode="lr" x="528.5" xml:space="preserve" y="885.5" zvalue="324">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" x="516.25" xml:space="preserve" y="963.5" zvalue="333">10kV团结Ⅱ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="516.25" xml:space="preserve" y="979.5" zvalue="333">回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,570.75,879.375) scale(1,1) translate(0,0)" writing-mode="lr" x="570.75" xml:space="preserve" y="883.88" zvalue="338">0520</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="192" stroke="rgb(255,255,255)" text-anchor="middle" x="442.75" xml:space="preserve" y="966" zvalue="343">10kV雅居乐Ⅰ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="192" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="442.75" xml:space="preserve" y="982" zvalue="343">回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="190" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,471.5,778.5) scale(1,1) translate(0,0)" writing-mode="lr" x="471.5" xml:space="preserve" y="783" zvalue="345">049</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="189" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,465.5,881.5) scale(1,1) translate(0,0)" writing-mode="lr" x="465.5" xml:space="preserve" y="886" zvalue="348">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="294" stroke="rgb(255,255,255)" text-anchor="middle" x="445.75" xml:space="preserve" y="496" zvalue="356">10kV十一号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="294" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="445.75" xml:space="preserve" y="512" zvalue="356">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="293" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,473.5,688.5) scale(1,1) translate(0,0)" writing-mode="lr" x="473.5" xml:space="preserve" y="693" zvalue="358">048</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="292" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,465.5,585.5) scale(1,1) translate(0,0)" writing-mode="lr" x="465.5" xml:space="preserve" y="590" zvalue="361">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="306" stroke="rgb(255,255,255)" text-anchor="middle" x="1018.34375" xml:space="preserve" y="574.5" zvalue="368">10kVⅠ母电压互</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="306" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1018.34375" xml:space="preserve" y="590.5" zvalue="368">感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="291" stroke="rgb(255,255,255)" text-anchor="middle" x="1215.90625" xml:space="preserve" y="965.609375" zvalue="373">10kV广母Ⅰ回</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="291" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1215.90625" xml:space="preserve" y="981.609375" zvalue="373">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1249.67,781.111) scale(1,1) translate(1.37372e-13,0)" writing-mode="lr" x="1249.67" xml:space="preserve" y="785.61" zvalue="375">061</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1239.83,884.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1239.83" xml:space="preserve" y="888.61" zvalue="378">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="319" stroke="rgb(255,255,255)" text-anchor="middle" x="1288.2421875" xml:space="preserve" y="965.609375" zvalue="385">10kV广母Ⅱ回</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="319" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1288.2421875" xml:space="preserve" y="981.609375" zvalue="385">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="318" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1315.5,781.111) scale(1,1) translate(0,-1.37243e-12)" writing-mode="lr" x="1315.5" xml:space="preserve" y="785.61" zvalue="387">062</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="317" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1307.5,884.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1307.5" xml:space="preserve" y="888.61" zvalue="390">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="333" stroke="rgb(255,255,255)" text-anchor="middle" x="1357.4609375" xml:space="preserve" y="966.609375" zvalue="397">10kV姐</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="333" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1357.4609375" xml:space="preserve" y="982.609375" zvalue="397">东线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="332" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1383.94,781.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1383.94" xml:space="preserve" y="785.61" zvalue="399">063</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="331" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1375.94,884.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1375.94" xml:space="preserve" y="888.61" zvalue="402">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="330" stroke="rgb(255,255,255)" text-anchor="middle" x="1424.296875" xml:space="preserve" y="964.609375" zvalue="408">10kV金</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="330" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1424.296875" xml:space="preserve" y="980.609375" zvalue="408">滇线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="329" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1451.61,781.111) scale(1,1) translate(0,-1.37243e-12)" writing-mode="lr" x="1451.61" xml:space="preserve" y="785.61" zvalue="410">064</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="328" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1443.61,884.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1443.61" xml:space="preserve" y="888.61" zvalue="413">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="353" stroke="rgb(255,255,255)" text-anchor="middle" x="1485.4609375" xml:space="preserve" y="965.609375" zvalue="421">10kV景成医</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="353" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1485.4609375" xml:space="preserve" y="981.609375" zvalue="421">院线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="352" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1511.94,781.111) scale(1,1) translate(0,-1.37243e-12)" writing-mode="lr" x="1511.94" xml:space="preserve" y="785.61" zvalue="423">065</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="351" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1503.94,884.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1503.94" xml:space="preserve" y="888.61" zvalue="425">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="368" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1583.17,777.222) scale(1,1) translate(0,-1.36552e-12)" writing-mode="lr" x="1583.17" xml:space="preserve" y="781.72" zvalue="435">066</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="367" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1588.17,826.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1588.17" xml:space="preserve" y="830.72" zvalue="437">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="366" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1574.71,854.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1574.71" xml:space="preserve" y="858.72" zvalue="442">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="364" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1667.67,777.222) scale(1,1) translate(0,-1.36552e-12)" writing-mode="lr" x="1667.67" xml:space="preserve" y="781.72" zvalue="449">067</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="363" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1674.17,826.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1674.17" xml:space="preserve" y="830.72" zvalue="451">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="362" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1652.71,854.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1652.71" xml:space="preserve" y="858.72" zvalue="455">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="393" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1749.39,780) scale(1,1) translate(0,0)" writing-mode="lr" x="1749.39" xml:space="preserve" y="784.5" zvalue="462">068</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="392" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1741.39,883) scale(1,1) translate(0,0)" writing-mode="lr" x="1741.39" xml:space="preserve" y="887.5" zvalue="465">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="391" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1698.64,887.375) scale(1,1) translate(0,0)" writing-mode="lr" x="1698.64" xml:space="preserve" y="891.87" zvalue="473">0680</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="409" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1815.5,781.222) scale(1,1) translate(0,-1.37352e-12)" writing-mode="lr" x="1815.5" xml:space="preserve" y="785.72" zvalue="480">069</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="408" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1803.28,883.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1803.28" xml:space="preserve" y="887.72" zvalue="483">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="407" stroke="rgb(255,255,255)" text-anchor="middle" x="1792.625" xml:space="preserve" y="967.71875" zvalue="488">10kV滇</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="407" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1792.625" xml:space="preserve" y="983.71875" zvalue="488">沙线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="420" stroke="rgb(255,255,255)" text-anchor="middle" x="1857.125" xml:space="preserve" y="969.25" zvalue="492">10kV雅居乐Ⅱ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="420" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1857.125" xml:space="preserve" y="985.25" zvalue="492">回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="419" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1879.28,780.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1879.28" xml:space="preserve" y="785.25" zvalue="494">071</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="418" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1873.28,883.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1873.28" xml:space="preserve" y="888.25" zvalue="497">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="429" stroke="rgb(255,255,255)" text-anchor="middle" x="1290.8515625" xml:space="preserve" y="571.9305555555555" zvalue="506">10kVⅢ母电压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="429" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1290.8515625" xml:space="preserve" y="587.9305555555555" zvalue="506">互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="437" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1143.89,682.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1143.89" xml:space="preserve" y="686.83" zvalue="511">013</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="439" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1205.86,693.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1205.86" xml:space="preserve" y="697.61" zvalue="513">3</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="438" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1144.69,639.472) scale(1,1) translate(0,0)" writing-mode="lr" x="1144.69" xml:space="preserve" y="643.97" zvalue="514">10kV分段</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="445" stroke="rgb(255,255,255)" text-anchor="middle" x="597.3828125" xml:space="preserve" y="961.0329861111111" zvalue="521">10kV#1站用变兼</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="445" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="597.3828125" xml:space="preserve" y="977.0329861111111" zvalue="521">接地变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="449" stroke="rgb(255,255,255)" text-anchor="middle" x="1722.765625" xml:space="preserve" y="977.295138888889" zvalue="525">10kV#3站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="449" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1722.765625" xml:space="preserve" y="993.295138888889" zvalue="525">兼接地变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" x="684.171875" xml:space="preserve" y="958.25" zvalue="533">10kV1号电</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="684.171875" xml:space="preserve" y="974.25" zvalue="533">容器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="211" stroke="rgb(255,255,255)" text-anchor="middle" x="762.921875" xml:space="preserve" y="957.25" zvalue="540">10kV2号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="211" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="762.921875" xml:space="preserve" y="973.25" zvalue="540">电容器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="339" stroke="rgb(255,255,255)" text-anchor="middle" x="1557.8046875" xml:space="preserve" y="958.75" zvalue="544">10kV3号电</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="339" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1557.8046875" xml:space="preserve" y="974.75" zvalue="544">容器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="396" stroke="rgb(255,255,255)" text-anchor="middle" x="1634.6953125" xml:space="preserve" y="958.734375" zvalue="548">10kV4号电</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="396" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1634.6953125" xml:space="preserve" y="974.734375" zvalue="548">容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="271" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,983,684) scale(1,1) translate(0,0)" writing-mode="lr" x="983" xml:space="preserve" y="688.5" zvalue="556">0901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="372" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1262,683) scale(1,1) translate(0,0)" writing-mode="lr" x="1262" xml:space="preserve" y="687.5" zvalue="560">0903</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="261" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,89.5,652.5) scale(1,1) translate(0,0)" writing-mode="lr" x="89.5" xml:space="preserve" y="657" zvalue="565">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="179" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.399,319.841) scale(1,1) translate(0,0)" writing-mode="lr" x="202.4" xml:space="preserve" y="324.34" zvalue="567">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="197" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.399,319.841) scale(1,1) translate(0,0)" writing-mode="lr" x="307.4" xml:space="preserve" y="324.34" zvalue="568">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="304" stroke="rgb(255,255,255)" text-anchor="middle" x="189.8359375" xml:space="preserve" y="460" zvalue="572">110kV Ⅱ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="304" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="189.8359375" xml:space="preserve" y="476" zvalue="572">母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="309" stroke="rgb(255,255,255)" text-anchor="middle" x="248.671875" xml:space="preserve" y="460" zvalue="574">10kV Ⅰ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="309" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="248.671875" xml:space="preserve" y="476" zvalue="574">母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="308" stroke="rgb(255,255,255)" text-anchor="middle" x="308.5" xml:space="preserve" y="461" zvalue="575">10kV Ⅲ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="308" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="308.5" xml:space="preserve" y="477" zvalue="575">母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="382" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,81,495.5) scale(1,1) translate(0,0)" writing-mode="lr" x="81" xml:space="preserve" y="500" zvalue="577">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="389" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,81,521) scale(1,1) translate(0,0)" writing-mode="lr" x="81" xml:space="preserve" y="525.5" zvalue="579">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="430" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,81,546.5) scale(1,1) translate(0,0)" writing-mode="lr" x="81" xml:space="preserve" y="551" zvalue="581">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="431" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,81,572) scale(1,1) translate(0,0)" writing-mode="lr" x="81" xml:space="preserve" y="576.5" zvalue="583">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="432" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,81,597.5) scale(1,1) translate(0,0)" writing-mode="lr" x="81" xml:space="preserve" y="602" zvalue="585">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="471" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,249.054,960) scale(1,1) translate(0,0)" writing-mode="lr" x="249.05" xml:space="preserve" y="966" zvalue="614">DianNong-01-2022</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="474" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,63,179) scale(1,1) translate(0,0)" writing-mode="lr" x="63" xml:space="preserve" y="184.5" zvalue="620">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="475" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,243,179) scale(1,1) translate(0,0)" writing-mode="lr" x="243" xml:space="preserve" y="184.5" zvalue="622">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="478" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,66.6875,203.25) scale(1,1) translate(0,0)" writing-mode="lr" x="66.69" xml:space="preserve" y="207.75" zvalue="627">110kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="479" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,249.75,203) scale(1,1) translate(0,0)" writing-mode="lr" x="249.75" xml:space="preserve" y="207.5" zvalue="629">110kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="482" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,70.1875,251) scale(1,1) translate(0,0)" writing-mode="lr" x="70.19" xml:space="preserve" y="255.5" zvalue="634">110kV#1变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="483" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,251.75,250.5) scale(1,1) translate(0,0)" writing-mode="lr" x="251.75" xml:space="preserve" y="255" zvalue="635">110kV#3变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="487" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,70.1875,274) scale(1,1) translate(0,0)" writing-mode="lr" x="70.19" xml:space="preserve" y="278.5" zvalue="640">110kV#1变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="486" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,251.75,273.5) scale(1,1) translate(0,0)" writing-mode="lr" x="251.75" xml:space="preserve" y="278" zvalue="641">110kV#3变档位</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="492" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,799,229) scale(1,1) translate(0,0)" writing-mode="lr" x="799" xml:space="preserve" y="233.5" zvalue="646">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="497" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,917.5,231) scale(1,1) translate(0,0)" writing-mode="lr" x="917.5" xml:space="preserve" y="235.5" zvalue="650">192</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="503" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1146,228.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1146" xml:space="preserve" y="233" zvalue="655">110kV分段112</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="508" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1328.5,230) scale(1,1) translate(0,0)" writing-mode="lr" x="1328.5" xml:space="preserve" y="234.5" zvalue="659">193</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="513" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1471.5,230) scale(1,1) translate(0,0)" writing-mode="lr" x="1471.5" xml:space="preserve" y="234.5" zvalue="663">103</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,67.6875,227.25) scale(1,1) translate(0,0)" writing-mode="lr" x="67.69" xml:space="preserve" y="231.75" zvalue="668">10kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,250.75,227) scale(1,1) translate(0,0)" writing-mode="lr" x="250.75" xml:space="preserve" y="231.5" zvalue="669">10kVⅢ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="505" stroke="rgb(255,255,255)" text-anchor="middle" x="514.25" xml:space="preserve" y="496" zvalue="695">10kV联</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="505" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="514.25" xml:space="preserve" y="512" zvalue="695">检线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="504" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,543.5,688.5) scale(1,1) translate(0,0)" writing-mode="lr" x="543.5" xml:space="preserve" y="693" zvalue="697">047</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="501" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,532.5,585.5) scale(1,1) translate(0,0)" writing-mode="lr" x="532.5" xml:space="preserve" y="590" zvalue="699">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="525" stroke="rgb(255,255,255)" text-anchor="middle" x="593.25" xml:space="preserve" y="495" zvalue="707">10kV新建</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="525" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="593.25" xml:space="preserve" y="511" zvalue="707">路线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="524" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,624,687.5) scale(1,1) translate(0,0)" writing-mode="lr" x="624" xml:space="preserve" y="692" zvalue="709">046</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="523" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,611.5,584.5) scale(1,1) translate(0,0)" writing-mode="lr" x="611.5" xml:space="preserve" y="589" zvalue="711">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="536" stroke="rgb(255,255,255)" text-anchor="middle" x="690.75" xml:space="preserve" y="495" zvalue="719">10kV备用Ⅲ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="536" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="690.75" xml:space="preserve" y="511" zvalue="719">回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="535" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,721,687.5) scale(1,1) translate(0,0)" writing-mode="lr" x="721" xml:space="preserve" y="692" zvalue="721">045</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="534" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,708.5,584.5) scale(1,1) translate(0,0)" writing-mode="lr" x="708.5" xml:space="preserve" y="589" zvalue="723">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="556" stroke="rgb(255,255,255)" text-anchor="middle" x="768.25" xml:space="preserve" y="495" zvalue="741">10kV备用Ⅳ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="556" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="768.25" xml:space="preserve" y="511" zvalue="741">回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="555" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,799,687.5) scale(1,1) translate(0,0)" writing-mode="lr" x="799" xml:space="preserve" y="692" zvalue="743">044</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="554" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,786.5,584.5) scale(1,1) translate(0,0)" writing-mode="lr" x="786.5" xml:space="preserve" y="589" zvalue="745">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="579" stroke="rgb(255,255,255)" text-anchor="middle" x="1558.25" xml:space="preserve" y="496" zvalue="753">10kV备用Ⅸ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="579" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1558.25" xml:space="preserve" y="512" zvalue="753">回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="578" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1587.5,688.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1587.5" xml:space="preserve" y="693" zvalue="755">076</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="577" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1579.5,585.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1579.5" xml:space="preserve" y="590" zvalue="757">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="576" stroke="rgb(255,255,255)" text-anchor="middle" x="1638.75" xml:space="preserve" y="496" zvalue="764">10kV备用Ⅷ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="576" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1638.75" xml:space="preserve" y="512" zvalue="764">回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="575" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1667.5,687.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1667.5" xml:space="preserve" y="692" zvalue="766">075</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="574" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1655.5,584.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1655.5" xml:space="preserve" y="589" zvalue="768">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="573" stroke="rgb(255,255,255)" text-anchor="middle" x="1721.25" xml:space="preserve" y="492" zvalue="775">10kV备用Ⅶ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="573" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1721.25" xml:space="preserve" y="508" zvalue="775">回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="572" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1753,685.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1753" xml:space="preserve" y="690" zvalue="777">074</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="571" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1740.5,582.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1740.5" xml:space="preserve" y="587" zvalue="779">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="570" stroke="rgb(255,255,255)" text-anchor="middle" x="1789.75" xml:space="preserve" y="494" zvalue="786">10kV备用Ⅵ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="570" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1789.75" xml:space="preserve" y="510" zvalue="786">回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="569" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1819,686.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1819" xml:space="preserve" y="691" zvalue="788">073</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="568" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1806.5,583.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1806.5" xml:space="preserve" y="588" zvalue="790">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="567" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1855.75,497.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1855.75" xml:space="preserve" y="502" zvalue="797">10kV飞海线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="566" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1880.89,686.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1880.89" xml:space="preserve" y="691" zvalue="799">072</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="565" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1872.5,583.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1872.5" xml:space="preserve" y="588" zvalue="801">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="623" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,158.054,1020) scale(1,1) translate(0,0)" writing-mode="lr" x="158.05" xml:space="preserve" y="1026" zvalue="970">段勇</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="625" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,338.054,1020) scale(1,1) translate(0,0)" writing-mode="lr" x="338.05" xml:space="preserve" y="1026" zvalue="971">20220714</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,296.235,405) scale(1,1) translate(0,0)" writing-mode="lr" x="296.2350463867188" xml:space="preserve" y="409.5" zvalue="1200">小电流接地</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="218" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,93.2857,323.539) scale(1,1) translate(0,3.46878e-13)" writing-mode="lr" x="93.28570556640625" xml:space="preserve" y="328.0388641357421" zvalue="1201">全站公用</text>
  <ellipse cx="1511.66" cy="230.66" fill="rgb(255,0,0)" fill-opacity="1" id="229" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1203"/>
  <ellipse cx="836.66" cy="230.66" fill="rgb(255,0,0)" fill-opacity="1" id="240" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1205"/>
  <ellipse cx="954.66" cy="230.66" fill="rgb(255,0,0)" fill-opacity="1" id="251" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1207"/>
  <ellipse cx="1366.66" cy="230.66" fill="rgb(255,0,0)" fill-opacity="1" id="283" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1211"/>
  <ellipse cx="1148.66" cy="266.66" fill="rgb(255,0,0)" fill-opacity="1" id="321" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1217"/>
 </g>
 <g id="ButtonClass">
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="156.09" y="392.25" zvalue="981"/></g>
  <g href="单厂站信息-20210617.svg"><rect fill-opacity="0" height="24" width="72.88" x="54.19" y="392.25" zvalue="982"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="54.19" y="351.75" zvalue="983"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="17">
   <path class="kv110" d="M 725.5 307.5 L 1124.25 307.5" stroke-width="6" zvalue="35"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674395684867" ObjectName="110kVⅠ母"/>
   <cge:TPSR_Ref TObjectID="9288674395684867"/></metadata>
  <path d="M 725.5 307.5 L 1124.25 307.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="20">
   <path class="kv110" d="M 1179.88 307.75 L 1578.63 307.75" stroke-width="6" zvalue="37"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674395750403" ObjectName="110kVⅡ母"/>
   <cge:TPSR_Ref TObjectID="9288674395750403"/></metadata>
  <path d="M 1179.88 307.75 L 1578.63 307.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="163">
   <path class="kv10" d="M 403 734.5 L 1133.56 734.5" stroke-width="6" zvalue="182"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674395815939" ObjectName="10kVⅠ母"/>
   <cge:TPSR_Ref TObjectID="9288674395815939"/></metadata>
  <path d="M 403 734.5 L 1133.56 734.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="164">
   <path class="kv10" d="M 1167.72 734.75 L 1897 734.75" stroke-width="6" zvalue="183"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674395881475" ObjectName="10kVⅢ母"/>
   <cge:TPSR_Ref TObjectID="9288674395881475"/></metadata>
  <path d="M 1167.72 734.75 L 1897 734.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="21">
   <use class="kv110" height="40" transform="rotate(0,943.5,115.5) scale(1.25,1.25) translate(-184.325,-18.1)" width="35" x="921.625" xlink:href="#ACLineSegment:220kV线路_0" y="90.5" zvalue="39"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249320062981" ObjectName="110kV汉滇线"/>
   <cge:TPSR_Ref TObjectID="8444249320062981_5066549677916161"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,943.5,115.5) scale(1.25,1.25) translate(-184.325,-18.1)" width="35" x="921.625" y="90.5"/></g>
  <g id="87">
   <use class="kv110" height="40" transform="rotate(0,1354,115.5) scale(1.25,1.25) translate(-266.425,-18.1)" width="35" x="1332.125" xlink:href="#ACLineSegment:220kV线路_0" y="90.5" zvalue="83"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249320128517" ObjectName="110kV瑞滇线"/>
   <cge:TPSR_Ref TObjectID="8444249320128517_5066549677916161"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1354,115.5) scale(1.25,1.25) translate(-266.425,-18.1)" width="35" x="1332.125" y="90.5"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="32">
   <use class="kv110" height="30" transform="rotate(0,938.5,277.75) scale(0.833333,0.833333) translate(186.45,53.05)" width="15" x="932.25" xlink:href="#Disconnector:刀闸_0" y="265.25" zvalue="48"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453653430276" ObjectName="110kV汉滇线1921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453653430276"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,938.5,277.75) scale(0.833333,0.833333) translate(186.45,53.05)" width="15" x="932.25" y="265.25"/></g>
  <g id="36">
   <use class="kv110" height="30" transform="rotate(0,938.5,176.25) scale(0.833333,0.833333) translate(186.45,32.75)" width="15" x="932.25" xlink:href="#Disconnector:刀闸_0" y="163.75" zvalue="52"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453653495812" ObjectName="110kV汉滇线1926隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453653495812"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,938.5,176.25) scale(0.833333,0.833333) translate(186.45,32.75)" width="15" x="932.25" y="163.75"/></g>
  <g id="65">
   <use class="kv110" height="30" transform="rotate(0,819.75,277.75) scale(0.833333,0.833333) translate(162.7,53.05)" width="15" x="813.5" xlink:href="#Disconnector:刀闸_0" y="265.25" zvalue="66"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453654413314" ObjectName="110k#1主变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453654413314"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,819.75,277.75) scale(0.833333,0.833333) translate(162.7,53.05)" width="15" x="813.5" y="265.25"/></g>
  <g id="62">
   <use class="kv110" height="30" transform="rotate(0,819.75,176.25) scale(0.833333,0.833333) translate(162.7,32.75)" width="15" x="813.5" xlink:href="#Disconnector:刀闸_0" y="163.75" zvalue="70"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453654347780" ObjectName="110k#1主变110kV侧1016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453654347780"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,819.75,176.25) scale(0.833333,0.833333) translate(162.7,32.75)" width="15" x="813.5" y="163.75"/></g>
  <g id="85">
   <use class="kv110" height="30" transform="rotate(0,1349,277.75) scale(0.833333,0.833333) translate(268.55,53.05)" width="15" x="1342.75" xlink:href="#Disconnector:刀闸_0" y="265.25" zvalue="87"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453654937602" ObjectName="110kV瑞滇线1932隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453654937602"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1349,277.75) scale(0.833333,0.833333) translate(268.55,53.05)" width="15" x="1342.75" y="265.25"/></g>
  <g id="82">
   <use class="kv110" height="30" transform="rotate(0,1349,176.25) scale(0.833333,0.833333) translate(268.55,32.75)" width="15" x="1342.75" xlink:href="#Disconnector:刀闸_0" y="163.75" zvalue="91"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453654872068" ObjectName="110kV瑞滇线1936隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453654872068"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1349,176.25) scale(0.833333,0.833333) translate(268.55,32.75)" width="15" x="1342.75" y="163.75"/></g>
  <g id="104">
   <use class="kv110" height="30" transform="rotate(0,1493,277.75) scale(0.833333,0.833333) translate(297.35,53.05)" width="15" x="1486.75" xlink:href="#Disconnector:刀闸_0" y="265.25" zvalue="107"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453655527428" ObjectName="110k#3主变110kV侧1032隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453655527428"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1493,277.75) scale(0.833333,0.833333) translate(297.35,53.05)" width="15" x="1486.75" y="265.25"/></g>
  <g id="101">
   <use class="kv110" height="30" transform="rotate(0,1493,176.25) scale(0.833333,0.833333) translate(297.35,32.75)" width="15" x="1486.75" xlink:href="#Disconnector:刀闸_0" y="163.75" zvalue="111"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453655461892" ObjectName="110k#3主变110kV侧1036隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453655461892"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1493,176.25) scale(0.833333,0.833333) translate(297.35,32.75)" width="15" x="1486.75" y="163.75"/></g>
  <g id="118">
   <use class="kv110" height="30" transform="rotate(0,1083.5,279.25) scale(0.833333,0.833333) translate(215.45,53.35)" width="15" x="1077.25" xlink:href="#Disconnector:刀闸_0" y="266.75" zvalue="135"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453655592964" ObjectName="110kV分段1121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453655592964"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1083.5,279.25) scale(0.833333,0.833333) translate(215.45,53.35)" width="15" x="1077.25" y="266.75"/></g>
  <g id="120">
   <use class="kv110" height="30" transform="rotate(0,1217.5,279.25) scale(0.833333,0.833333) translate(242.25,53.35)" width="15" x="1211.25" xlink:href="#Disconnector:刀闸_0" y="266.75" zvalue="137"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453655658500" ObjectName="110kV分段1122隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453655658500"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1217.5,279.25) scale(0.833333,0.833333) translate(242.25,53.35)" width="15" x="1211.25" y="266.75"/></g>
  <g id="146">
   <use class="kv110" height="30" transform="rotate(0,1046,373) scale(0.833333,0.833333) translate(207.95,72.1)" width="15" x="1039.75" xlink:href="#Disconnector:刀闸_0" y="360.5" zvalue="155"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453656313860" ObjectName="110kVⅠ母电压互感器1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453656313860"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1046,373) scale(0.833333,0.833333) translate(207.95,72.1)" width="15" x="1039.75" y="360.5"/></g>
  <g id="160">
   <use class="kv110" height="30" transform="rotate(0,1259.75,373) scale(0.833333,0.833333) translate(250.7,72.1)" width="15" x="1253.5" xlink:href="#Disconnector:刀闸_0" y="360.5" zvalue="171"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453656641540" ObjectName="110kVⅡ母电压互感器1902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453656641540"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1259.75,373) scale(0.833333,0.833333) translate(250.7,72.1)" width="15" x="1253.5" y="360.5"/></g>
  <g id="209">
   <use class="kv10" height="30" transform="rotate(0,681.277,851.5) scale(1.17857,1.1) translate(-100.992,-75.9091)" width="25" x="666.5446428571429" xlink:href="#Disconnector:双联接地刀_0" y="835" zvalue="243"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453657624580" ObjectName="10kV1号电容器0536隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453657624580"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,681.277,851.5) scale(1.17857,1.1) translate(-100.992,-75.9091)" width="25" x="666.5446428571429" y="835"/></g>
  <g id="265">
   <use class="kv10" height="30" transform="rotate(0,759.277,851.5) scale(1.17857,1.1) translate(-112.81,-75.9091)" width="25" x="744.5446428571429" xlink:href="#Disconnector:双联接地刀_0" y="835" zvalue="305"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453658738692" ObjectName="10kV2号电容器0546隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453658738692"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,759.277,851.5) scale(1.17857,1.1) translate(-112.81,-75.9091)" width="25" x="744.5446428571429" y="835"/></g>
  <g id="56">
   <use class="kv10" height="30" transform="rotate(90,568.75,893.625) scale(0.666667,0.6) translate(281.875,589.75)" width="15" x="563.75" xlink:href="#Disconnector:刀闸_0" y="884.625" zvalue="337"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453663391746" ObjectName="10kV#1站用变兼接地变0520隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453663391746"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,568.75,893.625) scale(0.666667,0.6) translate(281.875,589.75)" width="15" x="563.75" y="884.625"/></g>
  <g id="383">
   <use class="kv10" height="30" transform="rotate(0,1550.94,853.722) scale(1.17857,1.1) translate(-232.759,-76.1111)" width="25" x="1536.21130952381" xlink:href="#Disconnector:双联接地刀_0" y="837.2222222222222" zvalue="441"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453661818884" ObjectName="10kV3号电容器0666隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453661818884"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1550.94,853.722) scale(1.17857,1.1) translate(-232.759,-76.1111)" width="25" x="1536.21130952381" y="837.2222222222222"/></g>
  <g id="374">
   <use class="kv10" height="30" transform="rotate(0,1628.94,853.722) scale(1.17857,1.1) translate(-244.577,-76.1111)" width="25" x="1614.21130952381" xlink:href="#Disconnector:双联接地刀_0" y="837.2222222222222" zvalue="454"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453661556740" ObjectName="10kV4号电容器0676隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453661556740"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1628.94,853.722) scale(1.17857,1.1) translate(-244.577,-76.1111)" width="25" x="1614.21130952381" y="837.2222222222222"/></g>
  <g id="397">
   <use class="kv10" height="30" transform="rotate(90,1697.64,900.625) scale(0.666667,0.6) translate(846.319,594.417)" width="15" x="1692.638888888889" xlink:href="#Disconnector:刀闸_0" y="891.6249999999999" zvalue="472"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453663457282" ObjectName="10kV#3站用变兼接地变0680隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453663457282"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1697.64,900.625) scale(0.666667,0.6) translate(846.319,594.417)" width="15" x="1692.638888888889" y="891.6249999999999"/></g>
  <g id="436">
   <use class="kv10" height="30" transform="rotate(0,1225.44,694.111) scale(0.833333,0.833333) translate(243.839,136.322)" width="15" x="1219.194444444445" xlink:href="#Disconnector:刀闸_0" y="681.6111111111111" zvalue="512"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453663064066" ObjectName="10kV分段0133隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453663064066"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1225.44,694.111) scale(0.833333,0.833333) translate(243.839,136.322)" width="15" x="1219.194444444445" y="681.6111111111111"/></g>
  <g id="263">
   <use class="kv10" height="36" transform="rotate(0,1016,683) scale(1,1) translate(0,0)" width="14" x="1009" xlink:href="#Disconnector:手车刀闸_0" y="665" zvalue="555"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453660114947" ObjectName="10kVⅠ母电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453660114947"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1016,683) scale(1,1) translate(0,0)" width="14" x="1009" y="665"/></g>
  <g id="373">
   <use class="kv10" height="36" transform="rotate(0,1290,684) scale(1,1) translate(0,0)" width="14" x="1283" xlink:href="#Disconnector:手车刀闸_0" y="666" zvalue="559"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453685870594" ObjectName="10kVⅠ母电压互感器0903隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453685870594"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1290,684) scale(1,1) translate(0,0)" width="14" x="1283" y="666"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="35">
   <path class="kv110" d="M 938.55 290.04 L 938.55 307.5" stroke-width="1" zvalue="50"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="32@1" LinkObjectIDznd="17@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 938.55 290.04 L 938.55 307.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="38">
   <path class="kv110" d="M 939.39 139.26 L 939.39 164.16" stroke-width="1" zvalue="53"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="21@0" LinkObjectIDznd="36@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 939.39 139.26 L 939.39 164.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="46">
   <path class="kv110" d="M 954.56 154.94 L 939.39 154.94" stroke-width="1" zvalue="60"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44@0" LinkObjectIDznd="38" MaxPinNum="2"/>
   </metadata>
  <path d="M 954.56 154.94 L 939.39 154.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="kv110" d="M 954.56 203.44 L 938.5 203.44" stroke-width="1" zvalue="61"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="40@0" LinkObjectIDznd="498" MaxPinNum="2"/>
   </metadata>
  <path d="M 954.56 203.44 L 938.5 203.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="kv110" d="M 954.56 256.19 L 938.09 256.19" stroke-width="1" zvalue="62"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="42@0" LinkObjectIDznd="499" MaxPinNum="2"/>
   </metadata>
  <path d="M 954.56 256.19 L 938.09 256.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="kv110" d="M 819.8 290.04 L 819.8 307.5" stroke-width="1" zvalue="69"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@1" LinkObjectIDznd="17@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 819.8 290.04 L 819.8 307.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="kv110" d="M 1349.05 290.04 L 1349.05 307.75" stroke-width="1" zvalue="90"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="85@1" LinkObjectIDznd="20@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1349.05 290.04 L 1349.05 307.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="81">
   <path class="kv110" d="M 1349.89 139.26 L 1349.89 164.16" stroke-width="1" zvalue="93"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="87@0" LinkObjectIDznd="82@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1349.89 139.26 L 1349.89 164.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="kv110" d="M 1365.06 154.94 L 1349.89 154.94" stroke-width="1" zvalue="101"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="77@0" LinkObjectIDznd="81" MaxPinNum="2"/>
   </metadata>
  <path d="M 1365.06 154.94 L 1349.89 154.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="75">
   <path class="kv110" d="M 1365.06 203.44 L 1348.98 203.44" stroke-width="1" zvalue="102"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="79@0" LinkObjectIDznd="509" MaxPinNum="2"/>
   </metadata>
  <path d="M 1365.06 203.44 L 1348.98 203.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="kv110" d="M 1365.06 256.19 L 1349.03 256.19" stroke-width="1" zvalue="103"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="78@0" LinkObjectIDznd="510" MaxPinNum="2"/>
   </metadata>
  <path d="M 1365.06 256.19 L 1349.03 256.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="102">
   <path class="kv110" d="M 1493.05 290.04 L 1493.05 307.75" stroke-width="1" zvalue="110"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="104@1" LinkObjectIDznd="20@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1493.05 290.04 L 1493.05 307.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv110" d="M 880.79 450.45 L 880.79 126.25 L 819.75 126.25 L 819.82 164.16" stroke-width="1" zvalue="124"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="106@0" LinkObjectIDznd="62@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 880.79 450.45 L 880.79 126.25 L 819.75 126.25 L 819.82 164.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="111">
   <path class="kv110" d="M 1431.54 450.45 L 1431.54 126.25 L 1493 126.25 L 1493.07 164.16" stroke-width="1" zvalue="127"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109@0" LinkObjectIDznd="101@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1431.54 450.45 L 1431.54 126.25 L 1493 126.25 L 1493.07 164.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="kv110" d="M 804.19 154.94 L 819.81 154.94" stroke-width="1" zvalue="128"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="58@0" LinkObjectIDznd="108" MaxPinNum="2"/>
   </metadata>
  <path d="M 804.19 154.94 L 819.81 154.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv110" d="M 804.19 203.44 L 819.75 203.44" stroke-width="1" zvalue="129"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60@0" LinkObjectIDznd="493" MaxPinNum="2"/>
   </metadata>
  <path d="M 804.19 203.44 L 819.75 203.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="114">
   <path class="kv110" d="M 804.19 256.19 L 820.09 256.19" stroke-width="1" zvalue="130"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="59@0" LinkObjectIDznd="494" MaxPinNum="2"/>
   </metadata>
  <path d="M 804.19 256.19 L 820.09 256.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="115">
   <path class="kv110" d="M 1509.06 154.94 L 1493.06 154.94" stroke-width="1" zvalue="131"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="97@0" LinkObjectIDznd="111" MaxPinNum="2"/>
   </metadata>
  <path d="M 1509.06 154.94 L 1493.06 154.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="116">
   <path class="kv110" d="M 1509.06 203.44 L 1493.03 203.44" stroke-width="1" zvalue="132"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="514" MaxPinNum="2"/>
   </metadata>
  <path d="M 1509.06 203.44 L 1493.03 203.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="kv110" d="M 1509.06 256.19 L 1493.19 256.19" stroke-width="1" zvalue="133"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="98@0" LinkObjectIDznd="515" MaxPinNum="2"/>
   </metadata>
  <path d="M 1509.06 256.19 L 1493.19 256.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="122">
   <path class="kv110" d="M 1083.55 291.54 L 1083.55 307.5" stroke-width="1" zvalue="138"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="118@1" LinkObjectIDznd="17@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1083.55 291.54 L 1083.55 307.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="123">
   <path class="kv110" d="M 1217.55 291.54 L 1217.55 307.75" stroke-width="1" zvalue="139"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="120@1" LinkObjectIDznd="20@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1217.55 291.54 L 1217.55 307.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="133">
   <path class="kv110" d="M 1083.5 231.19 L 1083.57 267.16" stroke-width="1" zvalue="150"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="129@0" LinkObjectIDznd="118@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1083.5 231.19 L 1083.57 267.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="134">
   <path class="kv110" d="M 1217.5 231.19 L 1217.57 267.16" stroke-width="1" zvalue="151"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="132@0" LinkObjectIDznd="120@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1217.5 231.19 L 1217.57 267.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="147">
   <path class="kv110" d="M 1045.84 415 L 1045.84 385.29" stroke-width="1" zvalue="164"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="135@0" LinkObjectIDznd="146@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1045.84 415 L 1045.84 385.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="148">
   <path class="kv110" d="M 1046.07 360.91 L 1046.07 307.5" stroke-width="1" zvalue="165"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="146@0" LinkObjectIDznd="17@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1046.07 360.91 L 1046.07 307.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="kv110" d="M 1060.81 344.94 L 1046 344.94" stroke-width="1" zvalue="166"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="143@0" LinkObjectIDznd="148" MaxPinNum="2"/>
   </metadata>
  <path d="M 1060.81 344.94 L 1046 344.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="150">
   <path class="kv110" d="M 1060.81 401.44 L 1045.84 401.44" stroke-width="1" zvalue="167"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="144@0" LinkObjectIDznd="147" MaxPinNum="2"/>
   </metadata>
  <path d="M 1060.81 401.44 L 1045.84 401.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="157">
   <path class="kv110" d="M 1259.59 415 L 1259.59 385.29" stroke-width="1" zvalue="177"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@0" LinkObjectIDznd="160@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1259.59 415 L 1259.59 385.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="156">
   <path class="kv110" d="M 1274.56 344.94 L 1259.82 344.94" stroke-width="1" zvalue="178"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@0" LinkObjectIDznd="162" MaxPinNum="2"/>
   </metadata>
  <path d="M 1274.56 344.94 L 1259.82 344.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="155">
   <path class="kv110" d="M 1274.56 401.44 L 1259.59 401.44" stroke-width="1" zvalue="179"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="159@0" LinkObjectIDznd="157" MaxPinNum="2"/>
   </metadata>
  <path d="M 1274.56 401.44 L 1259.59 401.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="162">
   <path class="kv110" d="M 1259.82 360.91 L 1259.82 307.75" stroke-width="1" zvalue="180"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="160@0" LinkObjectIDznd="20@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1259.82 360.91 L 1259.82 307.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv10" d="M 880.75 553 L 880.75 673.5" stroke-width="1" zvalue="196"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="106@1" LinkObjectIDznd="94@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 880.75 553 L 880.75 673.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="kv10" d="M 880 710 L 880 734.5" stroke-width="1" zvalue="197"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="94@1" LinkObjectIDznd="163@14" MaxPinNum="2"/>
   </metadata>
  <path d="M 880 710 L 880 734.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="142">
   <path class="kv10" d="M 1431.5 553 L 1431.5 673.5" stroke-width="1" zvalue="200"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109@1" LinkObjectIDznd="137@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1431.5 553 L 1431.5 673.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="145">
   <path class="kv10" d="M 1431.5 710 L 1431.5 734.75" stroke-width="1" zvalue="201"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="137@1" LinkObjectIDznd="164@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1431.5 710 L 1431.5 734.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="168">
   <path class="kv10" d="M 889.25 634 L 880.75 634" stroke-width="1" zvalue="203"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="167@0" LinkObjectIDznd="96" MaxPinNum="2"/>
   </metadata>
  <path d="M 889.25 634 L 880.75 634" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="170">
   <path class="kv10" d="M 1439.25 637 L 1431.5 637" stroke-width="1" zvalue="206"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="169@0" LinkObjectIDznd="142" MaxPinNum="2"/>
   </metadata>
  <path d="M 1439.25 637 L 1431.5 637" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="175">
   <path class="kv110" d="M 880.77 475.5 L 952.6 475.5 L 952.6 493.8" stroke-width="1" zvalue="210"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="106@2" LinkObjectIDznd="171@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 880.77 475.5 L 952.6 475.5 L 952.6 493.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="176">
   <path class="kv110" d="M 1431.52 475.5 L 1346.6 475.5 L 1346.6 491.8" stroke-width="1" zvalue="211"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109@2" LinkObjectIDznd="173@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1431.52 475.5 L 1346.6 475.5 L 1346.6 491.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="183">
   <path class="kv10" d="M 1079 760.5 L 1079 734.5" stroke-width="1" zvalue="217"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="180@0" LinkObjectIDznd="163@13" MaxPinNum="2"/>
   </metadata>
  <path d="M 1079 760.5 L 1079 734.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="191">
   <path class="kv10" d="M 1079 797 L 1079 914.6" stroke-width="1" zvalue="225"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="180@1" LinkObjectIDznd="177@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1079 797 L 1079 914.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="194">
   <path class="kv10" d="M 1095.5 841.81 L 1095.5 822 L 1079 822" stroke-width="1" zvalue="228"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="184@0" LinkObjectIDznd="191" MaxPinNum="2"/>
   </metadata>
  <path d="M 1095.5 841.81 L 1095.5 822 L 1079 822" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="202">
   <path class="kv10" d="M 707.5 810.81 L 707.5 801 L 691 801" stroke-width="1" zvalue="240"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="205@0" LinkObjectIDznd="213" MaxPinNum="2"/>
   </metadata>
  <path d="M 707.5 810.81 L 707.5 801 L 691 801" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="207">
   <path class="kv10" d="M 691 755.5 L 691 734.5" stroke-width="1" zvalue="241"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="206@0" LinkObjectIDznd="163@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 691 755.5 L 691 734.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="208">
   <path class="kv10" d="M 691 792 L 691 801" stroke-width="1" zvalue="242"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="206@1" LinkObjectIDznd="202" MaxPinNum="2"/>
   </metadata>
  <path d="M 691 792 L 691 801" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="213">
   <path class="kv10" d="M 691 801 L 691 835.83" stroke-width="1" zvalue="246"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="208" LinkObjectIDznd="209@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 691 801 L 691 835.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="222">
   <path class="kv10" d="M 1017 759.5 L 1017 734.5" stroke-width="1" zvalue="252"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="223@0" LinkObjectIDznd="163@12" MaxPinNum="2"/>
   </metadata>
  <path d="M 1017 759.5 L 1017 734.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="219">
   <path class="kv10" d="M 1017 796 L 1017 915.6" stroke-width="1" zvalue="256"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="223@1" LinkObjectIDznd="224@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1017 796 L 1017 915.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="217">
   <path class="kv10" d="M 1032.5 840.81 L 1032.5 821 L 1017 821" stroke-width="1" zvalue="258"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="221@0" LinkObjectIDznd="219" MaxPinNum="2"/>
   </metadata>
  <path d="M 1032.5 840.81 L 1032.5 821 L 1017 821" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="233">
   <path class="kv10" d="M 955 759.5 L 955 734.5" stroke-width="1" zvalue="264"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="234@0" LinkObjectIDznd="163@11" MaxPinNum="2"/>
   </metadata>
  <path d="M 955 759.5 L 955 734.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="230">
   <path class="kv10" d="M 955 796 L 955 915.6" stroke-width="1" zvalue="268"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="234@1" LinkObjectIDznd="235@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 955 796 L 955 915.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="228">
   <path class="kv10" d="M 970.5 840.81 L 970.5 821 L 955 821" stroke-width="1" zvalue="270"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="232@0" LinkObjectIDznd="230" MaxPinNum="2"/>
   </metadata>
  <path d="M 970.5 840.81 L 970.5 821 L 955 821" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="244">
   <path class="kv10" d="M 892 758.5 L 892 734.5" stroke-width="1" zvalue="276"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="245@0" LinkObjectIDznd="163@10" MaxPinNum="2"/>
   </metadata>
  <path d="M 892 758.5 L 892 734.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="241">
   <path class="kv10" d="M 892 795 L 892 915.6" stroke-width="1" zvalue="280"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="245@1" LinkObjectIDznd="246@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 892 795 L 892 915.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="239">
   <path class="kv10" d="M 908.5 839.81 L 908.5 820 L 892 820" stroke-width="1" zvalue="282"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="243@0" LinkObjectIDznd="241" MaxPinNum="2"/>
   </metadata>
  <path d="M 908.5 839.81 L 908.5 820 L 892 820" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="255">
   <path class="kv10" d="M 832 757.5 L 832 734.5" stroke-width="1" zvalue="288"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="256@0" LinkObjectIDznd="163@9" MaxPinNum="2"/>
   </metadata>
  <path d="M 832 757.5 L 832 734.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="252">
   <path class="kv10" d="M 832 794 L 832 915.6" stroke-width="1" zvalue="292"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="256@1" LinkObjectIDznd="257@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 832 794 L 832 915.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="250">
   <path class="kv10" d="M 848.5 838.81 L 848.5 819 L 832 819" stroke-width="1" zvalue="294"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="254@0" LinkObjectIDznd="252" MaxPinNum="2"/>
   </metadata>
  <path d="M 848.5 838.81 L 848.5 819 L 832 819" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="268">
   <path class="kv10" d="M 789.5 810.81 L 789.5 801 L 769 801" stroke-width="1" zvalue="302"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="269@0" LinkObjectIDznd="272" MaxPinNum="2"/>
   </metadata>
  <path d="M 789.5 810.81 L 789.5 801 L 769 801" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="267">
   <path class="kv10" d="M 769 755.5 L 769 734.5" stroke-width="1" zvalue="303"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="270@0" LinkObjectIDznd="163@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 769 755.5 L 769 734.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="262">
   <path class="kv10" d="M 769 801 L 769 835.83" stroke-width="1" zvalue="309"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="268" LinkObjectIDznd="265@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 769 801 L 769 835.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="272">
   <path class="kv10" d="M 769 792 L 769 801" stroke-width="1" zvalue="310"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="270@1" LinkObjectIDznd="262" MaxPinNum="2"/>
   </metadata>
  <path d="M 769 792 L 769 801" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="287">
   <path class="kv10" d="M 594.81 760.5 L 594.81 734.5" stroke-width="1" zvalue="314"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="288@0" LinkObjectIDznd="163@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 594.81 760.5 L 594.81 734.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="281">
   <path class="kv10" d="M 515 759.5 L 515 734.5" stroke-width="1" zvalue="322"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="282@0" LinkObjectIDznd="163@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 515 759.5 L 515 734.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="277">
   <path class="kv10" d="M 528.5 840.81 L 528.5 821 L 516 821" stroke-width="1" zvalue="327"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="280@0" LinkObjectIDznd="31" MaxPinNum="2"/>
   </metadata>
  <path d="M 528.5 840.81 L 528.5 821 L 516 821" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="31">
   <path class="kv10" d="M 516 915.6 L 516 796" stroke-width="1" zvalue="334"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="30@0" LinkObjectIDznd="282@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 516 915.6 L 516 796" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="188">
   <path class="kv10" d="M 559.9 893.67 L 559.9 909.08" stroke-width="1" zvalue="340"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="56@1" LinkObjectIDznd="55@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 559.9 893.67 L 559.9 909.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="266">
   <path class="kv10" d="M 447 759 L 447 734.5" stroke-width="1" zvalue="346"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="289@0" LinkObjectIDznd="163@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 447 759 L 447 734.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="199">
   <path class="kv10" d="M 447 795.5 L 447 915.1" stroke-width="1" zvalue="350"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="289@1" LinkObjectIDznd="290@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 447 795.5 L 447 915.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="196">
   <path class="kv10" d="M 431 820.25 L 447 820.25" stroke-width="1" zvalue="351"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="203@0" LinkObjectIDznd="199" MaxPinNum="2"/>
   </metadata>
  <path d="M 431 820.25 L 447 820.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="195">
   <path class="kv10" d="M 463.5 840.31 L 463.5 820.5 L 447 820.5" stroke-width="1" zvalue="352"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="204@0" LinkObjectIDznd="199" MaxPinNum="2"/>
   </metadata>
  <path d="M 463.5 840.31 L 463.5 820.5 L 447 820.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="297">
   <path class="kv10" d="M 447 671.5 L 447 551.9" stroke-width="1" zvalue="363"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="301@1" LinkObjectIDznd="302@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 447 671.5 L 447 551.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="296">
   <path class="kv10" d="M 431 646.75 L 447 646.75" stroke-width="1" zvalue="364"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="298@0" LinkObjectIDznd="297" MaxPinNum="2"/>
   </metadata>
  <path d="M 431 646.75 L 447 646.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="295">
   <path class="kv10" d="M 463.5 626.69 L 463.5 646.5 L 447 646.5" stroke-width="1" zvalue="365"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="299@0" LinkObjectIDznd="297" MaxPinNum="2"/>
   </metadata>
  <path d="M 463.5 626.69 L 463.5 646.5 L 447 646.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="314">
   <path class="kv10" d="M 1221.33 761.61 L 1221.33 734.75" stroke-width="1" zvalue="376"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="315@0" LinkObjectIDznd="164@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1221.33 761.61 L 1221.33 734.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="311">
   <path class="kv10" d="M 1221.33 798.11 L 1221.33 916.71" stroke-width="1" zvalue="380"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="315@1" LinkObjectIDznd="316@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1221.33 798.11 L 1221.33 916.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="307">
   <path class="kv10" d="M 1237.83 842.92 L 1237.83 823.11 L 1221.33 823.11" stroke-width="1" zvalue="382"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="313@0" LinkObjectIDznd="311" MaxPinNum="2"/>
   </metadata>
  <path d="M 1237.83 842.92 L 1237.83 823.11 L 1221.33 823.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="325">
   <path class="kv10" d="M 1289 761.61 L 1289 734.75" stroke-width="1" zvalue="388"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="326@0" LinkObjectIDznd="164@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1289 761.61 L 1289 734.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="322">
   <path class="kv10" d="M 1289 798.11 L 1289 916.71" stroke-width="1" zvalue="392"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="326@1" LinkObjectIDznd="327@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1289 798.11 L 1289 916.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="320">
   <path class="kv10" d="M 1305.5 842.92 L 1305.5 823.11 L 1289 823.11" stroke-width="1" zvalue="394"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="324@0" LinkObjectIDznd="322" MaxPinNum="2"/>
   </metadata>
  <path d="M 1305.5 842.92 L 1305.5 823.11 L 1289 823.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="347">
   <path class="kv10" d="M 1357.44 761.61 L 1357.44 734.75" stroke-width="1" zvalue="400"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="348@0" LinkObjectIDznd="164@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1357.44 761.61 L 1357.44 734.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="344">
   <path class="kv10" d="M 1357.44 798.11 L 1357.44 916.71" stroke-width="1" zvalue="404"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="348@1" LinkObjectIDznd="349@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1357.44 798.11 L 1357.44 916.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="342">
   <path class="kv10" d="M 1373.94 842.92 L 1373.94 823.11 L 1357.44 823.11" stroke-width="1" zvalue="406"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="346@0" LinkObjectIDznd="344" MaxPinNum="2"/>
   </metadata>
  <path d="M 1373.94 842.92 L 1373.94 823.11 L 1357.44 823.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="336">
   <path class="kv10" d="M 1425.11 798.11 L 1425.11 916.71" stroke-width="1" zvalue="415"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="340@1" LinkObjectIDznd="341@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1425.11 798.11 L 1425.11 916.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="334">
   <path class="kv10" d="M 1441.61 842.92 L 1441.61 823.11 L 1425.11 823.11" stroke-width="1" zvalue="417"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="338@0" LinkObjectIDznd="336" MaxPinNum="2"/>
   </metadata>
  <path d="M 1441.61 842.92 L 1441.61 823.11 L 1425.11 823.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="350">
   <path class="kv10" d="M 1425.11 761.61 L 1425.11 734.75" stroke-width="1" zvalue="418"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="340@0" LinkObjectIDznd="164@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1425.11 761.61 L 1425.11 734.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="357">
   <path class="kv10" d="M 1485.44 798.11 L 1485.44 916.71" stroke-width="1" zvalue="427"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="360@1" LinkObjectIDznd="361@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1485.44 798.11 L 1485.44 916.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="355">
   <path class="kv10" d="M 1501.94 842.92 L 1501.94 823.11 L 1485.44 823.11" stroke-width="1" zvalue="429"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="359@0" LinkObjectIDznd="357" MaxPinNum="2"/>
   </metadata>
  <path d="M 1501.94 842.92 L 1501.94 823.11 L 1485.44 823.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="354">
   <path class="kv10" d="M 1485.44 761.61 L 1485.44 734.75" stroke-width="1" zvalue="430"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="360@0" LinkObjectIDznd="164@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1485.44 761.61 L 1485.44 734.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="386">
   <path class="kv10" d="M 1577.17 813.03 L 1577.17 803.22 L 1560.67 803.22" stroke-width="1" zvalue="438"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="387@0" LinkObjectIDznd="380" MaxPinNum="2"/>
   </metadata>
  <path d="M 1577.17 813.03 L 1577.17 803.22 L 1560.67 803.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="385">
   <path class="kv10" d="M 1560.67 757.72 L 1560.67 734.75" stroke-width="1" zvalue="439"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="388@0" LinkObjectIDznd="164@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1560.67 757.72 L 1560.67 734.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="384">
   <path class="kv10" d="M 1560.67 794.22 L 1560.67 803.22" stroke-width="1" zvalue="440"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="388@1" LinkObjectIDznd="386" MaxPinNum="2"/>
   </metadata>
  <path d="M 1560.67 794.22 L 1560.67 803.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="380">
   <path class="kv10" d="M 1560.67 803.22 L 1560.67 838.05" stroke-width="1" zvalue="445"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="384" LinkObjectIDznd="383@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1560.67 803.22 L 1560.67 838.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="376">
   <path class="kv10" d="M 1659.17 813.03 L 1659.17 803.22 L 1638.67 803.22" stroke-width="1" zvalue="452"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="377@0" LinkObjectIDznd="370" MaxPinNum="2"/>
   </metadata>
  <path d="M 1659.17 813.03 L 1659.17 803.22 L 1638.67 803.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="375">
   <path class="kv10" d="M 1638.67 757.72 L 1638.67 734.75" stroke-width="1" zvalue="453"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="378@0" LinkObjectIDznd="164@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 1638.67 757.72 L 1638.67 734.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="371">
   <path class="kv10" d="M 1638.67 803.22 L 1638.67 838.05" stroke-width="1" zvalue="458"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="376" LinkObjectIDznd="374@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1638.67 803.22 L 1638.67 838.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="370">
   <path class="kv10" d="M 1638.67 794.22 L 1638.67 803.22" stroke-width="1" zvalue="459"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="378@1" LinkObjectIDznd="371" MaxPinNum="2"/>
   </metadata>
  <path d="M 1638.67 794.22 L 1638.67 803.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="405">
   <path class="kv10" d="M 1722.89 760.5 L 1722.89 734.75" stroke-width="1" zvalue="463"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="406@0" LinkObjectIDznd="164@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1722.89 760.5 L 1722.89 734.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="416">
   <path class="kv10" d="M 1789 761.72 L 1789 734.75" stroke-width="1" zvalue="481"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="417@0" LinkObjectIDznd="164@9" MaxPinNum="2"/>
   </metadata>
  <path d="M 1789 761.72 L 1789 734.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="412">
   <path class="kv10" d="M 1803.28 843.03 L 1803.28 823.22 L 1790.78 823.22" stroke-width="1" zvalue="486"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="415@0" LinkObjectIDznd="410" MaxPinNum="2"/>
   </metadata>
  <path d="M 1803.28 843.03 L 1803.28 823.22 L 1790.78 823.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="410">
   <path class="kv10" d="M 1790.78 917.82 L 1790.78 798.22" stroke-width="1" zvalue="489"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="411@0" LinkObjectIDznd="417@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1790.78 917.82 L 1790.78 798.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="426">
   <path class="kv10" d="M 1854.78 761.25 L 1854.78 734.75" stroke-width="1" zvalue="495"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="427@0" LinkObjectIDznd="164@10" MaxPinNum="2"/>
   </metadata>
  <path d="M 1854.78 761.25 L 1854.78 734.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="423">
   <path class="kv10" d="M 1854.78 797.75 L 1854.78 917.35" stroke-width="1" zvalue="499"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="427@1" LinkObjectIDznd="428@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1854.78 797.75 L 1854.78 917.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="421">
   <path class="kv10" d="M 1871.28 842.56 L 1871.28 822.75 L 1854.78 822.75" stroke-width="1" zvalue="501"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="425@0" LinkObjectIDznd="423" MaxPinNum="2"/>
   </metadata>
  <path d="M 1871.28 842.56 L 1871.28 822.75 L 1854.78 822.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="442">
   <path class="kv10" d="M 1225.5 706.4 L 1225.5 734.75" stroke-width="1" zvalue="517"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="436@1" LinkObjectIDznd="164@13" MaxPinNum="2"/>
   </metadata>
  <path d="M 1225.5 706.4 L 1225.5 734.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="186">
   <path class="kv10" d="M 690.43 865.01 L 690.43 864.43" stroke-width="1" zvalue="533"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="182@0" LinkObjectIDznd="209@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 690.43 865.01 L 690.43 864.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="264">
   <path class="kv10" d="M 768.43 866.01 L 768.43 864.43" stroke-width="1" zvalue="541"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="212@0" LinkObjectIDznd="265@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 768.43 866.01 L 768.43 864.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="395">
   <path class="kv10" d="M 1560.22 868.01 L 1560.22 866.65" stroke-width="1" zvalue="545"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="390@0" LinkObjectIDznd="383@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1560.22 868.01 L 1560.22 866.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="443">
   <path class="kv10" d="M 1638.22 868.01 L 1638.22 866.65" stroke-width="1" zvalue="549"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="399@0" LinkObjectIDznd="374@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1638.22 868.01 L 1638.22 866.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="365">
   <path class="kv10" d="M 1016 647.22 L 1016 666" stroke-width="1" zvalue="556"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="305@0" LinkObjectIDznd="263@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1016 647.22 L 1016 666" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="369">
   <path class="kv10" d="M 1016 700 L 1016 734.5" stroke-width="1" zvalue="557"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="263@1" LinkObjectIDznd="222" MaxPinNum="2"/>
   </metadata>
  <path d="M 1016 700 L 1016 734.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="379">
   <path class="kv10" d="M 1290.01 646.11 L 1290 667" stroke-width="1" zvalue="561"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="433@0" LinkObjectIDznd="373@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1290.01 646.11 L 1290 667" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="381">
   <path class="kv10" d="M 1290 701 L 1290 734.75" stroke-width="1" zvalue="562"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="373@1" LinkObjectIDznd="325" MaxPinNum="2"/>
   </metadata>
  <path d="M 1290 701 L 1290 734.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="493">
   <path class="kv110" d="M 819.8 188.54 L 819.8 218.52" stroke-width="1" zvalue="646"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="62@1" LinkObjectIDznd="491@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 819.8 188.54 L 819.8 218.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="494">
   <path class="kv110" d="M 820.09 241.46 L 820.09 265.66" stroke-width="1" zvalue="647"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="491@1" LinkObjectIDznd="65@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 820.09 241.46 L 820.09 265.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="498">
   <path class="kv110" d="M 938.55 188.54 L 938.55 218.52" stroke-width="1" zvalue="650"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="36@1" LinkObjectIDznd="496@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 938.55 188.54 L 938.55 218.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="499">
   <path class="kv110" d="M 938.09 241.46 L 938.09 265.66" stroke-width="1" zvalue="651"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="496@1" LinkObjectIDznd="32@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 938.09 241.46 L 938.09 265.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="509">
   <path class="kv110" d="M 1349.05 188.54 L 1348.96 218.52" stroke-width="1" zvalue="659"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="82@1" LinkObjectIDznd="507@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1349.05 188.54 L 1348.96 218.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="510">
   <path class="kv110" d="M 1349.09 241.46 L 1349.07 265.66" stroke-width="1" zvalue="660"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="507@1" LinkObjectIDznd="85@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1349.09 241.46 L 1349.07 265.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="514">
   <path class="kv110" d="M 1493.05 188.54 L 1493.06 218.52" stroke-width="1" zvalue="663"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="101@1" LinkObjectIDznd="512@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1493.05 188.54 L 1493.06 218.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="515">
   <path class="kv110" d="M 1493.19 241.46 L 1493.19 265.66" stroke-width="1" zvalue="664"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="512@1" LinkObjectIDznd="104@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1493.19 241.46 L 1493.19 265.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="24">
   <path class="kv110" d="M 1134.24 250.93 L 1083.54 250.93" stroke-width="1" zvalue="665"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="502@1" LinkObjectIDznd="133" MaxPinNum="2"/>
   </metadata>
  <path d="M 1134.24 250.93 L 1083.54 250.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="25">
   <path class="kv110" d="M 1159.87 250.86 L 1217.54 250.86" stroke-width="1" zvalue="666"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="502@0" LinkObjectIDznd="134" MaxPinNum="2"/>
   </metadata>
  <path d="M 1159.87 250.86 L 1217.54 250.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="kv10" d="M 447 708 L 447 734.5" stroke-width="1" zvalue="675"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="301@0" LinkObjectIDznd="163@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 447 708 L 447 734.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="kv10" d="M 1688.79 900.67 L 1688.88 917.08" stroke-width="1" zvalue="676"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="397@1" LinkObjectIDznd="398@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1688.79 900.67 L 1688.88 917.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="517">
   <path class="kv10" d="M 515 671.5 L 515 551.9" stroke-width="1" zvalue="701"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="520@1" LinkObjectIDznd="521@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 515 671.5 L 515 551.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="516">
   <path class="kv10" d="M 498 646.75 L 515 646.75" stroke-width="1" zvalue="702"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="518@0" LinkObjectIDznd="517" MaxPinNum="2"/>
   </metadata>
  <path d="M 498 646.75 L 515 646.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="511">
   <path class="kv10" d="M 530.5 626.69 L 530.5 646.5 L 515 646.5" stroke-width="1" zvalue="703"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="519@0" LinkObjectIDznd="517" MaxPinNum="2"/>
   </metadata>
  <path d="M 530.5 626.69 L 530.5 646.5 L 515 646.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="506">
   <path class="kv10" d="M 515 708 L 515 734.5" stroke-width="1" zvalue="704"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="520@0" LinkObjectIDznd="163@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 515 708 L 515 734.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="529">
   <path class="kv10" d="M 594 670.5 L 594 550.9" stroke-width="1" zvalue="713"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="532@1" LinkObjectIDznd="533@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 594 670.5 L 594 550.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="528">
   <path class="kv10" d="M 577 645.5 L 594 645.5" stroke-width="1" zvalue="714"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="530@0" LinkObjectIDznd="529" MaxPinNum="2"/>
   </metadata>
  <path d="M 577 645.5 L 594 645.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="527">
   <path class="kv10" d="M 609.5 625.69 L 609.5 645.5 L 594 645.5" stroke-width="1" zvalue="715"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="531@0" LinkObjectIDznd="529" MaxPinNum="2"/>
   </metadata>
  <path d="M 609.5 625.69 L 609.5 645.5 L 594 645.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="526">
   <path class="kv10" d="M 594 707 L 594 734.5" stroke-width="1" zvalue="716"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="532@0" LinkObjectIDznd="287" MaxPinNum="2"/>
   </metadata>
  <path d="M 594 707 L 594 734.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="540">
   <path class="kv10" d="M 691 670.5 L 691 550.9" stroke-width="1" zvalue="725"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="543@1" LinkObjectIDznd="544@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 691 670.5 L 691 550.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="538">
   <path class="kv10" d="M 706.5 625.69 L 706.5 645.5 L 691 645.5" stroke-width="1" zvalue="727"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="542@0" LinkObjectIDznd="540" MaxPinNum="2"/>
   </metadata>
  <path d="M 706.5 625.69 L 706.5 645.5 L 691 645.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="537">
   <path class="kv10" d="M 691 707 L 691 734.5" stroke-width="1" zvalue="728"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="543@0" LinkObjectIDznd="163@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 691 707 L 691 734.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="560">
   <path class="kv10" d="M 769 670.5 L 769 550.9" stroke-width="1" zvalue="747"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="563@1" LinkObjectIDznd="564@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 769 670.5 L 769 550.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="558">
   <path class="kv10" d="M 784.5 625.69 L 784.5 645.5 L 769 645.5" stroke-width="1" zvalue="749"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="562@0" LinkObjectIDznd="560" MaxPinNum="2"/>
   </metadata>
  <path d="M 784.5 625.69 L 784.5 645.5 L 769 645.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="557">
   <path class="kv10" d="M 769 707 L 769 734.5" stroke-width="1" zvalue="750"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="563@0" LinkObjectIDznd="267" MaxPinNum="2"/>
   </metadata>
  <path d="M 769 707 L 769 734.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="615">
   <path class="kv10" d="M 1561 671.5 L 1561 551.9" stroke-width="1" zvalue="759"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="618@1" LinkObjectIDznd="619@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1561 671.5 L 1561 551.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="613">
   <path class="kv10" d="M 1577.5 626.69 L 1577.5 646.5 L 1561 646.5" stroke-width="1" zvalue="761"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="617@0" LinkObjectIDznd="615" MaxPinNum="2"/>
   </metadata>
  <path d="M 1577.5 626.69 L 1577.5 646.5 L 1561 646.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="612">
   <path class="kv10" d="M 1561 708 L 1561 735.13" stroke-width="1" zvalue="762"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="618@0" LinkObjectIDznd="385" MaxPinNum="2"/>
   </metadata>
  <path d="M 1561 708 L 1561 735.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="607">
   <path class="kv10" d="M 1638 670.5 L 1638 550.9" stroke-width="1" zvalue="770"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="610@1" LinkObjectIDznd="611@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1638 670.5 L 1638 550.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="605">
   <path class="kv10" d="M 1653.5 625.69 L 1653.5 645.5 L 1638 645.5" stroke-width="1" zvalue="772"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="609@0" LinkObjectIDznd="607" MaxPinNum="2"/>
   </metadata>
  <path d="M 1653.5 625.69 L 1653.5 645.5 L 1638 645.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="604">
   <path class="kv10" d="M 1638 707 L 1638 734.75" stroke-width="1" zvalue="773"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="610@0" LinkObjectIDznd="375" MaxPinNum="2"/>
   </metadata>
  <path d="M 1638 707 L 1638 734.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="599">
   <path class="kv10" d="M 1723 668.5 L 1723 548.9" stroke-width="1" zvalue="781"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="602@1" LinkObjectIDznd="603@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1723 668.5 L 1723 548.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="597">
   <path class="kv10" d="M 1738.5 623.69 L 1738.5 643.5 L 1723 643.5" stroke-width="1" zvalue="783"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="601@0" LinkObjectIDznd="599" MaxPinNum="2"/>
   </metadata>
  <path d="M 1738.5 623.69 L 1738.5 643.5 L 1723 643.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="596">
   <path class="kv10" d="M 1723 705 L 1723 734.75" stroke-width="1" zvalue="784"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="602@0" LinkObjectIDznd="405" MaxPinNum="2"/>
   </metadata>
  <path d="M 1723 705 L 1723 734.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="591">
   <path class="kv10" d="M 1789 669.5 L 1789 549.9" stroke-width="1" zvalue="792"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="594@1" LinkObjectIDznd="595@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1789 669.5 L 1789 549.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="589">
   <path class="kv10" d="M 1804.5 624.69 L 1804.5 644.5 L 1789 644.5" stroke-width="1" zvalue="794"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="593@0" LinkObjectIDznd="591" MaxPinNum="2"/>
   </metadata>
  <path d="M 1804.5 624.69 L 1804.5 644.5 L 1789 644.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="583">
   <path class="kv10" d="M 1854.78 669.5 L 1854.78 549.9" stroke-width="1" zvalue="803"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="586@1" LinkObjectIDznd="587@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1854.78 669.5 L 1854.78 549.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="581">
   <path class="kv10" d="M 1870.5 624.69 L 1870.5 644.5 L 1854.78 644.5" stroke-width="1" zvalue="805"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="585@0" LinkObjectIDznd="583" MaxPinNum="2"/>
   </metadata>
  <path d="M 1870.5 624.69 L 1870.5 644.5 L 1854.78 644.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="580">
   <path class="kv10" d="M 1854.78 706 L 1854.78 734.75" stroke-width="1" zvalue="806"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="586@0" LinkObjectIDznd="164@11" MaxPinNum="2"/>
   </metadata>
  <path d="M 1854.78 706 L 1854.78 734.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="620">
   <path class="kv10" d="M 1789 706 L 1789 734.75" stroke-width="1" zvalue="807"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="594@0" LinkObjectIDznd="164@12" MaxPinNum="2"/>
   </metadata>
  <path d="M 1789 706 L 1789 734.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="588">
   <path class="kv10" d="M 577.45 893.68 L 594.85 893.68" stroke-width="1" zvalue="836"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="56@0" LinkObjectIDznd="444@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 577.45 893.68 L 594.85 893.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="621">
   <path class="kv10" d="M 1706.34 900.68 L 1722.89 900.68" stroke-width="1" zvalue="837"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="397@0" LinkObjectIDznd="850" MaxPinNum="2"/>
   </metadata>
  <path d="M 1706.34 900.68 L 1722.89 900.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="715">
   <path class="kv10" d="M 1225.52 682.02 L 1225.52 663.33 L 1164 663.33" stroke-width="1" zvalue="1069"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="436@0" LinkObjectIDznd="435@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1225.52 682.02 L 1225.52 663.33 L 1164 663.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="716">
   <path class="kv10" d="M 1128.5 663.33 L 1072 663.33 L 1072 734.5" stroke-width="1" zvalue="1070"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="435@1" LinkObjectIDznd="163@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1128.5 663.33 L 1072 663.33 L 1072 734.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="819">
   <path class="kv10" d="M 1079 822 L 1063 822" stroke-width="1" zvalue="1163"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="194" LinkObjectIDznd="187@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1079 822 L 1063 822" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="820">
   <path class="kv10" d="M 516 820.75 L 496 820.75" stroke-width="1" zvalue="1164"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="31" LinkObjectIDznd="279@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 516 820.75 L 496 820.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="821">
   <path class="kv10" d="M 832 818.75 L 816 818.75" stroke-width="1" zvalue="1165"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="252" LinkObjectIDznd="253@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 832 818.75 L 816 818.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="822">
   <path class="kv10" d="M 892 820 L 876 820" stroke-width="1" zvalue="1166"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="239" LinkObjectIDznd="242@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 892 820 L 876 820" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="823">
   <path class="kv10" d="M 955 821 L 938 821" stroke-width="1" zvalue="1167"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="228" LinkObjectIDznd="231@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 955 821 L 938 821" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="824">
   <path class="kv10" d="M 1017 820.75 L 1000 820.75" stroke-width="1" zvalue="1168"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="219" LinkObjectIDznd="220@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1017 820.75 L 1000 820.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="825">
   <path class="kv10" d="M 1221.33 822.86 L 1205.33 822.86" stroke-width="1" zvalue="1169"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="311" LinkObjectIDznd="312@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1221.33 822.86 L 1205.33 822.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="827">
   <path class="kv10" d="M 1469.44 822.86 L 1485.44 822.86" stroke-width="1" zvalue="1171"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="358@0" LinkObjectIDznd="357" MaxPinNum="2"/>
   </metadata>
  <path d="M 1469.44 822.86 L 1485.44 822.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="828">
   <path class="kv10" d="M 1341.44 823.11 L 1357.44 823.11" stroke-width="1" zvalue="1172"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="345@0" LinkObjectIDznd="344" MaxPinNum="2"/>
   </metadata>
  <path d="M 1341.44 823.11 L 1357.44 823.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="829">
   <path class="kv10" d="M 1273 822.86 L 1289 822.86" stroke-width="1" zvalue="1173"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="323@0" LinkObjectIDznd="322" MaxPinNum="2"/>
   </metadata>
  <path d="M 1273 822.86 L 1289 822.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="830">
   <path class="kv10" d="M 1409.11 822.86 L 1426 822.86" stroke-width="1" zvalue="1174"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="337@0" LinkObjectIDznd="334" MaxPinNum="2"/>
   </metadata>
  <path d="M 1409.11 822.86 L 1426 822.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="833">
   <path class="kv10" d="M 1770.78 822.97 L 1790.78 822.97" stroke-width="1" zvalue="1177"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="414@0" LinkObjectIDznd="410" MaxPinNum="2"/>
   </metadata>
  <path d="M 1770.78 822.97 L 1790.78 822.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="836">
   <path class="kv10" d="M 594.81 797 L 594.81 893.68" stroke-width="1" zvalue="1180"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="288@1" LinkObjectIDznd="588" MaxPinNum="2"/>
   </metadata>
  <path d="M 594.81 797 L 594.81 893.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="837">
   <path class="kv10" d="M 611.5 841.81 L 611.5 823 L 594.81 823" stroke-width="1" zvalue="1181"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="286@0" LinkObjectIDznd="836" MaxPinNum="2"/>
   </metadata>
  <path d="M 611.5 841.81 L 611.5 823 L 594.81 823" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="838">
   <path class="kv10" d="M 579 821.75 L 594.81 821.75" stroke-width="1" zvalue="1182"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="285@0" LinkObjectIDznd="836" MaxPinNum="2"/>
   </metadata>
  <path d="M 579 821.75 L 594.81 821.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="842">
   <path class="kv10" d="M 674 643.75 L 691 643.75" stroke-width="1" zvalue="1186"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="541@0" LinkObjectIDznd="540" MaxPinNum="2"/>
   </metadata>
  <path d="M 674 643.75 L 691 643.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="843">
   <path class="kv10" d="M 1838.78 822.5 L 1854.78 822.5" stroke-width="1" zvalue="1187"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="424@0" LinkObjectIDznd="423" MaxPinNum="2"/>
   </metadata>
  <path d="M 1838.78 822.5 L 1854.78 822.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="844">
   <path class="kv10" d="M 752 645.75 L 769 645.75" stroke-width="1" zvalue="1188"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="561@0" LinkObjectIDznd="560" MaxPinNum="2"/>
   </metadata>
  <path d="M 752 645.75 L 769 645.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="845">
   <path class="kv10" d="M 1838 644.5 L 1854.78 644.5" stroke-width="1" zvalue="1189"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="584@0" LinkObjectIDznd="583" MaxPinNum="2"/>
   </metadata>
  <path d="M 1838 644.5 L 1854.78 644.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="846">
   <path class="kv10" d="M 1772 642.75 L 1789 642.75" stroke-width="1" zvalue="1190"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="592@0" LinkObjectIDznd="591" MaxPinNum="2"/>
   </metadata>
  <path d="M 1772 642.75 L 1789 642.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="847">
   <path class="kv10" d="M 1545 646.75 L 1561 646.75" stroke-width="1" zvalue="1191"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="616@0" LinkObjectIDznd="615" MaxPinNum="2"/>
   </metadata>
  <path d="M 1545 646.75 L 1561 646.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="848">
   <path class="kv10" d="M 1621 645.75 L 1638 645.75" stroke-width="1" zvalue="1192"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="608@0" LinkObjectIDznd="607" MaxPinNum="2"/>
   </metadata>
  <path d="M 1621 645.75 L 1638 645.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="849">
   <path class="kv10" d="M 1706 643.75 L 1723 643.75" stroke-width="1" zvalue="1193"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="600@0" LinkObjectIDznd="599" MaxPinNum="2"/>
   </metadata>
  <path d="M 1706 643.75 L 1723 643.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="850">
   <path class="kv10" d="M 1722.89 797 L 1722.89 909.06" stroke-width="1" zvalue="1194"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="406@1" LinkObjectIDznd="448@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1722.89 797 L 1722.89 909.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="851">
   <path class="kv10" d="M 1739.39 841.81 L 1739.39 822 L 1722.89 822" stroke-width="1" zvalue="1195"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="404@0" LinkObjectIDznd="850" MaxPinNum="2"/>
   </metadata>
  <path d="M 1739.39 841.81 L 1739.39 822 L 1722.89 822" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="852">
   <path class="kv10" d="M 1706.89 821.75 L 1722.89 821.75" stroke-width="1" zvalue="1196"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="403@0" LinkObjectIDznd="850" MaxPinNum="2"/>
   </metadata>
  <path d="M 1706.89 821.75 L 1722.89 821.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="40">
   <use class="kv110" height="20" transform="rotate(270,966.75,203.5) scale(1.25,1.25) translate(-192.1,-38.2)" width="10" x="960.5" xlink:href="#GroundDisconnector:地刀_0" y="191" zvalue="55"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453653626882" ObjectName="110kV汉滇线19260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453653626882"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,966.75,203.5) scale(1.25,1.25) translate(-192.1,-38.2)" width="10" x="960.5" y="191"/></g>
  <g id="42">
   <use class="kv110" height="20" transform="rotate(270,966.75,256.25) scale(1.25,1.25) translate(-192.1,-48.75)" width="10" x="960.5" xlink:href="#GroundDisconnector:地刀_0" y="243.75" zvalue="57"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453653757954" ObjectName="110kV汉滇线19217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453653757954"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,966.75,256.25) scale(1.25,1.25) translate(-192.1,-48.75)" width="10" x="960.5" y="243.75"/></g>
  <g id="44">
   <use class="kv110" height="20" transform="rotate(270,966.75,155) scale(1.25,1.25) translate(-192.1,-28.5)" width="10" x="960.5" xlink:href="#GroundDisconnector:地刀_0" y="142.5" zvalue="59"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453653889028" ObjectName="110kV汉滇线19267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453653889028"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,966.75,155) scale(1.25,1.25) translate(-192.1,-28.5)" width="10" x="960.5" y="142.5"/></g>
  <g id="60">
   <use class="kv110" height="20" transform="rotate(90,792,203.5) scale(-1.25,1.25) translate(-1424.35,-38.2)" width="10" x="785.75" xlink:href="#GroundDisconnector:地刀_0" y="191" zvalue="73"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453654282242" ObjectName="110k#1主变110kV侧10160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453654282242"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,792,203.5) scale(-1.25,1.25) translate(-1424.35,-38.2)" width="10" x="785.75" y="191"/></g>
  <g id="59">
   <use class="kv110" height="20" transform="rotate(90,792,256.25) scale(-1.25,1.25) translate(-1424.35,-48.75)" width="10" x="785.75" xlink:href="#GroundDisconnector:地刀_0" y="243.75" zvalue="75"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453654151170" ObjectName="110k#1主变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453654151170"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,792,256.25) scale(-1.25,1.25) translate(-1424.35,-48.75)" width="10" x="785.75" y="243.75"/></g>
  <g id="58">
   <use class="kv110" height="20" transform="rotate(90,792,155) scale(-1.25,1.25) translate(-1424.35,-28.5)" width="10" x="785.75" xlink:href="#GroundDisconnector:地刀_0" y="142.5" zvalue="77"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453654020098" ObjectName="110k#1主变110kV侧10167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453654020098"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,792,155) scale(-1.25,1.25) translate(-1424.35,-28.5)" width="10" x="785.75" y="142.5"/></g>
  <g id="79">
   <use class="kv110" height="20" transform="rotate(270,1377.25,203.5) scale(1.25,1.25) translate(-274.2,-38.2)" width="10" x="1371" xlink:href="#GroundDisconnector:地刀_0" y="191" zvalue="95"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453654806530" ObjectName="110kV瑞滇线19360接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453654806530"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1377.25,203.5) scale(1.25,1.25) translate(-274.2,-38.2)" width="10" x="1371" y="191"/></g>
  <g id="78">
   <use class="kv110" height="20" transform="rotate(270,1377.25,256.25) scale(1.25,1.25) translate(-274.2,-48.75)" width="10" x="1371" xlink:href="#GroundDisconnector:地刀_0" y="243.75" zvalue="97"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453654675460" ObjectName="110kV瑞滇线19327接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453654675460"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1377.25,256.25) scale(1.25,1.25) translate(-274.2,-48.75)" width="10" x="1371" y="243.75"/></g>
  <g id="77">
   <use class="kv110" height="20" transform="rotate(270,1377.25,155) scale(1.25,1.25) translate(-274.2,-28.5)" width="10" x="1371" xlink:href="#GroundDisconnector:地刀_0" y="142.5" zvalue="99"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453654544386" ObjectName="110kV瑞滇线19367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453654544386"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1377.25,155) scale(1.25,1.25) translate(-274.2,-28.5)" width="10" x="1371" y="142.5"/></g>
  <g id="99">
   <use class="kv110" height="20" transform="rotate(270,1521.25,203.5) scale(1.25,1.25) translate(-303,-38.2)" width="10" x="1515" xlink:href="#GroundDisconnector:地刀_0" y="191" zvalue="114"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453655396356" ObjectName="110k#3主变110kV侧10360接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453655396356"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1521.25,203.5) scale(1.25,1.25) translate(-303,-38.2)" width="10" x="1515" y="191"/></g>
  <g id="98">
   <use class="kv110" height="20" transform="rotate(270,1521.25,256.25) scale(1.25,1.25) translate(-303,-48.75)" width="10" x="1515" xlink:href="#GroundDisconnector:地刀_0" y="243.75" zvalue="116"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453655265284" ObjectName="110k#3主变110kV侧10327接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453655265284"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1521.25,256.25) scale(1.25,1.25) translate(-303,-48.75)" width="10" x="1515" y="243.75"/></g>
  <g id="97">
   <use class="kv110" height="20" transform="rotate(270,1521.25,155) scale(1.25,1.25) translate(-303,-28.5)" width="10" x="1515" xlink:href="#GroundDisconnector:地刀_0" y="142.5" zvalue="118"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453655134212" ObjectName="110k#3主变110kV侧10367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453655134212"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1521.25,155) scale(1.25,1.25) translate(-303,-28.5)" width="10" x="1515" y="142.5"/></g>
  <g id="129">
   <use class="kv110" height="20" transform="rotate(180,1083.44,219) scale(-1.25,1.25) translate(-1948.94,-41.3)" width="10" x="1077.1875" xlink:href="#GroundDisconnector:地刀_0" y="206.5" zvalue="146"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453655789572" ObjectName="110kV分段11217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453655789572"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1083.44,219) scale(-1.25,1.25) translate(-1948.94,-41.3)" width="10" x="1077.1875" y="206.5"/></g>
  <g id="132">
   <use class="kv110" height="20" transform="rotate(180,1217.44,219) scale(-1.25,1.25) translate(-2190.14,-41.3)" width="10" x="1211.1875" xlink:href="#GroundDisconnector:地刀_0" y="206.5" zvalue="148"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453655920644" ObjectName="110kV分段11227接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453655920644"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1217.44,219) scale(-1.25,1.25) translate(-2190.14,-41.3)" width="10" x="1211.1875" y="206.5"/></g>
  <g id="144">
   <use class="kv110" height="20" transform="rotate(270,1073,401.5) scale(1.25,1.25) translate(-213.35,-77.8)" width="10" x="1066.75" xlink:href="#GroundDisconnector:地刀_0" y="389" zvalue="158"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453656248324" ObjectName="110kVⅠ母电压互感器19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453656248324"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1073,401.5) scale(1.25,1.25) translate(-213.35,-77.8)" width="10" x="1066.75" y="389"/></g>
  <g id="143">
   <use class="kv110" height="20" transform="rotate(270,1073,345) scale(1.25,1.25) translate(-213.35,-66.5)" width="10" x="1066.75" xlink:href="#GroundDisconnector:地刀_0" y="332.5" zvalue="160"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453656117252" ObjectName="110kVⅠ母电压互感器19010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453656117252"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1073,345) scale(1.25,1.25) translate(-213.35,-66.5)" width="10" x="1066.75" y="332.5"/></g>
  <g id="159">
   <use class="kv110" height="20" transform="rotate(270,1286.75,401.5) scale(1.25,1.25) translate(-256.1,-77.8)" width="10" x="1280.5" xlink:href="#GroundDisconnector:地刀_0" y="389" zvalue="173"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453656576004" ObjectName="110kVⅡ母电压互感器19027接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453656576004"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1286.75,401.5) scale(1.25,1.25) translate(-256.1,-77.8)" width="10" x="1280.5" y="389"/></g>
  <g id="158">
   <use class="kv110" height="20" transform="rotate(270,1286.75,345) scale(1.25,1.25) translate(-256.1,-66.5)" width="10" x="1280.5" xlink:href="#GroundDisconnector:地刀_0" y="332.5" zvalue="175"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453656444932" ObjectName="110kVⅡ母电压互感器19020接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453656444932"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1286.75,345) scale(1.25,1.25) translate(-256.1,-66.5)" width="10" x="1280.5" y="332.5"/></g>
  <g id="171">
   <use class="kv110" height="40" transform="rotate(0,950,506) scale(1,-1) translate(0,-1012)" width="40" x="930" xlink:href="#GroundDisconnector:主变中性点接地刀_0" y="486" zvalue="207"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453717786626" ObjectName="110kV#1主变110kV侧中性点1010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453717786626"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,950,506) scale(1,-1) translate(0,-1012)" width="40" x="930" y="486"/></g>
  <g id="173">
   <use class="kv110" height="40" transform="rotate(0,1344,504) scale(1,-1) translate(0,-1008)" width="40" x="1324" xlink:href="#GroundDisconnector:主变中性点接地刀_0" y="484" zvalue="209"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453717917698" ObjectName="110kV#3主变110kV侧中性点1030接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453717917698"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1344,504) scale(1,-1) translate(0,-1008)" width="40" x="1324" y="484"/></g>
  <g id="184">
   <use class="kv10" height="20" transform="rotate(180,1095.44,854) scale(-1.25,-1.25) translate(-1970.54,-1534.7)" width="10" x="1089.187542915344" xlink:href="#GroundDisconnector:地刀_0" y="841.5" zvalue="219"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453657296900" ObjectName="10kV团结Ⅰ回线05967接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453657296900"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1095.44,854) scale(-1.25,-1.25) translate(-1970.54,-1534.7)" width="10" x="1089.187542915344" y="841.5"/></g>
  <g id="205">
   <use class="kv10" height="20" transform="rotate(180,707.438,823) scale(-1.25,-1.25) translate(-1272.14,-1478.9)" width="10" x="701.1875429153442" xlink:href="#GroundDisconnector:地刀_0" y="810.5" zvalue="236"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453657559044" ObjectName="10kV1号电容器05367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453657559044"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,707.438,823) scale(-1.25,-1.25) translate(-1272.14,-1478.9)" width="10" x="701.1875429153442" y="810.5"/></g>
  <g id="221">
   <use class="kv10" height="20" transform="rotate(180,1032.44,853) scale(-1.25,-1.25) translate(-1857.14,-1532.9)" width="10" x="1026.187542915344" xlink:href="#GroundDisconnector:地刀_0" y="840.5" zvalue="253"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453657821188" ObjectName="10kV一号线05867接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453657821188"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1032.44,853) scale(-1.25,-1.25) translate(-1857.14,-1532.9)" width="10" x="1026.187542915344" y="840.5"/></g>
  <g id="232">
   <use class="kv10" height="20" transform="rotate(180,970.438,853) scale(-1.25,-1.25) translate(-1745.54,-1532.9)" width="10" x="964.1875429153442" xlink:href="#GroundDisconnector:地刀_0" y="840.5" zvalue="265"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453658083332" ObjectName="10kV四号线05767接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453658083332"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,970.438,853) scale(-1.25,-1.25) translate(-1745.54,-1532.9)" width="10" x="964.1875429153442" y="840.5"/></g>
  <g id="243">
   <use class="kv10" height="20" transform="rotate(180,908.438,852) scale(-1.25,-1.25) translate(-1633.94,-1531.1)" width="10" x="902.1875429153442" xlink:href="#GroundDisconnector:地刀_0" y="839.5" zvalue="277"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453658345476" ObjectName="10kV三号线05667接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453658345476"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,908.438,852) scale(-1.25,-1.25) translate(-1633.94,-1531.1)" width="10" x="902.1875429153442" y="839.5"/></g>
  <g id="254">
   <use class="kv10" height="20" transform="rotate(180,848.438,851) scale(-1.25,-1.25) translate(-1525.94,-1529.3)" width="10" x="842.1875429153442" xlink:href="#GroundDisconnector:地刀_0" y="838.5" zvalue="289"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453658607620" ObjectName="10kV二号线05567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453658607620"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,848.438,851) scale(-1.25,-1.25) translate(-1525.94,-1529.3)" width="10" x="842.1875429153442" y="838.5"/></g>
  <g id="269">
   <use class="kv10" height="20" transform="rotate(180,789.438,823) scale(-1.25,-1.25) translate(-1419.74,-1478.9)" width="10" x="783.1875429153442" xlink:href="#GroundDisconnector:地刀_0" y="810.5" zvalue="300"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453658869764" ObjectName="10kV2号电容器05467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453658869764"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,789.438,823) scale(-1.25,-1.25) translate(-1419.74,-1478.9)" width="10" x="783.1875429153442" y="810.5"/></g>
  <g id="286">
   <use class="kv10" height="20" transform="rotate(180,611.438,854) scale(-1.25,-1.25) translate(-1099.34,-1534.7)" width="10" x="605.1875429153442" xlink:href="#GroundDisconnector:地刀_0" y="841.5" zvalue="315"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453659328516" ObjectName="10kV#1站用变兼接地变05267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453659328516"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,611.438,854) scale(-1.25,-1.25) translate(-1099.34,-1534.7)" width="10" x="605.1875429153442" y="841.5"/></g>
  <g id="280">
   <use class="kv10" height="20" transform="rotate(180,528.438,853) scale(-1.25,-1.25) translate(-949.938,-1532.9)" width="10" x="522.1875429153442" xlink:href="#GroundDisconnector:地刀_0" y="840.5" zvalue="323"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453659131908" ObjectName="10kV团结Ⅱ回线05167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453659131908"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,528.438,853) scale(-1.25,-1.25) translate(-949.938,-1532.9)" width="10" x="522.1875429153442" y="840.5"/></g>
  <g id="204">
   <use class="kv10" height="20" transform="rotate(180,463.438,852.5) scale(-1.25,-1.25) translate(-832.938,-1532)" width="10" x="457.1875429153442" xlink:href="#GroundDisconnector:地刀_0" y="840" zvalue="347"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453659721732" ObjectName="10kV雅居乐Ⅰ回线04967接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453659721732"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,463.438,852.5) scale(-1.25,-1.25) translate(-832.938,-1532)" width="10" x="457.1875429153442" y="840"/></g>
  <g id="299">
   <use class="kv10" height="20" transform="rotate(180,463.438,614.5) scale(-1.25,1.25) translate(-832.938,-120.4)" width="10" x="457.1875429153442" xlink:href="#GroundDisconnector:地刀_0" y="602" zvalue="360"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453659983875" ObjectName="10kV十一号线04867接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453659983875"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,463.438,614.5) scale(-1.25,1.25) translate(-832.938,-120.4)" width="10" x="457.1875429153442" y="602"/></g>
  <g id="313">
   <use class="kv10" height="20" transform="rotate(180,1237.77,855.111) scale(-1.25,-1.25) translate(-2226.74,-1536.7)" width="10" x="1231.520876248678" xlink:href="#GroundDisconnector:地刀_0" y="842.6111111111111" zvalue="377"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453660377091" ObjectName="10kV广母Ⅰ回线06167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453660377091"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1237.77,855.111) scale(-1.25,-1.25) translate(-2226.74,-1536.7)" width="10" x="1231.520876248678" y="842.6111111111111"/></g>
  <g id="324">
   <use class="kv10" height="20" transform="rotate(180,1305.44,855.111) scale(-1.25,-1.25) translate(-2348.54,-1536.7)" width="10" x="1299.187542915344" xlink:href="#GroundDisconnector:地刀_0" y="842.611111111111" zvalue="389"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453660639235" ObjectName="10kV广母Ⅱ回线06267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453660639235"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1305.44,855.111) scale(-1.25,-1.25) translate(-2348.54,-1536.7)" width="10" x="1299.187542915344" y="842.611111111111"/></g>
  <g id="346">
   <use class="kv10" height="20" transform="rotate(180,1373.88,855.111) scale(-1.25,-1.25) translate(-2471.74,-1536.7)" width="10" x="1367.631987359789" xlink:href="#GroundDisconnector:地刀_0" y="842.6111111111111" zvalue="401"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453661163524" ObjectName="10kV姐东线06367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453661163524"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1373.88,855.111) scale(-1.25,-1.25) translate(-2471.74,-1536.7)" width="10" x="1367.631987359789" y="842.6111111111111"/></g>
  <g id="338">
   <use class="kv10" height="20" transform="rotate(180,1441.55,855.111) scale(-1.25,-1.25) translate(-2593.54,-1536.7)" width="10" x="1435.298654026455" xlink:href="#GroundDisconnector:地刀_0" y="842.611111111111" zvalue="412"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453660901380" ObjectName="10kV金滇线06467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453660901380"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1441.55,855.111) scale(-1.25,-1.25) translate(-2593.54,-1536.7)" width="10" x="1435.298654026455" y="842.611111111111"/></g>
  <g id="359">
   <use class="kv10" height="20" transform="rotate(180,1501.88,855.111) scale(-1.25,-1.25) translate(-2702.14,-1536.7)" width="10" x="1495.631987359789" xlink:href="#GroundDisconnector:地刀_0" y="842.611111111111" zvalue="424"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453661425668" ObjectName="10kV景成医院线06567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453661425668"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1501.88,855.111) scale(-1.25,-1.25) translate(-2702.14,-1536.7)" width="10" x="1495.631987359789" y="842.611111111111"/></g>
  <g id="387">
   <use class="kv10" height="20" transform="rotate(180,1577.1,825.222) scale(-1.25,-1.25) translate(-2837.54,-1482.9)" width="10" x="1570.854209582011" xlink:href="#GroundDisconnector:地刀_0" y="812.7222222222222" zvalue="436"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453661949956" ObjectName="10kV3号电容器06667接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453661949956"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1577.1,825.222) scale(-1.25,-1.25) translate(-2837.54,-1482.9)" width="10" x="1570.854209582011" y="812.7222222222222"/></g>
  <g id="377">
   <use class="kv10" height="20" transform="rotate(180,1659.1,825.222) scale(-1.25,-1.25) translate(-2985.14,-1482.9)" width="10" x="1652.854209582011" xlink:href="#GroundDisconnector:地刀_0" y="812.7222222222222" zvalue="450"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453661687812" ObjectName="10kV4号电容器06767接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453661687812"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1659.1,825.222) scale(-1.25,-1.25) translate(-2985.14,-1482.9)" width="10" x="1652.854209582011" y="812.7222222222222"/></g>
  <g id="404">
   <use class="kv10" height="20" transform="rotate(180,1739.33,854) scale(-1.25,-1.25) translate(-3129.54,-1534.7)" width="10" x="1733.076431804233" xlink:href="#GroundDisconnector:地刀_0" y="841.4999999999999" zvalue="464"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453662343172" ObjectName="10kV#3站用变兼接地变06867接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453662343172"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1739.33,854) scale(-1.25,-1.25) translate(-3129.54,-1534.7)" width="10" x="1733.076431804233" y="841.4999999999999"/></g>
  <g id="415">
   <use class="kv10" height="20" transform="rotate(180,1803.22,855.222) scale(-1.25,-1.25) translate(-3244.54,-1536.9)" width="10" x="1796.965320693122" xlink:href="#GroundDisconnector:地刀_0" y="842.7222222222223" zvalue="482"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453662605316" ObjectName="10kV滇沙线06967接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453662605316"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1803.22,855.222) scale(-1.25,-1.25) translate(-3244.54,-1536.9)" width="10" x="1796.965320693122" y="842.7222222222223"/></g>
  <g id="425">
   <use class="kv10" height="20" transform="rotate(180,1871.22,854.75) scale(-1.25,-1.25) translate(-3366.94,-1536.05)" width="10" x="1864.965320693122" xlink:href="#GroundDisconnector:地刀_0" y="842.25" zvalue="496"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453662801922" ObjectName="10kV雅居乐Ⅱ回线07167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453662801922"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1871.22,854.75) scale(-1.25,-1.25) translate(-3366.94,-1536.05)" width="10" x="1864.965320693122" y="842.25"/></g>
  <g id="519">
   <use class="kv10" height="20" transform="rotate(180,530.438,614.5) scale(-1.25,1.25) translate(-953.538,-120.4)" width="10" x="524.1875429153442" xlink:href="#GroundDisconnector:地刀_0" y="602" zvalue="698"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453715361794" ObjectName="10kV联检线04767接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453715361794"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,530.438,614.5) scale(-1.25,1.25) translate(-953.538,-120.4)" width="10" x="524.1875429153442" y="602"/></g>
  <g id="531">
   <use class="kv10" height="20" transform="rotate(180,609.438,613.5) scale(-1.25,1.25) translate(-1095.74,-120.2)" width="10" x="603.1875429153442" xlink:href="#GroundDisconnector:地刀_0" y="601" zvalue="710"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453715623938" ObjectName="10kV新建路线04667接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453715623938"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,609.438,613.5) scale(-1.25,1.25) translate(-1095.74,-120.2)" width="10" x="603.1875429153442" y="601"/></g>
  <g id="542">
   <use class="kv10" height="20" transform="rotate(180,706.438,613.5) scale(-1.25,1.25) translate(-1270.34,-120.2)" width="10" x="700.1875429153442" xlink:href="#GroundDisconnector:地刀_0" y="601" zvalue="722"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453715886082" ObjectName="10kV备用Ⅲ回线04567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453715886082"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,706.438,613.5) scale(-1.25,1.25) translate(-1270.34,-120.2)" width="10" x="700.1875429153442" y="601"/></g>
  <g id="562">
   <use class="kv10" height="20" transform="rotate(180,784.438,613.5) scale(-1.25,1.25) translate(-1410.74,-120.2)" width="10" x="778.1875429153442" xlink:href="#GroundDisconnector:地刀_0" y="601" zvalue="744"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453716148226" ObjectName="10kV备用Ⅳ回线04467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453716148226"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,784.438,613.5) scale(-1.25,1.25) translate(-1410.74,-120.2)" width="10" x="778.1875429153442" y="601"/></g>
  <g id="617">
   <use class="kv10" height="20" transform="rotate(180,1577.44,614.5) scale(-1.25,1.25) translate(-2838.14,-120.4)" width="10" x="1571.187542915344" xlink:href="#GroundDisconnector:地刀_0" y="602" zvalue="756"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453717458946" ObjectName="10kV备用Ⅸ回线07667接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453717458946"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1577.44,614.5) scale(-1.25,1.25) translate(-2838.14,-120.4)" width="10" x="1571.187542915344" y="602"/></g>
  <g id="609">
   <use class="kv10" height="20" transform="rotate(180,1653.44,613.5) scale(-1.25,1.25) translate(-2974.94,-120.2)" width="10" x="1647.187542915344" xlink:href="#GroundDisconnector:地刀_0" y="601" zvalue="767"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453717196802" ObjectName="10kV备用Ⅷ回线07567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453717196802"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1653.44,613.5) scale(-1.25,1.25) translate(-2974.94,-120.2)" width="10" x="1647.187542915344" y="601"/></g>
  <g id="601">
   <use class="kv10" height="20" transform="rotate(180,1738.44,611.5) scale(-1.25,1.25) translate(-3127.94,-119.8)" width="10" x="1732.187542915344" xlink:href="#GroundDisconnector:地刀_0" y="599" zvalue="778"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453716934658" ObjectName="10kV备用Ⅶ回线07467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453716934658"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1738.44,611.5) scale(-1.25,1.25) translate(-3127.94,-119.8)" width="10" x="1732.187542915344" y="599"/></g>
  <g id="593">
   <use class="kv10" height="20" transform="rotate(180,1804.44,612.5) scale(-1.25,1.25) translate(-3246.74,-120)" width="10" x="1798.187542915344" xlink:href="#GroundDisconnector:地刀_0" y="600" zvalue="789"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453716672514" ObjectName="10kV备用Ⅵ回线07367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453716672514"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1804.44,612.5) scale(-1.25,1.25) translate(-3246.74,-120)" width="10" x="1798.187542915344" y="600"/></g>
  <g id="585">
   <use class="kv10" height="20" transform="rotate(180,1870.44,612.5) scale(-1.25,1.25) translate(-3365.54,-120)" width="10" x="1864.187542915344" xlink:href="#GroundDisconnector:地刀_0" y="600" zvalue="800"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453716410370" ObjectName="10kV飞海线07267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453716410370"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1870.44,612.5) scale(-1.25,1.25) translate(-3365.54,-120)" width="10" x="1864.187542915344" y="600"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="106">
   <g id="1060">
    <use class="kv110" height="30" transform="rotate(0,880.75,501.5) scale(3.75,3.66667) translate(-612.883,-324.727)" width="24" x="835.75" xlink:href="#PowerTransformer2:可调两卷变_0" y="446.5" zvalue="123"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874561028098" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1061">
    <use class="kv10" height="30" transform="rotate(0,880.75,501.5) scale(3.75,3.66667) translate(-612.883,-324.727)" width="24" x="835.75" xlink:href="#PowerTransformer2:可调两卷变_1" y="446.5" zvalue="123"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874561093634" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399521796098" ObjectName="110kV#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399521796098"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,880.75,501.5) scale(3.75,3.66667) translate(-612.883,-324.727)" width="24" x="835.75" y="446.5"/></g>
  <g id="109">
   <g id="1090">
    <use class="kv110" height="30" transform="rotate(0,1431.5,501.5) scale(3.75,3.66667) translate(-1016.77,-324.727)" width="24" x="1386.5" xlink:href="#PowerTransformer2:可调两卷变_0" y="446.5" zvalue="126"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874561159170" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1091">
    <use class="kv10" height="30" transform="rotate(0,1431.5,501.5) scale(3.75,3.66667) translate(-1016.77,-324.727)" width="24" x="1386.5" xlink:href="#PowerTransformer2:可调两卷变_1" y="446.5" zvalue="126"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874561224706" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399521861634" ObjectName="110kV#3主变"/>
   <cge:TPSR_Ref TObjectID="6755399521861634"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1431.5,501.5) scale(3.75,3.66667) translate(-1016.77,-324.727)" width="24" x="1386.5" y="446.5"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="135">
   <use class="kv110" height="35" transform="rotate(0,1033,435.75) scale(1.25,-1.25) translate(-202.85,-779.975)" width="30" x="1014.25" xlink:href="#Accessory:5卷PT带壁雷器_0" y="413.875" zvalue="152"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453655986180" ObjectName="110kVⅠ母电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1033,435.75) scale(1.25,-1.25) translate(-202.85,-779.975)" width="30" x="1014.25" y="413.875"/></g>
  <g id="161">
   <use class="kv110" height="35" transform="rotate(0,1246.75,435.75) scale(1.25,-1.25) translate(-245.6,-779.975)" width="30" x="1228" xlink:href="#Accessory:5卷PT带壁雷器_0" y="413.875" zvalue="169"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453656707076" ObjectName="110kVⅡ母电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1246.75,435.75) scale(1.25,-1.25) translate(-245.6,-779.975)" width="30" x="1228" y="413.875"/></g>
  <g id="167">
   <use class="kv10" height="20" transform="rotate(90,863,634) scale(3,3) translate(-555.333,-402.667)" width="20" x="833" xlink:href="#Accessory:线路PT3_0" y="604" zvalue="202"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453656772612" ObjectName="#1变低压F"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,863,634) scale(3,3) translate(-555.333,-402.667)" width="20" x="833" y="604"/></g>
  <g id="169">
   <use class="kv10" height="20" transform="rotate(90,1413,637) scale(3,3) translate(-922,-404.667)" width="20" x="1383" xlink:href="#Accessory:线路PT3_0" y="607" zvalue="205"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453656838148" ObjectName="#3变低压F"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1413,637) scale(3,3) translate(-922,-404.667)" width="20" x="1383" y="607"/></g>
  <g id="187">
   <use class="kv10" height="20" transform="rotate(0,1063,848) scale(2.25,3) translate(-578.056,-545.333)" width="20" x="1040.5" xlink:href="#Accessory:线路PT3_0" y="818" zvalue="221"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453657362436" ObjectName="团结Ⅰ回线F"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1063,848) scale(2.25,3) translate(-578.056,-545.333)" width="20" x="1040.5" y="818"/></g>
  <g id="220">
   <use class="kv10" height="20" transform="rotate(0,1000,847) scale(2.25,3) translate(-543.056,-544.667)" width="20" x="977.5" xlink:href="#Accessory:线路PT3_0" y="817" zvalue="255"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453657690116" ObjectName="一号线F"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1000,847) scale(2.25,3) translate(-543.056,-544.667)" width="20" x="977.5" y="817"/></g>
  <g id="231">
   <use class="kv10" height="20" transform="rotate(0,938,847) scale(2.25,3) translate(-508.611,-544.667)" width="20" x="915.5" xlink:href="#Accessory:线路PT3_0" y="817" zvalue="267"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453657952260" ObjectName="四号线F"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,938,847) scale(2.25,3) translate(-508.611,-544.667)" width="20" x="915.5" y="817"/></g>
  <g id="242">
   <use class="kv10" height="20" transform="rotate(0,876,846) scale(2.25,3) translate(-474.167,-544)" width="20" x="853.5" xlink:href="#Accessory:线路PT3_0" y="816" zvalue="279"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453658214404" ObjectName="三号线F"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,876,846) scale(2.25,3) translate(-474.167,-544)" width="20" x="853.5" y="816"/></g>
  <g id="253">
   <use class="kv10" height="20" transform="rotate(0,816,845) scale(2.25,3) translate(-440.833,-543.333)" width="20" x="793.5" xlink:href="#Accessory:线路PT3_0" y="815" zvalue="291"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453658476548" ObjectName="二号线F"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,816,845) scale(2.25,3) translate(-440.833,-543.333)" width="20" x="793.5" y="815"/></g>
  <g id="285">
   <use class="kv10" height="20" transform="rotate(0,579,848) scale(2.25,3) translate(-309.167,-545.333)" width="20" x="556.5" xlink:href="#Accessory:线路PT3_0" y="818" zvalue="317"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453659197444" ObjectName="#1站用变兼接地变F"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,579,848) scale(2.25,3) translate(-309.167,-545.333)" width="20" x="556.5" y="818"/></g>
  <g id="279">
   <use class="kv10" height="20" transform="rotate(0,496,847) scale(2.25,3) translate(-263.056,-544.667)" width="20" x="473.5" xlink:href="#Accessory:线路PT3_0" y="817" zvalue="325"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453659000836" ObjectName="团结Ⅱ回线F"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,496,847) scale(2.25,3) translate(-263.056,-544.667)" width="20" x="473.5" y="817"/></g>
  <g id="55">
   <use class="kv10" height="20" transform="rotate(0,560.99,918.833) scale(1.08333,1.08333) translate(-42.6531,-69.8462)" width="12" x="554.49" xlink:href="#Accessory:接地变中性点_0" y="908" zvalue="335"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453659459588" ObjectName="#1站用变兼接地变中性点"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,560.99,918.833) scale(1.08333,1.08333) translate(-42.6531,-69.8462)" width="12" x="554.49" y="908"/></g>
  <g id="203">
   <use class="kv10" height="20" transform="rotate(0,431,846.5) scale(2.25,3) translate(-226.944,-544.333)" width="20" x="408.5" xlink:href="#Accessory:线路PT3_0" y="816.5" zvalue="349"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453659590660" ObjectName="雅居乐Ⅰ回线F"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,431,846.5) scale(2.25,3) translate(-226.944,-544.333)" width="20" x="408.5" y="816.5"/></g>
  <g id="298">
   <use class="kv10" height="20" transform="rotate(0,431,620.5) scale(2.25,-3) translate(-226.944,-807.333)" width="20" x="408.5" xlink:href="#Accessory:线路PT3_0" y="590.5" zvalue="362"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453659852804" ObjectName="十一号线F"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,431,620.5) scale(2.25,-3) translate(-226.944,-807.333)" width="20" x="408.5" y="590.5"/></g>
  <g id="305">
   <use class="kv10" height="42" transform="rotate(0,1027.79,622) scale(1.2381,1.2381) translate(-194.08,-114.615)" width="30" x="1009.216321191526" xlink:href="#Accessory:4卷PT带容断器_0" y="596" zvalue="367"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453660180483" ObjectName="10kVⅠ母电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,1027.79,622) scale(1.2381,1.2381) translate(-194.08,-114.615)" width="30" x="1009.216321191526" y="596"/></g>
  <g id="312">
   <use class="kv10" height="20" transform="rotate(0,1205.33,849.111) scale(2.25,3) translate(-657.13,-546.074)" width="20" x="1182.833333333333" xlink:href="#Accessory:线路PT3_0" y="819.1111111111111" zvalue="379"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453660246019" ObjectName="广母Ⅰ回线F"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1205.33,849.111) scale(2.25,3) translate(-657.13,-546.074)" width="20" x="1182.833333333333" y="819.1111111111111"/></g>
  <g id="323">
   <use class="kv10" height="20" transform="rotate(0,1273,849.111) scale(2.25,3) translate(-694.722,-546.074)" width="20" x="1250.5" xlink:href="#Accessory:线路PT3_0" y="819.111111111111" zvalue="391"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453660508163" ObjectName="广母Ⅱ回线F"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1273,849.111) scale(2.25,3) translate(-694.722,-546.074)" width="20" x="1250.5" y="819.111111111111"/></g>
  <g id="345">
   <use class="kv10" height="20" transform="rotate(0,1341.44,849.361) scale(2.25,3) translate(-732.747,-546.241)" width="20" x="1318.944444444444" xlink:href="#Accessory:线路PT3_0" y="819.3611111111111" zvalue="403"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453661032452" ObjectName="姐东线F"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1341.44,849.361) scale(2.25,3) translate(-732.747,-546.241)" width="20" x="1318.944444444444" y="819.3611111111111"/></g>
  <g id="337">
   <use class="kv10" height="20" transform="rotate(0,1409.11,849.111) scale(2.25,3) translate(-770.34,-546.074)" width="20" x="1386.611111111111" xlink:href="#Accessory:线路PT3_0" y="819.111111111111" zvalue="414"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453660770308" ObjectName="金滇线F"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1409.11,849.111) scale(2.25,3) translate(-770.34,-546.074)" width="20" x="1386.611111111111" y="819.111111111111"/></g>
  <g id="358">
   <use class="kv10" height="20" transform="rotate(0,1469.44,849.111) scale(2.25,3) translate(-803.858,-546.074)" width="20" x="1446.944444444444" xlink:href="#Accessory:线路PT3_0" y="819.111111111111" zvalue="426"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453661294596" ObjectName="景成医院线F"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1469.44,849.111) scale(2.25,3) translate(-803.858,-546.074)" width="20" x="1446.944444444444" y="819.111111111111"/></g>
  <g id="403">
   <use class="kv10" height="20" transform="rotate(0,1706.89,848) scale(2.25,3) translate(-935.772,-545.333)" width="20" x="1684.388888888889" xlink:href="#Accessory:线路PT3_0" y="817.9999999999999" zvalue="466"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453662212100" ObjectName="#3站用变兼接地变F"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1706.89,848) scale(2.25,3) translate(-935.772,-545.333)" width="20" x="1684.388888888889" y="817.9999999999999"/></g>
  <g id="398">
   <use class="kv10" height="20" transform="rotate(0,1688.88,926.833) scale(1.08333,1.08333) translate(-129.414,-70.4615)" width="12" x="1682.378888888889" xlink:href="#Accessory:接地变中性点_0" y="916" zvalue="471"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453662146564" ObjectName="#3站用变兼接地变中性点"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1688.88,926.833) scale(1.08333,1.08333) translate(-129.414,-70.4615)" width="12" x="1682.378888888889" y="916"/></g>
  <g id="414">
   <use class="kv10" height="20" transform="rotate(0,1770.78,849.222) scale(2.25,3) translate(-971.265,-546.148)" width="20" x="1748.277777777778" xlink:href="#Accessory:线路PT3_0" y="819.2222222222223" zvalue="484"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453662474244" ObjectName="滇沙线F"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1770.78,849.222) scale(2.25,3) translate(-971.265,-546.148)" width="20" x="1748.277777777778" y="819.2222222222223"/></g>
  <g id="424">
   <use class="kv10" height="20" transform="rotate(0,1838.78,848.75) scale(2.25,3) translate(-1009.04,-545.833)" width="20" x="1816.277777777778" xlink:href="#Accessory:线路PT3_0" y="818.75" zvalue="498"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453662670850" ObjectName="雅居乐Ⅱ回线F"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1838.78,848.75) scale(2.25,3) translate(-1009.04,-545.833)" width="20" x="1816.277777777778" y="818.75"/></g>
  <g id="433">
   <use class="kv10" height="42" transform="rotate(0,1301.79,620.889) scale(1.2381,1.2381) translate(-246.774,-114.402)" width="30" x="1283.222222222222" xlink:href="#Accessory:4卷PT带容断器_0" y="594.8888888888889" zvalue="505"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453662932994" ObjectName="10kVⅢ母电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,1301.79,620.889) scale(1.2381,1.2381) translate(-246.774,-114.402)" width="30" x="1283.222222222222" y="594.8888888888889"/></g>
  <g id="518">
   <use class="kv10" height="20" transform="rotate(0,498,620.5) scale(2.25,-3) translate(-264.167,-807.333)" width="20" x="475.5" xlink:href="#Accessory:线路PT3_0" y="590.5" zvalue="700"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453715230722" ObjectName="备用Ⅰ回线F"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,498,620.5) scale(2.25,-3) translate(-264.167,-807.333)" width="20" x="475.5" y="590.5"/></g>
  <g id="530">
   <use class="kv10" height="20" transform="rotate(0,577,619.25) scale(2.25,-3) translate(-308.056,-805.667)" width="20" x="554.5" xlink:href="#Accessory:线路PT3_0" y="589.25" zvalue="712"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453715492866" ObjectName="新建路线F"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,577,619.25) scale(2.25,-3) translate(-308.056,-805.667)" width="20" x="554.5" y="589.25"/></g>
  <g id="541">
   <use class="kv10" height="20" transform="rotate(0,674,617.5) scale(2.25,-3) translate(-361.944,-803.333)" width="20" x="651.5" xlink:href="#Accessory:线路PT3_0" y="587.5" zvalue="724"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453715755010" ObjectName="备用Ⅲ回线F"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,674,617.5) scale(2.25,-3) translate(-361.944,-803.333)" width="20" x="651.5" y="587.5"/></g>
  <g id="561">
   <use class="kv10" height="20" transform="rotate(0,752,619.5) scale(2.25,-3) translate(-405.278,-806)" width="20" x="729.5" xlink:href="#Accessory:线路PT3_0" y="589.5" zvalue="746"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453716017154" ObjectName="备用Ⅳ回线F"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,752,619.5) scale(2.25,-3) translate(-405.278,-806)" width="20" x="729.5" y="589.5"/></g>
  <g id="616">
   <use class="kv10" height="20" transform="rotate(0,1545,620.5) scale(2.25,-3) translate(-845.833,-807.333)" width="20" x="1522.5" xlink:href="#Accessory:线路PT3_0" y="590.5" zvalue="758"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453717327874" ObjectName="备用Ⅸ回线F"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1545,620.5) scale(2.25,-3) translate(-845.833,-807.333)" width="20" x="1522.5" y="590.5"/></g>
  <g id="608">
   <use class="kv10" height="20" transform="rotate(0,1621,619.5) scale(2.25,-3) translate(-888.056,-806)" width="20" x="1598.5" xlink:href="#Accessory:线路PT3_0" y="589.5" zvalue="769"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453717065730" ObjectName="备用Ⅷ回线F"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1621,619.5) scale(2.25,-3) translate(-888.056,-806)" width="20" x="1598.5" y="589.5"/></g>
  <g id="600">
   <use class="kv10" height="20" transform="rotate(0,1706,617.5) scale(2.25,-3) translate(-935.278,-803.333)" width="20" x="1683.5" xlink:href="#Accessory:线路PT3_0" y="587.5" zvalue="780"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453716803586" ObjectName="备用Ⅶ回线F"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1706,617.5) scale(2.25,-3) translate(-935.278,-803.333)" width="20" x="1683.5" y="587.5"/></g>
  <g id="592">
   <use class="kv10" height="20" transform="rotate(0,1772,616.5) scale(2.25,-3) translate(-971.944,-802)" width="20" x="1749.5" xlink:href="#Accessory:线路PT3_0" y="586.5" zvalue="791"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453716541442" ObjectName="备用Ⅵ回线F"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1772,616.5) scale(2.25,-3) translate(-971.944,-802)" width="20" x="1749.5" y="586.5"/></g>
  <g id="584">
   <use class="kv10" height="20" transform="rotate(0,1838,618.25) scale(2.25,-3) translate(-1008.61,-804.333)" width="20" x="1815.5" xlink:href="#Accessory:线路PT3_0" y="588.25" zvalue="802"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453716279298" ObjectName="10kV雅居乐Ⅲ回线F"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1838,618.25) scale(2.25,-3) translate(-1008.61,-804.333)" width="20" x="1815.5" y="588.25"/></g>
 </g>
 <g id="BreakerClass">
  <g id="94">
   <use class="kv10" height="20" transform="rotate(0,880,692) scale(2,2) translate(-435,-336)" width="10" x="870" xlink:href="#Breaker:小车断路器_0" y="672" zvalue="195"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925075075075" ObjectName="110k#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473925075075075"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,880,692) scale(2,2) translate(-435,-336)" width="10" x="870" y="672"/></g>
  <g id="137">
   <use class="kv10" height="20" transform="rotate(0,1431.5,692) scale(2,2) translate(-710.75,-336)" width="10" x="1421.5" xlink:href="#Breaker:小车断路器_0" y="672" zvalue="199"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925075140611" ObjectName="110k#3主变10kV侧003断路器"/>
   <cge:TPSR_Ref TObjectID="6473925075140611"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1431.5,692) scale(2,2) translate(-710.75,-336)" width="10" x="1421.5" y="672"/></g>
  <g id="180">
   <use class="kv10" height="20" transform="rotate(0,1079,779) scale(2,2) translate(-534.5,-379.5)" width="10" x="1069" xlink:href="#Breaker:小车断路器_0" y="759" zvalue="215"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925075206147" ObjectName="10kV团结Ⅰ回线059断路器"/>
   <cge:TPSR_Ref TObjectID="6473925075206147"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1079,779) scale(2,2) translate(-534.5,-379.5)" width="10" x="1069" y="759"/></g>
  <g id="206">
   <use class="kv10" height="20" transform="rotate(0,691,774) scale(2,2) translate(-340.5,-377)" width="10" x="680.9999999999999" xlink:href="#Breaker:小车断路器_0" y="754" zvalue="234"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925075271683" ObjectName="10kV1号电容器053断路器"/>
   <cge:TPSR_Ref TObjectID="6473925075271683"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,691,774) scale(2,2) translate(-340.5,-377)" width="10" x="680.9999999999999" y="754"/></g>
  <g id="223">
   <use class="kv10" height="20" transform="rotate(0,1017,778) scale(2,2) translate(-503.5,-379)" width="10" x="1007" xlink:href="#Breaker:小车断路器_0" y="758" zvalue="250"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925075337219" ObjectName="10kV一号线058断路器"/>
   <cge:TPSR_Ref TObjectID="6473925075337219"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1017,778) scale(2,2) translate(-503.5,-379)" width="10" x="1007" y="758"/></g>
  <g id="234">
   <use class="kv10" height="20" transform="rotate(0,955,778) scale(2,2) translate(-472.5,-379)" width="10" x="945" xlink:href="#Breaker:小车断路器_0" y="758" zvalue="262"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925075402755" ObjectName="10kV四号线057断路器"/>
   <cge:TPSR_Ref TObjectID="6473925075402755"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,955,778) scale(2,2) translate(-472.5,-379)" width="10" x="945" y="758"/></g>
  <g id="245">
   <use class="kv10" height="20" transform="rotate(0,892,777) scale(2,2) translate(-441,-378.5)" width="10" x="882" xlink:href="#Breaker:小车断路器_0" y="757" zvalue="274"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925075468291" ObjectName="10kV三号线056断路器"/>
   <cge:TPSR_Ref TObjectID="6473925075468291"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,892,777) scale(2,2) translate(-441,-378.5)" width="10" x="882" y="757"/></g>
  <g id="256">
   <use class="kv10" height="20" transform="rotate(0,832,776) scale(2,2) translate(-411,-378)" width="10" x="822" xlink:href="#Breaker:小车断路器_0" y="756" zvalue="286"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925075533827" ObjectName="10kV二号线055断路器"/>
   <cge:TPSR_Ref TObjectID="6473925075533827"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,832,776) scale(2,2) translate(-411,-378)" width="10" x="822" y="756"/></g>
  <g id="270">
   <use class="kv10" height="20" transform="rotate(0,769,774) scale(2,2) translate(-379.5,-377)" width="10" x="759" xlink:href="#Breaker:小车断路器_0" y="754" zvalue="298"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925075599363" ObjectName="10kV2号电容器054断路器"/>
   <cge:TPSR_Ref TObjectID="6473925075599363"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,769,774) scale(2,2) translate(-379.5,-377)" width="10" x="759" y="754"/></g>
  <g id="288">
   <use class="kv10" height="20" transform="rotate(0,594.806,779) scale(2,2) translate(-292.403,-379.5)" width="10" x="584.8055555555554" xlink:href="#Breaker:小车断路器_0" y="759" zvalue="312"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925075730435" ObjectName="10kV#1站用变兼接地变052断路器"/>
   <cge:TPSR_Ref TObjectID="6473925075730435"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,594.806,779) scale(2,2) translate(-292.403,-379.5)" width="10" x="584.8055555555554" y="759"/></g>
  <g id="282">
   <use class="kv10" height="20" transform="rotate(0,515,778) scale(2,2) translate(-252.5,-379)" width="10" x="505" xlink:href="#Breaker:小车断路器_0" y="758" zvalue="320"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925075664899" ObjectName="10kV团结Ⅱ回线051断路器"/>
   <cge:TPSR_Ref TObjectID="6473925075664899"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,515,778) scale(2,2) translate(-252.5,-379)" width="10" x="505" y="758"/></g>
  <g id="289">
   <use class="kv10" height="20" transform="rotate(0,447,777.5) scale(2,2) translate(-218.5,-378.75)" width="10" x="437" xlink:href="#Breaker:小车断路器_0" y="757.5" zvalue="344"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925075795971" ObjectName="10kV雅居乐Ⅰ回线049断路器"/>
   <cge:TPSR_Ref TObjectID="6473925075795971"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,447,777.5) scale(2,2) translate(-218.5,-378.75)" width="10" x="437" y="757.5"/></g>
  <g id="301">
   <use class="kv10" height="20" transform="rotate(0,447,689.5) scale(2,-2) translate(-218.5,-1024.25)" width="10" x="437" xlink:href="#Breaker:小车断路器_0" y="669.5" zvalue="357"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925075861507" ObjectName="10kV十一号线048断路器"/>
   <cge:TPSR_Ref TObjectID="6473925075861507"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,447,689.5) scale(2,-2) translate(-218.5,-1024.25)" width="10" x="437" y="669.5"/></g>
  <g id="315">
   <use class="kv10" height="20" transform="rotate(0,1221.33,780.111) scale(2,2) translate(-605.667,-380.056)" width="10" x="1211.333333333333" xlink:href="#Breaker:小车断路器_0" y="760.1111111111111" zvalue="374"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925075927043" ObjectName="10kV广母Ⅰ回线061断路器"/>
   <cge:TPSR_Ref TObjectID="6473925075927043"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1221.33,780.111) scale(2,2) translate(-605.667,-380.056)" width="10" x="1211.333333333333" y="760.1111111111111"/></g>
  <g id="326">
   <use class="kv10" height="20" transform="rotate(0,1289,780.111) scale(2,2) translate(-639.5,-380.056)" width="10" x="1279" xlink:href="#Breaker:小车断路器_0" y="760.111111111111" zvalue="386"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925075992579" ObjectName="10kV广母Ⅱ回线062断路器"/>
   <cge:TPSR_Ref TObjectID="6473925075992579"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1289,780.111) scale(2,2) translate(-639.5,-380.056)" width="10" x="1279" y="760.111111111111"/></g>
  <g id="348">
   <use class="kv10" height="20" transform="rotate(0,1357.44,780.111) scale(2,2) translate(-673.722,-380.056)" width="10" x="1347.444444444444" xlink:href="#Breaker:小车断路器_0" y="760.1111111111111" zvalue="398"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925076123651" ObjectName="10kV姐东线063断路器"/>
   <cge:TPSR_Ref TObjectID="6473925076123651"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1357.44,780.111) scale(2,2) translate(-673.722,-380.056)" width="10" x="1347.444444444444" y="760.1111111111111"/></g>
  <g id="340">
   <use class="kv10" height="20" transform="rotate(0,1425.11,780.111) scale(2,2) translate(-707.556,-380.056)" width="10" x="1415.111111111111" xlink:href="#Breaker:小车断路器_0" y="760.111111111111" zvalue="409"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925076058115" ObjectName="10kV金滇线064断路器"/>
   <cge:TPSR_Ref TObjectID="6473925076058115"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1425.11,780.111) scale(2,2) translate(-707.556,-380.056)" width="10" x="1415.111111111111" y="760.111111111111"/></g>
  <g id="360">
   <use class="kv10" height="20" transform="rotate(0,1485.44,780.111) scale(2,2) translate(-737.722,-380.056)" width="10" x="1475.444444444444" xlink:href="#Breaker:小车断路器_0" y="760.111111111111" zvalue="422"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925076189187" ObjectName="10kV景成医院线065断路器"/>
   <cge:TPSR_Ref TObjectID="6473925076189187"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1485.44,780.111) scale(2,2) translate(-737.722,-380.056)" width="10" x="1475.444444444444" y="760.111111111111"/></g>
  <g id="388">
   <use class="kv10" height="20" transform="rotate(0,1560.67,776.222) scale(2,2) translate(-775.333,-378.111)" width="10" x="1550.666666666667" xlink:href="#Breaker:小车断路器_0" y="756.2222222222222" zvalue="434"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925076320259" ObjectName="10kV3号电容器066断路器"/>
   <cge:TPSR_Ref TObjectID="6473925076320259"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1560.67,776.222) scale(2,2) translate(-775.333,-378.111)" width="10" x="1550.666666666667" y="756.2222222222222"/></g>
  <g id="378">
   <use class="kv10" height="20" transform="rotate(0,1638.67,776.222) scale(2,2) translate(-814.333,-378.111)" width="10" x="1628.666666666667" xlink:href="#Breaker:小车断路器_0" y="756.2222222222222" zvalue="448"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925076254723" ObjectName="10kV4号电容器067断路器"/>
   <cge:TPSR_Ref TObjectID="6473925076254723"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1638.67,776.222) scale(2,2) translate(-814.333,-378.111)" width="10" x="1628.666666666667" y="756.2222222222222"/></g>
  <g id="406">
   <use class="kv10" height="20" transform="rotate(0,1722.89,779) scale(2,2) translate(-856.444,-379.5)" width="10" x="1712.888888888889" xlink:href="#Breaker:小车断路器_0" y="758.9999999999999" zvalue="461"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925076385795" ObjectName="10kV#3站用变兼接地变068断路器"/>
   <cge:TPSR_Ref TObjectID="6473925076385795"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1722.89,779) scale(2,2) translate(-856.444,-379.5)" width="10" x="1712.888888888889" y="758.9999999999999"/></g>
  <g id="417">
   <use class="kv10" height="20" transform="rotate(0,1789,780.222) scale(2,2) translate(-889.5,-380.111)" width="10" x="1779" xlink:href="#Breaker:小车断路器_0" y="760.2222222222223" zvalue="479"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925076451331" ObjectName="10kV滇沙线069断路器"/>
   <cge:TPSR_Ref TObjectID="6473925076451331"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1789,780.222) scale(2,2) translate(-889.5,-380.111)" width="10" x="1779" y="760.2222222222223"/></g>
  <g id="427">
   <use class="kv10" height="20" transform="rotate(0,1854.78,779.75) scale(2,2) translate(-922.389,-379.875)" width="10" x="1844.777777777778" xlink:href="#Breaker:小车断路器_0" y="759.75" zvalue="493"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925076516867" ObjectName="10kV雅居乐Ⅱ回线071断路器"/>
   <cge:TPSR_Ref TObjectID="6473925076516867"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1854.78,779.75) scale(2,2) translate(-922.389,-379.875)" width="10" x="1844.777777777778" y="759.75"/></g>
  <g id="435">
   <use class="kv10" height="20" transform="rotate(90,1146,663.333) scale(2,2) translate(-568,-321.667)" width="10" x="1136" xlink:href="#Breaker:小车母联_0" y="643.3333333333334" zvalue="510"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925076582403" ObjectName="10kV分段013断路器"/>
   <cge:TPSR_Ref TObjectID="6473925076582403"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1146,663.333) scale(2,2) translate(-568,-321.667)" width="10" x="1136" y="643.3333333333334"/></g>
  <g id="491">
   <use class="kv110" height="20" transform="rotate(0,820,230) scale(1.32,1.2) translate(-197.188,-36.3333)" width="10" x="813.4" xlink:href="#Breaker:开关_0" y="218" zvalue="645"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925074812931" ObjectName="110k#1主变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473925074812931"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,820,230) scale(1.32,1.2) translate(-197.188,-36.3333)" width="10" x="813.4" y="218"/></g>
  <g id="496">
   <use class="kv110" height="20" transform="rotate(0,938,230) scale(1.32,1.2) translate(-225.794,-36.3333)" width="10" x="931.4" xlink:href="#Breaker:开关_0" y="218" zvalue="649"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925074747395" ObjectName="110kV汉滇线192断路器"/>
   <cge:TPSR_Ref TObjectID="6473925074747395"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,938,230) scale(1.32,1.2) translate(-225.794,-36.3333)" width="10" x="931.4" y="218"/></g>
  <g id="502">
   <use class="kv110" height="20" transform="rotate(270,1147,251) scale(1.4,1.3) translate(-325.714,-54.9231)" width="10" x="1140" xlink:href="#Breaker:母联开关_0" y="238" zvalue="654"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925075009539" ObjectName="110kV分段112断路器"/>
   <cge:TPSR_Ref TObjectID="6473925075009539"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1147,251) scale(1.4,1.3) translate(-325.714,-54.9231)" width="10" x="1140" y="238"/></g>
  <g id="507">
   <use class="kv110" height="20" transform="rotate(0,1349,230) scale(1.32,1.2) translate(-325.43,-36.3333)" width="10" x="1342.4" xlink:href="#Breaker:开关_0" y="218" zvalue="658"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925074878467" ObjectName="110kV瑞滇线193断路器"/>
   <cge:TPSR_Ref TObjectID="6473925074878467"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1349,230) scale(1.32,1.2) translate(-325.43,-36.3333)" width="10" x="1342.4" y="218"/></g>
  <g id="512">
   <use class="kv110" height="20" transform="rotate(0,1493.1,230) scale(1.32,1.2) translate(-360.364,-36.3333)" width="10" x="1486.5" xlink:href="#Breaker:开关_0" y="218" zvalue="662"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925074944003" ObjectName="110k#3主变110kV侧103断路器"/>
   <cge:TPSR_Ref TObjectID="6473925074944003"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1493.1,230) scale(1.32,1.2) translate(-360.364,-36.3333)" width="10" x="1486.5" y="218"/></g>
  <g id="520">
   <use class="kv10" height="20" transform="rotate(0,515,689.5) scale(2,-2) translate(-252.5,-1024.25)" width="10" x="505" xlink:href="#Breaker:小车断路器_0" y="669.5" zvalue="696"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925083201539" ObjectName="10kV联检线047断路器"/>
   <cge:TPSR_Ref TObjectID="6473925083201539"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,515,689.5) scale(2,-2) translate(-252.5,-1024.25)" width="10" x="505" y="669.5"/></g>
  <g id="532">
   <use class="kv10" height="20" transform="rotate(0,594,688.5) scale(2,-2) translate(-292,-1022.75)" width="10" x="584" xlink:href="#Breaker:小车断路器_0" y="668.5" zvalue="708"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925083267075" ObjectName="10kV新建路线046断路器"/>
   <cge:TPSR_Ref TObjectID="6473925083267075"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,594,688.5) scale(2,-2) translate(-292,-1022.75)" width="10" x="584" y="668.5"/></g>
  <g id="543">
   <use class="kv10" height="20" transform="rotate(0,691,688.5) scale(2,-2) translate(-340.5,-1022.75)" width="10" x="681" xlink:href="#Breaker:小车断路器_0" y="668.5" zvalue="720"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925083332611" ObjectName="10kV备用Ⅲ回线045断路器"/>
   <cge:TPSR_Ref TObjectID="6473925083332611"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,691,688.5) scale(2,-2) translate(-340.5,-1022.75)" width="10" x="681" y="668.5"/></g>
  <g id="563">
   <use class="kv10" height="20" transform="rotate(0,769,688.5) scale(2,-2) translate(-379.5,-1022.75)" width="10" x="759" xlink:href="#Breaker:小车断路器_0" y="668.5" zvalue="742"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925083398147" ObjectName="10kV备用Ⅳ回线044断路器"/>
   <cge:TPSR_Ref TObjectID="6473925083398147"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,769,688.5) scale(2,-2) translate(-379.5,-1022.75)" width="10" x="759" y="668.5"/></g>
  <g id="618">
   <use class="kv10" height="20" transform="rotate(0,1561,689.5) scale(2,-2) translate(-775.5,-1024.25)" width="10" x="1551" xlink:href="#Breaker:小车断路器_0" y="669.5" zvalue="754"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925083725829" ObjectName="10kV备用Ⅸ回线076断路器"/>
   <cge:TPSR_Ref TObjectID="6473925083725829"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1561,689.5) scale(2,-2) translate(-775.5,-1024.25)" width="10" x="1551" y="669.5"/></g>
  <g id="610">
   <use class="kv10" height="20" transform="rotate(0,1638,688.5) scale(2,-2) translate(-814,-1022.75)" width="10" x="1628" xlink:href="#Breaker:小车断路器_0" y="668.5" zvalue="765"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925083660293" ObjectName="10kV备用Ⅷ回线075断路器"/>
   <cge:TPSR_Ref TObjectID="6473925083660293"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1638,688.5) scale(2,-2) translate(-814,-1022.75)" width="10" x="1628" y="668.5"/></g>
  <g id="602">
   <use class="kv10" height="20" transform="rotate(0,1723,686.5) scale(2,-2) translate(-856.5,-1019.75)" width="10" x="1713" xlink:href="#Breaker:小车断路器_0" y="666.5" zvalue="776"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925083594755" ObjectName="10kV备用Ⅶ回线074断路器"/>
   <cge:TPSR_Ref TObjectID="6473925083594755"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1723,686.5) scale(2,-2) translate(-856.5,-1019.75)" width="10" x="1713" y="666.5"/></g>
  <g id="594">
   <use class="kv10" height="20" transform="rotate(0,1789,687.5) scale(2,-2) translate(-889.5,-1021.25)" width="10" x="1779" xlink:href="#Breaker:小车断路器_0" y="667.5" zvalue="787"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925083529219" ObjectName="10kV备用Ⅵ回线073断路器"/>
   <cge:TPSR_Ref TObjectID="6473925083529219"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1789,687.5) scale(2,-2) translate(-889.5,-1021.25)" width="10" x="1779" y="667.5"/></g>
  <g id="586">
   <use class="kv10" height="20" transform="rotate(0,1854.78,687.5) scale(2,-2) translate(-922.389,-1021.25)" width="10" x="1844.777777777778" xlink:href="#Breaker:小车断路器_0" y="667.5" zvalue="798"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925083463683" ObjectName="10kV飞海线072断路器"/>
   <cge:TPSR_Ref TObjectID="6473925083463683"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1854.78,687.5) scale(2,-2) translate(-922.389,-1021.25)" width="10" x="1844.777777777778" y="667.5"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="177">
   <use class="kv10" height="30" transform="rotate(180,1080,929) scale(0.416667,1.06667) translate(1508.5,-57.0625)" width="12" x="1077.5" xlink:href="#EnergyConsumer:负荷_0" y="913" zvalue="212"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453657165828" ObjectName="10kV团结Ⅰ回线"/>
   <cge:TPSR_Ref TObjectID="6192453657165828"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1080,929) scale(0.416667,1.06667) translate(1508.5,-57.0625)" width="12" x="1077.5" y="913"/></g>
  <g id="224">
   <use class="kv10" height="30" transform="rotate(180,1017,930) scale(0.416667,1.06667) translate(1420.3,-57.125)" width="12" x="1014.5" xlink:href="#EnergyConsumer:负荷_0" y="914" zvalue="248"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453657886724" ObjectName="10kV一号线"/>
   <cge:TPSR_Ref TObjectID="6192453657886724"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1017,930) scale(0.416667,1.06667) translate(1420.3,-57.125)" width="12" x="1014.5" y="914"/></g>
  <g id="235">
   <use class="kv10" height="30" transform="rotate(180,955,930) scale(0.416667,1.06667) translate(1333.5,-57.125)" width="12" x="952.5" xlink:href="#EnergyConsumer:负荷_0" y="914" zvalue="260"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453658148868" ObjectName="10kV四号线"/>
   <cge:TPSR_Ref TObjectID="6192453658148868"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,955,930) scale(0.416667,1.06667) translate(1333.5,-57.125)" width="12" x="952.5" y="914"/></g>
  <g id="246">
   <use class="kv10" height="30" transform="rotate(180,893,930) scale(0.416667,1.06667) translate(1246.7,-57.125)" width="12" x="890.5" xlink:href="#EnergyConsumer:负荷_0" y="914" zvalue="272"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453658411012" ObjectName="10kV三号线"/>
   <cge:TPSR_Ref TObjectID="6192453658411012"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,893,930) scale(0.416667,1.06667) translate(1246.7,-57.125)" width="12" x="890.5" y="914"/></g>
  <g id="257">
   <use class="kv10" height="30" transform="rotate(180,833,930) scale(0.416667,1.06667) translate(1162.7,-57.125)" width="12" x="830.5" xlink:href="#EnergyConsumer:负荷_0" y="914" zvalue="284"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453658673156" ObjectName="10kV二号线"/>
   <cge:TPSR_Ref TObjectID="6192453658673156"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,833,930) scale(0.416667,1.06667) translate(1162.7,-57.125)" width="12" x="830.5" y="914"/></g>
  <g id="30">
   <use class="kv10" height="30" transform="rotate(180,516,930) scale(0.416667,1.06667) translate(718.9,-57.125)" width="12" x="513.5" xlink:href="#EnergyConsumer:负荷_0" y="914" zvalue="332"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453659394052" ObjectName="10kV团结Ⅱ回线"/>
   <cge:TPSR_Ref TObjectID="6192453659394052"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,516,930) scale(0.416667,1.06667) translate(718.9,-57.125)" width="12" x="513.5" y="914"/></g>
  <g id="290">
   <use class="kv10" height="30" transform="rotate(180,448,929.5) scale(0.416667,1.06667) translate(623.7,-57.0937)" width="12" x="445.5" xlink:href="#EnergyConsumer:负荷_0" y="913.5" zvalue="342"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453659787268" ObjectName="10kV雅居乐Ⅰ回线"/>
   <cge:TPSR_Ref TObjectID="6192453659787268"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,448,929.5) scale(0.416667,1.06667) translate(623.7,-57.0937)" width="12" x="445.5" y="913.5"/></g>
  <g id="302">
   <use class="kv10" height="30" transform="rotate(180,448,537.5) scale(0.416667,-1.06667) translate(623.7,-1040.41)" width="12" x="445.5" xlink:href="#EnergyConsumer:负荷_0" y="521.5" zvalue="355"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453660049411" ObjectName="10kV十一号线"/>
   <cge:TPSR_Ref TObjectID="6192453660049411"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,448,537.5) scale(0.416667,-1.06667) translate(623.7,-1040.41)" width="12" x="445.5" y="521.5"/></g>
  <g id="316">
   <use class="kv10" height="30" transform="rotate(180,1221.33,931.111) scale(0.416667,1.06667) translate(1706.37,-57.1944)" width="12" x="1218.833333333333" xlink:href="#EnergyConsumer:负荷_0" y="915.1111111111111" zvalue="372"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453660442627" ObjectName="10kV广母Ⅰ回线"/>
   <cge:TPSR_Ref TObjectID="6192453660442627"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1221.33,931.111) scale(0.416667,1.06667) translate(1706.37,-57.1944)" width="12" x="1218.833333333333" y="915.1111111111111"/></g>
  <g id="327">
   <use class="kv10" height="30" transform="rotate(180,1289,931.111) scale(0.416667,1.06667) translate(1801.1,-57.1944)" width="12" x="1286.5" xlink:href="#EnergyConsumer:负荷_0" y="915.111111111111" zvalue="384"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453660704771" ObjectName="10kV广母Ⅱ回线"/>
   <cge:TPSR_Ref TObjectID="6192453660704771"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1289,931.111) scale(0.416667,1.06667) translate(1801.1,-57.1944)" width="12" x="1286.5" y="915.111111111111"/></g>
  <g id="349">
   <use class="kv10" height="30" transform="rotate(180,1357.44,931.111) scale(0.416667,1.06667) translate(1896.92,-57.1944)" width="12" x="1354.944444444444" xlink:href="#EnergyConsumer:负荷_0" y="915.1111111111111" zvalue="396"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453661229060" ObjectName="10kV姐东线"/>
   <cge:TPSR_Ref TObjectID="6192453661229060"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1357.44,931.111) scale(0.416667,1.06667) translate(1896.92,-57.1944)" width="12" x="1354.944444444444" y="915.1111111111111"/></g>
  <g id="341">
   <use class="kv10" height="30" transform="rotate(180,1425.11,931.111) scale(0.416667,1.06667) translate(1991.66,-57.1944)" width="12" x="1422.611111111111" xlink:href="#EnergyConsumer:负荷_0" y="915.111111111111" zvalue="407"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453660966916" ObjectName="10kV金滇线"/>
   <cge:TPSR_Ref TObjectID="6192453660966916"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1425.11,931.111) scale(0.416667,1.06667) translate(1991.66,-57.1944)" width="12" x="1422.611111111111" y="915.111111111111"/></g>
  <g id="361">
   <use class="kv10" height="30" transform="rotate(180,1485.44,931.111) scale(0.416667,1.06667) translate(2076.12,-57.1944)" width="12" x="1482.944444444444" xlink:href="#EnergyConsumer:负荷_0" y="915.111111111111" zvalue="420"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453661491204" ObjectName="10kV景成医院线"/>
   <cge:TPSR_Ref TObjectID="6192453661491204"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1485.44,931.111) scale(0.416667,1.06667) translate(2076.12,-57.1944)" width="12" x="1482.944444444444" y="915.111111111111"/></g>
  <g id="411">
   <use class="kv10" height="30" transform="rotate(180,1790.78,932.222) scale(0.416667,1.06667) translate(2503.59,-57.2639)" width="12" x="1788.277777777778" xlink:href="#EnergyConsumer:负荷_0" y="916.2222222222223" zvalue="487"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453662408708" ObjectName="10kV滇沙线"/>
   <cge:TPSR_Ref TObjectID="6192453662408708"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1790.78,932.222) scale(0.416667,1.06667) translate(2503.59,-57.2639)" width="12" x="1788.277777777778" y="916.2222222222223"/></g>
  <g id="428">
   <use class="kv10" height="30" transform="rotate(180,1855.78,931.75) scale(0.416667,1.06667) translate(2594.59,-57.2344)" width="12" x="1853.277777777778" xlink:href="#EnergyConsumer:负荷_0" y="915.75" zvalue="491"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453662867458" ObjectName="10kV雅居乐Ⅱ回线"/>
   <cge:TPSR_Ref TObjectID="6192453662867458"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1855.78,931.75) scale(0.416667,1.06667) translate(2594.59,-57.2344)" width="12" x="1853.277777777778" y="915.75"/></g>
  <g id="444">
   <use class="kv10" height="30" transform="rotate(0,594.699,917.169) scale(1.62698,1.63056) translate(-220.399,-345.222)" width="28" x="571.9209576545242" xlink:href="#EnergyConsumer:站用变DY接地_0" y="892.7110097449374" zvalue="520"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454748930050" ObjectName="10kV#1站用变兼接地变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,594.699,917.169) scale(1.62698,1.63056) translate(-220.399,-345.222)" width="28" x="571.9209576545242" y="892.7110097449374"/></g>
  <g id="448">
   <use class="kv10" height="30" transform="rotate(0,1723.11,932.542) scale(1.62698,1.63056) translate(-655.25,-351.167)" width="28" x="1700.333333333333" xlink:href="#EnergyConsumer:站用变DY接地_0" y="908.0833333333328" zvalue="524"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454748995586" ObjectName="10kV#3站用变兼接地变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1723.11,932.542) scale(1.62698,1.63056) translate(-655.25,-351.167)" width="28" x="1700.333333333333" y="908.0833333333328"/></g>
  <g id="521">
   <use class="kv10" height="30" transform="rotate(180,515,537.5) scale(0.416667,-1.06667) translate(717.5,-1040.41)" width="12" x="512.5" xlink:href="#EnergyConsumer:负荷_0" y="521.5" zvalue="694"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453715427330" ObjectName="10kV联检线"/>
   <cge:TPSR_Ref TObjectID="6192453715427330"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,515,537.5) scale(0.416667,-1.06667) translate(717.5,-1040.41)" width="12" x="512.5" y="521.5"/></g>
  <g id="533">
   <use class="kv10" height="30" transform="rotate(180,594,536.5) scale(0.416667,-1.06667) translate(828.1,-1038.47)" width="12" x="591.5" xlink:href="#EnergyConsumer:负荷_0" y="520.5" zvalue="706"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453715689474" ObjectName="10kV新建路线"/>
   <cge:TPSR_Ref TObjectID="6192453715689474"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,594,536.5) scale(0.416667,-1.06667) translate(828.1,-1038.47)" width="12" x="591.5" y="520.5"/></g>
  <g id="544">
   <use class="kv10" height="30" transform="rotate(180,691,536.5) scale(0.416667,-1.06667) translate(963.9,-1038.47)" width="12" x="688.5" xlink:href="#EnergyConsumer:负荷_0" y="520.5" zvalue="718"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453715951618" ObjectName="10kV备用Ⅲ回线"/>
   <cge:TPSR_Ref TObjectID="6192453715951618"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,691,536.5) scale(0.416667,-1.06667) translate(963.9,-1038.47)" width="12" x="688.5" y="520.5"/></g>
  <g id="564">
   <use class="kv10" height="30" transform="rotate(180,769,536.5) scale(0.416667,-1.06667) translate(1073.1,-1038.47)" width="12" x="766.5" xlink:href="#EnergyConsumer:负荷_0" y="520.5" zvalue="740"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453716213762" ObjectName="10kV备用Ⅳ回线"/>
   <cge:TPSR_Ref TObjectID="6192453716213762"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,769,536.5) scale(0.416667,-1.06667) translate(1073.1,-1038.47)" width="12" x="766.5" y="520.5"/></g>
  <g id="619">
   <use class="kv10" height="30" transform="rotate(180,1562,537.5) scale(0.416667,-1.06667) translate(2183.3,-1040.41)" width="12" x="1559.5" xlink:href="#EnergyConsumer:负荷_0" y="521.5" zvalue="752"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453717524482" ObjectName="10kV备用Ⅸ回线"/>
   <cge:TPSR_Ref TObjectID="6192453717524482"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1562,537.5) scale(0.416667,-1.06667) translate(2183.3,-1040.41)" width="12" x="1559.5" y="521.5"/></g>
  <g id="611">
   <use class="kv10" height="30" transform="rotate(180,1638,536.5) scale(0.416667,-1.06667) translate(2289.7,-1038.47)" width="12" x="1635.5" xlink:href="#EnergyConsumer:负荷_0" y="520.5" zvalue="763"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453717262338" ObjectName="10kV备用Ⅷ回线"/>
   <cge:TPSR_Ref TObjectID="6192453717262338"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1638,536.5) scale(0.416667,-1.06667) translate(2289.7,-1038.47)" width="12" x="1635.5" y="520.5"/></g>
  <g id="603">
   <use class="kv10" height="30" transform="rotate(180,1723,534.5) scale(0.416667,-1.06667) translate(2408.7,-1034.59)" width="12" x="1720.5" xlink:href="#EnergyConsumer:负荷_0" y="518.5" zvalue="774"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453717000194" ObjectName="10kV备用Ⅶ回线"/>
   <cge:TPSR_Ref TObjectID="6192453717000194"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1723,534.5) scale(0.416667,-1.06667) translate(2408.7,-1034.59)" width="12" x="1720.5" y="518.5"/></g>
  <g id="595">
   <use class="kv10" height="30" transform="rotate(180,1789,535.5) scale(0.416667,-1.06667) translate(2501.1,-1036.53)" width="12" x="1786.5" xlink:href="#EnergyConsumer:负荷_0" y="519.5" zvalue="785"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453716738050" ObjectName="10kV备用Ⅵ回线"/>
   <cge:TPSR_Ref TObjectID="6192453716738050"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1789,535.5) scale(0.416667,-1.06667) translate(2501.1,-1036.53)" width="12" x="1786.5" y="519.5"/></g>
  <g id="587">
   <use class="kv10" height="30" transform="rotate(180,1855,535.5) scale(0.416667,-1.06667) translate(2593.5,-1036.53)" width="12" x="1852.5" xlink:href="#EnergyConsumer:负荷_0" y="519.5" zvalue="796"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453716475906" ObjectName="10kV飞海线"/>
   <cge:TPSR_Ref TObjectID="6192453716475906"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1855,535.5) scale(0.416667,-1.06667) translate(2593.5,-1036.53)" width="12" x="1852.5" y="519.5"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="182">
   <use class="kv10" height="50" transform="rotate(0,686.84,894.812) scale(1.3472,1.21248) translate(-172.672,-151.499)" width="25" x="669.9999999999999" xlink:href="#Compensator:电容20200722_0" y="864.4999999999999" zvalue="532"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453657427972" ObjectName="10kV1号电容器"/>
   <cge:TPSR_Ref TObjectID="6192453657427972"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,686.84,894.812) scale(1.3472,1.21248) translate(-172.672,-151.499)" width="25" x="669.9999999999999" y="864.4999999999999"/></g>
  <g id="212">
   <use class="kv10" height="50" transform="rotate(0,764.84,895.812) scale(1.3472,1.21248) translate(-192.774,-151.674)" width="25" x="747.9999999999999" xlink:href="#Compensator:电容20200722_0" y="865.4999999999999" zvalue="539"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453658935300" ObjectName="10kV2号电容器"/>
   <cge:TPSR_Ref TObjectID="6192453658935300"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,764.84,895.812) scale(1.3472,1.21248) translate(-192.774,-151.674)" width="25" x="747.9999999999999" y="865.4999999999999"/></g>
  <g id="390">
   <use class="kv10" height="50" transform="rotate(0,1556.63,897.812) scale(1.3472,1.21248) translate(-396.833,-152.024)" width="25" x="1539.785066666667" xlink:href="#Compensator:电容20200722_0" y="867.4999999999999" zvalue="543"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453662015492" ObjectName="10kV3号电容器"/>
   <cge:TPSR_Ref TObjectID="6192453662015492"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1556.63,897.812) scale(1.3472,1.21248) translate(-396.833,-152.024)" width="25" x="1539.785066666667" y="867.4999999999999"/></g>
  <g id="399">
   <use class="kv10" height="50" transform="rotate(0,1634.63,897.812) scale(1.3472,1.21248) translate(-416.935,-152.024)" width="25" x="1617.785066666667" xlink:href="#Compensator:电容20200722_0" y="867.4999999999999" zvalue="547"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453661753348" ObjectName="10kV4号电容器"/>
   <cge:TPSR_Ref TObjectID="6192453661753348"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1634.63,897.812) scale(1.3472,1.21248) translate(-416.935,-152.024)" width="25" x="1617.785066666667" y="867.4999999999999"/></g>
 </g>
 <g id="StateClass">
  <g id="303">
   <use height="30" transform="rotate(0,339.625,319) scale(0.708333,0.665547) translate(135.471,155.288)" width="30" x="329" xlink:href="#State:红绿圆(方形)_0" y="309.02" zvalue="569"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374920712193" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,339.625,319) scale(0.708333,0.665547) translate(135.471,155.288)" width="30" x="329" y="309.02"/></g>
  <g id="1035">
   <use height="30" transform="rotate(0,244,319) scale(0.708333,0.665547) translate(96.0956,155.288)" width="30" x="233.38" xlink:href="#State:红绿圆(方形)_0" y="309.02" zvalue="570"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562953950003207" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,244,319) scale(0.708333,0.665547) translate(96.0956,155.288)" width="30" x="233.38" y="309.02"/></g>
  <g id="993">
   <use height="30" transform="rotate(0,328.812,127.464) scale(1.22222,1.03092) translate(-49.7841,-3.35937)" width="90" x="273.81" xlink:href="#State:全站检修_0" y="112" zvalue="1072"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549677916161" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,328.812,127.464) scale(1.22222,1.03092) translate(-49.7841,-3.35937)" width="90" x="273.81" y="112"/></g>
  <g id="193">
   <use height="30" transform="rotate(0,296.235,405) scale(0.910937,0.8) translate(25.4004,98.25)" width="80" x="259.8" xlink:href="#State:间隔模板_0" y="393" zvalue="1198"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5629499534213125" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,296.235,405) scale(0.910937,0.8) translate(25.4004,98.25)" width="80" x="259.8" y="393"/></g>
  <g id="1082">
   <use height="30" transform="rotate(0,93.2857,323.539) scale(0.910937,0.8) translate(5.55806,77.8847)" width="80" x="56.85" xlink:href="#State:间隔模板_0" y="311.54" zvalue="1199"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5629500324904962" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,93.2857,323.539) scale(0.910937,0.8) translate(5.55806,77.8847)" width="80" x="56.85" y="311.54"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="434">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="434" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,133,496.5) scale(1,1) translate(0,0)" writing-mode="lr" x="132.7" xml:space="preserve" y="501.17" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132527583235" ObjectName=""/>
   </metadata>
  </g>
  <g id="452">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="452" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,192.333,498) scale(1,1) translate(0,0)" writing-mode="lr" x="192.04" xml:space="preserve" y="502.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132528107523" ObjectName=""/>
   </metadata>
  </g>
  <g id="453">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="453" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,250.667,498) scale(1,1) translate(0,0)" writing-mode="lr" x="250.37" xml:space="preserve" y="502.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132536627203" ObjectName=""/>
   </metadata>
  </g>
  <g id="454">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="454" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,308,497) scale(1,1) translate(0,0)" writing-mode="lr" x="307.7" xml:space="preserve" y="501.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132537151492" ObjectName=""/>
   </metadata>
  </g>
  <g id="458">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="458" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,133,522) scale(1,1) translate(0,0)" writing-mode="lr" x="132.7" xml:space="preserve" y="526.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132527321091" ObjectName=""/>
   </metadata>
  </g>
  <g id="457">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="457" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,192.333,522) scale(1,1) translate(0,0)" writing-mode="lr" x="192.04" xml:space="preserve" y="526.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132527845379" ObjectName=""/>
   </metadata>
  </g>
  <g id="456">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="456" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,250.667,522) scale(1,1) translate(0,0)" writing-mode="lr" x="250.37" xml:space="preserve" y="526.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132536365059" ObjectName=""/>
   </metadata>
  </g>
  <g id="455">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="455" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,308,522) scale(1,1) translate(0,0)" writing-mode="lr" x="307.7" xml:space="preserve" y="526.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132536889347" ObjectName=""/>
   </metadata>
  </g>
  <g id="462">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="462" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,133,546) scale(1,1) translate(0,0)" writing-mode="lr" x="132.7" xml:space="preserve" y="550.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132527386627" ObjectName=""/>
   </metadata>
  </g>
  <g id="461">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="461" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,192.333,546) scale(1,1) translate(0,0)" writing-mode="lr" x="192.04" xml:space="preserve" y="550.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132527910915" ObjectName=""/>
   </metadata>
  </g>
  <g id="460">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="460" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,250.667,546) scale(1,1) translate(0,0)" writing-mode="lr" x="250.37" xml:space="preserve" y="550.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132536430595" ObjectName=""/>
   </metadata>
  </g>
  <g id="459">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="459" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,308,546) scale(1,1) translate(0,0)" writing-mode="lr" x="307.7" xml:space="preserve" y="550.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132536954883" ObjectName=""/>
   </metadata>
  </g>
  <g id="466">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="466" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,133,571) scale(1,1) translate(0,0)" writing-mode="lr" x="132.7" xml:space="preserve" y="575.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132527452164" ObjectName=""/>
   </metadata>
  </g>
  <g id="465">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="465" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,192.333,571) scale(1,1) translate(0,0)" writing-mode="lr" x="192.04" xml:space="preserve" y="575.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132527976451" ObjectName=""/>
   </metadata>
  </g>
  <g id="464">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="464" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,250.667,571) scale(1,1) translate(0,0)" writing-mode="lr" x="250.37" xml:space="preserve" y="575.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132536496131" ObjectName=""/>
   </metadata>
  </g>
  <g id="463">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="463" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,308,571) scale(1,1) translate(0,0)" writing-mode="lr" x="307.7" xml:space="preserve" y="575.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132537020419" ObjectName=""/>
   </metadata>
  </g>
  <g id="470">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="470" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,133,596) scale(1,1) translate(0,0)" writing-mode="lr" x="132.7" xml:space="preserve" y="600.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132527779843" ObjectName=""/>
   </metadata>
  </g>
  <g id="469">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="469" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,192.333,596) scale(1,1) translate(0,0)" writing-mode="lr" x="192.04" xml:space="preserve" y="600.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132528304131" ObjectName=""/>
   </metadata>
  </g>
  <g id="468">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="468" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,250.667,596) scale(1,1) translate(0,0)" writing-mode="lr" x="250.37" xml:space="preserve" y="600.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132536823811" ObjectName=""/>
   </metadata>
  </g>
  <g id="467">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="467" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,308,596) scale(1,1) translate(0,0)" writing-mode="lr" x="307.7" xml:space="preserve" y="600.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132537348100" ObjectName=""/>
   </metadata>
  </g>
  <g id="476">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="476" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,169,178.5) scale(1,1) translate(0,0)" writing-mode="lr" x="168.64" xml:space="preserve" y="184.2" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132569722883" ObjectName=""/>
   </metadata>
  </g>
  <g id="477">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="477" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,348,177) scale(1,1) translate(0,0)" writing-mode="lr" x="347.64" xml:space="preserve" y="182.7" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132569788420" ObjectName=""/>
   </metadata>
  </g>
  <g id="480">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="15" id="480" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,169,203) scale(1,1) translate(0,0)" writing-mode="lr" x="168.64" xml:space="preserve" y="208.7" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132527714307" ObjectName=""/>
   </metadata>
  </g>
  <g id="481">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="15" id="481" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,348,204) scale(1,1) translate(0,0)" writing-mode="lr" x="347.64" xml:space="preserve" y="209.7" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132528238595" ObjectName=""/>
   </metadata>
  </g>
  <g id="484">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="484" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,169,251) scale(1,1) translate(0,0)" writing-mode="lr" x="168.64" xml:space="preserve" y="256.7" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132532891651" ObjectName=""/>
   </metadata>
  </g>
  <g id="485">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="485" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,348,251) scale(1,1) translate(0,0)" writing-mode="lr" x="347.64" xml:space="preserve" y="256.7" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132534595588" ObjectName=""/>
   </metadata>
  </g>
  <g id="488">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" id="488" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,169,274) scale(1,1) translate(0,0)" writing-mode="lr" x="168.64" xml:space="preserve" y="279.7" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132532957187" ObjectName=""/>
   </metadata>
  </g>
  <g id="489">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" id="489" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,348,274) scale(1,1) translate(0,0)" writing-mode="lr" x="347.64" xml:space="preserve" y="279.7" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132534661124" ObjectName=""/>
   </metadata>
  </g>
  <g id="39">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="15" id="39" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,169,227) scale(1,1) translate(0,0)" writing-mode="lr" x="168.64" xml:space="preserve" y="232.7" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132536758275" ObjectName=""/>
   </metadata>
  </g>
  <g id="61">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="15" id="61" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,348,228) scale(1,1) translate(0,0)" writing-mode="lr" x="347.64" xml:space="preserve" y="233.7" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132537282564" ObjectName=""/>
   </metadata>
  </g>
  <g id="64">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="64" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,938.5,17) scale(1,1) translate(0,0)" writing-mode="lr" x="938.03" xml:space="preserve" y="21.67" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132528369667" ObjectName="P"/>
   </metadata>
  </g>
  <g id="66">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="66" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1349,16) scale(1,1) translate(0,0)" writing-mode="lr" x="1348.53" xml:space="preserve" y="20.67" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132530925572" ObjectName="P"/>
   </metadata>
  </g>
  <g id="84">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="84" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,938.5,31.3333) scale(1,1) translate(0,0)" writing-mode="lr" x="938.03" xml:space="preserve" y="36" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132528435203" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="86">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="86" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1349,31) scale(1,1) translate(0,0)" writing-mode="lr" x="1348.53" xml:space="preserve" y="35.67" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132530991108" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="93">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="93" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,938.5,45.6667) scale(1,1) translate(0,0)" writing-mode="lr" x="938.03" xml:space="preserve" y="50.33" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132528500739" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="100">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="100" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1349,46) scale(1,1) translate(0,0)" writing-mode="lr" x="1348.53" xml:space="preserve" y="50.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132531056644" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="103">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="103" prefix="Ux:" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,938.5,60) scale(1,1) translate(0,0)" writing-mode="lr" x="938.03" xml:space="preserve" y="64.67" zvalue="1">Ux:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132529156099" ObjectName="Ux"/>
   </metadata>
  </g>
  <g id="105">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="105" prefix="Ux:" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1349,61) scale(1,1) translate(0,0)" writing-mode="lr" x="1348.53" xml:space="preserve" y="65.67" zvalue="1">Ux:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132531712004" ObjectName="Ux"/>
   </metadata>
  </g>
  <g id="125">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="125" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,820.25,63) scale(1,1) translate(0,0)" writing-mode="lr" x="819.78" xml:space="preserve" y="67.7" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132532432899" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="126">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="126" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,820.25,82) scale(1,1) translate(0,0)" writing-mode="lr" x="819.78" xml:space="preserve" y="86.7" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132532498435" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="127">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="127" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,925.25,606) scale(1,1) translate(0,0)" writing-mode="lr" x="924.78" xml:space="preserve" y="610.7" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132532563971" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="128">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="128" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,925.25,625) scale(1,1) translate(0,0)" writing-mode="lr" x="924.78" xml:space="preserve" y="629.7" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132532629507" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="300">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="300" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,820.25,101) scale(1,1) translate(0,0)" writing-mode="lr" x="819.78" xml:space="preserve" y="105.7" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132532695043" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="394">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="394" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,925.25,644) scale(1,1) translate(0,0)" writing-mode="lr" x="924.78" xml:space="preserve" y="648.7" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132533022723" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="490">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="490" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1142,163.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1141.53" xml:space="preserve" y="168.17" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132536037379" ObjectName="P"/>
   </metadata>
  </g>
  <g id="495">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="495" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1141,182.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1140.53" xml:space="preserve" y="187.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132536102915" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="500">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="500" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1141,200.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1140.53" xml:space="preserve" y="205.17" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132535840772" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="522">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="522" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1488,63) scale(1,1) translate(0,0)" writing-mode="lr" x="1487.53" xml:space="preserve" y="67.7" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132534136836" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="545">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="545" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1488,82) scale(1,1) translate(0,0)" writing-mode="lr" x="1487.53" xml:space="preserve" y="86.7" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132534202372" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="546">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="546" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1487,606) scale(1,1) translate(0,0)" writing-mode="lr" x="1486.53" xml:space="preserve" y="610.7" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132534267908" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="547">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="547" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1487,625) scale(1,1) translate(0,0)" writing-mode="lr" x="1486.53" xml:space="preserve" y="629.7" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132534333444" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="548">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="548" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1488,101) scale(1,1) translate(0,0)" writing-mode="lr" x="1487.53" xml:space="preserve" y="105.7" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132534398980" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="549">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="549" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1487,644) scale(1,1) translate(0,0)" writing-mode="lr" x="1486.53" xml:space="preserve" y="648.7" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132534726660" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="550">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="550" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1148,582.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1147.53" xml:space="preserve" y="587.5" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132565987332" ObjectName="P"/>
   </metadata>
  </g>
  <g id="551">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="551" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1148,599.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1147.53" xml:space="preserve" y="604.5" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132566052868" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="552">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="552" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1147,615.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1146.53" xml:space="preserve" y="620.5" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132565790724" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="447">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="447" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,435.75,1000.5) scale(1,1) translate(2.63789e-13,0)" writing-mode="lr" x="435.34" xml:space="preserve" y="1005.17" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132550389764" ObjectName="P"/>
   </metadata>
  </g>
  <g id="451">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="451" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,435.75,1020) scale(1,1) translate(2.63789e-13,0)" writing-mode="lr" x="435.34" xml:space="preserve" y="1024.67" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132550455300" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="622">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="622" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,435.75,1039) scale(1,1) translate(2.63789e-13,0)" writing-mode="lr" x="435.34" xml:space="preserve" y="1043.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132550520836" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="9">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="9" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,505.778,1000.5) scale(1,1) translate(3.10437e-13,0)" writing-mode="lr" x="505.36" xml:space="preserve" y="1005.17" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132548882436" ObjectName="P"/>
   </metadata>
  </g>
  <g id="553">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="553" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1082.63,999.5) scale(1,1) translate(-2.31566e-13,0)" writing-mode="lr" x="1082.22" xml:space="preserve" y="1004.17" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132538462212" ObjectName="P"/>
   </metadata>
  </g>
  <g id="624">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="624" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1019.63,1000.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1019.22" xml:space="preserve" y="1005.17" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132541411332" ObjectName="P"/>
   </metadata>
  </g>
  <g id="626">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="626" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,957.632,1000.5) scale(1,1) translate(0,0)" writing-mode="lr" x="957.22" xml:space="preserve" y="1005.17" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132542918660" ObjectName="P"/>
   </metadata>
  </g>
  <g id="634">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="634" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,891.889,1000.5) scale(1,1) translate(0,0)" writing-mode="lr" x="891.47" xml:space="preserve" y="1005.17" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132544425988" ObjectName="P"/>
   </metadata>
  </g>
  <g id="635">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="635" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,824.111,1000.5) scale(1,1) translate(0,0)" writing-mode="lr" x="823.7" xml:space="preserve" y="1005.17" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132545933315" ObjectName="P"/>
   </metadata>
  </g>
  <g id="636">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="636" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,448,429) scale(1,1) translate(2.71949e-13,0)" writing-mode="lr" x="447.59" xml:space="preserve" y="433.67" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132551897091" ObjectName="P"/>
   </metadata>
  </g>
  <g id="637">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="637" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1221.33,1000.5) scale(1,1) translate(-2.62364e-13,0)" writing-mode="lr" x="1220.92" xml:space="preserve" y="1005.17" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132553404419" ObjectName="P"/>
   </metadata>
  </g>
  <g id="638">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="638" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1289,1000.5) scale(1,1) translate(-2.77389e-13,0)" writing-mode="lr" x="1288.59" xml:space="preserve" y="1005.17" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132554911747" ObjectName="P"/>
   </metadata>
  </g>
  <g id="639">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="639" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1425.11,1000.5) scale(1,1) translate(-3.07612e-13,0)" writing-mode="lr" x="1424.7" xml:space="preserve" y="1005.17" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132556419075" ObjectName="P"/>
   </metadata>
  </g>
  <g id="640">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="640" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1357.44,1000.5) scale(1,1) translate(-2.92587e-13,0)" writing-mode="lr" x="1357.03" xml:space="preserve" y="1005.17" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132557926403" ObjectName="P"/>
   </metadata>
  </g>
  <g id="641">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="641" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1485.44,1000.5) scale(1,1) translate(-3.21009e-13,0)" writing-mode="lr" x="1485.03" xml:space="preserve" y="1005.17" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132559433732" ObjectName="P"/>
   </metadata>
  </g>
  <g id="642">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="642" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1790.78,1000.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1790.36" xml:space="preserve" y="1005.17" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132562776068" ObjectName="P"/>
   </metadata>
  </g>
  <g id="643">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="643" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1855.78,1000.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1855.36" xml:space="preserve" y="1005.17" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132564807684" ObjectName="P"/>
   </metadata>
  </g>
  <g id="644">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="644" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,515,429) scale(1,1) translate(3.1658e-13,0)" writing-mode="lr" x="514.59" xml:space="preserve" y="433.67" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132740640772" ObjectName="P"/>
   </metadata>
  </g>
  <g id="645">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="645" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,594,428) scale(1,1) translate(3.69205e-13,0)" writing-mode="lr" x="593.59" xml:space="preserve" y="432.67" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132742148099" ObjectName="P"/>
   </metadata>
  </g>
  <g id="646">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="646" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,691,428) scale(1,1) translate(2.1691e-13,0)" writing-mode="lr" x="690.59" xml:space="preserve" y="432.67" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132743655428" ObjectName="P"/>
   </metadata>
  </g>
  <g id="647">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="647" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,769,428) scale(1,1) translate(0,0)" writing-mode="lr" x="768.59" xml:space="preserve" y="432.67" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132745162755" ObjectName="P"/>
   </metadata>
  </g>
  <g id="648">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="648" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1855,428) scale(1,1) translate(0,0)" writing-mode="lr" x="1854.59" xml:space="preserve" y="432.67" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132746670083" ObjectName="P"/>
   </metadata>
  </g>
  <g id="649">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="649" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1789,428) scale(1,1) translate(0,0)" writing-mode="lr" x="1788.59" xml:space="preserve" y="432.67" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132748177411" ObjectName="P"/>
   </metadata>
  </g>
  <g id="650">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="650" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1723,428) scale(1,1) translate(0,0)" writing-mode="lr" x="1722.59" xml:space="preserve" y="432.67" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132749684739" ObjectName="P"/>
   </metadata>
  </g>
  <g id="651">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="651" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1638,428) scale(1,1) translate(0,0)" writing-mode="lr" x="1637.59" xml:space="preserve" y="432.67" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132751192067" ObjectName="P"/>
   </metadata>
  </g>
  <g id="652">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="652" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1562,428) scale(1,1) translate(0,0)" writing-mode="lr" x="1561.59" xml:space="preserve" y="432.67" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132752699395" ObjectName="P"/>
   </metadata>
  </g>
  <g id="653">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="653" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,505.778,1019.47) scale(1,1) translate(3.10437e-13,1.90432e-12)" writing-mode="lr" x="505.36" xml:space="preserve" y="1024.14" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132548947971" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="654">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="654" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1082.63,1018.47) scale(1,1) translate(-2.31566e-13,0)" writing-mode="lr" x="1082.22" xml:space="preserve" y="1023.14" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132538527748" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="655">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="655" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1019.63,1019.47) scale(1,1) translate(0,0)" writing-mode="lr" x="1019.22" xml:space="preserve" y="1024.14" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132541476868" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="656">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="656" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,957.632,1019.47) scale(1,1) translate(0,0)" writing-mode="lr" x="957.22" xml:space="preserve" y="1024.14" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132542984196" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="657">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="657" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,891.889,1019.47) scale(1,1) translate(0,0)" writing-mode="lr" x="891.47" xml:space="preserve" y="1024.14" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132544491524" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="658">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="658" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,824.111,1019.47) scale(1,1) translate(1.74163e-13,0)" writing-mode="lr" x="823.7" xml:space="preserve" y="1024.14" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132545998851" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="659">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="659" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,448,446) scale(1,1) translate(2.71949e-13,0)" writing-mode="lr" x="447.59" xml:space="preserve" y="450.67" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132551962627" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="660">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="660" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1221.33,1019.47) scale(1,1) translate(-2.62364e-13,0)" writing-mode="lr" x="1220.92" xml:space="preserve" y="1024.14" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132553469955" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="661">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="661" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1289,1019.47) scale(1,1) translate(-2.77389e-13,0)" writing-mode="lr" x="1288.59" xml:space="preserve" y="1024.14" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132554977283" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="662">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="662" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1425.11,1019.47) scale(1,1) translate(-3.07612e-13,0)" writing-mode="lr" x="1424.7" xml:space="preserve" y="1024.14" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132556484612" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="663">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="663" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1357.44,1019.47) scale(1,1) translate(-2.92587e-13,0)" writing-mode="lr" x="1357.03" xml:space="preserve" y="1024.14" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132557991939" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="664">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="664" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1485.44,1019.47) scale(1,1) translate(-3.21009e-13,0)" writing-mode="lr" x="1485.03" xml:space="preserve" y="1024.14" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132559499268" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="665">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="665" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1790.78,1019.47) scale(1,1) translate(0,0)" writing-mode="lr" x="1790.36" xml:space="preserve" y="1024.14" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132562841604" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="666">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="666" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1855.78,1019.47) scale(1,1) translate(0,0)" writing-mode="lr" x="1855.36" xml:space="preserve" y="1024.14" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132564873220" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="667">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="667" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,515,446) scale(1,1) translate(3.1658e-13,0)" writing-mode="lr" x="514.59" xml:space="preserve" y="450.67" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132740706307" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="668">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="668" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,594,445) scale(1,1) translate(3.69205e-13,0)" writing-mode="lr" x="593.59" xml:space="preserve" y="449.67" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132742213635" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="669">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="669" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,691,445) scale(1,1) translate(2.1691e-13,0)" writing-mode="lr" x="690.59" xml:space="preserve" y="449.67" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132743720964" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="670">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="670" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,769.909,444.091) scale(1,1) translate(0,0)" writing-mode="lr" x="769.5" xml:space="preserve" y="448.76" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132745228291" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="671">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="671" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1855,444.091) scale(1,1) translate(1.2092e-12,0)" writing-mode="lr" x="1854.59" xml:space="preserve" y="448.76" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132746735620" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="672">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="672" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1789,444.091) scale(1,1) translate(1.16523e-12,0)" writing-mode="lr" x="1788.59" xml:space="preserve" y="448.76" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132748242948" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="673">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="673" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1723.91,444.091) scale(1,1) translate(1.12188e-12,0)" writing-mode="lr" x="1723.5" xml:space="preserve" y="448.76" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132749750275" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="674">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="674" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1638.91,444.091) scale(1,1) translate(1.06525e-12,0)" writing-mode="lr" x="1638.5" xml:space="preserve" y="448.76" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132751257603" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="675">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="675" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1562.91,444.091) scale(1,1) translate(1.01463e-12,0)" writing-mode="lr" x="1562.5" xml:space="preserve" y="448.76" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132752764931" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="676">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="676" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,506.889,1039) scale(1,1) translate(3.11177e-13,0)" writing-mode="lr" x="506.47" xml:space="preserve" y="1043.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132549013507" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="677">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="677" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1082.63,1038) scale(1,1) translate(-2.31566e-13,0)" writing-mode="lr" x="1082.22" xml:space="preserve" y="1042.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132538593284" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="678">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="678" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1019.63,1039) scale(1,1) translate(0,0)" writing-mode="lr" x="1019.22" xml:space="preserve" y="1043.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132541542404" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="679">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="679" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,957.632,1039) scale(1,1) translate(0,0)" writing-mode="lr" x="957.22" xml:space="preserve" y="1043.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132543049732" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="680">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="680" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,891.889,1039) scale(1,1) translate(0,0)" writing-mode="lr" x="891.47" xml:space="preserve" y="1043.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132544557060" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="681">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="681" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,824.111,1039) scale(1,1) translate(0,0)" writing-mode="lr" x="823.7" xml:space="preserve" y="1043.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132546064387" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="682">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="682" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,448,463) scale(1,1) translate(2.71949e-13,0)" writing-mode="lr" x="447.59" xml:space="preserve" y="467.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132552028163" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="683">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="683" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1221.33,1039) scale(1,1) translate(-2.62364e-13,0)" writing-mode="lr" x="1220.92" xml:space="preserve" y="1043.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132553535491" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="684">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="684" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1289,1039) scale(1,1) translate(-2.77389e-13,0)" writing-mode="lr" x="1288.59" xml:space="preserve" y="1043.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132555042819" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="685">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="685" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1425.11,1039) scale(1,1) translate(-3.07612e-13,0)" writing-mode="lr" x="1424.7" xml:space="preserve" y="1043.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132556550148" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="686">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="686" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1357.44,1039) scale(1,1) translate(-2.92587e-13,0)" writing-mode="lr" x="1357.03" xml:space="preserve" y="1043.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132558057475" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="687">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="687" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1485.44,1039) scale(1,1) translate(-3.21009e-13,0)" writing-mode="lr" x="1485.03" xml:space="preserve" y="1043.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132559564804" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="688">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="688" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1790.78,1039) scale(1,1) translate(0,0)" writing-mode="lr" x="1790.36" xml:space="preserve" y="1043.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132562907140" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="689">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="689" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1855.78,1039) scale(1,1) translate(0,0)" writing-mode="lr" x="1855.36" xml:space="preserve" y="1043.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132564938756" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="690">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="690" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,515,463) scale(1,1) translate(3.1658e-13,0)" writing-mode="lr" x="514.59" xml:space="preserve" y="467.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132740771843" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="691">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="691" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,594,462) scale(1,1) translate(3.69205e-13,0)" writing-mode="lr" x="593.59" xml:space="preserve" y="466.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132742279171" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="692">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="692" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,691,462) scale(1,1) translate(2.1691e-13,0)" writing-mode="lr" x="690.59" xml:space="preserve" y="466.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132743786500" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="693">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="693" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,769,462) scale(1,1) translate(0,0)" writing-mode="lr" x="768.59" xml:space="preserve" y="466.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132745293827" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="694">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="694" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1855,462) scale(1,1) translate(1.2092e-12,0)" writing-mode="lr" x="1854.59" xml:space="preserve" y="466.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132746801155" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="695">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="695" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1789,462) scale(1,1) translate(1.16523e-12,0)" writing-mode="lr" x="1788.59" xml:space="preserve" y="466.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132748308484" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="696">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="696" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1723,462) scale(1,1) translate(1.12127e-12,0)" writing-mode="lr" x="1722.59" xml:space="preserve" y="466.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132749815811" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="697">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="697" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1638,462) scale(1,1) translate(1.06465e-12,0)" writing-mode="lr" x="1637.59" xml:space="preserve" y="466.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132751323139" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="698">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="698" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1562,462) scale(1,1) translate(1.01402e-12,0)" writing-mode="lr" x="1561.59" xml:space="preserve" y="466.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132752830467" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="699">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="699" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,588.909,997.072) scale(1,1) translate(0,0)" writing-mode="lr" x="588.5" xml:space="preserve" y="1001.74" zvalue="1">P:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136252256258" ObjectName="P"/>
   </metadata>
  </g>
  <g id="700">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="700" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,588.909,1016.57) scale(1,1) translate(0,0)" writing-mode="lr" x="588.5" xml:space="preserve" y="1021.24" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136252321794" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="701">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="701" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,588,1036.48) scale(1,1) translate(0,0)" writing-mode="lr" x="587.59" xml:space="preserve" y="1041.15" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136252387330" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="702">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="702" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,654.708,1001.39) scale(1,1) translate(0,0)" writing-mode="lr" x="654.46" xml:space="preserve" y="1006.03" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132539969540" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="703">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="703" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,653.799,1020.48) scale(1,1) translate(0,0)" writing-mode="lr" x="653.55" xml:space="preserve" y="1025.12" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132540035076" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="704">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="704" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,738.498,1000.28) scale(1,1) translate(0,0)" writing-mode="lr" x="738.25" xml:space="preserve" y="1004.92" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132547440643" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="705">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="705" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,738.498,1020.28) scale(1,1) translate(0,0)" writing-mode="lr" x="738.25" xml:space="preserve" y="1024.92" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132547506179" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="706">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="706" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1541.13,1000.28) scale(1,1) translate(0,0)" writing-mode="lr" x="1540.88" xml:space="preserve" y="1004.92" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132561858563" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="707">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="707" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1619.13,1000.28) scale(1,1) translate(0,0)" writing-mode="lr" x="1618.88" xml:space="preserve" y="1004.92" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132560941060" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="708">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="708" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1541.13,1020.28) scale(1,1) translate(0,0)" writing-mode="lr" x="1540.88" xml:space="preserve" y="1024.92" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132561924100" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="709">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="709" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1619.13,1020.28) scale(1,1) translate(0,0)" writing-mode="lr" x="1618.88" xml:space="preserve" y="1024.92" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132561006596" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="710">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="710" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1723.11,1006.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1722.7" xml:space="preserve" y="1011.17" zvalue="1">P:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136252846082" ObjectName="P"/>
   </metadata>
  </g>
  <g id="711">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="711" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1723.11,1026) scale(1,1) translate(0,0)" writing-mode="lr" x="1722.7" xml:space="preserve" y="1030.67" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136252911618" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="712">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="712" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1722.2,1045.91) scale(1,1) translate(0,0)" writing-mode="lr" x="1721.79" xml:space="preserve" y="1050.58" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136252977154" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="440">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="440" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1016,551.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1015.53" xml:space="preserve" y="556.28" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132536627203" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="441">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="441" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1292.72,550.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1292.25" xml:space="preserve" y="555.53" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132537151492" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="472">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="472" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,688.5,282.5) scale(1,1) translate(0,0)" writing-mode="lr" x="688.03" xml:space="preserve" y="287.28" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132527583235" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="473">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="473" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1621.88,287.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1621.41" xml:space="preserve" y="292.53" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132528107523" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
</svg>