<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549593309186" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Accessory:带熔断器的线路PT1_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.166666666666664" y2="5.916666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.916666666666663" y2="7.833333333333329"/>
   <rect fill-opacity="0" height="10.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,28.67) scale(1,1) translate(0,0)" width="6" x="12" y="23.33"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.75" y2="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.916666666666666" y2="7.916666666666666"/>
   <ellipse cx="15.15" cy="13.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11.25" y2="13.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.75" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.75" y2="15.66666666666666"/>
  </symbol>
  <symbol id="Accessory:避雷器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000003" y2="8.666666666666668"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="12.6" y2="18.6"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
  </symbol>
  <symbol id="Compensator:无功补偿20210816_0" viewBox="0,0,12,13">
   <use terminal-index="0" type="0" x="6.1" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.4" x2="4" y1="3.141666666666663" y2="3.141666666666663"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.141666666666663" x2="6.141666666666663" y1="0.04999999999999893" y2="3.141666666666661"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.141666666666663" x2="6.141666666666663" y1="5.15" y2="8.09166666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.629535048371164" x2="2.363367518766924" y1="11.72804518360739" y2="7.956509060518093"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.02953748863437" x2="7.38155138676536" y1="8.002747606646338" y2="11.51674385085443"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.116482967203315" x2="3.466407395865956" y1="8.199949647924862" y2="9.792275696188446"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.4" x2="4" y1="5.141666666666663" y2="5.141666666666663"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.201323776119781" x2="8.670438561349325" y1="7.945721353591398" y2="9.806332800169811"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.915200446966938" x2="0.6490329173626979" y1="12.7581213334275" y2="8.986585210338204"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.62680850872896" x2="8.978822406859944" y1="9.206377652950431" y2="12.72037389715852"/>
  </symbol>
  <symbol id="Accessory:PT6_0" viewBox="0,0,15,20">
   <use terminal-index="0" type="0" x="7.560000000000002" xlink:href="#terminal" y="0.42361930658363"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.160000000000005" x2="8.559999999999999" y1="15.48035578286757" y2="14.24281579851547"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.060000000000002" x2="8.459999999999996" y1="16.09912577504353" y2="17.33666575939564"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.459999999999999" x2="5.060000000000007" y1="4.75500925181603" y2="5.992549236168133"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.460000000000005" x2="9.859999999999998" y1="4.75500925181603" y2="5.992549236168133"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.859999999999999" x2="8.859999999999999" y1="14.65532912663279" y2="17.13040909533699"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.460000000000003" x2="7.460000000000003" y1="2.279929283111803" y2="4.755009251816007"/>
   <ellipse cx="7.5" cy="6.51" fill-opacity="0" rx="6" ry="6.06" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.7" cy="13.44" fill-opacity="0" rx="6" ry="6.06" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV水槽河二级" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="64" id="1" preserveAspectRatio="xMidYMid slice" width="298" x="50.21" xlink:href="logo.png" y="47.57"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="101" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,199.214,79.5714) scale(1,1) translate(0,0)" writing-mode="lr" x="199.21" xml:space="preserve" y="83.06999999999999" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,201.714,77.2618) scale(1,1) translate(0,0)" writing-mode="lr" x="201.71" xml:space="preserve" y="86.26000000000001" zvalue="3">10kV水槽河二级</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="69" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" width="72.88" x="50.19" y="175.25" zvalue="56"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" writing-mode="lr" x="86.63" xml:space="preserve" y="191.75" zvalue="56">信号一览</text>
  <line fill="none" id="99" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="384.2142857142856" x2="384.2142857142856" y1="15.57142857142867" y2="1045.571428571429" zvalue="4"/>
  <line fill="none" id="97" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.214285714286234" x2="377.2142857142858" y1="151.441921185511" y2="151.441921185511" zvalue="6"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,723.377,610.801) scale(1,1) translate(-1.50741e-13,0)" writing-mode="lr" x="723.38" xml:space="preserve" y="615.3" zvalue="10">#1主变800kVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,493.34,369.841) scale(1,1) translate(0,0)" writing-mode="lr" x="493.34" xml:space="preserve" y="374.34" zvalue="12">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,808.456,944.51) scale(1,1) translate(0,3.07671e-13)" writing-mode="lr" x="808.4563443455019" xml:space="preserve" y="949.0096849061007" zvalue="14">#1发电机630kW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,761.875,66.25) scale(1,1) translate(0,0)" writing-mode="lr" x="761.88" xml:space="preserve" y="70.75" zvalue="22">10kV平原环西Ⅰ回线</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="390.25" y2="413"/>
  <line fill="none" id="84" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="494.8704926140824" y2="494.8704926140824" zvalue="41"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="1001.0816" y2="1029"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,954) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="960" zvalue="43">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54,988) scale(1,1) translate(0,0)" writing-mode="lr" x="54" xml:space="preserve" y="994" zvalue="44">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,988) scale(1,1) translate(0,0)" writing-mode="lr" x="236" xml:space="preserve" y="994" zvalue="45">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="1022" zvalue="46">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="235" xml:space="preserve" y="1022" zvalue="47">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5,568.5) scale(1,1) translate(0,0)" writing-mode="lr" x="77.5" xml:space="preserve" y="573" zvalue="49">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="307.4" xml:space="preserve" y="192.34" zvalue="50">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,237.054,956) scale(1,1) translate(0,0)" writing-mode="lr" x="237.05" xml:space="preserve" y="962" zvalue="51">SCHEJ-01-2017</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,59.5,262) scale(1,1) translate(0,0)" writing-mode="lr" x="17" xml:space="preserve" y="266.5" zvalue="53">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240,262) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="266.5" zvalue="54">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,63.2361,336.361) scale(1,1) translate(0,0)" writing-mode="lr" x="63.24" xml:space="preserve" y="340.86" zvalue="55">6.3kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,288) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="292.5" zvalue="57">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239,288) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="292.5" zvalue="58">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,381.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="385.75" zvalue="59">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,380.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="384.75" zvalue="60">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,404.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="408.75" zvalue="61">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,403.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="407.75" zvalue="62">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,311) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="315.5" zvalue="63">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,238.5,310) scale(1,1) translate(0,0)" writing-mode="lr" x="196" xml:space="preserve" y="314.5" zvalue="64">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1593.85,362.425) scale(1,1) translate(0,0)" writing-mode="lr" x="1593.85" xml:space="preserve" y="366.92" zvalue="68">0321</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,733.93,834.925) scale(1,1) translate(0,0)" writing-mode="lr" x="733.9299999999999" xml:space="preserve" y="839.42" zvalue="79">4911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,794.25,322) scale(1,1) translate(0,0)" writing-mode="lr" x="794.25" xml:space="preserve" y="326.5" zvalue="148">031</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="144" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1161.38,613.801) scale(1,1) translate(0,0)" writing-mode="lr" x="1161.38" xml:space="preserve" y="618.3" zvalue="168">#2主变630kVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="143" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1239.45,945.51) scale(1,1) translate(0,3.08004e-13)" writing-mode="lr" x="1239.447201442581" xml:space="preserve" y="950.0096849061007" zvalue="170">#2发电机500kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="162" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1262.62,528.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1262.63" xml:space="preserve" y="532.75" zvalue="185">002</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="175" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1225.51,463.175) scale(1,1) translate(0,0)" writing-mode="lr" x="1225.51" xml:space="preserve" y="467.67" zvalue="187">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,737,240) scale(1,1) translate(0,0)" writing-mode="lr" x="737" xml:space="preserve" y="244.5" zvalue="193">0316</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,736,175) scale(1,1) translate(0,0)" writing-mode="lr" x="736" xml:space="preserve" y="179.5" zvalue="196">0318</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1126.5,277) scale(1,1) translate(0,0)" writing-mode="lr" x="1126.5" xml:space="preserve" y="281.5" zvalue="201">3号站用电</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,945.274,702) scale(1,1) translate(0,0)" writing-mode="lr" x="945.27" xml:space="preserve" y="706.5" zvalue="220">0.4kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,828.25,516.298) scale(1,1) translate(0,0)" writing-mode="lr" x="828.25" xml:space="preserve" y="520.8" zvalue="225">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,782.013,466.222) scale(1,1) translate(0,0)" writing-mode="lr" x="782.01" xml:space="preserve" y="470.72" zvalue="226">0011</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1598.77,701) scale(1,1) translate(0,0)" writing-mode="lr" x="1598.77" xml:space="preserve" y="705.5" zvalue="233">0.4kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="111" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,828.75,781.298) scale(1,1) translate(0,0)" writing-mode="lr" x="828.75" xml:space="preserve" y="785.8" zvalue="237">401</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="112" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,783.513,731.222) scale(1,1) translate(0,0)" writing-mode="lr" x="783.51" xml:space="preserve" y="735.72" zvalue="238">4011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,915.667,834.222) scale(1,1) translate(0,0)" writing-mode="lr" x="915.67" xml:space="preserve" y="838.72" zvalue="253">4912</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="142" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1259.25,781.298) scale(1,1) translate(0,0)" writing-mode="lr" x="1259.25" xml:space="preserve" y="785.8" zvalue="259">402</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="145" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1213.01,731.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1213.01" xml:space="preserve" y="735.72" zvalue="260">4012</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="160" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1172.17,837.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1172.17" xml:space="preserve" y="841.72" zvalue="268">4921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="165" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1346.67,835.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1346.67" xml:space="preserve" y="839.72" zvalue="272">4922</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="167" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,578,544.5) scale(1,1) translate(0,0)" writing-mode="lr" x="578" xml:space="preserve" y="549" zvalue="275">0.4kVⅠ段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="172" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,603.5,656.5) scale(1,1) translate(0,0)" writing-mode="lr" x="603.5" xml:space="preserve" y="661" zvalue="278">419</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="178" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1437,545.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1437" xml:space="preserve" y="550" zvalue="282">0.4kVⅡ段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="176" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1462.5,657.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1462.5" xml:space="preserve" y="662" zvalue="285">429</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1573.75,293.875) scale(1,1) translate(0,0)" writing-mode="lr" x="1573.75" xml:space="preserve" y="298.38" zvalue="297">032</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1556,195.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1556" xml:space="preserve" y="200" zvalue="300">1号电容器</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="50.19" y="175.25" zvalue="56"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="PowerTransformer2Class">
  <g id="414">
   <g id="4140">
    <use class="kv10" height="60" transform="rotate(0,807.249,611.981) scale(1.37426,1.31641) translate(-212.357,-137.602)" width="40" x="779.76" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="572.49" zvalue="9"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874456760323" ObjectName="10"/>
    </metadata>
   </g>
   <g id="4141">
    <use class="v400" height="60" transform="rotate(0,807.249,611.981) scale(1.37426,1.31641) translate(-212.357,-137.602)" width="40" x="779.76" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="572.49" zvalue="9"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874456825859" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399460257795" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399460257795"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,807.249,611.981) scale(1.37426,1.31641) translate(-212.357,-137.602)" width="40" x="779.76" y="572.49"/></g>
  <g id="159">
   <g id="1590">
    <use class="kv10" height="60" transform="rotate(0,1237.25,611.981) scale(1.37426,1.31641) translate(-329.46,-137.602)" width="40" x="1209.76" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="572.49" zvalue="167"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874456891395" ObjectName="10"/>
    </metadata>
   </g>
   <g id="1591">
    <use class="v400" height="60" transform="rotate(0,1237.25,611.981) scale(1.37426,1.31641) translate(-329.46,-137.602)" width="40" x="1209.76" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="572.49" zvalue="167"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874456956931" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399460323331" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399460323331"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1237.25,611.981) scale(1.37426,1.31641) translate(-329.46,-137.602)" width="40" x="1209.76" y="572.49"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="410">
   <path class="kv10" d="M 460.26 401.54 L 1693.26 401.54" stroke-width="6" zvalue="11"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674254913540" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674254913540"/></metadata>
  <path d="M 460.26 401.54 L 1693.26 401.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="40">
   <path class="v400" d="M 514 703 L 904.77 703" stroke-width="6" zvalue="219"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674255503364" ObjectName="0.4kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674255503364"/></metadata>
  <path d="M 514 703 L 904.77 703" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="v400" d="M 1069.77 703 L 1546 703" stroke-width="6" zvalue="232"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674255568900" ObjectName="0.4kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674255568900"/></metadata>
  <path d="M 1069.77 703 L 1546 703" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="212">
   <use class="v400" height="30" transform="rotate(0,806.475,898.307) scale(1.58467,1.58467) translate(-288.783,-322.665)" width="30" x="782.7051208787044" xlink:href="#Generator:发电机_0" y="874.5367361713461" zvalue="13"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450041741317" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192450041741317"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,806.475,898.307) scale(1.58467,1.58467) translate(-288.783,-322.665)" width="30" x="782.7051208787044" y="874.5367361713461"/></g>
  <g id="158">
   <use class="v400" height="30" transform="rotate(0,1237.47,899.307) scale(1.58467,1.58467) translate(-447.799,-323.034)" width="30" x="1213.695977975783" xlink:href="#Generator:发电机_0" y="875.5367361713461" zvalue="169"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450041872389" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192450041872389"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1237.47,899.307) scale(1.58467,1.58467) translate(-447.799,-323.034)" width="30" x="1213.695977975783" y="875.5367361713461"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="156">
   <use class="kv10" height="30" transform="rotate(0,761.875,104.938) scale(2.32143,1.52083) translate(-429.058,-28.125)" width="7" x="753.75" xlink:href="#ACLineSegment:线路_0" y="82.125" zvalue="21"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450041675781" ObjectName="10kV平原环西Ⅰ回线"/>
   <cge:TPSR_Ref TObjectID="6192450041675781_5066549593309186"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,761.875,104.938) scale(2.32143,1.52083) translate(-429.058,-28.125)" width="7" x="753.75" y="82.125"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="90">
   <use class="kv10" height="30" transform="rotate(0,1552.48,357.175) scale(-1.11111,-0.814815) translate(-2948.88,-798.303)" width="15" x="1544.146758417733" xlink:href="#Disconnector:刀闸_0" y="344.9523946217128" zvalue="67"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450041610245" ObjectName="1号电容器0321隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450041610245"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1552.48,357.175) scale(-1.11111,-0.814815) translate(-2948.88,-798.303)" width="15" x="1544.146758417733" y="344.9523946217128"/></g>
  <g id="105">
   <use class="v400" height="30" transform="rotate(0,707.972,835.925) scale(-1.11111,-0.814815) translate(-1344.31,-1864.61)" width="15" x="699.6381985664668" xlink:href="#Disconnector:刀闸_0" y="823.7023946217128" zvalue="78"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450041479173" ObjectName="#1发电机4911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450041479173"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,707.972,835.925) scale(-1.11111,-0.814815) translate(-1344.31,-1864.61)" width="15" x="699.6381985664668" y="823.7023946217128"/></g>
  <g id="163">
   <use class="kv10" height="30" transform="rotate(180,1237.72,464.175) scale(1.11111,-0.814815) translate(-122.939,-1036.62)" width="15" x="1229.388198566467" xlink:href="#Disconnector:刀闸_0" y="451.9523946217127" zvalue="186"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450041937925" ObjectName="#2主变10kV侧0021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450041937925"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1237.72,464.175) scale(1.11111,-0.814815) translate(-122.939,-1036.62)" width="15" x="1229.388198566467" y="451.9523946217127"/></g>
  <g id="1">
   <use class="kv10" height="30" transform="rotate(0,761,241) scale(-1,-0.733333) translate(-1522,-573.636)" width="15" x="753.5" xlink:href="#Disconnector:刀闸_0" y="230" zvalue="192"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450042003461" ObjectName="10kV并网线0316"/>
   <cge:TPSR_Ref TObjectID="6192450042003461"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,761,241) scale(-1,-0.733333) translate(-1522,-573.636)" width="15" x="753.5" y="230"/></g>
  <g id="5">
   <use class="kv10" height="30" transform="rotate(0,760,176) scale(-1,-0.733333) translate(-1520,-420)" width="15" x="752.5" xlink:href="#Disconnector:刀闸_0" y="165" zvalue="195"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450042068997" ObjectName="10kV并网线0318"/>
   <cge:TPSR_Ref TObjectID="6192450042068997"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,760,176) scale(-1,-0.733333) translate(-1520,-420)" width="15" x="752.5" y="165"/></g>
  <g id="46">
   <use class="kv10" height="30" transform="rotate(180,806.347,467.222) scale(1.11111,-0.814815) translate(-79.8013,-1043.41)" width="15" x="798.0131985664667" xlink:href="#Disconnector:刀闸_0" y="455" zvalue="225"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450048491525" ObjectName="#1主变10kV侧0011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450048491525"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,806.347,467.222) scale(1.11111,-0.814815) translate(-79.8013,-1043.41)" width="15" x="798.0131985664667" y="455"/></g>
  <g id="88">
   <use class="v400" height="30" transform="rotate(180,807.347,732.222) scale(1.11111,-0.814815) translate(-79.9013,-1633.64)" width="15" x="799.0131985664667" xlink:href="#Disconnector:刀闸_0" y="720" zvalue="237"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450048622597" ObjectName="#1发电机4011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450048622597"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,807.347,732.222) scale(1.11111,-0.814815) translate(-79.9013,-1633.64)" width="15" x="799.0131985664667" y="720"/></g>
  <g id="113">
   <use class="v400" height="30" transform="rotate(0,891.333,835.222) scale(-1.11111,-0.814815) translate(-1692.7,-1863.05)" width="15" x="883" xlink:href="#Disconnector:刀闸_0" y="823" zvalue="252"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450048753669" ObjectName="#1发电机4912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450048753669"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,891.333,835.222) scale(-1.11111,-0.814815) translate(-1692.7,-1863.05)" width="15" x="883" y="823"/></g>
  <g id="131">
   <use class="v400" height="30" transform="rotate(180,1237.35,732.222) scale(1.11111,-0.814815) translate(-122.901,-1633.64)" width="15" x="1229.013198566467" xlink:href="#Disconnector:刀闸_0" y="720" zvalue="259"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450048819205" ObjectName="#2发电机4012隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450048819205"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1237.35,732.222) scale(1.11111,-0.814815) translate(-122.901,-1633.64)" width="15" x="1229.013198566467" y="720"/></g>
  <g id="147">
   <use class="v400" height="30" transform="rotate(0,1148.33,838.222) scale(-1.11111,-0.814815) translate(-2181,-1869.73)" width="15" x="1140" xlink:href="#Disconnector:刀闸_0" y="826" zvalue="267"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450048950277" ObjectName="#1发电机4921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450048950277"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1148.33,838.222) scale(-1.11111,-0.814815) translate(-2181,-1869.73)" width="15" x="1140" y="826"/></g>
  <g id="153">
   <use class="v400" height="30" transform="rotate(0,1322.33,836.222) scale(-1.11111,-0.814815) translate(-2511.6,-1865.27)" width="15" x="1314" xlink:href="#Disconnector:刀闸_0" y="824" zvalue="271"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450049015813" ObjectName="#2发电机4922隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450049015813"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1322.33,836.222) scale(-1.11111,-0.814815) translate(-2511.6,-1865.27)" width="15" x="1314" y="824"/></g>
  <g id="48">
   <use class="kv10" height="30" transform="rotate(0,1177,325) scale(1,1) translate(0,0)" width="15" x="1169.5" xlink:href="#Disconnector:令克_0" y="310" zvalue="301"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450049277957" ObjectName="3号站用电另克"/>
   <cge:TPSR_Ref TObjectID="6192450049277957"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1177,325) scale(1,1) translate(0,0)" width="15" x="1169.5" y="310"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="59">
   <path class="kv10" d="M 1552.38 368.99 L 1552.38 401.54" stroke-width="1" zvalue="70"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="90@0" LinkObjectIDznd="410@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1552.38 368.99 L 1552.38 401.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="v400" d="M 807.5 807.5 L 707.9 807.5 L 707.9 823.91" stroke-width="1" zvalue="82"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="91" LinkObjectIDznd="105@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 807.5 807.5 L 707.9 807.5 L 707.9 823.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="55">
   <path class="v400" d="M 707.87 847.74 L 707.87 861.41" stroke-width="1" zvalue="83"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="105@0" LinkObjectIDznd="109@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 707.87 847.74 L 707.87 861.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="121">
   <path class="kv10" d="M 760.88 340.63 L 760.88 401.54" stroke-width="1" zvalue="148"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="117@1" LinkObjectIDznd="410@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 760.88 340.63 L 760.88 401.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="164">
   <path class="kv10" d="M 1237.3 573.2 L 1237.3 545.37" stroke-width="1" zvalue="187"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="159@0" LinkObjectIDznd="161@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1237.3 573.2 L 1237.3 545.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="173">
   <path class="kv10" d="M 1238.69 513.11 L 1238.69 476.19" stroke-width="1" zvalue="189"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@0" LinkObjectIDznd="163@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1238.69 513.11 L 1238.69 476.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="174">
   <path class="kv10" d="M 1237.62 452.36 L 1237.62 401.54" stroke-width="1" zvalue="190"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="163@0" LinkObjectIDznd="410@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1237.62 452.36 L 1237.62 401.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="3">
   <path class="kv10" d="M 760.88 297.28 L 760.91 251.64" stroke-width="1" zvalue="193"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="117@0" LinkObjectIDznd="1@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 760.88 297.28 L 760.91 251.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="7">
   <path class="kv10" d="M 760.94 230.19 L 760.94 186.64" stroke-width="1" zvalue="196"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1@1" LinkObjectIDznd="5@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 760.94 230.19 L 760.94 186.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="8">
   <path class="kv10" d="M 759.94 165.19 L 759.94 127.52" stroke-width="1" zvalue="197"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="5@1" LinkObjectIDznd="156@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 759.94 165.19 L 759.94 127.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="kv10" d="M 695.03 173.63 L 695.03 143 L 759.94 143" stroke-width="1" zvalue="199"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="9@0" LinkObjectIDznd="8" MaxPinNum="2"/>
   </metadata>
  <path d="M 695.03 173.63 L 695.03 143 L 759.94 143" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="v400" d="M 807.25 650.92 L 807.25 703" stroke-width="1" zvalue="220"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="414@1" LinkObjectIDznd="40@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 807.25 650.92 L 807.25 703" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="kv10" d="M 807.3 573.2 L 807.3 533.41" stroke-width="1" zvalue="226"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="414@0" LinkObjectIDznd="45@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 807.3 573.2 L 807.3 533.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="kv10" d="M 806.31 501.15 L 806.28 479.24" stroke-width="1" zvalue="228"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@0" LinkObjectIDznd="46@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 806.31 501.15 L 806.28 479.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="kv10" d="M 806.25 455.4 L 806.25 401.54" stroke-width="1" zvalue="229"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@0" LinkObjectIDznd="410@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 806.25 455.4 L 806.25 401.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="kv10" d="M 747.03 459.63 L 747.03 436 L 806.25 436" stroke-width="1" zvalue="231"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="54@0" LinkObjectIDznd="50" MaxPinNum="2"/>
   </metadata>
  <path d="M 747.03 459.63 L 747.03 436 L 806.25 436" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="v400" d="M 1237.25 650.92 L 1237.25 703" stroke-width="1" zvalue="233"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="159@1" LinkObjectIDznd="58@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1237.25 650.92 L 1237.25 703" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="v400" d="M 807.5 798.41 L 807.5 874.93" stroke-width="1" zvalue="239"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="87@1" LinkObjectIDznd="212@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 807.5 798.41 L 807.5 874.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="v400" d="M 807.25 703 L 807.25 720.4" stroke-width="1" zvalue="240"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="40@1" LinkObjectIDznd="88@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 807.25 703 L 807.25 720.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="v400" d="M 807.28 744.24 L 807.31 766.15" stroke-width="1" zvalue="241"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="88@1" LinkObjectIDznd="87@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 807.28 744.24 L 807.31 766.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="114">
   <path class="v400" d="M 891.24 864.66 L 891.24 847.04" stroke-width="1" zvalue="253"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@0" LinkObjectIDznd="113@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 891.24 864.66 L 891.24 847.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="115">
   <path class="v400" d="M 891.27 823.21 L 891.27 807.5 L 807.5 807.5" stroke-width="1" zvalue="254"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="113@1" LinkObjectIDznd="91" MaxPinNum="2"/>
   </metadata>
  <path d="M 891.27 823.21 L 891.27 807.5 L 807.5 807.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="133">
   <path class="v400" d="M 1237.5 798.41 L 1237.47 875.93" stroke-width="1" zvalue="261"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="126@1" LinkObjectIDznd="158@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1237.5 798.41 L 1237.47 875.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="136">
   <path class="v400" d="M 1237.25 703 L 1237.25 720.4" stroke-width="1" zvalue="262"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="58@1" LinkObjectIDznd="131@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1237.25 703 L 1237.25 720.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="141">
   <path class="v400" d="M 1237.28 744.24 L 1237.31 766.15" stroke-width="1" zvalue="263"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="131@1" LinkObjectIDznd="126@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1237.28 744.24 L 1237.31 766.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="148">
   <path class="v400" d="M 1237.5 807.5 L 1148.27 807.5 L 1148.27 826.21" stroke-width="1" zvalue="268"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="133" LinkObjectIDznd="147@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1237.5 807.5 L 1148.27 807.5 L 1148.27 826.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="151">
   <path class="v400" d="M 1148.24 850.04 L 1148.24 865.41" stroke-width="1" zvalue="269"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="147@0" LinkObjectIDznd="146@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1148.24 850.04 L 1148.24 865.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="154">
   <path class="v400" d="M 1321.75 860.66 L 1321.75 848.04" stroke-width="1" zvalue="272"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157@0" LinkObjectIDznd="153@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1321.75 860.66 L 1321.75 848.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="155">
   <path class="v400" d="M 1322.27 824.21 L 1322.27 807.5 L 1237.5 807.5" stroke-width="1" zvalue="273"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="153@1" LinkObjectIDznd="148" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.27 824.21 L 1322.27 807.5 L 1237.5 807.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="170">
   <path class="v400" d="M 582.86 609.25 L 582.86 644.59" stroke-width="1" zvalue="278"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="166@0" LinkObjectIDznd="169@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 582.86 609.25 L 582.86 644.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="171">
   <path class="v400" d="M 583.6 670.39 L 583.6 703" stroke-width="1" zvalue="279"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="169@1" LinkObjectIDznd="40@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 583.6 670.39 L 583.6 703" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="182">
   <path class="v400" d="M 1441.86 610.25 L 1441.86 645.59" stroke-width="1" zvalue="284"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="184@0" LinkObjectIDznd="183@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1441.86 610.25 L 1441.86 645.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="v400" d="M 1442.6 671.39 L 1442.6 703" stroke-width="1" zvalue="286"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="183@1" LinkObjectIDznd="58@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1442.6 671.39 L 1442.6 703" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="19">
   <path class="kv10" d="M 1192.63 371.97 L 1176.92 371.97" stroke-width="1" zvalue="293"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="20@0" LinkObjectIDznd="86" MaxPinNum="2"/>
   </metadata>
  <path d="M 1192.63 371.97 L 1176.92 371.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="29">
   <path class="kv10" d="M 1552.41 345.16 L 1552.5 310.99" stroke-width="1" zvalue="297"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="90@1" LinkObjectIDznd="24@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1552.41 345.16 L 1552.5 310.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="kv10" d="M 1552.31 278.73 L 1552.25 250.42" stroke-width="1" zvalue="298"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="24@0" LinkObjectIDznd="32@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1552.31 278.73 L 1552.25 250.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="kv10" d="M 1177.12 297.68 L 1177.08 311.75" stroke-width="1" zvalue="302"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="11@0" LinkObjectIDznd="48@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1177.12 297.68 L 1177.08 311.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="86">
   <path class="kv10" d="M 1176.92 337.25 L 1176.92 401.54" stroke-width="1" zvalue="303"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="48@1" LinkObjectIDznd="410@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1176.92 337.25 L 1176.92 401.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="102">
   <use class="v400" height="40" transform="rotate(0,891.236,882) scale(1.25,-0.9375) translate(-174.497,-1824.05)" width="30" x="872.4858032768213" xlink:href="#Accessory:带熔断器的线路PT1_0" y="863.25" zvalue="73"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450041544709" ObjectName="#1发电机PT2"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,891.236,882) scale(1.25,-0.9375) translate(-174.497,-1824.05)" width="30" x="872.4858032768213" y="863.25"/></g>
  <g id="157">
   <use class="v400" height="40" transform="rotate(0,1321.75,878) scale(1.25,-0.9375) translate(-260.6,-1815.78)" width="30" x="1303" xlink:href="#Accessory:带熔断器的线路PT1_0" y="859.25" zvalue="171"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450041806853" ObjectName="#2发电机PT2"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1321.75,878) scale(1.25,-0.9375) translate(-260.6,-1815.78)" width="30" x="1303" y="859.25"/></g>
  <g id="9">
   <use class="kv10" height="26" transform="rotate(0,695,186) scale(1,1) translate(0,0)" width="12" x="689" xlink:href="#Accessory:避雷器_0" y="173" zvalue="198"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450042134533" ObjectName="10kV并网线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,695,186) scale(1,1) translate(0,0)" width="12" x="689" y="173"/></g>
  <g id="20">
   <use class="kv10" height="26" transform="rotate(270,1205,372) scale(1,1) translate(0,0)" width="12" x="1199" xlink:href="#Accessory:避雷器_0" y="359" zvalue="206"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450042265605" ObjectName="3号站用电避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1205,372) scale(1,1) translate(0,0)" width="12" x="1199" y="359"/></g>
  <g id="54">
   <use class="kv10" height="26" transform="rotate(0,747,472) scale(1,1) translate(0,0)" width="12" x="741" xlink:href="#Accessory:避雷器_0" y="459" zvalue="230"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450048557061" ObjectName="#1主变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,747,472) scale(1,1) translate(0,0)" width="12" x="741" y="459"/></g>
  <g id="109">
   <use class="v400" height="40" transform="rotate(0,707.874,878.75) scale(1.25,-0.9375) translate(-137.825,-1817.33)" width="30" x="689.1240018432882" xlink:href="#Accessory:带熔断器的线路PT1_0" y="860" zvalue="250"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450048688133" ObjectName="#1发电机PT1"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,707.874,878.75) scale(1.25,-0.9375) translate(-137.825,-1817.33)" width="30" x="689.1240018432882" y="860"/></g>
  <g id="146">
   <use class="v400" height="40" transform="rotate(0,1146.75,882.75) scale(1.25,-0.9375) translate(-225.6,-1825.6)" width="30" x="1128" xlink:href="#Accessory:带熔断器的线路PT1_0" y="864" zvalue="265"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450048884741" ObjectName="#2发电机PT1"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1146.75,882.75) scale(1.25,-0.9375) translate(-225.6,-1825.6)" width="30" x="1128" y="864"/></g>
  <g id="166">
   <use class="v400" height="20" transform="rotate(0,582.75,592.333) scale(1.76667,-1.76667) translate(-247.142,-919.95)" width="15" x="569.5" xlink:href="#Accessory:PT6_0" y="574.6666666666666" zvalue="274"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450049081349" ObjectName="0.4kVⅠ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,582.75,592.333) scale(1.76667,-1.76667) translate(-247.142,-919.95)" width="15" x="569.5" y="574.6666666666666"/></g>
  <g id="184">
   <use class="v400" height="20" transform="rotate(0,1441.75,593.333) scale(1.76667,-1.76667) translate(-619.915,-921.516)" width="15" x="1428.5" xlink:href="#Accessory:PT6_0" y="575.6666666666666" zvalue="281"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450049146885" ObjectName="0.4kVⅡ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1441.75,593.333) scale(1.76667,-1.76667) translate(-619.915,-921.516)" width="15" x="1428.5" y="575.6666666666666"/></g>
 </g>
 <g id="BreakerClass">
  <g id="117">
   <use class="kv10" height="20" transform="rotate(0,760.875,319.25) scale(2.375,2.375) translate(-433.632,-171.079)" width="10" x="749" xlink:href="#Breaker:小车断路器_0" y="295.5" zvalue="147"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924560289796" ObjectName="10kV水槽河二级线031断路器"/>
   <cge:TPSR_Ref TObjectID="6473924560289796"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,760.875,319.25) scale(2.375,2.375) translate(-433.632,-171.079)" width="10" x="749" y="295.5"/></g>
  <g id="161">
   <use class="kv10" height="20" transform="rotate(0,1238.75,529.25) scale(1.875,1.6875) translate(-573.708,-208.745)" width="10" x="1229.375" xlink:href="#Breaker:开关_0" y="512.375" zvalue="184"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924560355332" ObjectName="#2主变10kV侧002断路器"/>
   <cge:TPSR_Ref TObjectID="6473924560355332"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1238.75,529.25) scale(1.875,1.6875) translate(-573.708,-208.745)" width="10" x="1229.375" y="512.375"/></g>
  <g id="45">
   <use class="kv10" height="20" transform="rotate(0,806.375,517.298) scale(1.875,1.6875) translate(-371.933,-203.876)" width="10" x="797" xlink:href="#Breaker:开关_0" y="500.4226053782873" zvalue="224"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924561797125" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473924561797125"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,806.375,517.298) scale(1.875,1.6875) translate(-371.933,-203.876)" width="10" x="797" y="500.4226053782873"/></g>
  <g id="87">
   <use class="v400" height="20" transform="rotate(0,807.375,782.298) scale(1.875,1.6875) translate(-372.4,-311.839)" width="10" x="798" xlink:href="#Breaker:开关_0" y="765.4226053782872" zvalue="236"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924561862661" ObjectName="#1发电机401断路器"/>
   <cge:TPSR_Ref TObjectID="6473924561862661"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,807.375,782.298) scale(1.875,1.6875) translate(-372.4,-311.839)" width="10" x="798" y="765.4226053782872"/></g>
  <g id="126">
   <use class="v400" height="20" transform="rotate(0,1237.38,782.298) scale(1.875,1.6875) translate(-573.067,-311.839)" width="10" x="1228" xlink:href="#Breaker:开关_0" y="765.4226053782872" zvalue="258"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924561928197" ObjectName="#2发电机402断路器"/>
   <cge:TPSR_Ref TObjectID="6473924561928197"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1237.38,782.298) scale(1.875,1.6875) translate(-573.067,-311.839)" width="10" x="1228" y="765.4226053782872"/></g>
  <g id="169">
   <use class="v400" height="20" transform="rotate(0,583.5,657.5) scale(1.5,1.35) translate(-192,-166.963)" width="10" x="576" xlink:href="#Breaker:开关_0" y="644" zvalue="277"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924561993733" ObjectName="0.4kVⅠ段母线电压互感器419断路器"/>
   <cge:TPSR_Ref TObjectID="6473924561993733"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,583.5,657.5) scale(1.5,1.35) translate(-192,-166.963)" width="10" x="576" y="644"/></g>
  <g id="183">
   <use class="v400" height="20" transform="rotate(0,1442.5,658.5) scale(1.5,1.35) translate(-478.333,-167.222)" width="10" x="1435" xlink:href="#Breaker:开关_0" y="645" zvalue="283"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924562059269" ObjectName="0.4kVⅡ段母线电压互感器429断路器"/>
   <cge:TPSR_Ref TObjectID="6473924562059269"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1442.5,658.5) scale(1.5,1.35) translate(-478.333,-167.222)" width="10" x="1435" y="645"/></g>
  <g id="24">
   <use class="kv10" height="20" transform="rotate(0,1552.38,294.875) scale(1.875,1.6875) translate(-720.067,-113.259)" width="10" x="1543" xlink:href="#Breaker:开关_0" y="278" zvalue="296"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924562124805" ObjectName="1号电容器032断路器"/>
   <cge:TPSR_Ref TObjectID="6473924562124805"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1552.38,294.875) scale(1.875,1.6875) translate(-720.067,-113.259)" width="10" x="1543" y="278"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="11">
   <use class="kv10" height="30" transform="rotate(0,1177,278) scale(1.35714,-1.36667) translate(-304.737,-475.915)" width="28" x="1158" xlink:href="#EnergyConsumer:站用变DY接地_0" y="257.5" zvalue="200"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450042200069" ObjectName="3号站用电"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1177,278) scale(1.35714,-1.36667) translate(-304.737,-475.915)" width="28" x="1158" y="257.5"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="32">
   <use class="kv10" height="13" transform="rotate(0,1552,236) scale(2.5,-2.30769) translate(-922.2,-329.767)" width="12" x="1537" xlink:href="#Compensator:无功补偿20210816_0" y="221" zvalue="299"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450042396678" ObjectName="1号电容器"/>
   <cge:TPSR_Ref TObjectID="6192450042396678"/></metadata>
  <rect fill="white" height="13" opacity="0" stroke="white" transform="rotate(0,1552,236) scale(2.5,-2.30769) translate(-922.2,-329.767)" width="12" x="1537" y="221"/></g>
 </g>
</svg>