<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549586034690" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="12.0194189818494"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="5.416666666666668" y2="12.02321291373541"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.92126160277786" x2="6.666666666666666" y1="12.10207526123773" y2="18.50000000000001"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.18646053143751" x2="24.5" y1="12.18904818695178" y2="19.00000000000001"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 7.33333 43.5833 L 23 43.5833 L 15 31.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Accessory:带电容器的互感器_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="0" x="16.64454135671556" xlink:href="#terminal" y="1.899791906472284"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.80000022125245" x2="25.80000022125245" y1="32.55513439909467" y2="52.24553022481447"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.30000001144409" x2="16.69999987411499" y1="56.74195385044482" y2="56.74195385044482"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.30000001144409" x2="16.69999987411499" y1="27.89999991989135" y2="27.89999991989135"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.69999987411499" x2="16.69999987411499" y1="54.30000092697147" y2="58.70000109481816"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.29999978256225" x2="14.29999978256225" y1="57.60000105285648" y2="58.80000109863285"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.30000001144409" x2="20.30000001144409" y1="55.20000096130374" y2="56.40000100708012"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.29999978256225" x2="19.09999996566772" y1="58.80000109863285" y2="58.80000109863285"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.30000001144409" x2="20.30000001144409" y1="27.69999991226196" y2="30"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.09999996566772" x2="19.09999996566772" y1="57.60000105285648" y2="58.80000109863285"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.71484703625342" x2="16.71484703625342" y1="21.43967834040884" y2="13.28421951401894"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.70078590034584" x2="16.70078590034584" y1="1.971708915871165" y2="4.848389291823086"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.72969450356765" x2="16.71484703625341" y1="13.1188453726847" y2="13.1188453726847"/>
   <path d="M 16.75 4.70456 A 8.05 8.34237 -360 1 0 24.8 13.0469" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.80561785567208" x2="16.80561785567208" y1="21.39999967193602" y2="52.4169428361697"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.79999987792969" x2="30.90000041580202" y1="24.99999980926513" y2="24.99999980926513"/>
   <rect fill-opacity="0" height="14.97" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,16.8,39.79) scale(1,1) translate(0,0)" width="5" x="14.3" y="32.31"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.08651666649808" x2="18.53220968183597" y1="54.23949554462703" y2="54.23949554462703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.08651666649808" x2="18.53220968183597" y1="52.69210819007492" y2="52.69210819007492"/>
   <path d="M 20.023 29.7393 A 4.26667 4.20359 0 0 1 20.2455 38.1282" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 20.023 38.3334 A 4.26667 4.20359 0 0 1 20.2455 46.7222" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 20.023 46.7406 A 4.26667 4.20359 0 0 1 20.2455 55.1294" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.01691584314756" x2="31.01691584314756" y1="45.66453494179018" y2="45.66453494179018"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.01691584314755" x2="28.98706501944623" y1="39.11821372069851" y2="36.0903078306318"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.01691584314756" x2="31.01691584314756" y1="45.66453494179018" y2="49.29802200987024"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="29.40055315020022" x2="32.85881751650618" y1="50.74645886254542" y2="50.74645886254542"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.01691584314756" x2="31.01691584314756" y1="29.51570352810102" y2="38.98975465842889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="30.04897771888256" x2="32.4265344707179" y1="52.14088920665508" y2="52.14088920665508"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.01691584314757" x2="33.0467666668489" y1="39.11821372069851" y2="36.0903078306318"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.01691584314756" x2="31.01691584314756" y1="24.99999980926513" y2="29.51570352810103"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="28.53598705862374" x2="33.72338360808266" y1="49.35202851843577" y2="49.35202851843577"/>
   <rect fill-opacity="0" height="16.15" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,31,37.59) scale(1,1) translate(0,0)" width="6.8" x="27.6" y="29.52"/>
  </symbol>
  <symbol id="Accessory:三卷PT带容断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,6.5) scale(1,1) translate(0,0)" width="4" x="13" y="3"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV果郎河电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="269.25" x="40.71" xlink:href="logo.png" y="45.14"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,175.339,75.1429) scale(1,1) translate(0,0)" writing-mode="lr" x="175.34" xml:space="preserve" y="78.64" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,177.048,74.8332) scale(1,1) translate(6.5186e-15,0)" writing-mode="lr" x="177.05" xml:space="preserve" y="83.83" zvalue="3">35kV果郎河电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="11" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,83.5893,183.143) scale(1,1) translate(0,0)" width="72.88" x="47.15" y="171.14" zvalue="23"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.5893,183.143) scale(1,1) translate(0,0)" writing-mode="lr" x="83.59" xml:space="preserve" y="187.64" zvalue="23">信号一览</text>
  <line fill="none" id="30" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="374.7142857142856" x2="374.7142857142856" y1="13.14285714285711" y2="1043.142857142857" zvalue="4"/>
  <line fill="none" id="28" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="-0.2857142857142208" x2="367.7142857142853" y1="149.0133497569395" y2="149.0133497569395" zvalue="6"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.964285714285552" x2="190.9642857142856" y1="244.8928571428571" y2="244.8928571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.964285714285552" x2="190.9642857142856" y1="270.8928571428571" y2="270.8928571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.964285714285552" x2="9.964285714285552" y1="244.8928571428571" y2="270.8928571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.9642857142856" x2="190.9642857142856" y1="244.8928571428571" y2="270.8928571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.9642857142856" x2="371.9642857142856" y1="244.8928571428571" y2="244.8928571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.9642857142856" x2="371.9642857142856" y1="270.8928571428571" y2="270.8928571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.9642857142856" x2="190.9642857142856" y1="244.8928571428571" y2="270.8928571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371.9642857142856" x2="371.9642857142856" y1="244.8928571428571" y2="270.8928571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.964285714285552" x2="190.9642857142856" y1="270.8928571428571" y2="270.8928571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.964285714285552" x2="190.9642857142856" y1="295.1428571428571" y2="295.1428571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.964285714285552" x2="9.964285714285552" y1="270.8928571428571" y2="295.1428571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.9642857142856" x2="190.9642857142856" y1="270.8928571428571" y2="295.1428571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.9642857142856" x2="371.9642857142856" y1="270.8928571428571" y2="270.8928571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.9642857142856" x2="371.9642857142856" y1="295.1428571428571" y2="295.1428571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.9642857142856" x2="190.9642857142856" y1="270.8928571428571" y2="295.1428571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371.9642857142856" x2="371.9642857142856" y1="270.8928571428571" y2="295.1428571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.964285714285552" x2="190.9642857142856" y1="295.1428571428571" y2="295.1428571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.964285714285552" x2="190.9642857142856" y1="317.8928571428571" y2="317.8928571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.964285714285552" x2="9.964285714285552" y1="295.1428571428571" y2="317.8928571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.9642857142856" x2="190.9642857142856" y1="295.1428571428571" y2="317.8928571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.9642857142856" x2="371.9642857142856" y1="295.1428571428571" y2="295.1428571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.9642857142856" x2="371.9642857142856" y1="317.8928571428571" y2="317.8928571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.9642857142856" x2="190.9642857142856" y1="295.1428571428571" y2="317.8928571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371.9642857142856" x2="371.9642857142856" y1="295.1428571428571" y2="317.8928571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.964285714285552" x2="190.9642857142856" y1="317.8928571428571" y2="317.8928571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.964285714285552" x2="190.9642857142856" y1="340.6428571428571" y2="340.6428571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.964285714285552" x2="9.964285714285552" y1="317.8928571428571" y2="340.6428571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.9642857142856" x2="190.9642857142856" y1="317.8928571428571" y2="340.6428571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.9642857142856" x2="371.9642857142856" y1="317.8928571428571" y2="317.8928571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.9642857142856" x2="371.9642857142856" y1="340.6428571428571" y2="340.6428571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.9642857142856" x2="190.9642857142856" y1="317.8928571428571" y2="340.6428571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371.9642857142856" x2="371.9642857142856" y1="317.8928571428571" y2="340.6428571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.964285714285552" x2="190.9642857142856" y1="340.6428571428571" y2="340.6428571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.964285714285552" x2="190.9642857142856" y1="363.3928571428571" y2="363.3928571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.964285714285552" x2="9.964285714285552" y1="340.6428571428571" y2="363.3928571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.9642857142856" x2="190.9642857142856" y1="340.6428571428571" y2="363.3928571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.9642857142856" x2="371.9642857142856" y1="340.6428571428571" y2="340.6428571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.9642857142856" x2="371.9642857142856" y1="363.3928571428571" y2="363.3928571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.9642857142856" x2="190.9642857142856" y1="340.6428571428571" y2="363.3928571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371.9642857142856" x2="371.9642857142856" y1="340.6428571428571" y2="363.3928571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.964285714285552" x2="190.9642857142856" y1="363.3928571428571" y2="363.3928571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.964285714285552" x2="190.9642857142856" y1="386.1428571428571" y2="386.1428571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.964285714285552" x2="9.964285714285552" y1="363.3928571428571" y2="386.1428571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.9642857142856" x2="190.9642857142856" y1="363.3928571428571" y2="386.1428571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.9642857142856" x2="371.9642857142856" y1="363.3928571428571" y2="363.3928571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.9642857142856" x2="371.9642857142856" y1="386.1428571428571" y2="386.1428571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.9642857142856" x2="190.9642857142856" y1="363.3928571428571" y2="386.1428571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371.9642857142856" x2="371.9642857142856" y1="363.3928571428571" y2="386.1428571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.964285714285552" x2="190.9642857142856" y1="386.1428571428571" y2="386.1428571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.964285714285552" x2="190.9642857142856" y1="408.8928571428571" y2="408.8928571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.964285714285552" x2="9.964285714285552" y1="386.1428571428571" y2="408.8928571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.9642857142856" x2="190.9642857142856" y1="386.1428571428571" y2="408.8928571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.9642857142856" x2="371.9642857142856" y1="386.1428571428571" y2="386.1428571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.9642857142856" x2="371.9642857142856" y1="408.8928571428571" y2="408.8928571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.9642857142856" x2="190.9642857142856" y1="386.1428571428571" y2="408.8928571428571"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371.9642857142856" x2="371.9642857142856" y1="386.1428571428571" y2="408.8928571428571"/>
  <line fill="none" id="26" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.964285714286007" x2="375.9642857142856" y1="490.7633497569395" y2="490.7633497569395" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8.964285714285552" x2="98.96428571428555" y1="929.8928571428571" y2="929.8928571428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8.964285714285552" x2="98.96428571428555" y1="969.056157142857" y2="969.056157142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8.964285714285552" x2="8.964285714285552" y1="929.8928571428571" y2="969.056157142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98.96428571428555" x2="98.96428571428555" y1="929.8928571428571" y2="969.056157142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98.96428571428555" x2="368.9642857142856" y1="929.8928571428571" y2="929.8928571428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98.96428571428555" x2="368.9642857142856" y1="969.056157142857" y2="969.056157142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98.96428571428555" x2="98.96428571428555" y1="929.8928571428571" y2="969.056157142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368.9642857142856" x2="368.9642857142856" y1="929.8928571428571" y2="969.056157142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8.964285714285552" x2="98.96428571428555" y1="969.0561271428571" y2="969.0561271428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8.964285714285552" x2="98.96428571428555" y1="996.9745271428571" y2="996.9745271428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8.964285714285552" x2="8.964285714285552" y1="969.0561271428571" y2="996.9745271428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98.96428571428555" x2="98.96428571428555" y1="969.0561271428571" y2="996.9745271428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98.96428571428555" x2="188.9642857142856" y1="969.0561271428571" y2="969.0561271428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98.96428571428555" x2="188.9642857142856" y1="996.9745271428571" y2="996.9745271428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98.96428571428555" x2="98.96428571428555" y1="969.0561271428571" y2="996.9745271428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.9642857142856" x2="188.9642857142856" y1="969.0561271428571" y2="996.9745271428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.9642857142857" x2="278.9642857142857" y1="969.0561271428571" y2="969.0561271428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.9642857142857" x2="278.9642857142857" y1="996.9745271428571" y2="996.9745271428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.9642857142857" x2="188.9642857142857" y1="969.0561271428571" y2="996.9745271428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.9642857142857" x2="278.9642857142857" y1="969.0561271428571" y2="996.9745271428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.9642857142856" x2="368.9642857142856" y1="969.0561271428571" y2="969.0561271428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.9642857142856" x2="368.9642857142856" y1="996.9745271428571" y2="996.9745271428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.9642857142856" x2="278.9642857142856" y1="969.0561271428571" y2="996.9745271428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368.9642857142856" x2="368.9642857142856" y1="969.0561271428571" y2="996.9745271428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8.964285714285552" x2="98.96428571428555" y1="996.9744571428571" y2="996.9744571428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8.964285714285552" x2="98.96428571428555" y1="1024.892857142857" y2="1024.892857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8.964285714285552" x2="8.964285714285552" y1="996.9744571428571" y2="1024.892857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98.96428571428555" x2="98.96428571428555" y1="996.9744571428571" y2="1024.892857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98.96428571428555" x2="188.9642857142856" y1="996.9744571428571" y2="996.9744571428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98.96428571428555" x2="188.9642857142856" y1="1024.892857142857" y2="1024.892857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98.96428571428555" x2="98.96428571428555" y1="996.9744571428571" y2="1024.892857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.9642857142856" x2="188.9642857142856" y1="996.9744571428571" y2="1024.892857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.9642857142857" x2="278.9642857142857" y1="996.9744571428571" y2="996.9744571428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.9642857142857" x2="278.9642857142857" y1="1024.892857142857" y2="1024.892857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.9642857142857" x2="188.9642857142857" y1="996.9744571428571" y2="1024.892857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.9642857142857" x2="278.9642857142857" y1="996.9744571428571" y2="1024.892857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.9642857142856" x2="368.9642857142856" y1="996.9744571428571" y2="996.9744571428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.9642857142856" x2="368.9642857142856" y1="1024.892857142857" y2="1024.892857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.9642857142856" x2="278.9642857142856" y1="996.9744571428571" y2="1024.892857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368.9642857142856" x2="368.9642857142856" y1="996.9744571428571" y2="1024.892857142857"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53.9643,949.893) scale(1,1) translate(0,0)" writing-mode="lr" x="53.96" xml:space="preserve" y="955.89" zvalue="10">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,50.9643,983.893) scale(1,1) translate(0,0)" writing-mode="lr" x="50.96" xml:space="preserve" y="989.89" zvalue="11">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232.964,983.893) scale(1,1) translate(0,0)" writing-mode="lr" x="232.96" xml:space="preserve" y="989.89" zvalue="12">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,45.9643,1011.89) scale(1,1) translate(0,0)" writing-mode="lr" x="45.96" xml:space="preserve" y="1017.89" zvalue="13">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,231.964,1011.89) scale(1,1) translate(0,0)" writing-mode="lr" x="231.96" xml:space="preserve" y="1017.89" zvalue="14">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,74.4643,564.393) scale(1,1) translate(0,0)" writing-mode="lr" x="74.46428571428555" xml:space="preserve" y="568.8928571428571" zvalue="16">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,199.363,183.734) scale(1,1) translate(0,0)" writing-mode="lr" x="199.36" xml:space="preserve" y="188.23" zvalue="17">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,304.363,183.734) scale(1,1) translate(0,0)" writing-mode="lr" x="304.36" xml:space="preserve" y="188.23" zvalue="18">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,56.4643,257.893) scale(1,1) translate(0,0)" writing-mode="lr" x="13.96" xml:space="preserve" y="262.39" zvalue="19">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,236.964,257.893) scale(1,1) translate(0,0)" writing-mode="lr" x="194.46" xml:space="preserve" y="262.39" zvalue="20">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6518,331.143) scale(1,1) translate(0,0)" writing-mode="lr" x="59.65" xml:space="preserve" y="335.64" zvalue="21">35kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57.9018,353.893) scale(1,1) translate(0,0)" writing-mode="lr" x="57.9" xml:space="preserve" y="358.39" zvalue="22">6.3kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,55.4643,283.893) scale(1,1) translate(0,0)" writing-mode="lr" x="12.96" xml:space="preserve" y="288.39" zvalue="24">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,235.964,283.893) scale(1,1) translate(0,0)" writing-mode="lr" x="193.46" xml:space="preserve" y="288.39" zvalue="25">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.6518,377.143) scale(1,1) translate(0,0)" writing-mode="lr" x="56.65" xml:space="preserve" y="381.64" zvalue="26">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,224.652,376.143) scale(1,1) translate(0,0)" writing-mode="lr" x="224.65" xml:space="preserve" y="380.64" zvalue="27">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.6518,400.143) scale(1,1) translate(0,-2.6022e-13)" writing-mode="lr" x="56.65" xml:space="preserve" y="404.64" zvalue="28">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,224.652,399.143) scale(1,1) translate(0,0)" writing-mode="lr" x="224.65" xml:space="preserve" y="403.64" zvalue="29">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,55.4643,306.893) scale(1,1) translate(0,0)" writing-mode="lr" x="12.96" xml:space="preserve" y="311.39" zvalue="30">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,235.464,305.893) scale(1,1) translate(0,0)" writing-mode="lr" x="192.96" xml:space="preserve" y="310.39" zvalue="31">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,239.152,328.893) scale(1,1) translate(0,0)" writing-mode="lr" x="239.15" xml:space="preserve" y="333.39" zvalue="32">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,221.339,949.893) scale(1,1) translate(0,0)" writing-mode="lr" x="221.34" xml:space="preserve" y="955.89" zvalue="33">GuoLangHe-01-2014</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" x="597.5234375" xml:space="preserve" y="504.7678579602922" zvalue="35">#1主变              </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="597.5234375" xml:space="preserve" y="520.7678579602922" zvalue="35">3150KVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" x="732.8046875" xml:space="preserve" y="501.6428579432624" zvalue="37">#2主变             </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="732.8046875" xml:space="preserve" y="517.6428579432624" zvalue="37">3150KVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1003.98,278.587) scale(1,1) translate(-2.16601e-13,3.88371e-13)" writing-mode="lr" x="1003.98" xml:space="preserve" y="283.09" zvalue="38">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,500.889,708.587) scale(1,1) translate(0,0)" writing-mode="lr" x="500.89" xml:space="preserve" y="713.09" zvalue="39">6.3kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1829.06,718.587) scale(1,1) translate(0,0)" writing-mode="lr" x="1829.06" xml:space="preserve" y="723.09" zvalue="40">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" x="1244.6484375" xml:space="preserve" y="504.1428577559335" zvalue="42">#3主变         </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1244.6484375" xml:space="preserve" y="520.1428577559335" zvalue="42">1600KVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" x="1380" xml:space="preserve" y="506.6428579602922" zvalue="44">#4主变                 </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1380" xml:space="preserve" y="522.6428579602923" zvalue="44">1600KVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,624.429,442.071) scale(1,1) translate(1.1513e-12,-2.87675e-13)" writing-mode="lr" x="624.4299999999999" xml:space="preserve" y="446.57" zvalue="45">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,758.5,444.929) scale(1,1) translate(0,-9.6526e-14)" writing-mode="lr" x="758.5" xml:space="preserve" y="449.43" zvalue="47">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,747.071,654.571) scale(1,1) translate(0,4.2899e-13)" writing-mode="lr" x="747.0700000000001" xml:space="preserve" y="659.0700000000001" zvalue="49">601</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,770.786,228.857) scale(1,1) translate(6.71336e-13,-4.8152e-14)" writing-mode="lr" x="770.79" xml:space="preserve" y="233.36" zvalue="51">361</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="154" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1036.29,595.143) scale(1,1) translate(0,0)" writing-mode="lr" x="1036.29" xml:space="preserve" y="599.64" zvalue="53">602</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="155" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1143.71,351.714) scale(1,1) translate(-2.50657e-13,4.16619e-13)" writing-mode="lr" x="1143.71" xml:space="preserve" y="356.21" zvalue="55">671</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="156" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1405.93,665.143) scale(1,1) translate(2.31553e-12,2.90434e-13)" writing-mode="lr" x="1405.93" xml:space="preserve" y="669.64" zvalue="57">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,702.929,843.143) scale(1,1) translate(8.44578e-13,1.85185e-13)" writing-mode="lr" x="702.9299999999999" xml:space="preserve" y="847.64" zvalue="59">661</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,638,342.714) scale(1,1) translate(0,0)" writing-mode="lr" x="638" xml:space="preserve" y="347.21" zvalue="64">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,765.857,342.714) scale(1,1) translate(0,0)" writing-mode="lr" x="765.86" xml:space="preserve" y="347.21" zvalue="68">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,739.214,32.5714) scale(1,1) translate(0,0)" writing-mode="lr" x="739.21" xml:space="preserve" y="37.07" zvalue="74">35kV果弄线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,722.089,167) scale(1,1) translate(0,0)" writing-mode="lr" x="722.09" xml:space="preserve" y="171.5" zvalue="75">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,821.679,120.786) scale(1,1) translate(-3.58031e-13,2.52798e-13)" writing-mode="lr" x="821.6799999999999" xml:space="preserve" y="125.29" zvalue="78">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,667.429,80.6429) scale(1,1) translate(2.12221e-12,7.66054e-15)" writing-mode="lr" x="667.4299999999999" xml:space="preserve" y="85.14" zvalue="83">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,701.571,707) scale(1,1) translate(0,0)" writing-mode="lr" x="701.5700000000001" xml:space="preserve" y="711.5" zvalue="90">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="101" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,660.143,789.857) scale(1,1) translate(0,0)" writing-mode="lr" x="660.14" xml:space="preserve" y="794.36" zvalue="95">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="98" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,671.232,982.768) scale(1,1) translate(6.8136e-14,0)" writing-mode="lr" x="671.2321428571428" xml:space="preserve" y="987.2678559167044" zvalue="98">#1发电机         2160KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="104" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,824.357,844.571) scale(1,1) translate(-5.41567e-13,1.85502e-13)" writing-mode="lr" x="824.36" xml:space="preserve" y="849.0700000000001" zvalue="101">662</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="103" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,781.571,791.286) scale(1,1) translate(0,0)" writing-mode="lr" x="781.5700000000001" xml:space="preserve" y="795.79" zvalue="104">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="102" stroke="rgb(255,255,255)" text-anchor="middle" x="800" xml:space="preserve" y="980.9821427209038" zvalue="107">#2发电机                   </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="102" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="800" xml:space="preserve" y="996.9821427209038" zvalue="107">2160KW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="112" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,994.429,985.857) scale(1,1) translate(0,0)" writing-mode="lr" x="994.4299999999999" xml:space="preserve" y="990.36" zvalue="110">#1厂用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="117" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,961.571,808.429) scale(1,1) translate(0,0)" writing-mode="lr" x="961.5700000000001" xml:space="preserve" y="812.9299999999999" zvalue="113">6811</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="128" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,565.028,802.214) scale(1,1) translate(0,0)" writing-mode="lr" x="565.03" xml:space="preserve" y="806.71" zvalue="121">6901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="136" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1259.26,802.214) scale(1,1) translate(0,0)" writing-mode="lr" x="1259.26" xml:space="preserve" y="806.71" zvalue="138">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="148" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1205.36,987.286) scale(1,1) translate(0,0)" writing-mode="lr" x="1205.36" xml:space="preserve" y="991.79" zvalue="147">#2厂用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="147" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1172.5,799.929) scale(1,1) translate(6.39964e-13,0)" writing-mode="lr" x="1172.5" xml:space="preserve" y="804.4299999999999" zvalue="150">0811</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="174" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1376.5,866) scale(1,1) translate(3.18279e-12,-1.51193e-12)" writing-mode="lr" x="1376.5" xml:space="preserve" y="870.5" zvalue="169">071</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="173" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1350.32,1000.43) scale(1,1) translate(0,0)" writing-mode="lr" x="1350.32" xml:space="preserve" y="1004.93" zvalue="171">备用一</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="172" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1332.29,798.429) scale(1,1) translate(0,0)" writing-mode="lr" x="1332.29" xml:space="preserve" y="802.9299999999999" zvalue="174">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="171" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1331.57,912.714) scale(1,1) translate(0,0)" writing-mode="lr" x="1331.57" xml:space="preserve" y="917.21" zvalue="177">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="186" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1459.36,866) scale(1,1) translate(3.37597e-12,-1.51193e-12)" writing-mode="lr" x="1459.36" xml:space="preserve" y="870.5" zvalue="182">072</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="185" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1433.18,1000.43) scale(1,1) translate(0,0)" writing-mode="lr" x="1433.18" xml:space="preserve" y="1004.93" zvalue="184">备用一线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="184" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1415.14,798.429) scale(1,1) translate(0,0)" writing-mode="lr" x="1415.14" xml:space="preserve" y="802.9299999999999" zvalue="187">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="183" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1414.43,912.714) scale(1,1) translate(0,0)" writing-mode="lr" x="1414.43" xml:space="preserve" y="917.21" zvalue="190">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="198" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1557.93,870.286) scale(1,1) translate(-4.12089e-12,-1.51955e-12)" writing-mode="lr" x="1557.93" xml:space="preserve" y="874.79" zvalue="195">073</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="197" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1531.75,1004.71) scale(1,1) translate(0,0)" writing-mode="lr" x="1531.75" xml:space="preserve" y="1009.21" zvalue="197">生活区线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="196" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1513.71,802.714) scale(1,1) translate(0,0)" writing-mode="lr" x="1513.71" xml:space="preserve" y="807.21" zvalue="200">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="195" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1513,917) scale(1,1) translate(0,0)" writing-mode="lr" x="1513" xml:space="preserve" y="921.5" zvalue="203">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="210" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1683.64,867.429) scale(1,1) translate(0,-1.51447e-12)" writing-mode="lr" x="1683.64" xml:space="preserve" y="871.9299999999999" zvalue="208">074</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="209" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1657.47,1001.86) scale(1,1) translate(0,0)" writing-mode="lr" x="1657.47" xml:space="preserve" y="1006.36" zvalue="210">尾水联络线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="208" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1639.43,799.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1639.43" xml:space="preserve" y="804.36" zvalue="213">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="207" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1638.71,914.143) scale(1,1) translate(0,0)" writing-mode="lr" x="1638.71" xml:space="preserve" y="918.64" zvalue="216">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="222" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1766.5,866) scale(1,1) translate(0,-1.51193e-12)" writing-mode="lr" x="1766.5" xml:space="preserve" y="870.5" zvalue="221">075</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="221" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1740.32,1000.43) scale(1,1) translate(0,0)" writing-mode="lr" x="1740.32" xml:space="preserve" y="1004.93" zvalue="223">盾中联络线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="220" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1722.29,798.429) scale(1,1) translate(0,0)" writing-mode="lr" x="1722.29" xml:space="preserve" y="802.9299999999999" zvalue="226">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="219" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1721.57,912.714) scale(1,1) translate(0,0)" writing-mode="lr" x="1721.57" xml:space="preserve" y="917.21" zvalue="229">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="249" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,993,678.429) scale(1,1) translate(0,0)" writing-mode="lr" x="993" xml:space="preserve" y="682.9299999999999" zvalue="234">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="250" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,992.286,514.143) scale(1,1) translate(0,-1.12386e-13)" writing-mode="lr" x="992.29" xml:space="preserve" y="518.64" zvalue="238">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="251" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1265.86,424.143) scale(1,1) translate(0,0)" writing-mode="lr" x="1265.86" xml:space="preserve" y="428.64" zvalue="242">6036</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="252" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1395.1,425.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1395.1" xml:space="preserve" y="430.07" zvalue="246">6046</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="277" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1356.7,609.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1356.7" xml:space="preserve" y="614.36" zvalue="250">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="278" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1359.02,709.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1359.02" xml:space="preserve" y="714.36" zvalue="254">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="264" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1395.02,562.714) scale(1,1) translate(0,0)" writing-mode="lr" x="1395.02" xml:space="preserve" y="567.21" zvalue="262">0015</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="263" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1267.29,562.714) scale(1,1) translate(0,0)" writing-mode="lr" x="1267.29" xml:space="preserve" y="567.21" zvalue="267">0014</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="284" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,473.107,789.929) scale(1,1) translate(8.12937e-13,-8.63278e-13)" writing-mode="lr" x="473.11" xml:space="preserve" y="794.4299999999999" zvalue="286">6631</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,145.964,1011.89) scale(1,1) translate(0,0)" writing-mode="lr" x="145.96" xml:space="preserve" y="1017.89" zvalue="307">杨立超</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="47.15" y="171.14" zvalue="23"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="PowerTransformer2Class">
  <g id="33">
   <g id="330">
    <use class="kv35" height="50" transform="rotate(0,654.286,504.286) scale(1.42857,1.42857) translate(-189.857,-140.571)" width="30" x="632.86" xlink:href="#PowerTransformer2:Y-D_0" y="468.57" zvalue="34"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874434674692" ObjectName="35"/>
    </metadata>
   </g>
   <g id="331">
    <use class="v6300" height="50" transform="rotate(0,654.286,504.286) scale(1.42857,1.42857) translate(-189.857,-140.571)" width="30" x="632.86" xlink:href="#PowerTransformer2:Y-D_1" y="468.57" zvalue="34"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874434740228" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399449575428" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399449575428"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,654.286,504.286) scale(1.42857,1.42857) translate(-189.857,-140.571)" width="30" x="632.86" y="468.57"/></g>
  <g id="34">
   <g id="340">
    <use class="kv35" height="50" transform="rotate(0,782.857,504.286) scale(1.42857,1.42857) translate(-228.429,-140.571)" width="30" x="761.4299999999999" xlink:href="#PowerTransformer2:Y-D_0" y="468.57" zvalue="36"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874434805764" ObjectName="35"/>
    </metadata>
   </g>
   <g id="341">
    <use class="v6300" height="50" transform="rotate(0,782.857,504.286) scale(1.42857,1.42857) translate(-228.429,-140.571)" width="30" x="761.4299999999999" xlink:href="#PowerTransformer2:Y-D_1" y="468.57" zvalue="36"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874434871300" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399449640964" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399449640964"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,782.857,504.286) scale(1.42857,1.42857) translate(-228.429,-140.571)" width="30" x="761.4299999999999" y="468.57"/></g>
  <g id="38">
   <g id="380">
    <use class="v6300" height="50" transform="rotate(0,1298.57,504.286) scale(1.42857,1.42857) translate(-383.143,-140.571)" width="30" x="1277.14" xlink:href="#PowerTransformer2:Y-D_0" y="468.57" zvalue="41"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874435002372" ObjectName="6.3"/>
    </metadata>
   </g>
   <g id="381">
    <use class="kv10" height="50" transform="rotate(0,1298.57,504.286) scale(1.42857,1.42857) translate(-383.143,-140.571)" width="30" x="1277.14" xlink:href="#PowerTransformer2:Y-D_1" y="468.57" zvalue="41"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874434936836" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399449706500" ObjectName="#3主变"/>
   <cge:TPSR_Ref TObjectID="6755399449706500"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1298.57,504.286) scale(1.42857,1.42857) translate(-383.143,-140.571)" width="30" x="1277.14" y="468.57"/></g>
  <g id="39">
   <g id="390">
    <use class="v6300" height="50" transform="rotate(0,1427.14,504.286) scale(1.42857,1.42857) translate(-421.714,-140.571)" width="30" x="1405.71" xlink:href="#PowerTransformer2:Y-D_0" y="468.57" zvalue="43"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874435133444" ObjectName="6.3"/>
    </metadata>
   </g>
   <g id="391">
    <use class="kv10" height="50" transform="rotate(0,1427.14,504.286) scale(1.42857,1.42857) translate(-421.714,-140.571)" width="30" x="1405.71" xlink:href="#PowerTransformer2:Y-D_1" y="468.57" zvalue="43"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874435067908" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399449772036" ObjectName="#4主变"/>
   <cge:TPSR_Ref TObjectID="6755399449772036"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1427.14,504.286) scale(1.42857,1.42857) translate(-421.714,-140.571)" width="30" x="1405.71" y="468.57"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="35">
   <path class="kv35" d="M 507.14 305.14 L 991.43 305.14" stroke-width="6" zvalue="37"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674242265093" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674242265093"/></metadata>
  <path d="M 507.14 305.14 L 991.43 305.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="36">
   <path class="v6300" d="M 491.43 745.14 L 1070 745.14" stroke-width="6" zvalue="38"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674242330629" ObjectName="6.3kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674242330629"/></metadata>
  <path d="M 491.43 745.14 L 1070 745.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="37">
   <path class="kv10" d="M 1161.43 745.14 L 1824.29 745.14" stroke-width="6" zvalue="39"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674242396164" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674242396164"/></metadata>
  <path d="M 1161.43 745.14 L 1824.29 745.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="40">
   <use class="kv35" height="20" transform="rotate(0,654.286,405.143) scale(2.14286,1.92857) translate(-343.238,-185.783)" width="10" x="643.5714199202401" xlink:href="#Breaker:开关_0" y="385.8571472167971" zvalue="44"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924508188677" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473924508188677"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,654.286,405.143) scale(2.14286,1.92857) translate(-343.238,-185.783)" width="10" x="643.5714199202401" y="385.8571472167971"/></g>
  <g id="41">
   <use class="kv35" height="20" transform="rotate(0,782.857,405.143) scale(2.14286,1.92857) translate(-411.81,-185.783)" width="10" x="772.1428615025111" xlink:href="#Breaker:开关_0" y="385.857147191252" zvalue="46"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924508254213" ObjectName="#2主变35kV侧302断路器"/>
   <cge:TPSR_Ref TObjectID="6473924508254213"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,782.857,405.143) scale(2.14286,1.92857) translate(-411.81,-185.783)" width="10" x="772.1428615025111" y="385.857147191252"/></g>
  <g id="42">
   <use class="v6300" height="20" transform="rotate(0,716.143,653) scale(2.14286,1.92857) translate(-376.229,-305.122)" width="10" x="705.4285714285716" xlink:href="#Breaker:开关_0" y="633.7142857142858" zvalue="48"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924508319749" ObjectName="601断路器"/>
   <cge:TPSR_Ref TObjectID="6473924508319749"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,716.143,653) scale(2.14286,1.92857) translate(-376.229,-305.122)" width="10" x="705.4285714285716" y="633.7142857142858"/></g>
  <g id="43">
   <use class="kv35" height="20" transform="rotate(0,739.286,225.857) scale(2.14286,1.92857) translate(-388.571,-99.4603)" width="10" x="728.5714285714287" xlink:href="#Breaker:开关_0" y="206.5714285714286" zvalue="50"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924508385285" ObjectName="35kV果弄线361断路器"/>
   <cge:TPSR_Ref TObjectID="6473924508385285"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,739.286,225.857) scale(2.14286,1.92857) translate(-388.571,-99.4603)" width="10" x="728.5714285714287" y="206.5714285714286"/></g>
  <g id="44">
   <use class="v6300" height="20" transform="rotate(0,1009.29,596.143) scale(2.14286,1.92857) translate(-532.571,-277.746)" width="10" x="998.5714285714286" xlink:href="#Breaker:开关_0" y="576.8571428571429" zvalue="52"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924508450821" ObjectName="602断路器"/>
   <cge:TPSR_Ref TObjectID="6473924508450821"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1009.29,596.143) scale(2.14286,1.92857) translate(-532.571,-277.746)" width="10" x="998.5714285714286" y="576.8571428571429"/></g>
  <g id="45">
   <use class="v6300" height="20" transform="rotate(90,1142.14,325.857) scale(2.14286,1.92857) translate(-603.429,-147.608)" width="10" x="1131.428571428571" xlink:href="#Breaker:开关_0" y="306.5714285714286" zvalue="54"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924508516357" ObjectName="671断路器"/>
   <cge:TPSR_Ref TObjectID="6473924508516357"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1142.14,325.857) scale(2.14286,1.92857) translate(-603.429,-147.608)" width="10" x="1131.428571428571" y="306.5714285714286"/></g>
  <g id="46">
   <use class="kv10" height="20" transform="rotate(0,1373.86,663) scale(2.14286,1.92857) translate(-727.01,-309.937)" width="10" x="1363.142857142857" xlink:href="#Breaker:开关_0" y="643.7142857142858" zvalue="56"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924508581893" ObjectName="001断路器"/>
   <cge:TPSR_Ref TObjectID="6473924508581893"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1373.86,663) scale(2.14286,1.92857) translate(-727.01,-309.937)" width="10" x="1363.142857142857" y="643.7142857142858"/></g>
  <g id="47">
   <use class="v6300" height="20" transform="rotate(0,675,843) scale(2.14286,1.92857) translate(-354.286,-396.603)" width="10" x="664.2857142857143" xlink:href="#Breaker:开关_0" y="823.7142857142856" zvalue="58"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924508647429" ObjectName="#1发电机661断路器"/>
   <cge:TPSR_Ref TObjectID="6473924508647429"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,675,843) scale(2.14286,1.92857) translate(-354.286,-396.603)" width="10" x="664.2857142857143" y="823.7142857142856"/></g>
  <g id="110">
   <use class="v6300" height="20" transform="rotate(0,796.429,844.429) scale(2.14286,1.92857) translate(-419.048,-397.291)" width="10" x="785.7142857142858" xlink:href="#Breaker:开关_0" y="825.142859322684" zvalue="100"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924508712965" ObjectName="#2发电机662断路器"/>
   <cge:TPSR_Ref TObjectID="6473924508712965"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,796.429,844.429) scale(2.14286,1.92857) translate(-419.048,-397.291)" width="10" x="785.7142857142858" y="825.142859322684"/></g>
  <g id="182">
   <use class="kv10" height="20" transform="rotate(0,1348.57,860.143) scale(2.14286,1.92857) translate(-713.524,-404.857)" width="10" x="1337.857142720904" xlink:href="#Breaker:开关_0" y="840.8571450710297" zvalue="168"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924508778501" ObjectName="备用一071断路器"/>
   <cge:TPSR_Ref TObjectID="6473924508778501"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1348.57,860.143) scale(2.14286,1.92857) translate(-713.524,-404.857)" width="10" x="1337.857142720904" y="840.8571450710297"/></g>
  <g id="194">
   <use class="kv10" height="20" transform="rotate(0,1431.43,860.143) scale(2.14286,1.92857) translate(-757.714,-404.857)" width="10" x="1420.714285578046" xlink:href="#Breaker:开关_0" y="840.8571450710295" zvalue="181"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924508844037" ObjectName="备用一072断路器"/>
   <cge:TPSR_Ref TObjectID="6473924508844037"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1431.43,860.143) scale(2.14286,1.92857) translate(-757.714,-404.857)" width="10" x="1420.714285578046" y="840.8571450710295"/></g>
  <g id="206">
   <use class="kv10" height="20" transform="rotate(0,1530,864.429) scale(2.14286,1.92857) translate(-810.286,-406.921)" width="10" x="1519.285714149475" xlink:href="#Breaker:开关_0" y="845.1428593567439" zvalue="194"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924508909573" ObjectName="生活区线073断路器"/>
   <cge:TPSR_Ref TObjectID="6473924508909573"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1530,864.429) scale(2.14286,1.92857) translate(-810.286,-406.921)" width="10" x="1519.285714149475" y="845.1428593567439"/></g>
  <g id="218">
   <use class="kv10" height="20" transform="rotate(0,1655.71,861.571) scale(2.14286,1.92857) translate(-877.333,-405.545)" width="10" x="1644.999999863761" xlink:href="#Breaker:开关_0" y="842.285716499601" zvalue="207"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924508975109" ObjectName="尾水联络线074断路器"/>
   <cge:TPSR_Ref TObjectID="6473924508975109"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1655.71,861.571) scale(2.14286,1.92857) translate(-877.333,-405.545)" width="10" x="1644.999999863761" y="842.285716499601"/></g>
  <g id="230">
   <use class="kv10" height="20" transform="rotate(0,1738.57,860.143) scale(2.14286,1.92857) translate(-921.524,-404.857)" width="10" x="1727.857142720904" xlink:href="#Breaker:开关_0" y="840.8571450710297" zvalue="220"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924509040645" ObjectName="盾中联络线075断路器"/>
   <cge:TPSR_Ref TObjectID="6473924509040645"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1738.57,860.143) scale(2.14286,1.92857) translate(-921.524,-404.857)" width="10" x="1727.857142720904" y="840.8571450710297"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="52">
   <path class="kv35" d="M 654.43 423.56 L 654.33 469.22" stroke-width="1" zvalue="59"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="40@1" LinkObjectIDznd="33@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 654.43 423.56 L 654.33 469.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="53">
   <path class="kv35" d="M 783 423.56 L 782.9 469.22" stroke-width="1" zvalue="60"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41@1" LinkObjectIDznd="34@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 783 423.56 L 782.9 469.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="kv35" d="M 652.98 305.14 L 652.98 328.52" stroke-width="1" zvalue="64"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="35@0" LinkObjectIDznd="58@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 652.98 305.14 L 652.98 328.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="kv35" d="M 652.94 359.16 L 652.94 386.69" stroke-width="1" zvalue="65"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="58@1" LinkObjectIDznd="40@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 652.94 359.16 L 652.94 386.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="kv35" d="M 780.84 305.14 L 780.84 328.52" stroke-width="1" zvalue="68"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="35@1" LinkObjectIDznd="62@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 780.84 305.14 L 780.84 328.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="kv35" d="M 780.8 359.16 L 780.8 386.69" stroke-width="1" zvalue="69"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="62@1" LinkObjectIDznd="41@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 780.8 359.16 L 780.8 386.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv35" d="M 739.43 244.28 L 739.43 305.14" stroke-width="1" zvalue="70"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="43@1" LinkObjectIDznd="35@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 739.43 244.28 L 739.43 305.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv35" d="M 739.21 207.41 L 739.18 183.45" stroke-width="1" zvalue="75"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="43@0" LinkObjectIDznd="72@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 739.21 207.41 L 739.18 183.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="kv35" d="M 739.21 152.81 L 739.21 71.46" stroke-width="1" zvalue="76"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="72@0" LinkObjectIDznd="70@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 739.21 152.81 L 739.21 71.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv35" d="M 818.93 139.36 L 739.21 139.36" stroke-width="1" zvalue="78"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="76@0" LinkObjectIDznd="74" MaxPinNum="2"/>
   </metadata>
  <path d="M 818.93 139.36 L 739.21 139.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="82">
   <path class="kv35" d="M 615.97 108.04 L 650.98 108.09" stroke-width="1" zvalue="83"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="79@0" LinkObjectIDznd="81@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 615.97 108.04 L 650.98 108.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="kv35" d="M 681.62 108.13 L 739.21 108.13" stroke-width="1" zvalue="84"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="81@0" LinkObjectIDznd="74" MaxPinNum="2"/>
   </metadata>
  <path d="M 681.62 108.13 L 739.21 108.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="v6300" d="M 654.29 539.5 L 654.29 592.29 L 782.86 592.29 L 782.86 539.5" stroke-width="1" zvalue="85"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="33@1" LinkObjectIDznd="34@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 654.29 539.5 L 654.29 592.29 L 782.86 592.29 L 782.86 539.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="86">
   <path class="v6300" d="M 716.07 592.29 L 716.07 634.55" stroke-width="1" zvalue="86"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="85" LinkObjectIDznd="42@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 716.07 592.29 L 716.07 634.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="v6300" d="M 716.29 671.42 L 716.29 692.81" stroke-width="1" zvalue="90"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="42@1" LinkObjectIDznd="89@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 716.29 671.42 L 716.29 692.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="v6300" d="M 716.52 723.45 L 716.52 745.14" stroke-width="1" zvalue="91"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="89@1" LinkObjectIDznd="36@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 716.52 723.45 L 716.52 745.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="v6300" d="M 674.93 824.55 L 674.93 806.3" stroke-width="1" zvalue="95"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@0" LinkObjectIDznd="94@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 674.93 824.55 L 674.93 806.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="v6300" d="M 675.13 775.66 L 675.13 745.14" stroke-width="1" zvalue="96"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="94@0" LinkObjectIDznd="36@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 675.13 775.66 L 675.13 745.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="99">
   <path class="v6300" d="M 673.21 914.84 L 673.21 861.42" stroke-width="1" zvalue="98"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="97@0" LinkObjectIDznd="47@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 673.21 914.84 L 673.21 861.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="v6300" d="M 796.36 825.98 L 796.36 807.73" stroke-width="1" zvalue="103"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="110@0" LinkObjectIDznd="109@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 796.36 825.98 L 796.36 807.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="107">
   <path class="v6300" d="M 796.55 777.09 L 796.55 745.14" stroke-width="1" zvalue="105"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109@0" LinkObjectIDznd="36@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 796.55 777.09 L 796.55 745.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="105">
   <path class="v6300" d="M 794.64 916.27 L 794.64 862.85" stroke-width="1" zvalue="108"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="106@0" LinkObjectIDznd="110@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 794.64 916.27 L 794.64 862.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="115">
   <path class="v6300" d="M 990.93 906.09 L 990.93 824.87" stroke-width="1" zvalue="113"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="111@0" LinkObjectIDznd="114@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 990.93 906.09 L 990.93 824.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="116">
   <path class="v6300" d="M 992.27 794.23 L 992.27 745.14" stroke-width="1" zvalue="114"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="36@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 992.27 794.23 L 992.27 745.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="150">
   <path class="kv10" d="M 1201.85 907.52 L 1201.85 826.3" stroke-width="1" zvalue="149"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="152@0" LinkObjectIDznd="151@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1201.85 907.52 L 1201.85 826.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="kv10" d="M 1203.2 795.66 L 1203.2 745.14" stroke-width="1" zvalue="151"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="151@0" LinkObjectIDznd="37@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1203.2 795.66 L 1203.2 745.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="kv10" d="M 1348.5 841.69 L 1348.5 814.87" stroke-width="1" zvalue="173"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="182@0" LinkObjectIDznd="180@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1348.5 841.69 L 1348.5 814.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="178">
   <path class="kv10" d="M 1348.94 784.23 L 1348.94 745.14" stroke-width="1" zvalue="175"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="180@0" LinkObjectIDznd="37@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1348.94 784.23 L 1348.94 745.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="176">
   <path class="kv10" d="M 1350.32 968.11 L 1350.32 929.16" stroke-width="1" zvalue="178"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="181@0" LinkObjectIDznd="177@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1350.32 968.11 L 1350.32 929.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="175">
   <path class="kv10" d="M 1350.37 898.52 L 1350.37 878.56" stroke-width="1" zvalue="179"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="177@0" LinkObjectIDznd="182@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1350.37 898.52 L 1350.37 878.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="191">
   <path class="kv10" d="M 1431.36 841.69 L 1431.36 814.87" stroke-width="1" zvalue="186"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="194@0" LinkObjectIDznd="192@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1431.36 841.69 L 1431.36 814.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="190">
   <path class="kv10" d="M 1431.8 784.23 L 1431.8 745.14" stroke-width="1" zvalue="188"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="192@0" LinkObjectIDznd="37@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1431.8 784.23 L 1431.8 745.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="188">
   <path class="kv10" d="M 1433.18 968.11 L 1433.18 929.16" stroke-width="1" zvalue="191"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="193@0" LinkObjectIDznd="189@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1433.18 968.11 L 1433.18 929.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="187">
   <path class="kv10" d="M 1433.22 898.52 L 1433.22 878.56" stroke-width="1" zvalue="192"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="189@0" LinkObjectIDznd="194@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1433.22 898.52 L 1433.22 878.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="203">
   <path class="kv10" d="M 1529.93 845.98 L 1529.93 819.16" stroke-width="1" zvalue="199"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="206@0" LinkObjectIDznd="204@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1529.93 845.98 L 1529.93 819.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="202">
   <path class="kv10" d="M 1530.37 788.52 L 1530.37 745.14" stroke-width="1" zvalue="201"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="204@0" LinkObjectIDznd="37@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1530.37 788.52 L 1530.37 745.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="200">
   <path class="kv10" d="M 1531.75 972.39 L 1531.75 933.45" stroke-width="1" zvalue="204"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="205@0" LinkObjectIDznd="201@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1531.75 972.39 L 1531.75 933.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="199">
   <path class="kv10" d="M 1531.8 902.81 L 1531.8 882.85" stroke-width="1" zvalue="205"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="201@0" LinkObjectIDznd="206@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1531.8 902.81 L 1531.8 882.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="215">
   <path class="kv10" d="M 1655.64 843.12 L 1655.64 816.3" stroke-width="1" zvalue="212"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="218@0" LinkObjectIDznd="216@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1655.64 843.12 L 1655.64 816.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="214">
   <path class="kv10" d="M 1656.08 785.66 L 1656.08 745.14" stroke-width="1" zvalue="214"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="216@0" LinkObjectIDznd="37@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1656.08 785.66 L 1656.08 745.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="212">
   <path class="kv10" d="M 1657.47 969.54 L 1657.47 930.59" stroke-width="1" zvalue="217"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="217@0" LinkObjectIDznd="213@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1657.47 969.54 L 1657.47 930.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="211">
   <path class="kv10" d="M 1657.51 899.95 L 1657.51 879.99" stroke-width="1" zvalue="218"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="213@0" LinkObjectIDznd="218@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1657.51 899.95 L 1657.51 879.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="227">
   <path class="kv10" d="M 1738.5 841.69 L 1738.5 814.87" stroke-width="1" zvalue="225"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="230@0" LinkObjectIDznd="228@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1738.5 841.69 L 1738.5 814.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="226">
   <path class="kv10" d="M 1738.94 784.23 L 1738.94 745.14" stroke-width="1" zvalue="227"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="228@0" LinkObjectIDznd="37@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1738.94 784.23 L 1738.94 745.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="224">
   <path class="kv10" d="M 1740.32 968.11 L 1740.32 929.16" stroke-width="1" zvalue="230"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="229@0" LinkObjectIDznd="225@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1740.32 968.11 L 1740.32 929.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="223">
   <path class="kv10" d="M 1740.37 898.52 L 1740.37 878.56" stroke-width="1" zvalue="231"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="225@0" LinkObjectIDznd="230@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1740.37 898.52 L 1740.37 878.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="232">
   <path class="v6300" d="M 1009.43 614.56 L 1009.43 664.23" stroke-width="1" zvalue="234"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44@1" LinkObjectIDznd="231@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1009.43 614.56 L 1009.43 664.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="233">
   <path class="v6300" d="M 1007.94 694.87 L 1007.94 745.14" stroke-width="1" zvalue="235"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="231@1" LinkObjectIDznd="36@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1007.94 694.87 L 1007.94 745.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="235">
   <path class="v6300" d="M 1009.21 577.69 L 1009.21 530.59" stroke-width="1" zvalue="238"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44@0" LinkObjectIDznd="234@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1009.21 577.69 L 1009.21 530.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="236">
   <path class="v6300" d="M 1009.41 499.95 L 1009.41 326 L 1123.72 326" stroke-width="1" zvalue="239"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="234@0" LinkObjectIDznd="45@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1009.41 499.95 L 1009.41 326 L 1123.72 326" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="238">
   <path class="v6300" d="M 1160.59 325.79 L 1358.57 325.79 L 1358.57 390.86 L 1297.98 390.86 L 1297.98 409.95" stroke-width="1" zvalue="242"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@0" LinkObjectIDznd="237@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1160.59 325.79 L 1358.57 325.79 L 1358.57 390.86 L 1297.98 390.86 L 1297.98 409.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="239">
   <path class="v6300" d="M 1297.94 440.59 L 1297.94 469.22" stroke-width="1" zvalue="243"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="237@1" LinkObjectIDznd="38@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1297.94 440.59 L 1297.94 469.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="241">
   <path class="v6300" d="M 1358.57 390.86 L 1427.23 390.86 L 1427.23 411.38" stroke-width="1" zvalue="246"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="238" LinkObjectIDznd="240@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1358.57 390.86 L 1427.23 390.86 L 1427.23 411.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="242">
   <path class="v6300" d="M 1427.19 442.02 L 1427.19 469.22" stroke-width="1" zvalue="247"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="240@1" LinkObjectIDznd="39@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1427.19 442.02 L 1427.19 469.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="244">
   <path class="kv10" d="M 1373.82 587.69 L 1373.82 595.66" stroke-width="1" zvalue="250"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="262" LinkObjectIDznd="243@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1373.82 587.69 L 1373.82 595.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="245">
   <path class="kv10" d="M 1373.79 626.3 L 1373.79 644.55" stroke-width="1" zvalue="251"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="243@1" LinkObjectIDznd="46@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1373.79 626.3 L 1373.79 644.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="247">
   <path class="kv10" d="M 1374 681.42 L 1374 695.66" stroke-width="1" zvalue="254"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@1" LinkObjectIDznd="246@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1374 681.42 L 1374 695.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="248">
   <path class="kv10" d="M 1373.96 726.3 L 1373.96 745.14" stroke-width="1" zvalue="255"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="246@1" LinkObjectIDznd="37@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1373.96 726.3 L 1373.96 745.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="261">
   <path class="kv10" d="M 1298.57 539.5 L 1298.57 548.52" stroke-width="1" zvalue="267"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@1" LinkObjectIDznd="260@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1298.57 539.5 L 1298.57 548.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="262">
   <path class="kv10" d="M 1299.37 579.16 L 1299.37 587.69 L 1427.1 587.69 L 1427.1 579.16" stroke-width="1" zvalue="268"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="260@1" LinkObjectIDznd="256@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1299.37 579.16 L 1299.37 587.69 L 1427.1 587.69 L 1427.1 579.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="279">
   <path class="kv10" d="M 1427.14 548.52 L 1427.14 539.5" stroke-width="1" zvalue="282"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="256@0" LinkObjectIDznd="39@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1427.14 548.52 L 1427.14 539.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="282">
   <path class="v6300" d="M 502.47 810.59 L 502.47 839.35" stroke-width="1" zvalue="286"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="281@1" LinkObjectIDznd="280@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 502.47 810.59 L 502.47 839.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="283">
   <path class="v6300" d="M 502.51 779.95 L 502.51 745.14" stroke-width="1" zvalue="287"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="281@0" LinkObjectIDznd="36@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 502.51 779.95 L 502.51 745.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv10" d="M 1283.44 785.95 L 1283.44 745.14" stroke-width="1" zvalue="292"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="141@0" LinkObjectIDznd="37@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1283.44 785.95 L 1283.44 745.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="129">
   <path class="v6300" d="M 589.21 785.95 L 589.21 745.14" stroke-width="1" zvalue="300"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="122@0" LinkObjectIDznd="36@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 589.21 785.95 L 589.21 745.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="130">
   <path class="v6300" d="M 588.23 866.49 L 588.23 816.59" stroke-width="1" zvalue="301"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="126@0" LinkObjectIDznd="122@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 588.23 866.49 L 588.23 816.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="133">
   <path class="kv10" d="M 1284.01 884.27 L 1284.01 816.59" stroke-width="1" zvalue="305"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="132@0" LinkObjectIDznd="141@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1284.01 884.27 L 1284.01 816.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="58">
   <use class="kv35" height="30" transform="rotate(0,652.857,343.714) scale(1.42857,1.04762) translate(-192.643,-14.9091)" width="15" x="642.1428571428572" xlink:href="#Disconnector:刀闸_0" y="328.0000000000001" zvalue="63"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449788116998" ObjectName="#1主变35kV侧3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449788116998"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,652.857,343.714) scale(1.42857,1.04762) translate(-192.643,-14.9091)" width="15" x="642.1428571428572" y="328.0000000000001"/></g>
  <g id="62">
   <use class="kv35" height="30" transform="rotate(0,780.714,343.714) scale(1.42857,1.04762) translate(-231,-14.9091)" width="15" x="770" xlink:href="#Disconnector:刀闸_0" y="328.0000001362392" zvalue="67"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449788182534" ObjectName="#2主变35kV侧3021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449788182534"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,780.714,343.714) scale(1.42857,1.04762) translate(-231,-14.9091)" width="15" x="770" y="328.0000001362392"/></g>
  <g id="72">
   <use class="kv35" height="30" transform="rotate(0,739.089,168) scale(1.42857,1.04762) translate(-218.512,-6.92208)" width="15" x="728.3746042130562" xlink:href="#Disconnector:刀闸_0" y="152.2857142857144" zvalue="74"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449788313606" ObjectName="35kV果弄线3616隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449788313606"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,739.089,168) scale(1.42857,1.04762) translate(-218.512,-6.92208)" width="15" x="728.3746042130562" y="152.2857142857144"/></g>
  <g id="81">
   <use class="kv35" height="30" transform="rotate(90,666.429,108) scale(1.42857,1.04762) translate(-196.714,-4.19481)" width="15" x="655.7142857142856" xlink:href="#Disconnector:刀闸_0" y="92.28571428571428" zvalue="82"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449788575750" ObjectName="35kV果弄线3619隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449788575750"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,666.429,108) scale(1.42857,1.04762) translate(-196.714,-4.19481)" width="15" x="655.7142857142856" y="92.28571428571428"/></g>
  <g id="89">
   <use class="v6300" height="30" transform="rotate(0,716.429,708) scale(1.42857,1.04762) translate(-211.714,-31.4675)" width="15" x="705.7142857142858" xlink:href="#Disconnector:刀闸_0" y="692.2857142857142" zvalue="89"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449788641286" ObjectName="601断路器6011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449788641286"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,716.429,708) scale(1.42857,1.04762) translate(-211.714,-31.4675)" width="15" x="705.7142857142858" y="692.2857142857142"/></g>
  <g id="94">
   <use class="v6300" height="30" transform="rotate(0,675,790.857) scale(1.42857,1.04762) translate(-199.286,-35.2338)" width="15" x="664.2857142857144" xlink:href="#Disconnector:刀闸_0" y="775.1428571428571" zvalue="94"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449788706822" ObjectName="#1发电机6611隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449788706822"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,675,790.857) scale(1.42857,1.04762) translate(-199.286,-35.2338)" width="15" x="664.2857142857144" y="775.1428571428571"/></g>
  <g id="109">
   <use class="v6300" height="30" transform="rotate(0,796.429,792.286) scale(1.42857,1.04762) translate(-235.714,-35.2987)" width="15" x="785.7142857142859" xlink:href="#Disconnector:刀闸_0" y="776.5714307512555" zvalue="102"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449788903430" ObjectName="#2发电机6621隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449788903430"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,796.429,792.286) scale(1.42857,1.04762) translate(-235.714,-35.2987)" width="15" x="785.7142857142859" y="776.5714307512555"/></g>
  <g id="114">
   <use class="v6300" height="30" transform="rotate(0,992.143,809.429) scale(1.42857,1.04762) translate(-294.429,-36.0779)" width="15" x="981.4285714285714" xlink:href="#Disconnector:刀闸_0" y="793.7142857142857" zvalue="112"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449789034502" ObjectName="#1厂用变6811隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449789034502"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,992.143,809.429) scale(1.42857,1.04762) translate(-294.429,-36.0779)" width="15" x="981.4285714285714" y="793.7142857142857"/></g>
  <g id="122">
   <use class="v6300" height="30" transform="rotate(0,589.063,801.143) scale(1.64868,1.04762) translate(-226.905,-35.7013)" width="15" x="576.6983365369919" xlink:href="#Disconnector:刀闸_0" y="785.4285714285716" zvalue="120"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449789100038" ObjectName="6.3kV母线PT6901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449789100038"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,589.063,801.143) scale(1.64868,1.04762) translate(-226.905,-35.7013)" width="15" x="576.6983365369919" y="785.4285714285716"/></g>
  <g id="141">
   <use class="kv10" height="30" transform="rotate(0,1283.29,801.143) scale(1.64868,1.04762) translate(-500.053,-35.7013)" width="15" x="1270.928571973528" xlink:href="#Disconnector:刀闸_0" y="785.4285736083984" zvalue="137"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449789165574" ObjectName="10kV母线PT0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449789165574"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1283.29,801.143) scale(1.64868,1.04762) translate(-500.053,-35.7013)" width="15" x="1270.928571973528" y="785.4285736083984"/></g>
  <g id="151">
   <use class="kv10" height="30" transform="rotate(0,1203.07,810.857) scale(1.42857,1.04762) translate(-357.707,-36.1429)" width="15" x="1192.3571434021" xlink:href="#Disconnector:刀闸_0" y="795.142859322684" zvalue="148"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449789231110" ObjectName="#2厂用变0811隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449789231110"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1203.07,810.857) scale(1.42857,1.04762) translate(-357.707,-36.1429)" width="15" x="1192.3571434021" y="795.142859322684"/></g>
  <g id="180">
   <use class="kv10" height="30" transform="rotate(0,1348.79,799.429) scale(1.64868,1.04762) translate(-525.825,-35.6234)" width="15" x="1336.428571292332" xlink:href="#Disconnector:刀闸_0" y="783.7142879281723" zvalue="172"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449789427718" ObjectName="备用一0711隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449789427718"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1348.79,799.429) scale(1.64868,1.04762) translate(-525.825,-35.6234)" width="15" x="1336.428571292332" y="783.7142879281723"/></g>
  <g id="177">
   <use class="kv10" height="30" transform="rotate(0,1350.22,913.714) scale(1.64868,1.04762) translate(-526.387,-40.8182)" width="15" x="1337.857142720904" xlink:href="#Disconnector:刀闸_0" y="898.0000022138867" zvalue="176"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449789362182" ObjectName="备用一0716隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449789362182"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1350.22,913.714) scale(1.64868,1.04762) translate(-526.387,-40.8182)" width="15" x="1337.857142720904" y="898.0000022138867"/></g>
  <g id="192">
   <use class="kv10" height="30" transform="rotate(0,1431.65,799.429) scale(1.64868,1.04762) translate(-558.425,-35.6234)" width="15" x="1419.285714149475" xlink:href="#Disconnector:刀闸_0" y="783.7142879281723" zvalue="185"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449789624326" ObjectName="备用一0721隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449789624326"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1431.65,799.429) scale(1.64868,1.04762) translate(-558.425,-35.6234)" width="15" x="1419.285714149475" y="783.7142879281723"/></g>
  <g id="189">
   <use class="kv10" height="30" transform="rotate(0,1433.08,913.714) scale(1.64868,1.04762) translate(-558.987,-40.8182)" width="15" x="1420.714285578047" xlink:href="#Disconnector:刀闸_0" y="898.0000022138868" zvalue="189"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449789558790" ObjectName="备用一0726隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449789558790"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1433.08,913.714) scale(1.64868,1.04762) translate(-558.987,-40.8182)" width="15" x="1420.714285578047" y="898.0000022138868"/></g>
  <g id="204">
   <use class="kv10" height="30" transform="rotate(0,1530.22,803.714) scale(1.64868,1.04762) translate(-597.209,-35.8182)" width="15" x="1517.857142720904" xlink:href="#Disconnector:刀闸_0" y="788.0000022138865" zvalue="198"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449789820934" ObjectName="生活区线0731隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449789820934"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1530.22,803.714) scale(1.64868,1.04762) translate(-597.209,-35.8182)" width="15" x="1517.857142720904" y="788.0000022138865"/></g>
  <g id="201">
   <use class="kv10" height="30" transform="rotate(0,1531.65,918) scale(1.64868,1.04762) translate(-597.771,-41.013)" width="15" x="1519.285714149475" xlink:href="#Disconnector:刀闸_0" y="902.285716499601" zvalue="202"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449789755398" ObjectName="生活区线0736隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449789755398"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1531.65,918) scale(1.64868,1.04762) translate(-597.771,-41.013)" width="15" x="1519.285714149475" y="902.285716499601"/></g>
  <g id="216">
   <use class="kv10" height="30" transform="rotate(0,1655.94,800.857) scale(1.64868,1.04762) translate(-646.671,-35.6883)" width="15" x="1643.571428435189" xlink:href="#Disconnector:刀闸_0" y="785.1428593567437" zvalue="211"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449790017542" ObjectName="尾水联络线0741隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449790017542"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1655.94,800.857) scale(1.64868,1.04762) translate(-646.671,-35.6883)" width="15" x="1643.571428435189" y="785.1428593567437"/></g>
  <g id="213">
   <use class="kv10" height="30" transform="rotate(0,1657.37,915.143) scale(1.64868,1.04762) translate(-647.233,-40.8831)" width="15" x="1644.999999863761" xlink:href="#Disconnector:刀闸_0" y="899.4285736424581" zvalue="215"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449789952006" ObjectName="尾水联络线0746隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449789952006"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1657.37,915.143) scale(1.64868,1.04762) translate(-647.233,-40.8831)" width="15" x="1644.999999863761" y="899.4285736424581"/></g>
  <g id="228">
   <use class="kv10" height="30" transform="rotate(0,1738.79,799.429) scale(1.64868,1.04762) translate(-679.272,-35.6234)" width="15" x="1726.428571292332" xlink:href="#Disconnector:刀闸_0" y="783.7142879281723" zvalue="224"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449790214150" ObjectName="盾中联络线0751隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449790214150"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1738.79,799.429) scale(1.64868,1.04762) translate(-679.272,-35.6234)" width="15" x="1726.428571292332" y="783.7142879281723"/></g>
  <g id="225">
   <use class="kv10" height="30" transform="rotate(0,1740.22,913.714) scale(1.64868,1.04762) translate(-679.834,-40.8182)" width="15" x="1727.857142720904" xlink:href="#Disconnector:刀闸_0" y="898.0000022138867" zvalue="228"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449790148614" ObjectName="盾中联络线0756隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449790148614"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1740.22,913.714) scale(1.64868,1.04762) translate(-679.834,-40.8182)" width="15" x="1727.857142720904" y="898.0000022138867"/></g>
  <g id="231">
   <use class="v6300" height="30" transform="rotate(0,1007.86,679.429) scale(1.42857,1.04762) translate(-299.143,-30.1688)" width="15" x="997.1428571428571" xlink:href="#Disconnector:刀闸_0" y="663.7142857142857" zvalue="233"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449790345222" ObjectName="602断路器6021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449790345222"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1007.86,679.429) scale(1.42857,1.04762) translate(-299.143,-30.1688)" width="15" x="997.1428571428571" y="663.7142857142857"/></g>
  <g id="234">
   <use class="v6300" height="30" transform="rotate(0,1009.29,515.143) scale(1.42857,1.04762) translate(-299.571,-22.7013)" width="15" x="998.5714285714286" xlink:href="#Disconnector:刀闸_0" y="499.4285714285714" zvalue="237"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449790410758" ObjectName="602断路器6026隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449790410758"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1009.29,515.143) scale(1.42857,1.04762) translate(-299.571,-22.7013)" width="15" x="998.5714285714286" y="499.4285714285714"/></g>
  <g id="237">
   <use class="v6300" height="30" transform="rotate(0,1297.86,425.143) scale(1.42857,1.04762) translate(-386.143,-18.6104)" width="15" x="1287.142857142857" xlink:href="#Disconnector:刀闸_0" y="409.4285714285714" zvalue="241"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449790476293" ObjectName="#3主变6.3kV侧6036隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449790476293"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1297.86,425.143) scale(1.42857,1.04762) translate(-386.143,-18.6104)" width="15" x="1287.142857142857" y="409.4285714285714"/></g>
  <g id="240">
   <use class="v6300" height="30" transform="rotate(0,1427.1,426.571) scale(1.42857,1.04762) translate(-424.916,-18.6753)" width="15" x="1416.388224837991" xlink:href="#Disconnector:刀闸_0" y="410.8571428571429" zvalue="245"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449790541829" ObjectName="#4主变6.3kV侧6046隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449790541829"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1427.1,426.571) scale(1.42857,1.04762) translate(-424.916,-18.6753)" width="15" x="1416.388224837991" y="410.8571428571429"/></g>
  <g id="243">
   <use class="kv10" height="30" transform="rotate(0,1373.7,610.857) scale(1.42857,1.04762) translate(-408.895,-27.0519)" width="15" x="1362.984050822706" xlink:href="#Disconnector:刀闸_0" y="595.1428571428571" zvalue="249"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449790607365" ObjectName="001断路器0016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449790607365"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1373.7,610.857) scale(1.42857,1.04762) translate(-408.895,-27.0519)" width="15" x="1362.984050822706" y="595.1428571428571"/></g>
  <g id="246">
   <use class="kv10" height="30" transform="rotate(0,1373.87,710.857) scale(1.42857,1.04762) translate(-408.948,-31.5974)" width="15" x="1363.16031849877" xlink:href="#Disconnector:刀闸_0" y="695.1428571428571" zvalue="253"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449790672901" ObjectName="001断路器0011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449790672901"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1373.87,710.857) scale(1.42857,1.04762) translate(-408.948,-31.5974)" width="15" x="1363.16031849877" y="695.1428571428571"/></g>
  <g id="256">
   <use class="kv10" height="30" transform="rotate(0,1427.02,563.714) scale(1.42857,1.04762) translate(-424.891,-24.9091)" width="15" x="1416.303175641627" xlink:href="#Disconnector:刀闸_0" y="548" zvalue="261"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449790738438" ObjectName="#4主变10kV侧0015隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449790738438"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1427.02,563.714) scale(1.42857,1.04762) translate(-424.891,-24.9091)" width="15" x="1416.303175641627" y="548"/></g>
  <g id="260">
   <use class="kv10" height="30" transform="rotate(0,1299.29,563.714) scale(1.42857,1.04762) translate(-386.571,-24.9091)" width="15" x="1288.571428571428" xlink:href="#Disconnector:刀闸_0" y="548" zvalue="266"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449790803974" ObjectName="#3主变10kV侧0014隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449790803974"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1299.29,563.714) scale(1.42857,1.04762) translate(-386.571,-24.9091)" width="15" x="1288.571428571428" y="548"/></g>
  <g id="281">
   <use class="v6300" height="30" transform="rotate(0,502.365,795.143) scale(1.64868,1.04762) translate(-192.793,-35.4286)" width="15" x="490.0000000000001" xlink:href="#Disconnector:刀闸_0" y="779.4285714285716" zvalue="285"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449790935046" ObjectName="6.3kV母线电压互感器6631隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449790935046"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,502.365,795.143) scale(1.64868,1.04762) translate(-192.793,-35.4286)" width="15" x="490.0000000000001" y="779.4285714285716"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="76">
   <use class="kv35" height="20" transform="rotate(270,832.857,139.429) scale(1.42857,1.42857) translate(-247.714,-37.5429)" width="10" x="825.7142857142858" xlink:href="#GroundDisconnector:地刀_0" y="125.1428571428573" zvalue="77"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449788444678" ObjectName="35kV果弄线36167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449788444678"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,832.857,139.429) scale(1.42857,1.42857) translate(-247.714,-37.5429)" width="10" x="825.7142857142858" y="125.1428571428573"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="79">
   <use class="kv35" height="18" transform="rotate(90,599.643,109.857) scale(1.95238,1.95238) translate(-285.366,-45.0174)" width="15" x="585" xlink:href="#Accessory:PT8_0" y="92.28571428571422" zvalue="79"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449788510214" ObjectName="35kV果弄线避雷器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(90,599.643,109.857) scale(1.95238,1.95238) translate(-285.366,-45.0174)" width="15" x="585" y="92.28571428571422"/></g>
  <g id="280">
   <use class="v6300" height="60" transform="rotate(0,507.857,880.5) scale(1.46429,1.46429) translate(-151.742,-265.254)" width="40" x="478.5714285714285" xlink:href="#Accessory:带电容器的互感器_0" y="836.5714285714286" zvalue="283"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449790869510" ObjectName="6.3kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,507.857,880.5) scale(1.46429,1.46429) translate(-151.742,-265.254)" width="40" x="478.5714285714285" y="836.5714285714286"/></g>
  <g id="126">
   <use class="v6300" height="30" transform="rotate(0,588.333,896.333) scale(2.03704,2.03704) translate(-283.96,-440.76)" width="30" x="557.7777777777778" xlink:href="#Accessory:三卷PT带容断器_0" y="865.7777777777778" zvalue="298"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449791000581" ObjectName="6.3kV母线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,588.333,896.333) scale(2.03704,2.03704) translate(-283.96,-440.76)" width="30" x="557.7777777777778" y="865.7777777777778"/></g>
  <g id="132">
   <use class="kv10" height="30" transform="rotate(0,1284.11,914.111) scale(2.03704,2.03704) translate(-638.174,-449.81)" width="30" x="1253.555555555556" xlink:href="#Accessory:三卷PT带容断器_0" y="883.5555555555555" zvalue="304"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449791066117" ObjectName="10kV母线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1284.11,914.111) scale(2.03704,2.03704) translate(-638.174,-449.81)" width="30" x="1253.555555555556" y="883.5555555555555"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="97">
   <use class="v6300" height="30" transform="rotate(0,673.214,939.071) scale(1.64286,1.64286) translate(-253.789,-357.82)" width="30" x="648.5714285714286" xlink:href="#Generator:发电机_0" y="914.4285714285714" zvalue="97"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449788772358" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449788772358"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,673.214,939.071) scale(1.64286,1.64286) translate(-253.789,-357.82)" width="30" x="648.5714285714286" y="914.4285714285714"/></g>
  <g id="106">
   <use class="v6300" height="30" transform="rotate(0,794.643,940.5) scale(1.64286,1.64286) translate(-301.304,-358.379)" width="30" x="770" xlink:href="#Generator:发电机_0" y="915.8571450369698" zvalue="106"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449788837894" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449788837894"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,794.643,940.5) scale(1.64286,1.64286) translate(-301.304,-358.379)" width="30" x="770" y="915.8571450369698"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="111">
   <use class="v6300" height="30" transform="rotate(0,990.714,939.395) scale(2.29592,2.31203) translate(-541.06,-513.407)" width="28" x="958.5714285714286" xlink:href="#EnergyConsumer:站用变DY接地_0" y="904.7142857142856" zvalue="109"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449788968966" ObjectName="#1厂用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,990.714,939.395) scale(2.29592,2.31203) translate(-541.06,-513.407)" width="28" x="958.5714285714286" y="904.7142857142856"/></g>
  <g id="152">
   <use class="kv10" height="30" transform="rotate(0,1201.64,940.823) scale(2.29592,2.31203) translate(-660.118,-514.218)" width="28" x="1169.500000544957" xlink:href="#EnergyConsumer:站用变DY接地_0" y="906.1428593226839" zvalue="146"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449789296646" ObjectName="#2厂用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1201.64,940.823) scale(2.29592,2.31203) translate(-660.118,-514.218)" width="28" x="1169.500000544957" y="906.1428593226839"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="181">
   <use class="kv10" height="30" transform="rotate(0,1350.32,978.714) scale(2.65306,-0.714286) translate(-835.569,-2353.2)" width="7" x="1341.037386585989" xlink:href="#ACLineSegment:线路_0" y="968.0000022138865" zvalue="170"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449789493254" ObjectName="备用一"/>
   <cge:TPSR_Ref TObjectID="6192449789493254_5066549586034690"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1350.32,978.714) scale(2.65306,-0.714286) translate(-835.569,-2353.2)" width="7" x="1341.037386585989" y="968.0000022138865"/></g>
  <g id="193">
   <use class="kv10" height="30" transform="rotate(0,1433.18,978.714) scale(2.65306,-0.714286) translate(-887.196,-2353.2)" width="7" x="1423.894529443132" xlink:href="#ACLineSegment:线路_0" y="968.0000022138867" zvalue="183"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449789689862" ObjectName="备用一线"/>
   <cge:TPSR_Ref TObjectID="6192449789689862_5066549586034690"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1433.18,978.714) scale(2.65306,-0.714286) translate(-887.196,-2353.2)" width="7" x="1423.894529443132" y="968.0000022138867"/></g>
  <g id="205">
   <use class="kv10" height="30" transform="rotate(0,1531.75,983) scale(2.65306,-0.714286) translate(-948.613,-2363.49)" width="7" x="1522.46595801456" xlink:href="#ACLineSegment:线路_0" y="972.2857164996008" zvalue="196"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449789886470" ObjectName="生活区线"/>
   <cge:TPSR_Ref TObjectID="6192449789886470_5066549586034690"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1531.75,983) scale(2.65306,-0.714286) translate(-948.613,-2363.49)" width="7" x="1522.46595801456" y="972.2857164996008"/></g>
 </g>
 <g id="StateClass">
  <g id="87">
   <use height="30" transform="rotate(0,307.812,124.464) scale(1.22222,1.03092) translate(-45.9659,-3.26938)" width="90" x="252.81" xlink:href="#State:全站检修_0" y="109" zvalue="313"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549586034690" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,307.812,124.464) scale(1.22222,1.03092) translate(-45.9659,-3.26938)" width="90" x="252.81" y="109"/></g>
 </g>
</svg>