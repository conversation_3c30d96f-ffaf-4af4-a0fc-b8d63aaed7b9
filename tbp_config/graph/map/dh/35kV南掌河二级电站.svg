<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549587214338" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:手车开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:手车开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="Breaker:手车开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.45181617405996" x2="1.714850492606708" y1="5.276370517142858" y2="14.05696281619048"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.583333333333333" x2="8.583333333333334" y1="5.166666666666667" y2="14.16666666666667"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="1" y1="23" y2="13"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="6" y1="23" y2="11"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="0" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.499999999999999" x2="5.916666666666666" y1="3.583333333333334" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="4.416666666666668" y2="20.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="4.249999999999998" y2="20.16666666666667"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="12.0194189818494"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="5.416666666666668" y2="12.02321291373541"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.92126160277786" x2="6.666666666666666" y1="12.10207526123773" y2="18.50000000000001"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.18646053143751" x2="24.5" y1="12.18904818695178" y2="19.00000000000001"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 7.33333 43.5833 L 23 43.5833 L 15 31.5 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV南掌河二级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="298" x="35" xlink:href="logo.png" y="42"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,184,72) scale(1,1) translate(0,0)" writing-mode="lr" x="184" xml:space="preserve" y="75.5" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,180.5,71.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="180.5" xml:space="preserve" y="80.69" zvalue="3">35kV南掌河二级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="8" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,94.4375,310) scale(1,1) translate(0,0)" width="72.88" x="58" y="298" zvalue="145"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,94.4375,310) scale(1,1) translate(0,0)" writing-mode="lr" x="94.44" xml:space="preserve" y="314.5" zvalue="145">信号一览</text>
  <line fill="none" id="32" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="377.0000000000001" x2="377.0000000000001" y1="10" y2="1040" zvalue="4"/>
  <line fill="none" id="30" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.000000000000796" x2="370.0000000000003" y1="145.8704926140824" y2="145.8704926140824" zvalue="6"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="185.0000000000001" y1="158.0000000000001" y2="158.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="185.0000000000001" y1="184.0000000000001" y2="184.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="4.000000000000114" y1="158.0000000000001" y2="184.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="185.0000000000001" y1="158.0000000000001" y2="184.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="366.0000000000001" y1="158.0000000000001" y2="158.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="366.0000000000001" y1="184.0000000000001" y2="184.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="185.0000000000001" y1="158.0000000000001" y2="184.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.0000000000001" x2="366.0000000000001" y1="158.0000000000001" y2="184.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="185.0000000000001" y1="184.0000000000001" y2="184.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="185.0000000000001" y1="208.2500000000001" y2="208.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="4.000000000000114" y1="184.0000000000001" y2="208.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="185.0000000000001" y1="184.0000000000001" y2="208.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="366.0000000000001" y1="184.0000000000001" y2="184.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="366.0000000000001" y1="208.2500000000001" y2="208.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="185.0000000000001" y1="184.0000000000001" y2="208.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.0000000000001" x2="366.0000000000001" y1="184.0000000000001" y2="208.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="185.0000000000001" y1="208.2500000000001" y2="208.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="185.0000000000001" y1="231.0000000000001" y2="231.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="4.000000000000114" y1="208.2500000000001" y2="231.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="185.0000000000001" y1="208.2500000000001" y2="231.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="366.0000000000001" y1="208.2500000000001" y2="208.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="366.0000000000001" y1="231.0000000000001" y2="231.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="185.0000000000001" y1="208.2500000000001" y2="231.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.0000000000001" x2="366.0000000000001" y1="208.2500000000001" y2="231.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="185.0000000000001" y1="231.0000000000001" y2="231.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="185.0000000000001" y1="253.7500000000001" y2="253.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="4.000000000000114" y1="231.0000000000001" y2="253.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="185.0000000000001" y1="231.0000000000001" y2="253.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="366.0000000000001" y1="231.0000000000001" y2="231.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="366.0000000000001" y1="253.7500000000001" y2="253.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="185.0000000000001" y1="231.0000000000001" y2="253.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.0000000000001" x2="366.0000000000001" y1="231.0000000000001" y2="253.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="185.0000000000001" y1="253.7500000000001" y2="253.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="185.0000000000001" y1="276.5000000000001" y2="276.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="4.000000000000114" y1="253.7500000000001" y2="276.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="185.0000000000001" y1="253.7500000000001" y2="276.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="366.0000000000001" y1="253.7500000000001" y2="253.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="366.0000000000001" y1="276.5000000000001" y2="276.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="185.0000000000001" y1="253.7500000000001" y2="276.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.0000000000001" x2="366.0000000000001" y1="253.7500000000001" y2="276.5000000000001"/>
  <line fill="none" id="28" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.000000000000796" x2="370.0000000000003" y1="615.8704926140824" y2="615.8704926140824" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.000000000000114" x2="93.00000000000011" y1="931" y2="931"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.000000000000114" x2="93.00000000000011" y1="970.1632999999999" y2="970.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.000000000000114" x2="3.000000000000114" y1="931" y2="970.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.00000000000011" x2="93.00000000000011" y1="931" y2="970.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.00000000000011" x2="363.0000000000001" y1="931" y2="931"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.00000000000011" x2="363.0000000000001" y1="970.1632999999999" y2="970.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.00000000000011" x2="93.00000000000011" y1="931" y2="970.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.0000000000001" x2="363.0000000000001" y1="931" y2="970.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.000000000000114" x2="93.00000000000011" y1="970.16327" y2="970.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.000000000000114" x2="93.00000000000011" y1="998.08167" y2="998.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.000000000000114" x2="3.000000000000114" y1="970.16327" y2="998.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.00000000000011" x2="93.00000000000011" y1="970.16327" y2="998.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.00000000000011" x2="183.0000000000001" y1="970.16327" y2="970.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.00000000000011" x2="183.0000000000001" y1="998.08167" y2="998.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.00000000000011" x2="93.00000000000011" y1="970.16327" y2="998.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="970.16327" y2="998.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.0000000000002" x2="273.0000000000002" y1="970.16327" y2="970.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.0000000000002" x2="273.0000000000002" y1="998.08167" y2="998.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.0000000000002" x2="183.0000000000002" y1="970.16327" y2="998.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.0000000000002" x2="273.0000000000002" y1="970.16327" y2="998.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.0000000000001" x2="363.0000000000001" y1="970.16327" y2="970.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.0000000000001" x2="363.0000000000001" y1="998.08167" y2="998.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.0000000000001" x2="273.0000000000001" y1="970.16327" y2="998.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.0000000000001" x2="363.0000000000001" y1="970.16327" y2="998.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.000000000000114" x2="93.00000000000011" y1="998.0816" y2="998.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.000000000000114" x2="93.00000000000011" y1="1026" y2="1026"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.000000000000114" x2="3.000000000000114" y1="998.0816" y2="1026"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.00000000000011" x2="93.00000000000011" y1="998.0816" y2="1026"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.00000000000011" x2="183.0000000000001" y1="998.0816" y2="998.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.00000000000011" x2="183.0000000000001" y1="1026" y2="1026"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.00000000000011" x2="93.00000000000011" y1="998.0816" y2="1026"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="998.0816" y2="1026"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.0000000000002" x2="273.0000000000002" y1="998.0816" y2="998.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.0000000000002" x2="273.0000000000002" y1="1026" y2="1026"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.0000000000002" x2="183.0000000000002" y1="998.0816" y2="1026"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.0000000000002" x2="273.0000000000002" y1="998.0816" y2="1026"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.0000000000001" x2="363.0000000000001" y1="998.0816" y2="998.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.0000000000001" x2="363.0000000000001" y1="1026" y2="1026"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.0000000000001" x2="273.0000000000001" y1="998.0816" y2="1026"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.0000000000001" x2="363.0000000000001" y1="998.0816" y2="1026"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48,951) scale(1,1) translate(0,0)" writing-mode="lr" x="48" xml:space="preserve" y="957" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,45,985) scale(1,1) translate(0,0)" writing-mode="lr" x="45" xml:space="preserve" y="991" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227,985) scale(1,1) translate(0,0)" writing-mode="lr" x="227" xml:space="preserve" y="991" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44,1013) scale(1,1) translate(0,0)" writing-mode="lr" x="44" xml:space="preserve" y="1019" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226,1013) scale(1,1) translate(0,0)" writing-mode="lr" x="226" xml:space="preserve" y="1019" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,68.5,645.5) scale(1,1) translate(0,-2.78222e-13)" writing-mode="lr" x="68.50000000000011" xml:space="preserve" y="649.9999999999999" zvalue="19">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,201.399,312.841) scale(1,1) translate(0,0)" writing-mode="lr" x="201.4" xml:space="preserve" y="317.34" zvalue="20">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,306.399,312.841) scale(1,1) translate(0,0)" writing-mode="lr" x="306.4" xml:space="preserve" y="317.34" zvalue="21">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,42,172) scale(1,1) translate(0,0)" writing-mode="lr" x="42" xml:space="preserve" y="177.5" zvalue="31">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,222,172) scale(1,1) translate(0,0)" writing-mode="lr" x="222" xml:space="preserve" y="177.5" zvalue="32">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49.6875,196.25) scale(1,1) translate(0,0)" writing-mode="lr" x="49.69" xml:space="preserve" y="200.75" zvalue="33">35kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,229.125,196.25) scale(1,1) translate(0,0)" writing-mode="lr" x="229.13" xml:space="preserve" y="200.75" zvalue="34">6.3kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49.1875,244) scale(1,1) translate(0,0)" writing-mode="lr" x="49.19" xml:space="preserve" y="248.5" zvalue="35">35kV#1变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,478.667,668.667) scale(1,1) translate(0,0)" writing-mode="lr" x="478.67" xml:space="preserve" y="673.17" zvalue="37">6.3kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,489.5,308.667) scale(1,1) translate(0,0)" writing-mode="lr" x="489.5" xml:space="preserve" y="313.17" zvalue="38">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,731.278,789.08) scale(1,1) translate(0,0)" writing-mode="lr" x="731.28" xml:space="preserve" y="793.58" zvalue="41">601</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" x="703.8046875" xml:space="preserve" y="966.6649740134186" zvalue="44">#1发电机                        </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="703.8046875" xml:space="preserve" y="982.6649740134186" zvalue="44">3200KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1174.61,792.746) scale(1,1) translate(0,0)" writing-mode="lr" x="1174.61" xml:space="preserve" y="797.25" zvalue="52">602</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" x="1147.1171875" xml:space="preserve" y="970.3212240134186" zvalue="55">#2发电机                      </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1147.1171875" xml:space="preserve" y="986.3212240134186" zvalue="55">3200KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1434.5,730.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1434.5" xml:space="preserve" y="735" zvalue="61">6121</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" x="802.4609375" xml:space="preserve" y="499.1022727272727" zvalue="65">#1主变              </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="802.4609375" xml:space="preserve" y="515.1022727272727" zvalue="65">8000KVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,719.387,601.043) scale(1,1) translate(0,0)" writing-mode="lr" x="719.39" xml:space="preserve" y="605.54" zvalue="67">611</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,717.636,380.121) scale(1,1) translate(0,0)" writing-mode="lr" x="717.64" xml:space="preserve" y="384.62" zvalue="75">3011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,675.389,454.457) scale(1,1) translate(0,0)" writing-mode="lr" x="675.39" xml:space="preserve" y="458.96" zvalue="78">3014</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1370.77,621) scale(1,1) translate(0,0)" writing-mode="lr" x="1370.77" xml:space="preserve" y="625.5" zvalue="84">6105</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1393.03,529.962) scale(1,1) translate(0,0)" writing-mode="lr" x="1393.03" xml:space="preserve" y="534.46" zvalue="87">6.3kV母线PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1136.03,490.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1136.03" xml:space="preserve" y="495" zvalue="90">35kV母线PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1104.5,370.507) scale(1,1) translate(0,0)" writing-mode="lr" x="1104.5" xml:space="preserve" y="375.01" zvalue="92">3105</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1200.39,360) scale(1,1) translate(0,0)" writing-mode="lr" x="1200.39" xml:space="preserve" y="364.5" zvalue="96">3104</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1205.39,433) scale(1,1) translate(-1.05772e-12,0)" writing-mode="lr" x="1205.39" xml:space="preserve" y="437.5" zvalue="98">3106</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,880.096,263.507) scale(1,1) translate(0,0)" writing-mode="lr" x="880.1" xml:space="preserve" y="268.01" zvalue="102">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="99" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,917.813,197.5) scale(1,1) translate(0,0)" writing-mode="lr" x="917.8099999999999" xml:space="preserve" y="202" zvalue="105">311</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="101" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,878.5,128.507) scale(1,1) translate(0,0)" writing-mode="lr" x="878.5" xml:space="preserve" y="133.01" zvalue="108">2</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,898,46) scale(1,1) translate(0,0)" writing-mode="lr" x="898" xml:space="preserve" y="50.5" zvalue="110">35kV西掌线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="104" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,804.389,248.389) scale(1,1) translate(0,0)" writing-mode="lr" x="804.39" xml:space="preserve" y="252.89" zvalue="113">14</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="103" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,806.389,184.389) scale(1,1) translate(0,0)" writing-mode="lr" x="806.39" xml:space="preserve" y="188.89" zvalue="116">16</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="102" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,809.389,116.389) scale(1,1) translate(0,0)" writing-mode="lr" x="809.39" xml:space="preserve" y="120.89" zvalue="119">18</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="109" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1546.86,586.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1546.86" xml:space="preserve" y="591.13" zvalue="123">#2站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="117" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1679,432) scale(1,1) translate(0,0)" writing-mode="lr" x="1679" xml:space="preserve" y="436.5" zvalue="136">至大坝</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,222,955) scale(1,1) translate(0,0)" writing-mode="lr" x="222" xml:space="preserve" y="961" zvalue="138">NanZhangHe-01-2014</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,140,985) scale(1,1) translate(0,0)" writing-mode="lr" x="140" xml:space="preserve" y="991" zvalue="140">杨立超</text>
 </g>
 <g id="ButtonClass">
  <g href="35kV南掌河二级电站.svg"><rect fill-opacity="0" height="24" width="72.88" x="58" y="298" zvalue="145"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="35">
   <path class="v6300" d="M 513.33 676.33 L 1703.33 676.33" stroke-width="6" zvalue="36"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674244362244" ObjectName="6.3kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674244362244"/></metadata>
  <path d="M 513.33 676.33 L 1703.33 676.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="36">
   <path class="kv35" d="M 525 314.67 L 1698.33 314.67" stroke-width="6" zvalue="37"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674244427780" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674244427780"/></metadata>
  <path d="M 525 314.67 L 1698.33 314.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="341">
   <use class="v6300" height="20" transform="rotate(0,704.264,790.16) scale(2.16108,2.16108) translate(-372.574,-412.918)" width="10" x="693.4589082118401" xlink:href="#Breaker:手车开关_0" y="768.5492600786007" zvalue="40"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924513759237" ObjectName="#1发电机601断路器"/>
   <cge:TPSR_Ref TObjectID="6473924513759237"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,704.264,790.16) scale(2.16108,2.16108) translate(-372.574,-412.918)" width="10" x="693.4589082118401" y="768.5492600786007"/></g>
  <g id="51">
   <use class="v6300" height="20" transform="rotate(0,1147.6,793.827) scale(2.16108,2.16108) translate(-610.764,-414.888)" width="10" x="1136.792241545174" xlink:href="#Breaker:手车开关_0" y="772.2159267452673" zvalue="51"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924513824773" ObjectName="#2发电机602断路器"/>
   <cge:TPSR_Ref TObjectID="6473924513824773"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1147.6,793.827) scale(2.16108,2.16108) translate(-610.764,-414.888)" width="10" x="1136.792241545174" y="772.2159267452673"/></g>
  <g id="64">
   <use class="v6300" height="20" transform="rotate(0,750.673,601.134) scale(2.16108,2.16108) translate(-397.508,-311.36)" width="10" x="739.8673046251995" xlink:href="#Breaker:手车开关_0" y="579.5227272727273" zvalue="66"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924513890309" ObjectName="#1主变6.3kV侧611断路器"/>
   <cge:TPSR_Ref TObjectID="6473924513890309"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,750.673,601.134) scale(2.16108,2.16108) translate(-397.508,-311.36)" width="10" x="739.8673046251995" y="579.5227272727273"/></g>
  <g id="91">
   <use class="kv35" height="20" transform="rotate(0,898.888,198.5) scale(1.485,1.35) translate(-291.151,-47.963)" width="10" x="891.4632851823077" xlink:href="#Breaker:开关_0" y="185" zvalue="104"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924513955845" ObjectName="35kV西掌线311断路器"/>
   <cge:TPSR_Ref TObjectID="6473924513955845"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,898.888,198.5) scale(1.485,1.35) translate(-291.151,-47.963)" width="10" x="891.4632851823077" y="185"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="44">
   <path class="v6300" d="M 703.01 896.43 L 703.01 809.61" stroke-width="1" zvalue="42"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="850@0" LinkObjectIDznd="341@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 703.01 896.43 L 703.01 809.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="v6300" d="M 704.26 770.17 L 704.26 676.33" stroke-width="1" zvalue="49"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="341@0" LinkObjectIDznd="35@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 704.26 770.17 L 704.26 676.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="v6300" d="M 1146.35 900.1 L 1146.35 813.28" stroke-width="1" zvalue="53"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@0" LinkObjectIDznd="51@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1146.35 900.1 L 1146.35 813.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="v6300" d="M 1147.6 773.84 L 1147.6 676.33" stroke-width="1" zvalue="56"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="51@0" LinkObjectIDznd="35@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1147.6 773.84 L 1147.6 676.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="v6300" d="M 1476.39 720.09 L 1476.39 676.33" stroke-width="1" zvalue="61"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="42@1" LinkObjectIDznd="35@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1476.39 720.09 L 1476.39 676.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="53">
   <path class="v6300" d="M 1476.49 751.96 L 1477.91 796.02" stroke-width="1" zvalue="62"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="42@0" LinkObjectIDznd="205@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1476.49 751.96 L 1477.91 796.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="v6300" d="M 750.67 581.14 L 750.67 538.91" stroke-width="1" zvalue="68"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="64@0" LinkObjectIDznd="88@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 750.67 581.14 L 750.67 538.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="v6300" d="M 750.67 620.58 L 750.67 676.33" stroke-width="1" zvalue="69"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="64@1" LinkObjectIDznd="35@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 750.67 620.58 L 750.67 676.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv35" d="M 688.17 421.93 L 748.29 421.93" stroke-width="1" zvalue="79"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="98@0" LinkObjectIDznd="65" MaxPinNum="2"/>
   </metadata>
  <path d="M 688.17 421.93 L 748.29 421.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="kv35" d="M 748.29 402.31 L 748.29 466.64" stroke-width="1" zvalue="80"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="94@1" LinkObjectIDznd="88@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 748.29 402.31 L 748.29 466.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv35" d="M 748.35 366.23 L 748.35 314.67" stroke-width="1" zvalue="81"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="94@0" LinkObjectIDznd="36@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 748.35 366.23 L 748.35 314.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="v6300" d="M 1396.39 639.91 L 1396.39 676.33" stroke-width="1" zvalue="84"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@1" LinkObjectIDznd="35@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1396.39 639.91 L 1396.39 676.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="55">
   <path class="v6300" d="M 1396.01 578.1 L 1396.49 608.04" stroke-width="1" zvalue="87"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="71@0" LinkObjectIDznd="67@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1396.01 578.1 L 1396.49 608.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="kv35" d="M 1135.89 353.61 L 1135.89 314.67" stroke-width="1" zvalue="92"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@0" LinkObjectIDznd="36@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1135.89 353.61 L 1135.89 314.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="kv35" d="M 1135.84 389.7 L 1137.01 436.37" stroke-width="1" zvalue="93"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@1" LinkObjectIDznd="61@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1135.84 389.7 L 1137.01 436.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="kv35" d="M 1186.6 343.68 L 1135.89 343.68" stroke-width="1" zvalue="98"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="75@0" LinkObjectIDznd="70" MaxPinNum="2"/>
   </metadata>
  <path d="M 1186.6 343.68 L 1135.89 343.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv35" d="M 1191.6 417.68 L 1136.54 417.68" stroke-width="1" zvalue="99"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="76@0" LinkObjectIDznd="72" MaxPinNum="2"/>
   </metadata>
  <path d="M 1191.6 417.68 L 1136.54 417.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="82">
   <path class="kv35" d="M 898.94 282.7 L 898.94 314.67" stroke-width="1" zvalue="102"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="81@1" LinkObjectIDznd="36@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 898.94 282.7 L 898.94 314.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="kv35" d="M 898.99 211.39 L 898.99 246.61" stroke-width="1" zvalue="105"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="91@1" LinkObjectIDznd="81@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 898.99 211.39 L 898.99 246.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="kv35" d="M 898.84 147.7 L 898.84 185.58" stroke-width="1" zvalue="108"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="84@1" LinkObjectIDznd="91@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 898.84 147.7 L 898.84 185.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="kv35" d="M 898 74.42 L 898.89 111.61" stroke-width="1" zvalue="110"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="86@0" LinkObjectIDznd="84@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 898 74.42 L 898.89 111.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="kv35" d="M 818.17 231.68 L 898.99 231.68" stroke-width="1" zvalue="113"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="90@0" LinkObjectIDznd="83" MaxPinNum="2"/>
   </metadata>
  <path d="M 818.17 231.68 L 898.99 231.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="kv35" d="M 820.17 165.68 L 898.84 165.68" stroke-width="1" zvalue="116"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="93@0" LinkObjectIDznd="85" MaxPinNum="2"/>
   </metadata>
  <path d="M 820.17 165.68 L 898.84 165.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="105">
   <path class="kv35" d="M 823.17 98.68 L 898.58 98.68" stroke-width="1" zvalue="120"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="96@0" LinkObjectIDznd="89" MaxPinNum="2"/>
   </metadata>
  <path d="M 823.17 98.68 L 898.58 98.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="kv35" d="M 1547.08 352.75 L 1547.08 314.67" stroke-width="1" zvalue="126"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@0" LinkObjectIDznd="36@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1547.08 352.75 L 1547.08 314.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="111">
   <path class="kv35" d="M 1546.92 378.25 L 1549.47 398.41" stroke-width="1" zvalue="127"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@1" LinkObjectIDznd="107@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1546.92 378.25 L 1549.47 398.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="v6300" d="M 1549.44 464.75 L 1549.44 443.13" stroke-width="1" zvalue="130"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@0" LinkObjectIDznd="107@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1549.44 464.75 L 1549.44 443.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="114">
   <path class="v6300" d="M 1549.27 490.25 L 1549.27 509.02" stroke-width="1" zvalue="131"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@1" LinkObjectIDznd="106@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1549.27 490.25 L 1549.27 509.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="115">
   <path class="v6300" d="M 1616.25 456.92 L 1549.44 456.92" stroke-width="1" zvalue="134"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="97@1" LinkObjectIDznd="113" MaxPinNum="2"/>
   </metadata>
  <path d="M 1616.25 456.92 L 1549.44 456.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="118">
   <path class="v6300" d="M 1666.35 456 L 1641.75 456" stroke-width="1" zvalue="136"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="116@0" LinkObjectIDznd="97@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1666.35 456 L 1641.75 456" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="v6300" height="30" transform="rotate(0,703.014,923.85) scale(1.85899,1.85899) translate(-311.959,-414.002)" width="30" x="675.1294777070149" xlink:href="#Generator:发电机_0" y="895.9653075999175" zvalue="43"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449810661382" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449810661382"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,703.014,923.85) scale(1.85899,1.85899) translate(-311.959,-414.002)" width="30" x="675.1294777070149" y="895.9653075999175"/></g>
  <g id="49">
   <use class="v6300" height="30" transform="rotate(0,1146.35,927.517) scale(1.85899,1.85899) translate(-516.812,-415.696)" width="30" x="1118.462811040348" xlink:href="#Generator:发电机_0" y="899.6319742665843" zvalue="54"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449810726918" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449810726918"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1146.35,927.517) scale(1.85899,1.85899) translate(-516.812,-415.696)" width="30" x="1118.462811040348" y="899.6319742665843"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="205">
   <use class="v6300" height="30" transform="rotate(0,1477.75,820.625) scale(1.69643,1.70833) translate(-596.905,-329.634)" width="28" x="1454" xlink:href="#EnergyConsumer:站用变DY接地_0" y="795" zvalue="58"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449810792454" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1477.75,820.625) scale(1.69643,1.70833) translate(-596.905,-329.634)" width="28" x="1454" y="795"/></g>
  <g id="106">
   <use class="v6300" height="30" transform="rotate(0,1549.11,533.625) scale(1.69643,1.70833) translate(-626.202,-210.634)" width="28" x="1525.363841670411" xlink:href="#EnergyConsumer:站用变DY接地_0" y="508" zvalue="122"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449812234246" ObjectName="#2站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1549.11,533.625) scale(1.69643,1.70833) translate(-626.202,-210.634)" width="28" x="1525.363841670411" y="508"/></g>
  <g id="116">
   <use class="v6300" height="30" transform="rotate(90,1683,456) scale(1.25,1.23333) translate(-335.1,-82.7703)" width="12" x="1675.5" xlink:href="#EnergyConsumer:负荷_0" y="437.5" zvalue="135"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449812496390" ObjectName="至大坝"/>
   <cge:TPSR_Ref TObjectID="6192449812496390"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1683,456) scale(1.25,1.23333) translate(-335.1,-82.7703)" width="12" x="1675.5" y="437.5"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="42">
   <use class="v6300" height="26" transform="rotate(0,1476.38,736) scale(1.23077,1.23077) translate(-275.438,-135)" width="12" x="1469" xlink:href="#Disconnector:单手车刀闸1212_0" y="720" zvalue="60"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449810857990" ObjectName="#1站用变6121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449810857990"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1476.38,736) scale(1.23077,1.23077) translate(-275.438,-135)" width="12" x="1469" y="720"/></g>
  <g id="94">
   <use class="kv35" height="30" transform="rotate(0,748.173,384.121) scale(1.9625,1.2338) translate(-359.72,-69.2814)" width="15" x="733.4545454545455" xlink:href="#Disconnector:刀闸_0" y="365.6136363636363" zvalue="73"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449811054598" ObjectName="#1主变35kV侧3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449811054598"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,748.173,384.121) scale(1.9625,1.2338) translate(-359.72,-69.2814)" width="15" x="733.4545454545455" y="365.6136363636363"/></g>
  <g id="67">
   <use class="v6300" height="26" transform="rotate(0,1396.38,624) scale(1.23077,-1.23077) translate(-260.438,-1128)" width="12" x="1389" xlink:href="#Disconnector:单手车刀闸1212_0" y="608" zvalue="83"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449811120134" ObjectName="6.3kV母线PT6105隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449811120134"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1396.38,624) scale(1.23077,-1.23077) translate(-260.438,-1128)" width="12" x="1389" y="608"/></g>
  <g id="69">
   <use class="kv35" height="30" transform="rotate(0,1135.72,371.507) scale(1.9625,1.2338) translate(-549.79,-66.8912)" width="15" x="1121" xlink:href="#Disconnector:刀闸_0" y="353.0000000000001" zvalue="91"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449811316742" ObjectName="35kV母线PT3105隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449811316742"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1135.72,371.507) scale(1.9625,1.2338) translate(-549.79,-66.8912)" width="15" x="1121" y="353.0000000000001"/></g>
  <g id="81">
   <use class="kv35" height="30" transform="rotate(0,898.815,264.507) scale(1.9625,1.2338) translate(-433.601,-46.6154)" width="15" x="884.0962727199934" xlink:href="#Disconnector:刀闸_0" y="246.0000000000001" zvalue="101"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449811644422" ObjectName="35kV西掌线3111隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449811644422"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,898.815,264.507) scale(1.9625,1.2338) translate(-433.601,-46.6154)" width="15" x="884.0962727199934" y="246.0000000000001"/></g>
  <g id="84">
   <use class="kv35" height="30" transform="rotate(0,898.719,129.507) scale(1.9625,1.2338) translate(-433.554,-21.0338)" width="15" x="884" xlink:href="#Disconnector:刀闸_0" y="111.0000000000001" zvalue="107"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449811709958" ObjectName="35kV西掌线3112隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449811709958"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,898.719,129.507) scale(1.9625,1.2338) translate(-433.554,-21.0338)" width="15" x="884" y="111.0000000000001"/></g>
  <g id="108">
   <use class="kv35" height="30" transform="rotate(0,1547,366) scale(1,1) translate(0,0)" width="15" x="1539.5" xlink:href="#Disconnector:令克_0" y="351" zvalue="125"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449812299782" ObjectName="#2站用变令克1"/>
   <cge:TPSR_Ref TObjectID="6192449812299782"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1547,366) scale(1,1) translate(0,0)" width="15" x="1539.5" y="351"/></g>
  <g id="112">
   <use class="v6300" height="30" transform="rotate(0,1549.35,478) scale(1,1) translate(0,0)" width="15" x="1541.85303030303" xlink:href="#Disconnector:令克_0" y="463" zvalue="129"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449812365318" ObjectName="#2站用变令克3"/>
   <cge:TPSR_Ref TObjectID="6192449812365318"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1549.35,478) scale(1,1) translate(0,0)" width="15" x="1541.85303030303" y="463"/></g>
  <g id="97">
   <use class="v6300" height="30" transform="rotate(90,1628.5,457) scale(1,1) translate(0,0)" width="15" x="1621" xlink:href="#Disconnector:令克_0" y="442" zvalue="133"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449812430854" ObjectName="#2站用变令克2"/>
   <cge:TPSR_Ref TObjectID="6192449812430854"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1628.5,457) scale(1,1) translate(0,0)" width="15" x="1621" y="442"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="88">
   <g id="880">
    <use class="kv35" height="50" transform="rotate(0,749.673,502.705) scale(1.46909,1.46909) translate(-232.339,-148.79)" width="30" x="727.64" xlink:href="#PowerTransformer2:Y-D_0" y="465.98" zvalue="64"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874438475780" ObjectName="35"/>
    </metadata>
   </g>
   <g id="881">
    <use class="v6300" height="50" transform="rotate(0,749.673,502.705) scale(1.46909,1.46909) translate(-232.339,-148.79)" width="30" x="727.64" xlink:href="#PowerTransformer2:Y-D_1" y="465.98" zvalue="64"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874438541316" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399451475971" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399451475971"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,749.673,502.705) scale(1.46909,1.46909) translate(-232.339,-148.79)" width="30" x="727.64" y="465.98"/></g>
  <g id="107">
   <g id="1070">
    <use class="kv35" height="50" transform="rotate(0,1549.44,420.727) scale(0.909091,0.909091) translate(153.58,39.8)" width="30" x="1535.8" xlink:href="#PowerTransformer2:Y-D_0" y="398" zvalue="124"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874438606852" ObjectName="35"/>
    </metadata>
   </g>
   <g id="1071">
    <use class="v6300" height="50" transform="rotate(0,1549.44,420.727) scale(0.909091,0.909091) translate(153.58,39.8)" width="30" x="1535.8" xlink:href="#PowerTransformer2:Y-D_1" y="398" zvalue="124"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874438672388" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399451541507" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399451541507"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1549.44,420.727) scale(0.909091,0.909091) translate(153.58,39.8)" width="30" x="1535.8" y="398"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="98">
   <use class="kv35" height="30" transform="rotate(90,675.389,421.944) scale(1.11574,0.892592) translate(-69.3665,49.1624)" width="12" x="668.6944427490234" xlink:href="#GroundDisconnector:地刀12_0" y="408.5555572509765" zvalue="77"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449810989062" ObjectName="#1主变35kV侧3014接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449810989062"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,675.389,421.944) scale(1.11574,0.892592) translate(-69.3665,49.1624)" width="12" x="668.6944427490234" y="408.5555572509765"/></g>
  <g id="75">
   <use class="kv35" height="30" transform="rotate(270,1199.39,343.694) scale(-1.11574,0.892592) translate(-2273.67,39.7464)" width="12" x="1192.694442749023" xlink:href="#GroundDisconnector:地刀12_0" y="330.3055572509766" zvalue="95"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449811447814" ObjectName="35kV母线PT3104接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449811447814"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1199.39,343.694) scale(-1.11574,0.892592) translate(-2273.67,39.7464)" width="12" x="1192.694442749023" y="330.3055572509766"/></g>
  <g id="76">
   <use class="kv35" height="30" transform="rotate(270,1204.39,417.694) scale(-1.11574,0.892592) translate(-2283.15,48.651)" width="12" x="1197.694442749023" xlink:href="#GroundDisconnector:地刀12_0" y="404.3055572509765" zvalue="97"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449811578885" ObjectName="35kV母线PT3106接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449811578885"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1204.39,417.694) scale(-1.11574,0.892592) translate(-2283.15,48.651)" width="12" x="1197.694442749023" y="404.3055572509765"/></g>
  <g id="90">
   <use class="kv35" height="30" transform="rotate(90,805.389,231.694) scale(1.11574,0.892592) translate(-82.8519,26.2692)" width="12" x="798.6944427490234" xlink:href="#GroundDisconnector:地刀12_0" y="218.3055572509765" zvalue="112"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449811906565" ObjectName="35kV西掌线31114接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449811906565"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,805.389,231.694) scale(1.11574,0.892592) translate(-82.8519,26.2692)" width="12" x="798.6944427490234" y="218.3055572509765"/></g>
  <g id="93">
   <use class="kv35" height="30" transform="rotate(90,807.389,165.694) scale(1.11574,0.892592) translate(-83.0594,18.3273)" width="12" x="800.6944427490234" xlink:href="#GroundDisconnector:地刀12_0" y="152.3055572509765" zvalue="115"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449812037638" ObjectName="35kV西掌线31116接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449812037638"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,807.389,165.694) scale(1.11574,0.892592) translate(-83.0594,18.3273)" width="12" x="800.6944427490234" y="152.3055572509765"/></g>
  <g id="96">
   <use class="kv35" height="30" transform="rotate(90,810.389,98.6944) scale(1.11574,0.892592) translate(-83.3706,10.265)" width="12" x="803.6944427490234" xlink:href="#GroundDisconnector:地刀12_0" y="85.30555725097651" zvalue="118"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449812168710" ObjectName="35kV西掌线31118接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449812168710"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,810.389,98.6944) scale(1.11574,0.892592) translate(-83.3706,10.265)" width="12" x="803.6944427490234" y="85.30555725097651"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="71">
   <use class="v6300" height="18" transform="rotate(0,1394.03,560.231) scale(-2.13675,-2.13675) translate(-2037.9,-812.188)" width="15" x="1378" xlink:href="#Accessory:PT8_0" y="541" zvalue="86"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449811185670" ObjectName="6.3kV母线PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1394.03,560.231) scale(-2.13675,-2.13675) translate(-2037.9,-812.188)" width="15" x="1378" y="541"/></g>
  <g id="61">
   <use class="kv35" height="18" transform="rotate(0,1135.03,454.231) scale(-2.13675,2.13675) translate(-1657.69,-231.42)" width="15" x="1119" xlink:href="#Accessory:PT8_0" y="435" zvalue="89"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449811251206" ObjectName="35kV母线PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1135.03,454.231) scale(-2.13675,2.13675) translate(-1657.69,-231.42)" width="15" x="1119" y="435"/></g>
 </g>
 <g id="StateClass">
  <g id="897">
   <use height="30" transform="rotate(0,335.25,313.983) scale(0.708333,0.665547) translate(133.669,152.767)" width="30" x="324.63" xlink:href="#State:红绿圆(方形)_0" y="304" zvalue="142"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374888271875" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,335.25,313.983) scale(0.708333,0.665547) translate(133.669,152.767)" width="30" x="324.63" y="304"/></g>
  <g id="160">
   <use height="30" transform="rotate(0,235.625,313.983) scale(0.708333,0.665547) translate(92.6471,152.767)" width="30" x="225" xlink:href="#State:红绿圆(方形)_0" y="304" zvalue="143"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,235.625,313.983) scale(0.708333,0.665547) translate(92.6471,152.767)" width="30" x="225" y="304"/></g>
  <g id="10">
   <use height="30" transform="rotate(0,314.812,123.464) scale(1.22222,1.03092) translate(-47.2386,-3.23939)" width="90" x="259.81" xlink:href="#State:全站检修_0" y="108" zvalue="151"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549587214338" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,314.812,123.464) scale(1.22222,1.03092) translate(-47.2386,-3.23939)" width="90" x="259.81" y="108"/></g>
 </g>
</svg>