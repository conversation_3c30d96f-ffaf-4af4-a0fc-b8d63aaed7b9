<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549592195074" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV北沟电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="64" id="1" preserveAspectRatio="xMidYMid slice" width="298" x="50.21" xlink:href="logo.png" y="47.57"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="102" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,199.214,79.5714) scale(1,1) translate(0,0)" writing-mode="lr" x="199.21" xml:space="preserve" y="83.06999999999999" zvalue="4"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,201.714,77.2618) scale(1,1) translate(0,0)" writing-mode="lr" x="201.71" xml:space="preserve" y="86.26000000000001" zvalue="5">10kV北沟电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="10" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,80.625,224) scale(1,1) translate(0,0)" width="72.88" x="44.19" y="212" zvalue="171"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,80.625,224) scale(1,1) translate(0,0)" writing-mode="lr" x="80.63" xml:space="preserve" y="228.5" zvalue="171">信号一览</text>
  <line fill="none" id="100" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="384.2142857142856" x2="384.2142857142856" y1="15.57142857142867" y2="1045.571428571429" zvalue="6"/>
  <line fill="none" id="98" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.214285714286234" x2="377.2142857142858" y1="151.441921185511" y2="151.441921185511" zvalue="8"/>
  <line fill="none" id="97" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.214285714286234" x2="377.2142857142858" y1="621.441921185511" y2="621.441921185511" zvalue="9"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="936.5714285714287" y2="936.5714285714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="975.7347285714286" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="10.21428571428555" y1="936.5714285714287" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="936.5714285714287" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="370.2142857142856" y1="936.5714285714287" y2="936.5714285714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="370.2142857142856" y1="975.7347285714286" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="936.5714285714287" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="370.2142857142856" x2="370.2142857142856" y1="936.5714285714287" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="975.7346985714287" y2="975.7346985714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="1003.653098571429" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="10.21428571428555" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="190.2142857142856" y1="975.7346985714287" y2="975.7346985714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="190.2142857142856" y1="1003.653098571429" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142856" x2="190.2142857142856" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="280.2142857142857" y1="975.7346985714287" y2="975.7346985714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="280.2142857142857" y1="1003.653098571429" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="190.2142857142857" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142857" x2="280.2142857142857" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="370.2142857142856" y1="975.7346985714287" y2="975.7346985714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="370.2142857142856" y1="1003.653098571429" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="280.2142857142856" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="370.2142857142856" x2="370.2142857142856" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="1003.653028571429" y2="1003.653028571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="1031.571428571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="10.21428571428555" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="190.2142857142856" y1="1003.653028571429" y2="1003.653028571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="190.2142857142856" y1="1031.571428571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142856" x2="190.2142857142856" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="280.2142857142857" y1="1003.653028571429" y2="1003.653028571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="280.2142857142857" y1="1031.571428571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="190.2142857142857" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142857" x2="280.2142857142857" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="370.2142857142856" y1="1003.653028571429" y2="1003.653028571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="370.2142857142856" y1="1031.571428571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="280.2142857142856" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="370.2142857142856" x2="370.2142857142856" y1="1003.653028571429" y2="1031.571428571429"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.2143,956.571) scale(1,1) translate(0,0)" writing-mode="lr" x="55.21" xml:space="preserve" y="962.5700000000001" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.2143,990.571) scale(1,1) translate(0,0)" writing-mode="lr" x="52.21" xml:space="preserve" y="996.5700000000001" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.214,990.571) scale(1,1) translate(0,0)" writing-mode="lr" x="234.21" xml:space="preserve" y="996.5700000000001" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="91" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51.2143,1018.57) scale(1,1) translate(0,0)" writing-mode="lr" x="51.21" xml:space="preserve" y="1024.57" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.214,1018.57) scale(1,1) translate(0,0)" writing-mode="lr" x="233.21" xml:space="preserve" y="1024.57" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,75.7143,651.071) scale(1,1) translate(0,2.10522e-13)" writing-mode="lr" x="75.71428571428555" xml:space="preserve" y="655.5714285714286" zvalue="19">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235.268,958.571) scale(1,1) translate(0,0)" writing-mode="lr" x="235.27" xml:space="preserve" y="964.5700000000001" zvalue="27">BeiGou-01-2014</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,802.77,375.258) scale(1,1) translate(-1.74924e-13,0)" writing-mode="lr" x="802.77" xml:space="preserve" y="379.76" zvalue="33">051</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,688.537,483.352) scale(1,1) translate(0,0)" writing-mode="lr" x="688.54" xml:space="preserve" y="487.85" zvalue="35">#1主变1000kVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,772.061,879.99) scale(1,1) translate(0,2.86182e-13)" writing-mode="lr" x="772.0606713030318" xml:space="preserve" y="884.49045413687" zvalue="40">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,801.402,304.869) scale(1,1) translate(0,0)" writing-mode="lr" x="801.4" xml:space="preserve" y="309.37" zvalue="56">0516</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,768.001,134) scale(1,1) translate(0,0)" writing-mode="lr" x="768" xml:space="preserve" y="138.5" zvalue="124">10kV歌北线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="114" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,786.551,692.758) scale(1,1) translate(0,0)" writing-mode="lr" x="786.55" xml:space="preserve" y="697.26" zvalue="156">401</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="115" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,793.205,622.369) scale(1,1) translate(0,0)" writing-mode="lr" x="793.2" xml:space="preserve" y="626.87" zvalue="157">4011</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="285.75" y2="285.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="311.75" y2="311.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="7" y1="285.75" y2="311.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="285.75" y2="311.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="285.75" y2="285.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="311.75" y2="311.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="285.75" y2="311.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369" x2="369" y1="285.75" y2="311.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="311.75" y2="311.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="336" y2="336"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="7" y1="311.75" y2="336"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="311.75" y2="336"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="311.75" y2="311.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="336" y2="336"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="311.75" y2="336"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369" x2="369" y1="311.75" y2="336"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="336" y2="336"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="358.75" y2="358.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="7" y1="336" y2="358.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="336" y2="358.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="336" y2="336"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="358.75" y2="358.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="336" y2="358.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369" x2="369" y1="336" y2="358.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="358.75" y2="358.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="381.5" y2="381.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="7" y1="358.75" y2="381.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="358.75" y2="381.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="358.75" y2="358.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="381.5" y2="381.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="358.75" y2="381.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369" x2="369" y1="358.75" y2="381.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="381.5" y2="381.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="404.25" y2="404.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="7" y1="381.5" y2="404.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="381.5" y2="404.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="381.5" y2="381.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="404.25" y2="404.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="381.5" y2="404.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369" x2="369" y1="381.5" y2="404.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="404.25" y2="404.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="427" y2="427"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="7" y1="404.25" y2="427"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="404.25" y2="427"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="404.25" y2="404.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="427" y2="427"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="404.25" y2="427"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369" x2="369" y1="404.25" y2="427"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="427" y2="427"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="449.75" y2="449.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="7" y1="427" y2="449.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="427" y2="449.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="427" y2="427"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="449.75" y2="449.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="427" y2="449.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369" x2="369" y1="427" y2="449.75"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,196.399,224.591) scale(1,1) translate(0,0)" writing-mode="lr" x="196.4" xml:space="preserve" y="229.09" zvalue="162">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,301.399,224.591) scale(1,1) translate(0,0)" writing-mode="lr" x="301.4" xml:space="preserve" y="229.09" zvalue="163">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,53.5,298.75) scale(1,1) translate(0,0)" writing-mode="lr" x="11" xml:space="preserve" y="303.25" zvalue="164">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,234,298.75) scale(1,1) translate(0,0)" writing-mode="lr" x="191.5" xml:space="preserve" y="303.25" zvalue="165">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,52.5,324.75) scale(1,1) translate(0,0)" writing-mode="lr" x="10" xml:space="preserve" y="329.25" zvalue="172">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,233,324.75) scale(1,1) translate(0,0)" writing-mode="lr" x="190.5" xml:space="preserve" y="329.25" zvalue="173">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53.6875,418) scale(1,1) translate(0,0)" writing-mode="lr" x="53.69" xml:space="preserve" y="422.5" zvalue="176">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,221.688,417) scale(1,1) translate(0,0)" writing-mode="lr" x="221.69" xml:space="preserve" y="421.5" zvalue="177">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53.6875,441) scale(1,1) translate(0,0)" writing-mode="lr" x="53.69" xml:space="preserve" y="445.5" zvalue="178">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,221.688,440) scale(1,1) translate(0,0)" writing-mode="lr" x="221.69" xml:space="preserve" y="444.5" zvalue="179">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,52.5,347.75) scale(1,1) translate(0,0)" writing-mode="lr" x="10" xml:space="preserve" y="352.25" zvalue="180">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,232.5,346.75) scale(1,1) translate(0,0)" writing-mode="lr" x="190" xml:space="preserve" y="351.25" zvalue="182">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,769.5,900) scale(1,1) translate(0,0)" writing-mode="lr" x="769.5" xml:space="preserve" y="904.5" zvalue="189">800kW</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="44.19" y="212" zvalue="171"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BreakerClass">
  <g id="418">
   <use class="kv10" height="20" transform="rotate(0,768.012,376.258) scale(1.22222,1.11111) translate(-138.527,-36.5147)" width="10" x="761.9008038365654" xlink:href="#Breaker:开关_0" y="365.1468253968253" zvalue="32"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924554915844" ObjectName="#1主变10kV侧051断路器"/>
   <cge:TPSR_Ref TObjectID="6473924554915844"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,768.012,376.258) scale(1.22222,1.11111) translate(-138.527,-36.5147)" width="10" x="761.9008038365654" y="365.1468253968253"/></g>
  <g id="108">
   <use class="v400" height="20" transform="rotate(0,767.19,693.758) scale(1.22222,1.11111) translate(-138.378,-68.2647)" width="10" x="761.078631472918" xlink:href="#Breaker:开关_0" y="682.6468253968253" zvalue="155"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924554981380" ObjectName="#1主变0.4kV侧401断路器"/>
   <cge:TPSR_Ref TObjectID="6473924554981380"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,767.19,693.758) scale(1.22222,1.11111) translate(-138.378,-68.2647)" width="10" x="761.078631472918" y="682.6468253968253"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="414">
   <g id="4140">
    <use class="kv10" height="60" transform="rotate(0,768.03,480.077) scale(1.6125,1.54462) translate(-279.482,-152.933)" width="40" x="735.78" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="433.74" zvalue="34"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874453483523" ObjectName="10"/>
    </metadata>
   </g>
   <g id="4141">
    <use class="v400" height="60" transform="rotate(0,768.03,480.077) scale(1.6125,1.54462) translate(-279.482,-152.933)" width="40" x="735.78" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="433.74" zvalue="34"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874453549059" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399458684931" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399458684931"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,768.03,480.077) scale(1.6125,1.54462) translate(-279.482,-152.933)" width="40" x="735.78" y="433.74"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="74">
   <path class="kv10" d="M 768.09 386.87 L 768.09 434.58" stroke-width="1" zvalue="36"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="418@1" LinkObjectIDznd="414@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 768.09 386.87 L 768.09 434.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="kv10" d="M 767.97 365.63 L 767.97 317.69" stroke-width="1" zvalue="55"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="418@0" LinkObjectIDznd="206@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 767.97 365.63 L 767.97 317.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv10" d="M 768 293.86 L 768 195.27" stroke-width="1" zvalue="123"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="206@1" LinkObjectIDznd="156@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 768 293.86 L 768 195.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="v400" d="M 767.18 611.36 L 767.18 525.76" stroke-width="1" zvalue="157"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109@1" LinkObjectIDznd="414@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 767.18 611.36 L 767.18 525.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="111">
   <path class="v400" d="M 767.15 635.19 L 767.15 683.13" stroke-width="1" zvalue="158"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109@0" LinkObjectIDznd="108@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 767.15 635.19 L 767.15 683.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="v400" d="M 767.27 704.37 L 767.27 795.25" stroke-width="1" zvalue="159"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@1" LinkObjectIDznd="212@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 767.27 704.37 L 767.27 795.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="212">
   <use class="v400" height="30" transform="rotate(0,767.271,822.672) scale(1.85899,1.85899) translate(-341.651,-367.25)" width="30" x="739.3863709131574" xlink:href="#Generator:发电机_0" y="794.7867361713461" zvalue="39"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450010218501" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192450010218501"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,767.271,822.672) scale(1.85899,1.85899) translate(-341.651,-367.25)" width="30" x="739.3863709131574" y="794.7867361713461"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="206">
   <use class="kv10" height="30" transform="rotate(0,768.069,305.869) scale(-1.11111,-0.814815) translate(-1458.5,-684.032)" width="15" x="759.7353709301144" xlink:href="#Disconnector:刀闸_0" y="293.6468390661572" zvalue="54"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450010152965" ObjectName="#1主变10kV侧0516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450010152965"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,768.069,305.869) scale(-1.11111,-0.814815) translate(-1458.5,-684.032)" width="15" x="759.7353709301144" y="293.6468390661572"/></g>
  <g id="109">
   <use class="v400" height="30" transform="rotate(0,767.247,623.369) scale(-1.11111,-0.814815) translate(-1456.94,-1391.19)" width="15" x="758.913198566467" xlink:href="#Disconnector:刀闸_0" y="611.1468390661572" zvalue="156"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450010349573" ObjectName="#1主变0.4kV侧4011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450010349573"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,767.247,623.369) scale(-1.11111,-0.814815) translate(-1456.94,-1391.19)" width="15" x="758.913198566467" y="611.1468390661572"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="156">
   <use class="kv10" height="30" transform="rotate(0,768.001,172.688) scale(2.32143,1.52083) translate(-432.545,-51.3271)" width="7" x="759.8757437922191" xlink:href="#ACLineSegment:线路_0" y="149.875" zvalue="122"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450010087429" ObjectName="10kV歌北线"/>
   <cge:TPSR_Ref TObjectID="6192450010087429_5066549592195074"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,768.001,172.688) scale(2.32143,1.52083) translate(-432.545,-51.3271)" width="7" x="759.8757437922191" y="149.875"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="273">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="273" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,153.611,298.917) scale(1,1) translate(0,0)" writing-mode="lr" x="153.77" xml:space="preserve" y="303.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126606864388" ObjectName="F"/>
   </metadata>
  </g>
  <g id="272">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="272" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,331.222,299.917) scale(1,1) translate(0,0)" writing-mode="lr" x="331.38" xml:space="preserve" y="304.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126606929924" ObjectName="F"/>
   </metadata>
  </g>
  <g id="260">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="260" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,153.611,323.917) scale(1,1) translate(0,0)" writing-mode="lr" x="153.77" xml:space="preserve" y="328.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126606733316" ObjectName="F"/>
   </metadata>
  </g>
  <g id="234">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="234" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,331.222,324.917) scale(1,1) translate(0,0)" writing-mode="lr" x="331.38" xml:space="preserve" y="329.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126606798852" ObjectName="F"/>
   </metadata>
  </g>
  <g id="209">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="209" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,153.611,347.917) scale(1,1) translate(0,0)" writing-mode="lr" x="153.77" xml:space="preserve" y="352.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126606733316" ObjectName="F"/>
   </metadata>
  </g>
  <g id="198">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="198" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,333.611,346.917) scale(1,1) translate(0,0)" writing-mode="lr" x="333.77" xml:space="preserve" y="351.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126606733316" ObjectName="F"/>
   </metadata>
  </g>
  <g id="287">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="287" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,153.611,414.917) scale(1,1) translate(0,0)" writing-mode="lr" x="153.77" xml:space="preserve" y="419.83" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="坝上水位"/>
   </metadata>
  </g>
  <g id="288">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="288" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,329.611,414.917) scale(1,1) translate(0,0)" writing-mode="lr" x="329.77" xml:space="preserve" y="419.83" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="雨量采集"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="271">
   <use height="30" transform="rotate(0,328.673,225.107) scale(0.708333,0.665547) translate(130.961,108.105)" width="30" x="318.05" xlink:href="#State:红绿圆(方形)_0" y="215.12" zvalue="170"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,328.673,225.107) scale(0.708333,0.665547) translate(130.961,108.105)" width="30" x="318.05" y="215.12"/></g>
  <g id="286">
   <use height="30" transform="rotate(0,233.048,225.107) scale(0.708333,0.665547) translate(91.5858,108.105)" width="30" x="222.42" xlink:href="#State:红绿圆(方形)_0" y="215.12" zvalue="184"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,233.048,225.107) scale(0.708333,0.665547) translate(91.5858,108.105)" width="30" x="222.42" y="215.12"/></g>
 </g>
</svg>