<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549585510402" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT12321_0" viewBox="0,0,30,29">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="28.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="2.916666666666664" y2="5.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.666666666666663" y2="7.583333333333329"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.666666666666664" y2="7.666666666666664"/>
   <ellipse cx="15.15" cy="13.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11" y2="13.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.5" y2="15.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.5" y2="15.41666666666666"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="PowerTransformer2:586_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="19.75" x2="10.08333333333333" y1="17.11381925541057" y2="17.11381925541057"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.98518194534241" x2="9.999999999999998" y1="9.386991192770131" y2="16.99800064973551"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00299079104462" x2="19.66666666666667" y1="9.301854138598824" y2="16.8218426122152"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:586_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="30.58333333333333" y2="36.5"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="21" y1="36.5" y2="40.5"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="10" y1="36.5" y2="40.5"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="Accessory:PT1111_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="33.75" xlink:href="#terminal" y="11.95"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="27.02377700368697" x2="19.18246971500333" y1="12.01581866675157" y2="12.01581866675157"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="30.4317302644139" x2="33.73333333333333" y1="12.01581866675157" y2="12.01581866675157"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="23.30947355115262" x2="23.30947355115262" y1="12.14512912951974" y2="19.34675167759026"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.21102695297582" x2="12.70307369224894" y1="12.09628971045682" y2="12.09628971045682"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="19.22373975336484" x2="19.22373975336484" y1="12.09628971045682" y2="12.09628971045682"/>
   <ellipse cx="23.29" cy="23.6" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="19.22373975336483" x2="19.22373975336483" y1="8.716505874834564" y2="15.55654458978453"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="30.36665011096792" x2="30.36665011096792" y1="12.09628971045682" y2="12.09628971045682"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="30.3666501109679" x2="30.3666501109679" y1="8.716505874834564" y2="15.55654458978453"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="16.12848687625285" x2="16.12848687625285" y1="8.716505874834558" y2="15.55654458978453"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="27.27139723385593" x2="27.27139723385593" y1="8.716505874834564" y2="15.55654458978453"/>
   <ellipse cx="25.79" cy="27.85" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="20.79" cy="27.85" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.96296296296296" x2="8.779897241477752" y1="12.08795620351516" y2="12.08795620351516"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="8.711988705963254" x2="8.711988705963254" y1="14.28440670580408" y2="10.00759086112023"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="7.461988705963254" x2="7.461988705963254" y1="13.53440670580408" y2="10.9242575277869"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.961988705963256" x2="5.961988705963256" y1="13.03440670580408" y2="11.59092419445357"/>
  </symbol>
  <symbol id="Accessory:机组PT2021_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变1节点_0" viewBox="0,0,26,38">
   <use terminal-index="0" type="0" x="9.116666666666667" xlink:href="#terminal" y="0.2500000000000071"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.45" x2="25.45" y1="30" y2="30"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.45" x2="24.45" y1="31" y2="31"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="9" y1="33" y2="38"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.45" x2="23.45" y1="32" y2="32"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23" x2="23" y1="24" y2="30"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19" x2="19" y1="24" y2="30"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="23" y1="24" y2="24"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="9" y1="2.25" y2="0.25"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19" x2="9" y1="30" y2="37"/>
   <ellipse cx="9.210000000000001" cy="10.97" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="9.201922857336841" y1="6.6" y2="10.70894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="5.216666666666669" y1="10.73394833233988" y2="13.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.22969569739165" x2="13.31666666666667" y1="10.68990500916851" y2="13.5"/>
   <ellipse cx="9.210000000000001" cy="24.47" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="9.201922857336841" y1="20.1" y2="24.20894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="5.216666666666669" y1="24.23394833233988" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.22969569739165" x2="13.31666666666667" y1="24.18990500916851" y2="27"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Disconnector:联体小车刀闸2_0" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="9.583333333333332" y2="1.050000000000001"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.083333333333333" x2="7.083333333333333" y1="26.41666666666667" y2="34.83333333333334"/>
   <rect fill-opacity="0" height="17" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,18) scale(1,1) translate(0,0)" width="8" x="3" y="9.5"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
  </symbol>
  <symbol id="Disconnector:联体小车刀闸2_1" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="35" y2="1.166666666666664"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <rect fill-opacity="0" height="17" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,18) scale(1,1) translate(0,0)" width="8" x="3" y="9.5"/>
  </symbol>
  <symbol id="Disconnector:联体小车刀闸2_2" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.083333333333333" x2="7.083333333333333" y1="9.999999999999995" y2="1.299999999999994"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="1.499999999999999" y1="26" y2="9.966666666666669"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.999999999999998" x2="12.41666666666667" y1="26.25" y2="10.05"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV护国河电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="64" id="1" preserveAspectRatio="xMidYMid slice" width="298" x="50.21" xlink:href="logo.png" y="47.57"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="102" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,199.214,79.5714) scale(1,1) translate(0,0)" writing-mode="lr" x="199.21" xml:space="preserve" y="83.06999999999999" zvalue="4"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,202.714,76.2618) scale(1,1) translate(0,0)" writing-mode="lr" x="202.71" xml:space="preserve" y="85.26000000000001" zvalue="5">35kV星云铝厂二级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="203" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" width="72.88" x="50.19" y="175.25" zvalue="361"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" writing-mode="lr" x="86.63" xml:space="preserve" y="191.75" zvalue="361">信号一览</text>
  <line fill="none" id="100" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="384.2142857142856" x2="384.2142857142856" y1="15.57142857142867" y2="1045.571428571429" zvalue="6"/>
  <line fill="none" id="98" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.214285714286234" x2="377.2142857142858" y1="151.441921185511" y2="151.441921185511" zvalue="8"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="390.25" y2="413"/>
  <line fill="none" id="268" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="494.8704926140824" y2="494.8704926140824" zvalue="335"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="1001.0816" y2="1029"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="266" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,954) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="960" zvalue="337">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="265" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54,988) scale(1,1) translate(0,0)" writing-mode="lr" x="54" xml:space="preserve" y="994" zvalue="338">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="264" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,988) scale(1,1) translate(0,0)" writing-mode="lr" x="236" xml:space="preserve" y="994" zvalue="339">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="263" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="1022" zvalue="340">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="262" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="235" xml:space="preserve" y="1022" zvalue="341">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="260" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5,568.5) scale(1,1) translate(0,0)" writing-mode="lr" x="77.5" xml:space="preserve" y="573" zvalue="343">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="259" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="202.4" xml:space="preserve" y="192.34" zvalue="344">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="258" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="307.4" xml:space="preserve" y="192.34" zvalue="345">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="254" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,147.054,988) scale(1,1) translate(0,0)" writing-mode="lr" x="147.05" xml:space="preserve" y="994" zvalue="347">李文杰</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="253" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,327.054,988) scale(1,1) translate(0,0)" writing-mode="lr" x="327.05" xml:space="preserve" y="994" zvalue="348">20210922</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="252" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,59.5,262) scale(1,1) translate(0,0)" writing-mode="lr" x="17" xml:space="preserve" y="266.5" zvalue="349">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="251" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240,262) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="266.5" zvalue="350">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="249" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,63.2361,336.361) scale(1,1) translate(0,0)" writing-mode="lr" x="63.24" xml:space="preserve" y="340.86" zvalue="352">6.3kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="202" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,288) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="292.5" zvalue="362">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="200" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239,288) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="292.5" zvalue="363">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="195" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,381.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="385.75" zvalue="366">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="193" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,380.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="384.75" zvalue="368">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="190" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,404.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="408.75" zvalue="370">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="187" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,403.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="407.75" zvalue="372">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="184" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,311) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="315.5" zvalue="374">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="182" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,238.5,310) scale(1,1) translate(0,0)" writing-mode="lr" x="196" xml:space="preserve" y="314.5" zvalue="376">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,551,614) scale(1,1) translate(0,0)" writing-mode="lr" x="551" xml:space="preserve" y="618.5" zvalue="383">6.3kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,755,898) scale(1,1) translate(0,0)" writing-mode="lr" x="755" xml:space="preserve" y="902.5" zvalue="419">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,755.5,923.25) scale(1,1) translate(0,0)" writing-mode="lr" x="755.5" xml:space="preserve" y="927.75" zvalue="420">2MW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,825.917,868.164) scale(1,1) translate(0,0)" writing-mode="lr" x="825.92" xml:space="preserve" y="872.66" zvalue="513">#1励磁PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,678.062,810.504) scale(1,1) translate(0,0)" writing-mode="lr" x="678.0599999999999" xml:space="preserve" y="815" zvalue="514">6911</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1141.71,460) scale(1,1) translate(0,0)" writing-mode="lr" x="1141.71" xml:space="preserve" y="464.5" zvalue="554">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1095.44,350) scale(1,1) translate(0,0)" writing-mode="lr" x="1095.44" xml:space="preserve" y="354.5" zvalue="557">051</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1072,177) scale(1,1) translate(0,0)" writing-mode="lr" x="1072" xml:space="preserve" y="181.5" zvalue="560">10kV国南线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1107.48,551.756) scale(1,1) translate(0,-1.20627e-13)" writing-mode="lr" x="1107.48" xml:space="preserve" y="556.26" zvalue="562">601</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="165" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1126.75,593) scale(1,1) translate(0,0)" writing-mode="lr" x="1126.75" xml:space="preserve" y="597.5" zvalue="565">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1084.16,293) scale(1,1) translate(0,0)" writing-mode="lr" x="1084.16" xml:space="preserve" y="297.5" zvalue="567">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="172" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,809.25,715) scale(1,1) translate(0,0)" writing-mode="lr" x="809.25" xml:space="preserve" y="719.5" zvalue="597">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="218" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1318.5,685.114) scale(1,1) translate(0,0)" writing-mode="lr" x="1318.5" xml:space="preserve" y="689.61" zvalue="629">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="220" stroke="rgb(255,255,255)" text-anchor="middle" x="1295.1484375" xml:space="preserve" y="808" zvalue="631">6.3kV母线电压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="220" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1295.1484375" xml:space="preserve" y="824" zvalue="631">互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1140.5,484.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1140.5" xml:space="preserve" y="488.75" zvalue="692">5MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1082.5,395) scale(1,1) translate(0,0)" writing-mode="lr" x="1082.5" xml:space="preserve" y="399.5" zvalue="695">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="111" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1083.19,598) scale(1,1) translate(0,0)" writing-mode="lr" x="1083.19" xml:space="preserve" y="602.5" zvalue="705">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="117" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,651,882.5) scale(1,1) translate(0,0)" writing-mode="lr" x="651" xml:space="preserve" y="887" zvalue="710">#1机组PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="121" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,764.437,672) scale(1,1) translate(0,0)" writing-mode="lr" x="764.4400000000001" xml:space="preserve" y="676.5" zvalue="713">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="125" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,779.25,730) scale(1,1) translate(0,0)" writing-mode="lr" x="779.25" xml:space="preserve" y="734.5" zvalue="716">651</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="134" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1103,898) scale(1,1) translate(0,0)" writing-mode="lr" x="1103" xml:space="preserve" y="902.5" zvalue="720">#2发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="227" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1103.5,923.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1103.5" xml:space="preserve" y="927.75" zvalue="721">2MW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="133" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1173.92,868.164) scale(1,1) translate(0,0)" writing-mode="lr" x="1173.92" xml:space="preserve" y="872.66" zvalue="723">#2励磁PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="132" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1027.78,810.504) scale(1,1) translate(0,0)" writing-mode="lr" x="1027.78" xml:space="preserve" y="815" zvalue="725">6921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="131" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1157.25,715) scale(1,1) translate(0,0)" writing-mode="lr" x="1157.25" xml:space="preserve" y="719.5" zvalue="728">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="130" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,999,882.5) scale(1,1) translate(0,0)" writing-mode="lr" x="999" xml:space="preserve" y="887" zvalue="735">#2机组PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="129" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1112.44,672) scale(1,1) translate(0,0)" writing-mode="lr" x="1112.44" xml:space="preserve" y="676.5" zvalue="737">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="128" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1127.25,730) scale(1,1) translate(0,0)" writing-mode="lr" x="1127.25" xml:space="preserve" y="734.5" zvalue="740">652</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="236" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1341.25,739) scale(1,1) translate(0,0)" writing-mode="lr" x="1341.25" xml:space="preserve" y="743.5" zvalue="745">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="242" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1522.05,805) scale(1,1) translate(0,0)" writing-mode="lr" x="1522.05" xml:space="preserve" y="809.5" zvalue="749">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="247" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1546.75,690) scale(1,1) translate(0,0)" writing-mode="lr" x="1546.75" xml:space="preserve" y="694.5" zvalue="750">6531</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1464.5,482.083) scale(1,1) translate(0,0)" writing-mode="lr" x="1464.5" xml:space="preserve" y="486.58" zvalue="760">#2站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1462.08,228) scale(1,1) translate(0,0)" writing-mode="lr" x="1462.08" xml:space="preserve" y="232.5" zvalue="764">10.5kV施工电</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="50.19" y="175.25" zvalue="361"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="244">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="244" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,262.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="267.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125076795398" ObjectName="F"/>
   </metadata>
  </g>
  <g id="245">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="245" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,263.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="268.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125076860934" ObjectName="F"/>
   </metadata>
  </g>
  <g id="246">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="246" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,158.333,336.278) scale(1,1) translate(0,0)" writing-mode="lr" x="158.49" xml:space="preserve" y="341.19" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125051891719" ObjectName="F"/>
   </metadata>
  </g>
  <g id="256">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="256" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,287.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="292.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125076664326" ObjectName="F"/>
   </metadata>
  </g>
  <g id="257">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="257" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,288.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="293.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125076729862" ObjectName="F"/>
   </metadata>
  </g>
  <g id="194">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="194" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,379.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="384.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126658768900" ObjectName="坝上水位"/>
   </metadata>
  </g>
  <g id="192">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="192" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,337.611,379.389) scale(1,1) translate(0,0)" writing-mode="lr" x="337.77" xml:space="preserve" y="384.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126658965508" ObjectName="雨量采集"/>
   </metadata>
  </g>
  <g id="189">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="189" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,401.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="406.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126659031044" ObjectName="坝下水位"/>
   </metadata>
  </g>
  <g id="183">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="183" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,159.611,311.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="316.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125076664326" ObjectName="F"/>
   </metadata>
  </g>
  <g id="181">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="181" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,339.611,310.167) scale(1,1) translate(0,0)" writing-mode="lr" x="339.77" xml:space="preserve" y="315.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125076664326" ObjectName="F"/>
   </metadata>
  </g>
  <g id="53">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="53" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1066,101.125) scale(1,1) translate(0,0)" writing-mode="lr" x="1065.53" xml:space="preserve" y="105.81" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125053202437" ObjectName="P"/>
   </metadata>
  </g>
  <g id="56">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="56" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1066,121.875) scale(1,1) translate(0,0)" writing-mode="lr" x="1065.53" xml:space="preserve" y="126.54" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125053267973" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="59">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="59" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1066,144.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1065.53" xml:space="preserve" y="149.31" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125053333509" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="88">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="88" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1221.05,532.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1220.49" xml:space="preserve" y="538.6799999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125054775301" ObjectName="P"/>
   </metadata>
  </g>
  <g id="89">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="89" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1221.05,553.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1220.49" xml:space="preserve" y="559.6799999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125054840837" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="91">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="91" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1221.05,574.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1220.49" xml:space="preserve" y="580.6799999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125055234053" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="92">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="92" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,747.498,952.75) scale(1,1) translate(-1.5421e-13,0)" writing-mode="lr" x="746.95" xml:space="preserve" y="958.9400000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125052022789" ObjectName="P"/>
   </metadata>
  </g>
  <g id="93">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="93" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,747.498,976.875) scale(1,1) translate(-1.5421e-13,0)" writing-mode="lr" x="746.95" xml:space="preserve" y="983.0599999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125052088325" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="94">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="94" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,747.498,1001) scale(1,1) translate(-1.5421e-13,0)" writing-mode="lr" x="746.95" xml:space="preserve" y="1007.19" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125052153861" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="103">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="103" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1292,849) scale(1,1) translate(0,0)" writing-mode="lr" x="1291.53" xml:space="preserve" y="853.66" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125051498501" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="104">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="104" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1292,869.875) scale(1,1) translate(0,0)" writing-mode="lr" x="1291.53" xml:space="preserve" y="874.53" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125051564037" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="105">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="105" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1292,890.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1291.53" xml:space="preserve" y="895.41" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125051629573" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="106">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="106" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1292,911.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1291.53" xml:space="preserve" y="916.28" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125051760645" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="107">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="107" prefix="F:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1292,932.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1291.53" xml:space="preserve" y="937.16" zvalue="1">F:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125051891719" ObjectName="F"/>
   </metadata>
  </g>
  <g id="272">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="272" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1103.5,952.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1102.95" xml:space="preserve" y="958.9400000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125057724421" ObjectName="P"/>
   </metadata>
  </g>
  <g id="273">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="273" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1103.5,976.875) scale(1,1) translate(0,0)" writing-mode="lr" x="1102.95" xml:space="preserve" y="983.0599999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125057789957" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="274">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="274" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1103.5,1001) scale(1,1) translate(0,0)" writing-mode="lr" x="1102.95" xml:space="preserve" y="1007.19" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125057855493" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="20">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="20" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1210,342.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1209.45" xml:space="preserve" y="348.68" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125054644229" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="16">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="16" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1210,363.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1209.45" xml:space="preserve" y="369.68" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125054709765" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="4">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="4" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1210,384.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1209.45" xml:space="preserve" y="390.68" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125054906373" ObjectName="LIa"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="211">
   <use height="30" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="359"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374889517059" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" y="178.37"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="360"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562951090667525" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" y="178.37"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="6">
   <path class="v6300" d="M 539 636 L 1706 636" stroke-width="6" zvalue="382"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674241478660" ObjectName="6.3kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674241478660"/></metadata>
  <path d="M 539 636 L 1706 636" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="48">
   <use class="v6300" height="30" transform="rotate(0,755.498,850) scale(1.5,1.5) translate(-244.333,-275.833)" width="30" x="732.998088416586" xlink:href="#Generator:发电机_0" y="827.5" zvalue="418"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449769111558" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449769111558"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,755.498,850) scale(1.5,1.5) translate(-244.333,-275.833)" width="30" x="732.998088416586" y="827.5"/></g>
  <g id="229">
   <use class="v6300" height="30" transform="rotate(0,1103.5,850) scale(1.5,1.5) translate(-360.333,-275.833)" width="30" x="1080.998088416586" xlink:href="#Generator:发电机_0" y="827.5" zvalue="719"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449770815493" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449770815493"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1103.5,850) scale(1.5,1.5) translate(-360.333,-275.833)" width="30" x="1080.998088416586" y="827.5"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="25">
   <use class="v6300" height="29" transform="rotate(0,825.917,840.891) scale(1.08779,-1.08779) translate(-65.3408,-1612.64)" width="30" x="809.5996989327974" xlink:href="#Accessory:PT12321_0" y="825.1180015592805" zvalue="512"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449769242630" ObjectName="#1励磁PT"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,825.917,840.891) scale(1.08779,-1.08779) translate(-65.3408,-1612.64)" width="30" x="809.5996989327974" y="825.1180015592805"/></g>
  <g id="138">
   <use class="v6300" height="26" transform="rotate(90,1010,517.911) scale(1,1) translate(0,0)" width="12" x="1004" xlink:href="#Accessory:避雷器1_0" y="504.9105406023799" zvalue="585"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449769308166" ObjectName="#1主变10kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1010,517.911) scale(1,1) translate(0,0)" width="12" x="1004" y="504.9105406023799"/></g>
  <g id="174">
   <use class="v6300" height="26" transform="rotate(0,714,808) scale(1,1) translate(0,0)" width="12" x="708" xlink:href="#Accessory:避雷器1_0" y="795" zvalue="599"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449769766918" ObjectName="#1发电机避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,714,808) scale(1,1) translate(0,0)" width="12" x="708" y="795"/></g>
  <g id="219">
   <use class="v6300" height="18" transform="rotate(0,1292.32,772.317) scale(2.17559,1.81299) translate(-689.491,-339.009)" width="15" x="1276" xlink:href="#Accessory:PT8_0" y="756" zvalue="630"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449769897990" ObjectName="6.3kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1292.32,772.317) scale(2.17559,1.81299) translate(-689.491,-339.009)" width="15" x="1276" y="756"/></g>
  <g id="57">
   <use class="kv10" height="40" transform="rotate(0,1133,309) scale(-1,-1) translate(-2266,-618)" width="40" x="1113" xlink:href="#Accessory:PT1111_0" y="289" zvalue="698"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449770029061" ObjectName="10kV国南线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1133,309) scale(-1,-1) translate(-2266,-618)" width="40" x="1113" y="289"/></g>
  <g id="61">
   <use class="kv10" height="26" transform="rotate(270,1132,259) scale(-1,1) translate(-2264,0)" width="12" x="1126" xlink:href="#Accessory:避雷器1_0" y="246" zvalue="700"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449770094597" ObjectName="10kV国南线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1132,259) scale(-1,1) translate(-2264,0)" width="12" x="1126" y="246"/></g>
  <g id="116">
   <use class="v6300" height="18" transform="rotate(0,654.667,850) scale(1.88889,1.88889) translate(-301.412,-392)" width="15" x="640.5" xlink:href="#Accessory:机组PT2021_0" y="833" zvalue="709"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449770225670" ObjectName="#1机组PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,654.667,850) scale(1.88889,1.88889) translate(-301.412,-392)" width="15" x="640.5" y="833"/></g>
  <g id="167">
   <use class="v6300" height="29" transform="rotate(0,1173.92,840.891) scale(1.08779,-1.08779) translate(-93.427,-1612.64)" width="30" x="1157.599698932797" xlink:href="#Accessory:PT12321_0" y="825.1180015592805" zvalue="722"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449770749957" ObjectName="#2励磁PT"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1173.92,840.891) scale(1.08779,-1.08779) translate(-93.427,-1612.64)" width="30" x="1157.599698932797" y="825.1180015592805"/></g>
  <g id="149">
   <use class="v6300" height="26" transform="rotate(0,1062,808) scale(1,1) translate(0,0)" width="12" x="1056" xlink:href="#Accessory:避雷器1_0" y="795" zvalue="730"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449770487814" ObjectName="#2发电机避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1062,808) scale(1,1) translate(0,0)" width="12" x="1056" y="795"/></g>
  <g id="143">
   <use class="v6300" height="18" transform="rotate(0,1002.67,850) scale(1.88889,1.88889) translate(-465.176,-392)" width="15" x="988.5" xlink:href="#Accessory:机组PT2021_0" y="833" zvalue="734"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449770422278" ObjectName="#2机组PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1002.67,850) scale(1.88889,1.88889) translate(-465.176,-392)" width="15" x="988.5" y="833"/></g>
  <g id="255">
   <use class="v6300" height="26" transform="rotate(0,1486,730) scale(1,1) translate(0,0)" width="12" x="1480" xlink:href="#Accessory:避雷器1_0" y="717" zvalue="753"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449771143173" ObjectName="#1站用变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1486,730) scale(1,1) translate(0,0)" width="12" x="1480" y="717"/></g>
  <g id="5">
   <use class="v10500" height="26" transform="rotate(0,1421,383) scale(1,1) translate(0,0)" width="12" x="1415" xlink:href="#Accessory:避雷器1_0" y="370" zvalue="767"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449771405317" ObjectName="#2站用变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1421,383) scale(1,1) translate(0,0)" width="12" x="1415" y="370"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="80">
   <use class="v6300" height="30" transform="rotate(0,652.687,805.399) scale(0.835563,1.0743) translate(127.214,-54.5848)" width="15" x="646.4201537147731" xlink:href="#Disconnector:刀闸_0" y="789.2843723313406" zvalue="513"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449769177094" ObjectName="#1发电机6911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449769177094"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,652.687,805.399) scale(0.835563,1.0743) translate(127.214,-54.5848)" width="15" x="646.4201537147731" y="789.2843723313406"/></g>
  <g id="153">
   <use class="kv10" height="30" transform="rotate(0,1072.91,294) scale(1,0.733333) translate(0,102.909)" width="15" x="1065.412222949139" xlink:href="#Disconnector:刀闸_0" y="283" zvalue="566"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449769373702" ObjectName="10kV国南线0516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449769373702"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1072.91,294) scale(1,0.733333) translate(0,102.909)" width="15" x="1065.412222949139" y="283"/></g>
  <g id="217">
   <use class="v6300" height="30" transform="rotate(0,1291.27,684.114) scale(0.835563,1.0743) translate(252.885,-46.1971)" width="15" x="1285" xlink:href="#Disconnector:刀闸_0" y="668" zvalue="628"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449769832454" ObjectName="6.3kV母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449769832454"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1291.27,684.114) scale(0.835563,1.0743) translate(252.885,-46.1971)" width="15" x="1285" y="668"/></g>
  <g id="13">
   <use class="kv10" height="30" transform="rotate(0,1073.5,396) scale(1,0.733333) translate(0,140)" width="15" x="1066" xlink:href="#Disconnector:刀闸_0" y="385" zvalue="694"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449769963526" ObjectName="10kV国南线0511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449769963526"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1073.5,396) scale(1,0.733333) translate(0,140)" width="15" x="1066" y="385"/></g>
  <g id="110">
   <use class="v6300" height="30" transform="rotate(0,1074.19,599) scale(1,0.733333) translate(0,213.818)" width="15" x="1066.686541260778" xlink:href="#Disconnector:刀闸_0" y="588" zvalue="704"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449770160134" ObjectName="#1主变6.3kV侧6011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449770160134"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1074.19,599) scale(1,0.733333) translate(0,213.818)" width="15" x="1066.686541260778" y="588"/></g>
  <g id="120">
   <use class="v6300" height="30" transform="rotate(0,755.437,673) scale(1,0.733333) translate(0,240.727)" width="15" x="747.9369239924802" xlink:href="#Disconnector:刀闸_0" y="662" zvalue="712"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449770291205" ObjectName="#1发电机6511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449770291205"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,755.437,673) scale(1,0.733333) translate(0,240.727)" width="15" x="747.9369239924802" y="662"/></g>
  <g id="164">
   <use class="v6300" height="30" transform="rotate(0,1000.69,805.399) scale(0.835563,1.0743) translate(195.7,-54.5848)" width="15" x="994.4201537147731" xlink:href="#Disconnector:刀闸_0" y="789.2843723313406" zvalue="724"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449770684421" ObjectName="#2发电机6921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449770684421"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1000.69,805.399) scale(0.835563,1.0743) translate(195.7,-54.5848)" width="15" x="994.4201537147731" y="789.2843723313406"/></g>
  <g id="140">
   <use class="v6300" height="30" transform="rotate(0,1103.44,673) scale(1,0.733333) translate(0,240.727)" width="15" x="1095.93692399248" xlink:href="#Disconnector:刀闸_0" y="662" zvalue="736"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449770356741" ObjectName="#2发电机6521隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449770356741"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1103.44,673) scale(1,0.733333) translate(0,240.727)" width="15" x="1095.93692399248" y="662"/></g>
  <g id="243">
   <use class="v6300" height="30" transform="rotate(0,1520,689) scale(1,0.733333) translate(0,246.545)" width="15" x="1512.5" xlink:href="#Disconnector:刀闸_0" y="678" zvalue="749"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449771077637" ObjectName="#1站用变6531隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449771077637"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1520,689) scale(1,0.733333) translate(0,246.545)" width="15" x="1512.5" y="678"/></g>
  <g id="21">
   <use class="v10500" height="30" transform="rotate(0,1460,325) scale(1,1) translate(0,0)" width="15" x="1452.5" xlink:href="#Disconnector:令克_0" y="310" zvalue="762"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449771274245" ObjectName="#2站用变跌落保险"/>
   <cge:TPSR_Ref TObjectID="6192449771274245"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1460,325) scale(1,1) translate(0,0)" width="15" x="1452.5" y="310"/></g>
  <g id="9">
   <use class="v10500" height="36" transform="rotate(0,1460,393) scale(1,1) translate(0,0)" width="14" x="1453" xlink:href="#Disconnector:联体小车刀闸2_0" y="375" zvalue="769"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449771470853" ObjectName="#2站用变刀闸"/>
   <cge:TPSR_Ref TObjectID="6192449771470853"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1460,393) scale(1,1) translate(0,0)" width="14" x="1453" y="375"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="23">
   <path class="v6300" d="M 652.74 821.24 L 652.74 834.21" stroke-width="1" zvalue="516"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="80@1" LinkObjectIDznd="116@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 652.74 821.24 L 652.74 834.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="156">
   <path class="v6300" d="M 1074.11 531.65 L 1074.11 504.26" stroke-width="1" zvalue="563"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@0" LinkObjectIDznd="163@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1074.11 531.65 L 1074.11 504.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="150">
   <path class="kv10" d="M 1072.97 304.81 L 1072.97 338.09" stroke-width="1" zvalue="569"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="153@1" LinkObjectIDznd="161@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1072.97 304.81 L 1072.97 338.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="168">
   <path class="v6300" d="M 1022.37 517.94 L 1074.11 517.95" stroke-width="1" zvalue="592"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="138@0" LinkObjectIDznd="156" MaxPinNum="2"/>
   </metadata>
  <path d="M 1022.37 517.94 L 1074.11 517.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="173">
   <path class="v6300" d="M 796.25 697.95 L 755.95 697.95" stroke-width="1" zvalue="597"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="171@0" LinkObjectIDznd="127" MaxPinNum="2"/>
   </metadata>
  <path d="M 796.25 697.95 L 755.95 697.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="176">
   <path class="v6300" d="M 755.5 772 L 652.69 772 L 652.76 789.82" stroke-width="1" zvalue="601"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="126" LinkObjectIDznd="80@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 755.5 772 L 652.69 772 L 652.76 789.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="177">
   <path class="v6300" d="M 714.03 795.63 L 714.03 772" stroke-width="1" zvalue="602"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="174@0" LinkObjectIDznd="176" MaxPinNum="2"/>
   </metadata>
  <path d="M 714.03 795.63 L 714.03 772" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="222">
   <path class="v6300" d="M 1291.34 668.53 L 1291.34 636" stroke-width="1" zvalue="633"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="217@0" LinkObjectIDznd="6@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1291.34 668.53 L 1291.34 636" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="223">
   <path class="v6300" d="M 1291.32 699.95 L 1291.32 757.16" stroke-width="1" zvalue="634"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="217@1" LinkObjectIDznd="219@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1291.32 699.95 L 1291.32 757.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="18">
   <path class="kv10" d="M 1074.1 363.89 L 1074.1 385.36" stroke-width="1" zvalue="695"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@1" LinkObjectIDznd="13@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1074.1 363.89 L 1074.1 385.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="19">
   <path class="kv10" d="M 1073.56 406.81 L 1073.56 423.58" stroke-width="1" zvalue="696"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="13@1" LinkObjectIDznd="163@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1073.56 406.81 L 1073.56 423.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="54">
   <path class="kv10" d="M 1073 283.36 L 1073 236.32" stroke-width="1" zvalue="697"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="153@0" LinkObjectIDznd="159@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1073 283.36 L 1073 236.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv10" d="M 1119.63 259.03 L 1073 259.03" stroke-width="1" zvalue="701"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@0" LinkObjectIDznd="54" MaxPinNum="2"/>
   </metadata>
  <path d="M 1119.63 259.03 L 1073 259.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv10" d="M 1119.25 317.05 L 1072.97 317.05" stroke-width="1" zvalue="702"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="57@0" LinkObjectIDznd="150" MaxPinNum="2"/>
   </metadata>
  <path d="M 1119.25 317.05 L 1072.97 317.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="v6300" d="M 1074.27 560.33 L 1074.27 588.36" stroke-width="1" zvalue="705"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@1" LinkObjectIDznd="110@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1074.27 560.33 L 1074.27 588.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="v6300" d="M 1074.25 609.81 L 1074.25 636" stroke-width="1" zvalue="706"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="110@1" LinkObjectIDznd="6@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1074.25 609.81 L 1074.25 636" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="114">
   <path class="v6300" d="M 1115.25 575.95 L 1074.27 575.95" stroke-width="1" zvalue="707"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="155@0" LinkObjectIDznd="112" MaxPinNum="2"/>
   </metadata>
  <path d="M 1115.25 575.95 L 1074.27 575.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="115">
   <path class="v6300" d="M 825.92 825.39 L 825.92 772 L 755.5 772" stroke-width="1" zvalue="708"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="25@0" LinkObjectIDznd="176" MaxPinNum="2"/>
   </metadata>
  <path d="M 825.92 825.39 L 825.92 772 L 755.5 772" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="123">
   <path class="v6300" d="M 755.52 662.36 L 755.52 636" stroke-width="1" zvalue="714"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="120@0" LinkObjectIDznd="6@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 755.52 662.36 L 755.52 636" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="126">
   <path class="v6300" d="M 755.5 827.88 L 755.5 742.89" stroke-width="1" zvalue="716"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="48@0" LinkObjectIDznd="124@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 755.5 827.88 L 755.5 742.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="v6300" d="M 755.95 717.09 L 755.95 683.81" stroke-width="1" zvalue="717"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="124@0" LinkObjectIDznd="120@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 755.95 717.09 L 755.95 683.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="162">
   <path class="v6300" d="M 1000.74 821.24 L 1000.74 834.21" stroke-width="1" zvalue="726"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="164@1" LinkObjectIDznd="143@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1000.74 821.24 L 1000.74 834.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="151">
   <path class="v6300" d="M 1144.25 697.95 L 1103.95 697.95" stroke-width="1" zvalue="729"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="154@0" LinkObjectIDznd="135" MaxPinNum="2"/>
   </metadata>
  <path d="M 1144.25 697.95 L 1103.95 697.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="148">
   <path class="v6300" d="M 1103.5 772 L 1000.69 772 L 1000.76 789.82" stroke-width="1" zvalue="731"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="136" LinkObjectIDznd="164@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1103.5 772 L 1000.69 772 L 1000.76 789.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="147">
   <path class="v6300" d="M 1062.03 795.63 L 1062.03 772" stroke-width="1" zvalue="732"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="149@0" LinkObjectIDznd="148" MaxPinNum="2"/>
   </metadata>
  <path d="M 1062.03 795.63 L 1062.03 772" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="146">
   <path class="v6300" d="M 1173.92 825.39 L 1173.92 772 L 1103.5 772" stroke-width="1" zvalue="733"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="167@0" LinkObjectIDznd="148" MaxPinNum="2"/>
   </metadata>
  <path d="M 1173.92 825.39 L 1173.92 772 L 1103.5 772" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="139">
   <path class="v6300" d="M 1103.52 662.36 L 1103.52 636" stroke-width="1" zvalue="738"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="140@0" LinkObjectIDznd="6@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1103.52 662.36 L 1103.52 636" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="136">
   <path class="v6300" d="M 1103.5 827.88 L 1103.5 742.89" stroke-width="1" zvalue="741"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="229@0" LinkObjectIDznd="137@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1103.5 827.88 L 1103.5 742.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="135">
   <path class="v6300" d="M 1103.95 717.09 L 1103.95 683.81" stroke-width="1" zvalue="742"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="137@0" LinkObjectIDznd="140@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1103.95 717.09 L 1103.95 683.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="238">
   <path class="v6300" d="M 1328.25 721.95 L 1291.32 721.95" stroke-width="1" zvalue="746"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="237@0" LinkObjectIDznd="223" MaxPinNum="2"/>
   </metadata>
  <path d="M 1328.25 721.95 L 1291.32 721.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="248">
   <path class="v6300" d="M 1520.09 678.36 L 1520.09 636" stroke-width="1" zvalue="750"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="243@0" LinkObjectIDznd="6@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1520.09 678.36 L 1520.09 636" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="250">
   <path class="v6300" d="M 1520.06 699.81 L 1520.06 739.32" stroke-width="1" zvalue="751"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="243@1" LinkObjectIDznd="241@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1520.06 699.81 L 1520.06 739.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="271">
   <path class="v6300" d="M 1486.03 717.63 L 1486.03 707 L 1520.06 707" stroke-width="1" zvalue="754"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="255@0" LinkObjectIDznd="250" MaxPinNum="2"/>
   </metadata>
  <path d="M 1486.03 717.63 L 1486.03 707 L 1520.06 707" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="3">
   <path class="v10500" d="M 1460.08 284.65 L 1460.08 311.75" stroke-width="1" zvalue="765"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="24@0" LinkObjectIDznd="21@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1460.08 284.65 L 1460.08 311.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="8">
   <path class="v10500" d="M 1421.03 370.63 L 1421.03 359.75 L 1459.97 359.75" stroke-width="1" zvalue="768"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="5@0" LinkObjectIDznd="12" MaxPinNum="2"/>
   </metadata>
  <path d="M 1421.03 370.63 L 1421.03 359.75 L 1459.97 359.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="12">
   <path class="v10500" d="M 1459.92 337.25 L 1460 376" stroke-width="1" zvalue="770"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="21@1" LinkObjectIDznd="9@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1459.92 337.25 L 1460 376" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="14">
   <path class="v10500" d="M 1460 410 L 1460 421.69" stroke-width="1" zvalue="771"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="9@1" LinkObjectIDznd="17@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1460 410 L 1460 421.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="163">
   <g id="1630">
    <use class="v6300" height="50" transform="rotate(0,1074.05,464) scale(1.82222,-1.64) translate(-472.297,-730.927)" width="30" x="1046.71" xlink:href="#PowerTransformer2:586_0" y="423" zvalue="553"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874433626116" ObjectName="6.3"/>
    </metadata>
   </g>
   <g id="1631">
    <use class="kv10" height="50" transform="rotate(0,1074.05,464) scale(1.82222,-1.64) translate(-472.297,-730.927)" width="30" x="1046.71" xlink:href="#PowerTransformer2:586_1" y="423" zvalue="553"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874433560580" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399449051140" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399449051140"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1074.05,464) scale(1.82222,-1.64) translate(-472.297,-730.927)" width="30" x="1046.71" y="423"/></g>
 </g>
 <g id="BreakerClass">
  <g id="161">
   <use class="kv10" height="20" transform="rotate(0,1074,351) scale(1.5,1.35) translate(-355.5,-87.5)" width="10" x="1066.5" xlink:href="#Breaker:开关_0" y="337.5" zvalue="556"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924504846341" ObjectName="10kV国南线051断路器"/>
   <cge:TPSR_Ref TObjectID="6473924504846341"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1074,351) scale(1.5,1.35) translate(-355.5,-87.5)" width="10" x="1066.5" y="337.5"/></g>
  <g id="158">
   <use class="v6300" height="20" transform="rotate(0,1074.16,546) scale(1.68319,1.5) translate(-432.577,-177)" width="10" x="1065.746144083167" xlink:href="#Breaker:开关_0" y="531" zvalue="561"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924504780805" ObjectName="#1主变6.3kV侧601断路器"/>
   <cge:TPSR_Ref TObjectID="6473924504780805"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1074.16,546) scale(1.68319,1.5) translate(-432.577,-177)" width="10" x="1065.746144083167" y="531"/></g>
  <g id="124">
   <use class="v6300" height="20" transform="rotate(0,756,730) scale(1.5,1.35) translate(-249.5,-185.759)" width="10" x="748.5" xlink:href="#Breaker:开关_0" y="716.5" zvalue="715"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924504911877" ObjectName="#1发电机651断路器"/>
   <cge:TPSR_Ref TObjectID="6473924504911877"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,756,730) scale(1.5,1.35) translate(-249.5,-185.759)" width="10" x="748.5" y="716.5"/></g>
  <g id="137">
   <use class="v6300" height="20" transform="rotate(0,1104,730) scale(1.5,1.35) translate(-365.5,-185.759)" width="10" x="1096.5" xlink:href="#Breaker:开关_0" y="716.5" zvalue="739"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924504977413" ObjectName="#2发电机652断路器"/>
   <cge:TPSR_Ref TObjectID="6473924504977413"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1104,730) scale(1.5,1.35) translate(-365.5,-185.759)" width="10" x="1096.5" y="716.5"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="155">
   <use class="v6300" height="20" transform="rotate(270,1125,576) scale(1,1) translate(0,0)" width="10" x="1120" xlink:href="#GroundDisconnector:地刀_0" y="566" zvalue="564"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449769504774" ObjectName="#1主变6.3kV侧60117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449769504774"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1125,576) scale(1,1) translate(0,0)" width="10" x="1120" y="566"/></g>
  <g id="171">
   <use class="v6300" height="20" transform="rotate(270,806,698) scale(1,1) translate(0,0)" width="10" x="801" xlink:href="#GroundDisconnector:地刀_0" y="688" zvalue="596"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449769701382" ObjectName="#1发电机65117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449769701382"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,806,698) scale(1,1) translate(0,0)" width="10" x="801" y="688"/></g>
  <g id="154">
   <use class="v6300" height="20" transform="rotate(270,1154,698) scale(1,1) translate(0,0)" width="10" x="1149" xlink:href="#GroundDisconnector:地刀_0" y="688" zvalue="727"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449770618885" ObjectName="#2发电机65217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449770618885"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1154,698) scale(1,1) translate(0,0)" width="10" x="1149" y="688"/></g>
  <g id="237">
   <use class="v6300" height="20" transform="rotate(270,1338,722) scale(1,1) translate(0,0)" width="10" x="1333" xlink:href="#GroundDisconnector:地刀_0" y="712" zvalue="744"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449770946565" ObjectName="6.3kV母线电压互感器09017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449770946565"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1338,722) scale(1,1) translate(0,0)" width="10" x="1333" y="712"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="241">
   <use class="v6300" height="38" transform="rotate(0,1524.97,763) scale(1.26316,1.26316) translate(-314.28,-153.958)" width="26" x="1508.545374950422" xlink:href="#EnergyConsumer:站用变1节点_0" y="739" zvalue="748"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449771012101" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="38" opacity="0" stroke="white" transform="rotate(0,1524.97,763) scale(1.26316,1.26316) translate(-314.28,-153.958)" width="26" x="1508.545374950422" y="739"/></g>
  <g id="17">
   <use class="v10500" height="30" transform="rotate(0,1459.72,448.333) scale(1.83711,1.85) translate(-653.425,-193.241)" width="28" x="1434" xlink:href="#EnergyConsumer:站用变DY接地_0" y="420.5826364057742" zvalue="759"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449771339781" ObjectName="#2站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1459.72,448.333) scale(1.83711,1.85) translate(-653.425,-193.241)" width="28" x="1434" y="420.5826364057742"/></g>
  <g id="24">
   <use class="v10500" height="30" transform="rotate(0,1460.08,268) scale(1.25,1.23333) translate(-290.517,-47.2027)" width="12" x="1452.583333333333" xlink:href="#EnergyConsumer:负荷_0" y="249.5" zvalue="763"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449771208709" ObjectName="10.5kV施工电"/>
   <cge:TPSR_Ref TObjectID="6192449771208709"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1460.08,268) scale(1.25,1.23333) translate(-290.517,-47.2027)" width="12" x="1452.583333333333" y="249.5"/></g>
  <g id="22">
   <use class="kv0" height="30" transform="rotate(0,1072,208) scale(1.25,1.23333) translate(-212.9,-35.8514)" width="12" x="1064.5" xlink:href="#EnergyConsumer:负荷_0" y="189.5" zvalue="776"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1072,208) scale(1.25,1.23333) translate(-212.9,-35.8514)" width="12" x="1064.5" y="189.5"/></g>
  <g id="26">
   <use class="kv0" height="30" transform="rotate(0,1077,223) scale(1.25,1.23333) translate(-213.9,-38.6892)" width="12" x="1069.5" xlink:href="#EnergyConsumer:负荷_0" y="204.5" zvalue="777"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1077,223) scale(1.25,1.23333) translate(-213.9,-38.6892)" width="12" x="1069.5" y="204.5"/></g>
 </g>
</svg>