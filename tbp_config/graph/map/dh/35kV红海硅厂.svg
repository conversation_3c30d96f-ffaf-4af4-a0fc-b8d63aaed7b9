<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549585575938" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Accessory:线路PT11带避雷器_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="20" xlink:href="#terminal" y="0.9166666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.91666666666667" x2="22.91666666666667" y1="23.75" y2="23.75"/>
   <rect fill-opacity="0" height="10" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,20,11) scale(1,1) translate(0,0)" width="6" x="17" y="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32" x2="20" y1="5" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32" x2="32" y1="17" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.13040845230975" x2="32.13040845230975" y1="16.76788570496156" y2="23.53635804601289"/>
   <rect fill-opacity="0" height="6.05" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,32.04,24.56) scale(1,1) translate(0,0)" width="15.34" x="24.37" y="21.53"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.98821300514558" x2="19.98821300514558" y1="1.029523490692871" y2="18.75"/>
   <path d="M 30.0147 23.4481 L 34.2865 23.4481 L 32.115 28.6155 L 30.0147 23.4481" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <ellipse cx="19.76" cy="32.23" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.76" cy="24.17" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.22179295459144" x2="32.22179295459144" y1="32.22550978083666" y2="36.2625308385742"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="29.01788570496155" x2="35.42570020422134" y1="36.23023467011234" y2="36.23023467011234"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="30.26384963537316" x2="34.53572596821302" y1="37.52208140858838" y2="37.52208140858838"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.15382387138148" x2="33.11176719059974" y1="38.49096646244535" y2="38.49096646244535"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.91666666666667" x2="22.91666666666667" y1="32.91666666666667" y2="32.91666666666667"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="EnergyConsumer:接地两卷变2_0" viewBox="0,0,18,30">
   <use terminal-index="0" type="0" x="8.949999999999999" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="9.039999999999999" cy="8.720000000000001" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.970000000000001" cy="20.89" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.035256190670173" x2="9.035256190670173" y1="4.35" y2="8.458948332339869"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.035256190670172" x2="5.05" y1="8.483948332339876" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.063029030724982" x2="13.15" y1="8.439905009168511" y2="11.25"/>
   <path d="M 9 19.5833 L 4.08333 25.1667 L 14.0833 25.1667 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Compensator:并联电容器4_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.4" x2="13" y1="10.14166666666666" y2="10.14166666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.14166666666666" x2="15.14166666666666" y1="7.049999999999999" y2="10.14166666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.14166666666666" x2="15.14166666666666" y1="12.15" y2="15.09166666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.20132377611978" x2="17.67043856134933" y1="14.9457213535914" y2="16.80633280016981"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.02953748863437" x2="16.38155138676536" y1="15.00274760664634" y2="18.51674385085443"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62953504837116" x2="11.36336751876692" y1="18.72804518360739" y2="14.95650906051809"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.27436487736098" x2="21.62368433608343" y1="18.01497797166685" y2="19.78531716477246"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.11648296720332" x2="12.46640739586596" y1="15.19994964792486" y2="16.79227569618845"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.4" x2="13" y1="12.14166666666666" y2="12.14166666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.74492973362254" x2="8.223429257390499" y1="17.82664382996614" y2="19.34171416699321"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.62680850872896" x2="17.97882240685994" y1="16.20637765295043" y2="19.72037389715852"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.91520044696694" x2="9.649032917362698" y1="19.7581213334275" y2="15.9865852103382"/>
  </symbol>
  <symbol id="Disconnector:联体小车刀闸2_0" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="9.583333333333332" y2="1.050000000000001"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.083333333333333" x2="7.083333333333333" y1="26.41666666666667" y2="34.83333333333334"/>
   <rect fill-opacity="0" height="17" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,18) scale(1,1) translate(0,0)" width="8" x="3" y="9.5"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
  </symbol>
  <symbol id="Disconnector:联体小车刀闸2_1" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="35" y2="1.166666666666664"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <rect fill-opacity="0" height="17" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,18) scale(1,1) translate(0,0)" width="8" x="3" y="9.5"/>
  </symbol>
  <symbol id="Disconnector:联体小车刀闸2_2" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.083333333333333" x2="7.083333333333333" y1="9.999999999999995" y2="1.299999999999994"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="1.499999999999999" y1="26" y2="9.966666666666669"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.999999999999998" x2="12.41666666666667" y1="26.25" y2="10.05"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV红海硅厂" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,176.796,95.3772) scale(1,1) translate(9.11012e-15,-5.83331e-14)" writing-mode="lr" x="176.8" xml:space="preserve" y="99.88" zvalue="3"/>
  <line fill="none" id="27" stroke="rgb(0,39,45)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.97222222222263" x2="328.5597708603561" y1="78.45055797356122" y2="78.45055797356122" zvalue="5"/>
  <line fill="none" id="26" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="384.75" x2="384.75" y1="51.25" y2="1041.25" zvalue="6"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="1" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,252.875,64.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="252.88" xml:space="preserve" y="73.69" zvalue="138">35kV红海硅厂</text>
  <image height="60" id="2" preserveAspectRatio="xMidYMid slice" width="277.25" x="73.75" xlink:href="logo.png" y="36.75"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,212.375,66.75) scale(1,1) translate(0,0)" writing-mode="lr" x="212.38" xml:space="preserve" y="70.25" zvalue="139"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="3" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,206,62.9403) scale(1,1) translate(0,0)" writing-mode="lr" x="206" xml:space="preserve" y="71.94" zvalue="140">35kV红海硅厂</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="28" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,76.4375,333) scale(1,1) translate(0,0)" width="72.88" x="40" y="321" zvalue="144"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,76.4375,333) scale(1,1) translate(0,0)" writing-mode="lr" x="76.44" xml:space="preserve" y="337.5" zvalue="144">信号一览</text>
  <line fill="none" id="24" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.22692454998003" x2="327.8381846035992" y1="170.8779458827686" y2="170.8779458827686" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="51.32142857142844" x2="122.7614285714285" y1="929.8127909390723" y2="929.8127909390723"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="51.32142857142844" x2="122.7614285714285" y1="981.1522909390724" y2="981.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="51.32142857142844" x2="51.32142857142844" y1="929.8127909390723" y2="981.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="122.7614285714285" x2="122.7614285714285" y1="929.8127909390723" y2="981.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="122.7619285714285" x2="357.3219285714284" y1="929.8127909390723" y2="929.8127909390723"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="122.7619285714285" x2="357.3219285714284" y1="981.1522909390724" y2="981.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="122.7619285714285" x2="122.7619285714285" y1="929.8127909390723" y2="981.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="357.3219285714284" x2="357.3219285714284" y1="929.8127909390723" y2="981.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="51.32142857142844" x2="122.7614285714285" y1="981.1522709390724" y2="981.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="51.32142857142844" x2="122.7614285714285" y1="1008.629770939072" y2="1008.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="51.32142857142844" x2="51.32142857142844" y1="981.1522709390724" y2="1008.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="122.7614285714285" x2="122.7614285714285" y1="981.1522709390724" y2="1008.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="122.7619285714285" x2="193.7972285714285" y1="981.1522709390724" y2="981.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="122.7619285714285" x2="193.7972285714285" y1="1008.629770939072" y2="1008.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="122.7619285714285" x2="122.7619285714285" y1="981.1522709390724" y2="1008.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.7972285714285" x2="193.7972285714285" y1="981.1522709390724" y2="1008.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.7972285714285" x2="275.5593285714284" y1="981.1522709390724" y2="981.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.7972285714285" x2="275.5593285714284" y1="1008.629770939072" y2="1008.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.7972285714285" x2="193.7972285714285" y1="981.1522709390724" y2="1008.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.5593285714284" x2="275.5593285714284" y1="981.1522709390724" y2="1008.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.5592285714284" x2="357.3213285714285" y1="981.1522709390724" y2="981.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.5592285714284" x2="357.3213285714285" y1="1008.629770939072" y2="1008.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.5592285714284" x2="275.5592285714284" y1="981.1522709390724" y2="1008.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="357.3213285714285" x2="357.3213285714285" y1="981.1522709390724" y2="1008.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="51.32142857142844" x2="122.7614285714285" y1="1008.629690939072" y2="1008.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="51.32142857142844" x2="122.7614285714285" y1="1036.107190939072" y2="1036.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="51.32142857142844" x2="51.32142857142844" y1="1008.629690939072" y2="1036.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="122.7614285714285" x2="122.7614285714285" y1="1008.629690939072" y2="1036.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="122.7619285714285" x2="193.7972285714285" y1="1008.629690939072" y2="1008.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="122.7619285714285" x2="193.7972285714285" y1="1036.107190939072" y2="1036.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="122.7619285714285" x2="122.7619285714285" y1="1008.629690939072" y2="1036.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.7972285714285" x2="193.7972285714285" y1="1008.629690939072" y2="1036.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.7972285714285" x2="275.5593285714284" y1="1008.629690939072" y2="1008.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.7972285714285" x2="275.5593285714284" y1="1036.107190939072" y2="1036.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.7972285714285" x2="193.7972285714285" y1="1008.629690939072" y2="1036.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.5593285714284" x2="275.5593285714284" y1="1008.629690939072" y2="1036.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.5592285714284" x2="357.3213285714285" y1="1008.629690939072" y2="1008.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.5592285714284" x2="357.3213285714285" y1="1036.107190939072" y2="1036.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.5592285714284" x2="275.5592285714284" y1="1008.629690939072" y2="1036.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="357.3213285714285" x2="357.3213285714285" y1="1008.629690939072" y2="1036.107190939072"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,198.374,959.386) scale(1,1) translate(-1.25847e-14,1.05311e-13)" writing-mode="lr" x="56.68" xml:space="preserve" y="965.39" zvalue="10">参考图号      HongHai-01-2019</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,128.899,996.755) scale(1,1) translate(-5.89785e-14,-1.53244e-12)" writing-mode="lr" x="66.40000000000001" xml:space="preserve" y="1002.75" zvalue="11">制图             </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,272.202,997.755) scale(1,1) translate(1.35558e-13,-1.53399e-12)" writing-mode="lr" x="203.5" xml:space="preserve" y="1003.75" zvalue="12">绘制日期    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,79.9923,1026.31) scale(1,1) translate(-5.66449e-14,-1.57838e-12)" writing-mode="lr" x="79.98999999999999" xml:space="preserve" y="1032.31" zvalue="13">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="18" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,235.336,1024.31) scale(1,1) translate(0,1.1252e-13)" writing-mode="lr" x="198.67" xml:space="preserve" y="1030.31" zvalue="14">更新日期    </text>
  <line fill="none" id="17" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="50.60611355802337" x2="355.2173736116425" y1="625.6445306693694" y2="625.6445306693694" zvalue="15"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,93.6707,642.686) scale(1,1) translate(5.11146e-15,-1.38553e-13)" writing-mode="lr" x="93.67070806204492" xml:space="preserve" y="647.1863811688671" zvalue="17">危险点(双击编辑）：</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="174.1071428571429" y2="174.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="200.1071428571429" y2="200.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="174.1071428571429" y2="200.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="174.1071428571429" y2="200.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="174.1071428571429" y2="174.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="200.1071428571429" y2="200.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="174.1071428571429" y2="200.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="174.1071428571429" y2="200.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="200.1071428571429" y2="200.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="224.3571428571429" y2="224.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="200.1071428571429" y2="224.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="200.1071428571429" y2="224.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="200.1071428571429" y2="200.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="224.3571428571429" y2="224.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="200.1071428571429" y2="224.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="200.1071428571429" y2="224.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="224.3571428571429" y2="224.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="247.1071428571429" y2="247.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="224.3571428571429" y2="247.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="224.3571428571429" y2="247.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="224.3571428571429" y2="224.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="247.1071428571429" y2="247.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="224.3571428571429" y2="247.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="224.3571428571429" y2="247.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="247.1071428571429" y2="247.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="269.8571428571429" y2="269.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="247.1071428571429" y2="269.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="247.1071428571429" y2="269.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="247.1071428571429" y2="247.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="269.8571428571429" y2="269.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="247.1071428571429" y2="269.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="247.1071428571429" y2="269.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="269.8571428571429" y2="269.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="292.6071428571429" y2="292.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="269.8571428571429" y2="292.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="269.8571428571429" y2="292.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="269.8571428571429" y2="269.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="292.6071428571429" y2="292.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="269.8571428571429" y2="292.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="269.8571428571429" y2="292.6071428571429"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,50.3214,188.107) scale(1,1) translate(0,0)" writing-mode="lr" x="50.32" xml:space="preserve" y="193.61" zvalue="28">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236.571,188.107) scale(1,1) translate(0,0)" writing-mode="lr" x="236.57" xml:space="preserve" y="193.61" zvalue="29">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.0089,212.357) scale(1,1) translate(0,0)" writing-mode="lr" x="54.01" xml:space="preserve" y="216.86" zvalue="30">35kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,850.125,444.75) scale(1,1) translate(0,0)" writing-mode="lr" x="850.13" xml:space="preserve" y="449.25" zvalue="43">35kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,692.024,193.644) scale(1,1) translate(0,0)" writing-mode="lr" x="692.02" xml:space="preserve" y="198.14" zvalue="44">35kV红海硅厂Ⅰ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,712.119,355.25) scale(1,1) translate(0,0)" writing-mode="lr" x="712.12" xml:space="preserve" y="359.75" zvalue="45">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,567,544.371) scale(1,1) translate(0,0)" writing-mode="lr" x="567" xml:space="preserve" y="548.87" zvalue="50">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,744.148,436.865) scale(1,1) translate(0,0)" writing-mode="lr" x="744.15" xml:space="preserve" y="441.37" zvalue="52">361</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,770.962,541.743) scale(1,1) translate(0,0)" writing-mode="lr" x="770.96" xml:space="preserve" y="546.24" zvalue="56">301</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,730.497,748.24) scale(1,1) translate(0,0)" writing-mode="lr" x="730.4974361175146" xml:space="preserve" y="752.7403846153845" zvalue="66">#1炉变 12500kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,864.654,536.743) scale(1,1) translate(0,0)" writing-mode="lr" x="864.65" xml:space="preserve" y="541.24" zvalue="69">363</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" x="847.515625" xml:space="preserve" y="702.6887015195994" zvalue="70">#1无功补偿 </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="847.515625" xml:space="preserve" y="718.6887015195994" zvalue="70">8400kVar</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,953.99,727.51) scale(1,1) translate(0,0)" writing-mode="lr" x="953.9903848354634" xml:space="preserve" y="732.0096153846154" zvalue="75">#1动力变3150kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,995.212,541.935) scale(1,1) translate(0,0)" writing-mode="lr" x="995.21" xml:space="preserve" y="546.4400000000001" zvalue="76">3021</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1484.88,446) scale(1,1) translate(0,0)" writing-mode="lr" x="1484.88" xml:space="preserve" y="450.5" zvalue="80">35kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1327.02,194.644) scale(1,1) translate(0,0)" writing-mode="lr" x="1327.02" xml:space="preserve" y="199.14" zvalue="82">35kV红海硅厂Ⅱ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1348.12,355.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1348.12" xml:space="preserve" y="359.75" zvalue="84">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1203,544.371) scale(1,1) translate(0,0)" writing-mode="lr" x="1203" xml:space="preserve" y="548.87" zvalue="87">3902</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1380.15,436.865) scale(1,1) translate(0,0)" writing-mode="lr" x="1380.15" xml:space="preserve" y="441.37" zvalue="89">362</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1406.96,541.743) scale(1,1) translate(0,0)" writing-mode="lr" x="1406.96" xml:space="preserve" y="546.24" zvalue="91">303</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1373.5,750.74) scale(1,1) translate(0,0)" writing-mode="lr" x="1373.497436117515" xml:space="preserve" y="755.2403846153845" zvalue="99">#2炉变 12500kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1500.65,536.743) scale(1,1) translate(0,0)" writing-mode="lr" x="1500.65" xml:space="preserve" y="541.24" zvalue="103">364</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1479.12,704.202) scale(1,1) translate(0,0)" writing-mode="lr" x="1479.125" xml:space="preserve" y="708.7019234804006" zvalue="105">#2无功补偿8400kVar</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1593.24,727.51) scale(1,1) translate(-6.84999e-13,0)" writing-mode="lr" x="1593.240384835463" xml:space="preserve" y="732.0096153846154" zvalue="109">#2动力变1250kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1631.21,541.935) scale(1,1) translate(0,0)" writing-mode="lr" x="1631.21" xml:space="preserve" y="546.4400000000001" zvalue="111">3042</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,246.009,212.357) scale(1,1) translate(0,0)" writing-mode="lr" x="246.01" xml:space="preserve" y="216.86" zvalue="122">35kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,181.068,331.091) scale(1,1) translate(0,0)" writing-mode="lr" x="181.07" xml:space="preserve" y="335.59" zvalue="142">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,286.068,331.091) scale(1,1) translate(0,0)" writing-mode="lr" x="286.07" xml:space="preserve" y="335.59" zvalue="143">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="115" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,155.992,1026.31) scale(1,1) translate(-1.41022e-13,-1.57838e-12)" writing-mode="lr" x="155.99" xml:space="preserve" y="1032.31" zvalue="148">杨立超</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="40" y="321" zvalue="144"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="36">
   <path class="kv35" d="M 549 474.5 L 1013.75 474.5" stroke-width="4" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674241544197" ObjectName="35kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674241544197"/></metadata>
  <path d="M 549 474.5 L 1013.75 474.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv35" d="M 1185 474.5 L 1649.75 474.5" stroke-width="4" zvalue="79"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674241609732" ObjectName="35kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674241609732"/></metadata>
  <path d="M 1185 474.5 L 1649.75 474.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="118">
   <use class="kv35" height="30" transform="rotate(0,702.173,339.067) scale(1.53764,1.1276) translate(-241.484,-36.4554)" width="15" x="690.6409236598054" xlink:href="#Disconnector:刀闸_0" y="322.1527269865601" zvalue="44"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449771732998" ObjectName="35kV红海硅厂Ⅰ回线3616隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449771732998"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,702.173,339.067) scale(1.53764,1.1276) translate(-241.484,-36.4554)" width="15" x="690.6409236598054" y="322.1527269865601"/></g>
  <g id="40">
   <use class="kv35" height="30" transform="rotate(0,615.392,538.468) scale(1.45225,1.06499) translate(-188.25,-31.8824)" width="15" x="604.5" xlink:href="#Disconnector:刀闸_0" y="522.4927520751953" zvalue="49"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449771601925" ObjectName="35kVⅠ段母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449771601925"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,615.392,538.468) scale(1.45225,1.06499) translate(-188.25,-31.8824)" width="15" x="604.5" y="522.4927520751953"/></g>
  <g id="74">
   <use class="kv35" height="36" transform="rotate(0,947.34,542.166) scale(1.28205,1.28205) translate(-206.44,-114.2)" width="14" x="938.3653846153846" xlink:href="#Disconnector:联体小车刀闸2_0" y="519.0889076819786" zvalue="75"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449771995141" ObjectName="#1动力变3021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449771995141"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,947.34,542.166) scale(1.28205,1.28205) translate(-206.44,-114.2)" width="14" x="938.3653846153846" y="519.0889076819786"/></g>
  <g id="111">
   <use class="kv35" height="30" transform="rotate(0,1338.17,339.067) scale(1.53764,1.1276) translate(-463.863,-36.4554)" width="15" x="1326.640923659805" xlink:href="#Disconnector:刀闸_0" y="322.1527269865601" zvalue="83"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449772453893" ObjectName="35kV红海硅厂Ⅱ回线3626隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449772453893"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1338.17,339.067) scale(1.53764,1.1276) translate(-463.863,-36.4554)" width="15" x="1326.640923659805" y="322.1527269865601"/></g>
  <g id="110">
   <use class="kv35" height="30" transform="rotate(0,1251.39,538.468) scale(1.45225,1.06499) translate(-386.31,-31.8824)" width="15" x="1240.5" xlink:href="#Disconnector:刀闸_0" y="522.4927520751953" zvalue="85"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449772388357" ObjectName="35kVⅡ段母线电压互感器3902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449772388357"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1251.39,538.468) scale(1.45225,1.06499) translate(-386.31,-31.8824)" width="15" x="1240.5" y="522.4927520751953"/></g>
  <g id="91">
   <use class="kv35" height="36" transform="rotate(0,1583.34,542.166) scale(1.28205,1.28205) translate(-346.36,-114.2)" width="14" x="1574.365384615385" xlink:href="#Disconnector:联体小车刀闸2_0" y="519.0889076819786" zvalue="108"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449772060677" ObjectName="#2动力变3042隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449772060677"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1583.34,542.166) scale(1.28205,1.28205) translate(-346.36,-114.2)" width="14" x="1574.365384615385" y="519.0889076819786"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="41">
   <use class="kv35" height="40" transform="rotate(0,615.481,629.392) scale(1.40708,1.40708) translate(-169.923,-173.947)" width="40" x="587.3390934679929" xlink:href="#Accessory:线路PT11带避雷器_0" y="601.25" zvalue="50"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449771667461" ObjectName="35kVⅠ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,615.481,629.392) scale(1.40708,1.40708) translate(-169.923,-173.947)" width="40" x="587.3390934679929" y="601.25"/></g>
  <g id="109">
   <use class="kv35" height="40" transform="rotate(0,1250.71,629.392) scale(1.40708,1.40708) translate(-353.7,-173.947)" width="40" x="1222.569862698762" xlink:href="#Accessory:线路PT11带避雷器_0" y="601.25" zvalue="86"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449772322821" ObjectName="35kVⅡ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1250.71,629.392) scale(1.40708,1.40708) translate(-353.7,-173.947)" width="40" x="1222.569862698762" y="601.25"/></g>
 </g>
 <g id="BreakerClass">
  <g id="45">
   <use class="kv35" height="20" transform="rotate(0,702.258,426.365) scale(3.25,3.25) translate(-474.929,-272.676)" width="10" x="686.0081637994472" xlink:href="#Breaker:小车断路器_0" y="393.8653846153846" zvalue="51"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924505042949" ObjectName="35kV红海硅厂Ⅰ回线361断路器"/>
   <cge:TPSR_Ref TObjectID="6473924505042949"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,702.258,426.365) scale(3.25,3.25) translate(-474.929,-272.676)" width="10" x="686.0081637994472" y="393.8653846153846"/></g>
  <g id="50">
   <use class="kv35" height="20" transform="rotate(0,729.538,537.743) scale(2.75,2.75) translate(-455.502,-324.7)" width="10" x="715.7884615384614" xlink:href="#Breaker:小车断路器_0" y="510.2427520751953" zvalue="55"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924505108485" ObjectName="#1炉变301断路器"/>
   <cge:TPSR_Ref TObjectID="6473924505108485"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,729.538,537.743) scale(2.75,2.75) translate(-455.502,-324.7)" width="10" x="715.7884615384614" y="510.2427520751953"/></g>
  <g id="65">
   <use class="kv35" height="20" transform="rotate(0,840.827,537.743) scale(2.75,2.75) translate(-526.322,-324.7)" width="10" x="827.0769230769231" xlink:href="#Breaker:小车断路器_0" y="510.2427520751953" zvalue="68"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924505174021" ObjectName="#1无功补偿装置363断路器"/>
   <cge:TPSR_Ref TObjectID="6473924505174021"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,840.827,537.743) scale(2.75,2.75) translate(-526.322,-324.7)" width="10" x="827.0769230769231" y="510.2427520751953"/></g>
  <g id="108">
   <use class="kv35" height="20" transform="rotate(0,1338.26,426.365) scale(3.25,3.25) translate(-915.236,-272.676)" width="10" x="1322.008163799447" xlink:href="#Breaker:小车断路器_0" y="393.8653846153846" zvalue="88"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924505370629" ObjectName="35kV红海硅厂Ⅱ回线362断路器"/>
   <cge:TPSR_Ref TObjectID="6473924505370629"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1338.26,426.365) scale(3.25,3.25) translate(-915.236,-272.676)" width="10" x="1322.008163799447" y="393.8653846153846"/></g>
  <g id="107">
   <use class="kv35" height="20" transform="rotate(0,1365.54,537.743) scale(2.75,2.75) translate(-860.229,-324.7)" width="10" x="1351.788461538461" xlink:href="#Breaker:小车断路器_0" y="510.2427520751953" zvalue="90"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924505305093" ObjectName="#2炉变303断路器"/>
   <cge:TPSR_Ref TObjectID="6473924505305093"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1365.54,537.743) scale(2.75,2.75) translate(-860.229,-324.7)" width="10" x="1351.788461538461" y="510.2427520751953"/></g>
  <g id="97">
   <use class="kv35" height="20" transform="rotate(0,1476.83,537.743) scale(2.75,2.75) translate(-931.049,-324.7)" width="10" x="1463.076923076923" xlink:href="#Breaker:小车断路器_0" y="510.2427520751953" zvalue="101"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924505239557" ObjectName="#2无功补偿装置364断路器"/>
   <cge:TPSR_Ref TObjectID="6473924505239557"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1476.83,537.743) scale(2.75,2.75) translate(-931.049,-324.7)" width="10" x="1463.076923076923" y="510.2427520751953"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="55">
   <path class="kv35" d="M 702.26 396.3 L 702.26 355.69" stroke-width="1" zvalue="58"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@0" LinkObjectIDznd="118@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 702.26 396.3 L 702.26 355.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="kv35" d="M 702.26 455.62 L 702.26 474.5" stroke-width="1" zvalue="59"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@1" LinkObjectIDznd="36@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 702.26 455.62 L 702.26 474.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="kv35" d="M 615.52 523.02 L 615.52 474.5" stroke-width="1" zvalue="60"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="40@0" LinkObjectIDznd="36@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 615.52 523.02 L 615.52 474.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv35" d="M 615.48 602.54 L 615.48 554.17" stroke-width="1" zvalue="61"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41@0" LinkObjectIDznd="40@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 615.48 602.54 L 615.48 554.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="kv35" d="M 729.54 512.31 L 729.54 474.5" stroke-width="1" zvalue="62"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="50@0" LinkObjectIDznd="36@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 729.54 512.31 L 729.54 474.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="kv35" d="M 702.31 322.71 L 702.31 280.01" stroke-width="1" zvalue="64"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="118@0" LinkObjectIDznd="39@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 702.31 322.71 L 702.31 280.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="kv35" d="M 729.54 652.69 L 729.54 562.49" stroke-width="1" zvalue="66"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="62@0" LinkObjectIDznd="50@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 729.54 652.69 L 729.54 562.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="kv35" d="M 840.83 512.31 L 840.83 474.5" stroke-width="1" zvalue="70"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@0" LinkObjectIDznd="36@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 840.83 512.31 L 840.83 474.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="kv35" d="M 840.83 634.6 L 840.83 562.49" stroke-width="1" zvalue="72"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@0" LinkObjectIDznd="65@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 840.83 634.6 L 840.83 562.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="kv35" d="M 946.87 632.69 L 946.87 563.96" stroke-width="1" zvalue="76"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="72@0" LinkObjectIDznd="74@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 946.87 632.69 L 946.87 563.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="kv35" d="M 947.34 520.37 L 947.34 474.5" stroke-width="1" zvalue="77"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="74@0" LinkObjectIDznd="36@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 947.34 520.37 L 947.34 474.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="106">
   <path class="kv35" d="M 1338.25 396.3 L 1338.26 355.69" stroke-width="1" zvalue="92"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@0" LinkObjectIDznd="111@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1338.25 396.3 L 1338.26 355.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="105">
   <path class="kv35" d="M 1338.26 455.62 L 1338.26 474.5" stroke-width="1" zvalue="93"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@1" LinkObjectIDznd="113@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1338.26 455.62 L 1338.26 474.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="104">
   <path class="kv35" d="M 1251.52 523.02 L 1251.52 474.5" stroke-width="1" zvalue="94"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="110@0" LinkObjectIDznd="113@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1251.52 523.02 L 1251.52 474.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="103">
   <path class="kv35" d="M 1250.71 602.54 L 1250.71 554.17" stroke-width="1" zvalue="95"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109@0" LinkObjectIDznd="110@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1250.71 602.54 L 1250.71 554.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="102">
   <path class="kv35" d="M 1365.54 512.31 L 1365.54 474.5" stroke-width="1" zvalue="96"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@0" LinkObjectIDznd="113@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1365.54 512.31 L 1365.54 474.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv35" d="M 1338.31 322.71 L 1338.31 281.01" stroke-width="1" zvalue="97"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="111@0" LinkObjectIDznd="112@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1338.31 322.71 L 1338.31 281.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="kv35" d="M 1365.54 652.69 L 1365.54 562.49" stroke-width="1" zvalue="100"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="107@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1365.54 652.69 L 1365.54 562.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="kv35" d="M 1476.83 512.31 L 1476.83 474.5" stroke-width="1" zvalue="104"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="97@0" LinkObjectIDznd="113@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1476.83 512.31 L 1476.83 474.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="94">
   <path class="kv35" d="M 1476.06 634.6 L 1476.06 562.49" stroke-width="1" zvalue="106"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="96@0" LinkObjectIDznd="97@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1476.06 634.6 L 1476.06 562.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="kv35" d="M 1582.87 632.69 L 1582.87 563.96" stroke-width="1" zvalue="110"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="93@0" LinkObjectIDznd="91@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1582.87 632.69 L 1582.87 563.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="kv35" d="M 1583.34 520.37 L 1583.34 474.5" stroke-width="1" zvalue="112"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="91@0" LinkObjectIDznd="113@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1583.34 520.37 L 1583.34 474.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="62">
   <use class="kv35" height="30" transform="rotate(0,729.649,685.212) scale(2.20513,2.20513) translate(-387.915,-356.399)" width="18" x="709.802564102564" xlink:href="#EnergyConsumer:接地两卷变2_0" y="652.1346153846155" zvalue="65"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449771798534" ObjectName="#1炉变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,729.649,685.212) scale(2.20513,2.20513) translate(-387.915,-356.399)" width="18" x="709.802564102564" y="652.1346153846155"/></g>
  <g id="72">
   <use class="kv35" height="30" transform="rotate(0,946.981,665.212) scale(2.20513,2.20513) translate(-506.69,-345.469)" width="18" x="927.1346153846155" xlink:href="#EnergyConsumer:接地两卷变2_0" y="632.1346153846155" zvalue="74"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449771929605" ObjectName="#1动力变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,946.981,665.212) scale(2.20513,2.20513) translate(-506.69,-345.469)" width="18" x="927.1346153846155" y="632.1346153846155"/></g>
  <g id="99">
   <use class="kv35" height="30" transform="rotate(0,1365.65,685.212) scale(2.20513,2.20513) translate(-735.497,-356.399)" width="18" x="1345.802564102564" xlink:href="#EnergyConsumer:接地两卷变2_0" y="652.1346153846155" zvalue="98"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449772257285" ObjectName="#2炉变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1365.65,685.212) scale(2.20513,2.20513) translate(-735.497,-356.399)" width="18" x="1345.802564102564" y="652.1346153846155"/></g>
  <g id="93">
   <use class="kv35" height="30" transform="rotate(0,1582.98,665.212) scale(2.20513,2.20513) translate(-854.271,-345.469)" width="18" x="1563.134615384615" xlink:href="#EnergyConsumer:接地两卷变2_0" y="632.1346153846155" zvalue="107"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449772126213" ObjectName="#2动力变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1582.98,665.212) scale(2.20513,2.20513) translate(-854.271,-345.469)" width="18" x="1563.134615384615" y="632.1346153846155"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="67">
   <use class="kv35" height="30" transform="rotate(0,840.827,653.673) scale(2.46154,2.46154) translate(-477.318,-366.195)" width="30" x="803.9038461538462" xlink:href="#Compensator:并联电容器4_0" y="616.75" zvalue="69"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449771864069" ObjectName="#1无功补偿"/>
   <cge:TPSR_Ref TObjectID="6192449771864069"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,840.827,653.673) scale(2.46154,2.46154) translate(-477.318,-366.195)" width="30" x="803.9038461538462" y="616.75"/></g>
  <g id="96">
   <use class="kv35" height="30" transform="rotate(0,1476.06,653.673) scale(2.46154,2.46154) translate(-854.486,-366.195)" width="30" x="1439.134615384615" xlink:href="#Compensator:并联电容器4_0" y="616.75" zvalue="102"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449772191749" ObjectName="#2无功补偿"/>
   <cge:TPSR_Ref TObjectID="6192449772191749"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1476.06,653.673) scale(2.46154,2.46154) translate(-854.486,-366.195)" width="30" x="1439.134615384615" y="616.75"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="31">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="31" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,502,497.167) scale(1,1) translate(0,0)" writing-mode="lr" x="501.53" xml:space="preserve" y="501.94" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125060673541" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="32">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="32" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1663.56,450.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1663.09" xml:space="preserve" y="455.28" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125069979653" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="34">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="34" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,115.571,209.968) scale(1,1) translate(0,0)" writing-mode="lr" x="115.72" xml:space="preserve" y="216.4" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125060870149" ObjectName="F"/>
   </metadata>
  </g>
  <g id="33">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="33" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,115.571,186) scale(1,1) translate(0,0)" writing-mode="lr" x="115.72" xml:space="preserve" y="192.43" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125076926469" ObjectName=""/>
   </metadata>
  </g>
  <g id="35">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="35" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,292.571,185.857) scale(1,1) translate(0,0)" writing-mode="lr" x="292.72" xml:space="preserve" y="192.29" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125076992005" ObjectName=""/>
   </metadata>
  </g>
  <g id="54">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="54" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,299.571,209.968) scale(1,1) translate(0,0)" writing-mode="lr" x="299.72" xml:space="preserve" y="216.4" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125070176261" ObjectName="F"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="427">
   <use height="30" transform="rotate(0,316.17,331.483) scale(0.708333,0.665547) translate(125.813,161.562)" width="30" x="305.54" xlink:href="#State:红绿圆(方形)_0" y="321.5" zvalue="145"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,316.17,331.483) scale(0.708333,0.665547) translate(125.813,161.562)" width="30" x="305.54" y="321.5"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,220.545,331.483) scale(0.708333,0.665547) translate(86.4375,161.562)" width="30" x="209.92" xlink:href="#State:红绿圆(方形)_0" y="321.5" zvalue="146"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,220.545,331.483) scale(0.708333,0.665547) translate(86.4375,161.562)" width="30" x="209.92" y="321.5"/></g>
  <g id="830">
   <use height="30" transform="rotate(0,319.812,148.464) scale(1.27778,1.03333) translate(-57.0245,-4.28916)" width="90" x="262.31" xlink:href="#State:全站检修_0" y="132.96" zvalue="152"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549585575938" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,319.812,148.464) scale(1.27778,1.03333) translate(-57.0245,-4.28916)" width="90" x="262.31" y="132.96"/></g>
 </g>
</svg>