<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549680340993" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Accessory:避雷器带指示灯_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="3"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="5" y2="3"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.83333333333333" x2="10.83333333333333" y1="17.75" y2="20.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.83333333333334" x2="18.83333333333334" y1="20.75" y2="18.33333333333334"/>
   <ellipse cx="18.83" cy="16" fill-opacity="0" rx="2.24" ry="2.24" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.83333333333333" x2="18.83333333333334" y1="4.75" y2="4.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.83333333333333" x2="18.83333333333334" y1="20.75" y2="20.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.83333333333333" x2="14.83333333333333" y1="23.08333333333334" y2="20.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.08333333333333" x2="16.08333333333334" y1="25.25" y2="25.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.08333333333333" x2="17.08333333333334" y1="23.25" y2="23.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.58333333333333" x2="16.58333333333334" y1="24.25" y2="24.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.16666666666667" x2="17.5" y1="14.33333333333333" y2="17.75"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.83,13.25) scale(1,1) translate(0,0)" width="6" x="7.83" y="8.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.83333333333333" x2="20.83333333333334" y1="11.75" y2="11.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.83333333333334" x2="18.83333333333334" y1="4.75" y2="9.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.83333333333333" x2="20.83333333333334" y1="9.75" y2="9.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.83333333333334" x2="18.83333333333334" y1="11.75" y2="13.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.833333333333334" x2="10.83333333333333" y1="13.75" y2="14.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.41666666666667" x2="20.41666666666667" y1="14.33333333333333" y2="17.33333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.83333333333333" x2="11.83333333333333" y1="14.75" y2="13.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.83333333333333" x2="10.83333333333333" y1="4.75" y2="14.75"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷带壁雷器_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="28.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="11.75" y1="17.75" y2="20.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="11" y1="14.75" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="12" y1="11.75" y2="11.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="9" y1="12.75" y2="12.75"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,11.04,18.25) scale(1,1) translate(0,0)" width="3.25" x="9.42" y="14.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.5" y1="10.83333333333333" y2="10.83333333333333"/>
   <path d="M 5.025 2.775 L 4.16667 9.5 L 5.025 7.60833 L 5.91667 9.5 L 5.025 2.775" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 5 23.75 L 11 23.75 L 11 17.75 L 10.25 20.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.000411522633746" x2="5.000411522633746" y1="7.627914951989029" y2="28.23902606310013"/>
  </symbol>
  <symbol id="Compensator:西郊变电容_0" viewBox="0,0,24,40">
   <use terminal-index="0" type="0" x="12" xlink:href="#terminal" y="2.449999999999999"/>
   <rect fill-opacity="0" height="5.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,12,23) scale(1,1) translate(0,0)" width="3" x="10.5" y="20.17"/>
   <path d="M 12 17.85 L 17.85 17.85 L 17.85 22.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="6.5" y1="33.10833333333333" y2="33.10833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.471296296296289" x2="6.471296296296289" y1="30.88611111111111" y2="33.12685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.50833333333333" x2="6.50833333333333" y1="20.10833333333333" y2="17.90462962962963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="29.60833333333333" y2="33.12685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="22.60833333333333" y2="28.60833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="17.79351851851851" y2="22.525"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.94090594744122" x2="6.94090594744122" y1="36.94166666666666" y2="34.94166666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.416666666666666" x2="12.00833333333334" y1="17.85833333333333" y2="17.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="33.10833333333333" y2="37.10833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.09166666666667" x2="17.09166666666667" y1="34.94166666666666" y2="36.94166666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.758333333333324" x2="2.758333333333324" y1="19.85833333333333" y2="31.60833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.08333333333333" x2="7.000000000000002" y1="37.10833333333333" y2="37.10833333333333"/>
   <path d="M 6.50833 23.7072 A 2.96392 1.81747 180 0 1 6.50833 20.0723" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 6.50833 27.3421 A 2.96392 1.81747 180 0 1 6.50833 23.7072" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 6.50833 30.8771 A 2.96392 1.81747 180 0 1 6.50833 27.2421" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.50833333333333" x2="10.50833333333333" y1="29.60833333333333" y2="29.60833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.50833333333333" x2="10.50833333333333" y1="28.60833333333333" y2="28.60833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12" x2="12" y1="2.25" y2="4.916666666666668"/>
   <path d="M 7.26667 9.85 A 4.91667 4.75 -450 1 0 12.0167 4.93333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 7.33333 9.83333 L 12 9.91667 L 12 18" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="20.56666666666668" y2="21.08015580397416"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.28450340888154" x2="18.47116270499357" y1="26.7156109584975" y2="26.7156109584975"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510778" x2="17.14262023217246" y1="23.75922956383932" y2="22.95550743587977"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.50700202690252" x2="18.32283029297954" y1="27.0857461490052" y2="27.0857461490052"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="25.36667381975841" y2="26.33114037330986"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.98783858485353" x2="18.76782752902158" y1="26.34547576798984" y2="26.34547576798984"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="21.08015580397418" y2="23.73243882624067"/>
   <rect fill-opacity="0" height="4.29" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,17.83,23.22) scale(1,1) translate(0,0)" width="2.33" x="16.67" y="21.08"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510781" x2="18.53565505804314" y1="23.75922956383932" y2="22.95550743587977"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT带保险_0" viewBox="0,0,11,29">
   <use terminal-index="0" type="0" x="5.5" xlink:href="#terminal" y="0.1666666666666661"/>
   <rect fill-opacity="0" height="6" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.5,7.5) scale(1,1) translate(0,0)" width="4" x="3.5" y="4.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="13.16666666666667" y2="0.5"/>
   <ellipse cx="5.4" cy="18.1" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.4" cy="23.78" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:线路PT三绕组_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="20" xlink:href="#terminal" y="0.9166666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32" x2="20" y1="5" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.91666666666667" x2="22.91666666666667" y1="24" y2="24"/>
   <rect fill-opacity="0" height="10" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,20,11) scale(1,1) translate(0,0)" width="6" x="17" y="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32" x2="32" y1="17" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="31.98040845230975" x2="31.98040845230975" y1="16.76788570496156" y2="23.53635804601289"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.98821300514558" x2="19.98821300514558" y1="1.029523490692871" y2="18.75"/>
   <path d="M 30.0147 23.4481 L 34.2865 23.4481 L 32.115 28.6155 L 30.0147 23.4481" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <rect fill-opacity="0" height="6.05" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,32.04,24.56) scale(1,1) translate(0,0)" width="15.34" x="24.37" y="21.53"/>
   <ellipse cx="19.76" cy="32.23" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.76" cy="24.17" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.22179295459144" x2="32.22179295459144" y1="32.22550978083666" y2="36.2625308385742"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="29.01788570496155" x2="35.42570020422134" y1="36.23023467011234" y2="36.23023467011234"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="30.26384963537316" x2="34.53572596821302" y1="37.52208140858838" y2="37.52208140858838"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.15382387138148" x2="33.11176719059974" y1="38.49096646244535" y2="38.49096646244535"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.91666666666667" x2="22.91666666666667" y1="32.66666666666667" y2="32.66666666666667"/>
   <ellipse cx="12.01" cy="28.48" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.166666666666664" x2="15.16666666666666" y1="28.91666666666667" y2="28.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀-带电显示_0" viewBox="0,0,21,29">
   <use terminal-index="0" type="0" x="10.57144275301999" xlink:href="#terminal" y="0.9988473696347349"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.5" x2="4.5" y1="4.5" y2="4.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="4.5" y1="4.5" y2="8.5"/>
   <ellipse cx="4.5" cy="16.25" fill-opacity="0" rx="2.83" ry="2.83" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="4.5" y1="11" y2="13.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="4.5" y1="21.5" y2="19.08333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="1.15" y2="4.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.367489181242" x2="16.367489181242" y1="20.5" y2="21.5"/>
   <path d="M 2.25 14.4167 L 6.58333 18" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.367489181242" x2="16.367489181242" y1="20.41666666666667" y2="18.01429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="21.35" y2="24.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="6" y1="8.5" y2="8.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.166666666666666" x2="13.5" y1="24.24694619969017" y2="24.24694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="8.583333333333334" x2="12.5" y1="26.08157590710536" y2="26.08157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.274021844661625" x2="11.93079938068318" y1="27.83348701738202" y2="27.83348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.16666666666667" x2="16.33141025641025" y1="8.166666666666666" y2="18.0142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.66666666666667" x2="18" y1="8.04249548976499" y2="8.04249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.367489181242" x2="16.367489181242" y1="8.000000000000004" y2="4.416666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.41666666666666" x2="4.416666666666666" y1="21.41666666666666" y2="21.41666666666666"/>
   <path d="M 6.66667 14.25 L 2.41667 18.3333" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="6" y1="10.91666666666667" y2="10.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀-带电显示_1" viewBox="0,0,21,29">
   <use terminal-index="0" type="0" x="10.57144275301999" xlink:href="#terminal" y="0.9988473696347349"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.166666666666666" x2="13.5" y1="24.24694619969017" y2="24.24694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="8.583333333333334" x2="12.5" y1="26.08157590710536" y2="26.08157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.274021844661625" x2="11.93079938068318" y1="27.83348701738202" y2="27.83348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.367489181242" x2="16.367489181242" y1="8.166666666666666" y2="20.41666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.367489181242" x2="16.367489181242" y1="8.000000000000004" y2="4.416666666666668"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.66666666666667" x2="18" y1="8.04249548976499" y2="8.04249548976499"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="4.5" y1="21.5" y2="19.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.367489181242" x2="16.367489181242" y1="20.41666666666667" y2="18.01429160524171"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="1.15" y2="4.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.367489181242" x2="16.367489181242" y1="20.5" y2="21.5"/>
   <ellipse cx="4.5" cy="16.25" fill-opacity="0" rx="2.83" ry="2.83" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.5" x2="4.5" y1="4.5" y2="4.5"/>
   <path d="M 2.25 14.4167 L 6.58333 18" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="4.5" y1="11" y2="13.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="6" y1="8.5" y2="8.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="4.5" y1="4.5" y2="8.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="21.35" y2="24.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.367489181242" x2="16.367489181242" y1="8.000000000000004" y2="4.416666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="6" y1="10.91666666666667" y2="10.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.41666666666666" x2="4.416666666666666" y1="21.41666666666666" y2="21.41666666666666"/>
   <path d="M 6.66667 14.25 L 2.41667 18.3333" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀-带电显示_2" viewBox="0,0,21,29">
   <use terminal-index="0" type="0" x="10.57144275301999" xlink:href="#terminal" y="0.9988473696347349"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="19.75" x2="12.75" y1="8.5" y2="17.75"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.367489181242" x2="16.367489181242" y1="20.41666666666667" y2="18.01429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.166666666666666" x2="13.5" y1="24.24694619969017" y2="24.24694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="8.583333333333334" x2="12.5" y1="26.08157590710536" y2="26.08157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.274021844661625" x2="11.93079938068318" y1="27.83348701738202" y2="27.83348701738202"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.75" x2="20" y1="8.383333333333333" y2="17.8"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.367489181242" x2="16.367489181242" y1="8.000000000000004" y2="4.416666666666668"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.66666666666667" x2="18" y1="8.04249548976499" y2="8.04249548976499"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="4.5" y1="21.5" y2="19.08333333333333"/>
   <ellipse cx="4.5" cy="16.25" fill-opacity="0" rx="2.83" ry="2.83" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="1.15" y2="4.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="21.35" y2="24.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.5" x2="4.5" y1="4.5" y2="4.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.367489181242" x2="16.367489181242" y1="20.5" y2="21.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="4.5" y1="4.5" y2="8.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="6" y1="8.5" y2="8.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="4.5" y1="11" y2="13.5"/>
   <path d="M 2.25 14.4167 L 6.58333 18" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.41666666666666" x2="4.416666666666666" y1="21.41666666666666" y2="21.41666666666666"/>
   <path d="M 6.66667 14.25 L 2.41667 18.3333" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="6" y1="10.91666666666667" y2="10.91666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id=":线路带避雷器_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15.16666666666667" xlink:href="#terminal" y="39.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.16022336769755" x2="15.16022336769755" y1="39.75" y2="0.8333333333333321"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="28.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="6.5" y1="33.25" y2="33.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="8.5" y1="31.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="7.5" y1="32.25" y2="32.25"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6.04,21.17) scale(1,1) translate(0,0)" width="6.08" x="3" y="14"/>
   <path d="M 15 9.25 L 6 9.25 L 6 21.25 L 6 21.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="7" y1="22" y2="16"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="5" y1="22" y2="16"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变带熔断器_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.8499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="17.5" y1="27" y2="21.65"/>
   <rect fill-opacity="0" height="4.32" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.06,5.25) scale(1,1) translate(0,0)" width="3.43" x="8.34" y="3.09"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.0235180220435" x2="10.0235180220435" y1="0.5833333333333091" y2="9.290410445610181"/>
   <ellipse cx="9.880000000000001" cy="13.47" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.94" cy="19.46" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="18.1368007916835" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.6" x2="15.39982126899018" y1="25.06619780557639" y2="25.06619780557639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75996425379804" x2="16.23985701519214" y1="25.90207720835499" y2="25.90207720835499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.91992850759607" x2="17.07989276139411" y1="26.73795661113357" y2="26.73795661113357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="17.5" y1="19.80855959724066" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.49991063449509" x2="17.49991063449509" y1="19.83333333333333" y2="24.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.10311503267975" x2="10.10311503267975" y1="29.16666666666667" y2="23.64357429718876"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.01340482573727" x2="10.01340482573727" y1="11.16666666666667" y2="12.83842547222383"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6934763181412" x2="10.01340482573727" y1="14.51018427778098" y2="12.83842547222382"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.333333333333332" x2="10.01340482573726" y1="14.51018427778098" y2="12.83842547222382"/>
  </symbol>
  <symbol id=":20220708_0" viewBox="0,0,15,40">
   <use terminal-index="0" type="0" x="12.75" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.74355670103088" x2="12.74355670103088" y1="39.33333333333334" y2="0.4166666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.083333333333331" x2="5.083333333333331" y1="31.83333333333334" y2="31.83333333333334"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,3.62,20.75) scale(1,1) translate(0,0)" width="6.08" x="0.58" y="13.58"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.083333333333331" x2="6.083333333333331" y1="30.83333333333334" y2="30.83333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.583333333333331" x2="2.583333333333331" y1="21.58333333333333" y2="15.58333333333333"/>
   <path d="M 12.5833 8.83333 L 3.58333 8.83333 L 3.58333 20.8333 L 3.58333 20.8333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.583333333333331" x2="3.583333333333331" y1="27.83333333333334" y2="30.83333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.083333333333331" x2="4.083333333333331" y1="32.83333333333333" y2="32.83333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.583333333333331" x2="4.583333333333331" y1="21.58333333333333" y2="15.58333333333333"/>
  </symbol>
  <symbol id="State:间隔模板_0" viewBox="0,0,80,30">
   <rect Plane="0" fill="none" fill-opacity="0" height="28.8" stroke="rgb(85,170,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,15) scale(1,1) translate(0,0)" width="79.2" x="0.4" y="0.6"/>
  </symbol>
  <symbol id="State:间隔模板_1" viewBox="0,0,80,30">
   <rect Plane="0" fill="none" fill-opacity="0" height="28.8" stroke="rgb(185,185,185)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,15) scale(1,1) translate(0,0)" width="79.2" x="0.4" y="0.6"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV那邦变" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="82.56999999999999" id="1" preserveAspectRatio="xMidYMid slice" width="249.69" x="62.31" xlink:href="logo.png" y="26.43"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="177" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,187.153,67.7136) scale(1,1) translate(-1.38349e-14,0)" writing-mode="lr" x="187.15" xml:space="preserve" y="71.20999999999999" zvalue="10073"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,216,67.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="216" xml:space="preserve" y="76.69" zvalue="10074">35kV那邦变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="143" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,190.531,392.25) scale(1,1) translate(0,0)" width="72.88" x="154.09" y="380.25" zvalue="10286"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,190.531,392.25) scale(1,1) translate(0,0)" writing-mode="lr" x="190.53" xml:space="preserve" y="396.75" zvalue="10286">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="142" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,88.625,392.25) scale(1,1) translate(0,0)" width="72.88" x="52.19" y="380.25" zvalue="10287"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,88.625,392.25) scale(1,1) translate(0,0)" writing-mode="lr" x="88.63" xml:space="preserve" y="396.75" zvalue="10287">AVC功能</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="141" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,88.625,355.75) scale(1,1) translate(0,0)" width="72.88" x="52.19" y="343.75" zvalue="10288"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,88.625,355.75) scale(1,1) translate(0,0)" writing-mode="lr" x="88.63" xml:space="preserve" y="360.25" zvalue="10288">信号一览</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="39" id="140" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,677,105.5) scale(1,1) translate(0,0)" width="266" x="544" y="86" zvalue="10314"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,677,105.5) scale(1,1) translate(0,0)" writing-mode="lr" x="677" xml:space="preserve" y="110" zvalue="10314">35kV昔那线那邦首部T线、厂变T线调度用图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,781.076,365.091) scale(1,1) translate(0,0)" writing-mode="lr" x="781.08" xml:space="preserve" y="369.59" zvalue="7577">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="164" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,610.076,686.535) scale(1,1) translate(0,0)" writing-mode="lr" x="610.08" xml:space="preserve" y="691.04" zvalue="7716">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="134" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1247.98,331.52) scale(1,1) translate(0,0)" writing-mode="lr" x="1247.98" xml:space="preserve" y="336.02" zvalue="8016">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="149" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1296.61,369.394) scale(1,1) translate(0,0)" writing-mode="lr" x="1296.61" xml:space="preserve" y="373.89" zvalue="8030">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="154" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1296.61,318.644) scale(1,1) translate(0,0)" writing-mode="lr" x="1296.61" xml:space="preserve" y="323.14" zvalue="8035">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="442" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,819.587,782.368) scale(1,1) translate(0,0)" writing-mode="lr" x="819.59" xml:space="preserve" y="786.87" zvalue="9394">024</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="219" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1268.75,227.091) scale(1,1) translate(0,0)" writing-mode="lr" x="1268.75" xml:space="preserve" y="231.59" zvalue="9703">35kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="631" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1110.98,440.584) scale(1,1) translate(0,0)" writing-mode="lr" x="1110.98" xml:space="preserve" y="445.08" zvalue="9723">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="629" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1109.55,410.68) scale(1,1) translate(0,0)" writing-mode="lr" x="1109.55" xml:space="preserve" y="415.18" zvalue="9725">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="628" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1161.11,645.542) scale(1,1) translate(0,0)" writing-mode="lr" x="1161.11" xml:space="preserve" y="650.04" zvalue="9729">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="625" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1150.25,677.125) scale(1,1) translate(0,0)" writing-mode="lr" x="1150.25" xml:space="preserve" y="681.63" zvalue="9735">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="823" stroke="rgb(255,255,255)" text-anchor="middle" x="1058.5390625" xml:space="preserve" y="525.8125001589457" zvalue="9740">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="823" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1058.5390625" xml:space="preserve" y="542.7968751589457" zvalue="9740">2500kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="854" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,805.083,676.096) scale(1,1) translate(0,0)" writing-mode="lr" x="805.08" xml:space="preserve" y="680.6" zvalue="9762">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="851" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,781.472,585.667) scale(1,1) translate(0,0)" writing-mode="lr" x="781.47" xml:space="preserve" y="590.17" zvalue="9772">10kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,866.226,739.474) scale(1,1) translate(0,0)" writing-mode="lr" x="866.23" xml:space="preserve" y="743.97" zvalue="9778">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,827.908,877.974) scale(1,1) translate(0,0)" writing-mode="lr" x="827.91" xml:space="preserve" y="882.47" zvalue="9784">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="107" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1099.91,851.474) scale(1,1) translate(0,0)" writing-mode="lr" x="1099.91" xml:space="preserve" y="855.97" zvalue="9859">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="106" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1052.84,782.368) scale(1,1) translate(0,0)" writing-mode="lr" x="1052.84" xml:space="preserve" y="786.87" zvalue="9861">021</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="104" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1099.73,731.474) scale(1,1) translate(0,0)" writing-mode="lr" x="1099.73" xml:space="preserve" y="735.97" zvalue="9869">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1009.15,311.645) scale(1,1) translate(3.32452e-13,0)" writing-mode="lr" x="1009.15" xml:space="preserve" y="316.14" zvalue="9968">321</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1007.92,273.004) scale(1,1) translate(0,3.80313e-13)" writing-mode="lr" x="1007.92" xml:space="preserve" y="277.5" zvalue="9970">8</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,959.526,178.63) scale(1,1) translate(0,0)" writing-mode="lr" x="959.53" xml:space="preserve" y="183.13" zvalue="9972">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1008.42,354.549) scale(1,1) translate(0,0)" writing-mode="lr" x="1008.42" xml:space="preserve" y="359.05" zvalue="9975">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,953.276,256.065) scale(1,1) translate(0,0)" writing-mode="lr" x="953.28" xml:space="preserve" y="260.56" zvalue="10028">87</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1007.92,206.088) scale(1,1) translate(0,0)" writing-mode="lr" x="1007.92" xml:space="preserve" y="210.59" zvalue="10032">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="190" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,872.041,831.245) scale(1,1) translate(0,0)" writing-mode="lr" x="872.04" xml:space="preserve" y="835.75" zvalue="10059">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="191" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,896.791,901.245) scale(1,1) translate(0,0)" writing-mode="lr" x="896.79" xml:space="preserve" y="905.75" zvalue="10061">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="189" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,870.791,795.245) scale(1,1) translate(0,0)" writing-mode="lr" x="870.79" xml:space="preserve" y="799.75" zvalue="10063">17</text>
  <line fill="none" id="174" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="152.8704926140824" y2="152.8704926140824" zvalue="10076"/>
  <line fill="none" id="173" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="380" x2="380" y1="11" y2="1041" zvalue="10077"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="165" y2="165"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="165" y2="165"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="283.5" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="260.75" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="260.75" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="283.5" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="260.75" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="260.75" y2="283.5"/>
  <line fill="none" id="171" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="622.8704926140824" y2="622.8704926140824" zvalue="10079"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="64" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3873" x2="227.3873" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="227.3872" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="345" x2="345" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="64" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3873" x2="227.3873" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="227.3872" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="345" x2="345" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="64" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3873" x2="227.3873" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="227.3872" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="345" x2="345" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="64" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3873" x2="227.3873" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="227.3872" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="345" x2="345" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="64" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3873" x2="227.3873" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="227.3872" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="345" x2="345" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="64" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3873" x2="227.3873" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="227.3872" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="345" x2="345" y1="582" y2="606.6794"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="938" y2="938"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="977.1632999999999" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="938" y2="938"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="977.1632999999999" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="1005.0816" y2="1033"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="165" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,958) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="964" zvalue="10083">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="163" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54,992) scale(1,1) translate(0,0)" writing-mode="lr" x="54" xml:space="preserve" y="998" zvalue="10084">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="162" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,992) scale(1,1) translate(0,0)" writing-mode="lr" x="236" xml:space="preserve" y="998" zvalue="10085">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="161" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,1020) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="1026" zvalue="10086">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="160" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235,1020) scale(1,1) translate(0,0)" writing-mode="lr" x="235" xml:space="preserve" y="1026" zvalue="10087">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="156" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,137.528,465.389) scale(1,1) translate(0,-9.9772e-14)" writing-mode="lr" x="137.5278049045141" xml:space="preserve" y="469.8888914320204" zvalue="10088">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="137" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5,652.5) scale(1,1) translate(0,0)" writing-mode="lr" x="77.5" xml:space="preserve" y="657" zvalue="10090">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="130" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,210.399,319.841) scale(1,1) translate(0,0)" writing-mode="lr" x="210.4" xml:space="preserve" y="324.34" zvalue="10091">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,315.399,319.841) scale(1,1) translate(0,0)" writing-mode="lr" x="315.4" xml:space="preserve" y="324.34" zvalue="10092">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="103" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,255.931,463.833) scale(1,1) translate(0,9.94266e-14)" writing-mode="lr" x="255.9305758608714" xml:space="preserve" y="468.3333358830876" zvalue="10093">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,89,495.75) scale(1,1) translate(0,0)" writing-mode="lr" x="89" xml:space="preserve" y="500.25" zvalue="10095">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,89,521.25) scale(1,1) translate(0,0)" writing-mode="lr" x="89" xml:space="preserve" y="525.75" zvalue="10096">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,89,544.25) scale(1,1) translate(0,0)" writing-mode="lr" x="89" xml:space="preserve" y="548.75" zvalue="10097">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,89,567.25) scale(1,1) translate(0,0)" writing-mode="lr" x="89" xml:space="preserve" y="571.75" zvalue="10098">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,89,594.25) scale(1,1) translate(0,0)" writing-mode="lr" x="89" xml:space="preserve" y="598.75" zvalue="10099">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,230.054,958) scale(1,1) translate(0,0)" writing-mode="lr" x="230.05" xml:space="preserve" y="964" zvalue="10100">NaBang-01-2015</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,147.054,992) scale(1,1) translate(0,0)" writing-mode="lr" x="147.05" xml:space="preserve" y="998" zvalue="10101">李艳</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,327.054,992) scale(1,1) translate(0,0)" writing-mode="lr" x="327.05" xml:space="preserve" y="998" zvalue="10102">20200901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51,179) scale(1,1) translate(0,0)" writing-mode="lr" x="51" xml:space="preserve" y="183.5" zvalue="10103">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,231,179) scale(1,1) translate(0,0)" writing-mode="lr" x="231" xml:space="preserve" y="183.5" zvalue="10104">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.6875,203.25) scale(1,1) translate(0,0)" writing-mode="lr" x="62.69" xml:space="preserve" y="207.75" zvalue="10105">35kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57.1875,251.409) scale(1,1) translate(0,0)" writing-mode="lr" x="57.19" xml:space="preserve" y="255.91" zvalue="10107">1号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57.1875,272.591) scale(1,1) translate(0,-4.08925e-13)" writing-mode="lr" x="57.19" xml:space="preserve" y="277.09" zvalue="10109">1号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.1875,227.25) scale(1,1) translate(0,0)" writing-mode="lr" x="62.19" xml:space="preserve" y="231.75" zvalue="10110">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="183" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,986.284,105.875) scale(1,1) translate(0,0)" writing-mode="lr" x="986.28" xml:space="preserve" y="110.38" zvalue="10140">35kV昔那线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="193" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,843.5,972.625) scale(1,1) translate(0,0)" writing-mode="lr" x="843.5" xml:space="preserve" y="977.13" zvalue="10146">10kV1号电容器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="204" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1079.45,936.625) scale(1,1) translate(2.31915e-13,0)" writing-mode="lr" x="1079.45" xml:space="preserve" y="941.13" zvalue="10156">10kV备用线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1046.75,869.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1046.75" xml:space="preserve" y="874" zvalue="10158">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="210" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1301.16,851.474) scale(1,1) translate(0,0)" writing-mode="lr" x="1301.16" xml:space="preserve" y="855.97" zvalue="10161">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="209" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1254.09,782.368) scale(1,1) translate(0,0)" writing-mode="lr" x="1254.09" xml:space="preserve" y="786.87" zvalue="10163">022</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="208" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1300.98,731.474) scale(1,1) translate(0,0)" writing-mode="lr" x="1300.98" xml:space="preserve" y="735.97" zvalue="10167">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1252,868.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1252" xml:space="preserve" y="873" zvalue="10173">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="226" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1529.91,851.474) scale(1,1) translate(0,0)" writing-mode="lr" x="1529.91" xml:space="preserve" y="855.97" zvalue="10176">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="225" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1482.84,782.368) scale(1,1) translate(0,0)" writing-mode="lr" x="1482.84" xml:space="preserve" y="786.87" zvalue="10178">023</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="224" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1529.73,731.474) scale(1,1) translate(0,0)" writing-mode="lr" x="1529.73" xml:space="preserve" y="735.97" zvalue="10182">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="223" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1508.45,935.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1508.45" xml:space="preserve" y="940.13" zvalue="10186">10kV那邦线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="91" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1481.75,872.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1481.75" xml:space="preserve" y="877" zvalue="10188">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="244" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1431.75,550.375) scale(1,1) translate(-9.23095e-13,0)" writing-mode="lr" x="1431.75" xml:space="preserve" y="554.88" zvalue="10194">35kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="246" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1408.62,416.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1408.63" xml:space="preserve" y="420.75" zvalue="10195">3221</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1144.94,478.372) scale(1,1) translate(0,0)" writing-mode="lr" x="1144.94" xml:space="preserve" y="482.87" zvalue="10203">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1144.89,606.818) scale(1,1) translate(0,0)" writing-mode="lr" x="1144.89" xml:space="preserve" y="611.3200000000001" zvalue="10207">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,824.374,646.412) scale(1,1) translate(0,0)" writing-mode="lr" x="824.37" xml:space="preserve" y="650.91" zvalue="10211">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1273,936.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1273" xml:space="preserve" y="941.13" zvalue="10290">10kV刀弄线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="146" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,289.387,393.461) scale(1,1) translate(0,-1.69403e-13)" writing-mode="lr" x="289.3868103027339" xml:space="preserve" y="397.9611358642578" zvalue="10312">小电流接地</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="139" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,91.4375,319) scale(1,1) translate(0,3.41838e-13)" writing-mode="lr" x="91.43749999999955" xml:space="preserve" y="323.4999999999999" zvalue="10313">全站公用</text>
 </g>
 <g id="ButtonClass">
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="154.09" y="380.25" zvalue="10286"/></g>
  <g href="单厂站信息-20210617.svg"><rect fill-opacity="0" height="24" width="72.88" x="52.19" y="380.25" zvalue="10287"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="52.19" y="343.75" zvalue="10288"/></g>
  <g href="35kV昔那线那邦首部T线、厂变T线.svg"><rect fill-opacity="0" height="39" width="266" x="544" y="86" zvalue="10314"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="48">
   <path class="kv35" d="M 762.5 383.09 L 1542.25 383.09" stroke-width="6" zvalue="7576"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674403614723" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674403614723"/></metadata>
  <path d="M 762.5 383.09 L 1542.25 383.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="166">
   <path class="kv10" d="M 619.67 702.09 L 1661 702.09" stroke-width="6" zvalue="7715"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674403680259" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674403680259"/></metadata>
  <path d="M 619.67 702.09 L 1661 702.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="1453">
   <use class="kv35" height="30" transform="rotate(180,1268.01,330.828) scale(0.947693,-0.6712) translate(69.5944,-828.649)" width="15" x="1260.902401042228" xlink:href="#Disconnector:刀闸_0" y="320.7597602301333" zvalue="8015"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453988057091" ObjectName="35kV母线3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453988057091"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1268.01,330.828) scale(0.947693,-0.6712) translate(69.5944,-828.649)" width="15" x="1260.902401042228" y="320.7597602301333"/></g>
  <g id="815">
   <use class="kv35" height="30" transform="rotate(0,1132.59,410.372) scale(0.947693,-0.6712) translate(62.1199,-1026.7)" width="15" x="1125.480010535841" xlink:href="#Disconnector:刀闸_0" y="400.3043387992517" zvalue="9724"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454102351875" ObjectName="#1主变35kV侧3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454102351875"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1132.59,410.372) scale(0.947693,-0.6712) translate(62.1199,-1026.7)" width="15" x="1125.480010535841" y="400.3043387992517"/></g>
  <g id="636">
   <use class="kv10" height="30" transform="rotate(0,1132.28,677.818) scale(0.947693,-0.6712) translate(62.1031,-1692.61)" width="15" x="1125.175204945725" xlink:href="#Disconnector:刀闸_0" y="667.75" zvalue="9734"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454102286339" ObjectName="#1主变10kV侧0011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454102286339"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1132.28,677.818) scale(0.947693,-0.6712) translate(62.1031,-1692.61)" width="15" x="1125.175204945725" y="667.75"/></g>
  <g id="864">
   <use class="kv10" height="30" transform="rotate(180,781.697,669.404) scale(0.947693,-0.6712) translate(42.7528,-1671.66)" width="15" x="774.5894229335038" xlink:href="#Disconnector:刀闸_0" y="659.3355178058908" zvalue="9761"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453986877443" ObjectName="10kV母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453986877443"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,781.697,669.404) scale(0.947693,-0.6712) translate(42.7528,-1671.66)" width="15" x="774.5894229335038" y="659.3355178058908"/></g>
  <g id="5">
   <use class="kv10" height="30" transform="rotate(180,845.312,738.891) scale(-0.947693,0.6712) translate(-1737.67,357.027)" width="15" x="838.2040430811066" xlink:href="#Disconnector:刀闸_0" y="728.8230764122643" zvalue="9777"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453986942979" ObjectName="10kV1号电容器0241隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453986942979"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,845.312,738.891) scale(-0.947693,0.6712) translate(-1737.67,357.027)" width="15" x="838.2040430811066" y="728.8230764122643"/></g>
  <g id="22">
   <use class="kv10" height="30" transform="rotate(180,845.744,873.641) scale(-0.947693,0.6712) translate(-1738.56,423.037)" width="15" x="838.6361446684602" xlink:href="#Disconnector:刀闸_0" y="863.5730764122643" zvalue="9783"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453987008515" ObjectName="10kV1号电容器0246隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453987008515"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,845.744,873.641) scale(-0.947693,0.6712) translate(-1738.56,423.037)" width="15" x="838.6361446684602" y="863.5730764122643"/></g>
  <g id="116">
   <use class="kv10" height="30" transform="rotate(180,1078.99,850.891) scale(-0.947693,0.6712) translate(-2217.93,411.892)" width="15" x="1071.88614466846" xlink:href="#Disconnector:刀闸_0" y="840.8230764122643" zvalue="9858"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453987139587" ObjectName="10kV备用线0216隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453987139587"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1078.99,850.891) scale(-0.947693,0.6712) translate(-2217.93,411.892)" width="15" x="1071.88614466846" y="840.8230764122643"/></g>
  <g id="109">
   <use class="kv10" height="30" transform="rotate(180,1078.81,730.891) scale(-0.947693,0.6712) translate(-2217.56,353.108)" width="15" x="1071.705504134621" xlink:href="#Disconnector:刀闸_0" y="720.8230764122643" zvalue="9868"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453987074051" ObjectName="10kV备用线0211隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453987074051"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1078.81,730.891) scale(-0.947693,0.6712) translate(-2217.56,353.108)" width="15" x="1071.705504134621" y="720.8230764122643"/></g>
  <g id="99">
   <use class="kv35" height="30" transform="rotate(0,984.211,270.838) scale(-0.947693,0.6712) translate(-2023.14,127.743)" width="15" x="977.1034928921569" xlink:href="#Disconnector:刀闸_0" y="260.7697927208583" zvalue="9969"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453987270659" ObjectName="35kV昔那线3218隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453987270659"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,984.211,270.838) scale(-0.947693,0.6712) translate(-2023.14,127.743)" width="15" x="977.1034928921569" y="260.7697927208583"/></g>
  <g id="96">
   <use class="kv35" height="30" transform="rotate(180,984.392,353.857) scale(0.947693,-0.6712) translate(53.9403,-885.988)" width="15" x="977.2841334259956" xlink:href="#Disconnector:刀闸_0" y="343.7885305077181" zvalue="9974"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453987205123" ObjectName="35kV昔那线3211隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453987205123"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,984.392,353.857) scale(0.947693,-0.6712) translate(53.9403,-885.988)" width="15" x="977.2841334259956" y="343.7885305077181"/></g>
  <g id="32">
   <use class="kv35" height="30" transform="rotate(0,984.211,212.088) scale(-0.947693,0.6712) translate(-2023.14,98.963)" width="15" x="977.1034928921569" xlink:href="#Disconnector:刀闸_0" y="202.0197927208583" zvalue="10031"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453987598339" ObjectName="35kV昔那线3216隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453987598339"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,984.211,212.088) scale(-0.947693,0.6712) translate(-2023.14,98.963)" width="15" x="977.1034928921569" y="202.0197927208583"/></g>
  <g id="222">
   <use class="kv10" height="30" transform="rotate(180,1280.24,850.891) scale(-0.947693,0.6712) translate(-2631.54,411.892)" width="15" x="1273.13614466846" xlink:href="#Disconnector:刀闸_0" y="840.8230764122643" zvalue="10160"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453989040131" ObjectName="10kV刀弄线0226隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453989040131"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1280.24,850.891) scale(-0.947693,0.6712) translate(-2631.54,411.892)" width="15" x="1273.13614466846" y="840.8230764122643"/></g>
  <g id="216">
   <use class="kv10" height="30" transform="rotate(180,1280.06,730.891) scale(-0.947693,0.6712) translate(-2631.17,353.108)" width="15" x="1272.955504134621" xlink:href="#Disconnector:刀闸_0" y="720.8230764122643" zvalue="10166"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453988974595" ObjectName="10kV刀弄线0221隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453988974595"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1280.06,730.891) scale(-0.947693,0.6712) translate(-2631.17,353.108)" width="15" x="1272.955504134621" y="720.8230764122643"/></g>
  <g id="236">
   <use class="kv10" height="30" transform="rotate(180,1508.99,850.891) scale(-0.947693,0.6712) translate(-3101.67,411.892)" width="15" x="1501.88614466846" xlink:href="#Disconnector:刀闸_0" y="840.8230764122643" zvalue="10175"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453989302275" ObjectName="10kV那邦线0236隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453989302275"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1508.99,850.891) scale(-0.947693,0.6712) translate(-3101.67,411.892)" width="15" x="1501.88614466846" y="840.8230764122643"/></g>
  <g id="232">
   <use class="kv10" height="30" transform="rotate(180,1508.81,730.891) scale(-0.947693,0.6712) translate(-3101.3,353.108)" width="15" x="1501.705504134621" xlink:href="#Disconnector:刀闸_0" y="720.8230764122643" zvalue="10181"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453989236739" ObjectName="10kV那邦线0231隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453989236739"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1508.81,730.891) scale(-0.947693,0.6712) translate(-3101.3,353.108)" width="15" x="1501.705504134621" y="720.8230764122643"/></g>
  <g id="245">
   <use class="kv35" height="30" transform="rotate(0,1436.25,417.25) scale(0.947693,-0.6712) translate(78.8803,-1043.83)" width="15" x="1429.142303114711" xlink:href="#Disconnector:刀闸_0" y="407.1819936485927" zvalue="10194"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453989433347" ObjectName="35kV1号站用变3221隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453989433347"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1436.25,417.25) scale(0.947693,-0.6712) translate(78.8803,-1043.83)" width="15" x="1429.142303114711" y="407.1819936485927"/></g>
  <g id="2">
   <use class="kv35" height="30" transform="rotate(0,1132.33,479.372) scale(0.947693,-0.6712) translate(62.1059,-1198.51)" width="15" x="1125.226269374257" xlink:href="#Disconnector:刀闸_0" y="469.3043387992516" zvalue="10202"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454102417411" ObjectName="#1主变35kV侧3016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454102417411"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1132.33,479.372) scale(0.947693,-0.6712) translate(62.1059,-1198.51)" width="15" x="1125.226269374257" y="469.3043387992516"/></g>
  <g id="10">
   <use class="kv10" height="30" transform="rotate(0,1132.28,607.818) scale(0.947693,-0.6712) translate(62.1031,-1518.32)" width="15" x="1125.175204945725" xlink:href="#Disconnector:刀闸_0" y="597.75" zvalue="10206"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454102482947" ObjectName="#1主变10kV侧0016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454102482947"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1132.28,607.818) scale(0.947693,-0.6712) translate(62.1031,-1518.32)" width="15" x="1125.175204945725" y="597.75"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="150">
   <use class="kv35" height="20" transform="rotate(90,1295.37,355.393) scale(1.24619,-1.0068) translate(-254.673,-708.317)" width="10" x="1289.143242399862" xlink:href="#GroundDisconnector:地刀_0" y="345.3248086033777" zvalue="8029"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453987991555" ObjectName="35kV母线39010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453987991555"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1295.37,355.393) scale(1.24619,-1.0068) translate(-254.673,-708.317)" width="10" x="1289.143242399862" y="345.3248086033777"/></g>
  <g id="170">
   <use class="kv35" height="20" transform="rotate(90,1295.37,304.643) scale(1.24619,-1.0068) translate(-254.673,-607.16)" width="10" x="1289.143242399862" xlink:href="#GroundDisconnector:地刀_0" y="294.5748086033776" zvalue="8034"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453987860483" ObjectName="35kV母线39017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453987860483"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1295.37,304.643) scale(1.24619,-1.0068) translate(-254.673,-607.16)" width="10" x="1289.143242399862" y="294.5748086033776"/></g>
  <g id="98">
   <use class="kv35" height="20" transform="rotate(270,954.196,163.315) scale(-1.24619,-1.0068) translate(-1718.66,-325.458)" width="10" x="947.9650768138583" xlink:href="#GroundDisconnector:地刀_0" y="153.246507813326" zvalue="9971"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453987401731" ObjectName="35kV昔那线32167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453987401731"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,954.196,163.315) scale(-1.24619,-1.0068) translate(-1718.66,-325.458)" width="10" x="947.9650768138583" y="153.246507813326"/></g>
  <g id="29">
   <use class="kv35" height="20" transform="rotate(270,954.196,239.315) scale(-1.24619,-1.0068) translate(-1718.66,-476.945)" width="10" x="947.9650768138583" xlink:href="#GroundDisconnector:地刀_0" y="229.246507813326" zvalue="10027"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453987532803" ObjectName="35kV昔那线32187接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453987532803"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,954.196,239.315) scale(-1.24619,-1.0068) translate(-1718.66,-476.945)" width="10" x="947.9650768138583" y="229.246507813326"/></g>
  <g id="27">
   <use class="kv10" height="20" transform="rotate(270,872.041,845.976) scale(1.24619,1.0068) translate(-171.043,-5.64631)" width="10" x="865.8099081128544" xlink:href="#GroundDisconnector:地刀_0" y="835.908141936711" zvalue="10058"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453988188163" ObjectName="10kV1号电容器02460接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453988188163"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,872.041,845.976) scale(1.24619,1.0068) translate(-171.043,-5.64631)" width="10" x="865.8099081128544" y="835.908141936711"/></g>
  <g id="28">
   <use class="kv10" height="20" transform="rotate(270,872.041,901.976) scale(1.24619,1.0068) translate(-171.043,-6.02457)" width="10" x="865.8099090665287" xlink:href="#GroundDisconnector:地刀_0" y="891.908141936711" zvalue="10060"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453988319235" ObjectName="10kV1号电容器02467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453988319235"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,872.041,901.976) scale(1.24619,1.0068) translate(-171.043,-6.02457)" width="10" x="865.8099090665287" y="891.908141936711"/></g>
  <g id="31">
   <use class="kv10" height="20" transform="rotate(270,872.041,809.976) scale(1.24619,1.0068) translate(-171.043,-5.40314)" width="10" x="865.8099082666505" xlink:href="#GroundDisconnector:地刀_0" y="799.908141936711" zvalue="10062"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453988450307" ObjectName="10kV1号电容器02417接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453988450307"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,872.041,809.976) scale(1.24619,1.0068) translate(-171.043,-5.40314)" width="10" x="865.8099082666505" y="799.908141936711"/></g>
  <g id="205">
   <use class="kv10" height="29" transform="rotate(0,1054.12,838.5) scale(1.25,1.2931) translate(-208.2,-185.81)" width="21" x="1041" xlink:href="#GroundDisconnector:地刀-带电显示_0" y="819.75" zvalue="10157"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453988777987" ObjectName="10kV备用线02167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453988777987"/></metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1054.12,838.5) scale(1.25,1.2931) translate(-208.2,-185.81)" width="21" x="1041" y="819.75"/></g>
  <g id="212">
   <use class="kv10" height="29" transform="rotate(0,1253.75,838.5) scale(1.25,1.2931) translate(-248.125,-185.81)" width="21" x="1240.625" xlink:href="#GroundDisconnector:地刀-带电显示_0" y="819.75" zvalue="10172"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453988843523" ObjectName="10kV刀弄线02267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453988843523"/></metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1253.75,838.5) scale(1.25,1.2931) translate(-248.125,-185.81)" width="21" x="1240.625" y="819.75"/></g>
  <g id="228">
   <use class="kv10" height="29" transform="rotate(0,1482.5,838.5) scale(1.25,1.2931) translate(-293.875,-185.81)" width="21" x="1469.375" xlink:href="#GroundDisconnector:地刀-带电显示_0" y="819.75" zvalue="10187"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453989105667" ObjectName="10kV那邦线02367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453989105667"/></metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1482.5,838.5) scale(1.25,1.2931) translate(-293.875,-185.81)" width="21" x="1469.375" y="819.75"/></g>
  <g id="15">
   <use class="kv10" height="20" transform="rotate(90,804.374,647.643) scale(1.24619,-1.0068) translate(-157.675,-1290.84)" width="10" x="798.1432423998622" xlink:href="#GroundDisconnector:地刀_0" y="637.5748086033776" zvalue="10210"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454102614019" ObjectName="10kV母线电压互感器09017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454102614019"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,804.374,647.643) scale(1.24619,-1.0068) translate(-157.675,-1290.84)" width="10" x="798.1432423998622" y="637.5748086033776"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="151">
   <path class="kv35" d="M 1267.95 340.72 L 1267.95 383.09" stroke-width="1" zvalue="8031"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1453@1" LinkObjectIDznd="48@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1267.95 340.72 L 1267.95 383.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="153">
   <path class="kv35" d="M 1285.56 355.46 L 1267.95 355.46" stroke-width="1" zvalue="8032"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@0" LinkObjectIDznd="151" MaxPinNum="2"/>
   </metadata>
  <path d="M 1285.56 355.46 L 1267.95 355.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="kv35" d="M 1267.93 321.09 L 1267.93 278.23" stroke-width="1" zvalue="8038"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1453@0" LinkObjectIDznd="218@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1267.93 321.09 L 1267.93 278.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="180">
   <path class="kv35" d="M 1285.56 304.71 L 1267.93 304.71" stroke-width="1" zvalue="8039"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@0" LinkObjectIDznd="179" MaxPinNum="2"/>
   </metadata>
  <path d="M 1285.56 304.71 L 1267.93 304.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="813">
   <path class="kv35" d="M 1132.65 400.48 L 1132.65 383.09" stroke-width="1" zvalue="9726"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="815@1" LinkObjectIDznd="48@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1132.65 400.48 L 1132.65 383.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="812">
   <path class="kv35" d="M 1132.48 427.28 L 1132.48 420.11" stroke-width="1" zvalue="9727"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="816@1" LinkObjectIDznd="815@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1132.48 427.28 L 1132.48 420.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="633">
   <path class="kv10" d="M 1132.82 654.5 L 1132.82 667.92" stroke-width="1" zvalue="9736"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="811@0" LinkObjectIDznd="636@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1132.82 654.5 L 1132.82 667.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="632">
   <path class="kv10" d="M 1132.37 687.55 L 1132.37 702.09" stroke-width="1" zvalue="9737"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="636@0" LinkObjectIDznd="166@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1132.37 687.55 L 1132.37 702.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="865">
   <path class="kv10" d="M 781.64 679.3 L 781.64 702.09" stroke-width="1" zvalue="9765"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="864@1" LinkObjectIDznd="166@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 781.64 679.3 L 781.64 702.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="857">
   <path class="kv10" d="M 781.61 659.67 L 781.61 636.81" stroke-width="1" zvalue="9769"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="864@0" LinkObjectIDznd="855@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 781.61 659.67 L 781.61 636.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="8">
   <path class="kv10" d="M 845.37 729 L 845.37 702.09" stroke-width="1" zvalue="9781"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="5@1" LinkObjectIDznd="166@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 845.37 729 L 845.37 702.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="kv10" d="M 1079.08 860.63 L 1079.08 886.44" stroke-width="1" zvalue="9864"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="116@0" LinkObjectIDznd="203@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1079.08 860.63 L 1079.08 886.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="kv10" d="M 1079.05 792.57 L 1079.05 841" stroke-width="1" zvalue="9867"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="115@0" LinkObjectIDznd="116@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1079.05 792.57 L 1079.05 841" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv10" d="M 1078.9 740.63 L 1078.9 766.69" stroke-width="1" zvalue="9870"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109@0" LinkObjectIDznd="115@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1078.9 740.63 L 1078.9 766.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="kv10" d="M 1078.87 721 L 1078.87 702.09" stroke-width="1" zvalue="9871"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109@1" LinkObjectIDznd="166@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1078.87 721 L 1078.87 702.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="24">
   <path class="kv35" d="M 984.33 363.75 L 984.33 383.09" stroke-width="1" zvalue="9926"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="90" LinkObjectIDznd="48@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 984.33 363.75 L 984.33 383.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="kv35" d="M 984.15 299.06 L 984.15 280.73" stroke-width="1" zvalue="9973"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="100@0" LinkObjectIDznd="99@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 984.15 299.06 L 984.15 280.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="kv35" d="M 984.31 324.95 L 984.31 344.12" stroke-width="1" zvalue="9978"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="100@1" LinkObjectIDznd="96@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 984.31 324.95 L 984.31 344.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="kv35" d="M 964.01 163.38 L 983.23 163.38" stroke-width="1" zvalue="9979"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="98@0" LinkObjectIDznd="41" MaxPinNum="2"/>
   </metadata>
  <path d="M 964.01 163.38 L 983.23 163.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="kv35" d="M 984.33 363.75 L 984.33 383.09" stroke-width="1" zvalue="9981"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="96@1" LinkObjectIDznd="48@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 984.33 363.75 L 984.33 383.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="kv35" d="M 983.23 150.19 L 983.23 202.35" stroke-width="1" zvalue="10040"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="182@0" LinkObjectIDznd="32@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 983.23 150.19 L 983.23 202.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="184">
   <path class="kv35" d="M 984.15 221.98 L 984.13 261.1" stroke-width="1" zvalue="10140"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="32@1" LinkObjectIDznd="99@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 984.15 221.98 L 984.13 261.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="185">
   <path class="kv35" d="M 964.01 239.38 L 984.14 239.38" stroke-width="1" zvalue="10141"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="29@0" LinkObjectIDznd="184" MaxPinNum="2"/>
   </metadata>
  <path d="M 964.01 239.38 L 984.14 239.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="186">
   <path class="kv35" d="M 1000.26 239.31 L 984.14 239.31" stroke-width="1" zvalue="10142"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="42@0" LinkObjectIDznd="184" MaxPinNum="2"/>
   </metadata>
  <path d="M 1000.26 239.31 L 984.14 239.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="194">
   <path class="kv10" d="M 845.65 766.69 L 845.65 748.63" stroke-width="1" zvalue="10146"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="546@1" LinkObjectIDznd="5@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 845.65 766.69 L 845.65 748.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="196">
   <path class="kv10" d="M 845.8 792.57 L 845.8 863.75" stroke-width="1" zvalue="10148"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="546@0" LinkObjectIDznd="22@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 845.8 792.57 L 845.8 863.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="197">
   <path class="kv10" d="M 845.83 883.38 L 845.83 916.56" stroke-width="1" zvalue="10149"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="22@0" LinkObjectIDznd="192@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 845.83 883.38 L 845.83 916.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="198">
   <path class="kv10" d="M 862.22 809.91 L 845.8 809.91" stroke-width="1" zvalue="10150"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="31@0" LinkObjectIDznd="196" MaxPinNum="2"/>
   </metadata>
  <path d="M 862.22 809.91 L 845.8 809.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="199">
   <path class="kv10" d="M 862.22 845.91 L 845.8 845.91" stroke-width="1" zvalue="10151"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="27@0" LinkObjectIDznd="196" MaxPinNum="2"/>
   </metadata>
  <path d="M 862.22 845.91 L 845.8 845.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="200">
   <path class="kv10" d="M 862.22 901.91 L 845.83 901.91" stroke-width="1" zvalue="10152"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="28@0" LinkObjectIDznd="197" MaxPinNum="2"/>
   </metadata>
  <path d="M 862.22 901.91 L 845.83 901.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="202">
   <path class="kv10" d="M 818.75 834.75 L 818.75 822.25 L 845.8 822.25" stroke-width="1" zvalue="10154"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="201@0" LinkObjectIDznd="196" MaxPinNum="2"/>
   </metadata>
  <path d="M 818.75 834.75 L 818.75 822.25 L 845.8 822.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="206">
   <path class="kv10" d="M 1054.21 821.04 L 1054.21 811 L 1079.05 811" stroke-width="1" zvalue="10158"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="205@0" LinkObjectIDznd="110" MaxPinNum="2"/>
   </metadata>
  <path d="M 1054.21 821.04 L 1054.21 811 L 1079.05 811" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="220">
   <path class="kv10" d="M 1280.33 860.63 L 1280.33 883.33" stroke-width="1" zvalue="10164"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="222@0" LinkObjectIDznd="21@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1280.33 860.63 L 1280.33 883.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="217">
   <path class="kv10" d="M 1280.3 792.57 L 1280.3 841" stroke-width="1" zvalue="10165"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="221@0" LinkObjectIDznd="222@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1280.3 792.57 L 1280.3 841" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="215">
   <path class="kv10" d="M 1280.15 740.63 L 1280.15 766.69" stroke-width="1" zvalue="10168"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="216@0" LinkObjectIDznd="221@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1280.15 740.63 L 1280.15 766.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="214">
   <path class="kv10" d="M 1280.12 721 L 1280.12 702.09" stroke-width="1" zvalue="10169"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="216@1" LinkObjectIDznd="166@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1280.12 721 L 1280.12 702.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="211">
   <path class="kv10" d="M 1253.84 821.04 L 1253.88 811 L 1280.3 811" stroke-width="1" zvalue="10173"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="212@0" LinkObjectIDznd="217" MaxPinNum="2"/>
   </metadata>
  <path d="M 1253.84 821.04 L 1253.88 811 L 1280.3 811" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="234">
   <path class="kv10" d="M 1509.08 860.63 L 1509.08 886.44" stroke-width="1" zvalue="10179"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="236@0" LinkObjectIDznd="229@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1509.08 860.63 L 1509.08 886.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="233">
   <path class="kv10" d="M 1509.05 792.57 L 1509.05 841" stroke-width="1" zvalue="10180"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="235@0" LinkObjectIDznd="236@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1509.05 792.57 L 1509.05 841" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="231">
   <path class="kv10" d="M 1508.9 740.63 L 1508.9 766.69" stroke-width="1" zvalue="10183"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="232@0" LinkObjectIDznd="235@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1508.9 740.63 L 1508.9 766.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="230">
   <path class="kv10" d="M 1508.87 721 L 1508.87 702.09" stroke-width="1" zvalue="10184"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="232@1" LinkObjectIDznd="166@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1508.87 721 L 1508.87 702.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="227">
   <path class="kv10" d="M 1482.59 821.04 L 1482.63 811 L 1509.05 811" stroke-width="1" zvalue="10188"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="228@0" LinkObjectIDznd="233" MaxPinNum="2"/>
   </metadata>
  <path d="M 1482.59 821.04 L 1482.63 811 L 1509.05 811" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="247">
   <path class="kv35" d="M 1436.33 450.23 L 1436.33 426.99" stroke-width="1" zvalue="10195"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="243@0" LinkObjectIDznd="245@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1436.33 450.23 L 1436.33 426.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="248">
   <path class="kv35" d="M 1436.31 407.35 L 1436.31 383.09" stroke-width="1" zvalue="10196"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="245@1" LinkObjectIDznd="48@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1436.31 407.35 L 1436.31 383.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="6">
   <path class="kv35" d="M 1132.64 453.16 L 1132.64 469.48" stroke-width="1" zvalue="10203"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="816@0" LinkObjectIDznd="2@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1132.64 453.16 L 1132.64 469.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="7">
   <path class="kv35" d="M 1132.42 489.11 L 1132.42 506.02" stroke-width="1" zvalue="10204"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="2@0" LinkObjectIDznd="822@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1132.42 489.11 L 1132.42 506.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="11">
   <path class="kv10" d="M 1132.39 584.57 L 1132.34 597.92" stroke-width="1" zvalue="10207"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="822@1" LinkObjectIDznd="10@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1132.39 584.57 L 1132.34 597.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="13">
   <path class="kv10" d="M 1132.37 617.55 L 1132.37 628.61" stroke-width="1" zvalue="10208"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="10@0" LinkObjectIDznd="811@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1132.37 617.55 L 1132.37 628.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="16">
   <path class="kv10" d="M 794.56 647.71 L 781.61 647.71" stroke-width="1" zvalue="10211"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="15@0" LinkObjectIDznd="857" MaxPinNum="2"/>
   </metadata>
  <path d="M 794.56 647.71 L 781.61 647.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="546">
   <use class="kv10" height="20" transform="rotate(180,845.75,779.618) scale(1.5542,1.35421) translate(-298.808,-200.377)" width="10" x="837.9790032033687" xlink:href="#Breaker:开关_0" y="766.0758972167969" zvalue="9393"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925119311875" ObjectName="10kV1号电容器024断路器"/>
   <cge:TPSR_Ref TObjectID="6473925119311875"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,845.75,779.618) scale(1.5542,1.35421) translate(-298.808,-200.377)" width="10" x="837.9790032033687" y="766.0758972167969"/></g>
  <g id="816">
   <use class="kv35" height="20" transform="rotate(180,1132.59,440.209) scale(1.5542,1.35421) translate(-401.089,-111.6)" width="10" x="1124.816710722901" xlink:href="#Breaker:开关_0" y="426.6666962122334" zvalue="9722"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925119442947" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473925119442947"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1132.59,440.209) scale(1.5542,1.35421) translate(-401.089,-111.6)" width="10" x="1124.816710722901" y="426.6666962122334"/></g>
  <g id="811">
   <use class="kv10" height="20" transform="rotate(180,1132.77,641.542) scale(1.5542,1.35421) translate(-401.154,-164.261)" width="10" x="1124.997344267822" xlink:href="#Breaker:开关_0" y="628" zvalue="9728"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925132812291" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473925132812291"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1132.77,641.542) scale(1.5542,1.35421) translate(-401.154,-164.261)" width="10" x="1124.997344267822" y="628"/></g>
  <g id="115">
   <use class="kv10" height="20" transform="rotate(180,1079,779.618) scale(1.5542,1.35421) translate(-381.981,-200.377)" width="10" x="1071.229003203369" xlink:href="#Breaker:开关_0" y="766.0758972316264" zvalue="9860"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925119508483" ObjectName="10kV备用线021断路器"/>
   <cge:TPSR_Ref TObjectID="6473925119508483"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1079,779.618) scale(1.5542,1.35421) translate(-381.981,-200.377)" width="10" x="1071.229003203369" y="766.0758972316264"/></g>
  <g id="100">
   <use class="kv35" height="20" transform="rotate(0,984.205,312.02) scale(1.5542,1.35421) translate(-348.179,-78.0706)" width="10" x="976.4340345345652" xlink:href="#Breaker:开关_0" y="298.4779758524579" zvalue="9966"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925119574019" ObjectName="35kV昔那线321断路器"/>
   <cge:TPSR_Ref TObjectID="6473925119574019"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,984.205,312.02) scale(1.5542,1.35421) translate(-348.179,-78.0706)" width="10" x="976.4340345345652" y="298.4779758524579"/></g>
  <g id="221">
   <use class="kv10" height="20" transform="rotate(180,1280.25,779.618) scale(1.5542,1.35421) translate(-453.743,-200.377)" width="10" x="1272.479003203369" xlink:href="#Breaker:开关_0" y="766.0758972316264" zvalue="10162"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925119639555" ObjectName="10kV刀弄线022断路器"/>
   <cge:TPSR_Ref TObjectID="6473925119639555"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1280.25,779.618) scale(1.5542,1.35421) translate(-453.743,-200.377)" width="10" x="1272.479003203369" y="766.0758972316264"/></g>
  <g id="235">
   <use class="kv10" height="20" transform="rotate(180,1509,779.618) scale(1.5542,1.35421) translate(-535.311,-200.377)" width="10" x="1501.229003203369" xlink:href="#Breaker:开关_0" y="766.0758972316264" zvalue="10177"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925119705091" ObjectName="10kV那邦线023断路器"/>
   <cge:TPSR_Ref TObjectID="6473925119705091"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1509,779.618) scale(1.5542,1.35421) translate(-535.311,-200.377)" width="10" x="1501.229003203369" y="766.0758972316264"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="218">
   <use class="kv35" height="40" transform="rotate(0,1267.5,260.341) scale(0.9375,-0.9375) translate(83.25,-539.288)" width="40" x="1248.75" xlink:href="#Accessory:线路PT三绕组_0" y="241.5909090909091" zvalue="9702"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453987729411" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1267.5,260.341) scale(0.9375,-0.9375) translate(83.25,-539.288)" width="40" x="1248.75" y="241.5909090909091"/></g>
  <g id="855">
   <use class="kv10" height="40" transform="rotate(0,780.222,618.917) scale(-0.9375,-0.9375) translate(-1613.71,-1280.34)" width="40" x="761.4724880605656" xlink:href="#Accessory:线路PT三绕组_0" y="600.1666666666666" zvalue="9771"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453986811907" ObjectName="10kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,780.222,618.917) scale(-0.9375,-0.9375) translate(-1613.71,-1280.34)" width="40" x="761.4724880605656" y="600.1666666666666"/></g>
  <g id="42">
   <use class="kv35" height="29" transform="rotate(270,1022.34,239.313) scale(1.39756,1.54085) translate(-288.637,-76.1586)" width="11" x="1014.655761595727" xlink:href="#Accessory:PT带保险_0" y="216.9710619052636" zvalue="10041"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453987663875" ObjectName="35kV昔那线PT"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(270,1022.34,239.313) scale(1.39756,1.54085) translate(-288.637,-76.1586)" width="11" x="1014.655761595727" y="216.9710619052636"/></g>
  <g id="201">
   <use class="kv10" height="30" transform="rotate(0,818.75,849.75) scale(1.25,1.25) translate(-160,-166.2)" width="30" x="800" xlink:href="#Accessory:避雷器带指示灯_0" y="831" zvalue="10153"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453988646915" ObjectName="10kV1号电容器避雷器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,818.75,849.75) scale(1.25,1.25) translate(-160,-166.2)" width="30" x="800" y="831"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="822">
   <g id="8220">
    <use class="kv35" height="30" transform="rotate(0,1132.39,545.125) scale(2.88454,2.80833) translate(-717.201,-323.89)" width="24" x="1097.77" xlink:href="#PowerTransformer2:可调不带中性点_0" y="503" zvalue="9739"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874573217794" ObjectName="35"/>
    </metadata>
   </g>
   <g id="8221">
    <use class="kv10" height="30" transform="rotate(0,1132.39,545.125) scale(2.88454,2.80833) translate(-717.201,-323.89)" width="24" x="1097.77" xlink:href="#PowerTransformer2:可调不带中性点_1" y="503" zvalue="9739"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874573283330" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399526121474" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399526121474"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1132.39,545.125) scale(2.88454,2.80833) translate(-717.201,-323.89)" width="24" x="1097.77" y="503"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="68">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="68" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,139.639,519.812) scale(1,1) translate(1.24653e-14,0)" writing-mode="lr" x="139.76" xml:space="preserve" y="524.74" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133839810563" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="67">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="67" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,139.639,544.625) scale(1,1) translate(1.24653e-14,0)" writing-mode="lr" x="139.76" xml:space="preserve" y="549.55" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133839876099" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="66">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="66" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,139.639,569.437) scale(1,1) translate(1.24653e-14,0)" writing-mode="lr" x="139.76" xml:space="preserve" y="574.37" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133839941635" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="65">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="65" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,139.639,495) scale(1,1) translate(0,0)" writing-mode="lr" x="139.76" xml:space="preserve" y="499.93" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133840072709" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="64">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="16" id="64" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,152.5,203.111) scale(1,1) translate(-2.4869e-14,0)" writing-mode="lr" x="152.68" xml:space="preserve" y="209.54" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133840203781" ObjectName="F"/>
   </metadata>
  </g>
  <g id="63">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="63" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,257.042,520.375) scale(1,1) translate(2.54997e-14,0)" writing-mode="lr" x="257.16" xml:space="preserve" y="525.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133840334851" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="58">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="58" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,257.042,545) scale(1,1) translate(2.54997e-14,0)" writing-mode="lr" x="257.16" xml:space="preserve" y="549.9299999999999" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133840400387" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="56">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,257.042,569.625) scale(1,1) translate(2.54997e-14,0)" writing-mode="lr" x="257.16" xml:space="preserve" y="574.55" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133840465923" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="55">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="55" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,257.042,495.75) scale(1,1) translate(2.54997e-14,0)" writing-mode="lr" x="257.16" xml:space="preserve" y="500.68" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133840596995" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="54">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="16" id="54" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,152.5,228) scale(1,1) translate(-2.4869e-14,0)" writing-mode="lr" x="152.68" xml:space="preserve" y="234.43" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133840728067" ObjectName="F"/>
   </metadata>
  </g>
  <g id="37">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,152.5,250.104) scale(1,1) translate(-2.4869e-14,0)" writing-mode="lr" x="152.68" xml:space="preserve" y="256.53" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133842890755" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="148">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="148" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,152.5,273.104) scale(1,1) translate(-2.4869e-14,3.49192e-13)" writing-mode="lr" x="152.68" xml:space="preserve" y="279.53" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133842956293" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="23">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="23" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,139.639,594.25) scale(1,1) translate(1.24653e-14,0)" writing-mode="lr" x="139.76" xml:space="preserve" y="599.1799999999999" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133840269315" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="20">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="20" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,257.042,594.25) scale(1,1) translate(2.54997e-14,0)" writing-mode="lr" x="257.16" xml:space="preserve" y="599.1799999999999" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133840793603" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="1">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="1" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,986.784,46.25) scale(1,1) translate(0,0)" writing-mode="lr" x="986.96" xml:space="preserve" y="51.12" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133845512197" ObjectName="P"/>
   </metadata>
  </g>
  <g id="18">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="18" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,986.784,63.25) scale(1,1) translate(0,0)" writing-mode="lr" x="986.96" xml:space="preserve" y="68.12" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133845577733" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="19" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,986.784,80.25) scale(1,1) translate(0,0)" writing-mode="lr" x="986.96" xml:space="preserve" y="85.12" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133845643269" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="25">
   <text Format="f5.2" Plane="0" SignFlag="up" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="25" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,846,999) scale(1,1) translate(0,0)" writing-mode="lr" x="846.2" xml:space="preserve" y="1003.91" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133846560771" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="26">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="26" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,846,1022) scale(1,1) translate(0,0)" writing-mode="lr" x="846.2" xml:space="preserve" y="1026.91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133846626307" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="30">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="30" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1082.2,955.75) scale(1,1) translate(1.15153e-13,0)" writing-mode="lr" x="1082.4" xml:space="preserve" y="960.66" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133847281667" ObjectName="P"/>
   </metadata>
  </g>
  <g id="34">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="34" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1512.2,955.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1512.4" xml:space="preserve" y="960.66" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133849640963" ObjectName="P"/>
   </metadata>
  </g>
  <g id="35">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="35" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1082.2,978.75) scale(1,1) translate(1.15153e-13,0)" writing-mode="lr" x="1082.4" xml:space="preserve" y="983.66" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133847347203" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="38">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="38" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1512.2,978.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1512.4" xml:space="preserve" y="983.66" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133849706499" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="39">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="39" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1082.2,1001.75) scale(1,1) translate(1.15153e-13,0)" writing-mode="lr" x="1082.4" xml:space="preserve" y="1006.66" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133847412739" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="43">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="43" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1512.2,1001.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1512.4" xml:space="preserve" y="1006.66" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133849772035" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="62">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="62" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1208.44,415.833) scale(1,1) translate(-2.56004e-13,0)" writing-mode="lr" x="1208.68" xml:space="preserve" y="422.32" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133842432005" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="69">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="69" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1209.55,445.056) scale(1,1) translate(0,0)" writing-mode="lr" x="1209.79" xml:space="preserve" y="451.54" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133842497541" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="71">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="71" prefix=" 油温:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1228.06,544.125) scale(1,1) translate(-2.5957e-13,0)" writing-mode="lr" x="1228.31" xml:space="preserve" y="550.61" zvalue="1"> 油温:dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133842890755" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="73">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="73" prefix=" 档位:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1229.27,572.681) scale(1,1) translate(1.30672e-13,0)" writing-mode="lr" x="1229.5" xml:space="preserve" y="579.17" zvalue="1"> 档位:ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133842956293" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="101">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="101" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1229.89,613.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1230.13" xml:space="preserve" y="620.24" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133842563077" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="102">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="102" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1229.89,640.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1230.13" xml:space="preserve" y="647.24" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133842628613" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="105">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="105" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1210.89,474.5) scale(1,1) translate(2.56547e-13,0)" writing-mode="lr" x="1211.13" xml:space="preserve" y="480.99" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133842694147" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="111">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="111" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1229.89,667.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1230.13" xml:space="preserve" y="674.24" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133843021829" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="47">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="47" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,152.5,177) scale(1,1) translate(0,0)" writing-mode="lr" x="152.68" xml:space="preserve" y="182.93" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133868318723" ObjectName="P"/>
   </metadata>
  </g>
  <g id="49">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="49" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,329.5,177.5) scale(1,1) translate(0,0)" writing-mode="lr" x="329.68" xml:space="preserve" y="183.43" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133868384259" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="60">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="60" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1281,955.75) scale(1,1) translate(-2.74447e-13,0)" writing-mode="lr" x="1280.53" xml:space="preserve" y="960.42" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133848199171" ObjectName="P"/>
   </metadata>
  </g>
  <g id="61">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="61" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1281,979.5) scale(1,1) translate(-2.74447e-13,0)" writing-mode="lr" x="1280.53" xml:space="preserve" y="984.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133848264707" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="145">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="145" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1281,1001.75) scale(1,1) translate(-2.74447e-13,0)" writing-mode="lr" x="1280.53" xml:space="preserve" y="1006.42" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133848330243" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="127">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="127" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1267.36,201) scale(1,1) translate(0,0)" writing-mode="lr" x="1267.48" xml:space="preserve" y="205.93" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133840072709" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="132">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="132" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,783.361,562) scale(1,1) translate(8.39329e-14,0)" writing-mode="lr" x="783.48" xml:space="preserve" y="566.9299999999999" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133840596995" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
 <g id="CompensatorClass">
  <g id="192">
   <use class="kv10" height="40" transform="rotate(0,846,938.5) scale(1.25,1.25) translate(-166.2,-182.7)" width="24" x="831" xlink:href="#Compensator:西郊变电容_0" y="913.5" zvalue="10145"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453988581379" ObjectName="10kV1号电容器"/>
   <cge:TPSR_Ref TObjectID="6192453988581379"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,846,938.5) scale(1.25,1.25) translate(-166.2,-182.7)" width="24" x="831" y="913.5"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="203">
   <use class="kv10" height="30" transform="rotate(0,1082.2,903) scale(1.25,-1.25) translate(-214.565,-1621.65)" width="15" x="1072.827027243217" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="884.25" zvalue="10155"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453988712451" ObjectName="10kV备用线"/>
   <cge:TPSR_Ref TObjectID="6192453988712451"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1082.2,903) scale(1.25,-1.25) translate(-214.565,-1621.65)" width="15" x="1072.827027243217" y="884.25"/></g>
  <g id="229">
   <use class="kv10" height="30" transform="rotate(0,1512.2,903) scale(1.25,-1.25) translate(-300.565,-1621.65)" width="15" x="1502.827027243217" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="884.25" zvalue="10185"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453989171203" ObjectName="10kV那邦线"/>
   <cge:TPSR_Ref TObjectID="6192453989171203"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1512.2,903) scale(1.25,-1.25) translate(-300.565,-1621.65)" width="15" x="1502.827027243217" y="884.25"/></g>
  <g id="243">
   <use class="kv35" height="30" transform="rotate(0,1436.33,491.5) scale(2.91667,2.91667) translate(-924.709,-294.236)" width="20" x="1407.1665190228" xlink:href="#EnergyConsumer:站用变带熔断器_0" y="447.75" zvalue="10193"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453989367811" ObjectName="35kV1号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1436.33,491.5) scale(2.91667,2.91667) translate(-924.709,-294.236)" width="20" x="1407.1665190228" y="447.75"/></g>
 </g>
 <g id="StateClass">
  <g id="1056">
   <use height="30" transform="rotate(0,264.286,320.071) scale(0.708333,0.665547) translate(104.449,155.827)" width="30" x="253.66" xlink:href="#State:红绿圆(方形)_0" y="310.09" zvalue="10247"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562957034782726" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,264.286,320.071) scale(0.708333,0.665547) translate(104.449,155.827)" width="30" x="253.66" y="310.09"/></g>
  <g id="1057">
   <use height="30" transform="rotate(0,351.911,319.071) scale(0.708333,0.665547) translate(140.529,155.324)" width="30" x="341.29" xlink:href="#State:红绿圆(方形)_0" y="309.09" zvalue="10249"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374924054529" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,351.911,319.071) scale(0.708333,0.665547) translate(140.529,155.324)" width="30" x="341.29" y="309.09"/></g>
  <g id="1084">
   <use height="30" transform="rotate(0,289.387,393.461) scale(0.910937,0.8) translate(24.7309,95.3653)" width="80" x="252.95" xlink:href="#State:间隔模板_0" y="381.46" zvalue="10310"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5629500525576194" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,289.387,393.461) scale(0.910937,0.8) translate(24.7309,95.3653)" width="80" x="252.95" y="381.46"/></g>
  <g id="1082">
   <use height="30" transform="rotate(0,91.4375,319) scale(0.910937,0.8) translate(5.37736,76.75)" width="80" x="55" xlink:href="#State:间隔模板_0" y="307" zvalue="10311"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5629500398305282" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,91.4375,319) scale(0.910937,0.8) translate(5.37736,76.75)" width="80" x="55" y="307"/></g>
 </g>
</svg>