<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549588393986" height="1045" id="thSvg" source="NR-PCS9000" viewBox="0 0 1900 1045" width="1900">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Accessory:PT带保险_0" viewBox="0,0,11,29">
   <use terminal-index="0" type="0" x="5.5" xlink:href="#terminal" y="0.1666666666666661"/>
   <rect fill-opacity="0" height="6" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.5,7.5) scale(1,1) translate(0,0)" width="4" x="3.5" y="4.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="13.16666666666667" y2="0.5"/>
   <ellipse cx="5.4" cy="18.1" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.4" cy="23.78" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:接地站用变两卷_0" viewBox="0,0,18,30">
   <use terminal-index="0" type="0" x="8.949999999999999" xlink:href="#terminal" y="0.5"/>
   <ellipse cx="9.039999999999999" cy="8.970000000000001" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.970000000000001" cy="21.14" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.035256190670173" x2="9.035256190670173" y1="4.6" y2="8.708948332339869"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.035256190670172" x2="5.05" y1="8.733948332339875" y2="11.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.063029030724982" x2="13.15" y1="8.689905009168513" y2="11.5"/>
   <path d="M 9 19.8333 L 4.08333 25.4167 L 14.0833 25.4167 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.08333333333333" x2="9.833333333333332" y1="16.5" y2="18.58333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="14" y1="9.75" y2="16.75"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.75" x2="11" y1="11.58333333333333" y2="9.666666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.75" x2="9.75" y1="11.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8" x2="15.08333333333333" y1="8.416666666666668" y2="25.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_1" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <rect fill-opacity="0" height="8" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,14.96,13) scale(1,1) translate(0,0)" width="4.42" x="12.75" y="9"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="5.416666666666668" y2="25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_2" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="20" y1="6" y2="25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20" x2="10.08333333333333" y1="6.000000000000004" y2="25.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
  </symbol>
  <symbol id="EnergyConsumer:电炉变20210928_0" viewBox="0,0,24,32">
   <use terminal-index="0" type="0" x="11.95" xlink:href="#terminal" y="0.25"/>
   <path d="M 20.5 5.25 L 23.5 5.25 L 21.5 7.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2499999999999982" x2="23.41666666666667" y1="13.25" y2="5.333333333333332"/>
   <ellipse cx="12.04" cy="8.720000000000001" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="11.97" cy="20.89" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.03525619067017" x2="12.03525619067017" y1="5.1" y2="9.208948332339869"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.03525619067017" x2="8.050000000000001" y1="9.233948332339876" y2="12.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.06302903072498" x2="16.15" y1="9.189905009168511" y2="12"/>
   <path d="M 12 18.8333 L 7.08333 24.4167 L 17.0833 24.4167 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Compensator:硅厂电容器2021_0" viewBox="0,0,10,29">
   <use terminal-index="0" type="0" x="4.958333333333334" xlink:href="#terminal" y="0.4980091012514087"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="19.33333333333334" y2="24.83333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.008333333333334" x2="5.008333333333334" y1="15.83333333333334" y2="0.3323378839590365"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,8) scale(1,1) translate(0,0)" width="6" x="2" y="2.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="-8.881784197001252e-16" x2="9.916666666666668" y1="15.80546075085324" y2="15.80546075085324"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.04999999999999893" x2="9.966666666666669" y1="19.27261092150171" y2="19.27261092150171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.04999999999999893" x2="9.966666666666669" y1="28.27261092150171" y2="28.27261092150171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="-8.881784197001252e-16" x2="9.916666666666668" y1="24.80546075085324" y2="24.80546075085324"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV中晟硅厂" InitShowingPlane="" fill="rgb(0,0,0)" height="1045" width="1900" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="60.19" y="313.25" zvalue="534"/></g>
 </g>
 <g id="OtherClass">
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="4" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,96.625,325.25) scale(1,1) translate(0,0)" width="72.88" x="60.19" y="313.25" zvalue="534"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,96.625,325.25) scale(1,1) translate(0,0)" writing-mode="lr" x="96.63" xml:space="preserve" y="329.75" zvalue="534">信号一览</text>
  <image height="60" id="2" preserveAspectRatio="xMidYMid slice" width="273.81" x="46.44" xlink:href="logo.png" y="40.75"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,183.347,70.75) scale(1,1) translate(-1.03127e-14,0)" writing-mode="lr" x="183.35" xml:space="preserve" y="74.25" zvalue="938"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="3" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,176.833,68.8153) scale(1,1) translate(1.95399e-14,0)" writing-mode="lr" x="176.83" xml:space="preserve" y="77.81999999999999" zvalue="939">10kV中晟硅厂</text>
  <line fill="none" id="130" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="375" x2="375" y1="49.5" y2="1039.5" zvalue="210"/>
  <line fill="none" id="136" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.47692454998014" x2="318.0881846035993" y1="169.1279458827686" y2="169.1279458827686" zvalue="501"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="113.0114285714285" y1="928.0627909390723" y2="928.0627909390723"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="113.0114285714285" y1="979.4022909390724" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="41.57142857142856" y1="928.0627909390723" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0114285714285" x2="113.0114285714285" y1="928.0627909390723" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="347.5719285714285" y1="928.0627909390723" y2="928.0627909390723"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="347.5719285714285" y1="979.4022909390724" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="113.0119285714286" y1="928.0627909390723" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="347.5719285714285" x2="347.5719285714285" y1="928.0627909390723" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="113.0114285714285" y1="979.4022709390724" y2="979.4022709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="113.0114285714285" y1="1006.879770939072" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="41.57142857142856" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0114285714285" x2="113.0114285714285" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="184.0472285714286" y1="979.4022709390724" y2="979.4022709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="184.0472285714286" y1="1006.879770939072" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="113.0119285714286" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="184.0472285714286" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="265.8093285714285" y1="979.4022709390724" y2="979.4022709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="265.8093285714285" y1="1006.879770939072" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="184.0472285714286" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8093285714285" x2="265.8093285714285" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8092285714285" x2="347.5713285714286" y1="979.4022709390724" y2="979.4022709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8092285714285" x2="347.5713285714286" y1="1006.879770939072" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8092285714285" x2="265.8092285714285" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="347.5713285714286" x2="347.5713285714286" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="113.0114285714285" y1="1006.879690939072" y2="1006.879690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="113.0114285714285" y1="1034.357190939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="41.57142857142856" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0114285714285" x2="113.0114285714285" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="184.0472285714286" y1="1006.879690939072" y2="1006.879690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="184.0472285714286" y1="1034.357190939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="113.0119285714286" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="184.0472285714286" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="265.8093285714285" y1="1006.879690939072" y2="1006.879690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="265.8093285714285" y1="1034.357190939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="184.0472285714286" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8093285714285" x2="265.8093285714285" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8092285714285" x2="347.5713285714286" y1="1006.879690939072" y2="1006.879690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8092285714285" x2="347.5713285714286" y1="1034.357190939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8092285714285" x2="265.8092285714285" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="347.5713285714286" x2="347.5713285714286" y1="1006.879690939072" y2="1034.357190939072"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="134" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,188.624,957.636) scale(1,1) translate(-1.04198e-14,1.05117e-13)" writing-mode="lr" x="46.93" xml:space="preserve" y="963.64" zvalue="503">参考图号     ZhongSheng-01-2014</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="109" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,119.149,995.005) scale(1,1) translate(5.03187e-14,-1.52972e-12)" writing-mode="lr" x="56.65" xml:space="preserve" y="1001" zvalue="504">制图             </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="98" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,269.875,996.005) scale(1,1) translate(-4.30209e-14,1.09377e-13)" writing-mode="lr" x="193.75" xml:space="preserve" y="1002" zvalue="505">绘制日期   20210928</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,70.2423,1024.56) scale(1,1) translate(-4.58202e-14,1.01293e-12)" writing-mode="lr" x="70.23999999999999" xml:space="preserve" y="1030.56" zvalue="506">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="96" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,269.746,1022.56) scale(1,1) translate(0,1.12325e-13)" writing-mode="lr" x="192.92" xml:space="preserve" y="1028.56" zvalue="507">更新日期    </text>
  <line fill="none" id="91" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="40.85611355802348" x2="345.4673736116426" y1="623.8945306693694" y2="623.8945306693694" zvalue="508"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.9207,640.936) scale(1,1) translate(2.94653e-15,-1.38164e-13)" writing-mode="lr" x="83.92070806204504" xml:space="preserve" y="645.4363811688671" zvalue="510">危险点(双击编辑）：</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="172.3571428571429" y2="172.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="198.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="2.571428571428442" y1="172.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="172.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="172.3571428571429" y2="172.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="198.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="172.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.5714285714284" x2="364.5714285714284" y1="172.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="198.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="222.6071428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="2.571428571428442" y1="198.3571428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="198.3571428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="198.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="222.6071428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="198.3571428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.5714285714284" x2="364.5714285714284" y1="198.3571428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="222.6071428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="245.3571428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="2.571428571428442" y1="222.6071428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="222.6071428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="222.6071428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="245.3571428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="222.6071428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.5714285714284" x2="364.5714285714284" y1="222.6071428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="245.3571428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="268.1071428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="2.571428571428442" y1="245.3571428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="245.3571428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="245.3571428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="268.1071428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="245.3571428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.5714285714284" x2="364.5714285714284" y1="245.3571428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="268.1071428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="290.8571428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="2.571428571428442" y1="268.1071428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="268.1071428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="268.1071428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="290.8571428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="268.1071428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.5714285714284" x2="364.5714285714284" y1="268.1071428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="452.3571428571429" y2="452.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="53.57142857142856" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="452.3571428571429" y2="452.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="452.3571428571429" y2="452.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9587285714285" x2="216.9587285714285" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="452.3571428571429" y2="452.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="216.9586285714286" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="452.3571428571429" y2="452.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.5714285714286" x2="334.5714285714286" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="53.57142857142856" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9587285714285" x2="216.9587285714285" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="216.9586285714286" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.5714285714286" x2="334.5714285714286" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="53.57142857142856" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9587285714285" x2="216.9587285714285" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="216.9586285714286" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.5714285714286" x2="334.5714285714286" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="564.6776428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="53.57142857142856" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="564.6776428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="564.6776428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9587285714285" x2="216.9587285714285" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="564.6776428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="216.9586285714286" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="564.6776428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.5714285714286" x2="334.5714285714286" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="564.6777428571429" y2="564.6777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="53.57142857142856" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="564.6777428571429" y2="564.6777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="564.6777428571429" y2="564.6777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9587285714285" x2="216.9587285714285" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="564.6777428571429" y2="564.6777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="216.9586285714286" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="564.6777428571429" y2="564.6777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.5714285714286" x2="334.5714285714286" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="614.0365428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="53.57142857142856" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="614.0365428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="614.0365428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9587285714285" x2="216.9587285714285" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="614.0365428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="216.9586285714286" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="614.0365428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.5714285714286" x2="334.5714285714286" y1="589.3571428571429" y2="614.0365428571429"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" x="130.578125" xml:space="preserve" y="467.359375" zvalue="513">35kV     母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="130.578125" xml:space="preserve" y="483.359375" zvalue="513">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,199.97,327.199) scale(1,1) translate(0,0)" writing-mode="lr" x="199.97" xml:space="preserve" y="331.7" zvalue="514">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,304.97,327.199) scale(1,1) translate(0,0)" writing-mode="lr" x="304.97" xml:space="preserve" y="331.7" zvalue="515">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.5714,502.857) scale(1,1) translate(0,0)" writing-mode="lr" x="78.57142857142856" xml:space="preserve" y="507.3571428571429" zvalue="517">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.5714,528.357) scale(1,1) translate(0,0)" writing-mode="lr" x="78.57142857142856" xml:space="preserve" y="532.8571428571429" zvalue="518">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.5714,553.857) scale(1,1) translate(0,0)" writing-mode="lr" x="78.57142857142856" xml:space="preserve" y="558.3571428571429" zvalue="519">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.5714,579.357) scale(1,1) translate(0,0)" writing-mode="lr" x="78.57142857142856" xml:space="preserve" y="583.8571428571429" zvalue="520">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.5714,604.857) scale(1,1) translate(0,0)" writing-mode="lr" x="78.57142857142856" xml:space="preserve" y="609.3571428571429" zvalue="521">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,40.5714,186.357) scale(1,1) translate(0,0)" writing-mode="lr" x="40.57" xml:space="preserve" y="191.86" zvalue="522">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.821,186.357) scale(1,1) translate(0,0)" writing-mode="lr" x="226.82" xml:space="preserve" y="191.86" zvalue="523">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44.2589,210.607) scale(1,1) translate(0,0)" writing-mode="lr" x="44.26" xml:space="preserve" y="215.11" zvalue="524">35kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,518.75,430) scale(1,1) translate(0,0)" writing-mode="lr" x="518.75" xml:space="preserve" y="434.5" zvalue="556">10kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,668,226.5) scale(1,1) translate(0,0)" writing-mode="lr" x="668" xml:space="preserve" y="231" zvalue="558">10kV中晟硅厂Ⅰ回线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,821.75,426) scale(1,1) translate(0,0)" writing-mode="lr" x="821.75" xml:space="preserve" y="430.5" zvalue="567">10kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1161.75,432) scale(1,1) translate(0,0)" writing-mode="lr" x="1161.75" xml:space="preserve" y="436.5" zvalue="569">10kVⅢ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1538.75,430) scale(1,1) translate(0,0)" writing-mode="lr" x="1538.75" xml:space="preserve" y="434.5" zvalue="571">10kV Ⅳ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,992,226.5) scale(1,1) translate(0,0)" writing-mode="lr" x="992" xml:space="preserve" y="231" zvalue="630">10kV中晟硅厂Ⅱ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="193" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,547.5,500) scale(1,1) translate(0,0)" writing-mode="lr" x="547.5" xml:space="preserve" y="504.5" zvalue="716">051</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,517.5,648.5) scale(1,1) translate(0,0)" writing-mode="lr" x="517.5" xml:space="preserve" y="653" zvalue="793">#1动力变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,708.498,802.5) scale(1,1) translate(-3.01758e-13,0)" writing-mode="lr" x="708.5" xml:space="preserve" y="807" zvalue="796">#1电炉变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,722.5,487.5) scale(1,1) translate(0,0)" writing-mode="lr" x="722.5" xml:space="preserve" y="492" zvalue="798">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,735.912,577.5) scale(1,1) translate(0,0)" writing-mode="lr" x="735.91" xml:space="preserve" y="582" zvalue="799">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,665,500.5) scale(1,1) translate(0,0)" writing-mode="lr" x="665" xml:space="preserve" y="505" zvalue="804">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,647.8,589) scale(1,1) translate(0,0)" writing-mode="lr" x="647.8" xml:space="preserve" y="593.5" zvalue="805">#1PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,665.5,646.5) scale(1,1) translate(0,0)" writing-mode="lr" x="665.5" xml:space="preserve" y="651" zvalue="809">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,650,731) scale(1,1) translate(0,0)" writing-mode="lr" x="650" xml:space="preserve" y="735.5" zvalue="813">5400Kvar</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,860.75,500) scale(1,1) translate(0,0)" writing-mode="lr" x="860.75" xml:space="preserve" y="504.5" zvalue="821">052</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,829.5,648.5) scale(1,1) translate(0,0)" writing-mode="lr" x="829.5" xml:space="preserve" y="653" zvalue="824">#2动力变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1020.5,802.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1020.5" xml:space="preserve" y="807" zvalue="827">#2电炉变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1034.5,487.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1034.5" xml:space="preserve" y="492" zvalue="830">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1047.91,577.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1047.91" xml:space="preserve" y="582" zvalue="831">002</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,977,500.5) scale(1,1) translate(0,0)" writing-mode="lr" x="977" xml:space="preserve" y="505" zvalue="836">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,959.8,589) scale(1,1) translate(0,0)" writing-mode="lr" x="959.8" xml:space="preserve" y="593.5" zvalue="839">#2PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,977.5,646.5) scale(1,1) translate(0,0)" writing-mode="lr" x="977.5" xml:space="preserve" y="651" zvalue="843">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,962,731) scale(1,1) translate(0,0)" writing-mode="lr" x="962" xml:space="preserve" y="735.5" zvalue="844">5400Kvar</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="218" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,520,675) scale(1,1) translate(0,0)" writing-mode="lr" x="520" xml:space="preserve" y="679.5" zvalue="848">200kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="219" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,833,672) scale(1,1) translate(0,0)" writing-mode="lr" x="833" xml:space="preserve" y="676.5" zvalue="850">200kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="220" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,708,832) scale(1,1) translate(0,0)" writing-mode="lr" x="708" xml:space="preserve" y="836.5" zvalue="852">12500kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="221" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1019,824) scale(1,1) translate(0,0)" writing-mode="lr" x="1019" xml:space="preserve" y="828.5" zvalue="854">12500kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="266" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1171.38,500) scale(1,1) translate(0,0)" writing-mode="lr" x="1171.38" xml:space="preserve" y="504.5" zvalue="860">053</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="287" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1142.5,643.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1142.5" xml:space="preserve" y="648" zvalue="863">#3动力变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="265" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1328.5,802.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1328.5" xml:space="preserve" y="807" zvalue="865">#3电炉变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="264" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1342.5,487.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1342.5" xml:space="preserve" y="492" zvalue="868">3</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="263" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1355.91,577.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1355.91" xml:space="preserve" y="582" zvalue="869">003</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="262" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1285,500.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1285" xml:space="preserve" y="505" zvalue="873">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="261" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1267.8,589) scale(1,1) translate(0,0)" writing-mode="lr" x="1267.8" xml:space="preserve" y="593.5" zvalue="875">#3PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="260" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1285.5,646.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1285.5" xml:space="preserve" y="651" zvalue="879">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="272" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1270,731) scale(1,1) translate(0,0)" writing-mode="lr" x="1270" xml:space="preserve" y="735.5" zvalue="880">5400Kvar</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="269" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1327,824) scale(1,1) translate(0,0)" writing-mode="lr" x="1327" xml:space="preserve" y="828.5" zvalue="883">12500kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="288" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1143,663) scale(1,1) translate(0,0)" writing-mode="lr" x="1143" xml:space="preserve" y="667.5" zvalue="887">200kVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="289" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1248,226.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1248" xml:space="preserve" y="231" zvalue="890">10kV中晟硅厂Ⅲ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="294" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1541.75,500) scale(1,1) translate(0,0)" writing-mode="lr" x="1541.75" xml:space="preserve" y="504.5" zvalue="895">061</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="293" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1509.5,648.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1509.5" xml:space="preserve" y="653" zvalue="898">#1环保变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="295" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1512,675) scale(1,1) translate(0,0)" writing-mode="lr" x="1512" xml:space="preserve" y="679.5" zvalue="900">1000kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="315" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1633.75,500) scale(1,1) translate(0,0)" writing-mode="lr" x="1633.75" xml:space="preserve" y="504.5" zvalue="919">062</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="314" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1601.5,648.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1601.5" xml:space="preserve" y="653" zvalue="922">#2环保变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="316" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1604,675) scale(1,1) translate(0,0)" writing-mode="lr" x="1604" xml:space="preserve" y="679.5" zvalue="924">1000kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="322" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1729.75,500) scale(1,1) translate(0,0)" writing-mode="lr" x="1729.75" xml:space="preserve" y="504.5" zvalue="927">063</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="321" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1697.5,648.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1697.5" xml:space="preserve" y="653" zvalue="930">#3环保变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="323" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1700,675) scale(1,1) translate(0,0)" writing-mode="lr" x="1700" xml:space="preserve" y="679.5" zvalue="932">630kVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="328" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1620,226.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1620" xml:space="preserve" y="231" zvalue="935">10kV中晟硅厂动力线</text>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="51">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="51" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,127.571,210.468) scale(1,1) translate(0,0)" writing-mode="lr" x="127.72" xml:space="preserve" y="216.9" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="8" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,127.571,186.5) scale(1,1) translate(0,0)" writing-mode="lr" x="127.72" xml:space="preserve" y="192.93" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125586731014" ObjectName=""/>
   </metadata>
  </g>
  <g id="13">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="13" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,304.571,186.357) scale(1,1) translate(0,0)" writing-mode="lr" x="304.72" xml:space="preserve" y="192.79" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125586796550" ObjectName=""/>
   </metadata>
  </g>
  <g id="144">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="144" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,125.071,528.093) scale(1,1) translate(0,0)" writing-mode="lr" x="125.2" xml:space="preserve" y="533" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="143">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="143" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,125.071,553.218) scale(1,1) translate(0,-1.20508e-13)" writing-mode="lr" x="125.2" xml:space="preserve" y="558.13" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="142">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="142" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,125.071,578.343) scale(1,1) translate(0,-2.52173e-13)" writing-mode="lr" x="125.2" xml:space="preserve" y="583.25" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="141">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="141" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,126.5,502.968) scale(1,1) translate(0,0)" writing-mode="lr" x="126.63" xml:space="preserve" y="507.88" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="148">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="148" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,124.321,603.468) scale(1,1) translate(0,0)" writing-mode="lr" x="124.45" xml:space="preserve" y="608.38" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="U0"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="160">
   <use height="30" transform="rotate(0,334.196,327.857) scale(0.708333,0.665547) translate(133.235,159.739)" width="30" x="323.57" xlink:href="#State:红绿圆(方形)_0" y="317.87" zvalue="538"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.196,327.857) scale(0.708333,0.665547) translate(133.235,159.739)" width="30" x="323.57" y="317.87"/></g>
  <g id="54">
   <use height="30" transform="rotate(0,238.571,327.857) scale(0.708333,0.665547) translate(93.8603,159.739)" width="30" x="227.95" xlink:href="#State:红绿圆(方形)_0" y="317.87" zvalue="539"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,238.571,327.857) scale(0.708333,0.665547) translate(93.8603,159.739)" width="30" x="227.95" y="317.87"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="100">
   <path class="kv10" d="M 504 443 L 766 443" stroke-width="4" zvalue="555"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674246590468" ObjectName="10kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674246590468"/></metadata>
  <path d="M 504 443 L 766 443" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="82">
   <path class="kv10" d="M 815 443 L 1091 443" stroke-width="4" zvalue="566"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674246524932" ObjectName="10kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674246524932"/></metadata>
  <path d="M 815 443 L 1091 443" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="81">
   <path class="kv10" d="M 1123 443 L 1384 443" stroke-width="4" zvalue="568"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674246459396" ObjectName="10kVⅢ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674246459396"/></metadata>
  <path d="M 1123 443 L 1384 443" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="kv10" d="M 1472 443 L 1753 443" stroke-width="4" zvalue="570"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674246393860" ObjectName="10kV Ⅳ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674246393860"/></metadata>
  <path d="M 1472 443 L 1753 443" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="191">
   <use class="kv10" height="30" transform="rotate(0,524.5,501) scale(0.5,0.9) translate(517,54.1667)" width="30" x="517" xlink:href="#Disconnector:跌落刀闸_0" y="487.5" zvalue="715"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449843429382" ObjectName="051"/>
   <cge:TPSR_Ref TObjectID="6192449843429382"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,524.5,501) scale(0.5,0.9) translate(517,54.1667)" width="30" x="517" y="487.5"/></g>
  <g id="24">
   <use class="kv10" height="30" transform="rotate(0,711.5,488.5) scale(-1,-0.733333) translate(-1423,-1158.64)" width="15" x="704" xlink:href="#Disconnector:刀闸_0" y="477.5" zvalue="797"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449843625990" ObjectName="#1电炉变0011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449843625990"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,711.5,488.5) scale(-1,-0.733333) translate(-1423,-1158.64)" width="15" x="704" y="477.5"/></g>
  <g id="43">
   <use class="kv10" height="30" transform="rotate(0,650.5,501.5) scale(-1,-0.733333) translate(-1301,-1189.36)" width="15" x="643" xlink:href="#Disconnector:刀闸_0" y="490.5" zvalue="803"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449843691526" ObjectName="#1电炉变0019隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449843691526"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,650.5,501.5) scale(-1,-0.733333) translate(-1301,-1189.36)" width="15" x="643" y="490.5"/></g>
  <g id="49">
   <use class="kv10" height="30" transform="rotate(0,649.5,647.5) scale(-1,-0.733333) translate(-1299,-1534.45)" width="15" x="642" xlink:href="#Disconnector:刀闸_0" y="636.5" zvalue="808"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449843822598" ObjectName="#1电炉变0016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449843822598"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,649.5,647.5) scale(-1,-0.733333) translate(-1299,-1534.45)" width="15" x="642" y="636.5"/></g>
  <g id="216">
   <use class="kv10" height="30" transform="rotate(0,836.5,501) scale(0.5,0.9) translate(829,54.1667)" width="30" x="829" xlink:href="#Disconnector:跌落刀闸_0" y="487.5" zvalue="820"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449844412422" ObjectName="052"/>
   <cge:TPSR_Ref TObjectID="6192449844412422"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,836.5,501) scale(0.5,0.9) translate(829,54.1667)" width="30" x="829" y="487.5"/></g>
  <g id="201">
   <use class="kv10" height="30" transform="rotate(0,1023.5,488.5) scale(-1,-0.733333) translate(-2047,-1158.64)" width="15" x="1016" xlink:href="#Disconnector:刀闸_0" y="477.5" zvalue="828"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449844215814" ObjectName="#2电炉变0022隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449844215814"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1023.5,488.5) scale(-1,-0.733333) translate(-2047,-1158.64)" width="15" x="1016" y="477.5"/></g>
  <g id="107">
   <use class="kv10" height="30" transform="rotate(0,962.5,501.5) scale(-1,-0.733333) translate(-1925,-1189.36)" width="15" x="955" xlink:href="#Disconnector:刀闸_0" y="490.5" zvalue="835"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449844150278" ObjectName="#2电炉变0029隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449844150278"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,962.5,501.5) scale(-1,-0.733333) translate(-1925,-1189.36)" width="15" x="955" y="490.5"/></g>
  <g id="99">
   <use class="kv10" height="30" transform="rotate(0,961.5,647.5) scale(-1,-0.733333) translate(-1923,-1534.45)" width="15" x="954" xlink:href="#Disconnector:刀闸_0" y="636.5" zvalue="841"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449844019206" ObjectName="#2电炉变0026隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449844019206"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,961.5,647.5) scale(-1,-0.733333) translate(-1923,-1534.45)" width="15" x="954" y="636.5"/></g>
  <g id="286">
   <use class="kv10" height="30" transform="rotate(0,1144.5,501) scale(0.5,0.9) translate(1137,54.1667)" width="30" x="1137" xlink:href="#Disconnector:跌落刀闸_0" y="487.5" zvalue="859"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449844936710" ObjectName="053"/>
   <cge:TPSR_Ref TObjectID="6192449844936710"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1144.5,501) scale(0.5,0.9) translate(1137,54.1667)" width="30" x="1137" y="487.5"/></g>
  <g id="281">
   <use class="kv10" height="30" transform="rotate(0,1331.5,488.5) scale(-1,-0.733333) translate(-2663,-1158.64)" width="15" x="1324" xlink:href="#Disconnector:刀闸_0" y="477.5" zvalue="866"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449844740102" ObjectName="#3电炉变0033隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449844740102"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1331.5,488.5) scale(-1,-0.733333) translate(-2663,-1158.64)" width="15" x="1324" y="477.5"/></g>
  <g id="277">
   <use class="kv10" height="30" transform="rotate(0,1270.5,501.5) scale(-1,-0.733333) translate(-2541,-1189.36)" width="15" x="1263" xlink:href="#Disconnector:刀闸_0" y="490.5" zvalue="872"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449844674566" ObjectName="#3电炉变0039隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449844674566"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1270.5,501.5) scale(-1,-0.733333) translate(-2541,-1189.36)" width="15" x="1263" y="490.5"/></g>
  <g id="274">
   <use class="kv10" height="30" transform="rotate(0,1269.5,647.5) scale(-1,-0.733333) translate(-2539,-1534.45)" width="15" x="1262" xlink:href="#Disconnector:刀闸_0" y="636.5" zvalue="877"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449844543494" ObjectName="#3电炉变0036隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449844543494"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1269.5,647.5) scale(-1,-0.733333) translate(-2539,-1534.45)" width="15" x="1262" y="636.5"/></g>
  <g id="299">
   <use class="kv10" height="30" transform="rotate(0,1516.5,501) scale(0.5,0.9) translate(1509,54.1667)" width="30" x="1509" xlink:href="#Disconnector:跌落刀闸_0" y="487.5" zvalue="894"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449845133318" ObjectName="061"/>
   <cge:TPSR_Ref TObjectID="6192449845133318"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1516.5,501) scale(0.5,0.9) translate(1509,54.1667)" width="30" x="1509" y="487.5"/></g>
  <g id="320">
   <use class="kv10" height="30" transform="rotate(0,1608.5,501) scale(0.5,0.9) translate(1601,54.1667)" width="30" x="1601" xlink:href="#Disconnector:跌落刀闸_0" y="487.5" zvalue="918"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449845264390" ObjectName="062"/>
   <cge:TPSR_Ref TObjectID="6192449845264390"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1608.5,501) scale(0.5,0.9) translate(1601,54.1667)" width="30" x="1601" y="487.5"/></g>
  <g id="327">
   <use class="kv10" height="30" transform="rotate(0,1704.5,501) scale(0.5,0.9) translate(1697,54.1667)" width="30" x="1697" xlink:href="#Disconnector:跌落刀闸_0" y="487.5" zvalue="926"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449845395462" ObjectName="063"/>
   <cge:TPSR_Ref TObjectID="6192449845395462"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1704.5,501) scale(0.5,0.9) translate(1697,54.1667)" width="30" x="1697" y="487.5"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="199">
   <path class="kv10" d="M 524.5 487.5 L 524.5 443" stroke-width="1" zvalue="723"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@0" LinkObjectIDznd="100@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 524.5 487.5 L 524.5 443" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="18">
   <path class="kv10" d="M 524.5 513.6 L 524.5 557.7" stroke-width="1" zvalue="794"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@1" LinkObjectIDznd="15@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 524.5 513.6 L 524.5 557.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="40">
   <path class="kv10" d="M 711.44 477.69 L 711.44 443" stroke-width="1" zvalue="799"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="24@1" LinkObjectIDznd="100@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 711.44 477.69 L 711.44 443" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="kv10" d="M 711.41 499.14 L 711.41 558.15" stroke-width="1" zvalue="800"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="24@0" LinkObjectIDznd="38@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 711.41 499.14 L 711.41 558.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="kv10" d="M 711.41 598.3 L 711.41 713.12" stroke-width="1" zvalue="801"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@1" LinkObjectIDznd="19@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 711.41 598.3 L 711.41 713.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="kv10" d="M 650.44 490.69 L 650.44 460.5 L 711.44 460.5" stroke-width="1" zvalue="805"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="43@1" LinkObjectIDznd="40" MaxPinNum="2"/>
   </metadata>
  <path d="M 650.44 490.69 L 650.44 460.5 L 711.44 460.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="kv10" d="M 650.41 512.14 L 650.41 530.22" stroke-width="1" zvalue="806"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="43@0" LinkObjectIDznd="45@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 650.41 512.14 L 650.41 530.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="kv10" d="M 649.44 636.69 L 649.44 623.5 L 711.41 623.5" stroke-width="1" zvalue="814"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@1" LinkObjectIDznd="42" MaxPinNum="2"/>
   </metadata>
  <path d="M 649.44 636.69 L 649.44 623.5 L 711.41 623.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="kv10" d="M 649.41 658.14 L 649.41 679.5" stroke-width="1" zvalue="815"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@0" LinkObjectIDznd="52@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 649.41 658.14 L 649.41 679.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv10" d="M 668.5 283.34 L 668.5 443" stroke-width="1" zvalue="816"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92@0" LinkObjectIDznd="100@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 668.5 283.34 L 668.5 443" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="214">
   <path class="kv10" d="M 836.5 487.5 L 836.5 443" stroke-width="1" zvalue="822"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="216@0" LinkObjectIDznd="82@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 836.5 487.5 L 836.5 443" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="210">
   <path class="kv10" d="M 836.5 513.6 L 836.5 557.7" stroke-width="1" zvalue="825"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="216@1" LinkObjectIDznd="213@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 836.5 513.6 L 836.5 557.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="kv10" d="M 1023.41 499.14 L 1023.41 558.15" stroke-width="1" zvalue="833"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="201@0" LinkObjectIDznd="128@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1023.41 499.14 L 1023.41 558.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv10" d="M 1023.41 598.3 L 1023.41 713.12" stroke-width="1" zvalue="834"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="128@1" LinkObjectIDznd="209@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1023.41 598.3 L 1023.41 713.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv10" d="M 962.41 512.14 L 962.41 530.22" stroke-width="1" zvalue="840"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@0" LinkObjectIDznd="104@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 962.41 512.14 L 962.41 530.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="kv10" d="M 961.44 636.69 L 961.44 623.5 L 1023.41 623.5" stroke-width="1" zvalue="845"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@1" LinkObjectIDznd="108" MaxPinNum="2"/>
   </metadata>
  <path d="M 961.44 636.69 L 961.44 623.5 L 1023.41 623.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="kv10" d="M 961.41 658.14 L 961.41 679.5" stroke-width="1" zvalue="846"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="95@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 961.41 658.14 L 961.41 679.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="222">
   <path class="kv10" d="M 1023.44 477.69 L 1023.44 443" stroke-width="1" zvalue="855"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="201@1" LinkObjectIDznd="82@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1023.44 477.69 L 1023.44 443" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="258">
   <path class="kv10" d="M 962.44 490.69 L 962.44 443" stroke-width="1" zvalue="856"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@1" LinkObjectIDznd="82@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 962.44 490.69 L 962.44 443" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="259">
   <path class="kv10" d="M 992.5 283.34 L 992.5 443" stroke-width="1" zvalue="857"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="138@0" LinkObjectIDznd="82@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 992.5 283.34 L 992.5 443" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="285">
   <path class="kv10" d="M 1144.5 487.5 L 1144.5 443" stroke-width="1" zvalue="861"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="286@0" LinkObjectIDznd="81@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1144.5 487.5 L 1144.5 443" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="283">
   <path class="kv10" d="M 1144.5 513.6 L 1144.5 557.7" stroke-width="1" zvalue="863"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="286@1" LinkObjectIDznd="284@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1144.5 513.6 L 1144.5 557.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="279">
   <path class="kv10" d="M 1331.41 499.14 L 1331.41 558.15" stroke-width="1" zvalue="870"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="281@0" LinkObjectIDznd="280@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1331.41 499.14 L 1331.41 558.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="278">
   <path class="kv10" d="M 1331.41 598.3 L 1331.41 713.12" stroke-width="1" zvalue="871"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="280@1" LinkObjectIDznd="282@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1331.41 598.3 L 1331.41 713.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="275">
   <path class="kv10" d="M 1270.41 512.14 L 1270.41 530.22" stroke-width="1" zvalue="876"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="277@0" LinkObjectIDznd="276@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1270.41 512.14 L 1270.41 530.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="271">
   <path class="kv10" d="M 1269.44 636.69 L 1269.44 623.5 L 1331.41 623.5" stroke-width="1" zvalue="881"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="274@1" LinkObjectIDznd="278" MaxPinNum="2"/>
   </metadata>
  <path d="M 1269.44 636.69 L 1269.44 623.5 L 1331.41 623.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="270">
   <path class="kv10" d="M 1269.41 658.14 L 1269.41 679.5" stroke-width="1" zvalue="882"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="274@0" LinkObjectIDznd="273@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1269.41 658.14 L 1269.41 679.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="268">
   <path class="kv10" d="M 1331.44 477.69 L 1331.44 443" stroke-width="1" zvalue="884"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="281@1" LinkObjectIDznd="81@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1331.44 477.69 L 1331.44 443" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="291">
   <path class="kv10" d="M 1248.5 283.34 L 1248.5 443" stroke-width="1" zvalue="891"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="290@0" LinkObjectIDznd="81@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1248.5 283.34 L 1248.5 443" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="292">
   <path class="kv10" d="M 1270.44 490.69 L 1270.44 443" stroke-width="1" zvalue="892"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="277@1" LinkObjectIDznd="81@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1270.44 490.69 L 1270.44 443" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="298">
   <path class="kv10" d="M 1516.5 487.5 L 1516.5 443" stroke-width="1" zvalue="896"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="299@0" LinkObjectIDznd="80@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1516.5 487.5 L 1516.5 443" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="296">
   <path class="kv10" d="M 1516.5 513.6 L 1516.5 557.7" stroke-width="1" zvalue="899"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="299@1" LinkObjectIDznd="297@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1516.5 513.6 L 1516.5 557.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="319">
   <path class="kv10" d="M 1608.5 487.5 L 1608.5 443" stroke-width="1" zvalue="920"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="320@0" LinkObjectIDznd="80@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1608.5 487.5 L 1608.5 443" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="317">
   <path class="kv10" d="M 1608.5 513.6 L 1608.5 557.7" stroke-width="1" zvalue="923"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="320@1" LinkObjectIDznd="318@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1608.5 513.6 L 1608.5 557.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="326">
   <path class="kv10" d="M 1704.5 487.5 L 1704.5 443" stroke-width="1" zvalue="928"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="327@0" LinkObjectIDznd="80@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1704.5 487.5 L 1704.5 443" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="324">
   <path class="kv10" d="M 1704.5 513.6 L 1704.5 557.7" stroke-width="1" zvalue="931"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="327@1" LinkObjectIDznd="325@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1704.5 513.6 L 1704.5 557.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="330">
   <path class="kv10" d="M 1620.5 283.34 L 1620.5 443" stroke-width="1" zvalue="936"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="329@0" LinkObjectIDznd="80@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1620.5 283.34 L 1620.5 443" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="15">
   <use class="kv10" height="30" transform="rotate(0,523.6,592.5) scale(2.4,2.4) translate(-292.833,-324.625)" width="18" x="502" xlink:href="#EnergyConsumer:接地站用变两卷_0" y="556.5" zvalue="792"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449843494918" ObjectName="#1动力变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,523.6,592.5) scale(2.4,2.4) translate(-292.833,-324.625)" width="18" x="502" y="556.5"/></g>
  <g id="19">
   <use class="kv10" height="32" transform="rotate(0,710.123,752) scale(2.46875,2.46875) translate(-404.854,-423.892)" width="24" x="680.4984375" xlink:href="#EnergyConsumer:电炉变20210928_0" y="712.5" zvalue="795"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449843560454" ObjectName="#1电炉变"/>
   </metadata>
  <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(0,710.123,752) scale(2.46875,2.46875) translate(-404.854,-423.892)" width="24" x="680.4984375" y="712.5"/></g>
  <g id="213">
   <use class="kv10" height="30" transform="rotate(0,835.6,592.5) scale(2.4,2.4) translate(-474.833,-324.625)" width="18" x="814" xlink:href="#EnergyConsumer:接地站用变两卷_0" y="556.5" zvalue="823"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449844346886" ObjectName="#2动力变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,835.6,592.5) scale(2.4,2.4) translate(-474.833,-324.625)" width="18" x="814" y="556.5"/></g>
  <g id="209">
   <use class="kv10" height="32" transform="rotate(0,1022.12,752) scale(2.46875,2.46875) translate(-590.474,-423.892)" width="24" x="992.4984375" xlink:href="#EnergyConsumer:电炉变20210928_0" y="712.5" zvalue="826"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449844281350" ObjectName="#2电炉变"/>
   </metadata>
  <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(0,1022.12,752) scale(2.46875,2.46875) translate(-590.474,-423.892)" width="24" x="992.4984375" y="712.5"/></g>
  <g id="284">
   <use class="kv10" height="30" transform="rotate(0,1143.6,592.5) scale(2.4,2.4) translate(-654.5,-324.625)" width="18" x="1122" xlink:href="#EnergyConsumer:接地站用变两卷_0" y="556.5" zvalue="862"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449844871174" ObjectName="#3动力变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1143.6,592.5) scale(2.4,2.4) translate(-654.5,-324.625)" width="18" x="1122" y="556.5"/></g>
  <g id="282">
   <use class="kv10" height="32" transform="rotate(0,1330.12,752) scale(2.46875,2.46875) translate(-773.714,-423.892)" width="24" x="1300.4984375" xlink:href="#EnergyConsumer:电炉变20210928_0" y="712.5" zvalue="864"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449844805638" ObjectName="#3电炉变"/>
   </metadata>
  <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(0,1330.12,752) scale(2.46875,2.46875) translate(-773.714,-423.892)" width="24" x="1300.4984375" y="712.5"/></g>
  <g id="297">
   <use class="kv10" height="30" transform="rotate(0,1515.6,592.5) scale(2.4,2.4) translate(-871.5,-324.625)" width="18" x="1494" xlink:href="#EnergyConsumer:接地站用变两卷_0" y="556.5" zvalue="897"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449845067782" ObjectName="#1环保变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1515.6,592.5) scale(2.4,2.4) translate(-871.5,-324.625)" width="18" x="1494" y="556.5"/></g>
  <g id="318">
   <use class="kv10" height="30" transform="rotate(0,1607.6,592.5) scale(2.4,2.4) translate(-925.167,-324.625)" width="18" x="1586" xlink:href="#EnergyConsumer:接地站用变两卷_0" y="556.5" zvalue="921"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449845198854" ObjectName="#2环保变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1607.6,592.5) scale(2.4,2.4) translate(-925.167,-324.625)" width="18" x="1586" y="556.5"/></g>
  <g id="325">
   <use class="kv10" height="30" transform="rotate(0,1703.6,592.5) scale(2.4,2.4) translate(-981.167,-324.625)" width="18" x="1682" xlink:href="#EnergyConsumer:接地站用变两卷_0" y="556.5" zvalue="929"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449845329926" ObjectName="#3环保变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1703.6,592.5) scale(2.4,2.4) translate(-981.167,-324.625)" width="18" x="1682" y="556.5"/></g>
 </g>
 <g id="BreakerClass">
  <g id="38">
   <use class="kv10" height="20" transform="rotate(0,711.412,578.5) scale(2.2,2.2) translate(-382.043,-303.545)" width="10" x="700.4122229491392" xlink:href="#Breaker:小车断路器_0" y="556.5" zvalue="798"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924522147845" ObjectName="#1电炉变001断路器"/>
   <cge:TPSR_Ref TObjectID="6473924522147845"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,711.412,578.5) scale(2.2,2.2) translate(-382.043,-303.545)" width="10" x="700.4122229491392" y="556.5"/></g>
  <g id="128">
   <use class="kv10" height="20" transform="rotate(0,1023.41,578.5) scale(2.2,2.2) translate(-552.225,-303.545)" width="10" x="1012.412222949139" xlink:href="#Breaker:小车断路器_0" y="556.5" zvalue="829"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924522213381" ObjectName="#2电炉变002断路器"/>
   <cge:TPSR_Ref TObjectID="6473924522213381"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1023.41,578.5) scale(2.2,2.2) translate(-552.225,-303.545)" width="10" x="1012.412222949139" y="556.5"/></g>
  <g id="280">
   <use class="kv10" height="20" transform="rotate(0,1331.41,578.5) scale(2.2,2.2) translate(-720.225,-303.545)" width="10" x="1320.412222949139" xlink:href="#Breaker:小车断路器_0" y="556.5" zvalue="867"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924522278917" ObjectName="#3电炉变003断路器"/>
   <cge:TPSR_Ref TObjectID="6473924522278917"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1331.41,578.5) scale(2.2,2.2) translate(-720.225,-303.545)" width="10" x="1320.412222949139" y="556.5"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="45">
   <use class="kv10" height="29" transform="rotate(0,650.412,548.75) scale(1.2931,1.2931) translate(-145.815,-120.133)" width="11" x="643.300153983622" xlink:href="#Accessory:PT带保险_0" y="530" zvalue="804"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449843757062" ObjectName="#1PT"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,650.412,548.75) scale(1.2931,1.2931) translate(-145.815,-120.133)" width="11" x="643.300153983622" y="530"/></g>
  <g id="104">
   <use class="kv10" height="29" transform="rotate(0,962.412,548.75) scale(1.2931,1.2931) translate(-216.535,-120.133)" width="11" x="955.300153983622" xlink:href="#Accessory:PT带保险_0" y="530" zvalue="837"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449844084742" ObjectName="#2PT"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,962.412,548.75) scale(1.2931,1.2931) translate(-216.535,-120.133)" width="11" x="955.300153983622" y="530"/></g>
  <g id="276">
   <use class="kv10" height="29" transform="rotate(0,1270.41,548.75) scale(1.2931,1.2931) translate(-286.348,-120.133)" width="11" x="1263.300153983622" xlink:href="#Accessory:PT带保险_0" y="530" zvalue="874"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449844609030" ObjectName="#3PT"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1270.41,548.75) scale(1.2931,1.2931) translate(-286.348,-120.133)" width="11" x="1263.300153983622" y="530"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="52">
   <use class="kv10" height="29" transform="rotate(0,649.454,693.5) scale(1,1) translate(0,0)" width="10" x="644.4538896158059" xlink:href="#Compensator:硅厂电容器2021_0" y="679" zvalue="809"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449843888134" ObjectName="#1电容"/>
   <cge:TPSR_Ref TObjectID="6192449843888134"/></metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,649.454,693.5) scale(1,1) translate(0,0)" width="10" x="644.4538896158059" y="679"/></g>
  <g id="95">
   <use class="kv10" height="29" transform="rotate(0,961.454,693.5) scale(1,1) translate(0,0)" width="10" x="956.4538896158059" xlink:href="#Compensator:硅厂电容器2021_0" y="679" zvalue="842"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449843953670" ObjectName="#2电容"/>
   <cge:TPSR_Ref TObjectID="6192449843953670"/></metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,961.454,693.5) scale(1,1) translate(0,0)" width="10" x="956.4538896158059" y="679"/></g>
  <g id="273">
   <use class="kv10" height="29" transform="rotate(0,1269.45,693.5) scale(1,1) translate(0,0)" width="10" x="1264.453889615806" xlink:href="#Compensator:硅厂电容器2021_0" y="679" zvalue="878"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449844477958" ObjectName="#3电容"/>
   <cge:TPSR_Ref TObjectID="6192449844477958"/></metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1269.45,693.5) scale(1,1) translate(0,0)" width="10" x="1264.453889615806" y="679"/></g>
 </g>
</svg>