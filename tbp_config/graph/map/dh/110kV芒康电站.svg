<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549582233602" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Disconnector:特殊刀闸_0" viewBox="0,0,45,25">
   <use terminal-index="0" type="0" x="28.55" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="28.55" xlink:href="#terminal" y="24.45"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="3.5" y1="3.25" y2="9.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="2.35" y2="10.35"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.66666666666667" x2="5.583333333333332" y1="6.333333333333335" y2="6.333333333333335"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6" x2="11.6" y1="4.383333333333335" y2="8.133333333333336"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.699999999999999" x2="1.699999999999999" y1="4.25" y2="8.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.66666666666666" x2="27.83333333333334" y1="6.416666666666666" y2="22.08333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.58333333333334" x2="28.58333333333334" y1="22.66666666666667" y2="24.41666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.59150672768254" x2="28.59150672768254" y1="3.000000000000002" y2="0.6887951642647483"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="26.96666666666667" x2="29.93333333333333" y1="3.005662181544974" y2="3.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:特殊刀闸_1" viewBox="0,0,45,25">
   <use terminal-index="0" type="0" x="28.55" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="28.55" xlink:href="#terminal" y="24.45"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.699999999999999" x2="1.699999999999999" y1="4.25" y2="8.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="3.5" y1="3.25" y2="9.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="2.35" y2="10.35"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.66666666666667" x2="5.583333333333332" y1="6.333333333333335" y2="6.333333333333335"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6" x2="11.6" y1="4.383333333333335" y2="8.133333333333336"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.5" x2="28.5" y1="3.833333333333332" y2="22"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.58333333333334" x2="28.58333333333334" y1="22.66666666666667" y2="24.41666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.59150672768254" x2="28.59150672768254" y1="3.000000000000002" y2="0.6887951642647483"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="26.96666666666667" x2="29.93333333333333" y1="3.005662181544974" y2="3.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:特殊刀闸_2" viewBox="0,0,45,25">
   <use terminal-index="0" type="0" x="28.55" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="28.55" xlink:href="#terminal" y="24.45"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6" x2="11.6" y1="4.383333333333335" y2="8.133333333333336"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.66666666666667" x2="5.583333333333332" y1="6.333333333333335" y2="6.333333333333335"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="3.5" y1="3.25" y2="9.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="2.35" y2="10.35"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.699999999999999" x2="1.699999999999999" y1="4.25" y2="8.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.58333333333334" x2="28.58333333333334" y1="22.66666666666667" y2="24.41666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.59150672768254" x2="28.59150672768254" y1="3.000000000000002" y2="0.6887951642647483"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="26.96666666666667" x2="29.93333333333333" y1="3.005662181544974" y2="3.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="21.16666666666667" x2="35.5" y1="3.083333333333332" y2="24.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="35.5" x2="21.16666666666667" y1="3.166666666666664" y2="24.83333333333334"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="14.63292181069959"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="9.583333333333334" y2="14.63582329690123"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00459493611119" x2="10.08333333333333" y1="14.63240401999107" y2="19.41666666666666"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01979386477084" x2="20.08333333333333" y1="14.63518747193215" y2="19.41666666666666"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 10.1667 40.3233 L 19.9167 40.3233 L 15.0833 32.3333 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:110kVXIANLUPT_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="12.90667828048158" xlink:href="#terminal" y="37.28457520218116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27" x2="13" y1="26.75" y2="26.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.08333333333334" x2="29.08333333333334" y1="14.5" y2="14.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27" x2="27" y1="22.75" y2="26.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.93916361389983" x2="12.93916361389983" y1="33.96630546659506" y2="37.26790853551449"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.93916361389983" x2="12.93916361389983" y1="30.79012345679012" y2="22.71704491718448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.91963465760507" x2="12.91963465760507" y1="19.74560215515698" y2="13.06430394728183"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.01963465760507" x2="13.01963465760507" y1="22.75831495554599" y2="22.75831495554599"/>
   <ellipse cx="23.37" cy="20.6" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.556517488649495" x2="16.39655620359946" y1="22.67498162221265" y2="22.67498162221265"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.01963465760507" x2="13.01963465760507" y1="33.90122531314907" y2="33.90122531314907"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.639850821982819" x2="16.47988953693279" y1="19.66306207843401" y2="19.66306207843401"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.639850821982824" x2="16.47988953693279" y1="33.90122531314905" y2="33.90122531314905"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.639850821982824" x2="16.47988953693279" y1="30.80597243603708" y2="30.80597243603708"/>
   <ellipse cx="26.96" cy="14.43" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="10.72318415531615" x2="15" y1="12.99639541176734" y2="12.99639541176734"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.47318415531615" x2="14.08333333333333" y1="11.74639541176734" y2="11.74639541176734"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.97318415531615" x2="13.41666666666667" y1="10.24639541176734" y2="10.24639541176734"/>
   <ellipse cx="30.62" cy="20.6" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.25" x2="25.25" y1="20.5" y2="20.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.08333333333334" x2="33.08333333333334" y1="20.41666666666667" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="GroundDisconnector:12547_0" viewBox="0,0,45,45">
   <use terminal-index="0" type="0" x="14.5" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.77607149197439" x2="33.45507703101916" y1="38.59219204932567" y2="38.59219204932567"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.96414179204035" x2="14.96414179204035" y1="19.56938379593636" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.53290622076525" x2="33.53290622076525" y1="22.06726518291196" y2="38.52333818738227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.72817975850747" x2="33.72817975850747" y1="18.16909267494318" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.416666666666625" x2="14.8889734851558" y1="30.74450652239035" y2="19.51308960898588"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.84076764320101" x2="14.84076764320101" y1="32.44927516103209" y2="38.6666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.85395107780631" x2="12.1757277082614" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.61798904427344" x2="30.93976567472853" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.6532657596445" x2="12.86778351328125" y1="7.44637056159085" y2="7.44637056159085"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="35.41730372611165" x2="31.6318214797484" y1="7.446370561590845" y2="7.446370561590845"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.00950311944433" x2="14.2744904231945" y1="6.499999999999968" y2="6.499999999999968"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.77354108591146" x2="33.03852838966164" y1="6.499999999999968" y2="6.499999999999968"/>
   <rect fill-opacity="0" height="14.22" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,33.51,25.24) scale(-1,1) translate(-2360.48,0)" width="8.140000000000001" x="29.44" y="18.13"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
  </symbol>
  <symbol id="GroundDisconnector:12547_1" viewBox="0,0,45,45">
   <use terminal-index="0" type="0" x="14.5" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.53290622076525" x2="33.53290622076525" y1="22.06726518291196" y2="38.52333818738227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.77607149197439" x2="33.45507703101916" y1="38.59219204932567" y2="38.59219204932567"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.96414179204035" x2="14.96414179204035" y1="19.56938379593636" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.72817975850747" x2="33.72817975850747" y1="18.16909267494318" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.8889734851558" x2="14.8889734851558" y1="32.25" y2="19.51308960898588"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.84076764320101" x2="14.84076764320101" y1="32.44927516103209" y2="38.6666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.85395107780631" x2="12.1757277082614" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.61798904427344" x2="30.93976567472853" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.6532657596445" x2="12.86778351328125" y1="7.44637056159085" y2="7.44637056159085"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="35.41730372611165" x2="31.6318214797484" y1="7.446370561590845" y2="7.446370561590845"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.00950311944433" x2="14.2744904231945" y1="6.499999999999968" y2="6.499999999999968"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.77354108591146" x2="33.03852838966164" y1="6.499999999999968" y2="6.499999999999968"/>
   <rect fill-opacity="0" height="14.22" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,33.51,25.24) scale(-1,1) translate(-2360.48,0)" width="8.140000000000001" x="29.44" y="18.13"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
  </symbol>
  <symbol id="GroundDisconnector:12547_2" viewBox="0,0,45,45">
   <use terminal-index="0" type="0" x="14.5" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="19.88620689655174" x2="9.700000000000015" y1="19.49501474926254" y2="32.31404129793511"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="9.699999999999994" x2="20.24999999999999" y1="19.33333333333333" y2="32.38333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.77607149197439" x2="33.45507703101916" y1="38.59219204932567" y2="38.59219204932567"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.53290622076525" x2="33.53290622076525" y1="22.06726518291196" y2="38.52333818738227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.96414179204035" x2="14.96414179204035" y1="19.56938379593636" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.72817975850747" x2="33.72817975850747" y1="18.16909267494318" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.84076764320101" x2="14.84076764320101" y1="32.44927516103209" y2="38.6666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.85395107780631" x2="12.1757277082614" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.61798904427344" x2="30.93976567472853" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.6532657596445" x2="12.86778351328125" y1="7.44637056159085" y2="7.44637056159085"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="35.41730372611165" x2="31.6318214797484" y1="7.446370561590845" y2="7.446370561590845"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.00950311944433" x2="14.2744904231945" y1="6.499999999999968" y2="6.499999999999968"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.77354108591146" x2="33.03852838966164" y1="6.499999999999968" y2="6.499999999999968"/>
   <rect fill-opacity="0" height="14.22" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,33.51,25.24) scale(-1,1) translate(-2360.48,0)" width="8.140000000000001" x="29.44" y="18.13"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
  </symbol>
  <symbol id="Accessory:线路PT11带避雷器_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="20" xlink:href="#terminal" y="0.9166666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.91666666666667" x2="22.91666666666667" y1="23.75" y2="23.75"/>
   <rect fill-opacity="0" height="10" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,20,11) scale(1,1) translate(0,0)" width="6" x="17" y="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32" x2="20" y1="5" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32" x2="32" y1="17" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.13040845230975" x2="32.13040845230975" y1="16.76788570496156" y2="23.53635804601289"/>
   <rect fill-opacity="0" height="6.05" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,32.04,24.56) scale(1,1) translate(0,0)" width="15.34" x="24.37" y="21.53"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.98821300514558" x2="19.98821300514558" y1="1.029523490692871" y2="18.75"/>
   <path d="M 30.0147 23.4481 L 34.2865 23.4481 L 32.115 28.6155 L 30.0147 23.4481" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <ellipse cx="19.76" cy="32.23" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.76" cy="24.17" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.22179295459144" x2="32.22179295459144" y1="32.22550978083666" y2="36.2625308385742"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="29.01788570496155" x2="35.42570020422134" y1="36.23023467011234" y2="36.23023467011234"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="30.26384963537316" x2="34.53572596821302" y1="37.52208140858838" y2="37.52208140858838"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.15382387138148" x2="33.11176719059974" y1="38.49096646244535" y2="38.49096646244535"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.91666666666667" x2="22.91666666666667" y1="32.91666666666667" y2="32.91666666666667"/>
  </symbol>
  <symbol id="Accessory:五卷PT_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="25.16666666666667" y2="1"/>
   <rect fill-opacity="0" height="14" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,14) scale(1,1) translate(0,0)" width="6" x="7" y="7"/>
   <path d="M 5.11667 33.0667 L 5.11667 37.0667 L 8.11667 35.0667 L 5.11667 33.0667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.66666666666667" x2="9.999999999999998" y1="4" y2="4"/>
   <ellipse cx="13.48" cy="27.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="27.48744029990987" y2="26.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="29.96252026861409" y2="27.48744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="27.48744029990987" y2="26.24990031555776"/>
   <ellipse cx="13.48" cy="34.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="6.48" cy="27.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.44906826265991" x2="6.44906826265991" y1="29.96252026861409" y2="27.48744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.449068262659912" x2="8.849068262659907" y1="27.48744029990987" y2="26.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.449068262659907" x2="4.049068262659912" y1="27.48744029990987" y2="26.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="34.48744029990986" y2="33.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="34.48744029990986" y2="33.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="36.96252026861409" y2="34.48744029990989"/>
   <ellipse cx="6.48" cy="34.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.11944444444444" x2="29.63333333333333" y1="28.26666666666667" y2="28.26666666666667"/>
   <rect fill-opacity="0" height="16.15" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,27.29,13.93) scale(1,1) translate(0,0)" width="7.58" x="23.5" y="5.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="27.55" y1="3.93333333333333" y2="5.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="27.55" y1="5.85" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="24.55" y1="15.85" y2="12.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="30.55" y1="15.85" y2="12.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.16111111111111" x2="30.27222222222222" y1="26.88508771929826" y2="26.88508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="27.55" y1="21.85" y2="25.45"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.88333333333333" x2="31.55" y1="25.50350877192984" y2="25.50350877192984"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV芒康电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="256" x="45.75" xlink:href="logo.png" y="48.75"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,173.75,78.75) scale(1,1) translate(0,0)" writing-mode="lr" x="173.75" xml:space="preserve" y="82.25" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,174.25,78.4403) scale(1,1) translate(0,0)" writing-mode="lr" x="174.25" xml:space="preserve" y="87.44" zvalue="3">110kV芒康电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="273" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" width="72.88" x="50.19" y="175.25" zvalue="422"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" writing-mode="lr" x="86.63" xml:space="preserve" y="191.75" zvalue="422">信号一览</text>
  <line fill="none" id="35" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="379.75" x2="379.75" y1="16.75" y2="1046.75" zvalue="4"/>
  <line fill="none" id="33" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.750000000000455" x2="372.75" y1="152.6204926140824" y2="152.6204926140824" zvalue="6"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="101" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,680.653,959.279) scale(1,1) translate(-1.44095e-13,2.09534e-13)" writing-mode="lr" x="680.6531887118525" xml:space="preserve" y="963.7791630113579" zvalue="41">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,749.998,962.258) scale(1,1) translate(0,0)" writing-mode="lr" x="750" xml:space="preserve" y="966.76" zvalue="43">励磁变PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="99" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,618.158,690.738) scale(1,1) translate(0,0)" writing-mode="lr" x="618.16" xml:space="preserve" y="695.24" zvalue="45">6.3kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="98" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,544.63,760.828) scale(1,1) translate(0,0)" writing-mode="lr" x="544.63" xml:space="preserve" y="765.33" zvalue="47">励磁变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,653.534,797.825) scale(1,1) translate(0,0)" writing-mode="lr" x="653.53" xml:space="preserve" y="802.3200000000001" zvalue="49">611</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="96" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,638.261,733.646) scale(1,1) translate(0,0)" writing-mode="lr" x="638.26" xml:space="preserve" y="738.15" zvalue="51">6111c</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,714.732,874.743) scale(1,1) translate(0,0)" writing-mode="lr" x="714.73" xml:space="preserve" y="879.24" zvalue="53">6912c</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1080.76,931.63) scale(1,1) translate(0,0)" writing-mode="lr" x="1080.76" xml:space="preserve" y="936.13" zvalue="59">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1109.69,791.53) scale(1,1) translate(0,0)" writing-mode="lr" x="1109.69" xml:space="preserve" y="796.03" zvalue="61">614</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1054.46,742.375) scale(1,1) translate(0,0)" writing-mode="lr" x="1054.46" xml:space="preserve" y="746.87" zvalue="64">6141c</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="91" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,746.472,660.144) scale(1,1) translate(0,0)" writing-mode="lr" x="746.47" xml:space="preserve" y="664.64" zvalue="68">6901c</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,700.629,557.023) scale(1,1) translate(0,1.21575e-13)" writing-mode="lr" x="700.63" xml:space="preserve" y="561.52" zvalue="72">6.3kVⅠ母电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1293.23,671.149) scale(1,1) translate(0,0)" writing-mode="lr" x="1293.23" xml:space="preserve" y="675.65" zvalue="76">6.3kVⅡ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,659.775,311.803) scale(1,1) translate(0,0)" writing-mode="lr" x="659.78" xml:space="preserve" y="316.3" zvalue="87">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,987.042,379.615) scale(1,1) translate(0,0)" writing-mode="lr" x="987.04" xml:space="preserve" y="384.12" zvalue="89">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,996.152,434.277) scale(1,1) translate(0,0)" writing-mode="lr" x="996.15" xml:space="preserve" y="438.78" zvalue="91">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1017.95,411.827) scale(1,1) translate(0,0)" writing-mode="lr" x="1017.95" xml:space="preserve" y="416.33" zvalue="94">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1056.62,490.352) scale(1,1) translate(0,0)" writing-mode="lr" x="1056.62" xml:space="preserve" y="494.85" zvalue="97">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,858.546,536.379) scale(1,1) translate(0,0)" writing-mode="lr" x="858.55" xml:space="preserve" y="540.88" zvalue="102">1010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1020.4,667.242) scale(1,1) translate(0,0)" writing-mode="lr" x="1020.4" xml:space="preserve" y="671.74" zvalue="104">6011c</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="160" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1056.62,509.874) scale(1,1) translate(0,0)" writing-mode="lr" x="1056.62" xml:space="preserve" y="514.37" zvalue="106">10MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1464.36,379.615) scale(1,1) translate(0,0)" writing-mode="lr" x="1464.36" xml:space="preserve" y="384.12" zvalue="108">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1473.47,434.277) scale(1,1) translate(0,0)" writing-mode="lr" x="1473.47" xml:space="preserve" y="438.78" zvalue="110">102</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1495.27,411.827) scale(1,1) translate(0,0)" writing-mode="lr" x="1495.27" xml:space="preserve" y="416.33" zvalue="113">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1533.94,490.352) scale(1,1) translate(-1.00381e-12,-1.05557e-13)" writing-mode="lr" x="1533.94" xml:space="preserve" y="494.85" zvalue="116">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1334.86,536.379) scale(1,1) translate(0,0)" writing-mode="lr" x="1334.86" xml:space="preserve" y="540.88" zvalue="121">1020</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1492.84,669.242) scale(1,1) translate(0,0)" writing-mode="lr" x="1492.84" xml:space="preserve" y="673.74" zvalue="123">6022c</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="153" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1533.94,509.874) scale(1,1) translate(0,0)" writing-mode="lr" x="1533.94" xml:space="preserve" y="514.37" zvalue="125">5MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1624.47,655.636) scale(1,1) translate(0,0)" writing-mode="lr" x="1624.47" xml:space="preserve" y="660.14" zvalue="127">6902c</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1205.59,249.739) scale(1,1) translate(0,0)" writing-mode="lr" x="1205.59" xml:space="preserve" y="254.24" zvalue="137">111</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1197.03,315.681) scale(1,1) translate(0,0)" writing-mode="lr" x="1197.03" xml:space="preserve" y="320.18" zvalue="138">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1198.65,190.739) scale(1,1) translate(0,0)" writing-mode="lr" x="1198.65" xml:space="preserve" y="195.24" zvalue="141">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1180.8,83.1506) scale(1,1) translate(0,0)" writing-mode="lr" x="1180.8" xml:space="preserve" y="87.65000000000001" zvalue="145">110kV芒康电站线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1232.65,283.903) scale(1,1) translate(0,0)" writing-mode="lr" x="1232.65" xml:space="preserve" y="288.4" zvalue="148">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1232.65,236.291) scale(1,1) translate(0,0)" writing-mode="lr" x="1232.65" xml:space="preserve" y="240.79" zvalue="150">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1232.65,183.147) scale(1,1) translate(0,0)" writing-mode="lr" x="1232.65" xml:space="preserve" y="187.65" zvalue="152">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,595.045,960.756) scale(1,1) translate(0,0)" writing-mode="lr" x="595.05" xml:space="preserve" y="965.26" zvalue="197">机组PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,560.627,878.204) scale(1,1) translate(0,0)" writing-mode="lr" x="560.63" xml:space="preserve" y="882.7" zvalue="200">6911c</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,561.18,811.454) scale(1,1) translate(0,0)" writing-mode="lr" x="561.1799999999999" xml:space="preserve" y="815.95" zvalue="204">6913c</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="104" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,928.653,958.279) scale(1,1) translate(0,2.09312e-13)" writing-mode="lr" x="928.6531887118526" xml:space="preserve" y="962.7791630113578" zvalue="268">#2发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="103" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,998.998,962.258) scale(1,1) translate(1.07802e-13,0)" writing-mode="lr" x="999" xml:space="preserve" y="966.76" zvalue="270">励磁变PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="102" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,793.63,760.828) scale(1,1) translate(0,0)" writing-mode="lr" x="793.63" xml:space="preserve" y="765.33" zvalue="272">励磁变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,902.534,797.825) scale(1,1) translate(0,0)" writing-mode="lr" x="902.53" xml:space="preserve" y="802.3200000000001" zvalue="274">612</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,887.261,733.646) scale(1,1) translate(0,0)" writing-mode="lr" x="887.26" xml:space="preserve" y="738.15" zvalue="276">6121c</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,963.732,874.743) scale(1,1) translate(0,0)" writing-mode="lr" x="963.73" xml:space="preserve" y="879.24" zvalue="278">6922c</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,844.045,960.756) scale(1,1) translate(0,0)" writing-mode="lr" x="844.05" xml:space="preserve" y="965.26" zvalue="285">机组PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,809.627,879.204) scale(1,1) translate(0,0)" writing-mode="lr" x="809.63" xml:space="preserve" y="883.7" zvalue="288">6921c</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,810.18,811.454) scale(1,1) translate(0,0)" writing-mode="lr" x="810.1799999999999" xml:space="preserve" y="815.95" zvalue="290">6923c</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="197" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1337.65,964.279) scale(1,1) translate(-8.69935e-13,2.10645e-13)" writing-mode="lr" x="1337.653188711853" xml:space="preserve" y="968.7791630113578" zvalue="298">#3发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="196" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1406,968.258) scale(1,1) translate(0,0)" writing-mode="lr" x="1406" xml:space="preserve" y="972.76" zvalue="300">励磁变PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="194" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1200.63,766.828) scale(1,1) translate(9.17922e-13,0)" writing-mode="lr" x="1200.63" xml:space="preserve" y="771.33" zvalue="302">励磁变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="193" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1309.53,803.825) scale(1,1) translate(0,0)" writing-mode="lr" x="1309.53" xml:space="preserve" y="808.3200000000001" zvalue="304">613</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="191" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1294.26,739.646) scale(1,1) translate(0,0)" writing-mode="lr" x="1294.26" xml:space="preserve" y="744.15" zvalue="306">6132c</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="190" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1370.73,880.743) scale(1,1) translate(0,0)" writing-mode="lr" x="1370.73" xml:space="preserve" y="885.24" zvalue="308">6932c</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="189" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1251.05,966.756) scale(1,1) translate(0,0)" writing-mode="lr" x="1251.05" xml:space="preserve" y="971.26" zvalue="315">机组PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="187" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1216.63,885.204) scale(1,1) translate(0,0)" writing-mode="lr" x="1216.63" xml:space="preserve" y="889.7" zvalue="318">6931c</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="186" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1217.18,817.454) scale(1,1) translate(0,0)" writing-mode="lr" x="1217.18" xml:space="preserve" y="821.95" zvalue="320">6933c</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="221" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1543.76,933.63) scale(1,1) translate(0,0)" writing-mode="lr" x="1543.76" xml:space="preserve" y="938.13" zvalue="328">#2站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="220" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1571.57,816.863) scale(1,1) translate(0,0)" writing-mode="lr" x="1571.57" xml:space="preserve" y="821.36" zvalue="330">615</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="219" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1513.46,744.375) scale(1,1) translate(0,0)" writing-mode="lr" x="1513.46" xml:space="preserve" y="748.87" zvalue="333">6152c</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="231" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1608.24,556.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1608.24" xml:space="preserve" y="561" zvalue="340">6.3kVⅡ母电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1197.23,595.068) scale(1,1) translate(3.81856e-13,0)" writing-mode="lr" x="1197.234637204458" xml:space="preserve" y="599.5681818181819" zvalue="344">110kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1209.76,458.889) scale(1,1) translate(0,0)" writing-mode="lr" x="1209.76" xml:space="preserve" y="463.39" zvalue="346">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1234.93,412.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1234.93" xml:space="preserve" y="416.72" zvalue="348">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1235.44,509.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1235.44" xml:space="preserve" y="513.61" zvalue="354">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="106" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1023.4,587.612) scale(1,1) translate(-2.22711e-13,0)" writing-mode="lr" x="1023.4" xml:space="preserve" y="592.11" zvalue="359">6016c</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="107" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1016.85,627.506) scale(1,1) translate(-2.23155e-13,0)" writing-mode="lr" x="1016.85" xml:space="preserve" y="632.01" zvalue="361">601</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="115" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1492.7,625.506) scale(1,1) translate(0,0)" writing-mode="lr" x="1492.7" xml:space="preserve" y="630.01" zvalue="366">602</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1493.23,585.612) scale(1,1) translate(0,0)" writing-mode="lr" x="1493.23" xml:space="preserve" y="590.11" zvalue="368">6026c</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="390.25" y2="413"/>
  <line fill="none" id="297" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="494.8704926140824" y2="494.8704926140824" zvalue="400"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="1001.0816" y2="1029"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="295" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,954) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="960" zvalue="402">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="294" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54,988) scale(1,1) translate(0,0)" writing-mode="lr" x="54" xml:space="preserve" y="994" zvalue="403">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="293" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,988) scale(1,1) translate(0,0)" writing-mode="lr" x="236" xml:space="preserve" y="994" zvalue="404">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="292" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="1022" zvalue="405">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="291" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="235" xml:space="preserve" y="1022" zvalue="406">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="289" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5,568.5) scale(1,1) translate(0,0)" writing-mode="lr" x="77.5" xml:space="preserve" y="573" zvalue="408">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="288" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="202.4" xml:space="preserve" y="192.34" zvalue="409">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="287" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="307.4" xml:space="preserve" y="192.34" zvalue="410">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="286" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,59.5,262) scale(1,1) translate(0,0)" writing-mode="lr" x="17" xml:space="preserve" y="266.5" zvalue="411">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="285" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240,262) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="266.5" zvalue="412">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="284" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.6875,335.25) scale(1,1) translate(0,0)" writing-mode="lr" x="62.69" xml:space="preserve" y="339.75" zvalue="413">110kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="283" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,241.625,334) scale(1,1) translate(0,0)" writing-mode="lr" x="241.63" xml:space="preserve" y="338.5" zvalue="414">6.3kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="282" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,359.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="363.75" zvalue="415">6.3kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="272" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,288) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="292.5" zvalue="423">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="271" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239,288) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="292.5" zvalue="424">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="266" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,381.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="385.75" zvalue="427">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="262" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,380.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="384.75" zvalue="429">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="260" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,404.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="408.75" zvalue="431">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="258" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,403.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="407.75" zvalue="432">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="257" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,311) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="315.5" zvalue="433">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="254" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,238.5,310) scale(1,1) translate(0,0)" writing-mode="lr" x="196" xml:space="preserve" y="314.5" zvalue="435">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,209.5,953) scale(1,1) translate(0,0)" writing-mode="lr" x="209.5" xml:space="preserve" y="959" zvalue="439">MangKang-01-2013</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,679,978) scale(1,1) translate(0,0)" writing-mode="lr" x="679" xml:space="preserve" y="982.5" zvalue="452">3.5MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,929,976) scale(1,1) translate(0,0)" writing-mode="lr" x="929" xml:space="preserve" y="980.5" zvalue="454">3.5MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1336,982) scale(1,1) translate(0,0)" writing-mode="lr" x="1336" xml:space="preserve" y="986.5" zvalue="456">3.5MW</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="50.19" y="175.25" zvalue="422"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="v6300" height="30" transform="rotate(0,677.721,922.149) scale(1.39882,1.39882) translate(-187.245,-256.934)" width="30" x="656.7383087230376" xlink:href="#Generator:发电机_0" y="901.1664821930161" zvalue="40"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449575518213" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449575518213"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,677.721,922.149) scale(1.39882,1.39882) translate(-187.245,-256.934)" width="30" x="656.7383087230376" y="901.1664821930161"/></g>
  <g id="185">
   <use class="v6300" height="30" transform="rotate(0,927.721,923.149) scale(1.39882,1.39882) translate(-258.523,-257.219)" width="30" x="906.7383087230377" xlink:href="#Generator:发电机_0" y="902.1664821930161" zvalue="267"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449576239110" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449576239110"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,927.721,923.149) scale(1.39882,1.39882) translate(-258.523,-257.219)" width="30" x="906.7383087230377" y="902.1664821930161"/></g>
  <g id="218">
   <use class="v6300" height="30" transform="rotate(0,1334.72,929.149) scale(1.39882,1.39882) translate(-374.564,-258.93)" width="30" x="1313.738308723038" xlink:href="#Generator:发电机_0" y="908.1664821930161" zvalue="297"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449576828933" ObjectName="#3发电机"/>
   <cge:TPSR_Ref TObjectID="6192449576828933"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1334.72,929.149) scale(1.39882,1.39882) translate(-374.564,-258.93)" width="30" x="1313.738308723038" y="908.1664821930161"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="276">
   <use class="v6300" height="40" transform="rotate(0,745.493,923.149) scale(1.01415,1.01415) translate(-10.1157,-12.5938)" width="40" x="725.2095940925877" xlink:href="#Accessory:线路PT11带避雷器_0" y="902.8658932564888" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449575452677" ObjectName="#1发电机励磁变PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,745.493,923.149) scale(1.01415,1.01415) translate(-10.1157,-12.5938)" width="40" x="725.2095940925877" y="902.8658932564888"/></g>
  <g id="253">
   <use class="v6300" height="40" transform="rotate(0,592.702,753.617) scale(1.08512,-1.05626) translate(-44.7899,-1465.97)" width="40" x="571" xlink:href="#Accessory:线路PT11带避雷器_0" y="732.4919630393205" zvalue="46"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449575387141" ObjectName="#1发电机励磁变PT1"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,592.702,753.617) scale(1.08512,-1.05626) translate(-44.7899,-1465.97)" width="40" x="571" y="732.4919630393205"/></g>
  <g id="182">
   <use class="v6300" height="18" transform="rotate(0,703.241,596.5) scale(3.08159,-2.72222) translate(-459.422,-800.122)" width="15" x="680.1286055851065" xlink:href="#Accessory:PT8_0" y="571.9999542236328" zvalue="71"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449574993925" ObjectName="6.3kVⅠ母电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,703.241,596.5) scale(3.08159,-2.72222) translate(-459.422,-800.122)" width="15" x="680.1286055851065" y="571.9999542236328"/></g>
  <g id="356">
   <use class="kv110" height="40" transform="rotate(180,1112.3,112.689) scale(1.30243,-1.48446) translate(-252.23,-178.913)" width="40" x="1086.25" xlink:href="#Accessory:110kVXIANLUPT_0" y="83" zvalue="156"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449575583749" ObjectName="110kV芒康电站线线路PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(180,1112.3,112.689) scale(1.30243,-1.48446) translate(-252.23,-178.913)" width="40" x="1086.25" y="83"/></g>
  <g id="298">
   <use class="v6300" height="18" transform="rotate(0,595.045,920.896) scale(2.70439,2.25366) translate(-362.233,-500.99)" width="15" x="574.7623076072686" xlink:href="#Accessory:PT8_0" y="900.6133431379355" zvalue="196"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449573486597" ObjectName="#1发电机机组PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,595.045,920.896) scale(2.70439,2.25366) translate(-362.233,-500.99)" width="15" x="574.7623076072686" y="900.6133431379355"/></g>
  <g id="313">
   <use class="v6300" height="26" transform="rotate(90,725.8,757.062) scale(-0.976105,-0.976105) translate(-1469.51,-1532.97)" width="12" x="719.9433484979505" xlink:href="#Accessory:避雷器1_0" y="744.3727286776511" zvalue="206"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449573289990" ObjectName="#1发电机避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,725.8,757.062) scale(-0.976105,-0.976105) translate(-1469.51,-1532.97)" width="12" x="719.9433484979505" y="744.3727286776511"/></g>
  <g id="54">
   <use class="v6300" height="26" transform="rotate(180,1130.24,850.84) scale(-0.976105,-0.976105) translate(-2288.3,-1722.82)" width="12" x="1124.387792942395" xlink:href="#Accessory:避雷器1_0" y="838.150506455429" zvalue="264"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449575649285" ObjectName="#1站用变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,1130.24,850.84) scale(-0.976105,-0.976105) translate(-2288.3,-1722.82)" width="12" x="1124.387792942395" y="838.150506455429"/></g>
  <g id="184">
   <use class="v6300" height="40" transform="rotate(0,994.493,923.149) scale(1.01415,1.01415) translate(-13.589,-12.5938)" width="40" x="974.2095940925877" xlink:href="#Accessory:线路PT11带避雷器_0" y="902.8658932564889" zvalue="269"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449576173574" ObjectName="#2发电机励磁变PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,994.493,923.149) scale(1.01415,1.01415) translate(-13.589,-12.5938)" width="40" x="974.2095940925877" y="902.8658932564889"/></g>
  <g id="181">
   <use class="v6300" height="40" transform="rotate(0,841.702,753.617) scale(1.08512,-1.05626) translate(-64.3217,-1465.97)" width="40" x="820" xlink:href="#Accessory:线路PT11带避雷器_0" y="732.4919630393205" zvalue="271"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449576108038" ObjectName="#2发电机励磁变PT1"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,841.702,753.617) scale(1.08512,-1.05626) translate(-64.3217,-1465.97)" width="40" x="820" y="732.4919630393205"/></g>
  <g id="134">
   <use class="v6300" height="18" transform="rotate(0,844.045,920.896) scale(2.70439,2.25366) translate(-519.16,-500.99)" width="15" x="823.7623076072686" xlink:href="#Accessory:PT8_0" y="900.6133431379355" zvalue="284"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449575911429" ObjectName="#2发电机机组PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,844.045,920.896) scale(2.70439,2.25366) translate(-519.16,-500.99)" width="15" x="823.7623076072686" y="900.6133431379355"/></g>
  <g id="127">
   <use class="v6300" height="26" transform="rotate(90,974.8,757.062) scale(-0.976105,-0.976105) translate(-1973.61,-1532.97)" width="12" x="968.9433484979505" xlink:href="#Accessory:避雷器1_0" y="744.3727286776511" zvalue="291"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449575714821" ObjectName="#2发电机避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,974.8,757.062) scale(-0.976105,-0.976105) translate(-1973.61,-1532.97)" width="12" x="968.9433484979505" y="744.3727286776511"/></g>
  <g id="217">
   <use class="v6300" height="40" transform="rotate(0,1401.49,929.149) scale(1.01415,1.01415) translate(-19.2661,-12.6775)" width="40" x="1381.209594092588" xlink:href="#Accessory:线路PT11带避雷器_0" y="908.8658932564889" zvalue="299"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449576763397" ObjectName="#3发电机励磁变PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1401.49,929.149) scale(1.01415,1.01415) translate(-19.2661,-12.6775)" width="40" x="1381.209594092588" y="908.8658932564889"/></g>
  <g id="216">
   <use class="v6300" height="40" transform="rotate(0,1248.7,759.617) scale(1.08512,-1.05626) translate(-96.2473,-1477.65)" width="40" x="1227" xlink:href="#Accessory:线路PT11带避雷器_0" y="738.4919630393205" zvalue="301"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449576697862" ObjectName="#3发电机励磁变PT1"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1248.7,759.617) scale(1.08512,-1.05626) translate(-96.2473,-1477.65)" width="40" x="1227" y="738.4919630393205"/></g>
  <g id="207">
   <use class="v6300" height="18" transform="rotate(0,1251.05,926.896) scale(2.70439,2.25366) translate(-775.664,-504.328)" width="15" x="1230.762307607269" xlink:href="#Accessory:PT8_0" y="906.6133431379355" zvalue="314"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449576501254" ObjectName="#3发电机机组PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1251.05,926.896) scale(2.70439,2.25366) translate(-775.664,-504.328)" width="15" x="1230.762307607269" y="906.6133431379355"/></g>
  <g id="202">
   <use class="v6300" height="26" transform="rotate(90,1381.8,763.062) scale(-0.976105,-0.976105) translate(-2797.57,-1545.11)" width="12" x="1375.943348497951" xlink:href="#Accessory:避雷器1_0" y="750.3727286776511" zvalue="321"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449576304646" ObjectName="#3发电机避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1381.8,763.062) scale(-0.976105,-0.976105) translate(-2797.57,-1545.11)" width="12" x="1375.943348497951" y="750.3727286776511"/></g>
  <g id="223">
   <use class="v6300" height="26" transform="rotate(180,1598.8,805.062) scale(-0.976105,-0.976105) translate(-3236.88,-1630.14)" width="12" x="1592.943348497951" xlink:href="#Accessory:避雷器1_0" y="792.3727286776511" zvalue="336"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449576894469" ObjectName="#2站用变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,1598.8,805.062) scale(-0.976105,-0.976105) translate(-3236.88,-1630.14)" width="12" x="1592.943348497951" y="792.3727286776511"/></g>
  <g id="230">
   <use class="v6300" height="18" transform="rotate(0,1601.24,592.5) scale(3.08159,-2.72222) translate(-1066.01,-794.653)" width="15" x="1578.128605585106" xlink:href="#Accessory:PT8_0" y="567.9999542236328" zvalue="339"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449577091077" ObjectName="6.3kVⅡ母电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1601.24,592.5) scale(3.08159,-2.72222) translate(-1066.01,-794.653)" width="15" x="1578.128605585106" y="567.9999542236328"/></g>
  <g id="112">
   <use class="kv110" height="40" transform="rotate(0,1197.8,545.909) scale(1.375,1.375) translate(-319.173,-141.384)" width="40" x="1170.302819022639" xlink:href="#Accessory:五卷PT_0" y="518.4090909090909" zvalue="343"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449577484293" ObjectName="110kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1197.8,545.909) scale(1.375,1.375) translate(-319.173,-141.384)" width="40" x="1170.302819022639" y="518.4090909090909"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="255">
   <path class="v6300" d="M 559 706.33 L 1105.61 706.33" stroke-width="6" zvalue="44"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674234859525" ObjectName="6.3kVⅠ母"/>
   <cge:TPSR_Ref TObjectID="9288674234859525"/></metadata>
  <path d="M 559 706.33 L 1105.61 706.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="v6300" d="M 1247.15 709.08 L 1714.98 709.08" stroke-width="6" zvalue="75"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674234793989" ObjectName="6.3kVⅡ母"/>
   <cge:TPSR_Ref TObjectID="9288674234793989"/></metadata>
  <path d="M 1247.15 709.08 L 1714.98 709.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="251">
   <path class="kv110" d="M 661.48 337.18 L 1747.4 337.18" stroke-width="6" zvalue="86"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674234728452" ObjectName="110kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674234728452"/></metadata>
  <path d="M 661.48 337.18 L 1747.4 337.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="249">
   <use class="v6300" height="20" transform="rotate(0,678.584,798.757) scale(1.55425,1.39882) translate(-239.213,-223.748)" width="10" x="670.8123030891996" xlink:href="#Breaker:开关_0" y="784.7690399834298" zvalue="48"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924476076037" ObjectName="#1发电机611断路器"/>
   <cge:TPSR_Ref TObjectID="6473924476076037"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,678.584,798.757) scale(1.55425,1.39882) translate(-239.213,-223.748)" width="10" x="670.8123030891996" y="784.7690399834298"/></g>
  <g id="195">
   <use class="v6300" height="20" transform="rotate(0,1085.93,792.506) scale(1.55425,1.39882) translate(-384.473,-221.965)" width="10" x="1078.159093348474" xlink:href="#Breaker:开关_0" y="778.5175450491263" zvalue="60"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924476010501" ObjectName="#1站用变614断路器"/>
   <cge:TPSR_Ref TObjectID="6473924476010501"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1085.93,792.506) scale(1.55425,1.39882) translate(-384.473,-221.965)" width="10" x="1078.159093348474" y="778.5175450491263"/></g>
  <g id="267">
   <use class="kv110" height="20" transform="rotate(0,975.871,435.253) scale(1.19302,1.08456) translate(-156.92,-33.0903)" width="10" x="969.9054808841444" xlink:href="#Breaker:开关_0" y="424.4077889488317" zvalue="90"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924475944965" ObjectName="#1主变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473924475944965"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,975.871,435.253) scale(1.19302,1.08456) translate(-156.92,-33.0903)" width="10" x="969.9054808841444" y="424.4077889488317"/></g>
  <g id="314">
   <use class="kv110" height="20" transform="rotate(0,1453.19,435.253) scale(1.19302,1.08456) translate(-234.145,-33.0903)" width="10" x="1447.220851005609" xlink:href="#Breaker:开关_0" y="424.4077889488317" zvalue="109"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924475879429" ObjectName="#2主变110kV侧102断路器"/>
   <cge:TPSR_Ref TObjectID="6473924475879429"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1453.19,435.253) scale(1.19302,1.08456) translate(-234.145,-33.0903)" width="10" x="1447.220851005609" y="424.4077889488317"/></g>
  <g id="370">
   <use class="kv110" height="20" transform="rotate(0,1184.77,250.715) scale(1.19302,1.08456) translate(-190.718,-18.7022)" width="10" x="1178.804431721683" xlink:href="#Breaker:开关_0" y="239.8697059740355" zvalue="135"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924475813893" ObjectName="110kV芒康电站线111断路器"/>
   <cge:TPSR_Ref TObjectID="6473924475813893"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1184.77,250.715) scale(1.19302,1.08456) translate(-190.718,-18.7022)" width="10" x="1178.804431721683" y="239.8697059740355"/></g>
  <g id="170">
   <use class="v6300" height="20" transform="rotate(0,927.584,798.757) scale(1.55425,1.39882) translate(-328.006,-223.748)" width="10" x="919.8123030891996" xlink:href="#Breaker:开关_0" y="784.7690399834298" zvalue="273"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924476141573" ObjectName="#2发电机612断路器"/>
   <cge:TPSR_Ref TObjectID="6473924476141573"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,927.584,798.757) scale(1.55425,1.39882) translate(-328.006,-223.748)" width="10" x="919.8123030891996" y="784.7690399834298"/></g>
  <g id="215">
   <use class="v6300" height="20" transform="rotate(0,1334.58,804.757) scale(1.55425,1.39882) translate(-473.143,-225.458)" width="10" x="1326.8123030892" xlink:href="#Breaker:开关_0" y="790.7690399834298" zvalue="303"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924476207109" ObjectName="#3发电机613断路器"/>
   <cge:TPSR_Ref TObjectID="6473924476207109"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1334.58,804.757) scale(1.55425,1.39882) translate(-473.143,-225.458)" width="10" x="1326.8123030892" y="790.7690399834298"/></g>
  <g id="228">
   <use class="v6300" height="20" transform="rotate(0,1547.82,817.839) scale(1.55425,1.39882) translate(-549.183,-229.188)" width="10" x="1540.047982237363" xlink:href="#Breaker:开关_0" y="803.8508783824597" zvalue="329"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924476272645" ObjectName="#2站用变615断路器"/>
   <cge:TPSR_Ref TObjectID="6473924476272645"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1547.82,817.839) scale(1.55425,1.39882) translate(-549.183,-229.188)" width="10" x="1540.047982237363" y="803.8508783824597"/></g>
  <g id="47">
   <use class="v6300" height="20" transform="rotate(0,973.93,628.506) scale(1.55425,1.39882) translate(-344.534,-175.207)" width="10" x="966.1590933484742" xlink:href="#Breaker:开关_0" y="614.5175450491263" zvalue="360"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924476600325" ObjectName="#1主变6.3kV侧601断路器"/>
   <cge:TPSR_Ref TObjectID="6473924476600325"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,973.93,628.506) scale(1.55425,1.39882) translate(-344.534,-175.207)" width="10" x="966.1590933484742" y="614.5175450491263"/></g>
  <g id="108">
   <use class="v6300" height="20" transform="rotate(0,1452.93,626.506) scale(1.55425,1.39882) translate(-515.346,-174.637)" width="10" x="1445.159093348474" xlink:href="#Breaker:开关_0" y="612.5175450491263" zvalue="365"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924476665861" ObjectName="#2主变6.3kV侧602断路器"/>
   <cge:TPSR_Ref TObjectID="6473924476665861"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1452.93,626.506) scale(1.55425,1.39882) translate(-515.346,-174.637)" width="10" x="1445.159093348474" y="612.5175450491263"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="246">
   <use class="v6300" height="25" transform="rotate(0,678.502,732.326) scale(0.345388,0.911825) translate(1271.23,69.7151)" width="45" x="670.7306055005888" xlink:href="#Disconnector:特殊刀闸_0" y="720.9281145056467" zvalue="50"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449575321605" ObjectName="#1发电机6111c隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449575321605"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,678.502,732.326) scale(0.345388,0.911825) translate(1271.23,69.7151)" width="45" x="670.7306055005888" y="720.9281145056467"/></g>
  <g id="242">
   <use class="v6300" height="25" transform="rotate(0,746.527,874.925) scale(0.345388,0.911825) translate(1400.16,83.5047)" width="45" x="738.7559798570895" xlink:href="#Disconnector:特殊刀闸_0" y="863.5267475711245" zvalue="52"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449575256069" ObjectName="#1发电机6912c隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449575256069"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,746.527,874.925) scale(0.345388,0.911825) translate(1400.16,83.5047)" width="45" x="738.7559798570895" y="863.5267475711245"/></g>
  <g id="192">
   <use class="v6300" height="25" transform="rotate(0,1086.17,743.307) scale(0.345388,0.911825) translate(2043.88,70.777)" width="45" x="1078.396955180224" xlink:href="#Disconnector:特殊刀闸_0" y="731.9092963335944" zvalue="63"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449575124997" ObjectName="#1站用变6141c隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449575124997"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1086.17,743.307) scale(0.345388,0.911825) translate(2043.88,70.777)" width="45" x="1078.396955180224" y="731.9092963335944"/></g>
  <g id="188">
   <use class="v6300" height="25" transform="rotate(0,700.745,663.12) scale(-0.36152,-0.954414) translate(-2653.44,-1358.48)" width="45" x="692.6102942641758" xlink:href="#Disconnector:特殊刀闸_0" y="651.1895425605613" zvalue="67"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449577943045" ObjectName="6.3kVⅠ母电压互感器6901c隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449577943045"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,700.745,663.12) scale(-0.36152,-0.954414) translate(-2653.44,-1358.48)" width="45" x="692.6102942641758" y="651.1895425605613"/></g>
  <g id="268">
   <use class="kv110" height="30" transform="rotate(0,975.871,380.592) scale(1.08456,0.795345) translate(-75.4526,94.8625)" width="15" x="967.7363585348306" xlink:href="#Disconnector:刀闸_0" y="368.6613449037437" zvalue="88"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449574928389" ObjectName="#1主变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449574928389"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,975.871,380.592) scale(1.08456,0.795345) translate(-75.4526,94.8625)" width="15" x="967.7363585348306" y="368.6613449037437"/></g>
  <g id="281">
   <use class="v6300" height="25" transform="rotate(0,973.925,668.612) scale(0.36152,0.954414) translate(1705.68,31.3655)" width="45" x="965.7904748987728" xlink:href="#Disconnector:特殊刀闸_0" y="656.682162393403" zvalue="103"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449574600710" ObjectName="#1主变6.3kV侧6011c隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449574600710"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,973.925,668.612) scale(0.36152,0.954414) translate(1705.68,31.3655)" width="45" x="965.7904748987728" y="656.682162393403"/></g>
  <g id="315">
   <use class="kv110" height="30" transform="rotate(0,1453.19,380.592) scale(1.08456,0.795345) translate(-112.668,94.8625)" width="15" x="1445.051728656295" xlink:href="#Disconnector:刀闸_0" y="368.6613449037437" zvalue="107"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449574535174" ObjectName="#2主变110kV侧1021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449574535174"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1453.19,380.592) scale(1.08456,0.795345) translate(-112.668,94.8625)" width="15" x="1445.051728656295" y="368.6613449037437"/></g>
  <g id="303">
   <use class="v6300" height="25" transform="rotate(0,1451.37,668.612) scale(0.36152,0.954414) translate(2548.88,31.3655)" width="45" x="1443.230907381011" xlink:href="#Disconnector:特殊刀闸_0" y="656.6821623934029" zvalue="122"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449574207494" ObjectName="#2主变6.3kV侧6022c隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449574207494"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1451.37,668.612) scale(0.36152,0.954414) translate(2548.88,31.3655)" width="45" x="1443.230907381011" y="656.6821623934029"/></g>
  <g id="338">
   <use class="v6300" height="25" transform="rotate(0,1599.74,656.612) scale(-0.36152,-0.954414) translate(-6039.13,-1345.16)" width="45" x="1591.603046578836" xlink:href="#Disconnector:特殊刀闸_0" y="644.6821622087033" zvalue="126"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449574141958" ObjectName="6.3kVⅡ母电压互感器6902c隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449574141958"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1599.74,656.612) scale(-0.36152,-0.954414) translate(-6039.13,-1345.16)" width="45" x="1591.603046578836" y="644.6821622087033"/></g>
  <g id="369">
   <use class="kv110" height="30" transform="rotate(0,1184.77,297.135) scale(-1.08456,-0.795345) translate(-2276.53,-673.796)" width="15" x="1176.635309385299" xlink:href="#Disconnector:刀闸_0" y="285.2043760437488" zvalue="136"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449574076422" ObjectName="110kV芒康电站线1111隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449574076422"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1184.77,297.135) scale(-1.08456,-0.795345) translate(-2276.53,-673.796)" width="15" x="1176.635309385299" y="285.2043760437488"/></g>
  <g id="368">
   <use class="kv110" height="30" transform="rotate(0,1184.77,191.715) scale(-1.08456,-0.795345) translate(-2276.53,-435.832)" width="15" x="1176.635309411157" xlink:href="#Disconnector:刀闸_0" y="179.7850172561778" zvalue="139"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449574010886" ObjectName="110kV芒康电站线1116隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449574010886"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1184.77,191.715) scale(-1.08456,-0.795345) translate(-2276.53,-435.832)" width="15" x="1176.635309411157" y="179.7850172561778"/></g>
  <g id="301">
   <use class="v6300" height="25" transform="rotate(0,592.5,876.928) scale(0.345388,0.911825) translate(1108.23,83.6984)" width="45" x="584.7292478031624" xlink:href="#Disconnector:特殊刀闸_0" y="865.5301477291957" zvalue="198"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449573421061" ObjectName="#1发电机6911c隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449573421061"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,592.5,876.928) scale(0.345388,0.911825) translate(1108.23,83.6984)" width="45" x="584.7292478031624" y="865.5301477291957"/></g>
  <g id="308">
   <use class="v6300" height="25" transform="rotate(0,592.303,810.928) scale(0.345388,0.911825) translate(1107.86,77.3161)" width="45" x="584.5315694243909" xlink:href="#Disconnector:特殊刀闸_0" y="799.5301477291957" zvalue="202"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449573355526" ObjectName="#1发电机6913c隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449573355526"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,592.303,810.928) scale(0.345388,0.911825) translate(1107.86,77.3161)" width="45" x="584.5315694243909" y="799.5301477291957"/></g>
  <g id="141">
   <use class="v6300" height="25" transform="rotate(0,927.502,732.326) scale(0.345388,0.911825) translate(1743.16,69.7151)" width="45" x="919.7306055005888" xlink:href="#Disconnector:特殊刀闸_0" y="720.9281145056467" zvalue="275"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449576042501" ObjectName="#2发电机6121c隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449576042501"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,927.502,732.326) scale(0.345388,0.911825) translate(1743.16,69.7151)" width="45" x="919.7306055005888" y="720.9281145056467"/></g>
  <g id="140">
   <use class="v6300" height="25" transform="rotate(0,995.527,874.925) scale(0.345388,0.911825) translate(1872.09,83.5047)" width="45" x="987.7559798570895" xlink:href="#Disconnector:特殊刀闸_0" y="863.5267475711244" zvalue="277"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449575976965" ObjectName="#2发电机6922c隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449575976965"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,995.527,874.925) scale(0.345388,0.911825) translate(1872.09,83.5047)" width="45" x="987.7559798570895" y="863.5267475711244"/></g>
  <g id="133">
   <use class="v6300" height="25" transform="rotate(0,841.5,877.928) scale(0.345388,0.911825) translate(1580.16,83.7951)" width="45" x="833.7292478031624" xlink:href="#Disconnector:特殊刀闸_0" y="866.5301477291957" zvalue="286"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449575845893" ObjectName="#2发电机6921c隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449575845893"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,841.5,877.928) scale(0.345388,0.911825) translate(1580.16,83.7951)" width="45" x="833.7292478031624" y="866.5301477291957"/></g>
  <g id="131">
   <use class="v6300" height="25" transform="rotate(0,841.303,810.928) scale(0.345388,0.911825) translate(1579.79,77.3161)" width="45" x="833.5315694243909" xlink:href="#Disconnector:特殊刀闸_0" y="799.5301477291957" zvalue="289"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449575780357" ObjectName="#2发电机6923c隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449575780357"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,841.303,810.928) scale(0.345388,0.911825) translate(1579.79,77.3161)" width="45" x="833.5315694243909" y="799.5301477291957"/></g>
  <g id="214">
   <use class="v6300" height="25" transform="rotate(0,1334.5,738.326) scale(0.345388,0.911825) translate(2514.54,70.2953)" width="45" x="1326.730605500589" xlink:href="#Disconnector:特殊刀闸_0" y="726.9281145056467" zvalue="305"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449576632326" ObjectName="#3发电机6132c隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449576632326"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1334.5,738.326) scale(0.345388,0.911825) translate(2514.54,70.2953)" width="45" x="1326.730605500589" y="726.9281145056467"/></g>
  <g id="213">
   <use class="v6300" height="25" transform="rotate(0,1402.53,880.925) scale(0.345388,0.911825) translate(2643.47,84.0849)" width="45" x="1394.75597985709" xlink:href="#Disconnector:特殊刀闸_0" y="869.5267475711244" zvalue="307"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449576566790" ObjectName="#3发电机6932c隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449576566790"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1402.53,880.925) scale(0.345388,0.911825) translate(2643.47,84.0849)" width="45" x="1394.75597985709" y="869.5267475711244"/></g>
  <g id="206">
   <use class="v6300" height="25" transform="rotate(0,1248.5,883.928) scale(0.345388,0.911825) translate(2351.55,84.3753)" width="45" x="1240.729247803162" xlink:href="#Disconnector:特殊刀闸_0" y="872.5301477291957" zvalue="316"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449576435718" ObjectName="#3发电机6931c隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449576435718"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1248.5,883.928) scale(0.345388,0.911825) translate(2351.55,84.3753)" width="45" x="1240.729247803162" y="872.5301477291957"/></g>
  <g id="203">
   <use class="v6300" height="25" transform="rotate(0,1248.3,816.928) scale(0.345388,0.911825) translate(2351.17,77.8963)" width="45" x="1240.531569424391" xlink:href="#Disconnector:特殊刀闸_0" y="805.5301477291957" zvalue="319"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449576370182" ObjectName="#3发电机6933c隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449576370182"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1248.3,816.928) scale(0.345388,0.911825) translate(2351.17,77.8963)" width="45" x="1240.531569424391" y="805.5301477291957"/></g>
  <g id="226">
   <use class="v6300" height="25" transform="rotate(0,1549.17,745.307) scale(0.345388,0.911825) translate(2921.4,70.9704)" width="45" x="1541.396955180224" xlink:href="#Disconnector:特殊刀闸_0" y="733.9092963335944" zvalue="332"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449576960005" ObjectName="#2站用变6152c隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449576960005"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1549.17,745.307) scale(0.345388,0.911825) translate(2921.4,70.9704)" width="45" x="1541.396955180224" y="733.9092963335944"/></g>
  <g id="49">
   <use class="kv110" height="30" transform="rotate(0,1183.98,459.889) scale(1.11111,0.814815) translate(-117.565,101.742)" width="15" x="1175.651525218077" xlink:href="#Disconnector:刀闸_0" y="447.6666666666667" zvalue="345"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449577418757" ObjectName="110kV母线电压互感器1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449577418757"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1183.98,459.889) scale(1.11111,0.814815) translate(-117.565,101.742)" width="15" x="1175.651525218077" y="447.6666666666667"/></g>
  <g id="43">
   <use class="v6300" height="25" transform="rotate(0,973.925,588.612) scale(0.36152,0.954414) translate(1705.68,27.5444)" width="45" x="965.7904748987728" xlink:href="#Disconnector:特殊刀闸_0" y="576.682162393403" zvalue="358"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449579384838" ObjectName="#1主变6.3kV侧6016c隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449579384838"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,973.925,588.612) scale(0.36152,0.954414) translate(1705.68,27.5444)" width="45" x="965.7904748987728" y="576.682162393403"/></g>
  <g id="110">
   <use class="v6300" height="25" transform="rotate(0,1452.37,586.612) scale(0.36152,0.954414) translate(2550.65,27.4488)" width="45" x="1444.230907381011" xlink:href="#Disconnector:特殊刀闸_0" y="574.682162393403" zvalue="367"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449579450374" ObjectName="#2主变6.3kV侧6026c隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449579450374"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1452.37,586.612) scale(0.36152,0.954414) translate(2550.65,27.4488)" width="45" x="1444.230907381011" y="574.682162393403"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="180">
   <path class="v6300" d="M 678.53 785.38 L 678.52 743.22" stroke-width="1" zvalue="54"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="249@0" LinkObjectIDznd="246@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 678.53 785.38 L 678.52 743.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="178">
   <path class="v6300" d="M 678.69 812.12 L 678.69 901.52" stroke-width="1" zvalue="55"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="249@1" LinkObjectIDznd="850@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 678.69 812.12 L 678.69 901.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="177">
   <path class="v6300" d="M 745.49 903.8 L 745.49 885.82" stroke-width="1" zvalue="56"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="276@0" LinkObjectIDznd="242@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 745.49 903.8 L 745.49 885.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="176">
   <path class="v6300" d="M 746.54 864.21 L 746.56 839.03 L 678.69 839.03" stroke-width="1" zvalue="57"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="242@0" LinkObjectIDznd="178" MaxPinNum="2"/>
   </metadata>
  <path d="M 746.54 864.21 L 746.56 839.03 L 678.69 839.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="175">
   <path class="v6300" d="M 1086.03 805.86 L 1086.03 864.66" stroke-width="1" zvalue="62"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="195@1" LinkObjectIDznd="205@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1086.03 805.86 L 1086.03 864.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="174">
   <path class="v6300" d="M 1086.19 706.33 L 1086.19 732.59" stroke-width="1" zvalue="65"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="255@2" LinkObjectIDznd="192@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1086.19 706.33 L 1086.19 732.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="173">
   <path class="v6300" d="M 1086.19 754.2 L 1086.19 779.12" stroke-width="1" zvalue="66"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="192@1" LinkObjectIDznd="195@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1086.19 754.2 L 1086.19 779.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="172">
   <path class="v6300" d="M 700.73 674.33 L 700.73 706.33" stroke-width="1" zvalue="69"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="188@0" LinkObjectIDznd="255@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 700.73 674.33 L 700.73 706.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="171">
   <path class="v6300" d="M 700.73 651.71 L 700.73 619.26" stroke-width="1" zvalue="70"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="188@1" LinkObjectIDznd="182@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 700.73 651.71 L 700.73 619.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="166">
   <path class="kv110" d="M 975.94 392.32 L 975.94 424.88" stroke-width="1" zvalue="92"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="268@1" LinkObjectIDznd="267@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 975.94 392.32 L 975.94 424.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="165">
   <path class="kv110" d="M 975.97 369.06 L 975.97 337.18" stroke-width="1" zvalue="95"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="268@0" LinkObjectIDznd="251@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 975.97 369.06 L 975.97 337.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="164">
   <path class="kv110" d="M 975.95 445.61 L 975.95 463.18" stroke-width="1" zvalue="98"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="267@1" LinkObjectIDznd="263@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 975.95 445.61 L 975.95 463.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="163">
   <path class="kv110" d="M 1007.38 399.95 L 975.94 399.95" stroke-width="1" zvalue="99"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="265@0" LinkObjectIDznd="166" MaxPinNum="2"/>
   </metadata>
  <path d="M 1007.38 399.95 L 975.94 399.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="162">
   <path class="kv110" d="M 974.12 488.84 L 887.8 488.84 L 887.8 512.79" stroke-width="1" zvalue="100"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="263@2" LinkObjectIDznd="259@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 974.12 488.84 L 887.8 488.84 L 887.8 512.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="161">
   <path class="v6300" d="M 973.94 680.02 L 973.94 706.33" stroke-width="1" zvalue="105"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="281@1" LinkObjectIDznd="255@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 973.94 680.02 L 973.94 706.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="159">
   <path class="kv110" d="M 1453.25 392.32 L 1453.25 424.88" stroke-width="1" zvalue="111"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="315@1" LinkObjectIDznd="314@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1453.25 392.32 L 1453.25 424.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="158">
   <path class="kv110" d="M 1453.28 369.06 L 1453.28 337.18" stroke-width="1" zvalue="114"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="315@0" LinkObjectIDznd="251@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1453.28 369.06 L 1453.28 337.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="157">
   <path class="kv110" d="M 1453.27 445.61 L 1453.27 463.18" stroke-width="1" zvalue="117"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="314@1" LinkObjectIDznd="310@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1453.27 445.61 L 1453.27 463.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="156">
   <path class="kv110" d="M 1484.69 399.95 L 1453.25 399.95" stroke-width="1" zvalue="118"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="312@0" LinkObjectIDznd="159" MaxPinNum="2"/>
   </metadata>
  <path d="M 1484.69 399.95 L 1453.25 399.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="155">
   <path class="kv110" d="M 1451.43 488.84 L 1364.12 488.84 L 1364.12 512.79" stroke-width="1" zvalue="119"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="310@2" LinkObjectIDznd="306@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1451.43 488.84 L 1364.12 488.84 L 1364.12 512.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="154">
   <path class="v6300" d="M 1451.38 680.02 L 1451.38 709.08" stroke-width="1" zvalue="124"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="303@1" LinkObjectIDznd="179@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1451.38 680.02 L 1451.38 709.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="152">
   <path class="v6300" d="M 1599.72 667.83 L 1599.72 709.08" stroke-width="1" zvalue="128"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="338@0" LinkObjectIDznd="179@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1599.72 667.83 L 1599.72 709.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="151">
   <path class="v6300" d="M 1599.72 645.21 L 1599.72 615.26" stroke-width="1" zvalue="129"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="338@1" LinkObjectIDznd="230@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1599.72 645.21 L 1599.72 615.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="v6300" d="M 678.52 721.61 L 678.52 706.33" stroke-width="1" zvalue="134"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="246@0" LinkObjectIDznd="255@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 678.52 721.61 L 678.52 706.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="148">
   <path class="kv110" d="M 1184.67 308.67 L 1184.67 337.18" stroke-width="1" zvalue="140"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="369@0" LinkObjectIDznd="46" MaxPinNum="2"/>
   </metadata>
  <path d="M 1184.67 308.67 L 1184.67 337.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="147">
   <path class="kv110" d="M 1184.7 285.41 L 1184.7 261.07" stroke-width="1" zvalue="142"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="369@1" LinkObjectIDznd="370@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1184.7 285.41 L 1184.7 261.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="146">
   <path class="kv110" d="M 1184.73 240.34 L 1184.67 203.25" stroke-width="1" zvalue="143"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="370@0" LinkObjectIDznd="368@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1184.73 240.34 L 1184.67 203.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="145">
   <path class="kv110" d="M 1184.7 179.99 L 1184.7 140.52" stroke-width="1" zvalue="146"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="368@1" LinkObjectIDznd="364@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1184.7 179.99 L 1184.7 140.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="144">
   <path class="kv110" d="M 1220.67 168.99 L 1184.7 168.99" stroke-width="1" zvalue="153"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="360@0" LinkObjectIDznd="145" MaxPinNum="2"/>
   </metadata>
  <path d="M 1220.67 168.99 L 1184.7 168.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="143">
   <path class="kv110" d="M 1220.67 222.14 L 1184.7 222.14" stroke-width="1" zvalue="154"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="361@0" LinkObjectIDznd="146" MaxPinNum="2"/>
   </metadata>
  <path d="M 1220.67 222.14 L 1184.7 222.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="142">
   <path class="kv110" d="M 1220.67 269.75 L 1184.7 269.75" stroke-width="1" zvalue="155"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="362@0" LinkObjectIDznd="147" MaxPinNum="2"/>
   </metadata>
  <path d="M 1220.67 269.75 L 1184.7 269.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="128">
   <path class="v6300" d="M 592.53 902.05 L 592.52 887.82" stroke-width="1" zvalue="199"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="298@0" LinkObjectIDznd="301@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 592.53 902.05 L 592.52 887.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="v6300" d="M 713.73 757.03 L 678.52 757.03" stroke-width="1" zvalue="207"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="313@0" LinkObjectIDznd="180" MaxPinNum="2"/>
   </metadata>
  <path d="M 713.73 757.03 L 678.52 757.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="183">
   <path class="kv110" d="M 1121.54 138.35 L 1121.54 150.5 L 1184.7 150.5" stroke-width="1" zvalue="258"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="356@0" LinkObjectIDznd="145" MaxPinNum="2"/>
   </metadata>
  <path d="M 1121.54 138.35 L 1121.54 150.5 L 1184.7 150.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="53">
   <path class="v6300" d="M 592.7 773.77 L 592.7 800.21" stroke-width="1" zvalue="259"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="253@0" LinkObjectIDznd="308@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 592.7 773.77 L 592.7 800.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="55">
   <path class="v6300" d="M 592.32 821.82 L 592.32 866.21" stroke-width="1" zvalue="261"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="308@1" LinkObjectIDznd="301@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 592.32 821.82 L 592.32 866.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="v6300" d="M 592.32 842 L 678.69 842" stroke-width="1" zvalue="262"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="55" LinkObjectIDznd="178" MaxPinNum="2"/>
   </metadata>
  <path d="M 592.32 842 L 678.69 842" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="139">
   <path class="v6300" d="M 927.53 785.38 L 927.52 743.22" stroke-width="1" zvalue="279"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@0" LinkObjectIDznd="141@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 927.53 785.38 L 927.52 743.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="138">
   <path class="v6300" d="M 927.69 812.12 L 927.72 902.52" stroke-width="1" zvalue="280"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@1" LinkObjectIDznd="185@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 927.69 812.12 L 927.72 902.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="137">
   <path class="v6300" d="M 994.49 903.8 L 994.49 885.82" stroke-width="1" zvalue="281"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="184@0" LinkObjectIDznd="140@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 994.49 903.8 L 994.49 885.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="136">
   <path class="v6300" d="M 995.54 864.21 L 995.56 839.03 L 927.7 839.03" stroke-width="1" zvalue="282"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="140@0" LinkObjectIDznd="138" MaxPinNum="2"/>
   </metadata>
  <path d="M 995.54 864.21 L 995.56 839.03 L 927.7 839.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="135">
   <path class="v6300" d="M 927.52 721.61 L 927.52 706.33" stroke-width="1" zvalue="283"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="141@0" LinkObjectIDznd="255@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 927.52 721.61 L 927.52 706.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="132">
   <path class="v6300" d="M 841.53 902.05 L 841.52 888.82" stroke-width="1" zvalue="287"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="134@0" LinkObjectIDznd="133@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 841.53 902.05 L 841.52 888.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="126">
   <path class="v6300" d="M 962.73 757.03 L 927.52 757.03" stroke-width="1" zvalue="292"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="127@0" LinkObjectIDznd="139" MaxPinNum="2"/>
   </metadata>
  <path d="M 962.73 757.03 L 927.52 757.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="125">
   <path class="v6300" d="M 841.7 773.77 L 841.7 800.21" stroke-width="1" zvalue="293"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="181@0" LinkObjectIDznd="131@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 841.7 773.77 L 841.7 800.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="v6300" d="M 841.32 821.82 L 841.32 867.21" stroke-width="1" zvalue="294"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="131@1" LinkObjectIDznd="133@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 841.32 821.82 L 841.32 867.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="105">
   <path class="v6300" d="M 841.32 842 L 927.7 842" stroke-width="1" zvalue="295"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109" LinkObjectIDznd="138" MaxPinNum="2"/>
   </metadata>
  <path d="M 841.32 842 L 927.7 842" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="212">
   <path class="v6300" d="M 1334.53 791.38 L 1334.52 749.22" stroke-width="1" zvalue="309"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="215@0" LinkObjectIDznd="214@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1334.53 791.38 L 1334.52 749.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="211">
   <path class="v6300" d="M 1334.69 818.12 L 1334.72 908.52" stroke-width="1" zvalue="310"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="215@1" LinkObjectIDznd="218@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1334.69 818.12 L 1334.72 908.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="210">
   <path class="v6300" d="M 1401.49 909.8 L 1401.49 891.82" stroke-width="1" zvalue="311"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="217@0" LinkObjectIDznd="213@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1401.49 909.8 L 1401.49 891.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="209">
   <path class="v6300" d="M 1402.54 870.21 L 1402.56 845.03 L 1334.7 845.03" stroke-width="1" zvalue="312"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="213@0" LinkObjectIDznd="211" MaxPinNum="2"/>
   </metadata>
  <path d="M 1402.54 870.21 L 1402.56 845.03 L 1334.7 845.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="208">
   <path class="v6300" d="M 1334.52 727.61 L 1334.52 709.08" stroke-width="1" zvalue="313"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="214@0" LinkObjectIDznd="179@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1334.52 727.61 L 1334.52 709.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="204">
   <path class="v6300" d="M 1248.53 908.05 L 1248.52 894.82" stroke-width="1" zvalue="317"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="207@0" LinkObjectIDznd="206@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1248.53 908.05 L 1248.52 894.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="201">
   <path class="v6300" d="M 1369.73 763.03 L 1334.52 763.03" stroke-width="1" zvalue="322"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="202@0" LinkObjectIDznd="212" MaxPinNum="2"/>
   </metadata>
  <path d="M 1369.73 763.03 L 1334.52 763.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="200">
   <path class="v6300" d="M 1248.7 779.77 L 1248.7 806.21" stroke-width="1" zvalue="323"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="216@0" LinkObjectIDznd="203@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1248.7 779.77 L 1248.7 806.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="199">
   <path class="v6300" d="M 1248.32 827.82 L 1248.32 873.21" stroke-width="1" zvalue="324"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="203@1" LinkObjectIDznd="206@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1248.32 827.82 L 1248.32 873.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="198">
   <path class="v6300" d="M 1248.32 848 L 1334.7 848" stroke-width="1" zvalue="325"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="199" LinkObjectIDznd="211" MaxPinNum="2"/>
   </metadata>
  <path d="M 1248.32 848 L 1334.7 848" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="227">
   <path class="v6300" d="M 1547.92 831.2 L 1547.92 866.66" stroke-width="1" zvalue="331"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="228@1" LinkObjectIDznd="229@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1547.92 831.2 L 1547.92 866.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="225">
   <path class="v6300" d="M 1549.19 709.08 L 1549.19 734.59" stroke-width="1" zvalue="334"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="179@3" LinkObjectIDznd="226@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1549.19 709.08 L 1549.19 734.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="224">
   <path class="v6300" d="M 1549.19 756.2 L 1549.19 804.46" stroke-width="1" zvalue="335"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="226@1" LinkObjectIDznd="228@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1549.19 756.2 L 1549.19 804.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="222">
   <path class="v6300" d="M 1598.83 792.99 L 1598.83 782 L 1549.19 782" stroke-width="1" zvalue="337"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="223@0" LinkObjectIDznd="224" MaxPinNum="2"/>
   </metadata>
  <path d="M 1598.83 792.99 L 1598.83 782 L 1549.19 782" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="38">
   <path class="v6300" d="M 1130.28 838.77 L 1130.28 825.78 L 1086.03 825.78" stroke-width="1" zvalue="341"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="54@0" LinkObjectIDznd="175" MaxPinNum="2"/>
   </metadata>
  <path d="M 1130.28 838.77 L 1130.28 825.78 L 1086.03 825.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="46">
   <path class="kv110" d="M 1184.08 337.18 L 1184.08 448.07" stroke-width="1" zvalue="350"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="251@2" LinkObjectIDznd="49@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1184.08 337.18 L 1184.08 448.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="kv110" d="M 1184.05 471.9 L 1184.05 519.78" stroke-width="1" zvalue="351"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@1" LinkObjectIDznd="112@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1184.05 471.9 L 1184.05 519.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="kv110" d="M 1224.1 400.06 L 1184.08 400.06" stroke-width="1" zvalue="355"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="48@0" LinkObjectIDznd="46" MaxPinNum="2"/>
   </metadata>
  <path d="M 1224.1 400.06 L 1184.08 400.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="86">
   <path class="kv110" d="M 1226.61 487.61 L 1184.05 487.61" stroke-width="1" zvalue="356"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44@0" LinkObjectIDznd="45" MaxPinNum="2"/>
   </metadata>
  <path d="M 1226.61 487.61 L 1184.05 487.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="v6300" d="M 974.03 641.86 L 973.94 657.4" stroke-width="1" zvalue="361"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@1" LinkObjectIDznd="281@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 974.03 641.86 L 973.94 657.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="87">
   <path class="v6300" d="M 973.88 615.12 L 973.94 600.02" stroke-width="1" zvalue="362"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@0" LinkObjectIDznd="43@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 973.88 615.12 L 973.94 600.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="88">
   <path class="v6300" d="M 974.08 552.19 L 974.08 577.4" stroke-width="1" zvalue="363"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="263@1" LinkObjectIDznd="43@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 974.08 552.19 L 974.08 577.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="111">
   <path class="v6300" d="M 1451.38 657.4 L 1451.38 639.86" stroke-width="1" zvalue="368"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="303@0" LinkObjectIDznd="108@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1451.38 657.4 L 1451.38 639.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="v6300" d="M 1452.88 613.12 L 1452.88 598.02" stroke-width="1" zvalue="369"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@0" LinkObjectIDznd="110@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1452.88 613.12 L 1452.88 598.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="114">
   <path class="v6300" d="M 1452.38 575.4 L 1452.38 552.19" stroke-width="1" zvalue="370"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="110@0" LinkObjectIDznd="310@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1452.38 575.4 L 1452.38 552.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="205">
   <use class="v6300" height="30" transform="rotate(0,1084.54,888.681) scale(1.65589,1.66751) translate(-420.4,-345.73)" width="28" x="1061.359106203922" xlink:href="#EnergyConsumer:站用变DY接地_0" y="863.6686190813465" zvalue="58"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449575190533" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1084.54,888.681) scale(1.65589,1.66751) translate(-420.4,-345.73)" width="28" x="1061.359106203922" y="863.6686190813465"/></g>
  <g id="229">
   <use class="v6300" height="30" transform="rotate(0,1547.54,890.681) scale(1.65589,1.66751) translate(-603.793,-346.531)" width="28" x="1524.359106203922" xlink:href="#EnergyConsumer:站用变DY接地_0" y="865.6686190813464" zvalue="327"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449577025541" ObjectName="#2站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1547.54,890.681) scale(1.65589,1.66751) translate(-603.793,-346.531)" width="28" x="1524.359106203922" y="865.6686190813464"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="265">
   <use class="kv110" height="20" transform="rotate(270,1017.95,399.897) scale(-1.08456,1.08456) translate(-1956.11,-30.3336)" width="10" x="1012.528734781175" xlink:href="#GroundDisconnector:地刀_0" y="389.0510948657603" zvalue="93"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449574862853" ObjectName="#1主变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449574862853"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1017.95,399.897) scale(-1.08456,1.08456) translate(-1956.11,-30.3336)" width="10" x="1012.528734781175" y="389.0510948657603"/></g>
  <g id="259">
   <use class="kv110" height="45" transform="rotate(0,894.848,530.411) scale(0.881034,-1.10129) translate(118.155,-1009.76)" width="45" x="875.0251868969808" xlink:href="#GroundDisconnector:12547_0" y="505.6317632047069" zvalue="101"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449574731782" ObjectName="#1主变110kV侧1010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449574731782"/></metadata>
  <rect fill="white" height="45" opacity="0" stroke="white" transform="rotate(0,894.848,530.411) scale(0.881034,-1.10129) translate(118.155,-1009.76)" width="45" x="875.0251868969808" y="505.6317632047069"/></g>
  <g id="312">
   <use class="kv110" height="20" transform="rotate(270,1495.27,399.897) scale(-1.08456,1.08456) translate(-2873.53,-30.3336)" width="10" x="1489.84410490264" xlink:href="#GroundDisconnector:地刀_0" y="389.0510948657603" zvalue="112"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449574469638" ObjectName="#2主变110kV侧10217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449574469638"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1495.27,399.897) scale(-1.08456,1.08456) translate(-2873.53,-30.3336)" width="10" x="1489.84410490264" y="389.0510948657603"/></g>
  <g id="306">
   <use class="kv110" height="45" transform="rotate(0,1371.16,530.411) scale(0.881034,-1.10129) translate(182.472,-1009.76)" width="45" x="1351.340557018445" xlink:href="#GroundDisconnector:12547_0" y="505.6317632047069" zvalue="120"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449574338566" ObjectName="#2主变110kV侧1020中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449574338566"/></metadata>
  <rect fill="white" height="45" opacity="0" stroke="white" transform="rotate(0,1371.16,530.411) scale(0.881034,-1.10129) translate(182.472,-1009.76)" width="45" x="1351.340557018445" y="505.6317632047069"/></g>
  <g id="362">
   <use class="kv110" height="20" transform="rotate(270,1231.24,269.695) scale(-1.08456,1.08456) translate(-2366.07,-20.182)" width="10" x="1225.820158362823" xlink:href="#GroundDisconnector:地刀_0" y="258.8495315889925" zvalue="147"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449573879814" ObjectName="110kV芒康电站线11117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449573879814"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1231.24,269.695) scale(-1.08456,1.08456) translate(-2366.07,-20.182)" width="10" x="1225.820158362823" y="258.8495315889925"/></g>
  <g id="361">
   <use class="kv110" height="20" transform="rotate(270,1231.24,222.083) scale(-1.08456,1.08456) translate(-2366.07,-16.4698)" width="10" x="1225.820158362823" xlink:href="#GroundDisconnector:地刀_0" y="211.2372911337567" zvalue="149"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449573748742" ObjectName="110kV芒康电站线11160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449573748742"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1231.24,222.083) scale(-1.08456,1.08456) translate(-2366.07,-16.4698)" width="10" x="1225.820158362823" y="211.2372911337567"/></g>
  <g id="360">
   <use class="kv110" height="20" transform="rotate(270,1231.24,168.939) scale(-1.08456,1.08456) translate(-2366.07,-12.3263)" width="10" x="1225.820158466255" xlink:href="#GroundDisconnector:地刀_0" y="158.09379389233" zvalue="151"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449573617669" ObjectName="110kV芒康电站线11167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449573617669"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1231.24,168.939) scale(-1.08456,1.08456) translate(-2366.07,-12.3263)" width="10" x="1225.820158466255" y="158.09379389233"/></g>
  <g id="48">
   <use class="kv110" height="20" transform="rotate(270,1234.93,400) scale(-1.11111,1.11111) translate(-2345.81,-38.8889)" width="10" x="1229.373747546264" xlink:href="#GroundDisconnector:地刀_0" y="388.8888888888889" zvalue="347"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449577353221" ObjectName="110kV母线电压互感器19010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449577353221"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1234.93,400) scale(-1.11111,1.11111) translate(-2345.81,-38.8889)" width="10" x="1229.373747546264" y="388.8888888888889"/></g>
  <g id="44">
   <use class="kv110" height="20" transform="rotate(270,1237.44,487.556) scale(-1.11111,1.11111) translate(-2350.59,-47.6444)" width="10" x="1231.888888888889" xlink:href="#GroundDisconnector:地刀_0" y="476.4444444444444" zvalue="352"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449577222149" ObjectName="110kV母线电压互感器19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449577222149"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1237.44,487.556) scale(-1.11111,1.11111) translate(-2350.59,-47.6444)" width="10" x="1231.888888888889" y="476.4444444444444"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="263">
   <g id="2630">
    <use class="kv110" height="50" transform="rotate(0,974.081,507.599) scale(2.09863,1.80926) translate(-493.45,-206.811)" width="30" x="942.6" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="462.37" zvalue="96"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874422353924" ObjectName="110"/>
    </metadata>
   </g>
   <g id="2631">
    <use class="v6300" height="50" transform="rotate(0,974.081,507.599) scale(2.09863,1.80926) translate(-493.45,-206.811)" width="30" x="942.6" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="462.37" zvalue="96"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874422484996" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399443546116" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399443546116"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,974.081,507.599) scale(2.09863,1.80926) translate(-493.45,-206.811)" width="30" x="942.6" y="462.37"/></g>
  <g id="310">
   <g id="3100">
    <use class="kv110" height="50" transform="rotate(0,1451.4,507.599) scale(2.09863,1.80926) translate(-743.323,-206.811)" width="30" x="1419.92" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="462.37" zvalue="115"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874422222852" ObjectName="110"/>
    </metadata>
   </g>
   <g id="3101">
    <use class="v6300" height="50" transform="rotate(0,1451.4,507.599) scale(2.09863,1.80926) translate(-743.323,-206.811)" width="30" x="1419.92" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="462.37" zvalue="115"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874422288388" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399443480580" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399443480580"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1451.4,507.599) scale(2.09863,1.80926) translate(-743.323,-206.811)" width="30" x="1419.92" y="462.37"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="117">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="117" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1183.7,29.7454) scale(1,1) translate(-2.52843e-13,0)" writing-mode="lr" x="1183.23" xml:space="preserve" y="34.44" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124241080324" ObjectName="P"/>
   </metadata>
  </g>
  <g id="118">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="118" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1183.7,44.6204) scale(1,1) translate(0,0)" writing-mode="lr" x="1183.23" xml:space="preserve" y="49.31" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124241145860" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="119">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="119" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1183.7,60.7454) scale(1,1) translate(0,0)" writing-mode="lr" x="1183.23" xml:space="preserve" y="65.44" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124241211396" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="123">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="123" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1382.9,394.809) scale(1,1) translate(0,4.23125e-14)" writing-mode="lr" x="1382.34" xml:space="preserve" y="399.53" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124242653188" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="129">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="129" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,902.331,390.559) scale(1,1) translate(0,4.19795e-14)" writing-mode="lr" x="901.78" xml:space="preserve" y="395.26" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124245209092" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="130">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="130" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1382.9,416.184) scale(1,1) translate(0,4.49354e-14)" writing-mode="lr" x="1382.34" xml:space="preserve" y="420.87" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124242718724" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="150">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="150" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,902.331,411.184) scale(1,1) translate(0,-1.77521e-13)" writing-mode="lr" x="901.78" xml:space="preserve" y="415.87" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124245274628" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="167">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="167" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1385.15,597.665) scale(1,1) translate(0,-1.29858e-13)" writing-mode="lr" x="1384.59" xml:space="preserve" y="602.37" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124242784260" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="168">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="168" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,892.581,597.54) scale(1,1) translate(0,-1.29858e-13)" writing-mode="lr" x="892.03" xml:space="preserve" y="602.24" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124255629316" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="169">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="169" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1385.15,624.04) scale(1,1) translate(0,-1.35659e-13)" writing-mode="lr" x="1384.59" xml:space="preserve" y="628.75" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124242849796" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="232">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="232" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,892.581,622.915) scale(1,1) translate(0,-1.3541e-13)" writing-mode="lr" x="892.03" xml:space="preserve" y="627.62" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124255694852" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="233">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="233" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1382.9,440.684) scale(1,1) translate(0,4.75583e-14)" writing-mode="lr" x="1382.34" xml:space="preserve" y="445.38" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124242915332" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="234">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="234" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,902.331,434.309) scale(1,1) translate(0,4.67812e-14)" writing-mode="lr" x="901.78" xml:space="preserve" y="439.01" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124245471236" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="235">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="235" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1385.15,648.54) scale(1,1) translate(0,2.8292e-13)" writing-mode="lr" x="1384.59" xml:space="preserve" y="653.22" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124243243012" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="236">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="236" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,892.581,648.165) scale(1,1) translate(0,2.81921e-13)" writing-mode="lr" x="892.03" xml:space="preserve" y="652.88" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124255760388" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="237">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="237" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,675.221,996.316) scale(1,1) translate(0,1.09288e-13)" writing-mode="lr" x="674.67" xml:space="preserve" y="1001.01" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124250976260" ObjectName="P"/>
   </metadata>
  </g>
  <g id="238">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="238" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,925.221,989.191) scale(1,1) translate(0,2.17355e-13)" writing-mode="lr" x="924.67" xml:space="preserve" y="993.85" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124252155908" ObjectName="P"/>
   </metadata>
  </g>
  <g id="239">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="239" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1334.72,992.191) scale(1,1) translate(0,1.08816e-13)" writing-mode="lr" x="1334.17" xml:space="preserve" y="996.88" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124253335556" ObjectName="P"/>
   </metadata>
  </g>
  <g id="240">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="240" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,675.221,1012.19) scale(1,1) translate(0,1.11065e-13)" writing-mode="lr" x="674.67" xml:space="preserve" y="1016.88" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124251041796" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="241">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="241" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,925.221,1010.07) scale(1,1) translate(0,2.21935e-13)" writing-mode="lr" x="924.67" xml:space="preserve" y="1014.73" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124252221444" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="243">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="243" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1334.72,1013.44) scale(1,1) translate(0,1.11148e-13)" writing-mode="lr" x="1334.17" xml:space="preserve" y="1018.14" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124253401092" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="244">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="244" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,675.221,1032.07) scale(1,1) translate(0,1.13396e-13)" writing-mode="lr" x="674.67" xml:space="preserve" y="1036.74" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124251107332" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="245">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="245" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,925.221,1030.94) scale(1,1) translate(0,1.13257e-13)" writing-mode="lr" x="924.67" xml:space="preserve" y="1035.61" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124252286982" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="247">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="247" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1334.72,1034.32) scale(1,1) translate(0,2.26959e-12)" writing-mode="lr" x="1334.17" xml:space="preserve" y="1039.01" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124253466628" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="280">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="280" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,334.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="339.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124248158212" ObjectName="F"/>
   </metadata>
  </g>
  <g id="279">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="279" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,262.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="267.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124260741124" ObjectName="F"/>
   </metadata>
  </g>
  <g id="278">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="278" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,263.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="268.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124260806660" ObjectName="F"/>
   </metadata>
  </g>
  <g id="277">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="277" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,337.222,335.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="340.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124250845188" ObjectName="F"/>
   </metadata>
  </g>
  <g id="270">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="270" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,287.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="292.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124260610052" ObjectName="F"/>
   </metadata>
  </g>
  <g id="269">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="269" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,288.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="293.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124260675588" ObjectName="F"/>
   </metadata>
  </g>
  <g id="264">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="264" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,379.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="384.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124263165956" ObjectName="F"/>
   </metadata>
  </g>
  <g id="256">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="256" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,159.611,311.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="316.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127184433157" ObjectName="装机利用率"/>
   </metadata>
  </g>
  <g id="252">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="252" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,339.611,310.167) scale(1,1) translate(0,0)" writing-mode="lr" x="339.77" xml:space="preserve" y="315.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127184367621" ObjectName="厂用电率"/>
   </metadata>
  </g>
  <g id="250">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="250" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,155.972,353.917) scale(1,1) translate(0,0)" writing-mode="lr" x="156.13" xml:space="preserve" y="358.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124248682500" ObjectName="F"/>
   </metadata>
  </g>
  <g id="2">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="2" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,678.483,188.682) scale(1,1) translate(0,0)" writing-mode="lr" x="678.01" xml:space="preserve" y="193.46" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124247764996" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="3">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="3" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1628.15,460.578) scale(1,1) translate(0,0)" writing-mode="lr" x="1627.68" xml:space="preserve" y="465.36" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124248289284" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="4">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="4" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,609,587.831) scale(1,1) translate(0,0)" writing-mode="lr" x="608.53" xml:space="preserve" y="592.61" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124250451972" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="5">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="5" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,678.483,217.682) scale(1,1) translate(0,0)" writing-mode="lr" x="678.01" xml:space="preserve" y="222.46" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124247830532" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="6">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="6" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1628.15,489.578) scale(1,1) translate(0,0)" writing-mode="lr" x="1627.68" xml:space="preserve" y="494.36" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124248354820" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="7" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,609,616.831) scale(1,1) translate(0,0)" writing-mode="lr" x="608.53" xml:space="preserve" y="621.61" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124250517508" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="8" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,678.483,246.682) scale(1,1) translate(0,0)" writing-mode="lr" x="678.01" xml:space="preserve" y="251.46" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124247896068" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="9">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="9" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1628.15,518.578) scale(1,1) translate(0,0)" writing-mode="lr" x="1627.68" xml:space="preserve" y="523.36" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124248420356" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="10">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="10" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,609,645.831) scale(1,1) translate(0,-2.79035e-13)" writing-mode="lr" x="608.53" xml:space="preserve" y="650.61" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124250583044" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="11">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="11" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,683.483,277.182) scale(1,1) translate(0,0)" writing-mode="lr" x="683.01" xml:space="preserve" y="281.96" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124248027140" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="12">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="12" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1206.15,724.078) scale(1,1) translate(-2.57826e-13,0)" writing-mode="lr" x="1205.68" xml:space="preserve" y="728.86" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124248551428" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="13">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="13" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,526,723.331) scale(1,1) translate(0,0)" writing-mode="lr" x="525.53" xml:space="preserve" y="728.11" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124250714116" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="275">
   <use height="30" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="420"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374884995076" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" y="178.37"/></g>
  <g id="274">
   <use height="30" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="421"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562950076563460" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" y="178.37"/></g>
 </g>
</svg>