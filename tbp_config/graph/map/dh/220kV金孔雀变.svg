<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="ACLineSegment:220kV线路_0" viewBox="0,0,35,40">
   <use terminal-index="0" type="0" x="14.20833333333333" xlink:href="#terminal" y="39.00547521122963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.5" x2="30.5" y1="30.75" y2="28.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.5" x2="27.5" y1="25.75" y2="30.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="21.50545055364641" x2="21.50545055364641" y1="13.04138864447597" y2="19.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.5" x2="24.5" y1="23.75" y2="25.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="5.5" y1="37" y2="37"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="18.91094883543335" x2="24.38823024054981" y1="12.92541949757221" y2="12.92541949757221"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.5" x2="18.5" y1="23.75" y2="25.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.5" x2="30.5" y1="25.75" y2="27.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="6.5" y1="36" y2="36"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="7.5" y1="35" y2="35"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.04,24.92) scale(1,1) translate(0,0)" width="6.08" x="2" y="17.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="32" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="4" y1="25.75" y2="19.75"/>
   <path d="M 14 13 L 5 13 L 5 25 L 5 25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="6" y1="25.75" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.5" x2="21.5" y1="20.75" y2="23.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.46058610156545" x2="14.15436235204273" y1="12.92541949757221" y2="12.92541949757221"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.16022336769755" x2="14.16022336769755" y1="38.83333333333334" y2="0.8333333333333321"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.46383161512025" x2="31.41666666666666" y1="12.99758812251703" y2="12.99758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.35940244368076" x2="24.35940244368076" y1="12.99758812251703" y2="12.99758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.36598892707138" x2="31.36598892707138" y1="9.749999999999996" y2="16.24517624503405"/>
   <ellipse cx="21.51" cy="23.82" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="28.55" cy="28.08" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="21.76" cy="32.08" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="24.35940244368077" x2="24.35940244368077" y1="10.48325293741726" y2="15.58409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.51910080183274" x2="32.51910080183274" y1="11.01295093653441" y2="15.34306843322376"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="33.38393470790376" x2="33.38393470790376" y1="11.91505874834466" y2="13.89969593432727"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.57589728904158" x2="16.57589728904158" y1="12.99758812251703" y2="12.99758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="26.52148720885832" x2="26.52148720885832" y1="10.48325293741726" y2="15.5840919325617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="16.49256395570825" x2="16.49256395570825" y1="10.33333333333333" y2="15.43417232847779"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="18.73798205421914" x2="18.73798205421914" y1="10.48325293741726" y2="15.58409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.58333333333333" x2="18.58333333333333" y1="32.5" y2="34.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.58333333333334" x2="24.58333333333334" y1="32.5" y2="34.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.58333333333333" x2="21.58333333333333" y1="29.5" y2="32.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
  <symbol id="State:间隔模板_0" viewBox="0,0,80,30">
   <rect Plane="0" fill="none" fill-opacity="0" height="28.8" stroke="rgb(85,170,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,15) scale(1,1) translate(0,0)" width="79.2" x="0.4" y="0.6"/>
  </symbol>
  <symbol id="State:间隔模板_1" viewBox="0,0,80,30">
   <rect Plane="0" fill="none" fill-opacity="0" height="28.8" stroke="rgb(185,185,185)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,15) scale(1,1) translate(0,0)" width="79.2" x="0.4" y="0.6"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="220kV金孔雀变" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="82.56999999999999" id="1" preserveAspectRatio="xMidYMid slice" width="249.69" x="42.31" xlink:href="logo.png" y="34.43"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,167.153,75.7136) scale(1,1) translate(-9.39397e-15,0)" writing-mode="lr" x="167.15" xml:space="preserve" y="79.20999999999999" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,196,75.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="196" xml:space="preserve" y="84.69" zvalue="3">220kV金孔雀变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="805" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,71.625,419.25) scale(1,1) translate(0,0)" width="72.88" x="35.19" y="407.25" zvalue="1057"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,71.625,419.25) scale(1,1) translate(0,0)" writing-mode="lr" x="71.63" xml:space="preserve" y="423.75" zvalue="1057">AVC功能</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="362" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,71.625,380.75) scale(1,1) translate(0,0)" width="72.88" x="35.19" y="368.75" zvalue="1113"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,71.625,380.75) scale(1,1) translate(0,0)" writing-mode="lr" x="71.63" xml:space="preserve" y="385.25" zvalue="1113">信号一览</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="36" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,180.5,419.25) scale(1,1) translate(0,0)" width="72.88" x="144.06" y="407.25" zvalue="1224"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,180.5,419.25) scale(1,1) translate(0,0)" writing-mode="lr" x="180.5" xml:space="preserve" y="423.75" zvalue="1224">光字巡检</text>
  <line fill="none" id="39" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.00000000000045" x2="383" y1="152.8704926140824" y2="152.8704926140824" zvalue="5"/>
  <line fill="none" id="38" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="384" x2="384" y1="11" y2="1041" zvalue="6"/>
  <line fill="none" id="37" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.25000000000045" x2="384.25" y1="611.8704926140824" y2="611.8704926140824" zvalue="7"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="13.25" x2="103.25" y1="925" y2="925"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="13.25" x2="103.25" y1="977.1632999999999" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="13.25" x2="13.25" y1="925" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103.25" x2="103.25" y1="925" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103.25" x2="373.25" y1="925" y2="925"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103.25" x2="373.25" y1="977.1632999999999" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103.25" x2="103.25" y1="925" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="373.25" x2="373.25" y1="925" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="13.25" x2="103.25" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="13.25" x2="103.25" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="13.25" x2="13.25" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103.25" x2="103.25" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103.25" x2="193.25" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103.25" x2="193.25" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103.25" x2="103.25" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.25" x2="193.25" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.2500000000001" x2="283.2500000000001" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.2500000000001" x2="283.2500000000001" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.2500000000001" x2="193.2500000000001" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="283.2500000000001" x2="283.2500000000001" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="283.25" x2="373.25" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="283.25" x2="373.25" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="283.25" x2="283.25" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="373.25" x2="373.25" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="13.25" x2="103.25" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="13.25" x2="103.25" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="13.25" x2="13.25" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103.25" x2="103.25" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103.25" x2="193.25" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103.25" x2="193.25" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103.25" x2="103.25" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.25" x2="193.25" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.2500000000001" x2="283.2500000000001" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.2500000000001" x2="283.2500000000001" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.2500000000001" x2="193.2500000000001" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="283.2500000000001" x2="283.2500000000001" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="283.25" x2="373.25" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="283.25" x2="373.25" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="283.25" x2="283.25" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="373.25" x2="373.25" y1="1005.0816" y2="1033"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="33" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,62.625,953.25) scale(1,1) translate(0,0)" writing-mode="lr" x="30.25" xml:space="preserve" y="957.75" zvalue="11">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,238,1020) scale(1,1) translate(0,0)" writing-mode="lr" x="238" xml:space="preserve" y="1024.5" zvalue="15">更新日期</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17" x2="198" y1="162" y2="162"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17" x2="198" y1="188" y2="188"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17" x2="17" y1="162" y2="188"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198" x2="198" y1="162" y2="188"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198" x2="379" y1="162" y2="162"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198" x2="379" y1="188" y2="188"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198" x2="198" y1="162" y2="188"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="379" x2="379" y1="162" y2="188"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17" x2="198" y1="188" y2="188"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17" x2="198" y1="212.25" y2="212.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17" x2="17" y1="188" y2="212.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198" x2="198" y1="188" y2="212.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198" x2="379" y1="188" y2="188"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198" x2="379" y1="212.25" y2="212.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198" x2="198" y1="188" y2="212.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="379" x2="379" y1="188" y2="212.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17" x2="198" y1="212.25" y2="212.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17" x2="198" y1="236.5" y2="236.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17" x2="17" y1="212.25" y2="236.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198" x2="198" y1="212.25" y2="236.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198" x2="379" y1="212.25" y2="212.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198" x2="379" y1="236.5" y2="236.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198" x2="198" y1="212.25" y2="236.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="379" x2="379" y1="212.25" y2="236.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17" x2="198" y1="236.5" y2="236.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17" x2="198" y1="259.25" y2="259.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17" x2="17" y1="236.5" y2="259.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198" x2="198" y1="236.5" y2="259.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198" x2="379" y1="236.5" y2="236.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198" x2="379" y1="259.25" y2="259.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198" x2="198" y1="236.5" y2="259.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="379" x2="379" y1="236.5" y2="259.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17" x2="198" y1="259.25" y2="259.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17" x2="198" y1="282" y2="282"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17" x2="17" y1="259.25" y2="282"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198" x2="198" y1="259.25" y2="282"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198" x2="379" y1="259.25" y2="259.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198" x2="379" y1="282" y2="282"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198" x2="198" y1="259.25" y2="282"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="379" x2="379" y1="259.25" y2="282"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17" x2="198" y1="282" y2="282"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17" x2="198" y1="304.75" y2="304.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17" x2="17" y1="282" y2="304.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198" x2="198" y1="282" y2="304.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198" x2="379" y1="282" y2="282"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198" x2="379" y1="304.75" y2="304.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198" x2="198" y1="282" y2="304.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="379" x2="379" y1="282" y2="304.75"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55,176) scale(1,1) translate(0,0)" writing-mode="lr" x="55" xml:space="preserve" y="180.5" zvalue="28">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235,176) scale(1,1) translate(0,0)" writing-mode="lr" x="235" xml:space="preserve" y="180.5" zvalue="29">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57.6875,201.25) scale(1,1) translate(0,0)" writing-mode="lr" x="57.69" xml:space="preserve" y="205.75" zvalue="30">#1主变高频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,240.75,201) scale(1,1) translate(0,0)" writing-mode="lr" x="240.75" xml:space="preserve" y="205.5" zvalue="31">#2主变高频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,70.1875,272) scale(1,1) translate(0,0)" writing-mode="lr" x="70.19" xml:space="preserve" y="276.5" zvalue="32">220kV#1主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,253.75,271.5) scale(1,1) translate(0,0)" writing-mode="lr" x="253.75" xml:space="preserve" y="276" zvalue="33">220kV#2主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,72.1875,295) scale(1,1) translate(0,0)" writing-mode="lr" x="72.19" xml:space="preserve" y="299.5" zvalue="34">220kV#1主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,252.5,294.5) scale(1,1) translate(0,0)" writing-mode="lr" x="252.5" xml:space="preserve" y="299" zvalue="35">220kV#2主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,65.6875,226.25) scale(1,1) translate(0,0)" writing-mode="lr" x="65.69" xml:space="preserve" y="230.75" zvalue="36">110kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,248.75,226) scale(1,1) translate(0,0)" writing-mode="lr" x="248.75" xml:space="preserve" y="230.5" zvalue="37">110kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,58.9375,248.75) scale(1,1) translate(0,0)" writing-mode="lr" x="58.94" xml:space="preserve" y="253.25" zvalue="38">35kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,240.75,249.75) scale(1,1) translate(0,0)" writing-mode="lr" x="240.75" xml:space="preserve" y="254.25" zvalue="39">35kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,245.399,339.341) scale(1,1) translate(0,0)" writing-mode="lr" x="245.4" xml:space="preserve" y="343.84" zvalue="40">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,314.399,339.341) scale(1,1) translate(0,0)" writing-mode="lr" x="314.4" xml:space="preserve" y="343.84" zvalue="41">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.25,633.25) scale(1,1) translate(0,0)" writing-mode="lr" x="83.25" xml:space="preserve" y="637.75" zvalue="43">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,772.023,575.192) scale(1,1) translate(0,0)" writing-mode="lr" x="772.02" xml:space="preserve" y="579.6900000000001" zvalue="46">220kVⅡ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,761.795,685.529) scale(1,1) translate(0,0)" writing-mode="lr" x="761.8" xml:space="preserve" y="690.03" zvalue="47">220kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="827" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1061.28,508.131) scale(1,1) translate(0,0)" writing-mode="lr" x="1061.28" xml:space="preserve" y="512.63" zvalue="269">241</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="285" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1118.72,581.649) scale(1,1) translate(0,0)" writing-mode="lr" x="1118.72" xml:space="preserve" y="586.15" zvalue="271">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="283" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1030.85,565.607) scale(1,1) translate(0,0)" writing-mode="lr" x="1030.85" xml:space="preserve" y="570.11" zvalue="273">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="464" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1059.25,452.083) scale(1,1) translate(0,0)" writing-mode="lr" x="1059.25" xml:space="preserve" y="456.58" zvalue="277">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="828" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1082.32,312.25) scale(1,1) translate(-2.27334e-13,0)" writing-mode="lr" x="1082.32" xml:space="preserve" y="316.75" zvalue="281">220kV金孔雀Ⅰ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="378" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1133.62,527.484) scale(1,1) translate(0,0)" writing-mode="lr" x="1133.62" xml:space="preserve" y="531.98" zvalue="283">27</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="32.07174603174599" x2="66.67504603174598" y1="443.9999777777778" y2="443.9999777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="32.07174603174599" x2="66.67504603174598" y1="481.5369777777778" y2="481.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="32.07174603174599" x2="32.07174603174599" y1="443.9999777777778" y2="481.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.67504603174598" x2="66.67504603174598" y1="443.9999777777778" y2="481.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.67474603174605" x2="115.1302460317461" y1="443.9999777777778" y2="443.9999777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.67474603174605" x2="115.1302460317461" y1="481.5369777777778" y2="481.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.67474603174605" x2="66.67474603174605" y1="443.9999777777778" y2="481.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.1302460317461" x2="115.1302460317461" y1="443.9999777777778" y2="481.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.130046031746" x2="161.737046031746" y1="443.9999777777778" y2="443.9999777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.130046031746" x2="161.737046031746" y1="481.5369777777778" y2="481.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.130046031746" x2="115.130046031746" y1="443.9999777777778" y2="481.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.737046031746" x2="161.737046031746" y1="443.9999777777778" y2="481.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.737046031746" x2="210.0085460317461" y1="443.9999777777778" y2="443.9999777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.737046031746" x2="210.0085460317461" y1="481.5369777777778" y2="481.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.737046031746" x2="161.737046031746" y1="443.9999777777778" y2="481.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.0085460317461" x2="210.0085460317461" y1="443.9999777777778" y2="481.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.0084460317461" x2="262.336146031746" y1="443.9999777777778" y2="443.9999777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.0084460317461" x2="262.336146031746" y1="481.5369777777778" y2="481.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.0084460317461" x2="210.0084460317461" y1="443.9999777777778" y2="481.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="262.336146031746" x2="262.336146031746" y1="443.9999777777778" y2="481.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="262.336246031746" x2="314.663946031746" y1="443.9999777777778" y2="443.9999777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="262.336246031746" x2="314.663946031746" y1="481.5369777777778" y2="481.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="262.336246031746" x2="262.336246031746" y1="443.9999777777778" y2="481.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="314.663946031746" x2="314.663946031746" y1="443.9999777777778" y2="481.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="314.6647460317461" x2="366.992446031746" y1="443.9999777777778" y2="443.9999777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="314.6647460317461" x2="366.992446031746" y1="481.5369777777778" y2="481.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="314.6647460317461" x2="314.6647460317461" y1="443.9999777777778" y2="481.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.992446031746" x2="366.992446031746" y1="443.9999777777778" y2="481.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="32.07174603174599" x2="66.67504603174598" y1="481.5369777777778" y2="481.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="32.07174603174599" x2="66.67504603174598" y1="505.2406777777778" y2="505.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="32.07174603174599" x2="32.07174603174599" y1="481.5369777777778" y2="505.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.67504603174598" x2="66.67504603174598" y1="481.5369777777778" y2="505.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.67474603174605" x2="115.1302460317461" y1="481.5369777777778" y2="481.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.67474603174605" x2="115.1302460317461" y1="505.2406777777778" y2="505.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.67474603174605" x2="66.67474603174605" y1="481.5369777777778" y2="505.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.1302460317461" x2="115.1302460317461" y1="481.5369777777778" y2="505.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.130046031746" x2="161.737046031746" y1="481.5369777777778" y2="481.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.130046031746" x2="161.737046031746" y1="505.2406777777778" y2="505.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.130046031746" x2="115.130046031746" y1="481.5369777777778" y2="505.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.737046031746" x2="161.737046031746" y1="481.5369777777778" y2="505.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.737046031746" x2="210.0085460317461" y1="481.5369777777778" y2="481.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.737046031746" x2="210.0085460317461" y1="505.2406777777778" y2="505.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.737046031746" x2="161.737046031746" y1="481.5369777777778" y2="505.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.0085460317461" x2="210.0085460317461" y1="481.5369777777778" y2="505.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.0084460317461" x2="262.336146031746" y1="481.5369777777778" y2="481.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.0084460317461" x2="262.336146031746" y1="505.2406777777778" y2="505.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.0084460317461" x2="210.0084460317461" y1="481.5369777777778" y2="505.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="262.336146031746" x2="262.336146031746" y1="481.5369777777778" y2="505.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="262.336246031746" x2="314.663946031746" y1="481.5369777777778" y2="481.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="262.336246031746" x2="314.663946031746" y1="505.2406777777778" y2="505.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="262.336246031746" x2="262.336246031746" y1="481.5369777777778" y2="505.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="314.663946031746" x2="314.663946031746" y1="481.5369777777778" y2="505.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="314.6647460317461" x2="366.992446031746" y1="481.5369777777778" y2="481.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="314.6647460317461" x2="366.992446031746" y1="505.2406777777778" y2="505.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="314.6647460317461" x2="314.6647460317461" y1="481.5369777777778" y2="505.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.992446031746" x2="366.992446031746" y1="481.5369777777778" y2="505.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="32.07174603174599" x2="66.67504603174598" y1="505.2406777777778" y2="505.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="32.07174603174599" x2="66.67504603174598" y1="528.9443777777777" y2="528.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="32.07174603174599" x2="32.07174603174599" y1="505.2406777777778" y2="528.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.67504603174598" x2="66.67504603174598" y1="505.2406777777778" y2="528.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.67474603174605" x2="115.1302460317461" y1="505.2406777777778" y2="505.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.67474603174605" x2="115.1302460317461" y1="528.9443777777777" y2="528.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.67474603174605" x2="66.67474603174605" y1="505.2406777777778" y2="528.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.1302460317461" x2="115.1302460317461" y1="505.2406777777778" y2="528.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.130046031746" x2="161.737046031746" y1="505.2406777777778" y2="505.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.130046031746" x2="161.737046031746" y1="528.9443777777777" y2="528.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.130046031746" x2="115.130046031746" y1="505.2406777777778" y2="528.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.737046031746" x2="161.737046031746" y1="505.2406777777778" y2="528.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.737046031746" x2="210.0085460317461" y1="505.2406777777778" y2="505.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.737046031746" x2="210.0085460317461" y1="528.9443777777777" y2="528.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.737046031746" x2="161.737046031746" y1="505.2406777777778" y2="528.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.0085460317461" x2="210.0085460317461" y1="505.2406777777778" y2="528.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.0084460317461" x2="262.336146031746" y1="505.2406777777778" y2="505.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.0084460317461" x2="262.336146031746" y1="528.9443777777777" y2="528.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.0084460317461" x2="210.0084460317461" y1="505.2406777777778" y2="528.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="262.336146031746" x2="262.336146031746" y1="505.2406777777778" y2="528.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="262.336246031746" x2="314.663946031746" y1="505.2406777777778" y2="505.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="262.336246031746" x2="314.663946031746" y1="528.9443777777777" y2="528.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="262.336246031746" x2="262.336246031746" y1="505.2406777777778" y2="528.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="314.663946031746" x2="314.663946031746" y1="505.2406777777778" y2="528.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="314.6647460317461" x2="366.992446031746" y1="505.2406777777778" y2="505.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="314.6647460317461" x2="366.992446031746" y1="528.9443777777777" y2="528.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="314.6647460317461" x2="314.6647460317461" y1="505.2406777777778" y2="528.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.992446031746" x2="366.992446031746" y1="505.2406777777778" y2="528.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="32.07174603174599" x2="66.67504603174598" y1="528.9444377777778" y2="528.9444377777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="32.07174603174599" x2="66.67504603174598" y1="552.6481377777777" y2="552.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="32.07174603174599" x2="32.07174603174599" y1="528.9444377777778" y2="552.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.67504603174598" x2="66.67504603174598" y1="528.9444377777778" y2="552.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.67474603174605" x2="115.1302460317461" y1="528.9444377777778" y2="528.9444377777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.67474603174605" x2="115.1302460317461" y1="552.6481377777777" y2="552.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.67474603174605" x2="66.67474603174605" y1="528.9444377777778" y2="552.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.1302460317461" x2="115.1302460317461" y1="528.9444377777778" y2="552.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.130046031746" x2="161.737046031746" y1="528.9444377777778" y2="528.9444377777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.130046031746" x2="161.737046031746" y1="552.6481377777777" y2="552.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.130046031746" x2="115.130046031746" y1="528.9444377777778" y2="552.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.737046031746" x2="161.737046031746" y1="528.9444377777778" y2="552.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.737046031746" x2="210.0085460317461" y1="528.9444377777778" y2="528.9444377777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.737046031746" x2="210.0085460317461" y1="552.6481377777777" y2="552.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.737046031746" x2="161.737046031746" y1="528.9444377777778" y2="552.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.0085460317461" x2="210.0085460317461" y1="528.9444377777778" y2="552.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.0084460317461" x2="262.336146031746" y1="528.9444377777778" y2="528.9444377777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.0084460317461" x2="262.336146031746" y1="552.6481377777777" y2="552.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.0084460317461" x2="210.0084460317461" y1="528.9444377777778" y2="552.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="262.336146031746" x2="262.336146031746" y1="528.9444377777778" y2="552.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="262.336246031746" x2="314.663946031746" y1="528.9444377777778" y2="528.9444377777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="262.336246031746" x2="314.663946031746" y1="552.6481377777777" y2="552.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="262.336246031746" x2="262.336246031746" y1="528.9444377777778" y2="552.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="314.663946031746" x2="314.663946031746" y1="528.9444377777778" y2="552.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="314.6647460317461" x2="366.992446031746" y1="528.9444377777778" y2="528.9444377777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="314.6647460317461" x2="366.992446031746" y1="552.6481377777777" y2="552.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="314.6647460317461" x2="314.6647460317461" y1="528.9444377777778" y2="552.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.992446031746" x2="366.992446031746" y1="528.9444377777778" y2="552.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="32.07174603174599" x2="66.67504603174598" y1="552.6480777777778" y2="552.6480777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="32.07174603174599" x2="66.67504603174598" y1="576.3517777777778" y2="576.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="32.07174603174599" x2="32.07174603174599" y1="552.6480777777778" y2="576.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.67504603174598" x2="66.67504603174598" y1="552.6480777777778" y2="576.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.67474603174605" x2="115.1302460317461" y1="552.6480777777778" y2="552.6480777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.67474603174605" x2="115.1302460317461" y1="576.3517777777778" y2="576.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.67474603174605" x2="66.67474603174605" y1="552.6480777777778" y2="576.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.1302460317461" x2="115.1302460317461" y1="552.6480777777778" y2="576.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.130046031746" x2="161.737046031746" y1="552.6480777777778" y2="552.6480777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.130046031746" x2="161.737046031746" y1="576.3517777777778" y2="576.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.130046031746" x2="115.130046031746" y1="552.6480777777778" y2="576.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.737046031746" x2="161.737046031746" y1="552.6480777777778" y2="576.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.737046031746" x2="210.0085460317461" y1="552.6480777777778" y2="552.6480777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.737046031746" x2="210.0085460317461" y1="576.3517777777778" y2="576.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.737046031746" x2="161.737046031746" y1="552.6480777777778" y2="576.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.0085460317461" x2="210.0085460317461" y1="552.6480777777778" y2="576.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.0084460317461" x2="262.336146031746" y1="552.6480777777778" y2="552.6480777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.0084460317461" x2="262.336146031746" y1="576.3517777777778" y2="576.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.0084460317461" x2="210.0084460317461" y1="552.6480777777778" y2="576.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="262.336146031746" x2="262.336146031746" y1="552.6480777777778" y2="576.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="262.336246031746" x2="314.663946031746" y1="552.6480777777778" y2="552.6480777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="262.336246031746" x2="314.663946031746" y1="576.3517777777778" y2="576.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="262.336246031746" x2="262.336246031746" y1="552.6480777777778" y2="576.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="314.663946031746" x2="314.663946031746" y1="552.6480777777778" y2="576.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="314.6647460317461" x2="366.992446031746" y1="552.6480777777778" y2="552.6480777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="314.6647460317461" x2="366.992446031746" y1="576.3517777777778" y2="576.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="314.6647460317461" x2="314.6647460317461" y1="552.6480777777778" y2="576.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.992446031746" x2="366.992446031746" y1="552.6480777777778" y2="576.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="32.07174603174599" x2="66.67504603174598" y1="576.3518777777778" y2="576.3518777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="32.07174603174599" x2="66.67504603174598" y1="600.0555777777778" y2="600.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="32.07174603174599" x2="32.07174603174599" y1="576.3518777777778" y2="600.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.67504603174598" x2="66.67504603174598" y1="576.3518777777778" y2="600.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.67474603174605" x2="115.1302460317461" y1="576.3518777777778" y2="576.3518777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.67474603174605" x2="115.1302460317461" y1="600.0555777777778" y2="600.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.67474603174605" x2="66.67474603174605" y1="576.3518777777778" y2="600.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.1302460317461" x2="115.1302460317461" y1="576.3518777777778" y2="600.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.130046031746" x2="161.737046031746" y1="576.3518777777778" y2="576.3518777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.130046031746" x2="161.737046031746" y1="600.0555777777778" y2="600.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.130046031746" x2="115.130046031746" y1="576.3518777777778" y2="600.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.737046031746" x2="161.737046031746" y1="576.3518777777778" y2="600.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.737046031746" x2="210.0085460317461" y1="576.3518777777778" y2="576.3518777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.737046031746" x2="210.0085460317461" y1="600.0555777777778" y2="600.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.737046031746" x2="161.737046031746" y1="576.3518777777778" y2="600.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.0085460317461" x2="210.0085460317461" y1="576.3518777777778" y2="600.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.0084460317461" x2="262.336146031746" y1="576.3518777777778" y2="576.3518777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.0084460317461" x2="262.336146031746" y1="600.0555777777778" y2="600.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.0084460317461" x2="210.0084460317461" y1="576.3518777777778" y2="600.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="262.336146031746" x2="262.336146031746" y1="576.3518777777778" y2="600.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="262.336246031746" x2="314.663946031746" y1="576.3518777777778" y2="576.3518777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="262.336246031746" x2="314.663946031746" y1="600.0555777777778" y2="600.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="262.336246031746" x2="262.336246031746" y1="576.3518777777778" y2="600.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="314.663946031746" x2="314.663946031746" y1="576.3518777777778" y2="600.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="314.6647460317461" x2="366.992446031746" y1="576.3518777777778" y2="576.3518777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="314.6647460317461" x2="366.992446031746" y1="600.0555777777778" y2="600.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="314.6647460317461" x2="314.6647460317461" y1="576.3518777777778" y2="600.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.992446031746" x2="366.992446031746" y1="576.3518777777778" y2="600.0555777777778"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="650" stroke="rgb(255,255,255)" text-anchor="middle" x="186.8359375" xml:space="preserve" y="459.0779656304019" zvalue="781">110kVⅠ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="650" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="186.8359375" xml:space="preserve" y="476.0779656304019" zvalue="781">母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="649" stroke="rgb(255,255,255)" text-anchor="middle" x="236.7421875" xml:space="preserve" y="459.0779656304019" zvalue="782">110kVⅡ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="649" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="236.7421875" xml:space="preserve" y="476.0779656304019" zvalue="782">母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="648" stroke="rgb(255,255,255)" text-anchor="middle" x="91" xml:space="preserve" y="459.0779656304019" zvalue="783">#1主变高</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="648" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="91" xml:space="preserve" y="476.0779656304019" zvalue="783">压侧</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="647" stroke="rgb(255,255,255)" text-anchor="middle" x="138.5" xml:space="preserve" y="459.0779656304019" zvalue="784">#2主变高</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="647" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="138.5" xml:space="preserve" y="476.0779656304019" zvalue="784">压侧</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="646" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,289.747,462.078) scale(1,1) translate(0,9.83658e-14)" writing-mode="lr" x="289.7466024121145" xml:space="preserve" y="466.5779637230767" zvalue="785">35kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="645" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,343.33,462.078) scale(1,1) translate(0,0)" writing-mode="lr" x="343.329935745448" xml:space="preserve" y="466.5779637230766" zvalue="786">35kVⅡ母</text>
  <text fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="644" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,51.89,494.84) scale(1,1) translate(0,-1.07656e-13)" writing-mode="lr" x="51.89" xml:space="preserve" y="499.34" zvalue="787">Uab</text>
  <text fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="643" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,49.1956,518.617) scale(1,1) translate(0,0)" writing-mode="lr" x="49.2" xml:space="preserve" y="523.12" zvalue="788">Ua</text>
  <text fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="642" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,50.0845,541.951) scale(1,1) translate(0,0)" writing-mode="lr" x="50.08" xml:space="preserve" y="546.45" zvalue="789">Ub</text>
  <text fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="641" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,48.9734,589.729) scale(1,1) translate(0,1.93088e-13)" writing-mode="lr" x="48.97" xml:space="preserve" y="594.23" zvalue="790">U0</text>
  <text fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="636" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,50.64,565.284) scale(1,1) translate(0,0)" writing-mode="lr" x="50.64" xml:space="preserve" y="569.78" zvalue="795">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,224.625,951.25) scale(1,1) translate(0,0)" writing-mode="lr" x="147.25" xml:space="preserve" y="955.75" zvalue="1100">DaiLongZ-01-2022</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,71.625,340) scale(1,1) translate(0,3.65153e-13)" writing-mode="lr" x="71.62499999999955" xml:space="preserve" y="344.4999999999999" zvalue="1210">全站公用</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="453" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1133.53,474.192) scale(1,1) translate(0,0)" writing-mode="lr" x="1133.53" xml:space="preserve" y="478.69" zvalue="1297">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="733" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1131.12,409.4) scale(1,1) translate(0,0)" writing-mode="lr" x="1131.12" xml:space="preserve" y="413.9" zvalue="1299">67</text>
 </g>
 <g id="ButtonClass">
  <g href="单厂站信息-20210617.svg"><rect fill-opacity="0" height="24" width="72.88" x="35.19" y="407.25" zvalue="1057"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="35.19" y="368.75" zvalue="1113"/></g>
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="144.06" y="407.25" zvalue="1224"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="856">
   <path class="kv220" d="M 778 615.21 L 1607.25 615.21" stroke-width="4" zvalue="45"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="220kVⅡ母"/>
   </metadata>
  <path d="M 778 615.21 L 1607.25 615.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="855">
   <path class="kv220" d="M 772 652.39 L 1607.25 652.39" stroke-width="4" zvalue="46"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="220kVⅠ母"/>
   </metadata>
  <path d="M 772 652.39 L 1607.25 652.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="288">
   <use class="kv220" height="20" transform="rotate(0,1080.12,509.131) scale(1.46889,-1.322) translate(-342.445,-891.032)" width="10" x="1072.775678089911" xlink:href="#Breaker:开关_0" y="495.9109346757106" zvalue="268"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="220kV金孔雀Ⅰ回线241断路器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1080.12,509.131) scale(1.46889,-1.322) translate(-342.445,-891.032)" width="10" x="1072.775678089911" y="495.9109346757106"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="287">
   <use class="kv220" height="30" transform="rotate(0,1101.09,568.774) scale(0.839827,-0.615873) translate(208.8,-1498.06)" width="15" x="1094.789642290147" xlink:href="#Disconnector:刀闸_0" y="559.5357176235746" zvalue="269"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="220kV孔雀Ⅰ回线2811隔离开关"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1101.09,568.774) scale(0.839827,-0.615873) translate(208.8,-1498.06)" width="15" x="1094.789642290147" y="559.5357176235746"/></g>
  <g id="284">
   <use class="kv220" height="30" transform="rotate(0,1056.9,566.274) scale(0.839827,-0.615873) translate(200.372,-1491.5)" width="15" x="1050.599166099671" xlink:href="#Disconnector:刀闸_0" y="557.0357176235743" zvalue="272"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="220kV金孔雀Ⅰ回线2812隔离开关"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1056.9,566.274) scale(0.839827,-0.615873) translate(200.372,-1491.5)" width="15" x="1050.599166099671" y="557.0357176235743"/></g>
  <g id="280">
   <use class="kv220" height="30" transform="rotate(0,1080.12,445.583) scale(0.839827,-0.615873) translate(204.801,-1174.84)" width="15" x="1073.821429027135" xlink:href="#Disconnector:刀闸_0" y="436.3452414330982" zvalue="276"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="220kV金塔Ⅰ回线2816隔离开关"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1080.12,445.583) scale(0.839827,-0.615873) translate(204.801,-1174.84)" width="15" x="1073.821429027135" y="436.3452414330982"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="276">
   <use class="kv220" height="40" transform="rotate(180,1077.82,351.131) scale(0.714286,-0.714286) translate(426.128,-848.429)" width="35" x="1065.320309669026" xlink:href="#ACLineSegment:220kV线路_0" y="336.8452382314772" zvalue="280"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="220kV金孔雀Ⅰ回线"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(180,1077.82,351.131) scale(0.714286,-0.714286) translate(426.128,-848.429)" width="35" x="1065.320309669026" y="336.8452382314772"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="274">
   <use class="kv220" height="20" transform="rotate(90,1134.66,547.161) scale(0.94375,-0.835887) translate(67.3475,-1203.39)" width="10" x="1129.941033121339" xlink:href="#GroundDisconnector:地刀_0" y="538.8018430004414" zvalue="282"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="220kV金孔雀Ⅰ回线28127接地开关"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1134.66,547.161) scale(0.94375,-0.835887) translate(67.3475,-1203.39)" width="10" x="1129.941033121339" y="538.8018430004414"/></g>
  <g id="389">
   <use class="kv220" height="20" transform="rotate(90,1109.16,478.036) scale(0.94375,-0.835887) translate(65.8276,-1051.57)" width="10" x="1104.441033121339" xlink:href="#GroundDisconnector:地刀_0" y="469.6768430004414" zvalue="1296"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="220kV金孔雀Ⅰ回线28160接地开关"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1109.16,478.036) scale(0.94375,-0.835887) translate(65.8276,-1051.57)" width="10" x="1104.441033121339" y="469.6768430004414"/></g>
  <g id="478">
   <use class="kv220" height="20" transform="rotate(90,1103.62,407.619) scale(0.94375,-0.835887) translate(65.4973,-896.909)" width="10" x="1098.899366454672" xlink:href="#GroundDisconnector:地刀_0" y="399.2601763337748" zvalue="1298"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="220kV金孔雀Ⅰ回线28167接地开关"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1103.62,407.619) scale(0.94375,-0.835887) translate(65.4973,-896.909)" width="10" x="1098.899366454672" y="399.2601763337748"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="640">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="640" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,339.742,540.25) scale(1,1) translate(-6.98128e-14,0)" writing-mode="lr" x="339.48" xml:space="preserve" y="544.92" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="639">
   <text Format="f5.2" Plane="0" fill="rgb(170,255,255)" font-family="SimSun" font-size="13" id="639" stroke="rgb(170,255,255)" text-anchor="middle" transform="rotate(0,338.742,493.694) scale(1,1) translate(-6.95907e-14,0)" writing-mode="lr" x="338.48" xml:space="preserve" y="498.36" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="638">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="638" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,339.742,518.028) scale(1,1) translate(-6.98128e-14,0)" writing-mode="lr" x="339.48" xml:space="preserve" y="522.6900000000001" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ubc"/>
   </metadata>
  </g>
  <g id="637">
   <text Format="f5.2" Plane="0" fill="rgb(170,255,255)" font-family="SimSun" font-size="13" id="637" stroke="rgb(170,255,255)" text-anchor="middle" transform="rotate(0,339.742,588.028) scale(1,1) translate(-6.98128e-14,-2.56474e-13)" writing-mode="lr" x="339.48" xml:space="preserve" y="592.6900000000001" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="635">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="635" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,339.742,564.833) scale(1,1) translate(-6.98128e-14,0)" writing-mode="lr" x="339.48" xml:space="preserve" y="569.5" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="634">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="634" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,288.492,540.25) scale(1,1) translate(-5.8433e-14,0)" writing-mode="lr" x="288.23" xml:space="preserve" y="544.92" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="633">
   <text Format="f5.2" Plane="0" fill="rgb(170,255,255)" font-family="SimSun" font-size="13" id="633" stroke="rgb(170,255,255)" text-anchor="middle" transform="rotate(0,288.492,494.694) scale(1,1) translate(-5.8433e-14,0)" writing-mode="lr" x="288.23" xml:space="preserve" y="499.36" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="632">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="632" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,288.492,518.028) scale(1,1) translate(-5.8433e-14,0)" writing-mode="lr" x="288.23" xml:space="preserve" y="522.6900000000001" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ubc"/>
   </metadata>
  </g>
  <g id="631">
   <text Format="f5.2" Plane="0" fill="rgb(170,255,255)" font-family="SimSun" font-size="13" id="631" stroke="rgb(170,255,255)" text-anchor="middle" transform="rotate(0,288.492,588.028) scale(1,1) translate(-5.8433e-14,-2.56474e-13)" writing-mode="lr" x="288.23" xml:space="preserve" y="592.6900000000001" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="630">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="630" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,288.492,564.833) scale(1,1) translate(-5.8433e-14,0)" writing-mode="lr" x="288.23" xml:space="preserve" y="569.5" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="623">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="623" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,238.492,540.25) scale(1,1) translate(-4.73307e-14,0)" writing-mode="lr" x="238.23" xml:space="preserve" y="544.92" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="622">
   <text Format="f5.2" Plane="0" fill="rgb(170,255,255)" font-family="SimSun" font-size="13" id="622" stroke="rgb(170,255,255)" text-anchor="middle" transform="rotate(0,238.492,494.694) scale(1,1) translate(-4.73307e-14,0)" writing-mode="lr" x="238.23" xml:space="preserve" y="499.36" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="621">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="621" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,238.492,518.028) scale(1,1) translate(-4.73307e-14,0)" writing-mode="lr" x="238.23" xml:space="preserve" y="522.6900000000001" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ubc"/>
   </metadata>
  </g>
  <g id="616">
   <text Format="f5.2" Plane="0" fill="rgb(170,255,255)" font-family="SimSun" font-size="13" id="616" stroke="rgb(170,255,255)" text-anchor="middle" transform="rotate(0,238.492,588.028) scale(1,1) translate(-4.73307e-14,-2.56474e-13)" writing-mode="lr" x="238.23" xml:space="preserve" y="592.6900000000001" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="615">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="615" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,238.492,564.833) scale(1,1) translate(-4.73307e-14,0)" writing-mode="lr" x="238.23" xml:space="preserve" y="569.5" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="614">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="614" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,187.242,540.25) scale(1,1) translate(-3.5951e-14,0)" writing-mode="lr" x="186.98" xml:space="preserve" y="544.92" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="612">
   <text Format="f5.2" Plane="0" fill="rgb(170,255,255)" font-family="SimSun" font-size="13" id="612" stroke="rgb(170,255,255)" text-anchor="middle" transform="rotate(0,187.242,494.694) scale(1,1) translate(-3.5951e-14,0)" writing-mode="lr" x="186.98" xml:space="preserve" y="499.36" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="346">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="346" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,187.242,518.028) scale(1,1) translate(-3.5951e-14,0)" writing-mode="lr" x="186.98" xml:space="preserve" y="522.6900000000001" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ubc"/>
   </metadata>
  </g>
  <g id="311">
   <text Format="f5.2" Plane="0" fill="rgb(170,255,255)" font-family="SimSun" font-size="13" id="311" stroke="rgb(170,255,255)" text-anchor="middle" transform="rotate(0,187.242,588.028) scale(1,1) translate(-3.5951e-14,-2.56474e-13)" writing-mode="lr" x="186.98" xml:space="preserve" y="592.6900000000001" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="310">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="310" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,187.242,564.833) scale(1,1) translate(-3.5951e-14,0)" writing-mode="lr" x="186.98" xml:space="preserve" y="569.5" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="306">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="306" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,138.492,540.25) scale(1,1) translate(-2.51263e-14,0)" writing-mode="lr" x="138.23" xml:space="preserve" y="544.92" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="305">
   <text Format="f5.2" Plane="0" fill="rgb(170,255,255)" font-family="SimSun" font-size="13" id="305" stroke="rgb(170,255,255)" text-anchor="middle" transform="rotate(0,138.492,494.694) scale(1,1) translate(-2.51263e-14,0)" writing-mode="lr" x="138.23" xml:space="preserve" y="499.36" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="301">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="301" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,138.492,518.028) scale(1,1) translate(-2.51263e-14,0)" writing-mode="lr" x="138.23" xml:space="preserve" y="522.6900000000001" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ubc"/>
   </metadata>
  </g>
  <g id="167">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="167" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,138.492,564.833) scale(1,1) translate(-2.51263e-14,0)" writing-mode="lr" x="138.23" xml:space="preserve" y="569.5" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="162">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="162" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,90.9921,540.25) scale(1,1) translate(-1.45792e-14,0)" writing-mode="lr" x="90.73" xml:space="preserve" y="544.92" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="132">
   <text Format="f5.2" Plane="0" fill="rgb(170,255,255)" font-family="SimSun" font-size="13" id="132" stroke="rgb(170,255,255)" text-anchor="middle" transform="rotate(0,90.9921,494.694) scale(1,1) translate(-1.45792e-14,0)" writing-mode="lr" x="90.73" xml:space="preserve" y="499.36" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="129">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="129" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,90.9921,518.028) scale(1,1) translate(-1.45792e-14,0)" writing-mode="lr" x="90.73" xml:space="preserve" y="522.6900000000001" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ubc"/>
   </metadata>
  </g>
  <g id="71">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="71" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,90.9921,564.833) scale(1,1) translate(-1.45792e-14,0)" writing-mode="lr" x="90.73" xml:space="preserve" y="569.5" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="652">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="652" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,159.27,176) scale(1,1) translate(0,0)" writing-mode="lr" x="158.88" xml:space="preserve" y="180.67" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="1069">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="1069" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,340.329,271.016) scale(1,1) translate(0,3.47077e-13)" writing-mode="lr" x="340.09" xml:space="preserve" y="277.18" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="YW1"/>
   </metadata>
  </g>
  <g id="1112">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1112" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,340.329,251.25) scale(1,1) translate(-6.99432e-14,0)" writing-mode="lr" x="340.07" xml:space="preserve" y="255.92" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="1131">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="1131" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,159.27,270.405) scale(1,1) translate(0,3.46263e-13)" writing-mode="lr" x="159.04" xml:space="preserve" y="276.57" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="YW1"/>
   </metadata>
  </g>
  <g id="1140">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1140" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,340.329,176.667) scale(1,1) translate(0,0)" writing-mode="lr" x="339.94" xml:space="preserve" y="181.34" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="1150">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1150" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,159.27,201.694) scale(1,1) translate(0,0)" writing-mode="lr" x="159.01" xml:space="preserve" y="206.36" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="1151">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1151" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,340.329,203.758) scale(1,1) translate(0,0)" writing-mode="lr" x="340.07" xml:space="preserve" y="208.42" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="1153">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1153" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,159.27,225.667) scale(1,1) translate(-2.97399e-14,0)" writing-mode="lr" x="159.01" xml:space="preserve" y="230.33" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="1154">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1154" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,159.27,248.917) scale(1,1) translate(-2.97399e-14,0)" writing-mode="lr" x="159.01" xml:space="preserve" y="253.58" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="1189">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1189" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,340.329,225.667) scale(1,1) translate(-6.99432e-14,0)" writing-mode="lr" x="340.07" xml:space="preserve" y="230.33" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="656">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="656" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,340.329,294.016) scale(1,1) translate(0,0)" writing-mode="lr" x="340.09" xml:space="preserve" y="300.18" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="655">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="655" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,159.27,293.405) scale(1,1) translate(0,0)" writing-mode="lr" x="159.04" xml:space="preserve" y="299.57" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="35">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="35" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1077.82,409.417) scale(1,1) translate(-2.29554e-13,0)" writing-mode="lr" x="1077.36" xml:space="preserve" y="414.08" zvalue="1">P:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="667">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="667" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1077.82,426.226) scale(1,1) translate(-2.29554e-13,0)" writing-mode="lr" x="1077.36" xml:space="preserve" y="430.88" zvalue="1">Q:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="678">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="678" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1077.82,443.036) scale(1,1) translate(-2.29554e-13,0)" writing-mode="lr" x="1077.36" xml:space="preserve" y="447.69" zvalue="1">Ia:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="689">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="689" prefix="Ux:" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1077.82,459.845) scale(1,1) translate(-2.29554e-13,0)" writing-mode="lr" x="1077.36" xml:space="preserve" y="464.5" zvalue="1">Ux:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ux"/>
   </metadata>
  </g>
  <g id="727">
   <text Format="f5.2" Plane="0" fill="rgb(170,255,255)" font-family="SimSun" font-size="13" id="727" stroke="rgb(170,255,255)" text-anchor="middle" transform="rotate(0,532.242,909.694) scale(1,1) translate(-1.12556e-13,0)" writing-mode="lr" x="531.98" xml:space="preserve" y="914.36" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="728">
   <text Format="f5.2" Plane="0" fill="rgb(170,255,255)" font-family="SimSun" font-size="13" id="728" stroke="rgb(170,255,255)" text-anchor="middle" transform="rotate(0,608.492,909.694) scale(1,1) translate(-1.29487e-13,0)" writing-mode="lr" x="608.23" xml:space="preserve" y="914.36" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="729">
   <text Format="f5.2" Plane="0" fill="rgb(170,255,255)" font-family="SimSun" font-size="13" id="729" stroke="rgb(170,255,255)" text-anchor="middle" transform="rotate(0,552.492,231.694) scale(1,1) translate(-1.17053e-13,0)" writing-mode="lr" x="552.23" xml:space="preserve" y="236.36" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="730">
   <text Format="f5.2" Plane="0" fill="rgb(170,255,255)" font-family="SimSun" font-size="13" id="730" stroke="rgb(170,255,255)" text-anchor="middle" transform="rotate(0,1566.74,229.694) scale(1,1) translate(-3.42261e-13,0)" writing-mode="lr" x="1566.48" xml:space="preserve" y="234.36" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="654">
   <use height="30" transform="rotate(0,341.625,340.5) scale(0.708333,0.665547) translate(136.294,166.093)" width="30" x="331" xlink:href="#State:红绿圆(方形)_0" y="330.52" zvalue="833"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,341.625,340.5) scale(0.708333,0.665547) translate(136.294,166.093)" width="30" x="331" y="330.52"/></g>
  <g id="653">
   <use height="30" transform="rotate(0,282,339.5) scale(0.708333,0.665547) translate(111.743,165.59)" width="30" x="271.38" xlink:href="#State:红绿圆(方形)_0" y="329.52" zvalue="834"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,282,339.5) scale(0.708333,0.665547) translate(111.743,165.59)" width="30" x="271.38" y="329.52"/></g>
  <g id="826">
   <use height="30" transform="rotate(0,331.812,128.464) scale(1.27778,1.03333) translate(-59.6332,-3.64399)" width="90" x="274.31" xlink:href="#State:全站检修_0" y="112.96" zvalue="1098"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,331.812,128.464) scale(1.27778,1.03333) translate(-59.6332,-3.64399)" width="90" x="274.31" y="112.96"/></g>
  <g id="1082">
   <use height="30" transform="rotate(0,71.625,340) scale(0.910937,0.8) translate(3.44029,82)" width="80" x="35.19" xlink:href="#State:间隔模板_0" y="328" zvalue="1208"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,71.625,340) scale(0.910937,0.8) translate(3.44029,82)" width="80" x="35.19" y="328"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="804">
   <path class="kv220" d="M 1101.16 577.71 L 1101.16 652.39" stroke-width="1" zvalue="1299"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="287@0" LinkObjectIDznd="855@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1101.16 577.71 L 1101.16 652.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="806">
   <path class="kv220" d="M 1056.97 575.21 L 1056.97 615.21" stroke-width="1" zvalue="1300"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="284@0" LinkObjectIDznd="856@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1056.97 575.21 L 1056.97 615.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="854">
   <path class="kv220" d="M 1056.95 557.19 L 1056.95 543.5 L 1101.14 543.5 L 1101.14 559.69" stroke-width="1" zvalue="1301"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="284@1" LinkObjectIDznd="287@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1056.95 557.19 L 1056.95 543.5 L 1101.14 543.5 L 1101.14 559.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="865">
   <path class="kv220" d="M 1080.07 543.5 L 1080.07 521.78" stroke-width="1" zvalue="1302"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="854" LinkObjectIDznd="288@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1080.07 543.5 L 1080.07 521.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="869">
   <path class="kv220" d="M 1126.51 547.21 L 1101.14 547.21" stroke-width="1" zvalue="1303"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="274@0" LinkObjectIDznd="854" MaxPinNum="2"/>
   </metadata>
  <path d="M 1126.51 547.21 L 1101.14 547.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="871">
   <path class="kv220" d="M 1080.22 496.51 L 1080.19 454.52" stroke-width="1" zvalue="1304"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="288@1" LinkObjectIDznd="280@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1080.22 496.51 L 1080.19 454.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="872">
   <path class="kv220" d="M 1080.17 436.5 L 1080.17 364.71" stroke-width="1" zvalue="1305"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="280@1" LinkObjectIDznd="276@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1080.17 436.5 L 1080.17 364.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="877">
   <path class="kv220" d="M 1095.47 407.67 L 1080.17 407.67" stroke-width="1" zvalue="1306"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="478@0" LinkObjectIDznd="872" MaxPinNum="2"/>
   </metadata>
  <path d="M 1095.47 407.67 L 1080.17 407.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="878">
   <path class="kv220" d="M 1101.01 478.08 L 1080.21 478.08" stroke-width="1" zvalue="1307"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="389@0" LinkObjectIDznd="871" MaxPinNum="2"/>
   </metadata>
  <path d="M 1101.01 478.08 L 1080.21 478.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
</svg>