<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549684273153" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Disconnector:特殊刀闸_0" viewBox="0,0,45,25">
   <use terminal-index="0" type="0" x="21.05" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="21.05" xlink:href="#terminal" y="24.45"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="3.5" y1="3.25" y2="9.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="2.35" y2="10.35"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.66666666666667" x2="5.583333333333332" y1="6.333333333333335" y2="6.333333333333335"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6" x2="11.6" y1="4.383333333333335" y2="8.133333333333336"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.699999999999999" x2="1.699999999999999" y1="4.25" y2="8.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.66666666666667" x2="22.58333333333334" y1="6.41666666666667" y2="22.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="21.08333333333334" x2="21.08333333333334" y1="22.66666666666667" y2="24.41666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="21.09150672768254" x2="21.09150672768254" y1="3.000000000000002" y2="0.6887951642647483"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="19.46666666666667" x2="22.43333333333333" y1="3.005662181544974" y2="3.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:特殊刀闸_1" viewBox="0,0,45,25">
   <use terminal-index="0" type="0" x="21.05" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="21.05" xlink:href="#terminal" y="24.45"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.699999999999999" x2="1.699999999999999" y1="4.25" y2="8.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="3.5" y1="3.25" y2="9.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="2.35" y2="10.35"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.66666666666667" x2="5.583333333333332" y1="6.333333333333335" y2="6.333333333333335"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6" x2="11.6" y1="4.383333333333335" y2="8.133333333333336"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="26.41666666666667" x2="18.5" y1="4.416666666666668" y2="23"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="21.08333333333334" x2="21.08333333333334" y1="22.66666666666667" y2="24.41666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="21.09150672768254" x2="21.09150672768254" y1="3.000000000000002" y2="0.6887951642647483"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="19.46666666666667" x2="22.43333333333333" y1="3.005662181544974" y2="3.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:特殊刀闸_2" viewBox="0,0,45,25">
   <use terminal-index="0" type="0" x="21.05" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="21.05" xlink:href="#terminal" y="24.45"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6" x2="11.6" y1="4.383333333333335" y2="8.133333333333336"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.66666666666667" x2="5.583333333333332" y1="6.333333333333335" y2="6.333333333333335"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="3.5" y1="3.25" y2="9.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="2.35" y2="10.35"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.699999999999999" x2="1.699999999999999" y1="4.25" y2="8.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="21.08333333333334" x2="21.08333333333334" y1="22.66666666666667" y2="24.41666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="21.09150672768254" x2="21.09150672768254" y1="3.000000000000002" y2="0.6887951642647483"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="19.46666666666667" x2="22.43333333333333" y1="3.005662181544974" y2="3.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.91666666666667" x2="28.25" y1="3.083333333333332" y2="24.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="27.5" x2="13.16666666666667" y1="3.166666666666664" y2="24.83333333333334"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:带熔断器四卷PT_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12" y2="1"/>
   <ellipse cx="15.25" cy="16.75" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.08" cy="24.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="11.25" cy="20.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="18.82" cy="20.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="11" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="11" y1="23.25" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.25" x2="15.25" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.25" x2="15.25" y1="19" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.25" x2="15.25" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17" x2="15" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="26.75" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="15" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17" x2="20.94444444444444" y1="21.46612466124661" y2="21.46612466124661"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17" x2="18.31481481481482" y1="21.46612466124661" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.94444444444444" x2="19.62962962962963" y1="21.46612466124661" y2="19"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15.01,6.21) scale(1,1) translate(0,0)" width="4.92" x="12.55" y="2.5"/>
  </symbol>
  <symbol id="Accessory:PT带保险_0" viewBox="0,0,11,29">
   <use terminal-index="0" type="0" x="5.5" xlink:href="#terminal" y="0.1666666666666661"/>
   <rect fill-opacity="0" height="6" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.5,7.5) scale(1,1) translate(0,0)" width="4" x="3.5" y="4.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="13.16666666666667" y2="0.5"/>
   <ellipse cx="5.4" cy="18.1" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.4" cy="23.78" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:带电显示器_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.41666666666667" x2="8.750000000000002" y1="11.08333333333333" y2="14.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.08333333333333" x2="10.08333333333333" y1="1.5" y2="6.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.08333333333333" x2="10.08333333333333" y1="8.5" y2="10.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333334" x2="12.58333333333333" y1="6.5" y2="6.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333334" x2="12.58333333333333" y1="8.5" y2="8.5"/>
   <ellipse cx="10.08" cy="12.75" fill-opacity="0" rx="2.24" ry="2.24" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.083333333333334" x2="11.08333333333333" y1="19.5" y2="19.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.666666666666668" x2="11.66666666666667" y1="11.08333333333333" y2="14.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.583333333333334" x2="11.58333333333333" y1="18.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.08333333333333" x2="10.08333333333333" y1="17.5" y2="15.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.083333333333334" x2="12.08333333333333" y1="17.5" y2="17.5"/>
  </symbol>
  <symbol id="Accessory:腊撒线路PT_0" viewBox="0,0,12,32">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="0.6666666666666661"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="3" y1="27" y2="25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="27" y2="30"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="13.66666666666667" y2="1"/>
   <path d="M 6 15 L 3 20 L 9 20 L 6 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="9" y1="27" y2="25"/>
   <rect fill-opacity="0" height="6" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,8) scale(1,1) translate(0,0)" width="4" x="4" y="5"/>
   <ellipse cx="5.9" cy="18.6" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.9" cy="26.28" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Accessory:五卷PT_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="25.16666666666667" y2="1"/>
   <rect fill-opacity="0" height="14" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,14) scale(1,1) translate(0,0)" width="6" x="7" y="7"/>
   <path d="M 5.11667 33.0667 L 5.11667 37.0667 L 8.11667 35.0667 L 5.11667 33.0667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.66666666666667" x2="9.999999999999998" y1="4" y2="4"/>
   <ellipse cx="13.48" cy="27.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="27.48744029990987" y2="26.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="29.96252026861409" y2="27.48744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="27.48744029990987" y2="26.24990031555776"/>
   <ellipse cx="13.48" cy="34.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="6.48" cy="27.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.44906826265991" x2="6.44906826265991" y1="29.96252026861409" y2="27.48744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.449068262659912" x2="8.849068262659907" y1="27.48744029990987" y2="26.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.449068262659907" x2="4.049068262659912" y1="27.48744029990987" y2="26.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="34.48744029990986" y2="33.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="34.48744029990986" y2="33.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="36.96252026861409" y2="34.48744029990989"/>
   <ellipse cx="6.48" cy="34.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.11944444444444" x2="29.63333333333333" y1="28.26666666666667" y2="28.26666666666667"/>
   <rect fill-opacity="0" height="16.15" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,27.29,13.93) scale(1,1) translate(0,0)" width="7.58" x="23.5" y="5.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="27.55" y1="3.93333333333333" y2="5.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="27.55" y1="5.85" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="24.55" y1="15.85" y2="12.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="30.55" y1="15.85" y2="12.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.16111111111111" x2="30.27222222222222" y1="26.88508771929826" y2="26.88508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="27.55" y1="21.85" y2="25.45"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.88333333333333" x2="31.55" y1="25.50350877192984" y2="25.50350877192984"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="14.63292181069959"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="9.583333333333334" y2="14.63582329690123"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00459493611119" x2="10.08333333333333" y1="14.63240401999107" y2="19.41666666666666"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01979386477084" x2="20.08333333333333" y1="14.63518747193215" y2="19.41666666666666"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 10.1667 40.3233 L 19.9167 40.3233 L 15.0833 32.3333 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id=":单相三绕组_0" viewBox="0,0,45,45">
   <use terminal-index="0" type="0" x="22.5" xlink:href="#terminal" y="44.60000000000001"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="30.00545055364641" x2="30.00545055364641" y1="18.54138864447597" y2="24.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.96058610156545" x2="22.65436235204273" y1="18.42541949757221" y2="18.42541949757221"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="31.5" x2="28.5" y1="29.5" y2="29.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="27.41094883543335" x2="32.88823024054981" y1="18.42541949757221" y2="18.42541949757221"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.66022336769755" x2="22.66022336769755" y1="44.33333333333334" y2="0.2499999999999964"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.96383161512026" x2="39.91666666666666" y1="18.49758812251703" y2="18.49758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.85940244368076" x2="32.85940244368076" y1="18.49758812251703" y2="18.49758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="39.86598892707138" x2="39.86598892707138" y1="15.25" y2="21.74517624503405"/>
   <ellipse cx="30.01" cy="29.32" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="37.05" cy="33.58" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="30.26" cy="37.58" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="32.85940244368077" x2="32.85940244368077" y1="15.98325293741726" y2="21.08409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="41.01910080183274" x2="41.01910080183274" y1="16.51295093653441" y2="20.84306843322376"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="41.88393470790376" x2="41.88393470790376" y1="17.41505874834466" y2="19.39969593432727"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="25.07589728904158" x2="25.07589728904158" y1="18.49758812251703" y2="18.49758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="24.99256395570825" x2="24.99256395570825" y1="15.83333333333333" y2="20.93417232847779"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="35.02148720885832" x2="35.02148720885832" y1="15.98325293741726" y2="21.08409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="27.23798205421914" x2="27.23798205421914" y1="15.98325293741726" y2="21.08409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="31.58333333333334" x2="28.58333333333334" y1="37.58333333333334" y2="37.58333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38.58333333333334" x2="35.58333333333334" y1="33.58333333333334" y2="33.58333333333334"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV真通电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="256" x="49.5" xlink:href="logo.png" y="35"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,177.5,65) scale(1,1) translate(0,0)" writing-mode="lr" x="177.5" xml:space="preserve" y="68.5" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,178,64.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="178" xml:space="preserve" y="73.69" zvalue="3">110kV真通电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="176" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,83.625,195) scale(1,1) translate(0,0)" width="72.88" x="47.19" y="183" zvalue="256"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.625,195) scale(1,1) translate(0,0)" writing-mode="lr" x="83.63" xml:space="preserve" y="199.5" zvalue="256">信号一览</text>
  <line fill="none" id="35" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="383.5" x2="383.5" y1="3" y2="1033" zvalue="4"/>
  <line fill="none" id="33" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.500000000000455" x2="376.5" y1="138.8704926140824" y2="138.8704926140824" zvalue="6"/>
  <line fill="none" id="31" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.500000000000455" x2="376.5" y1="608.8704926140824" y2="608.8704926140824" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="9.5" x2="99.5" y1="924" y2="924"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="9.5" x2="99.5" y1="963.1632999999999" y2="963.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="9.5" x2="9.5" y1="924" y2="963.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="99.5" x2="99.5" y1="924" y2="963.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="99.5" x2="369.5" y1="924" y2="924"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="99.5" x2="369.5" y1="963.1632999999999" y2="963.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="99.5" x2="99.5" y1="924" y2="963.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="369.5" x2="369.5" y1="924" y2="963.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="9.5" x2="99.5" y1="963.16327" y2="963.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="9.5" x2="99.5" y1="991.08167" y2="991.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="9.5" x2="9.5" y1="963.16327" y2="991.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="99.5" x2="99.5" y1="963.16327" y2="991.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="99.5" x2="189.5" y1="963.16327" y2="963.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="99.5" x2="189.5" y1="991.08167" y2="991.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="99.5" x2="99.5" y1="963.16327" y2="991.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="189.5" x2="189.5" y1="963.16327" y2="991.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="189.5000000000001" x2="279.5000000000001" y1="963.16327" y2="963.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="189.5000000000001" x2="279.5000000000001" y1="991.08167" y2="991.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="189.5000000000001" x2="189.5000000000001" y1="963.16327" y2="991.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5000000000001" x2="279.5000000000001" y1="963.16327" y2="991.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5" x2="369.5" y1="963.16327" y2="963.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5" x2="369.5" y1="991.08167" y2="991.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5" x2="279.5" y1="963.16327" y2="991.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="369.5" x2="369.5" y1="963.16327" y2="991.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="9.5" x2="99.5" y1="991.0816" y2="991.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="9.5" x2="99.5" y1="1019" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="9.5" x2="9.5" y1="991.0816" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="99.5" x2="99.5" y1="991.0816" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="99.5" x2="189.5" y1="991.0816" y2="991.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="99.5" x2="189.5" y1="1019" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="99.5" x2="99.5" y1="991.0816" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="189.5" x2="189.5" y1="991.0816" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="189.5000000000001" x2="279.5000000000001" y1="991.0816" y2="991.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="189.5000000000001" x2="279.5000000000001" y1="1019" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="189.5000000000001" x2="189.5000000000001" y1="991.0816" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5000000000001" x2="279.5000000000001" y1="991.0816" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5" x2="369.5" y1="991.0816" y2="991.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5" x2="369.5" y1="1019" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5" x2="279.5" y1="991.0816" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="369.5" x2="369.5" y1="991.0816" y2="1019"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.5,944) scale(1,1) translate(0,0)" writing-mode="lr" x="54.5" xml:space="preserve" y="950" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51.5,978) scale(1,1) translate(0,0)" writing-mode="lr" x="51.5" xml:space="preserve" y="984" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.5,978) scale(1,1) translate(0,0)" writing-mode="lr" x="233.5" xml:space="preserve" y="984" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,50.5,1006) scale(1,1) translate(0,0)" writing-mode="lr" x="50.5" xml:space="preserve" y="1012" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232.5,1006) scale(1,1) translate(0,0)" writing-mode="lr" x="232.5" xml:space="preserve" y="1012" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,75,638.5) scale(1,1) translate(0,0)" writing-mode="lr" x="75" xml:space="preserve" y="643" zvalue="19">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.554,946) scale(1,1) translate(0,0)" writing-mode="lr" x="234.55" xml:space="preserve" y="952" zvalue="29">ZhenTong-01-2020</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,476,626.125) scale(1,1) translate(0,0)" writing-mode="lr" x="476" xml:space="preserve" y="630.63" zvalue="40">6.3kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" x="686.3515625" xml:space="preserve" y="931.4869357854257" zvalue="42">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="686.3515625" xml:space="preserve" y="947.4869357854257" zvalue="42">4MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,659.622,761.419) scale(1,1) translate(0,0)" writing-mode="lr" x="659.62" xml:space="preserve" y="765.92" zvalue="48">651</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,643.94,695.669) scale(1,1) translate(0,0)" writing-mode="lr" x="643.9400000000001" xml:space="preserve" y="700.17" zvalue="50">6511</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,806.749,842.554) scale(1,1) translate(0,0)" writing-mode="lr" x="806.75" xml:space="preserve" y="847.05" zvalue="52">6912</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,711.007,844.022) scale(1,1) translate(0,0)" writing-mode="lr" x="711.01" xml:space="preserve" y="848.52" zvalue="65">6911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,775.444,869.361) scale(1,1) translate(0,0)" writing-mode="lr" x="775.4400000000001" xml:space="preserve" y="873.86" zvalue="82">7</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,886,847.333) scale(1,1) translate(0,0)" writing-mode="lr" x="886" xml:space="preserve" y="851.83" zvalue="84">7</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" x="1183.3359375" xml:space="preserve" y="931.4869357854257" zvalue="93">#2发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1183.3359375" xml:space="preserve" y="947.4869357854257" zvalue="93">4MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1156.62,761.419) scale(1,1) translate(0,0)" writing-mode="lr" x="1156.62" xml:space="preserve" y="765.92" zvalue="97">652</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1140.94,695.669) scale(1,1) translate(0,0)" writing-mode="lr" x="1140.94" xml:space="preserve" y="700.17" zvalue="99">6521</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1304.84,843.554) scale(1,1) translate(0,0)" writing-mode="lr" x="1304.84" xml:space="preserve" y="848.05" zvalue="101">6922</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1208.01,844.022) scale(1,1) translate(0,0)" writing-mode="lr" x="1208.01" xml:space="preserve" y="848.52" zvalue="109">6921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1272.44,869.361) scale(1,1) translate(0,0)" writing-mode="lr" x="1272.44" xml:space="preserve" y="873.86" zvalue="115">7</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1383,847.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1383" xml:space="preserve" y="851.83" zvalue="117">7</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="109" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,999.028,494.028) scale(1,1) translate(0,0)" writing-mode="lr" x="999.0277777777778" xml:space="preserve" y="498.5277777777778" zvalue="130">6.3kV母线PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="120" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,966.316,622.206) scale(1,1) translate(0,0)" writing-mode="lr" x="966.3200000000001" xml:space="preserve" y="626.71" zvalue="135">6901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="121" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1042.33,615.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1042.33" xml:space="preserve" y="619.72" zvalue="139">69017</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="130" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,755.431,297.972) scale(1,1) translate(0,0)" writing-mode="lr" x="755.4299999999999" xml:space="preserve" y="302.47" zvalue="142">1516</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="129" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,746.194,355.972) scale(1,1) translate(0,0)" writing-mode="lr" x="746.1900000000001" xml:space="preserve" y="360.47" zvalue="144">151</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="128" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,662.083,341.943) scale(1,1) translate(0,0)" writing-mode="lr" x="662.08" xml:space="preserve" y="346.44" zvalue="147">60</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="127" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,811.499,413.419) scale(1,1) translate(-1.74041e-13,4.41967e-14)" writing-mode="lr" x="811.5" xml:space="preserve" y="417.92" zvalue="150">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="126" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,593.95,457.5) scale(1,1) translate(0,0)" writing-mode="lr" x="593.95" xml:space="preserve" y="462" zvalue="155">1010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="125" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,749.635,569.163) scale(1,1) translate(0,0)" writing-mode="lr" x="749.63" xml:space="preserve" y="573.66" zvalue="157">601</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,817.651,557.786) scale(1,1) translate(0,-1.21966e-13)" writing-mode="lr" x="817.65" xml:space="preserve" y="562.29" zvalue="165">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="131" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,808.146,433.419) scale(1,1) translate(0,0)" writing-mode="lr" x="808.15" xml:space="preserve" y="437.92" zvalue="169">10MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,710.374,624.706) scale(1,1) translate(0,0)" writing-mode="lr" x="710.37" xml:space="preserve" y="629.21" zvalue="176">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,710.541,512.483) scale(1,1) translate(0,0)" writing-mode="lr" x="710.54" xml:space="preserve" y="516.98" zvalue="183">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,659.194,296.5) scale(1,1) translate(0,0)" writing-mode="lr" x="659.1900000000001" xml:space="preserve" y="301" zvalue="188">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,725.514,154.5) scale(1,1) translate(0,0)" writing-mode="lr" x="725.51" xml:space="preserve" y="159" zvalue="194">110kV真南线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1406.63,569.163) scale(1,1) translate(0,0)" writing-mode="lr" x="1406.63" xml:space="preserve" y="573.66" zvalue="196">654</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1441.65,558.786) scale(1,1) translate(0,-1.22188e-13)" writing-mode="lr" x="1441.65" xml:space="preserve" y="563.29" zvalue="198">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1367.37,624.706) scale(1,1) translate(0,0)" writing-mode="lr" x="1367.37" xml:space="preserve" y="629.21" zvalue="200">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1367.54,512.483) scale(1,1) translate(0,0)" writing-mode="lr" x="1367.54" xml:space="preserve" y="516.98" zvalue="205">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="124" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1381,438.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1381" xml:space="preserve" y="443" zvalue="208">6.3kV三合村线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="150" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1717.04,620.163) scale(1,1) translate(0,0)" writing-mode="lr" x="1717.04" xml:space="preserve" y="624.66" zvalue="211">655</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="156" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1698.04,404.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1698.04" xml:space="preserve" y="409" zvalue="216">#2厂用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="159" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1699.04,426.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1699.04" xml:space="preserve" y="431" zvalue="220">（生活区用电）</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="163" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1540.37,736.706) scale(1,1) translate(0,0)" writing-mode="lr" x="1540.37" xml:space="preserve" y="741.21" zvalue="223">6531</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="164" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1602.47,743.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1602.47" xml:space="preserve" y="747.83" zvalue="225">65317</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1571.74,895) scale(1,1) translate(0,0)" writing-mode="lr" x="1571.74" xml:space="preserve" y="899.5" zvalue="228">#1厂用变</text>
  <rect fill="none" fill-opacity="0" height="293" id="171" stroke="rgb(255,255,255)" stroke-dasharray="6 2" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,1391,535.5) scale(1,1) translate(0,0)" width="202" x="1290" y="389" zvalue="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="256.75" y2="256.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="282.75" y2="282.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="10" y1="256.75" y2="282.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="256.75" y2="282.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="256.75" y2="256.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="282.75" y2="282.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="256.75" y2="282.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372" x2="372" y1="256.75" y2="282.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="282.75" y2="282.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="307" y2="307"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="10" y1="282.75" y2="307"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="282.75" y2="307"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="282.75" y2="282.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="307" y2="307"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="282.75" y2="307"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372" x2="372" y1="282.75" y2="307"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="307" y2="307"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="329.75" y2="329.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="10" y1="307" y2="329.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="307" y2="329.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="307" y2="307"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="329.75" y2="329.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="307" y2="329.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372" x2="372" y1="307" y2="329.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="329.75" y2="329.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="352.5" y2="352.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="10" y1="329.75" y2="352.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="329.75" y2="352.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="329.75" y2="329.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="352.5" y2="352.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="329.75" y2="352.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372" x2="372" y1="329.75" y2="352.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="352.5" y2="352.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="375.25" y2="375.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="10" y1="352.5" y2="375.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="352.5" y2="375.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="352.5" y2="352.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="375.25" y2="375.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="352.5" y2="375.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372" x2="372" y1="352.5" y2="375.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="375.25" y2="375.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="398" y2="398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="10" y1="375.25" y2="398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="375.25" y2="398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="375.25" y2="375.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="398" y2="398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="375.25" y2="398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372" x2="372" y1="375.25" y2="398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="398" y2="398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="420.75" y2="420.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="10" y1="398" y2="420.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="398" y2="420.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="398" y2="398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="420.75" y2="420.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="398" y2="420.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372" x2="372" y1="398" y2="420.75"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="185" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,199.399,195.591) scale(1,1) translate(0,0)" writing-mode="lr" x="199.4" xml:space="preserve" y="200.09" zvalue="243">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="184" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,304.399,195.591) scale(1,1) translate(0,0)" writing-mode="lr" x="304.4" xml:space="preserve" y="200.09" zvalue="244">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="183" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,56.5,269.75) scale(1,1) translate(0,0)" writing-mode="lr" x="14" xml:space="preserve" y="274.25" zvalue="245">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="182" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,237,269.75) scale(1,1) translate(0,0)" writing-mode="lr" x="194.5" xml:space="preserve" y="274.25" zvalue="246">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="179" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.6875,367) scale(1,1) translate(0,0)" writing-mode="lr" x="56.69" xml:space="preserve" y="371.5" zvalue="249">6.3kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="175" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,55.5,295.75) scale(1,1) translate(0,0)" writing-mode="lr" x="13" xml:space="preserve" y="300.25" zvalue="257">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="174" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,236,295.75) scale(1,1) translate(0,0)" writing-mode="lr" x="193.5" xml:space="preserve" y="300.25" zvalue="258">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="173" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.6875,389) scale(1,1) translate(0,0)" writing-mode="lr" x="56.69" xml:space="preserve" y="393.5" zvalue="261">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="172" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,224.688,388) scale(1,1) translate(0,0)" writing-mode="lr" x="224.69" xml:space="preserve" y="392.5" zvalue="262">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="167" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.6875,412) scale(1,1) translate(0,0)" writing-mode="lr" x="56.69" xml:space="preserve" y="416.5" zvalue="263">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="166" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,224.688,411) scale(1,1) translate(0,0)" writing-mode="lr" x="224.69" xml:space="preserve" y="415.5" zvalue="264">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="165" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,55.5,318.75) scale(1,1) translate(0,0)" writing-mode="lr" x="13" xml:space="preserve" y="323.25" zvalue="265">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="158" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,235.5,317.75) scale(1,1) translate(0,0)" writing-mode="lr" x="193" xml:space="preserve" y="322.25" zvalue="267">厂用电率</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="47.19" y="183" zvalue="256"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="38">
   <path class="v6300" d="M 525 660 L 1772.5 660" stroke-width="6" zvalue="39"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674421047299" ObjectName="6.3kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674421047299"/></metadata>
  <path d="M 525 660 L 1772.5 660" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="v6300" height="30" transform="rotate(0,685.391,889.811) scale(1.43307,1.43307) translate(-200.626,-262.401)" width="30" x="663.894586889557" xlink:href="#Generator:发电机_0" y="868.3152867370809" zvalue="41"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454778617858" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192454778617858"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,685.391,889.811) scale(1.43307,1.43307) translate(-200.626,-262.401)" width="30" x="663.894586889557" y="868.3152867370809"/></g>
  <g id="108">
   <use class="v6300" height="30" transform="rotate(0,1182.39,889.811) scale(1.43307,1.43307) translate(-350.817,-262.401)" width="30" x="1160.894586889557" xlink:href="#Generator:发电机_0" y="868.3152867370809" zvalue="92"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454779994114" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192454779994114"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1182.39,889.811) scale(1.43307,1.43307) translate(-350.817,-262.401)" width="30" x="1160.894586889557" y="868.3152867370809"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="276">
   <use class="v6300" height="30" transform="rotate(0,839.353,907.589) scale(1.3853,1.3853) translate(-227.672,-246.651)" width="30" x="818.573117758717" xlink:href="#Accessory:带熔断器四卷PT_0" y="886.8095970873403" zvalue="43"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454778552322" ObjectName="#1发电机励磁变PT3"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,839.353,907.589) scale(1.3853,1.3853) translate(-227.672,-246.651)" width="30" x="818.573117758717" y="886.8095970873403"/></g>
  <g id="253">
   <use class="v6300" height="29" transform="rotate(0,784.336,959.912) scale(1.83786,1.05277) translate(-352.962,-47.3509)" width="11" x="774.228002307312" xlink:href="#Accessory:PT带保险_0" y="944.6464848429416" zvalue="45"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454778486786" ObjectName="#1发电机励磁变PT2"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,784.336,959.912) scale(1.83786,1.05277) translate(-352.962,-47.3509)" width="11" x="774.228002307312" y="944.6464848429416"/></g>
  <g id="298">
   <use class="v6300" height="20" transform="rotate(0,621.438,899.14) scale(2.35268,2.35268) translate(-343.771,-503.437)" width="20" x="597.9116303404467" xlink:href="#Accessory:带电显示器_0" y="875.6130158907592" zvalue="58"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454778814466" ObjectName="#1发电机显示器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,621.438,899.14) scale(2.35268,2.35268) translate(-343.771,-503.437)" width="20" x="597.9116303404467" y="875.6130158907592"/></g>
  <g id="313">
   <use class="v6300" height="26" transform="rotate(90,728.5,797.519) scale(-1,-1) translate(-1457,-1595.04)" width="12" x="722.4999999999999" xlink:href="#Accessory:避雷器1_0" y="784.5192307692307" zvalue="68"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454778224642" ObjectName="#1发电机避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,728.5,797.519) scale(-1,-1) translate(-1457,-1595.04)" width="12" x="722.4999999999999" y="784.5192307692307"/></g>
  <g id="63">
   <use class="v6300" height="32" transform="rotate(0,741.7,960.347) scale(2.00453,1.1352) translate(-365.661,-112.209)" width="12" x="729.6724467517565" xlink:href="#Accessory:腊撒线路PT_0" y="942.1839080459768" zvalue="71"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454778683394" ObjectName="#1发电机励磁变PT1"/>
   </metadata>
  <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(0,741.7,960.347) scale(2.00453,1.1352) translate(-365.661,-112.209)" width="12" x="729.6724467517565" y="942.1839080459768"/></g>
  <g id="327">
   <use class="v6300" height="26" transform="rotate(180,569.217,906.734) scale(-1.22559,-1.22559) translate(-1032.31,-1643.63)" width="12" x="561.8632478632475" xlink:href="#Accessory:避雷器1_0" y="890.8012820512821" zvalue="78"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454778748930" ObjectName="#1发电机避雷器2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,569.217,906.734) scale(-1.22559,-1.22559) translate(-1032.31,-1643.63)" width="12" x="561.8632478632475" y="890.8012820512821"/></g>
  <g id="107">
   <use class="v6300" height="30" transform="rotate(0,1336.35,907.589) scale(1.3853,1.3853) translate(-365.904,-246.651)" width="30" x="1315.573117758717" xlink:href="#Accessory:带熔断器四卷PT_0" y="886.8095970873403" zvalue="94"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454779928578" ObjectName="#2发电机励磁变PT3"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1336.35,907.589) scale(1.3853,1.3853) translate(-365.904,-246.651)" width="30" x="1315.573117758717" y="886.8095970873403"/></g>
  <g id="106">
   <use class="v6300" height="29" transform="rotate(0,1281.34,959.912) scale(1.83786,1.05277) translate(-579.538,-47.3509)" width="11" x="1271.228002307312" xlink:href="#Accessory:PT带保险_0" y="944.6464848429416" zvalue="95"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454779863042" ObjectName="#2发电机励磁变PT2"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1281.34,959.912) scale(1.83786,1.05277) translate(-579.538,-47.3509)" width="11" x="1271.228002307312" y="944.6464848429416"/></g>
  <g id="97">
   <use class="v6300" height="20" transform="rotate(0,1118.44,899.14) scale(2.35268,2.35268) translate(-629.523,-503.437)" width="20" x="1094.911630340447" xlink:href="#Accessory:带电显示器_0" y="875.6130158907592" zvalue="107"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454779666434" ObjectName="#2发电机显示器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1118.44,899.14) scale(2.35268,2.35268) translate(-629.523,-503.437)" width="20" x="1094.911630340447" y="875.6130158907592"/></g>
  <g id="95">
   <use class="v6300" height="26" transform="rotate(90,1225.5,797.519) scale(-1,-1) translate(-2451,-1595.04)" width="12" x="1219.5" xlink:href="#Accessory:避雷器1_0" y="784.5192307692307" zvalue="110"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454779535362" ObjectName="#2发电机避雷器3"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1225.5,797.519) scale(-1,-1) translate(-2451,-1595.04)" width="12" x="1219.5" y="784.5192307692307"/></g>
  <g id="93">
   <use class="v6300" height="32" transform="rotate(0,1238.7,960.347) scale(2.00453,1.1352) translate(-614.722,-112.209)" width="12" x="1226.672446751757" xlink:href="#Accessory:腊撒线路PT_0" y="942.1839080459768" zvalue="112"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454779469826" ObjectName="#2发电机励磁变PT1"/>
   </metadata>
  <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(0,1238.7,960.347) scale(2.00453,1.1352) translate(-614.722,-112.209)" width="12" x="1226.672446751757" y="942.1839080459768"/></g>
  <g id="92">
   <use class="v6300" height="26" transform="rotate(180,1066.22,906.734) scale(-1.22559,-1.22559) translate(-1934.82,-1643.63)" width="12" x="1058.863247863248" xlink:href="#Accessory:避雷器1_0" y="890.8012820512821" zvalue="113"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454779404290" ObjectName="#2发电机避雷器4"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,1066.22,906.734) scale(-1.22559,-1.22559) translate(-1934.82,-1643.63)" width="12" x="1058.863247863248" y="890.8012820512821"/></g>
  <g id="113">
   <use class="v6300" height="40" transform="rotate(0,1007.78,545.778) scale(1.375,-1.375) translate(-267.348,-935.207)" width="40" x="980.2777777777778" xlink:href="#Accessory:五卷PT_0" y="518.2777777777778" zvalue="128"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454780059650" ObjectName="6.3kV母线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1007.78,545.778) scale(1.375,-1.375) translate(-267.348,-935.207)" width="40" x="980.2777777777778" y="518.2777777777778"/></g>
  <g id="142">
   <use class="v6300" height="20" transform="rotate(270,839.772,485.14) scale(2.35268,2.35268) translate(-469.303,-265.406)" width="20" x="816.2449636737799" xlink:href="#Accessory:带电显示器_0" y="461.6130158907592" zvalue="171"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454780780546" ObjectName="#1主变显示器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,839.772,485.14) scale(2.35268,2.35268) translate(-469.303,-265.406)" width="20" x="816.2449636737799" y="461.6130158907592"/></g>
  <g id="4">
   <use class="v6300" height="26" transform="rotate(180,1513.22,842.734) scale(-1.22559,-1.22559) translate(-2746.55,-1527.42)" width="12" x="1505.863247863248" xlink:href="#Accessory:避雷器1_0" y="826.8012820512821" zvalue="226"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454919192578" ObjectName="#1厂用变避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,1513.22,842.734) scale(-1.22559,-1.22559) translate(-2746.55,-1527.42)" width="12" x="1505.863247863248" y="826.8012820512821"/></g>
 </g>
 <g id="BreakerClass">
  <g id="249">
   <use class="v6300" height="20" transform="rotate(0,685.284,762.375) scale(1.59229,1.43307) translate(-251.948,-226.055)" width="10" x="677.3229380627334" xlink:href="#Breaker:开关_0" y="748.0439675338483" zvalue="47"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925214797827" ObjectName="#1发电机651断路器"/>
   <cge:TPSR_Ref TObjectID="6473925214797827"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,685.284,762.375) scale(1.59229,1.43307) translate(-251.948,-226.055)" width="10" x="677.3229380627334" y="748.0439675338483"/></g>
  <g id="105">
   <use class="v6300" height="20" transform="rotate(0,1182.28,762.375) scale(1.59229,1.43307) translate(-436.819,-226.055)" width="10" x="1174.322938062733" xlink:href="#Breaker:开关_0" y="748.0439675338483" zvalue="96"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925214863363" ObjectName="#2发电机652断路器"/>
   <cge:TPSR_Ref TObjectID="6473925214863363"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1182.28,762.375) scale(1.59229,1.43307) translate(-436.819,-226.055)" width="10" x="1174.322938062733" y="748.0439675338483"/></g>
  <g id="267">
   <use class="kv110" height="20" transform="rotate(0,725.417,356.972) scale(1.5,1.35) translate(-239.306,-89.0484)" width="10" x="717.9166768524383" xlink:href="#Breaker:开关_0" y="343.4722222222222" zvalue="143"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925214994435" ObjectName="#1主变110kV侧151断路器"/>
   <cge:TPSR_Ref TObjectID="6473925214994435"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,725.417,356.972) scale(1.5,1.35) translate(-239.306,-89.0484)" width="10" x="717.9166768524383" y="343.4722222222222"/></g>
  <g id="282">
   <use class="v6300" height="20" transform="rotate(0,723.421,570.163) scale(1.5,1.35) translate(-238.64,-144.32)" width="10" x="715.9206882336298" xlink:href="#Breaker:开关_0" y="556.6627044677737" zvalue="156"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925214928899" ObjectName="#1主变6.3kV侧601断路器"/>
   <cge:TPSR_Ref TObjectID="6473925214928899"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,723.421,570.163) scale(1.5,1.35) translate(-238.64,-144.32)" width="10" x="715.9206882336298" y="556.6627044677737"/></g>
  <g id="114">
   <use class="v6300" height="20" transform="rotate(0,1380.42,570.163) scale(1.5,1.35) translate(-457.64,-144.32)" width="10" x="1372.92068823363" xlink:href="#Breaker:开关_0" y="556.6627044677737" zvalue="195"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925215059971" ObjectName="6.3kV三合村线654断路器"/>
   <cge:TPSR_Ref TObjectID="6473925215059971"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1380.42,570.163) scale(1.5,1.35) translate(-457.64,-144.32)" width="10" x="1372.92068823363" y="556.6627044677737"/></g>
  <g id="133">
   <use class="v6300" height="20" transform="rotate(0,1696.04,621.163) scale(1.5,1.35) translate(-562.846,-157.542)" width="10" x="1688.537170491473" xlink:href="#Breaker:开关_0" y="607.6627044677737" zvalue="210"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925215125507" ObjectName="#2厂用变6.3kV侧655断路器"/>
   <cge:TPSR_Ref TObjectID="6473925215125507"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1696.04,621.163) scale(1.5,1.35) translate(-562.846,-157.542)" width="10" x="1688.537170491473" y="607.6627044677737"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="246">
   <use class="v6300" height="25" transform="rotate(0,685.166,694.317) scale(0.353843,0.934146) translate(1236.65,48.1236)" width="45" x="677.2049337208473" xlink:href="#Disconnector:特殊刀闸_0" y="682.6402229389662" zvalue="49"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454778421250" ObjectName="#1发电机6511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454778421250"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,685.166,694.317) scale(0.353843,0.934146) translate(1236.65,48.1236)" width="45" x="677.2049337208473" y="682.6402229389662"/></g>
  <g id="242">
   <use class="v6300" height="25" transform="rotate(0,839.322,842.74) scale(0.353843,0.934146) translate(1518.16,58.5869)" width="45" x="831.3604682830065" xlink:href="#Disconnector:特殊刀闸_0" y="831.062988823516" zvalue="51"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454778355714" ObjectName="#1发电机6912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454778355714"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,839.322,842.74) scale(0.353843,0.934146) translate(1518.16,58.5869)" width="45" x="831.3604682830065" y="831.062988823516"/></g>
  <g id="308">
   <use class="v6300" height="25" transform="rotate(0,742.891,843.483) scale(0.353843,0.934146) translate(1342.06,58.6393)" width="45" x="734.9297500955054" xlink:href="#Disconnector:特殊刀闸_0" y="831.8065785671059" zvalue="64"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454778290178" ObjectName="#1发电机6911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454778290178"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,742.891,843.483) scale(0.353843,0.934146) translate(1342.06,58.6393)" width="45" x="734.9297500955054" y="831.8065785671059"/></g>
  <g id="104">
   <use class="v6300" height="25" transform="rotate(0,1182.17,694.317) scale(0.353843,0.934146) translate(2144.23,48.1236)" width="45" x="1174.204933720847" xlink:href="#Disconnector:特殊刀闸_0" y="682.6402229389662" zvalue="98"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454779797506" ObjectName="#2发电机6521隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454779797506"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1182.17,694.317) scale(0.353843,0.934146) translate(2144.23,48.1236)" width="45" x="1174.204933720847" y="682.6402229389662"/></g>
  <g id="103">
   <use class="v6300" height="25" transform="rotate(0,1337.41,843.74) scale(0.353843,0.934146) translate(2427.72,58.6573)" width="45" x="1329.451117616873" xlink:href="#Disconnector:特殊刀闸_0" y="832.062988823516" zvalue="100"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454779731970" ObjectName="#2发电机6922隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454779731970"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1337.41,843.74) scale(0.353843,0.934146) translate(2427.72,58.6573)" width="45" x="1329.451117616873" y="832.062988823516"/></g>
  <g id="96">
   <use class="v6300" height="25" transform="rotate(0,1239.89,843.483) scale(0.353843,0.934146) translate(2249.64,58.6393)" width="45" x="1231.929750095505" xlink:href="#Disconnector:特殊刀闸_0" y="831.8065785671059" zvalue="108"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454779600898" ObjectName="#2发电机6921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454779600898"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1239.89,843.483) scale(0.353843,0.934146) translate(2249.64,58.6393)" width="45" x="1231.929750095505" y="831.8065785671059"/></g>
  <g id="115">
   <use class="v6300" height="25" transform="rotate(0,992.389,623.206) scale(0.353843,0.934146) translate(1797.67,43.1105)" width="45" x="984.4271559430697" xlink:href="#Disconnector:特殊刀闸_0" y="611.5291118278551" zvalue="134"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454780125186" ObjectName="6.3kV母线PT6901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454780125186"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,992.389,623.206) scale(0.353843,0.934146) translate(1797.67,43.1105)" width="45" x="984.4271559430697" y="611.5291118278551"/></g>
  <g id="268">
   <use class="kv110" height="30" transform="rotate(0,725.417,300.972) scale(1.11111,0.814815) translate(-71.7083,65.625)" width="15" x="717.0833435058597" xlink:href="#Disconnector:刀闸_0" y="288.75" zvalue="141"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454780715010" ObjectName="#1主变110kV侧1516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454780715010"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,725.417,300.972) scale(1.11111,0.814815) translate(-71.7083,65.625)" width="15" x="717.0833435058597" y="288.75"/></g>
  <g id="145">
   <use class="v6300" height="25" transform="rotate(0,722.336,625.706) scale(0.353843,0.934146) translate(1304.53,43.2867)" width="45" x="714.3741945399498" xlink:href="#Disconnector:特殊刀闸_0" y="614.0288007893282" zvalue="175"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454780846082" ObjectName="#1主变6.3kV侧6011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454780846082"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,722.336,625.706) scale(0.353843,0.934146) translate(1304.53,43.2867)" width="45" x="714.3741945399498" y="614.0288007893282"/></g>
  <g id="151">
   <use class="v6300" height="25" transform="rotate(0,724.002,513.483) scale(0.353843,0.934146) translate(1307.57,35.3755)" width="45" x="716.0408612066165" xlink:href="#Disconnector:特殊刀闸_0" y="501.806578567106" zvalue="182"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454780911618" ObjectName="#1主变6.3kV侧6016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454780911618"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,724.002,513.483) scale(0.353843,0.934146) translate(1307.57,35.3755)" width="45" x="716.0408612066165" y="501.806578567106"/></g>
  <g id="111">
   <use class="v6300" height="25" transform="rotate(0,1379.34,625.706) scale(0.353843,0.934146) translate(2504.28,43.2867)" width="45" x="1371.37419453995" xlink:href="#Disconnector:特殊刀闸_0" y="614.0288007893282" zvalue="199"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454781370370" ObjectName="6.3kV三合村线6541隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454781370370"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1379.34,625.706) scale(0.353843,0.934146) translate(2504.28,43.2867)" width="45" x="1371.37419453995" y="614.0288007893282"/></g>
  <g id="62">
   <use class="v6300" height="25" transform="rotate(0,1381,513.483) scale(0.353843,0.934146) translate(2507.32,35.3755)" width="45" x="1373.040861206616" xlink:href="#Disconnector:特殊刀闸_0" y="501.806578567106" zvalue="204"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454781304834" ObjectName="6.3kV三合村线6546隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454781304834"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1381,513.483) scale(0.353843,0.934146) translate(2507.32,35.3755)" width="45" x="1373.040861206616" y="501.806578567106"/></g>
  <g id="1">
   <use class="v6300" height="25" transform="rotate(0,1564.34,737.706) scale(0.353843,0.934146) translate(2842.11,51.1823)" width="45" x="1556.37419453995" xlink:href="#Disconnector:特殊刀闸_0" y="726.0288007893282" zvalue="222"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454918995970" ObjectName="#1厂用变6531隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454918995970"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1564.34,737.706) scale(0.353843,0.934146) translate(2842.11,51.1823)" width="45" x="1556.37419453995" y="726.0288007893282"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="58">
   <path class="v6300" d="M 685.23 748.66 L 685.2 705.57" stroke-width="1" zvalue="53"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="249@0" LinkObjectIDznd="246@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 685.23 748.66 L 685.2 705.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="v6300" d="M 685.39 776.06 L 685.39 868.67" stroke-width="1" zvalue="54"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="249@1" LinkObjectIDznd="850@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 685.39 776.06 L 685.39 868.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="v6300" d="M 839.35 888.19 L 839.35 853.99" stroke-width="1" zvalue="55"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="276@0" LinkObjectIDznd="242@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 839.35 888.19 L 839.35 853.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="55">
   <path class="v6300" d="M 839.34 831.76 L 839.35 812.11 L 685.39 812.11" stroke-width="1" zvalue="56"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="242@0" LinkObjectIDznd="57" MaxPinNum="2"/>
   </metadata>
  <path d="M 839.34 831.76 L 839.35 812.11 L 685.39 812.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="54">
   <path class="v6300" d="M 685.2 683.03 L 685.2 660" stroke-width="1" zvalue="57"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="246@0" LinkObjectIDznd="38@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 685.2 683.03 L 685.2 660" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="v6300" d="M 716.13 797.49 L 685.39 797.49" stroke-width="1" zvalue="69"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="313@0" LinkObjectIDznd="57" MaxPinNum="2"/>
   </metadata>
  <path d="M 716.13 797.49 L 685.39 797.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="67">
   <path class="v6300" d="M 742.92 854.73 L 742.92 942.94" stroke-width="1" zvalue="84"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="308@1" LinkObjectIDznd="63@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 742.92 854.73 L 742.92 942.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="v6300" d="M 742.92 832.2 L 742.92 812.11" stroke-width="1" zvalue="85"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="308@0" LinkObjectIDznd="55" MaxPinNum="2"/>
   </metadata>
  <path d="M 742.92 832.2 L 742.92 812.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="v6300" d="M 765.72 889.86 L 742.92 889.86" stroke-width="1" zvalue="86"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="362@0" LinkObjectIDznd="67" MaxPinNum="2"/>
   </metadata>
  <path d="M 765.72 889.86 L 742.92 889.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="v6300" d="M 784.34 944.82 L 784.34 924.89 L 742.92 924.89" stroke-width="1" zvalue="87"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="253@0" LinkObjectIDznd="67" MaxPinNum="2"/>
   </metadata>
  <path d="M 784.34 944.82 L 784.34 924.89 L 742.92 924.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="v6300" d="M 569.26 891.58 L 569.26 827.11 L 685.39 827.11" stroke-width="1" zvalue="88"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="327@0" LinkObjectIDznd="57" MaxPinNum="2"/>
   </metadata>
  <path d="M 569.26 891.58 L 569.26 827.11 L 685.39 827.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="v6300" d="M 621.44 877.97 L 621.44 827.11" stroke-width="1" zvalue="89"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="298@0" LinkObjectIDznd="71" MaxPinNum="2"/>
   </metadata>
  <path d="M 621.44 877.97 L 621.44 827.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="v6300" d="M 871.83 873.18 L 839.35 873.18" stroke-width="1" zvalue="90"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="66@0" LinkObjectIDznd="56" MaxPinNum="2"/>
   </metadata>
  <path d="M 871.83 873.18 L 839.35 873.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="102">
   <path class="v6300" d="M 1182.23 748.66 L 1182.2 705.57" stroke-width="1" zvalue="102"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="105@0" LinkObjectIDznd="104@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1182.23 748.66 L 1182.2 705.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="v6300" d="M 1182.39 776.06 L 1182.39 868.67" stroke-width="1" zvalue="103"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="105@1" LinkObjectIDznd="108@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1182.39 776.06 L 1182.39 868.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="100">
   <path class="v6300" d="M 1336.35 888.19 L 1336.35 854.99" stroke-width="1" zvalue="104"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@0" LinkObjectIDznd="103@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1336.35 888.19 L 1336.35 854.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="99">
   <path class="v6300" d="M 1337.43 832.76 L 1337.44 812.11 L 1182.39 812.11" stroke-width="1" zvalue="105"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="103@0" LinkObjectIDznd="101" MaxPinNum="2"/>
   </metadata>
  <path d="M 1337.43 832.76 L 1337.44 812.11 L 1182.39 812.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="v6300" d="M 1182.2 683.03 L 1182.2 660" stroke-width="1" zvalue="106"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="104@0" LinkObjectIDznd="38@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1182.2 683.03 L 1182.2 660" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="94">
   <path class="v6300" d="M 1213.13 797.49 L 1182.39 797.49" stroke-width="1" zvalue="111"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="95@0" LinkObjectIDznd="101" MaxPinNum="2"/>
   </metadata>
  <path d="M 1213.13 797.49 L 1182.39 797.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="v6300" d="M 1239.92 854.73 L 1239.92 942.94" stroke-width="1" zvalue="118"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="96@1" LinkObjectIDznd="93@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1239.92 854.73 L 1239.92 942.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="88">
   <path class="v6300" d="M 1239.92 832.2 L 1239.92 812.11" stroke-width="1" zvalue="119"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="96@0" LinkObjectIDznd="99" MaxPinNum="2"/>
   </metadata>
  <path d="M 1239.92 832.2 L 1239.92 812.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="87">
   <path class="v6300" d="M 1262.72 889.86 L 1239.92 889.86" stroke-width="1" zvalue="120"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="91@0" LinkObjectIDznd="89" MaxPinNum="2"/>
   </metadata>
  <path d="M 1262.72 889.86 L 1239.92 889.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="86">
   <path class="v6300" d="M 1281.34 944.82 L 1281.34 924.89 L 1239.92 924.89" stroke-width="1" zvalue="121"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="106@0" LinkObjectIDznd="89" MaxPinNum="2"/>
   </metadata>
  <path d="M 1281.34 944.82 L 1281.34 924.89 L 1239.92 924.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="v6300" d="M 1066.26 891.58 L 1066.26 827.11 L 1182.39 827.11" stroke-width="1" zvalue="122"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92@0" LinkObjectIDznd="101" MaxPinNum="2"/>
   </metadata>
  <path d="M 1066.26 891.58 L 1066.26 827.11 L 1182.39 827.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="84">
   <path class="v6300" d="M 1118.44 877.97 L 1118.44 827.11" stroke-width="1" zvalue="123"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="97@0" LinkObjectIDznd="85" MaxPinNum="2"/>
   </metadata>
  <path d="M 1118.44 877.97 L 1118.44 827.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="v6300" d="M 1368.83 873.18 L 1336.35 873.18" stroke-width="1" zvalue="124"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="90@0" LinkObjectIDznd="100" MaxPinNum="2"/>
   </metadata>
  <path d="M 1368.83 873.18 L 1336.35 873.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="116">
   <path class="v6300" d="M 994.03 571.9 L 994.03 611.92" stroke-width="1" zvalue="135"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="113@0" LinkObjectIDznd="115@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 994.03 571.9 L 994.03 611.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="v6300" d="M 992.42 634.45 L 992.42 660" stroke-width="1" zvalue="136"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="115@1" LinkObjectIDznd="38@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 992.42 634.45 L 992.42 660" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="119">
   <path class="v6300" d="M 1023.72 593.29 L 994.03 593.29" stroke-width="1" zvalue="139"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="118@0" LinkObjectIDznd="116" MaxPinNum="2"/>
   </metadata>
  <path d="M 1023.72 593.29 L 994.03 593.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="141">
   <path class="kv110" d="M 725.48 312.99 L 725.48 344.06" stroke-width="1" zvalue="145"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="268@1" LinkObjectIDznd="267@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 725.48 312.99 L 725.48 344.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="140">
   <path class="kv110" d="M 725.51 289.15 L 725.51 212.05" stroke-width="1" zvalue="148"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="268@0" LinkObjectIDznd="42@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 725.51 289.15 L 725.51 212.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="139">
   <path class="kv110" d="M 725.52 369.86 L 725.52 385.59" stroke-width="1" zvalue="151"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="267@1" LinkObjectIDznd="263@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 725.52 369.86 L 725.52 385.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="137">
   <path class="kv110" d="M 724.05 411.87 L 633.78 411.87 L 633.78 435.9" stroke-width="1" zvalue="153"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="263@2" LinkObjectIDznd="259@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 724.05 411.87 L 633.78 411.87 L 633.78 435.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="143">
   <path class="v6300" d="M 818.6 485.14 L 724.02 485.14" stroke-width="1" zvalue="172"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="142@0" LinkObjectIDznd="152" MaxPinNum="2"/>
   </metadata>
  <path d="M 818.6 485.14 L 724.02 485.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="146">
   <path class="v6300" d="M 722.37 660 L 722.37 636.95" stroke-width="1" zvalue="176"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@3" LinkObjectIDznd="145@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 722.37 660 L 722.37 636.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="147">
   <path class="v6300" d="M 722.37 614.42 L 722.37 583.06" stroke-width="1" zvalue="177"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@0" LinkObjectIDznd="282@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 722.37 614.42 L 722.37 583.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="v6300" d="M 805.28 533.14 L 724.03 533.14" stroke-width="1" zvalue="179"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="275@0" LinkObjectIDznd="153" MaxPinNum="2"/>
   </metadata>
  <path d="M 805.28 533.14 L 724.03 533.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="152">
   <path class="v6300" d="M 724.02 476.77 L 724.02 502.51" stroke-width="1" zvalue="183"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="263@1" LinkObjectIDznd="151@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 724.02 476.77 L 724.02 502.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="153">
   <path class="v6300" d="M 724.03 524.73 L 724.03 557.25" stroke-width="1" zvalue="184"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="151@1" LinkObjectIDznd="282@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 724.03 524.73 L 724.03 557.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="154">
   <path class="kv110" d="M 674.92 328.56 L 725.48 328.56" stroke-width="1" zvalue="185"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="265@0" LinkObjectIDznd="141" MaxPinNum="2"/>
   </metadata>
  <path d="M 674.92 328.56 L 725.48 328.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="157">
   <path class="kv110" d="M 671.03 280.78 L 725.51 280.78" stroke-width="1" zvalue="189"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="155@0" LinkObjectIDznd="140" MaxPinNum="2"/>
   </metadata>
  <path d="M 671.03 280.78 L 725.51 280.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="v6300" d="M 1379.37 660 L 1379.37 636.95" stroke-width="1" zvalue="201"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@4" LinkObjectIDznd="111@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1379.37 660 L 1379.37 636.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="v6300" d="M 1379.35 614.73 L 1379.35 583.06" stroke-width="1" zvalue="202"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="111@0" LinkObjectIDznd="114@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1379.35 614.73 L 1379.35 583.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="138">
   <path class="v6300" d="M 1696.14 634.06 L 1696.14 660" stroke-width="1" zvalue="216"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="133@1" LinkObjectIDznd="38@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1696.14 634.06 L 1696.14 660" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="144">
   <path class="v6300" d="M 1696.04 582.15 L 1695.99 608.25" stroke-width="1" zvalue="217"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="134@1" LinkObjectIDznd="133@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1696.04 582.15 L 1695.99 608.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="148">
   <path class="v400" d="M 1696.04 483.4 L 1696.07 543.03" stroke-width="1" zvalue="218"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="136@0" LinkObjectIDznd="134@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1696.04 483.4 L 1696.07 543.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="135">
   <path class="v6300" d="M 1564.37 726.42 L 1564.37 660" stroke-width="1" zvalue="228"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1@0" LinkObjectIDznd="38@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1564.37 726.42 L 1564.37 660" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="160">
   <path class="v6300" d="M 1564.37 830.32 L 1564.37 748.95" stroke-width="1" zvalue="229"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="16@0" LinkObjectIDznd="1@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1564.37 830.32 L 1564.37 748.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="161">
   <path class="v6300" d="M 1590.28 776.14 L 1564.37 776.14" stroke-width="1" zvalue="230"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="2@0" LinkObjectIDznd="160" MaxPinNum="2"/>
   </metadata>
  <path d="M 1590.28 776.14 L 1564.37 776.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="162">
   <path class="v6300" d="M 1513.26 827.58 L 1513.26 801 L 1564.37 801" stroke-width="1" zvalue="231"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="4@0" LinkObjectIDznd="160" MaxPinNum="2"/>
   </metadata>
  <path d="M 1513.26 827.58 L 1513.26 801 L 1564.37 801" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="168">
   <path class="v6300" d="M 1380.37 557.25 L 1380.37 524.65" stroke-width="1" zvalue="235"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="62@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1380.37 557.25 L 1380.37 524.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="169">
   <path class="v6300" d="M 1381.02 502.51 L 1381.02 485.03" stroke-width="1" zvalue="236"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="62@0" LinkObjectIDznd="123@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1381.02 502.51 L 1381.02 485.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="170">
   <path class="v6300" d="M 1429.28 534.14 L 1380.37 534.14" stroke-width="1" zvalue="237"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@0" LinkObjectIDznd="168" MaxPinNum="2"/>
   </metadata>
  <path d="M 1429.28 534.14 L 1380.37 534.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="362">
   <use class="v6300" height="20" transform="rotate(270,776.556,889.806) scale(-1.11111,1.11111) translate(-1474.9,-87.8694)" width="10" x="771" xlink:href="#GroundDisconnector:地刀_0" y="878.6944495307074" zvalue="81"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454778945538" ObjectName="#1发电机69117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454778945538"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,776.556,889.806) scale(-1.11111,1.11111) translate(-1474.9,-87.8694)" width="10" x="771" y="878.6944495307074"/></g>
  <g id="66">
   <use class="v6300" height="20" transform="rotate(270,882.667,873.139) scale(-0.850017,1.11111) translate(-1921.83,-86.2028)" width="10" x="878.4165818959268" xlink:href="#GroundDisconnector:地刀_0" y="862.027822706279" zvalue="83"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454779076610" ObjectName="#1发电机69127接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454779076610"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,882.667,873.139) scale(-0.850017,1.11111) translate(-1921.83,-86.2028)" width="10" x="878.4165818959268" y="862.027822706279"/></g>
  <g id="91">
   <use class="v6300" height="20" transform="rotate(270,1273.56,889.806) scale(-1.11111,1.11111) translate(-2419.2,-87.8694)" width="10" x="1268" xlink:href="#GroundDisconnector:地刀_0" y="878.6944495307074" zvalue="114"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454779338754" ObjectName="#2发电机69217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454779338754"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1273.56,889.806) scale(-1.11111,1.11111) translate(-2419.2,-87.8694)" width="10" x="1268" y="878.6944495307074"/></g>
  <g id="90">
   <use class="v6300" height="20" transform="rotate(270,1379.67,873.139) scale(-0.850017,1.11111) translate(-3003.52,-86.2028)" width="10" x="1375.416581895927" xlink:href="#GroundDisconnector:地刀_0" y="862.027822706279" zvalue="116"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454779207682" ObjectName="#2发电机69227接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454779207682"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1379.67,873.139) scale(-0.850017,1.11111) translate(-3003.52,-86.2028)" width="10" x="1375.416581895927" y="862.027822706279"/></g>
  <g id="118">
   <use class="v6300" height="20" transform="rotate(270,1034.56,593.25) scale(-0.850017,1.11111) translate(-2252.41,-58.2139)" width="10" x="1030.305470784816" xlink:href="#GroundDisconnector:地刀_0" y="582.1389338173904" zvalue="138"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454780256258" ObjectName="6.3kV母线PT69017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454780256258"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1034.56,593.25) scale(-0.850017,1.11111) translate(-2252.41,-58.2139)" width="10" x="1030.305470784816" y="582.1389338173904"/></g>
  <g id="265">
   <use class="kv110" height="20" transform="rotate(90,664.083,328.528) scale(0.550017,1.11111) translate(541.053,-31.7417)" width="10" x="661.3332578870986" xlink:href="#GroundDisconnector:地刀_0" y="317.4167308807373" zvalue="146"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454780649474" ObjectName="#1主变110kV侧15160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454780649474"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,664.083,328.528) scale(0.550017,1.11111) translate(541.053,-31.7417)" width="10" x="661.3332578870986" y="317.4167308807373"/></g>
  <g id="259">
   <use class="kv110" height="40" transform="rotate(0,631.142,451.386) scale(1.01543,-1.26928) translate(-9.27991,-801.622)" width="40" x="610.8333333333335" xlink:href="#GroundDisconnector:主变中性点接地刀_0" y="426" zvalue="154"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454780518402" ObjectName="#1主变110kV侧1010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454780518402"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,631.142,451.386) scale(1.01543,-1.26928) translate(-9.27991,-801.622)" width="40" x="610.8333333333335" y="426"/></g>
  <g id="275">
   <use class="v6300" height="20" transform="rotate(270,817.469,533.111) scale(-0.555556,1.25) translate(-2291.14,-104.122)" width="10" x="814.6914682539684" xlink:href="#GroundDisconnector:地刀_0" y="520.6111111111111" zvalue="164"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454780387330" ObjectName="#1主变6.3kV侧60167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454780387330"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,817.469,533.111) scale(-0.555556,1.25) translate(-2291.14,-104.122)" width="10" x="814.6914682539684" y="520.6111111111111"/></g>
  <g id="155">
   <use class="kv110" height="20" transform="rotate(90,660.194,280.75) scale(0.550017,1.11111) translate(537.872,-26.9639)" width="10" x="657.4443689982097" xlink:href="#GroundDisconnector:地刀_0" y="269.6389531029595" zvalue="187"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454781042690" ObjectName="#1主变110kV侧15167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454781042690"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,660.194,280.75) scale(0.550017,1.11111) translate(537.872,-26.9639)" width="10" x="657.4443689982097" y="269.6389531029595"/></g>
  <g id="112">
   <use class="v6300" height="20" transform="rotate(270,1441.47,534.111) scale(-0.555556,1.25) translate(-4038.34,-104.322)" width="10" x="1438.691468253969" xlink:href="#GroundDisconnector:地刀_0" y="521.6111111111111" zvalue="197"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454781501442" ObjectName="6.3kV三合村线65467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454781501442"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1441.47,534.111) scale(-0.555556,1.25) translate(-4038.34,-104.322)" width="10" x="1438.691468253969" y="521.6111111111111"/></g>
  <g id="2">
   <use class="v6300" height="20" transform="rotate(270,1602.47,776.111) scale(-0.555556,1.25) translate(-4489.14,-152.722)" width="10" x="1599.691468253968" xlink:href="#GroundDisconnector:地刀_0" y="763.6111111111111" zvalue="224"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454919127042" ObjectName="#1厂用变65317接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454919127042"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1602.47,776.111) scale(-0.555556,1.25) translate(-4489.14,-152.722)" width="10" x="1599.691468253968" y="763.6111111111111"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="263">
   <g id="2630">
    <use class="kv110" height="50" transform="rotate(0,724.02,431.089) scale(1.95556,1.85355) translate(-339.449,-177.175)" width="30" x="694.6900000000001" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="384.75" zvalue="149"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874591698946" ObjectName="110"/>
    </metadata>
   </g>
   <g id="2631">
    <use class="v6300" height="50" transform="rotate(0,724.02,431.089) scale(1.95556,1.85355) translate(-339.449,-177.175)" width="30" x="694.6900000000001" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="384.75" zvalue="149"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874597859330" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399533133826" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399533133826"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,724.02,431.089) scale(1.95556,1.85355) translate(-339.449,-177.175)" width="30" x="694.6900000000001" y="384.75"/></g>
  <g id="134">
   <g id="1340">
    <use class="v400" height="50" transform="rotate(0,1696.04,562.551) scale(0.922222,0.795062) translate(141.873,139.882)" width="30" x="1682.2" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="542.67" zvalue="212"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874597924866" ObjectName="0.4"/>
    </metadata>
   </g>
   <g id="1341">
    <use class="v6300" height="50" transform="rotate(0,1696.04,562.551) scale(0.922222,0.795062) translate(141.873,139.882)" width="30" x="1682.2" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="542.67" zvalue="212"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874591895554" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399533199362" ObjectName="厂用变"/>
   <cge:TPSR_Ref TObjectID="6755399533199362"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1696.04,562.551) scale(0.922222,0.795062) translate(141.873,139.882)" width="30" x="1682.2" y="542.67"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="136">
   <use class="v400" height="30" transform="rotate(0,1696.04,468.525) scale(0.166667,1.10168) translate(8475.19,-41.7159)" width="12" x="1695.037170410156" xlink:href="#EnergyConsumer:负荷_0" y="452" zvalue="215"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454781632514" ObjectName="#2厂用变"/>
   <cge:TPSR_Ref TObjectID="6192454781632514"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1696.04,468.525) scale(0.166667,1.10168) translate(8475.19,-41.7159)" width="12" x="1695.037170410156" y="452"/></g>
  <g id="16">
   <use class="v6300" height="30" transform="rotate(0,1564.24,850) scale(1.35714,1.36667) translate(-406.643,-222.549)" width="28" x="1545.241604378202" xlink:href="#EnergyConsumer:站用变DY接地_0" y="829.5" zvalue="227"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454919258114" ObjectName="#1厂用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1564.24,850) scale(1.35714,1.36667) translate(-406.643,-222.549)" width="28" x="1545.241604378202" y="829.5"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="244">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="244" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,156.611,269.917) scale(1,1) translate(0,0)" writing-mode="lr" x="156.77" xml:space="preserve" y="274.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136397221890" ObjectName="F"/>
   </metadata>
  </g>
  <g id="245">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="245" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,334.222,270.917) scale(1,1) translate(0,0)" writing-mode="lr" x="334.38" xml:space="preserve" y="275.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136397287426" ObjectName="F"/>
   </metadata>
  </g>
  <g id="256">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="256" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,156.611,294.917) scale(1,1) translate(0,0)" writing-mode="lr" x="156.77" xml:space="preserve" y="299.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136397090818" ObjectName="F"/>
   </metadata>
  </g>
  <g id="257">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="257" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,334.222,295.917) scale(1,1) translate(0,0)" writing-mode="lr" x="334.38" xml:space="preserve" y="300.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136397156354" ObjectName="F"/>
   </metadata>
  </g>
  <g id="234">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="234" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,156.611,318.917) scale(1,1) translate(0,0)" writing-mode="lr" x="156.77" xml:space="preserve" y="323.83" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="装机率用率"/>
   </metadata>
  </g>
  <g id="240">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="240" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,336.611,317.917) scale(1,1) translate(0,0)" writing-mode="lr" x="336.77" xml:space="preserve" y="322.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127180304391" ObjectName="厂用电率"/>
   </metadata>
  </g>
  <g id="132">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="132" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,156.611,385.667) scale(1,1) translate(0,0)" writing-mode="lr" x="156.77" xml:space="preserve" y="390.58" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="64">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="64" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,156.611,365.667) scale(1,1) translate(0,0)" writing-mode="lr" x="156.77" xml:space="preserve" y="370.58" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="61">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="61" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,332.611,385.667) scale(1,1) translate(0,0)" writing-mode="lr" x="332.77" xml:space="preserve" y="390.58" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="30">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="30" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,332.611,409.667) scale(1,1) translate(0,2.65898e-13)" writing-mode="lr" x="332.77" xml:space="preserve" y="414.58" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="3">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="3" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,685.391,963.807) scale(1,1) translate(0,0)" writing-mode="lr" x="684.84" xml:space="preserve" y="970.09" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136354099202" ObjectName="P"/>
   </metadata>
  </g>
  <g id="5">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="5" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,685.391,1000.81) scale(1,1) translate(0,0)" writing-mode="lr" x="684.84" xml:space="preserve" y="1007.09" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136354164738" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="6">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="6" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,685.391,1037.81) scale(1,1) translate(0,-1.58588e-12)" writing-mode="lr" x="684.84" xml:space="preserve" y="1044.09" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136354230274" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="7" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1179.39,962.807) scale(1,1) translate(-2.50109e-13,0)" writing-mode="lr" x="1178.84" xml:space="preserve" y="969.09" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136355278850" ObjectName="P"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="8" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1179.39,999.807) scale(1,1) translate(-2.50109e-13,0)" writing-mode="lr" x="1178.84" xml:space="preserve" y="1006.09" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136355344386" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="9">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="9" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1179.39,1036.81) scale(1,1) translate(-2.50109e-13,-1.58432e-12)" writing-mode="lr" x="1178.84" xml:space="preserve" y="1043.09" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136355409922" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="177">
   <use height="30" transform="rotate(0,331.673,196.107) scale(0.708333,0.665547) translate(132.196,93.5318)" width="30" x="321.05" xlink:href="#State:红绿圆(方形)_0" y="186.12" zvalue="254"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374930214913" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,331.673,196.107) scale(0.708333,0.665547) translate(132.196,93.5318)" width="30" x="321.05" y="186.12"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,236.048,196.107) scale(0.708333,0.665547) translate(92.8211,93.5318)" width="30" x="225.42" xlink:href="#State:红绿圆(方形)_0" y="186.12" zvalue="255"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562962233819137" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,236.048,196.107) scale(0.708333,0.665547) translate(92.8211,93.5318)" width="30" x="225.42" y="186.12"/></g>
  <g id="11">
   <use height="30" transform="rotate(0,321.812,122.464) scale(1.22222,1.03092) translate(-48.5114,-3.20939)" width="90" x="266.81" xlink:href="#State:全站检修_0" y="107" zvalue="280"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549684273153" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,321.812,122.464) scale(1.22222,1.03092) translate(-48.5114,-3.20939)" width="90" x="266.81" y="107"/></g>
 </g>
</svg>