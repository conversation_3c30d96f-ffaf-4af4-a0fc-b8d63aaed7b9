<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1100" id="thSvg" viewBox="0 0 2100 1100" width="2100">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="Breaker:0_0" viewBox="0,0,50,50">
 <use Plane="0" x="14" xlink:href="#terminal" y="7"/>
 <use Plane="0" x="14" xlink:href="#terminal" y="38"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="31" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,14,22)" width="16" x="6" y="7"/>
</symbol>
<symbol id="Breaker:0_1" viewBox="0,0,50,50">
 <use Plane="0" x="14" xlink:href="#terminal" y="7"/>
 <use Plane="0" x="14" xlink:href="#terminal" y="38"/>
 <rect AFMask="2147483647" Plane="0" fill="rgb(0,0,255)" height="31" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,15,22)" width="16" x="7" y="7"/>
</symbol>
<symbol id="Fuse:11111_0" viewBox="0,0,100,100">
 <use Plane="0" x="32" xlink:href="#terminal" y="56"/>
 <use Plane="0" x="35" xlink:href="#terminal" y="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="48" x2="33" y1="16" y2="54"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="30" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(22,41,35)" width="14" x="34" y="20"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="24" x2="48" y1="14" y2="14"/>
</symbol>
<symbol id="Arrester:bn_避雷器11_0" viewBox="0,0,40,20">
 <use Plane="0" x="36" xlink:href="#terminal" y="10"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="10" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,20,10)" width="22" x="9" y="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="29" x2="23" y1="10" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="5" x2="5" y1="6" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="3" x2="3" y1="7" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="22" x2="22" y1="8" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="6" x2="9" y1="10" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="29" x2="35" y1="10" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="22" x2="19" y1="8" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="19" x2="22" y1="10" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1" x2="1" y1="8" y2="12"/>
</symbol>
<symbol id="Status:bn_不变化颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(0,0,255)" height="1" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_不变化颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(85,0,255)" height="1" stroke="rgb(85,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Disconnector:bn_刀闸3_0" viewBox="0,0,20,40">
 <use Plane="0" x="7" xlink:href="#terminal" y="3"/>
 <use Plane="0" x="7" xlink:href="#terminal" y="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="35" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="7" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="1" x2="15" y1="31" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="18" y1="7" y2="31"/>
</symbol>
<symbol id="Disconnector:bn_刀闸3_1" viewBox="0,0,20,40">
 <use Plane="0" x="7" xlink:href="#terminal" y="3"/>
 <use Plane="0" x="7" xlink:href="#terminal" y="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="35" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="1" x2="15" y1="31" y2="31"/>
</symbol>
<symbol id="Disconnector:bn_刀闸5_0" viewBox="0,0,20,40">
 <use Plane="0" x="7" xlink:href="#terminal" y="4"/>
 <use Plane="0" x="7" xlink:href="#terminal" y="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="35" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="9" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="1" x2="15" y1="10" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="18" x2="8" y1="10" y2="30"/>
</symbol>
<symbol id="Disconnector:bn_刀闸5_1" viewBox="0,0,20,40">
 <use Plane="0" x="7" xlink:href="#terminal" y="4"/>
 <use Plane="0" x="7" xlink:href="#terminal" y="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="35" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="1" x2="15" y1="10" y2="10"/>
</symbol>
<symbol id="PT:bn_电压互感器_0" viewBox="0,0,60,60">
 <use Plane="0" x="30" xlink:href="#terminal" y="5"/>
 <circle AFMask="2147483647" Plane="0" cx="35" cy="35" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="35" cy="25" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="25" cy="25" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="26" x2="26" y1="32" y2="36"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="23" x2="26" y1="39" y2="36"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="26" x2="29" y1="36" y2="39"/>
 <circle AFMask="2147483647" Plane="0" cx="25" cy="35" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="26" x2="29" y1="25" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="26" x2="26" y1="21" y2="25"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="23" x2="26" y1="28" y2="25"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="33" x2="36" y1="28" y2="25"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="36" x2="36" y1="21" y2="25"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="36" x2="39" y1="25" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="7" x2="13" y1="47" y2="47"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="8" x2="12" y1="49" y2="49"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="11" y1="51" y2="51"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="11" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,10,38)" width="6" x="7" y="33"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="5" x2="15" y1="34" y2="44"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="0" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,28,11)" width="0" x="28" y="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="44" y2="47"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="32" x2="39" y1="37" y2="37"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="32" x2="34" y1="37" y2="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="39" x2="37" y1="37" y2="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="30" x2="30" y1="20" y2="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="19" x2="10" y1="25" y2="25"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="25" y2="32"/>
</symbol>
<symbol id="PT:bn_电压互感器004_0" viewBox="0,0,100,100">
 <use Plane="0" x="33" xlink:href="#terminal" y="97"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="28" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,75,51)" width="12" x="69" y="37"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="21" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,33,67)" width="10" x="28" y="57"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="33" y1="17" y2="24"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="28" y1="24" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="38" y1="24" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="38" y1="40" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="28" y1="40" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="33" y1="33" y2="40"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="49" x2="49" y1="33" y2="40"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="49" x2="44" y1="40" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="49" x2="54" y1="40" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="52" x2="52" y1="17" y2="30"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="52" x2="45" y1="30" y2="27"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="52" x2="45" y1="17" y2="21"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="5" x2="15" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="7" x2="13" y1="10" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="11" y1="8" y2="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="10" x2="10" y1="40" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="10" x2="22" y1="40" y2="40"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="70" x2="80" y1="32" y2="32"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="72" x2="78" y1="30" y2="30"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="74" x2="76" y1="28" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="75" x2="75" y1="32" y2="37"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="75" x2="75" y1="65" y2="62"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="33" y1="51" y2="97"/>
 <path AFMask="2147483647" Plane="0" d="M 72 61 L 75 44 L 78 61 Z" fill="rgb(0,0,255)" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="33" cy="24" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="33" cy="40" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="49" cy="40" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="49" cy="24" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="0" d="M 75 66 L 75 84 L 33 84 " fill="none" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="PT:bn_电压互感器46_0" viewBox="0,0,18,44">
 <use Plane="0" x="9" xlink:href="#terminal" y="40"/>
 <circle AFMask="2147483647" Plane="0" cx="9" cy="21" fill="none" r="8" stroke="rgb(255,255,254)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="9" cy="10" fill="none" r="8" stroke="rgb(255,255,254)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="9" y1="29" y2="39"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="7" x2="13" y1="10" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="7" x2="13" y1="21" y2="21"/>
</symbol>
<symbol id="Breaker:bn_断路器3_0" viewBox="0,0,38,18">
 <use Plane="0" x="3" xlink:href="#terminal" y="7"/>
 <use Plane="0" x="33" xlink:href="#terminal" y="7"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="15" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,18,8)" width="30" x="3" y="1"/>
</symbol>
<symbol id="Breaker:bn_断路器3_1" viewBox="0,0,38,18">
 <use Plane="0" x="3" xlink:href="#terminal" y="7"/>
 <use Plane="0" x="33" xlink:href="#terminal" y="7"/>
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,254)" height="15" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,18,8)" width="30" x="3" y="1"/>
</symbol>
<symbol id="Status:bn_非实测颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,85,0)" height="1" stroke="rgb(170,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_非实测颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,85,0)" height="1" stroke="rgb(170,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_工况退出颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,170,255)" height="1" stroke="rgb(170,170,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_工况退出颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,170,255)" height="1" stroke="rgb(170,170,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸111_0" viewBox="0,0,34,16">
 <use Plane="0" x="31" xlink:href="#terminal" y="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="27" x2="27" y1="3" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="16" x2="6" y1="8" y2="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="3" y1="4" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="1" x2="1" y1="6" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="30" x2="27" y1="8" y2="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="5" x2="5" y1="3" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="16" x2="25" y1="8" y2="2"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸111_1" viewBox="0,0,34,16">
 <use Plane="0" x="31" xlink:href="#terminal" y="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="27" x2="27" y1="3" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="30" x2="6" y1="8" y2="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="3" y1="4" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="1" x2="1" y1="6" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="5" x2="5" y1="3" y2="13"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸12_0" viewBox="0,0,40,20">
 <use Plane="0" x="4" xlink:href="#terminal" y="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="31" y1="6" y2="18"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="28" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="33" y1="8" y2="16"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="35" x2="35" y1="10" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="28" y1="2" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="4" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="9" y1="5" y2="19"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸12_1" viewBox="0,0,40,20">
 <use Plane="0" x="4" xlink:href="#terminal" y="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="31" y1="6" y2="18"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="3" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="33" y1="8" y2="16"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="35" x2="35" y1="10" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="9" y1="5" y2="19"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸13_0" viewBox="0,0,40,20">
 <use Plane="0" x="5" xlink:href="#terminal" y="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="31" y1="6" y2="18"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="30" x2="27" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="33" y1="8" y2="16"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="35" x2="35" y1="10" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="27" y1="2" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="5" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="9" y1="5" y2="19"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸13_1" viewBox="0,0,40,20">
 <use Plane="0" x="5" xlink:href="#terminal" y="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="31" y1="6" y2="18"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="5" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="33" y1="8" y2="16"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="35" x2="35" y1="10" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="9" y1="5" y2="19"/>
</symbol>
<symbol id="Transformer2:bn_两卷变压器10_0" viewBox="0,0,50,76">
 <use Plane="0" x="26" xlink:href="#terminal" y="3"/>
 <circle AFMask="2147483647" Plane="0" cx="25" cy="21" fill="none" r="18" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="25" x2="33" y1="21" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="26" x2="18" y1="21" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="25" x2="25" y1="21" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="4" x2="45" y1="44" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="45" x2="45" y1="3" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="45" x2="43" y1="3" y2="3"/>
</symbol>
<symbol id="Transformer2:bn_两卷变压器10_1" viewBox="0,0,50,76">
 <use Plane="1" x="26" xlink:href="#terminal" y="72"/>
 <circle AFMask="2147483647" Plane="1" cx="25" cy="53" fill="none" r="18" stroke="rgb(0,0,255)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 25 47 L 16 60 L 35 60 Z" fill="none" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Transformer2:bn_两卷变压器10_2" viewBox="0,0,50,76">
 <use Plane="2" x="25" xlink:href="#terminal" y="21"/>
</symbol>
<symbol id="Fuse:bn_熔断器3_0" viewBox="0,0,32,16">
 <use Plane="0" x="4" xlink:href="#terminal" y="12"/>
 <use Plane="0" x="26" xlink:href="#terminal" y="12"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="5" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(23,16,7)" width="18" x="7" y="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="5" x2="27" y1="3" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="7" x2="7" y1="9" y2="15"/>
</symbol>
<symbol id="Status:bn_数据封锁颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,85,0)" height="1" stroke="rgb(255,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_数据封锁颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,85,0)" height="1" stroke="rgb(255,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_0_0" viewBox="0,0,20,74">
 <rect AFMask="2147483647" Plane="0" fill="none" height="0" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,4,16)" width="0" x="4" y="16"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="30" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,9,37)" width="13" x="3" y="22"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_0_1" viewBox="0,0,20,74">
 <rect AFMask="2147483647" Plane="0" fill="none" height="30" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,9,36)" width="13" x="3" y="21"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_0_2" viewBox="0,0,20,74">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,254)" height="30" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,9,36)" width="13" x="3" y="21"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_0_3" viewBox="0,0,20,74">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,254)" height="30" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,9,37)" width="13" x="3" y="22"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_1_0" viewBox="0,0,20,74">
 <use Plane="1" x="9" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="9" xlink:href="#terminal" y="69"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="21" y2="10"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="52" y2="63"/>
 <path AFMask="2147483647" Plane="1" d="M 3 11 L 10 5 L 17 12 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 3 57 L 9 64 L 16 57 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 3 15 L 10 9 L 17 16 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 3 61 L 9 68 L 16 61 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_1_1" viewBox="0,0,20,74">
 <use Plane="1" x="9" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="9" xlink:href="#terminal" y="69"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="21" y2="9"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="52" y2="65"/>
 <path AFMask="2147483647" Plane="1" d="M 4 12 L 10 6 L 16 12 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
 <path AFMask="2147483647" Plane="1" d="M 4 61 L 10 67 L 16 61 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_1_2" viewBox="0,0,20,74">
 <use Plane="1" x="9" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="9" xlink:href="#terminal" y="69"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="22" y2="10"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="52" y2="63"/>
 <path AFMask="2147483647" Plane="1" d="M 3 11 L 9 4 L 16 11 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 3 15 L 9 8 L 16 15 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 2 57 L 9 63 L 16 56 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 2 61 L 9 67 L 16 60 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_1_3" viewBox="0,0,20,74">
 <use Plane="1" x="9" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="9" xlink:href="#terminal" y="69"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="21" y2="8"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="52" y2="65"/>
 <path AFMask="2147483647" Plane="1" d="M 3 11 L 9 5 L 15 11 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
 <path AFMask="2147483647" Plane="1" d="M 3 60 L 9 66 L 15 60 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关4_0_0" viewBox="0,0,20,70">
 <rect AFMask="2147483647" Plane="0" fill="none" height="0" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,4,14)" width="0" x="4" y="14"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关4_0_1" viewBox="0,0,20,70">
 <rect AFMask="2147483647" Plane="0" fill="none" height="29" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,10,34)" width="12" x="4" y="20"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关4_0_2" viewBox="0,0,20,70">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,254)" height="27" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,10,34)" width="12" x="4" y="21"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关4_0_3" viewBox="0,0,20,70">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,254)" height="27" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,10,34)" width="12" x="4" y="21"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关4_1_0" viewBox="0,0,20,70">
 <use Plane="1" x="10" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="10" xlink:href="#terminal" y="66"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="57" y2="12"/>
 <path AFMask="2147483647" Plane="1" d="M 4 17 L 9 11 L 16 17 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 4 11 L 9 5 L 16 11 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 4 51 L 9 57 L 16 51 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 4 57 L 9 63 L 16 57 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关4_1_1" viewBox="0,0,20,70">
 <use Plane="1" x="10" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="10" xlink:href="#terminal" y="66"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="19" y2="8"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="49" y2="60"/>
 <path AFMask="2147483647" Plane="1" d="M 5 11 L 10 6 L 15 11 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
 <path AFMask="2147483647" Plane="1" d="M 5 57 L 10 62 L 15 57 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关4_1_2" viewBox="0,0,20,70">
 <use Plane="1" x="10" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="10" xlink:href="#terminal" y="66"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="20" y2="12"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="49" y2="57"/>
 <path AFMask="2147483647" Plane="1" d="M 4 16 L 9 11 L 16 17 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 4 11 L 9 5 L 16 11 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 4 51 L 9 57 L 16 51 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 4 57 L 9 63 L 16 57 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关4_1_3" viewBox="0,0,20,70">
 <use Plane="1" x="10" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="10" xlink:href="#terminal" y="66"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="20" y2="9"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="49" y2="60"/>
 <path AFMask="2147483647" Plane="1" d="M 4 11 L 10 5 L 14 11 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
 <path AFMask="2147483647" Plane="1" d="M 4 56 L 10 62 L 14 56 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
</symbol>
<symbol id="Status:bn_越限颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_越限颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Transformer2:bn_站用变03_0" viewBox="0,0,60,86">
 <use Plane="0" x="38" xlink:href="#terminal" y="3"/>
 <circle AFMask="2147483647" Plane="0" cx="38" cy="20" fill="none" r="15" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="38" x2="48" y1="12" y2="24"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="38" x2="29" y1="12" y2="24"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="29" x2="48" y1="24" y2="24"/>
</symbol>
<symbol id="Transformer2:bn_站用变03_1" viewBox="0,0,60,86">
 <circle AFMask="2147483647" Plane="1" cx="38" cy="44" fill="none" r="15" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="38" x2="38" y1="45" y2="35"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="38" x2="45" y1="45" y2="52"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="38" x2="31" y1="45" y2="52"/>
stroke="rgb(0,0,255)" fill="none" Plane="1" AFMask="2147483647"/&gt;
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="13" x2="38" y1="48" y2="73"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="38" x2="38" y1="60" y2="85"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="12" x2="14" y1="78" y2="78"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="11" x2="15" y1="76" y2="76"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="13" x2="9" y1="74" y2="74"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="11" x2="11" y1="74" y2="74"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="11" x2="17" y1="74" y2="74"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="13" x2="13" y1="45" y2="74"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="20" x2="13" y1="45" y2="45"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="18" x2="23" y1="45" y2="45"/>
</symbol>
<symbol id="Transformer2:bn_中心变＃1站用变_0" viewBox="0,0,60,100">
 <use Plane="0" x="38" xlink:href="#terminal" y="3"/>
 <circle AFMask="2147483647" Plane="0" cx="38" cy="20" fill="none" r="15" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="38" x2="38" y1="8" y2="16"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="38" x2="31" y1="16" y2="23"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="38" x2="45" y1="16" y2="23"/>
</symbol>
<symbol id="Transformer2:bn_中心变＃1站用变_1" viewBox="0,0,60,100">
 <circle AFMask="2147483647" Plane="1" cx="38" cy="44" fill="none" r="15" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="38" x2="38" y1="45" y2="35"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="38" x2="45" y1="45" y2="52"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="38" x2="31" y1="45" y2="52"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="13" x2="38" y1="48" y2="73"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="38" x2="38" y1="60" y2="90"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="12" x2="14" y1="78" y2="78"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="11" x2="15" y1="76" y2="76"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="13" x2="9" y1="74" y2="74"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="11" x2="11" y1="74" y2="74"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="11" x2="17" y1="74" y2="74"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="13" x2="13" y1="45" y2="74"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="20" x2="13" y1="45" y2="45"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="18" x2="23" y1="45" y2="45"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="38" y1="45" y2="45"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="23" y1="45" y2="45"/>
</symbol>
<symbol id="GZP:gg_光子牌1_0" viewBox="0,0,36,36">
 <circle AFMask="2147483647" Plane="0" cx="18" cy="18" fill="rgb(0,255,0)" r="15" stroke="rgb(0,255,0)" stroke-width="1"/>
</symbol>
<symbol id="GZP:gg_光子牌1_1" viewBox="0,0,36,36">
 <circle AFMask="2147483647" Plane="0" cx="18" cy="18" fill="rgb(255,0,0)" r="15" stroke="rgb(255,0,0)" stroke-width="1"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="-1" InitShowingPlane="0," fill="rgb(0,0,0)" height="1100" width="2100" x="0" y="0"/>
</g>
<g id="Other_Layer">
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="20" x2="368" y1="240" y2="240"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="21" x2="369" y1="574" y2="574"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="19" x2="369" y1="363" y2="363"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="19" x2="369" y1="754" y2="754"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="19" x2="369" y1="1009" y2="1009"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="20" x2="368" y1="510" y2="510"/>
 
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="107" x2="107" y1="363" y2="511"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="175" x2="175" y1="517" y2="635"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="107" x2="369" y1="414" y2="414"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="107" x2="369" y1="462" y2="462"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="301" x2="301" y1="239" y2="357"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="233" x2="233" y1="239" y2="357"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="160" x2="160" y1="239" y2="357"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="90" x2="90" y1="239" y2="357"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="21" x2="369" y1="634" y2="634"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="20" x2="368" y1="293" y2="293"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="371" x2="371" y1="18" y2="1009"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="20" x2="368" y1="158" y2="158"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="20" x2="368" y1="16" y2="16"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="16" x2="16" y1="18" y2="1009"/>
</g>
<g id="Bus_Layer">
 <g id="30000143">
  <path d="M 537 324 L 1205 324" stroke="rgb(0,0,255)" stroke-width="3"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 537 324 L 1205 324" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="30000144">
  <path d="M 1317 324 L 1838 324" stroke="rgb(0,0,255)" stroke-width="3"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 1317 324 L 1838 324" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="30000145">
  <path d="M 526 701 L 1243 701" stroke="rgb(0,0,255)" stroke-width="3"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 526 701 L 1243 701" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="30000146">
  <path d="M 1323 701 L 1970 701" stroke="rgb(0,0,255)" stroke-width="3"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 1323 701 L 1970 701" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="Breaker_Layer">
 <g id="100000350">
  <use class="kv-1" height="50" transform="rotate(0,1670,420) scale(1,1) translate(-14,-23)" width="50" x="1670" xlink:href="#Breaker:0_0" y="420"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1670,420) scale(1,1) translate(-14,-23)" width="50" x="1670" y="420"/></g>
 <g id="100000349">
  <use class="kv-1" height="18" transform="rotate(0,1259,404) scale(1,1) translate(-18,-8)" width="38" x="1259" xlink:href="#Breaker:bn_断路器3_0" y="404"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1259,404) scale(1,1) translate(-18,-8)" width="38" x="1259" y="404"/></g>
 <g id="100000347">
  <use class="kv-1" height="50" transform="rotate(0,755,431) scale(1,1) translate(-14,-23)" width="50" x="755" xlink:href="#Breaker:0_0" y="431"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,755,431) scale(1,1) translate(-14,-23)" width="50" x="755" y="431"/></g>
 <g id="100000499">
  <use class="kv-1" height="50" transform="rotate(0,798,245) scale(1,1) translate(-14,-23)" width="50" x="798" xlink:href="#Breaker:0_0" y="245"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,798,245) scale(1,1) translate(-14,-23)" width="50" x="798" y="245"/></g>
</g>
<g id="Disconnector_Layer">
 <g id="101000362">
  <use class="kv-1" height="40" transform="rotate(0,1532,372) scale(1,1) translate(-8,-20)" width="20" x="1532" xlink:href="#Disconnector:bn_刀闸5_0" y="372"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1532,372) scale(1,1) translate(-8,-20)" width="20" x="1532" y="372"/></g>
 <g id="101000361">
  <use class="kv-1" height="40" transform="rotate(0,1007,367) scale(1,1) translate(-8,-20)" width="20" x="1007" xlink:href="#Disconnector:bn_刀闸5_0" y="367"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1007,367) scale(1,1) translate(-8,-20)" width="20" x="1007" y="367"/></g>
 <g id="101000360">
  <use class="kv-1" height="40" transform="rotate(0,1669,367) scale(1,1) translate(-8,-20)" width="20" x="1669" xlink:href="#Disconnector:bn_刀闸5_0" y="367"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1669,367) scale(1,1) translate(-8,-20)" width="20" x="1669" y="367"/></g>
 <g id="101000359">
  <use class="kv-1" height="40" transform="rotate(360,1328,360) scale(-1,1) translate(-2664,-19)" width="20" x="1328" xlink:href="#Disconnector:bn_刀闸3_0" y="360"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(360,1328,360) scale(-1,1) translate(-2664,-19)" width="20" x="1328" y="360"/></g>
 <g id="101000358">
  <use class="kv-1" height="40" transform="rotate(360,1197,360) scale(-1,1) translate(-2402,-19)" width="20" x="1197" xlink:href="#Disconnector:bn_刀闸3_0" y="360"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(360,1197,360) scale(-1,1) translate(-2402,-19)" width="20" x="1197" y="360"/></g>
 <g id="101000353">
  <use class="kv-1" height="40" transform="rotate(0,754,369) scale(1,1) translate(-8,-20)" width="20" x="754" xlink:href="#Disconnector:bn_刀闸5_0" y="369"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,754,369) scale(1,1) translate(-8,-20)" width="20" x="754" y="369"/></g>
 <g id="101000500">
  <use class="kv-1" height="40" transform="rotate(0,797,294) scale(1,1) translate(-8,-19)" width="20" x="797" xlink:href="#Disconnector:bn_刀闸3_0" y="294"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,797,294) scale(1,1) translate(-8,-19)" width="20" x="797" y="294"/></g>
 <g id="101000501">
  <use class="kv-1" height="40" transform="rotate(0,797,200) scale(1,1) translate(-8,-19)" width="20" x="797" xlink:href="#Disconnector:bn_刀闸3_0" y="200"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,797,200) scale(1,1) translate(-8,-19)" width="20" x="797" y="200"/></g>
</g>
<g id="GroundDisconnector_Layer">
 <g id="111000423">
  <use class="kv-1" height="20" transform="rotate(0,1834,814) scale(1,1) translate(-5,-12)" width="40" x="1834" xlink:href="#GroundDisconnector:bn_接地刀闸13_0" y="814"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1834,814) scale(1,1) translate(-5,-12)" width="40" x="1834" y="814"/></g>
 <g id="111000422">
  <use class="kv-1" height="20" transform="rotate(0,1714,814) scale(1,1) translate(-5,-12)" width="40" x="1714" xlink:href="#GroundDisconnector:bn_接地刀闸13_0" y="814"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1714,814) scale(1,1) translate(-5,-12)" width="40" x="1714" y="814"/></g>
 <g id="111000421">
  <use class="kv-1" height="20" transform="rotate(0,1594,814) scale(1,1) translate(-5,-12)" width="40" x="1594" xlink:href="#GroundDisconnector:bn_接地刀闸13_0" y="814"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1594,814) scale(1,1) translate(-5,-12)" width="40" x="1594" y="814"/></g>
 <g id="111000420">
  <use class="kv-1" height="20" transform="rotate(0,1474,814) scale(1,1) translate(-5,-12)" width="40" x="1474" xlink:href="#GroundDisconnector:bn_接地刀闸13_0" y="814"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1474,814) scale(1,1) translate(-5,-12)" width="40" x="1474" y="814"/></g>
 <g id="111000419">
  <use class="kv-1" height="20" transform="rotate(0,1354,814) scale(1,1) translate(-5,-12)" width="40" x="1354" xlink:href="#GroundDisconnector:bn_接地刀闸13_0" y="814"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1354,814) scale(1,1) translate(-5,-12)" width="40" x="1354" y="814"/></g>
 <g id="111000418">
  <use class="kv-1" height="20" transform="rotate(0,1234,814) scale(1,1) translate(-5,-12)" width="40" x="1234" xlink:href="#GroundDisconnector:bn_接地刀闸13_0" y="814"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1234,814) scale(1,1) translate(-5,-12)" width="40" x="1234" y="814"/></g>
 <g id="111000417">
  <use class="kv-1" height="20" transform="rotate(0,1114,814) scale(1,1) translate(-5,-12)" width="40" x="1114" xlink:href="#GroundDisconnector:bn_接地刀闸13_0" y="814"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1114,814) scale(1,1) translate(-5,-12)" width="40" x="1114" y="814"/></g>
 <g id="111000416">
  <use class="kv-1" height="20" transform="rotate(0,994,814) scale(1,1) translate(-5,-12)" width="40" x="994" xlink:href="#GroundDisconnector:bn_接地刀闸13_0" y="814"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,994,814) scale(1,1) translate(-5,-12)" width="40" x="994" y="814"/></g>
 <g id="111000415">
  <use class="kv-1" height="20" transform="rotate(0,874,814) scale(1,1) translate(-5,-12)" width="40" x="874" xlink:href="#GroundDisconnector:bn_接地刀闸13_0" y="814"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,874,814) scale(1,1) translate(-5,-12)" width="40" x="874" y="814"/></g>
 <g id="111000414">
  <use class="kv-1" height="20" transform="rotate(0,754,814) scale(1,1) translate(-5,-12)" width="40" x="754" xlink:href="#GroundDisconnector:bn_接地刀闸13_0" y="814"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,754,814) scale(1,1) translate(-5,-12)" width="40" x="754" y="814"/></g>
 <g id="111000413">
  <use class="kv-1" height="20" transform="rotate(0,634,814) scale(1,1) translate(-5,-12)" width="40" x="634" xlink:href="#GroundDisconnector:bn_接地刀闸13_0" y="814"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,634,814) scale(1,1) translate(-5,-12)" width="40" x="634" y="814"/></g>
 <g id="111000412">
  <use class="kv-1" height="16" transform="rotate(0,1508,403) scale(1,1) translate(-31,-8)" width="34" x="1508" xlink:href="#GroundDisconnector:bn_接地刀闸111_0" y="403"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(0,1508,403) scale(1,1) translate(-31,-8)" width="34" x="1508" y="403"/></g>
 <g id="111000411">
  <use class="kv-1" height="16" transform="rotate(0,1506,345) scale(1,1) translate(-31,-8)" width="34" x="1506" xlink:href="#GroundDisconnector:bn_接地刀闸111_0" y="345"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(0,1506,345) scale(1,1) translate(-31,-8)" width="34" x="1506" y="345"/></g>
 <g id="111000410">
  <use class="kv-1" height="16" transform="rotate(0,983,398) scale(1,1) translate(-31,-8)" width="34" x="983" xlink:href="#GroundDisconnector:bn_接地刀闸111_0" y="398"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(0,983,398) scale(1,1) translate(-31,-8)" width="34" x="983" y="398"/></g>
 <g id="111000409">
  <use class="kv-1" height="16" transform="rotate(0,981,340) scale(1,1) translate(-31,-8)" width="34" x="981" xlink:href="#GroundDisconnector:bn_接地刀闸111_0" y="340"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(0,981,340) scale(1,1) translate(-31,-8)" width="34" x="981" y="340"/></g>
 <g id="111000503">
  <use class="kv-1" height="20" transform="rotate(360,766,170) scale(-1,1) translate(-1536,-12)" width="40" x="766" xlink:href="#GroundDisconnector:bn_接地刀闸12_0" y="170"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(360,766,170) scale(-1,1) translate(-1536,-12)" width="40" x="766" y="170"/></g>
</g>
<g id="Transformer2_Layer">
<g id="102000376">
 <g id="1020003760">
  <use class="kv-1" height="76" transform="rotate(0,1669,507) scale(1,1) translate(-26,-32)" width="50" x="1669" xlink:href="#Transformer2:bn_两卷变压器10_0" y="507"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="1020003761">
  <use class="kv-1" height="76" transform="rotate(0,1669,507) scale(1,1) translate(-26,-32)" width="50" x="1669" xlink:href="#Transformer2:bn_两卷变压器10_1" y="507"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <metadata>
  <cge:PSR_Ref ObjectID="-1" ObjectName="-1" Plane="0"/>
 </metadata>
<rect fill="white" height="76" opacity="0" stroke="white" transform="rotate(0,1669,507) scale(1,1) translate(-26,-32)" width="50" x="1669" y="507"/></g>
<g id="102000375">
 <g id="1020003750">
  <use class="kv-1" height="76" transform="rotate(0,754,514) scale(1,1) translate(-26,-32)" width="50" x="754" xlink:href="#Transformer2:bn_两卷变压器10_0" y="514"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="1020003751">
  <use class="kv-1" height="76" transform="rotate(0,754,514) scale(1,1) translate(-26,-32)" width="50" x="754" xlink:href="#Transformer2:bn_两卷变压器10_1" y="514"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <metadata>
  <cge:PSR_Ref ObjectID="-1" ObjectName="-1" Plane="0"/>
 </metadata>
<rect fill="white" height="76" opacity="0" stroke="white" transform="rotate(0,754,514) scale(1,1) translate(-26,-32)" width="50" x="754" y="514"/></g>
<g id="102000516">
 <g id="1020005160">
  <use class="kv-1" height="100" transform="rotate(0,913,185) scale(1,1) translate(-38,-3)" width="60" x="913" xlink:href="#Transformer2:bn_中心变＃1站用变_0" y="185"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="1020005161">
  <use class="kv-1" height="100" transform="rotate(0,913,185) scale(1,1) translate(-38,-3)" width="60" x="913" xlink:href="#Transformer2:bn_中心变＃1站用变_1" y="185"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <metadata>
  <cge:PSR_Ref ObjectID="-1" ObjectName="-1" Plane="0"/>
 </metadata>
<rect fill="white" height="100" opacity="0" stroke="white" transform="rotate(0,913,185) scale(1,1) translate(-38,-3)" width="60" x="913" y="185"/></g>
<g id="102000540">
 <g id="1020005400">
  <use class="kv-1" height="86" transform="rotate(0,618,845) scale(1,1) translate(-38,-3)" width="60" x="618" xlink:href="#Transformer2:bn_站用变03_0" y="845"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="1020005401">
  <use class="kv-1" height="86" transform="rotate(0,618,845) scale(1,1) translate(-38,-3)" width="60" x="618" xlink:href="#Transformer2:bn_站用变03_1" y="845"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <metadata>
  <cge:PSR_Ref ObjectID="-1" ObjectName="-1" Plane="0"/>
 </metadata>
<rect fill="white" height="86" opacity="0" stroke="white" transform="rotate(0,618,845) scale(1,1) translate(-38,-3)" width="60" x="618" y="845"/></g>
</g>
<g id="DollyBreaker_Layer">
 <g id="110000398">
  <use class="kv-1" height="74" transform="rotate(0,1818,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="1818" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="760"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1818,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="1818" y="760"/></g>
 <g id="110000398">
  <use class="kv-1" height="74" transform="rotate(0,1818,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="1818" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="760"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1818,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="1818" y="760"/></g>
 <g id="110000397">
  <use class="kv-1" height="74" transform="rotate(0,1698,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="1698" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="760"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1698,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="1698" y="760"/></g>
 <g id="110000397">
  <use class="kv-1" height="74" transform="rotate(0,1698,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="1698" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="760"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1698,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="1698" y="760"/></g>
 <g id="110000396">
  <use class="kv-1" height="74" transform="rotate(0,1578,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="1578" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="760"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1578,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="1578" y="760"/></g>
 <g id="110000396">
  <use class="kv-1" height="74" transform="rotate(0,1578,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="1578" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="760"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1578,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="1578" y="760"/></g>
 <g id="110000395">
  <use class="kv-1" height="74" transform="rotate(0,1458,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="1458" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="760"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1458,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="1458" y="760"/></g>
 <g id="110000395">
  <use class="kv-1" height="74" transform="rotate(0,1458,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="1458" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="760"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1458,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="1458" y="760"/></g>
 <g id="110000394">
  <use class="kv-1" height="74" transform="rotate(0,1338,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="1338" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="760"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1338,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="1338" y="760"/></g>
 <g id="110000394">
  <use class="kv-1" height="74" transform="rotate(0,1338,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="1338" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="760"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1338,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="1338" y="760"/></g>
 <g id="110000393">
  <use class="kv-1" height="74" transform="rotate(0,1218,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="1218" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="760"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1218,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="1218" y="760"/></g>
 <g id="110000393">
  <use class="kv-1" height="74" transform="rotate(0,1218,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="1218" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="760"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1218,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="1218" y="760"/></g>
 <g id="110000392">
  <use class="kv-1" height="74" transform="rotate(0,1098,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="1098" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="760"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1098,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="1098" y="760"/></g>
 <g id="110000392">
  <use class="kv-1" height="74" transform="rotate(0,1098,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="1098" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="760"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1098,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="1098" y="760"/></g>
 <g id="110000391">
  <use class="kv-1" height="74" transform="rotate(0,978,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="978" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="760"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,978,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="978" y="760"/></g>
 <g id="110000391">
  <use class="kv-1" height="74" transform="rotate(0,978,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="978" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="760"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,978,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="978" y="760"/></g>
 <g id="110000390">
  <use class="kv-1" height="74" transform="rotate(0,858,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="858" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="760"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,858,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="858" y="760"/></g>
 <g id="110000390">
  <use class="kv-1" height="74" transform="rotate(0,858,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="858" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="760"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,858,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="858" y="760"/></g>
 <g id="110000389">
  <use class="kv-1" height="74" transform="rotate(0,738,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="738" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="760"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,738,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="738" y="760"/></g>
 <g id="110000389">
  <use class="kv-1" height="74" transform="rotate(0,738,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="738" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="760"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,738,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="738" y="760"/></g>
 <g id="110000388">
  <use class="kv-1" height="74" transform="rotate(0,618,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="618" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="760"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,618,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="618" y="760"/></g>
 <g id="110000388">
  <use class="kv-1" height="74" transform="rotate(0,618,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="618" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="760"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,618,760) scale(1,0.879) translate(-10,67.6189)" width="20" x="618" y="760"/></g>
 <g id="110000387">
  <use class="kv-1" height="70" transform="rotate(0,1237,668) scale(1,0.501) translate(-10,630.333)" width="20" x="1237" xlink:href="#DollyBreaker:bn_小车开关4_0_0" y="668"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,1237,668) scale(1,0.501) translate(-10,630.333)" width="20" x="1237" y="668"/></g>
 <g id="110000387">
  <use class="kv-1" height="70" transform="rotate(0,1237,668) scale(1,0.501) translate(-10,630.333)" width="20" x="1237" xlink:href="#DollyBreaker:bn_小车开关4_1_0" y="668"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,1237,668) scale(1,0.501) translate(-10,630.333)" width="20" x="1237" y="668"/></g>
 <g id="110000386">
  <use class="kv-1" height="74" transform="rotate(0,1335,670) scale(0.862,0.584) translate(203.724,440.26)" width="20" x="1335" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="670"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1335,670) scale(0.862,0.584) translate(203.724,440.26)" width="20" x="1335" y="670"/></g>
 <g id="110000386">
  <use class="kv-1" height="74" transform="rotate(0,1335,670) scale(0.862,0.584) translate(203.724,440.26)" width="20" x="1335" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="670"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1335,670) scale(0.862,0.584) translate(203.724,440.26)" width="20" x="1335" y="670"/></g>
 <g id="110000385">
  <use class="kv-1" height="70" transform="rotate(0,1467,659) scale(1,0.444) translate(-10,790.234)" width="20" x="1467" xlink:href="#DollyBreaker:bn_小车开关4_0_0" y="659"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,1467,659) scale(1,0.444) translate(-10,790.234)" width="20" x="1467" y="659"/></g>
 <g id="110000385">
  <use class="kv-1" height="70" transform="rotate(0,1467,659) scale(1,0.444) translate(-10,790.234)" width="20" x="1467" xlink:href="#DollyBreaker:bn_小车开关4_1_0" y="659"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,1467,659) scale(1,0.444) translate(-10,790.234)" width="20" x="1467" y="659"/></g>
 <g id="110000384">
  <use class="kv-1" height="70" transform="rotate(0,933,645) scale(1,0.444) translate(-10,772.703)" width="20" x="933" xlink:href="#DollyBreaker:bn_小车开关4_0_0" y="645"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,933,645) scale(1,0.444) translate(-10,772.703)" width="20" x="933" y="645"/></g>
 <g id="110000384">
  <use class="kv-1" height="70" transform="rotate(0,933,645) scale(1,0.444) translate(-10,772.703)" width="20" x="933" xlink:href="#DollyBreaker:bn_小车开关4_1_0" y="645"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,933,645) scale(1,0.444) translate(-10,772.703)" width="20" x="933" y="645"/></g>
 <g id="110000382">
  <use class="kv-1" height="74" transform="rotate(0,754,610) scale(1,1) translate(-10,-37)" width="20" x="754" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="610"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,754,610) scale(1,1) translate(-10,-37)" width="20" x="754" y="610"/></g>
 <g id="110000382">
  <use class="kv-1" height="74" transform="rotate(0,754,610) scale(1,1) translate(-10,-37)" width="20" x="754" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="610"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,754,610) scale(1,1) translate(-10,-37)" width="20" x="754" y="610"/></g>
 <g id="110000383">
  <use class="kv-1" height="74" transform="rotate(0,1669,608) scale(1,1) translate(-10,-37)" width="20" x="1669" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="608"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1669,608) scale(1,1) translate(-10,-37)" width="20" x="1669" y="608"/></g>
 <g id="110000383">
  <use class="kv-1" height="74" transform="rotate(0,1669,608) scale(1,1) translate(-10,-37)" width="20" x="1669" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="608"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1669,608) scale(1,1) translate(-10,-37)" width="20" x="1669" y="608"/></g>
</g>
<g id="ACLineSegment_Layer">
 <g id="36000343">
 <path d="M 1818 896 L 1818 962" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="ST=-1/LN=-1" ObjectName="ST=-1/LN=-1" Plane="0"/>
  </metadata>
 <path d="M 1818 896 L 1818 962" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="36000342">
 <path d="M 1698 896 L 1698 962" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="ST=-1/LN=-1" ObjectName="ST=-1/LN=-1" Plane="0"/>
  </metadata>
 <path d="M 1698 896 L 1698 962" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="36000341">
 <path d="M 1578 896 L 1578 962" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="ST=-1/LN=-1" ObjectName="ST=-1/LN=-1" Plane="0"/>
  </metadata>
 <path d="M 1578 896 L 1578 962" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="36000340">
 <path d="M 1458 896 L 1458 962" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="ST=-1/LN=-1" ObjectName="ST=-1/LN=-1" Plane="0"/>
  </metadata>
 <path d="M 1458 896 L 1458 962" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="36000339">
 <path d="M 1338 896 L 1338 962" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="ST=-1/LN=-1" ObjectName="ST=-1/LN=-1" Plane="0"/>
  </metadata>
 <path d="M 1338 896 L 1338 962" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="36000338">
 <path d="M 1218 896 L 1218 962" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="ST=-1/LN=-1" ObjectName="ST=-1/LN=-1" Plane="0"/>
  </metadata>
 <path d="M 1218 896 L 1218 962" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="36000337">
 <path d="M 1098 896 L 1098 962" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="ST=-1/LN=-1" ObjectName="ST=-1/LN=-1" Plane="0"/>
  </metadata>
 <path d="M 1098 896 L 1098 962" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="36000336">
 <path d="M 978 896 L 978 962" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="ST=-1/LN=-1" ObjectName="ST=-1/LN=-1" Plane="0"/>
  </metadata>
 <path d="M 978 896 L 978 962" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="36000335">
 <path d="M 858 896 L 858 962" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="ST=-1/LN=-1" ObjectName="ST=-1/LN=-1" Plane="0"/>
  </metadata>
 <path d="M 858 896 L 858 962" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="36000334">
 <path d="M 738 896 L 738 962" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="ST=-1/LN=-1" ObjectName="ST=-1/LN=-1" Plane="0"/>
  </metadata>
 <path d="M 738 896 L 738 962" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="PT_Layer">
 <g id="107000381">
  <use class="kv-1" height="60" transform="rotate(360,1467,615) scale(1,-1) translate(-30,-1236)" width="60" x="1467" xlink:href="#PT:bn_电压互感器_0" y="615"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(360,1467,615) scale(1,-1) translate(-30,-1236)" width="60" x="1467" y="615"/></g>
 <g id="107000380">
  <use class="kv-1" height="60" transform="rotate(360,933,603) scale(1,-1) translate(-30,-1212)" width="60" x="933" xlink:href="#PT:bn_电压互感器_0" y="603"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(360,933,603) scale(1,-1) translate(-30,-1212)" width="60" x="933" y="603"/></g>
 <g id="107000379">
  <use class="kv-1" height="100" transform="rotate(360,1532,415) scale(1,-1) translate(-33,-927)" width="100" x="1532" xlink:href="#PT:bn_电压互感器004_0" y="415"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="100" opacity="0" stroke="white" transform="rotate(360,1532,415) scale(1,-1) translate(-33,-927)" width="100" x="1532" y="415"/></g>
 <g id="107000378">
  <use class="kv-1" height="100" transform="rotate(360,1007,410) scale(1,-1) translate(-33,-917)" width="100" x="1007" xlink:href="#PT:bn_电压互感器004_0" y="410"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="100" opacity="0" stroke="white" transform="rotate(360,1007,410) scale(1,-1) translate(-33,-917)" width="100" x="1007" y="410"/></g>
 <g id="107000502">
  <use class="kv-1" height="44" transform="rotate(270,720,140) scale(1,1) translate(-9,-41)" width="18" x="720" xlink:href="#PT:bn_电压互感器46_0" y="140"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="44" opacity="0" stroke="white" transform="rotate(270,720,140) scale(1,1) translate(-9,-41)" width="18" x="720" y="140"/></g>
</g>
<g id="GZP_Layer">
 <g id="135000460">
  <use class="kv-1" height="36" transform="rotate(0,257,692) scale(1,1) translate(-18,-18)" width="36" x="257" xlink:href="#GZP:gg_光子牌1_0" y="692"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,257,692) scale(1,1) translate(-18,-18)" width="36" x="257" y="692"/></g>
</g>
<g id="Arrester_Layer">
 <g id="133000456">
  <use class="kv-1" height="20" transform="rotate(0,1793,814) scale(1,1) translate(-36,-10)" width="40" x="1793" xlink:href="#Arrester:bn_避雷器11_0" y="814"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1793,814) scale(1,1) translate(-36,-10)" width="40" x="1793" y="814"/></g>
 <g id="133000454">
  <use class="kv-1" height="20" transform="rotate(0,1673,814) scale(1,1) translate(-36,-10)" width="40" x="1673" xlink:href="#Arrester:bn_避雷器11_0" y="814"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1673,814) scale(1,1) translate(-36,-10)" width="40" x="1673" y="814"/></g>
 <g id="133000452">
  <use class="kv-1" height="20" transform="rotate(0,1553,814) scale(1,1) translate(-36,-10)" width="40" x="1553" xlink:href="#Arrester:bn_避雷器11_0" y="814"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1553,814) scale(1,1) translate(-36,-10)" width="40" x="1553" y="814"/></g>
 <g id="133000450">
  <use class="kv-1" height="20" transform="rotate(0,1433,814) scale(1,1) translate(-36,-10)" width="40" x="1433" xlink:href="#Arrester:bn_避雷器11_0" y="814"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1433,814) scale(1,1) translate(-36,-10)" width="40" x="1433" y="814"/></g>
 <g id="133000448">
  <use class="kv-1" height="20" transform="rotate(0,1313,814) scale(1,1) translate(-36,-10)" width="40" x="1313" xlink:href="#Arrester:bn_避雷器11_0" y="814"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1313,814) scale(1,1) translate(-36,-10)" width="40" x="1313" y="814"/></g>
 <g id="133000446">
  <use class="kv-1" height="20" transform="rotate(0,1193,814) scale(1,1) translate(-36,-10)" width="40" x="1193" xlink:href="#Arrester:bn_避雷器11_0" y="814"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1193,814) scale(1,1) translate(-36,-10)" width="40" x="1193" y="814"/></g>
 <g id="133000444">
  <use class="kv-1" height="20" transform="rotate(0,1073,814) scale(1,1) translate(-36,-10)" width="40" x="1073" xlink:href="#Arrester:bn_避雷器11_0" y="814"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1073,814) scale(1,1) translate(-36,-10)" width="40" x="1073" y="814"/></g>
 <g id="133000442">
  <use class="kv-1" height="20" transform="rotate(0,953,814) scale(1,1) translate(-36,-10)" width="40" x="953" xlink:href="#Arrester:bn_避雷器11_0" y="814"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,953,814) scale(1,1) translate(-36,-10)" width="40" x="953" y="814"/></g>
 <g id="133000440">
  <use class="kv-1" height="20" transform="rotate(0,833,814) scale(1,1) translate(-36,-10)" width="40" x="833" xlink:href="#Arrester:bn_避雷器11_0" y="814"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,833,814) scale(1,1) translate(-36,-10)" width="40" x="833" y="814"/></g>
 <g id="133000438">
  <use class="kv-1" height="20" transform="rotate(0,713,814) scale(1,1) translate(-36,-10)" width="40" x="713" xlink:href="#Arrester:bn_避雷器11_0" y="814"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,713,814) scale(1,1) translate(-36,-10)" width="40" x="713" y="814"/></g>
 <g id="133000436">
  <use class="kv-1" height="20" transform="rotate(0,593,814) scale(1,1) translate(-36,-10)" width="40" x="593" xlink:href="#Arrester:bn_避雷器11_0" y="814"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,593,814) scale(1,1) translate(-36,-10)" width="40" x="593" y="814"/></g>
 <g id="133000505">
  <use class="kv-1" height="20" transform="rotate(0,773,105) scale(1,1) translate(-36,-10)" width="40" x="773" xlink:href="#Arrester:bn_避雷器11_0" y="105"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,773,105) scale(1,1) translate(-36,-10)" width="40" x="773" y="105"/></g>
 <g id="133000529">
  <use class="kv-1" height="20" transform="rotate(0,735,666) scale(1,1) translate(-36,-10)" width="40" x="735" xlink:href="#Arrester:bn_避雷器11_0" y="666"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,735,666) scale(1,1) translate(-36,-10)" width="40" x="735" y="666"/></g>
 <g id="133000530">
  <use class="kv-1" height="20" transform="rotate(0,1642,663) scale(1,1) translate(-36,-10)" width="40" x="1642" xlink:href="#Arrester:bn_避雷器11_0" y="663"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1642,663) scale(1,1) translate(-36,-10)" width="40" x="1642" y="663"/></g>
</g>
<g id="Fuse_Layer">
 <g id="131000504">
  <use class="kv-1" height="16" transform="rotate(360,755,140) scale(-1,1) translate(-1526,-12)" width="32" x="755" xlink:href="#Fuse:bn_熔断器3_0" y="140"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(360,755,140) scale(-1,1) translate(-1526,-12)" width="32" x="755" y="140"/></g>
 <g id="131000514">
  <use class="kv-1" height="100" transform="rotate(360,911,154) scale(0.51,-0.51) translate(841.275,-489.961)" width="100" x="911" xlink:href="#Fuse:11111_0" y="154"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="100" opacity="0" stroke="white" transform="rotate(360,911,154) scale(0.51,-0.51) translate(841.275,-489.961)" width="100" x="911" y="154"/></g>
</g>
<g id="Status_Layer">
 <g id="126000428">
  <use class="kv-1" height="40" transform="rotate(0,54,266) scale(0.7,0.7) translate(-6.85714,94)" width="60" x="54" xlink:href="#Status:bn_工况退出颜色显示_0" y="266"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,54,266) scale(0.7,0.7) translate(-6.85714,94)" width="60" x="54" y="266"/></g>
 <g id="126000429">
  <use class="kv-1" height="40" transform="rotate(0,124,266) scale(0.7,0.7) translate(23.1429,94)" width="60" x="124" xlink:href="#Status:bn_不变化颜色显示_0" y="266"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,124,266) scale(0.7,0.7) translate(23.1429,94)" width="60" x="124" y="266"/></g>
 <g id="126000430">
  <use class="kv-1" height="40" transform="rotate(0,194,266) scale(0.7,0.7) translate(53.1429,94)" width="60" x="194" xlink:href="#Status:bn_越限颜色显示_0" y="266"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,194,266) scale(0.7,0.7) translate(53.1429,94)" width="60" x="194" y="266"/></g>
 <g id="126000431">
  <use class="kv-1" height="40" transform="rotate(0,267,266) scale(0.7,0.7) translate(84.4286,94)" width="60" x="267" xlink:href="#Status:bn_非实测颜色显示_0" y="266"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,267,266) scale(0.7,0.7) translate(84.4286,94)" width="60" x="267" y="266"/></g>
 <g id="126000432">
  <use class="kv-1" height="40" transform="rotate(0,334,266) scale(0.7,0.7) translate(113.143,94)" width="60" x="334" xlink:href="#Status:bn_数据封锁颜色显示_0" y="266"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,334,266) scale(0.7,0.7) translate(113.143,94)" width="60" x="334" y="266"/></g>
</g>
<g id="Clock_Layer">
 <g id="56000345">
  
 <metadata/></g>
</g>
<g id="Link_Layer">
 <g id="34000317">
 <path d="M 1793 814 L 1818 814" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133000456_0" Pin1InfoVect0LinkObjId="34000314_1" Pin1InfoVect1LinkObjId="34000315_1" Pin1InfoVect2LinkObjId="34000316_0" Plane="0"/>
  </metadata>
 <path d="M 1793 814 L 1818 814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000316">
 <path d="M 1818 814 L 1818 896" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000314_1" Pin0InfoVect1LinkObjId="34000317_1" Pin0InfoVect2LinkObjId="34000315_1" Pin1InfoVect0LinkObjId="36000343_0" Plane="0"/>
  </metadata>
 <path d="M 1818 814 L 1818 896" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000315">
 <path d="M 1818 788 L 1818 814" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000398_1" Pin1InfoVect0LinkObjId="34000314_1" Pin1InfoVect1LinkObjId="34000317_1" Pin1InfoVect2LinkObjId="34000316_0" Plane="0"/>
  </metadata>
 <path d="M 1818 788 L 1818 814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000314">
 <path d="M 1834 814 L 1818 814" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111000423_0" Pin1InfoVect0LinkObjId="34000315_1" Pin1InfoVect1LinkObjId="34000316_0" Pin1InfoVect2LinkObjId="34000317_1" Plane="0"/>
  </metadata>
 <path d="M 1834 814 L 1818 814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000313">
 <path d="M 1818 701 L 1818 731" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="30000146_0" Pin1InfoVect0LinkObjId="110000398_0" Plane="0"/>
  </metadata>
 <path d="M 1818 701 L 1818 731" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000304">
 <path d="M 1698 701 L 1698 731" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="30000146_0" Pin1InfoVect0LinkObjId="110000397_0" Plane="0"/>
  </metadata>
 <path d="M 1698 701 L 1698 731" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000305">
 <path d="M 1714 814 L 1698 814" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111000422_0" Pin1InfoVect0LinkObjId="34000307_0" Pin1InfoVect1LinkObjId="34000308_1" Pin1InfoVect2LinkObjId="34000306_1" Plane="0"/>
  </metadata>
 <path d="M 1714 814 L 1698 814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000306">
 <path d="M 1698 788 L 1698 814" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000397_1" Pin1InfoVect0LinkObjId="34000307_0" Pin1InfoVect1LinkObjId="34000308_1" Pin1InfoVect2LinkObjId="34000305_1" Plane="0"/>
  </metadata>
 <path d="M 1698 788 L 1698 814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000307">
 <path d="M 1698 814 L 1698 896" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000305_1" Pin0InfoVect1LinkObjId="34000306_1" Pin0InfoVect2LinkObjId="34000308_1" Pin1InfoVect0LinkObjId="36000342_0" Plane="0"/>
  </metadata>
 <path d="M 1698 814 L 1698 896" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000308">
 <path d="M 1673 814 L 1698 814" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133000454_0" Pin1InfoVect0LinkObjId="34000307_0" Pin1InfoVect1LinkObjId="34000305_1" Pin1InfoVect2LinkObjId="34000306_1" Plane="0"/>
  </metadata>
 <path d="M 1673 814 L 1698 814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000299">
 <path d="M 1553 814 L 1578 814" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133000452_0" Pin1InfoVect0LinkObjId="34000298_0" Pin1InfoVect1LinkObjId="34000297_1" Pin1InfoVect2LinkObjId="34000296_1" Plane="0"/>
  </metadata>
 <path d="M 1553 814 L 1578 814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000298">
 <path d="M 1578 814 L 1578 896" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000296_1" Pin0InfoVect1LinkObjId="34000297_1" Pin0InfoVect2LinkObjId="34000299_1" Pin1InfoVect0LinkObjId="36000341_0" Plane="0"/>
  </metadata>
 <path d="M 1578 814 L 1578 896" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000297">
 <path d="M 1578 788 L 1578 814" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000396_1" Pin1InfoVect0LinkObjId="34000298_0" Pin1InfoVect1LinkObjId="34000296_1" Pin1InfoVect2LinkObjId="34000299_1" Plane="0"/>
  </metadata>
 <path d="M 1578 788 L 1578 814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000296">
 <path d="M 1594 814 L 1578 814" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111000421_0" Pin1InfoVect0LinkObjId="34000298_0" Pin1InfoVect1LinkObjId="34000297_1" Pin1InfoVect2LinkObjId="34000299_1" Plane="0"/>
  </metadata>
 <path d="M 1594 814 L 1578 814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000295">
 <path d="M 1578 701 L 1578 731" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="30000146_0" Pin1InfoVect0LinkObjId="110000396_0" Plane="0"/>
  </metadata>
 <path d="M 1578 701 L 1578 731" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000286">
 <path d="M 1458 701 L 1458 731" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="30000146_0" Pin1InfoVect0LinkObjId="110000395_0" Plane="0"/>
  </metadata>
 <path d="M 1458 701 L 1458 731" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000287">
 <path d="M 1474 814 L 1458 814" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111000420_0" Pin1InfoVect0LinkObjId="34000288_1" Pin1InfoVect1LinkObjId="34000289_0" Pin1InfoVect2LinkObjId="34000290_1" Plane="0"/>
  </metadata>
 <path d="M 1474 814 L 1458 814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000288">
 <path d="M 1458 788 L 1458 814" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000395_1" Pin1InfoVect0LinkObjId="34000287_1" Pin1InfoVect1LinkObjId="34000289_0" Pin1InfoVect2LinkObjId="34000290_1" Plane="0"/>
  </metadata>
 <path d="M 1458 788 L 1458 814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000289">
 <path d="M 1458 814 L 1458 896" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000287_1" Pin0InfoVect1LinkObjId="34000288_1" Pin0InfoVect2LinkObjId="34000290_1" Pin1InfoVect0LinkObjId="36000340_0" Plane="0"/>
  </metadata>
 <path d="M 1458 814 L 1458 896" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000290">
 <path d="M 1433 814 L 1458 814" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133000450_0" Pin1InfoVect0LinkObjId="34000287_1" Pin1InfoVect1LinkObjId="34000288_1" Pin1InfoVect2LinkObjId="34000289_0" Plane="0"/>
  </metadata>
 <path d="M 1433 814 L 1458 814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000281">
 <path d="M 1313 814 L 1338 814" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133000448_0" Pin1InfoVect0LinkObjId="34000280_0" Pin1InfoVect1LinkObjId="34000279_1" Pin1InfoVect2LinkObjId="34000278_1" Plane="0"/>
  </metadata>
 <path d="M 1313 814 L 1338 814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000280">
 <path d="M 1338 814 L 1338 896" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000278_1" Pin0InfoVect1LinkObjId="34000279_1" Pin0InfoVect2LinkObjId="34000281_1" Pin1InfoVect0LinkObjId="36000339_0" Plane="0"/>
  </metadata>
 <path d="M 1338 814 L 1338 896" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000279">
 <path d="M 1338 788 L 1338 814" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000394_1" Pin1InfoVect0LinkObjId="34000280_0" Pin1InfoVect1LinkObjId="34000278_1" Pin1InfoVect2LinkObjId="34000281_1" Plane="0"/>
  </metadata>
 <path d="M 1338 788 L 1338 814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000278">
 <path d="M 1354 814 L 1338 814" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111000419_0" Pin1InfoVect0LinkObjId="34000280_0" Pin1InfoVect1LinkObjId="34000279_1" Pin1InfoVect2LinkObjId="34000281_1" Plane="0"/>
  </metadata>
 <path d="M 1354 814 L 1338 814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000277">
 <path d="M 1338 701 L 1338 731" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="30000146_0" Pin1InfoVect0LinkObjId="110000394_0" Plane="0"/>
  </metadata>
 <path d="M 1338 701 L 1338 731" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000276">
 <path d="M 1237 652 L 1237 637 L 1335 637 L 1335 651" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000387_0" Pin1InfoVect0LinkObjId="110000386_0" Plane="0"/>
  </metadata>
 <path d="M 1237 652 L 1237 637 L 1335 637 L 1335 651" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000275">
 <path d="M 1237 684 L 1237 701" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000387_1" Pin1InfoVect0LinkObjId="30000145_0" Plane="0"/>
  </metadata>
 <path d="M 1237 684 L 1237 701" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000270">
 <path d="M 1193 814 L 1218 814" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133000446_0" Pin1InfoVect0LinkObjId="34000267_1" Pin1InfoVect1LinkObjId="34000268_1" Pin1InfoVect2LinkObjId="34000269_0" Plane="0"/>
  </metadata>
 <path d="M 1193 814 L 1218 814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000269">
 <path d="M 1218 814 L 1218 896" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000267_1" Pin0InfoVect1LinkObjId="34000268_1" Pin0InfoVect2LinkObjId="34000270_1" Pin1InfoVect0LinkObjId="36000338_0" Plane="0"/>
  </metadata>
 <path d="M 1218 814 L 1218 896" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000268">
 <path d="M 1218 788 L 1218 814" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000393_1" Pin1InfoVect0LinkObjId="34000267_1" Pin1InfoVect1LinkObjId="34000269_0" Pin1InfoVect2LinkObjId="34000270_1" Plane="0"/>
  </metadata>
 <path d="M 1218 788 L 1218 814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000267">
 <path d="M 1234 814 L 1218 814" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111000418_0" Pin1InfoVect0LinkObjId="34000268_1" Pin1InfoVect1LinkObjId="34000269_0" Pin1InfoVect2LinkObjId="34000270_1" Plane="0"/>
  </metadata>
 <path d="M 1234 814 L 1218 814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000266">
 <path d="M 1218 701 L 1218 731" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="30000145_0" Pin1InfoVect0LinkObjId="110000393_0" Plane="0"/>
  </metadata>
 <path d="M 1218 701 L 1218 731" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000257">
 <path d="M 1098 701 L 1098 731" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="30000145_0" Pin1InfoVect0LinkObjId="110000392_0" Plane="0"/>
  </metadata>
 <path d="M 1098 701 L 1098 731" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000258">
 <path d="M 1114 814 L 1098 814" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111000417_0" Pin1InfoVect0LinkObjId="34000260_0" Pin1InfoVect1LinkObjId="34000261_1" Pin1InfoVect2LinkObjId="34000259_1" Plane="0"/>
  </metadata>
 <path d="M 1114 814 L 1098 814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000259">
 <path d="M 1098 788 L 1098 814" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000392_1" Pin1InfoVect0LinkObjId="34000260_0" Pin1InfoVect1LinkObjId="34000261_1" Pin1InfoVect2LinkObjId="34000258_1" Plane="0"/>
  </metadata>
 <path d="M 1098 788 L 1098 814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000260">
 <path d="M 1098 814 L 1098 896" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000258_1" Pin0InfoVect1LinkObjId="34000259_1" Pin0InfoVect2LinkObjId="34000261_1" Pin1InfoVect0LinkObjId="36000337_0" Plane="0"/>
  </metadata>
 <path d="M 1098 814 L 1098 896" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000261">
 <path d="M 1073 814 L 1098 814" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133000444_0" Pin1InfoVect0LinkObjId="34000260_0" Pin1InfoVect1LinkObjId="34000258_1" Pin1InfoVect2LinkObjId="34000259_1" Plane="0"/>
  </metadata>
 <path d="M 1073 814 L 1098 814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000252">
 <path d="M 953 814 L 978 814" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133000442_0" Pin1InfoVect0LinkObjId="34000249_1" Pin1InfoVect1LinkObjId="34000250_1" Pin1InfoVect2LinkObjId="34000251_0" Plane="0"/>
  </metadata>
 <path d="M 953 814 L 978 814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000251">
 <path d="M 978 814 L 978 896" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000252_1" Pin0InfoVect1LinkObjId="34000249_1" Pin0InfoVect2LinkObjId="34000250_1" Pin1InfoVect0LinkObjId="36000336_0" Plane="0"/>
  </metadata>
 <path d="M 978 814 L 978 896" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000250">
 <path d="M 978 788 L 978 814" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000391_1" Pin1InfoVect0LinkObjId="34000252_1" Pin1InfoVect1LinkObjId="34000251_0" Pin1InfoVect2LinkObjId="34000249_1" Plane="0"/>
  </metadata>
 <path d="M 978 788 L 978 814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000249">
 <path d="M 994 814 L 978 814" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111000416_0" Pin1InfoVect0LinkObjId="34000252_1" Pin1InfoVect1LinkObjId="34000251_0" Pin1InfoVect2LinkObjId="34000250_1" Plane="0"/>
  </metadata>
 <path d="M 994 814 L 978 814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000248">
 <path d="M 978 701 L 978 731" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="30000145_0" Pin1InfoVect0LinkObjId="110000391_0" Plane="0"/>
  </metadata>
 <path d="M 978 701 L 978 731" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000239">
 <path d="M 858 701 L 858 731" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="30000145_0" Pin1InfoVect0LinkObjId="110000390_0" Plane="0"/>
  </metadata>
 <path d="M 858 701 L 858 731" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000240">
 <path d="M 874 814 L 858 814" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111000415_0" Pin1InfoVect0LinkObjId="34000242_0" Pin1InfoVect1LinkObjId="34000243_1" Pin1InfoVect2LinkObjId="34000241_1" Plane="0"/>
  </metadata>
 <path d="M 874 814 L 858 814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000241">
 <path d="M 858 788 L 858 814" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000390_1" Pin1InfoVect0LinkObjId="34000242_0" Pin1InfoVect1LinkObjId="34000243_1" Pin1InfoVect2LinkObjId="34000240_1" Plane="0"/>
  </metadata>
 <path d="M 858 788 L 858 814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000242">
 <path d="M 858 814 L 858 896" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000240_1" Pin0InfoVect1LinkObjId="34000241_1" Pin0InfoVect2LinkObjId="34000243_1" Pin1InfoVect0LinkObjId="36000335_0" Plane="0"/>
  </metadata>
 <path d="M 858 814 L 858 896" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000243">
 <path d="M 833 814 L 858 814" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133000440_0" Pin1InfoVect0LinkObjId="34000242_0" Pin1InfoVect1LinkObjId="34000240_1" Pin1InfoVect2LinkObjId="34000241_1" Plane="0"/>
  </metadata>
 <path d="M 833 814 L 858 814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000234">
 <path d="M 713 814 L 738 814" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133000438_0" Pin1InfoVect0LinkObjId="34000231_1" Pin1InfoVect1LinkObjId="34000232_1" Pin1InfoVect2LinkObjId="34000233_0" Plane="0"/>
  </metadata>
 <path d="M 713 814 L 738 814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000233">
 <path d="M 738 814 L 738 896" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000234_1" Pin0InfoVect1LinkObjId="34000231_1" Pin0InfoVect2LinkObjId="34000232_1" Pin1InfoVect0LinkObjId="36000334_0" Plane="0"/>
  </metadata>
 <path d="M 738 814 L 738 896" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000232">
 <path d="M 738 788 L 738 814" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000389_1" Pin1InfoVect0LinkObjId="34000234_1" Pin1InfoVect1LinkObjId="34000233_0" Pin1InfoVect2LinkObjId="34000231_1" Plane="0"/>
  </metadata>
 <path d="M 738 788 L 738 814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000231">
 <path d="M 754 814 L 738 814" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111000414_0" Pin1InfoVect0LinkObjId="34000234_1" Pin1InfoVect1LinkObjId="34000233_0" Pin1InfoVect2LinkObjId="34000232_1" Plane="0"/>
  </metadata>
 <path d="M 754 814 L 738 814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000230">
 <path d="M 738 701 L 738 731" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="30000145_0" Pin1InfoVect0LinkObjId="110000389_0" Plane="0"/>
  </metadata>
 <path d="M 738 701 L 738 731" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000225">
 <path d="M 593 814 L 618 814" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133000436_0" Pin1InfoVect0LinkObjId="34000222_1" Pin1InfoVect1LinkObjId="34000224_0" Pin1InfoVect2LinkObjId="34000223_1" Plane="0"/>
  </metadata>
 <path d="M 593 814 L 618 814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000224">
 <path d="M 618 814 L 618 845" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000222_1" Pin0InfoVect1LinkObjId="34000223_1" Pin0InfoVect2LinkObjId="34000225_1" Pin1InfoVect0LinkObjId="102000540_0" Plane="0"/>
  </metadata>
 <path d="M 618 814 L 618 845" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000223">
 <path d="M 618 788 L 618 814" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000388_1" Pin1InfoVect0LinkObjId="34000222_1" Pin1InfoVect1LinkObjId="34000224_0" Pin1InfoVect2LinkObjId="34000225_1" Plane="0"/>
  </metadata>
 <path d="M 618 788 L 618 814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000222">
 <path d="M 634 814 L 618 814" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111000413_0" Pin1InfoVect0LinkObjId="34000223_1" Pin1InfoVect1LinkObjId="34000224_0" Pin1InfoVect2LinkObjId="34000225_1" Plane="0"/>
  </metadata>
 <path d="M 634 814 L 618 814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000221">
 <path d="M 618 701 L 618 731" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="30000145_0" Pin1InfoVect0LinkObjId="110000388_0" Plane="0"/>
  </metadata>
 <path d="M 618 701 L 618 731" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000220">
 <path d="M 1467 673 L 1467 701" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000385_1" Pin1InfoVect0LinkObjId="30000146_0" Plane="0"/>
  </metadata>
 <path d="M 1467 673 L 1467 701" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000219">
 <path d="M 1467 615 L 1467 645" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="107000381_0" Pin1InfoVect0LinkObjId="110000385_0" Plane="0"/>
  </metadata>
 <path d="M 1467 615 L 1467 645" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000218">
 <path d="M 933 659 L 933 701" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000384_1" Pin1InfoVect0LinkObjId="30000145_0" Plane="0"/>
  </metadata>
 <path d="M 933 659 L 933 701" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000217">
 <path d="M 933 603 L 933 631" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="107000380_0" Pin1InfoVect0LinkObjId="110000384_0" Plane="0"/>
  </metadata>
 <path d="M 933 603 L 933 631" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000216">
 <path d="M 1669 547 L 1669 575" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="102000376_1" Pin1InfoVect0LinkObjId="110000383_0" Plane="0"/>
  </metadata>
 <path d="M 1669 547 L 1669 575" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000215">
 <path d="M 1532 403 L 1532 415" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000213_1" Pin0InfoVect1LinkObjId="34000214_1" Pin1InfoVect0LinkObjId="107000379_0" Plane="0"/>
  </metadata>
 <path d="M 1532 403 L 1532 415" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000214">
 <path d="M 1532 387 L 1532 403" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000362_1" Pin1InfoVect0LinkObjId="34000215_0" Pin1InfoVect1LinkObjId="34000213_1" Plane="0"/>
  </metadata>
 <path d="M 1532 387 L 1532 403" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000213">
 <path d="M 1508 403 L 1532 403" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111000412_0" Pin1InfoVect0LinkObjId="34000215_0" Pin1InfoVect1LinkObjId="34000214_1" Plane="0"/>
  </metadata>
 <path d="M 1508 403 L 1532 403" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000212">
 <path d="M 1532 345 L 1532 357" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000211_1" Pin0InfoVect1LinkObjId="34000210_0" Pin1InfoVect0LinkObjId="101000362_0" Plane="0"/>
  </metadata>
 <path d="M 1532 345 L 1532 357" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000211">
 <path d="M 1532 324 L 1532 345" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="30000144_0" Pin1InfoVect0LinkObjId="34000210_0" Pin1InfoVect1LinkObjId="34000212_0" Plane="0"/>
  </metadata>
 <path d="M 1532 324 L 1532 345" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000210">
 <path d="M 1532 345 L 1506 345" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000211_1" Pin0InfoVect1LinkObjId="34000212_0" Pin1InfoVect0LinkObjId="111000411_0" Plane="0"/>
  </metadata>
 <path d="M 1532 345 L 1506 345" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000209">
 <path d="M 1007 398 L 1007 410" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000208_1" Pin0InfoVect1LinkObjId="34000207_1" Pin1InfoVect0LinkObjId="107000378_0" Plane="0"/>
  </metadata>
 <path d="M 1007 398 L 1007 410" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000208">
 <path d="M 1007 382 L 1007 398" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000361_1" Pin1InfoVect0LinkObjId="34000207_1" Pin1InfoVect1LinkObjId="34000209_0" Plane="0"/>
  </metadata>
 <path d="M 1007 382 L 1007 398" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000207">
 <path d="M 983 398 L 1007 398" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111000410_0" Pin1InfoVect0LinkObjId="34000208_1" Pin1InfoVect1LinkObjId="34000209_0" Plane="0"/>
  </metadata>
 <path d="M 983 398 L 1007 398" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000206">
 <path d="M 1007 340 L 1007 352" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000205_1" Pin0InfoVect1LinkObjId="34000204_0" Pin1InfoVect0LinkObjId="101000361_0" Plane="0"/>
  </metadata>
 <path d="M 1007 340 L 1007 352" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000205">
 <path d="M 1007 324 L 1007 340" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="30000143_0" Pin1InfoVect0LinkObjId="34000204_0" Pin1InfoVect1LinkObjId="34000206_0" Plane="0"/>
  </metadata>
 <path d="M 1007 324 L 1007 340" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000204">
 <path d="M 1007 340 L 981 340" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000205_1" Pin0InfoVect1LinkObjId="34000206_0" Pin1InfoVect0LinkObjId="111000409_0" Plane="0"/>
  </metadata>
 <path d="M 1007 340 L 981 340" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000201">
 <path d="M 1669 382 L 1669 404" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000360_1" Pin1InfoVect0LinkObjId="100000350_0" Plane="0"/>
  </metadata>
 <path d="M 1669 382 L 1669 404" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000202">
 <path d="M 1669 435 L 1669 478" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000350_1" Pin1InfoVect0LinkObjId="102000376_0" Plane="0"/>
  </metadata>
 <path d="M 1669 435 L 1669 478" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000203">
 <path d="M 1669 352 L 1669 324" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000360_0" Pin1InfoVect0LinkObjId="30000144_0" Plane="0"/>
  </metadata>
 <path d="M 1669 352 L 1669 324" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000200">
 <path d="M 1197 404 L 1197 404 L 1197 376" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000199_1" Pin1InfoVect0LinkObjId="101000358_1" Plane="0"/>
  </metadata>
 <path d="M 1197 404 L 1197 404 L 1197 376" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000199">
 <path d="M 1244 404 L 1197 404" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000349_0" Pin1InfoVect0LinkObjId="34000200_0" Plane="0"/>
  </metadata>
 <path d="M 1244 404 L 1197 404" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000197">
 <path d="M 1327 404 L 1327 404 L 1327 376" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000196_1" Pin1InfoVect0LinkObjId="101000359_1" Plane="0"/>
  </metadata>
 <path d="M 1327 404 L 1327 404 L 1327 376" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000196">
 <path d="M 1274 404 L 1327 404" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000349_1" Pin1InfoVect0LinkObjId="34000197_0" Plane="0"/>
  </metadata>
 <path d="M 1274 404 L 1327 404" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000194">
 <path d="M 1328 344 L 1328 324" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000359_0" Pin1InfoVect0LinkObjId="30000144_0" Plane="0"/>
  </metadata>
 <path d="M 1328 344 L 1328 324" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000193">
 <path d="M 1197 324 L 1197 344" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="30000143_0" Pin1InfoVect0LinkObjId="101000358_0" Plane="0"/>
  </metadata>
 <path d="M 1197 324 L 1197 344" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000151">
 <path d="M 754 354 L 754 324" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000353_0" Pin1InfoVect0LinkObjId="30000143_0" Plane="0"/>
  </metadata>
 <path d="M 754 354 L 754 324" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000152">
 <path d="M 754 384 L 754 415" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000353_1" Pin1InfoVect0LinkObjId="100000347_0" Plane="0"/>
  </metadata>
 <path d="M 754 384 L 754 415" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000153">
 <path d="M 754 446 L 754 485" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000347_1" Pin1InfoVect0LinkObjId="102000375_0" Plane="0"/>
  </metadata>
 <path d="M 754 446 L 754 485" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000154">
 <path d="M 754 554 L 754 577" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="102000375_1" Pin1InfoVect0LinkObjId="110000382_0" Plane="0"/>
  </metadata>
 <path d="M 754 554 L 754 577" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000156">
 <path d="M 1335 689 L 1335 701" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000386_1" Pin1InfoVect0LinkObjId="30000146_0" Plane="0"/>
  </metadata>
 <path d="M 1335 689 L 1335 701" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000488">
 <path d="M 797 310 L 797 324" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000500_1" Pin1InfoVect0LinkObjId="30000143_0" Plane="0"/>
  </metadata>
 <path d="M 797 310 L 797 324" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000490">
 <path d="M 797 140 L 797 153" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000493_1" Pin0InfoVect1LinkObjId="34000527_1" Pin1InfoVect0LinkObjId="34000497_0" Plane="0"/>
  </metadata>
 <path d="M 797 140 L 797 153" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000491">
 <path d="M 797 260 L 797 257" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000499_1" Pin1InfoVect0LinkObjId="34000492_0" Plane="0"/>
  </metadata>
 <path d="M 797 260 L 797 257" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000492">
 <path d="M 797 257 L 797 278" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000491_1" Pin1InfoVect0LinkObjId="101000500_0" Plane="0"/>
  </metadata>
 <path d="M 797 257 L 797 278" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000493">
 <path d="M 767 140 L 797 140" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="131000504_0" Pin1InfoVect0LinkObjId="34000490_0" Pin1InfoVect1LinkObjId="34000527_1" Plane="0"/>
  </metadata>
 <path d="M 767 140 L 797 140" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000494">
 <path d="M 720 140 L 744 140" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="107000502_0" Pin1InfoVect0LinkObjId="131000504_1" Plane="0"/>
  </metadata>
 <path d="M 720 140 L 744 140" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000495">
 <path d="M 797 216 L 797 229" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000501_1" Pin1InfoVect0LinkObjId="100000499_0" Plane="0"/>
  </metadata>
 <path d="M 797 216 L 797 229" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000496">
 <path d="M 766 170 L 797 170" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111000503_0" Pin1InfoVect0LinkObjId="34000497_1" Pin1InfoVect1LinkObjId="34000498_0" Plane="0"/>
  </metadata>
 <path d="M 766 170 L 797 170" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000497">
 <path d="M 797 153 L 797 170" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000490_1" Pin1InfoVect0LinkObjId="34000496_1" Pin1InfoVect1LinkObjId="34000498_0" Plane="0"/>
  </metadata>
 <path d="M 797 153 L 797 170" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000498">
 <path d="M 797 170 L 797 184" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000496_1" Pin0InfoVect1LinkObjId="34000497_1" Pin1InfoVect0LinkObjId="101000501_0" Plane="0"/>
  </metadata>
 <path d="M 797 170 L 797 184" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000508">
 <path d="M 773 105 L 797 105" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133000505_0" Pin1InfoVect0LinkObjId="34000509_1" Pin1InfoVect1LinkObjId="34000526_0" Plane="0"/>
  </metadata>
 <path d="M 773 105 L 797 105" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000509">
 <path d="M 797 87 L 797 105" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin1InfoVect0LinkObjId="34000508_1" Pin1InfoVect1LinkObjId="34000526_0" Plane="0"/>
  </metadata>
 <path d="M 797 87 L 797 105" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000524">
 <path d="M 913 167 L 913 185" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="131000514_1" Pin1InfoVect0LinkObjId="102000516_0" Plane="0"/>
  </metadata>
 <path d="M 913 167 L 913 185" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000525">
 <path d="M 797 123 L 910 123 L 910 144" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000526_1" Pin0InfoVect1LinkObjId="34000527_0" Pin1InfoVect0LinkObjId="131000514_0" Plane="0"/>
  </metadata>
 <path d="M 797 123 L 910 123 L 910 144" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000526">
 <path d="M 797 105 L 797 123" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000509_1" Pin0InfoVect1LinkObjId="34000508_1" Pin1InfoVect0LinkObjId="34000525_0" Pin1InfoVect1LinkObjId="34000527_0" Plane="0"/>
  </metadata>
 <path d="M 797 105 L 797 123" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000527">
 <path d="M 797 123 L 797 140" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000525_0" Pin0InfoVect1LinkObjId="34000526_1" Pin1InfoVect0LinkObjId="34000493_1" Pin1InfoVect1LinkObjId="34000490_0" Plane="0"/>
  </metadata>
 <path d="M 797 123 L 797 140" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000534">
 <path d="M 735 666 L 754 666" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133000529_0" Pin1InfoVect0LinkObjId="34000536_0" Pin1InfoVect1LinkObjId="34000535_1" Plane="0"/>
  </metadata>
 <path d="M 735 666 L 754 666" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000535">
 <path d="M 754 642 L 754 666" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000382_1" Pin1InfoVect0LinkObjId="34000536_0" Pin1InfoVect1LinkObjId="34000534_1" Plane="0"/>
  </metadata>
 <path d="M 754 642 L 754 666" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000536">
 <path d="M 754 666 L 754 701" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000534_1" Pin0InfoVect1LinkObjId="34000535_1" Pin1InfoVect0LinkObjId="30000145_0" Plane="0"/>
  </metadata>
 <path d="M 754 666 L 754 701" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000537">
 <path d="M 1642 663 L 1669 663" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133000530_0" Pin1InfoVect0LinkObjId="34000538_1" Pin1InfoVect1LinkObjId="34000539_0" Plane="0"/>
  </metadata>
 <path d="M 1642 663 L 1669 663" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000538">
 <path d="M 1669 640 L 1669 663" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000383_1" Pin1InfoVect0LinkObjId="34000537_1" Pin1InfoVect1LinkObjId="34000539_0" Plane="0"/>
  </metadata>
 <path d="M 1669 640 L 1669 663" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000539">
 <path d="M 1669 663 L 1669 701" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000537_1" Pin0InfoVect1LinkObjId="34000538_1" Pin1InfoVect0LinkObjId="30000146_0" Plane="0"/>
  </metadata>
 <path d="M 1669 663 L 1669 701" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="MeasurementValue_Layer">
 <g id="33000147">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="26" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="229" xml:space="preserve" y="571">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000148">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="26" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="230" xml:space="preserve" y="622">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
</g>
<g id="Text_Layer">
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="56" font-size="24" font-width="24" stroke="rgb(0,0,0)" writing-mode="lr" x="133" xml:space="preserve" y="111">35kV勐养变电站</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(230,232,254)" writing-mode="lr" x="1769" xml:space="preserve" y="991">备用线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="15" font-size="15" font-width="15" stroke="rgb(255,255,254)" writing-mode="lr" x="1839" xml:space="preserve" y="840">05867</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="1833" xml:space="preserve" y="769">058</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="1713" xml:space="preserve" y="769">056</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="15" font-size="15" font-width="15" stroke="rgb(255,255,254)" writing-mode="lr" x="1719" xml:space="preserve" y="840">05667</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(230,232,254)" writing-mode="lr" x="1649" xml:space="preserve" y="991">备用线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(230,232,254)" writing-mode="lr" x="1529" xml:space="preserve" y="991">备用线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="15" font-size="15" font-width="15" stroke="rgb(255,255,254)" writing-mode="lr" x="1599" xml:space="preserve" y="840">05467</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="1593" xml:space="preserve" y="769">054</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="1473" xml:space="preserve" y="769">052</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="15" font-size="15" font-width="15" stroke="rgb(255,255,254)" writing-mode="lr" x="1479" xml:space="preserve" y="840">05267</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(230,232,254)" writing-mode="lr" x="1421" xml:space="preserve" y="992">备用线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="16" font-width="16" stroke="rgb(230,232,254)" writing-mode="lr" x="1298" xml:space="preserve" y="991">曼伞 七队线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="15" font-size="15" font-width="15" stroke="rgb(255,255,254)" writing-mode="lr" x="1359" xml:space="preserve" y="840">05067</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="1353" xml:space="preserve" y="769">050</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="19" font-size="15" font-width="15" stroke="rgb(230,232,254)" writing-mode="lr" x="1150" xml:space="preserve" y="990">医院 农场机关线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="15" font-size="15" font-width="15" stroke="rgb(255,255,254)" writing-mode="lr" x="1239" xml:space="preserve" y="840">05167</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="1233" xml:space="preserve" y="769">051</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="1113" xml:space="preserve" y="769">053</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="15" font-size="15" font-width="15" stroke="rgb(255,255,254)" writing-mode="lr" x="1119" xml:space="preserve" y="840">05367</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(230,232,254)" writing-mode="lr" x="1078" xml:space="preserve" y="991">备用线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(230,232,254)" writing-mode="lr" x="958" xml:space="preserve" y="991">备用线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="15" font-size="15" font-width="15" stroke="rgb(255,255,254)" writing-mode="lr" x="999" xml:space="preserve" y="840">05567</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="993" xml:space="preserve" y="769">055</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="873" xml:space="preserve" y="769">057</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="15" font-size="15" font-width="15" stroke="rgb(255,255,254)" writing-mode="lr" x="879" xml:space="preserve" y="840">05767</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(230,232,254)" writing-mode="lr" x="838" xml:space="preserve" y="991">三队线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(230,232,254)" writing-mode="lr" x="696" xml:space="preserve" y="991">兴洪胶厂</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="15" font-size="15" font-width="15" stroke="rgb(255,255,254)" writing-mode="lr" x="759" xml:space="preserve" y="840">05967</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="753" xml:space="preserve" y="769">059</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="15" font-size="15" font-width="15" stroke="rgb(255,255,254)" writing-mode="lr" x="639" xml:space="preserve" y="840">01167</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="633" xml:space="preserve" y="769">011</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="16" font-size="14" font-width="14" stroke="rgb(255,255,254)" writing-mode="lr" x="1250" xml:space="preserve" y="674">0301</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="15" font-width="15" stroke="rgb(255,255,254)" writing-mode="lr" x="1350" xml:space="preserve" y="674">030</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1482" xml:space="preserve" y="669">0901</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1447" xml:space="preserve" y="556">II母TV</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="948" xml:space="preserve" y="655">0901</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="913" xml:space="preserve" y="544">I母TV</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1469" xml:space="preserve" y="433">39020</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1464" xml:space="preserve" y="379">39027</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1542" xml:space="preserve" y="382">3902</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1520" xml:space="preserve" y="530">II母TV</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="944" xml:space="preserve" y="428">39010</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="939" xml:space="preserve" y="374">39017</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1017" xml:space="preserve" y="377">3901</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="995" xml:space="preserve" y="525">I母TV</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1686" xml:space="preserve" y="377">3021</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1687" xml:space="preserve" y="430">320</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1689" xml:space="preserve" y="618">020</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1245" xml:space="preserve" y="393">312</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1339" xml:space="preserve" y="370">3122</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1203" xml:space="preserve" y="370">3121</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="537" xml:space="preserve" y="319">35kVI母</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1759" xml:space="preserve" y="319">35kVII母</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="764" xml:space="preserve" y="379">3011</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="769" xml:space="preserve" y="441">301</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="526" xml:space="preserve" y="696">10kVI母</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="769" xml:space="preserve" y="620">001</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="56" font-size="24" font-width="24" stroke="rgb(0,0,0)" writing-mode="lr" x="153" xml:space="preserve" y="115">35kV勐龙变电站</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="tb" x="65" xml:space="preserve" y="401">单</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,0,0)" writing-mode="lr" x="187" xml:space="preserve" y="412">MW</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="59" xml:space="preserve" y="570">总有功</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="99" xml:space="preserve" y="708">事故总:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="120" xml:space="preserve" y="412">有功:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="225" xml:space="preserve" y="412"> 无功:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,0,0)" writing-mode="lr" x="308" xml:space="preserve" y="412">MVr</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="120" xml:space="preserve" y="458">电流:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,0,0)" writing-mode="lr" x="187" xml:space="preserve" y="458">A</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="237" xml:space="preserve" y="458">电压:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,0,0)" writing-mode="lr" x="308" xml:space="preserve" y="458">kV</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="120" xml:space="preserve" y="508">频率:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,0,0)" writing-mode="lr" x="187" xml:space="preserve" y="508">Hz</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="237" xml:space="preserve" y="508">温度:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,0,0)" writing-mode="lr" x="308" xml:space="preserve" y="509">℃</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="54" xml:space="preserve" y="625">总无功</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="tb" x="65" xml:space="preserve" y="470">位</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="31" xml:space="preserve" y="326">工况</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="31" xml:space="preserve" y="349">退出</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="114" xml:space="preserve" y="326">不</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="103" xml:space="preserve" y="351">变化</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="171" xml:space="preserve" y="340">越限</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="254" xml:space="preserve" y="326">非</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="242" xml:space="preserve" y="351">实测</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="312" xml:space="preserve" y="328">数据</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="312" xml:space="preserve" y="351">封锁</text>
 <text AFMask="39039" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="781" xml:space="preserve" y="50">大腊线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="807" xml:space="preserve" y="304">3711</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="807" xml:space="preserve" y="210">3716</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="812" xml:space="preserve" y="255">371</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="723" xml:space="preserve" y="200">37167</text>
 <text AFMask="39039" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="16" font-size="16" font-width="17" stroke="rgb(255,255,254)" writing-mode="lr" x="868" xml:space="preserve" y="299">35kV站用变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="17" font-size="17" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="747" xml:space="preserve" y="161">3718</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href="农垦电网.other.svg"><rect fill-opacity="0" height="112" stroke-opacity="0" stroke-width="2" width="337" x="29" y="32"/></g>
</g>
</svg>