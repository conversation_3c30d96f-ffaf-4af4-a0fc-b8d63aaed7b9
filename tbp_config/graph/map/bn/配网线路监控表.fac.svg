<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1600" id="thSvg" viewBox="0 0 3350 1600" width="3350">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="Status:bn_不变化颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(0,0,255)" height="1" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_不变化颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(85,0,255)" height="1" stroke="rgb(85,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_越限颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_越限颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:越下限告警_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,0,255)" height="1" stroke="rgb(170,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:越下限告警_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:正常状态显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,127)" height="1" stroke="rgb(255,255,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:正常状态显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(85,0,255)" height="1" stroke="rgb(85,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="版纳_dp" InitShowingPlane="0," fill="rgb(0,0,0)" height="1600" width="3350" x="0" y="0"/>
</g>
<g id="Other_Layer">
 
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="60" x2="1622" y1="257" y2="257"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1713" x2="3297" y1="462" y2="464"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="60" x2="1624" y1="203" y2="203"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1625" x2="1624" y1="202" y2="1542"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="61" x2="60" y1="204" y2="1542"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="300" x2="299" y1="204" y2="1542"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="300" x2="1622" y1="307" y2="307"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1713" x2="3297" y1="1490" y2="1491"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="668" x2="668" y1="204" y2="1542"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="802" x2="802" y1="206" y2="1542"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="968" x2="968" y1="204" y2="1542"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1222" x2="1222" y1="204" y2="1542"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1421" x2="1422" y1="204" y2="1542"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="300" x2="1622" y1="359" y2="358"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="300" x2="1623" y1="410" y2="409"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="300" x2="1622" y1="462" y2="462"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="300" x2="1622" y1="513" y2="513"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="300" x2="1622" y1="565" y2="565"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="300" x2="1623" y1="668" y2="667"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="60" x2="1622" y1="718" y2="719"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="300" x2="1622" y1="771" y2="771"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="300" x2="1623" y1="822" y2="822"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="300" x2="1622" y1="874" y2="874"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="300" x2="1622" y1="925" y2="925"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="60" x2="1622" y1="977" y2="977"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="300" x2="1622" y1="1028" y2="1028"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="60" x2="1622" y1="1080" y2="1080"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="300" x2="1622" y1="1131" y2="1131"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="60" x2="1622" y1="1182" y2="1182"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="304" x2="1623" y1="1234" y2="1234"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="60" x2="1622" y1="1287" y2="1287"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="300" x2="1622" y1="1337" y2="1337"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="60" x2="1622" y1="1390" y2="1390"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="300" x2="1622" y1="1440" y2="1440"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="300" x2="1623" y1="1492" y2="1492"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="60" x2="1623" y1="1543" y2="1543"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1941" x2="3297" y1="309" y2="310"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1713" x2="3297" y1="360" y2="360"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1941" x2="3297" y1="412" y2="411"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1941" x2="3297" y1="1182" y2="1183"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1939" x2="3297" y1="1234" y2="1233"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1939" x2="3297" y1="1285" y2="1284"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1713" x2="3297" y1="1336" y2="1337"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1940" x2="3297" y1="1388" y2="1388"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1939" x2="3297" y1="1439" y2="1439"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1713" x2="3297" y1="257" y2="257"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1713" x2="3297" y1="206" y2="206"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="3297" x2="3298" y1="207" y2="1491"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1714" x2="1714" y1="207" y2="1491"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1938" x2="1937" y1="207" y2="1491"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1938" x2="3297" y1="514" y2="515"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="2308" x2="2309" y1="207" y2="1491"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="2453" x2="2454" y1="207" y2="1491"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="2609" x2="2608" y1="207" y2="1491"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="2900" x2="2899" y1="207" y2="1491"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="3098" x2="3099" y1="207" y2="1491"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1938" x2="3297" y1="565" y2="566"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1938" x2="3297" y1="617" y2="616"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1713" x2="3297" y1="667" y2="667"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1938" x2="3297" y1="720" y2="720"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1713" x2="3297" y1="772" y2="771"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1938" x2="3297" y1="822" y2="821"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1713" x2="3297" y1="875" y2="874"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1938" x2="3297" y1="925" y2="925"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1713" x2="3297" y1="977" y2="978"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1713" x2="3297" y1="1028" y2="1028"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1938" x2="3297" y1="1079" y2="1079"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1713" x2="3297" y1="1131" y2="1132"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="300" x2="1622" y1="616" y2="616"/>
</g>
<g id="Status_Layer">
 <g id="126011758">
  <use class="kv-1" height="40" transform="rotate(0,2458,173) scale(0.857,0.857) translate(380.145,8.86698)" width="60" x="2458" xlink:href="#Status:正常状态显示_0" y="173"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,2458,173) scale(0.857,0.857) translate(380.145,8.86698)" width="60" x="2458" y="173"/></g>
 <g id="126011757">
  <use class="kv-1" height="40" transform="rotate(0,2690,172) scale(0.893,0.893) translate(292.318,0.609184)" width="60" x="2690" xlink:href="#Status:bn_不变化颜色显示_0" y="172"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,2690,172) scale(0.893,0.893) translate(292.318,0.609184)" width="60" x="2690" y="172"/></g>
 <g id="126011756">
  <use class="kv-1" height="40" transform="rotate(0,2924,172) scale(0.896,0.896) translate(309.393,-0.0357285)" width="60" x="2924" xlink:href="#Status:bn_越限颜色显示_0" y="172"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,2924,172) scale(0.896,0.896) translate(309.393,-0.0357285)" width="60" x="2924" y="172"/></g>
 <g id="126011759">
  <use class="kv-1" height="40" transform="rotate(0,3146,171) scale(1,1) translate(-30,-20)" width="60" x="3146" xlink:href="#Status:越下限告警_0" y="171"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,3146,171) scale(1,1) translate(-30,-20)" width="60" x="3146" y="171"/></g>
</g>
<g id="MeasurementValue_Layer">
 <g id="33009464">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1472" xml:space="preserve" y="400">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="116812415982633051" ObjectName="ST=版纳_110kV_江北变/LN=ACLN_10kV江外联I回线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33009463">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1472" xml:space="preserve" y="349">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052502462" ObjectName="版纳_110kV_江北变\版纳_110kV_江北变/10kV\LD_10kV江城联I回线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33009468">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1472" xml:space="preserve" y="297">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="116812415982633055" ObjectName="ST=版纳_110kV_江北变/LN=ACLN_10kV江外联III回线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33009469">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1472" xml:space="preserve" y="452">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="116812415982633049" ObjectName="ST=版纳_110kV_江北变/LN=ACLN_10kV江农联南I回线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33009474">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1472" xml:space="preserve" y="504">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="116812415982633047" ObjectName="ST=版纳_110kV_江北变/LN=ACLN_10kV江农联南II回线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33009479">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1472" xml:space="preserve" y="555">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="116812415982633041" ObjectName="ST=版纳_110kV_江北变/LN=ACLN_10kV江外联II回线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33009484">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1472" xml:space="preserve" y="607">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052502463" ObjectName="版纳_110kV_江北变\版纳_110kV_江北变/10kV\LD_10kV江城联II回线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33009489">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1472" xml:space="preserve" y="659">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="116812415982633037" ObjectName="ST=版纳_110kV_江北变/LN=ACLN_10kV江农联北I回线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33009494">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1472" xml:space="preserve" y="710">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="116812415982633035" ObjectName="ST=-1/LN=-1:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33009499">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1472" xml:space="preserve" y="762">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052502724" ObjectName="版纳_110kV_城南变\版纳_110kV_城南变/10kV\LD_10kV北过境线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33009504">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1472" xml:space="preserve" y="813">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052502725" ObjectName="版纳_110kV_城南变\版纳_110kV_城南变/10kV\LD_10kV世纪金源I回线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33009509">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1472" xml:space="preserve" y="865">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052502731" ObjectName="版纳_110kV_城南变\版纳_110kV_城南变/10kV\LD_10kV城曼I回线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33009514">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1472" xml:space="preserve" y="917">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052502732" ObjectName="版纳_110kV_城南变\版纳_110kV_城南变/10kV\LD_10kV城曼II回线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33009519">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1472" xml:space="preserve" y="968">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052502728" ObjectName="版纳_110kV_城南变\版纳_110kV_城南变/10kV\LD_10kV世纪金源II回线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33009524">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1472" xml:space="preserve" y="1020">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052501237" ObjectName="版纳_110kV_曼弄枫变\版纳_110kV_曼弄枫变-10kV\LD_10kV曼果联络I回线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33009529">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1472" xml:space="preserve" y="1072">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052501233" ObjectName="版纳_110kV_曼弄枫变\版纳_110kV_曼弄枫变-10kV\LD_10kV曼果联络II回线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33009534">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1472" xml:space="preserve" y="1123">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052502429" ObjectName="版纳_110kV_嘎栋变\版纳_110kV_嘎栋变-10kV\LD_10kV栋西联络线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33009539">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1472" xml:space="preserve" y="1175">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052502430" ObjectName="版纳_110kV_嘎栋变\版纳_110kV_嘎栋变-10kV\LD_10kV栋西联络II回线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33009544">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1472" xml:space="preserve" y="1226">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="116812330083288426" ObjectName="ST=-1/LN=-1:I" Plane="0"/>
  </metadata>
 </g>
 <g id="33009549">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1472" xml:space="preserve" y="1278">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="116812415982634689" ObjectName="ST=版纳_110kV_勐海变/LN=ACLN_环线Ⅰ回线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33009554">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1472" xml:space="preserve" y="1330">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052501077" ObjectName="版纳_110kV_勐罕变\版纳_110kV_勐罕变-10kV\LD_10kV小磨公路线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33009559">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1472" xml:space="preserve" y="1381">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052501078" ObjectName="版纳_110kV_勐罕变\版纳_110kV_勐罕变-10kV\LD_10kV傣族园线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33009564">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1472" xml:space="preserve" y="1433">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795528455" ObjectName="122723089845855495:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33009569">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1472" xml:space="preserve" y="1485">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795528468" ObjectName="122723089845855508:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33009574">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1472" xml:space="preserve" y="1536">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795528481" ObjectName="122723089845855521:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33009579">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3147" xml:space="preserve" y="302">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052502564" ObjectName="版纳_110kV_勐仑变\版纳_110kV_勐仑变/10kV\LD_10kV曼仑线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33009584">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3147" xml:space="preserve" y="354">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052502566" ObjectName="版纳_110kV_勐仑变\版纳_110kV_勐仑变/10kV\LD_10kV城子寨线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33009589">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3147" xml:space="preserve" y="405">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052502437" ObjectName="版纳_110kV_勐龙变\版纳_110kV_勐龙变-10kV\LD_10kV勐龙政府线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33009594">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3147" xml:space="preserve" y="457">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052502438" ObjectName="版纳_110kV_勐龙变\版纳_110kV_勐龙变-10kV\LD_10kV曼伞铁矿线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33009599">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3147" xml:space="preserve" y="1172">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052502807" ObjectName="版纳_35kV_城西变\版纳_35kV_城西变/10kV\LD_10kV栋西联络II回线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33009604">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3147" xml:space="preserve" y="1224">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052502808" ObjectName="版纳_35kV_城西变\版纳_35kV_城西变/10kV\LD_10kV西体联络线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33009609">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3147" xml:space="preserve" y="1276">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052502809" ObjectName="版纳_35kV_城西变\版纳_35kV_城西变/10kV\LD_10kV栋西联络线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33009614">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3147" xml:space="preserve" y="1327">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052502811" ObjectName="版纳_35kV_城西变\版纳_35kV_城西变/10kV\LD_10kV城南照明线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33009619">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3147" xml:space="preserve" y="1379">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052502528" ObjectName="版纳_35kV_城中变\版纳_35kV_城中变/10kV\LD_10kV城北照明线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33009624">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3147" xml:space="preserve" y="1430">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052502529" ObjectName="版纳_35kV_城中变\版纳_35kV_城中变/10kV\LD_10kV傣医院线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33009629">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3147" xml:space="preserve" y="1482">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052502523" ObjectName="版纳_35kV_城中变\版纳_35kV_城中变/10kV\LD_10kV城北电炉线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33011664">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3147" xml:space="preserve" y="605">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052502517" ObjectName="版纳_35kV_城中变\版纳_35kV_城中变/10kV\LD_10kV市医院线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33011663">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3147" xml:space="preserve" y="554">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052502521" ObjectName="版纳_35kV_城中变\版纳_35kV_城中变/10kV\LD_10kV中曼线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33011665">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3147" xml:space="preserve" y="502">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052502522" ObjectName="版纳_35kV_城中变\版纳_35kV_城中变/10kV\LD_10kV辉中Ⅱ回线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33011666">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3147" xml:space="preserve" y="657">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052502526" ObjectName="版纳_35kV_城中变\版纳_35kV_城中变/10kV\LD_10kV中小线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33011667">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3147" xml:space="preserve" y="708">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052501463" ObjectName="版纳_35kV_基诺变\版纳_35kV_基诺变/10kV\LD_10kV基诺曼戈龙线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33011668">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3147" xml:space="preserve" y="760">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052501464" ObjectName="版纳_35kV_基诺变\版纳_35kV_基诺变/10kV\LD_10kV勐腊线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33011669">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3147" xml:space="preserve" y="811">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052501452" ObjectName="版纳_35kV_勐养变\版纳_35kV_勐养变-10kV\LD_10kV曼养联络线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33011670">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3147" xml:space="preserve" y="863">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052501456" ObjectName="版纳_35kV_勐养变\版纳_35kV_勐养变-10kV\LD_10kV思小线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33011671">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3147" xml:space="preserve" y="915">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052501470" ObjectName="版纳_35kV_茶园变\版纳_35kV_茶园变-10kV\LD_10kV昆罕线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33011672">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3147" xml:space="preserve" y="966">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="116812330083287285" ObjectName="ST=版纳_35kV_茶园变/LN=ACLN_10kV岗茶联络线:I" Plane="0"/>
  </metadata>
 </g>
 <g id="33011673">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3147" xml:space="preserve" y="1018">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052502385" ObjectName="版纳_35kV_普文变\版纳_35kV_普文变10kV\LD_10kV咖啡厂线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33011674">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3147" xml:space="preserve" y="1069">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052501417" ObjectName="版纳_35kV_景哈变\版纳_35kV_景哈变-10kV\LD_10kV戈牛I回线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33011675">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3147" xml:space="preserve" y="1121">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052501418" ObjectName="版纳_35kV_景哈变\版纳_35kV_景哈变-10kV\LD_10kV达西利线:I_A" Plane="0"/>
  </metadata>
 </g>
</g>
<g id="Text_Layer">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="50" font-size="50" font-width="50" stroke="rgb(0,255,255)" writing-mode="lr" x="1543" xml:space="preserve" y="92">配网线路监控表</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="113" xml:space="preserve" y="243">电压等级</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1436" xml:space="preserve" y="243">实时电流（A）</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="339" xml:space="preserve" y="294">10kV江外联III回线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="712" xml:space="preserve" y="294">011</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1297" xml:space="preserve" y="295">550</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="848" xml:space="preserve" y="295">800/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1059" xml:space="preserve" y="294">556</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="339" xml:space="preserve" y="346">10kV江城联 I 回线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="714" xml:space="preserve" y="346">012</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1297" xml:space="preserve" y="346">556</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1059" xml:space="preserve" y="345">556</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="339" xml:space="preserve" y="397">10kV江外联 I 回线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="714" xml:space="preserve" y="397">014</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1297" xml:space="preserve" y="398">556</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1059" xml:space="preserve" y="397">556</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="339" xml:space="preserve" y="449">10kV江农联南 I 回线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="714" xml:space="preserve" y="449">015</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1297" xml:space="preserve" y="449">432</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1059" xml:space="preserve" y="448">455</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="339" xml:space="preserve" y="500">10kV江农联南 II 回线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="714" xml:space="preserve" y="500">016</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1297" xml:space="preserve" y="501">320</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1059" xml:space="preserve" y="500">337</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="714" xml:space="preserve" y="552">018</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1297" xml:space="preserve" y="552">556</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1059" xml:space="preserve" y="551">556</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="714" xml:space="preserve" y="603">019</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1297" xml:space="preserve" y="604">556</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1059" xml:space="preserve" y="603">556</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="714" xml:space="preserve" y="655">021</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1297" xml:space="preserve" y="655">432</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1059" xml:space="preserve" y="654">455</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="714" xml:space="preserve" y="706">023</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1297" xml:space="preserve" y="707">320</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1059" xml:space="preserve" y="706">337</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="714" xml:space="preserve" y="758">059</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1297" xml:space="preserve" y="759">400</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="848" xml:space="preserve" y="759">600/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1059" xml:space="preserve" y="758">400</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="714" xml:space="preserve" y="809">062</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1297" xml:space="preserve" y="810">600</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="848" xml:space="preserve" y="810">600/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1059" xml:space="preserve" y="809">600</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="714" xml:space="preserve" y="861">064</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1297" xml:space="preserve" y="862">560</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="848" xml:space="preserve" y="862">800/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1059" xml:space="preserve" y="861">556</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="714" xml:space="preserve" y="912">077</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1297" xml:space="preserve" y="913">500</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="848" xml:space="preserve" y="913">500/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1059" xml:space="preserve" y="912">0</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="714" xml:space="preserve" y="964">078</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1297" xml:space="preserve" y="965">500</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="848" xml:space="preserve" y="965">500/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1059" xml:space="preserve" y="964">0</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="714" xml:space="preserve" y="1015">054</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1297" xml:space="preserve" y="1016">400</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="848" xml:space="preserve" y="1016">600/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1059" xml:space="preserve" y="1015">531</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="714" xml:space="preserve" y="1067">063</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1297" xml:space="preserve" y="1068">400</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="848" xml:space="preserve" y="1068">600/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1059" xml:space="preserve" y="1067">531</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="714" xml:space="preserve" y="1118">042</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1297" xml:space="preserve" y="1119">400</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="848" xml:space="preserve" y="1119">800/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1059" xml:space="preserve" y="1118">404</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="714" xml:space="preserve" y="1170">081</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1297" xml:space="preserve" y="1171">256</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="848" xml:space="preserve" y="1171">800/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1059" xml:space="preserve" y="1170">269</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="714" xml:space="preserve" y="1221">051</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1297" xml:space="preserve" y="1222">335</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="848" xml:space="preserve" y="1222">800/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1059" xml:space="preserve" y="1221">337</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="714" xml:space="preserve" y="1273">053</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1297" xml:space="preserve" y="1274">335</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="848" xml:space="preserve" y="1274">400/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1059" xml:space="preserve" y="1273">337</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="714" xml:space="preserve" y="1324">052</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1297" xml:space="preserve" y="1326">275</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="848" xml:space="preserve" y="1326">400/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1059" xml:space="preserve" y="1325">278</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="714" xml:space="preserve" y="1376">053</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1297" xml:space="preserve" y="1377">336</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="848" xml:space="preserve" y="1377">400/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1059" xml:space="preserve" y="1376">336</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="714" xml:space="preserve" y="1427">051</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1297" xml:space="preserve" y="1429">335</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="848" xml:space="preserve" y="1429">800/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1059" xml:space="preserve" y="1428">337</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="714" xml:space="preserve" y="1479">052</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1297" xml:space="preserve" y="1480">335</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="848" xml:space="preserve" y="1480">800/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1059" xml:space="preserve" y="1479">337</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="714" xml:space="preserve" y="1530">053</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1297" xml:space="preserve" y="1532">460</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="848" xml:space="preserve" y="1532">800/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1059" xml:space="preserve" y="1531">472</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2352" xml:space="preserve" y="295">053</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2977" xml:space="preserve" y="297">265</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2490" xml:space="preserve" y="297">600/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2724" xml:space="preserve" y="296">265</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2352" xml:space="preserve" y="347">054</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2977" xml:space="preserve" y="349">265</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2490" xml:space="preserve" y="349">600/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2724" xml:space="preserve" y="348">265</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2352" xml:space="preserve" y="398">072</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2977" xml:space="preserve" y="400">570</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2490" xml:space="preserve" y="400">600/1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2724" xml:space="preserve" y="399">570</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="105" xml:space="preserve" y="499">110kV江北变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2352" xml:space="preserve" y="450">065</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2977" xml:space="preserve" y="452">570</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2490" xml:space="preserve" y="452">600/1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2724" xml:space="preserve" y="451">570</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2352" xml:space="preserve" y="1170">083</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2977" xml:space="preserve" y="1170">192</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2490" xml:space="preserve" y="1170">200/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2724" xml:space="preserve" y="1170">202</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2352" xml:space="preserve" y="1222">084</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2977" xml:space="preserve" y="1222">556</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2490" xml:space="preserve" y="1222">600/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2724" xml:space="preserve" y="1222">556</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2352" xml:space="preserve" y="1273">085</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2977" xml:space="preserve" y="1273">265</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2490" xml:space="preserve" y="1273">300/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2724" xml:space="preserve" y="1273">265</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2352" xml:space="preserve" y="1325">092</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2977" xml:space="preserve" y="1325">404</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2490" xml:space="preserve" y="1325">400/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2724" xml:space="preserve" y="1325">404</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2352" xml:space="preserve" y="1376">075</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2977" xml:space="preserve" y="1376">265</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2490" xml:space="preserve" y="1376">300/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2724" xml:space="preserve" y="1376">265</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2352" xml:space="preserve" y="1428">078</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2977" xml:space="preserve" y="1428">101</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2490" xml:space="preserve" y="1428">100/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2724" xml:space="preserve" y="1428">101</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2352" xml:space="preserve" y="1479">061</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2977" xml:space="preserve" y="1479">265</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2490" xml:space="preserve" y="1479">300/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2724" xml:space="preserve" y="1479">265</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="374" xml:space="preserve" y="243">线路名称</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="677" xml:space="preserve" y="243">开关编号</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1234" xml:space="preserve" y="243">告警电流（A）</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="841" xml:space="preserve" y="243">CT变比</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="978" xml:space="preserve" y="242">最大允许电流（A）</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="848" xml:space="preserve" y="346">800/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="848" xml:space="preserve" y="398">800/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="848" xml:space="preserve" y="449">800/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="848" xml:space="preserve" y="501">800/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="848" xml:space="preserve" y="552">800/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="848" xml:space="preserve" y="604">800/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="848" xml:space="preserve" y="655">800/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="848" xml:space="preserve" y="707">800/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="1763" xml:space="preserve" y="244">电压等级</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="3107" xml:space="preserve" y="244">实时电流（A）</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2011" xml:space="preserve" y="501">10kV中体 II 回线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2352" xml:space="preserve" y="501">062</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2977" xml:space="preserve" y="501">354</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2490" xml:space="preserve" y="501">400/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2724" xml:space="preserve" y="501">354</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2011" xml:space="preserve" y="553">10kV中曼线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2352" xml:space="preserve" y="552">063</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2977" xml:space="preserve" y="552">404</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2724" xml:space="preserve" y="552">404</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2011" xml:space="preserve" y="605">10kV市医院线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2352" xml:space="preserve" y="604">067</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2977" xml:space="preserve" y="604">202</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2724" xml:space="preserve" y="604">202</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2011" xml:space="preserve" y="656">10kV中小线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2352" xml:space="preserve" y="655">073</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2977" xml:space="preserve" y="655">404</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2724" xml:space="preserve" y="655">404</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2011" xml:space="preserve" y="708">10kV基诺曼戈龙线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2352" xml:space="preserve" y="707">051</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2977" xml:space="preserve" y="707">200</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2724" xml:space="preserve" y="707">211</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2011" xml:space="preserve" y="759">10kV勐腊线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2352" xml:space="preserve" y="758">052</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2977" xml:space="preserve" y="758">253</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2724" xml:space="preserve" y="758">253</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2011" xml:space="preserve" y="810">10kV曼养联络线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2352" xml:space="preserve" y="810">061</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2977" xml:space="preserve" y="810">168</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2724" xml:space="preserve" y="810">168</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2011" xml:space="preserve" y="862">10kV思小线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2352" xml:space="preserve" y="861">065</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2977" xml:space="preserve" y="861">240</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2724" xml:space="preserve" y="861">253</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2011" xml:space="preserve" y="914">10kV昆罕线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2352" xml:space="preserve" y="913">073</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2977" xml:space="preserve" y="913">148</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2724" xml:space="preserve" y="913">148</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2011" xml:space="preserve" y="965">10kV岗茶联络线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2352" xml:space="preserve" y="964">074</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2977" xml:space="preserve" y="964">152</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2490" xml:space="preserve" y="964">200/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2724" xml:space="preserve" y="964">152</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2011" xml:space="preserve" y="1017">10kV咖啡厂线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2352" xml:space="preserve" y="1016">081</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2977" xml:space="preserve" y="1016">192</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2490" xml:space="preserve" y="1016">300/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2738" xml:space="preserve" y="1016">0</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2011" xml:space="preserve" y="1069">10kV戈牛 I 回线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2352" xml:space="preserve" y="1067">051</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2977" xml:space="preserve" y="1067">188</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2490" xml:space="preserve" y="1067">200/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2724" xml:space="preserve" y="1067">198</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2011" xml:space="preserve" y="1118">10kV达西利线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2352" xml:space="preserve" y="1119">052</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2977" xml:space="preserve" y="1119">188</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2490" xml:space="preserve" y="1119">200/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2724" xml:space="preserve" y="1119">198</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="1755" xml:space="preserve" y="579">35kV城中变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2036" xml:space="preserve" y="244">线路名称</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2327" xml:space="preserve" y="244">开关编号</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2910" xml:space="preserve" y="244">告警电流（A）</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2477" xml:space="preserve" y="244">CT变比</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2630" xml:space="preserve" y="244">最大允许电流（A）</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2490" xml:space="preserve" y="552">400/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2490" xml:space="preserve" y="604">200/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2490" xml:space="preserve" y="655">400/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2490" xml:space="preserve" y="707">200/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2490" xml:space="preserve" y="758">400/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2490" xml:space="preserve" y="810">200/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2490" xml:space="preserve" y="861">400/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2490" xml:space="preserve" y="913">200/5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="97" xml:space="preserve" y="860">110kV城南变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="339" xml:space="preserve" y="552">10kV江外联 II 回线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="339" xml:space="preserve" y="603">10kV江城联 II 回线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="339" xml:space="preserve" y="655">10kV江农联北 I 回线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="339" xml:space="preserve" y="706">10kV江农联北 II 回线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="339" xml:space="preserve" y="757">10kV北过境线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="339" xml:space="preserve" y="809">10kV世纪金源 I 回线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="339" xml:space="preserve" y="860">10kV城曼 I 回线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="339" xml:space="preserve" y="912">10kV城曼 II 回线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="339" xml:space="preserve" y="963">10kV世纪金源 II 回线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="339" xml:space="preserve" y="1015">10kV曼果联络 I 回线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="339" xml:space="preserve" y="1066">10kV曼果联络 II 回线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="339" xml:space="preserve" y="1117">10kV栋西联络线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="339" xml:space="preserve" y="1169">10kV栋西联络 II 线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="339" xml:space="preserve" y="1220">10kV海中联络线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="339" xml:space="preserve" y="1272">10kV环线 I 回线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="339" xml:space="preserve" y="1323">10kV小磨公路专线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="339" xml:space="preserve" y="1375">10kV傣族园线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="339" xml:space="preserve" y="1426">10kV竹蓬线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="339" xml:space="preserve" y="1478">10kV茶厂线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="339" xml:space="preserve" y="1529">10kV岗茶联络线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2011" xml:space="preserve" y="294">10kV曼仑线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2011" xml:space="preserve" y="346">10kV城子寨线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2011" xml:space="preserve" y="397">10kV龙塔联络 I 回线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2011" xml:space="preserve" y="449">10kV龙塔联络 II 回线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2011" xml:space="preserve" y="1172">10kV栋西联络 II 回线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2011" xml:space="preserve" y="1222">10kV西体联络线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2011" xml:space="preserve" y="1274">10kV栋西联络线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2011" xml:space="preserve" y="1324">10kV城南照明线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2011" xml:space="preserve" y="1376">10kV城北照明线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2011" xml:space="preserve" y="1427">10kV傣医院线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2011" xml:space="preserve" y="1478">10kV城北电炉线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="90" xml:space="preserve" y="1040">110kV曼弄枫变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="100" xml:space="preserve" y="1143">110kV嘎栋变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="99" xml:space="preserve" y="1243">110kV勐海变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="102" xml:space="preserve" y="1349">110kV勐罕变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="86" xml:space="preserve" y="1478">110kV大渡岗变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1741" xml:space="preserve" y="322">110kV勐仑变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1749" xml:space="preserve" y="423">110kV勐龙变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="1755" xml:space="preserve" y="1243">35kV城西变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="1755" xml:space="preserve" y="1429">35kV城中变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="1755" xml:space="preserve" y="729">35kV基诺变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="1755" xml:space="preserve" y="835">35kV勐养变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="1755" xml:space="preserve" y="935">35kV茶园变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="1755" xml:space="preserve" y="1092">35kV景哈变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="1755" xml:space="preserve" y="1016">35kV普文变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="39" stroke="rgb(255,255,254)" writing-mode="lr" x="2497" xml:space="preserve" y="188">实时态</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="39" stroke="rgb(255,255,254)" writing-mode="lr" x="2964" xml:space="preserve" y="188">越上限</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="39" stroke="rgb(255,255,254)" writing-mode="lr" x="2734" xml:space="preserve" y="188">不变化</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="39" stroke="rgb(255,255,254)" writing-mode="lr" x="3187" xml:space="preserve" y="188">越下限</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href=""><rect fill-opacity="0" height="69" stroke-opacity="0" stroke-width="2" width="370" x="1535" y="36"/></g>
</g>
</svg>