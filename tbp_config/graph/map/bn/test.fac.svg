<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1181" id="thSvg" viewBox="0 0 1417 1181" width="1417">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="-1" InitShowingPlane="0," fill="rgb(0,0,0)" height="1181" width="1417" x="0" y="0"/>
</g>
<g id="Other_Layer">
 
 <rect AFMask="34943" Plane="0" fill="none" height="65" stroke="rgb(0,169,254)" stroke-width="2" transform="rotate(0,837,469)" width="76" x="799" y="437"/>
 <rect AFMask="34943" Plane="0" fill="none" height="67" stroke="rgb(255,255,0)" stroke-width="2" transform="rotate(0,931,305)" width="131" x="866" y="272"/>
 <rect AFMask="34943" Plane="0" fill="none" height="54" stroke="rgb(255,255,0)" stroke-width="2" transform="rotate(0,1244,331)" width="143" x="1173" y="304"/>
 <rect AFMask="34943" Plane="0" fill="none" height="67" stroke="rgb(255,255,0)" stroke-width="2" transform="rotate(0,287,307)" width="131" x="222" y="274"/>
 <rect AFMask="34943" Plane="0" fill="none" height="65" stroke="rgb(0,169,254)" stroke-width="2" transform="rotate(0,198,420)" width="76" x="160" y="388"/>
</g>
<g id="Bus_Layer">
 <g id="30000000">
  <path d="M 112 -21375500 L 328 -21363000" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 112 -21375500 L 328 -21363000" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="30000001">
  <path d="M 112 -21363000 L 328 -202197" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 112 -21363000 L 328 -202197" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="Breaker_Layer">
 <g id="100000000">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 </g>
 <g id="100000001">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 </g>
 <g id="100000002">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 </g>
 <g id="100000003">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 </g>
 <g id="100000004">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 </g>
 <g id="100000005">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 </g>
 <g id="100000006">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 </g>
 <g id="100000007">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 </g>
 <g id="100000008">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 </g>
 <g id="100000009">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 </g>
 <g id="100000010">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 </g>
 <g id="100000011">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 </g>
 <g id="100000012">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 </g>
 <g id="100000013">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 </g>
 <g id="100000014">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 </g>
 <g id="100000015">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 </g>
 <g id="100000016">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 </g>
 <g id="100000017">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 </g>
 <g id="100000018">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 </g>
 <g id="100000019">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 </g>
 <g id="100000020">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 </g>
 <g id="100000021">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 </g>
 <g id="100000022">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 </g>
 <g id="100000023">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 </g>
 <g id="100000024">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 </g>
</g>
<g id="Disconnector_Layer">
 <g id="101000000">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000001">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000002">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000003">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000004">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000005">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000006">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000007">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000008">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000009">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000010">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000011">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000012">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000013">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000014">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000015">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000016">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000017">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000018">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000019">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000020">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000021">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000022">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000023">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000024">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000025">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000026">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000027">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000028">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000029">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000030">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000031">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000032">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000033">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000034">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000035">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000036">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000037">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000038">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000039">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000040">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000041">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000042">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000043">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000044">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000045">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000046">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000047">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000048">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000049">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000050">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000051">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101000052">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
</g>
<g id="GroundDisconnector_Layer">
 <g id="111000000">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000001">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000002">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000003">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000004">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000005">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000006">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000007">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000008">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000009">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000010">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000011">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000012">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000013">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000014">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000015">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000016">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000017">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000018">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000019">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000020">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000021">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000022">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000023">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000024">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000025">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000026">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000027">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000028">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000029">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000030">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000031">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000032">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000033">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000034">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000035">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000036">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000037">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000038">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000039">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="111000040">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
</g>
<g id="Transformer2_Layer">
<g id="102000000">
 <g id="1020000000">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="1020000001">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <metadata>
  <cge:PSR_Ref ObjectID="-1" ObjectName="-1" Plane="0"/>
 </metadata>
</g>
<g id="102000001">
 <g id="1020000010">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="1020000011">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <metadata>
  <cge:PSR_Ref ObjectID="-1" ObjectName="-1" Plane="0"/>
 </metadata>
</g>
<g id="102000002">
 <g id="1020000020">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="1020000021">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <metadata>
  <cge:PSR_Ref ObjectID="-1" ObjectName="-1" Plane="0"/>
 </metadata>
</g>
<g id="102000003">
 <g id="1020000030">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="1020000031">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <metadata>
  <cge:PSR_Ref ObjectID="-1" ObjectName="-1" Plane="0"/>
 </metadata>
</g>
<g id="102000004">
 <g id="1020000040">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="1020000041">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <metadata>
  <cge:PSR_Ref ObjectID="-1" ObjectName="-1" Plane="0"/>
 </metadata>
</g>
<g id="102000005">
 <g id="1020000050">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="1020000051">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <metadata>
  <cge:PSR_Ref ObjectID="-1" ObjectName="-1" Plane="0"/>
 </metadata>
</g>
</g>
<g id="Compensator_Layer">
 <g id="113000000">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="113000001">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="113000002">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="113000003">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
</g>
<g id="Term_Layer">
 <g id="112000000">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="112000001">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="112000002">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="112000003">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="112000004">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="112000005">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="112000006">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="112000007">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="112000008">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="112000009">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="112000010">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="112000011">
  
  <metadata>
   <cge:PSR_Ref AFMask="34943" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
</g>
<g id="Link_Layer">
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="0">
 <path d="M 0 0 L 0 0" stroke="rgb(93,92,88)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34943" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 0 0 L 0 0" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="Text_Layer">
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="30" font-width="30" stroke="rgb(255,255,254)" writing-mode="lr" x="654" xml:space="preserve" y="39">九长线-福田支线</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,254)" writing-mode="lr" x="80" xml:space="preserve" y="460">I</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="170" xml:space="preserve" y="334">#1</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(118,118,118)" writing-mode="lr" x="557" xml:space="preserve" y="514">备用</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(118,118,118)" writing-mode="lr" x="557" xml:space="preserve" y="553">备用</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(118,118,118)" writing-mode="lr" x="557" xml:space="preserve" y="592">备用</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(118,118,118)" writing-mode="lr" x="557" xml:space="preserve" y="631">备用</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(118,118,118)" writing-mode="lr" x="557" xml:space="preserve" y="670">备用</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(118,118,118)" writing-mode="lr" x="557" xml:space="preserve" y="709">备用</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(118,118,118)" writing-mode="lr" x="557" xml:space="preserve" y="748">备用</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(118,118,118)" writing-mode="lr" x="557" xml:space="preserve" y="787">备用</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(118,118,118)" writing-mode="lr" x="557" xml:space="preserve" y="826">备用</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(118,118,118)" writing-mode="lr" x="557" xml:space="preserve" y="865">备用</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(118,118,118)" writing-mode="lr" x="557" xml:space="preserve" y="904">备用</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(118,118,118)" writing-mode="lr" x="557" xml:space="preserve" y="943">备用</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="557" xml:space="preserve" y="1029">5C1</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="557" xml:space="preserve" y="1067">5C2</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(118,118,118)" writing-mode="lr" x="557" xml:space="preserve" y="984">备用</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="562" xml:space="preserve" y="1172">#1站用变</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1188" xml:space="preserve" y="1028">#3电容器</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1194" xml:space="preserve" y="984">52PT</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1190" xml:space="preserve" y="516">墟镇甲线</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1190" xml:space="preserve" y="555">墟镇乙线</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1190" xml:space="preserve" y="594">接仙桥线</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1190" xml:space="preserve" y="633">林场线</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1190" xml:space="preserve" y="672">农村线</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1190" xml:space="preserve" y="711">东洋场线</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1190" xml:space="preserve" y="750">河洞线</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1190" xml:space="preserve" y="789">福横线</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(155,170,180)" writing-mode="lr" x="1190" xml:space="preserve" y="828">备用</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(155,170,180)" writing-mode="lr" x="1190" xml:space="preserve" y="867">备用</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(155,170,180)" writing-mode="lr" x="1190" xml:space="preserve" y="906">备用</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(155,170,180)" writing-mode="lr" x="1190" xml:space="preserve" y="945">备用</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1188" xml:space="preserve" y="1068">#4电容器</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="811" xml:space="preserve" y="303">#2</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="15" font-size="15" font-width="15" stroke="rgb(255,255,254)" writing-mode="lr" x="879" xml:space="preserve" y="138">1T17940</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="15" font-size="15" font-width="15" stroke="rgb(255,255,254)" writing-mode="lr" x="642" xml:space="preserve" y="308">12000</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="732" xml:space="preserve" y="201">1102</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,254)" writing-mode="lr" x="715" xml:space="preserve" y="461">II</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="116" xml:space="preserve" y="1157">至站外电源</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="50" font-size="50" font-width="50" stroke="rgb(255,255,127)" writing-mode="lr" x="1143" xml:space="preserve" y="234">福田站</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="788" xml:space="preserve" y="400">502</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="17" font-size="17" font-width="17" stroke="rgb(255,255,254)" writing-mode="lr" x="797" xml:space="preserve" y="497">C</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="17" font-size="17" font-width="17" stroke="rgb(255,255,254)" writing-mode="lr" x="796" xml:space="preserve" y="478">B</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="17" font-size="17" font-width="17" stroke="rgb(255,255,254)" writing-mode="lr" x="795" xml:space="preserve" y="459">A</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,254)" writing-mode="lr" x="860" xml:space="preserve" y="304">档位</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,254)" writing-mode="lr" x="860" xml:space="preserve" y="334">油温</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="404" xml:space="preserve" y="450">512</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1188" xml:space="preserve" y="1112">#2接地变</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="17" font-size="17" font-width="17" stroke="rgb(255,255,254)" writing-mode="lr" x="899" xml:space="preserve" y="1096">552</text>
 <text AFMask="34830" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="15" font-size="15" font-width="15" stroke="rgb(255,255,254)" writing-mode="lr" x="677" xml:space="preserve" y="619">P</text>
 <text AFMask="34830" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="15" font-size="15" font-width="15" stroke="rgb(255,255,254)" writing-mode="lr" x="678" xml:space="preserve" y="640">Q</text>
 <text AFMask="34830" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1170" xml:space="preserve" y="331">有功</text>
 <text AFMask="34830" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1170" xml:space="preserve" y="352">无功</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="30" font-width="30" stroke="rgb(255,255,254)" writing-mode="lr" x="87" xml:space="preserve" y="41">九福线</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="17" font-size="17" font-width="17" stroke="rgb(255,255,254)" writing-mode="lr" x="257" xml:space="preserve" y="1096">551</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="546" xml:space="preserve" y="1112">#1接地变</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="17" font-size="17" font-width="17" stroke="rgb(255,255,254)" writing-mode="lr" x="314" xml:space="preserve" y="1009">5C1</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="17" font-size="17" font-width="17" stroke="rgb(255,255,254)" writing-mode="lr" x="314" xml:space="preserve" y="1048">5C2</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="17" font-size="17" font-width="17" stroke="rgb(255,255,254)" writing-mode="lr" x="342" xml:space="preserve" y="500">F1</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,254)" writing-mode="lr" x="216" xml:space="preserve" y="336">油温</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,254)" writing-mode="lr" x="216" xml:space="preserve" y="306">档位</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="17" font-size="17" font-width="17" stroke="rgb(255,255,254)" writing-mode="lr" x="158" xml:space="preserve" y="448">C</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="17" font-size="17" font-width="17" stroke="rgb(255,255,254)" writing-mode="lr" x="157" xml:space="preserve" y="429">B</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="17" font-size="17" font-width="17" stroke="rgb(255,255,254)" writing-mode="lr" x="156" xml:space="preserve" y="410">A</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="15" font-size="15" font-width="15" stroke="rgb(255,255,254)" writing-mode="lr" x="-1" xml:space="preserve" y="310">11000</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="88" xml:space="preserve" y="203">1101</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="17" font-size="17" font-width="17" stroke="rgb(255,255,254)" writing-mode="lr" x="972" xml:space="preserve" y="501">F13</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="17" font-size="17" font-width="17" stroke="rgb(255,255,254)" writing-mode="lr" x="867" xml:space="preserve" y="1009">5C3</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="17" font-size="17" font-width="17" stroke="rgb(255,255,254)" writing-mode="lr" x="973" xml:space="preserve" y="540">F14</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="17" font-size="17" font-width="17" stroke="rgb(255,255,254)" writing-mode="lr" x="973" xml:space="preserve" y="579">F15</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="17" font-size="17" font-width="17" stroke="rgb(255,255,254)" writing-mode="lr" x="973" xml:space="preserve" y="618">F16</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="17" font-size="17" font-width="17" stroke="rgb(255,255,254)" writing-mode="lr" x="973" xml:space="preserve" y="657">F17</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="17" font-size="17" font-width="17" stroke="rgb(255,255,254)" writing-mode="lr" x="973" xml:space="preserve" y="696">F18</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="17" font-size="17" font-width="17" stroke="rgb(255,255,254)" writing-mode="lr" x="973" xml:space="preserve" y="735">F19</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="17" font-size="17" font-width="17" stroke="rgb(255,255,254)" writing-mode="lr" x="973" xml:space="preserve" y="774">F20</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="17" font-size="17" font-width="17" stroke="rgb(255,255,254)" writing-mode="lr" x="973" xml:space="preserve" y="813">F21</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="17" font-size="17" font-width="17" stroke="rgb(255,255,254)" writing-mode="lr" x="973" xml:space="preserve" y="852">F22</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="17" font-size="17" font-width="17" stroke="rgb(255,255,254)" writing-mode="lr" x="973" xml:space="preserve" y="891">F23</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="17" font-size="17" font-width="17" stroke="rgb(255,255,254)" writing-mode="lr" x="973" xml:space="preserve" y="930">F24</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="17" font-size="17" font-width="17" stroke="rgb(255,255,254)" writing-mode="lr" x="867" xml:space="preserve" y="1048">5C4</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="17" font-size="17" font-width="17" stroke="rgb(255,255,254)" writing-mode="lr" x="352" xml:space="preserve" y="1153">510</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href="惠州厂站接线图目录1c.fac.svg"><rect fill-opacity="0" height="91" stroke-opacity="0" stroke-width="1" width="202" x="1142" y="164"/></g>
 <g ChangePicPlane="0," Plane="0" href="HZ_220kV_九潭站.fac.svg"><rect fill-opacity="0" height="41" stroke-opacity="0" stroke-width="1" width="234" x="667" y="6"/></g>
 <g ChangePicPlane="0," Plane="0" href="HZ_220kV_九潭站.fac.svg"><rect fill-opacity="0" height="41" stroke-opacity="0" stroke-width="1" width="234" x="50" y="6"/></g>
</g>
</svg>