<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1039" id="thSvg" viewBox="0 0 1920 1039" width="1920">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="Breaker:0_0" viewBox="0,0,50,50">
 <use Plane="0" x="14" xlink:href="#terminal" y="7"/>
 <use Plane="0" x="14" xlink:href="#terminal" y="38"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="31" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,14,22)" width="16" x="6" y="7"/>
</symbol>
<symbol id="Breaker:0_1" viewBox="0,0,50,50">
 <use Plane="0" x="14" xlink:href="#terminal" y="7"/>
 <use Plane="0" x="14" xlink:href="#terminal" y="38"/>
 <rect AFMask="2147483647" Plane="0" fill="rgb(0,0,255)" height="31" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,15,22)" width="16" x="7" y="7"/>
</symbol>
<symbol id="Fuse:11111_0" viewBox="0,0,100,100">
 <use Plane="0" x="32" xlink:href="#terminal" y="56"/>
 <use Plane="0" x="35" xlink:href="#terminal" y="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="48" x2="33" y1="16" y2="54"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="30" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(22,41,35)" width="14" x="34" y="20"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="24" x2="48" y1="14" y2="14"/>
</symbol>
<symbol id="Arrester:bn_避雷器123_0" viewBox="0,0,16,40">
 <use Plane="0" x="8" xlink:href="#terminal" y="4"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="18" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,8,19)" width="10" x="3" y="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="8" x2="8" y1="28" y2="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="8" x2="8" y1="5" y2="18"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1" x2="15" y1="35" y2="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="4" x2="12" y1="37" y2="37"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="6" x2="10" y1="39" y2="39"/>
 <path AFMask="2147483647" Plane="0" d="M 6 17 L 8 23 L 10 17 Z" fill="none" stroke="rgb(93,92,88)" stroke-width="1"/>
</symbol>
<symbol id="Status:bn_不变化颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(0,0,255)" height="1" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_不变化颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(85,0,255)" height="1" stroke="rgb(85,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Disconnector:bn_刀闸1_0" viewBox="0,0,40,20">
 <use Plane="0" x="32" xlink:href="#terminal" y="10"/>
 <use Plane="0" x="2" xlink:href="#terminal" y="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="29" y1="1" y2="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="29" y1="11" y2="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="4" y1="11" y2="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="18" y2="4"/>
</symbol>
<symbol id="Disconnector:bn_刀闸1_1" viewBox="0,0,40,20">
 <use Plane="0" x="32" xlink:href="#terminal" y="10"/>
 <use Plane="0" x="2" xlink:href="#terminal" y="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="33" y1="11" y2="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="18" y2="4"/>
</symbol>
<symbol id="Disconnector:bn_刀闸3_0" viewBox="0,0,20,40">
 <use Plane="0" x="7" xlink:href="#terminal" y="3"/>
 <use Plane="0" x="7" xlink:href="#terminal" y="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="35" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="7" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="1" x2="15" y1="31" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="18" y1="7" y2="31"/>
</symbol>
<symbol id="Disconnector:bn_刀闸3_1" viewBox="0,0,20,40">
 <use Plane="0" x="7" xlink:href="#terminal" y="3"/>
 <use Plane="0" x="7" xlink:href="#terminal" y="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="35" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="1" x2="15" y1="31" y2="31"/>
</symbol>
<symbol id="PT:bn_电压互感器009_0" viewBox="0,0,50,46">
 <use Plane="0" x="33" xlink:href="#terminal" y="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="25" x2="25" y1="32" y2="32"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="25" x2="25" y1="32" y2="32"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="33" x2="33" y1="18" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="38" x2="38" y1="30" y2="37"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="38" x2="41" y1="30" y2="33"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="38" x2="41" y1="37" y2="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="30" x2="33" y1="21" y2="24"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="33" x2="36" y1="24" y2="21"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="33" x2="33" y1="24" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="26" x2="26" y1="34" y2="38"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="26" x2="29" y1="34" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="23" x2="26" y1="31" y2="34"/>
 <circle AFMask="2147483647" Plane="0" cx="32" cy="24" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="27" cy="32" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="38" cy="32" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
</symbol>
<symbol id="PT:bn_电压互感器11_0" viewBox="0,0,26,40">
 <use Plane="0" x="13" xlink:href="#terminal" y="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="6" x2="6" y1="25" y2="25"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="6" x2="6" y1="25" y2="25"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="13" x2="13" y1="18" y2="3"/>
 <circle AFMask="2147483647" Plane="0" cx="12" cy="24" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="8" cy="31" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="17" cy="31" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
</symbol>
<symbol id="Status:bn_非实测颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,85,0)" height="1" stroke="rgb(170,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_非实测颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,85,0)" height="1" stroke="rgb(170,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_工况退出颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,170,255)" height="1" stroke="rgb(170,170,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_工况退出颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,170,255)" height="1" stroke="rgb(170,170,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸13_0" viewBox="0,0,40,20">
 <use Plane="0" x="5" xlink:href="#terminal" y="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="31" y1="6" y2="18"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="30" x2="27" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="33" y1="8" y2="16"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="35" x2="35" y1="10" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="27" y1="2" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="5" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="9" y1="5" y2="19"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸13_1" viewBox="0,0,40,20">
 <use Plane="0" x="5" xlink:href="#terminal" y="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="31" y1="6" y2="18"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="5" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="33" y1="8" y2="16"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="35" x2="35" y1="10" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="9" y1="5" y2="19"/>
</symbol>
<symbol id="Transformer2:bn_两卷变压器8_0" viewBox="0,0,50,76">
 <use Plane="0" x="26" xlink:href="#terminal" y="3"/>
 <circle AFMask="2147483647" Plane="0" cx="25" cy="21" fill="none" r="18" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="25" x2="33" y1="21" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="26" x2="18" y1="21" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="25" x2="25" y1="21" y2="31"/>
</symbol>
<symbol id="Transformer2:bn_两卷变压器8_1" viewBox="0,0,50,76">
 <use Plane="1" x="26" xlink:href="#terminal" y="72"/>
 <circle AFMask="2147483647" Plane="1" cx="25" cy="53" fill="none" r="18" stroke="rgb(0,0,255)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 25 47 L 16 60 L 35 60 Z" fill="none" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Fuse:bn_熔断器1_0" viewBox="0,0,20,50">
 <use Plane="0" x="10" xlink:href="#terminal" y="3"/>
 <use Plane="0" x="10" xlink:href="#terminal" y="43"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="3" y2="43"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="30" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,10,23)" width="14" x="3" y="8"/>
</symbol>
<symbol id="Fuse:bn_熔断器11_0" viewBox="0,0,32,16">
 <use Plane="0" x="29" xlink:href="#terminal" y="7"/>
 <use Plane="0" x="2" xlink:href="#terminal" y="7"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="8" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,16,8)" width="18" x="7" y="4"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="5" x2="27" y1="8" y2="8"/>
</symbol>
<symbol id="Status:bn_数据封锁颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,85,0)" height="1" stroke="rgb(255,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_数据封锁颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,85,0)" height="1" stroke="rgb(255,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_越限颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_越限颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Terminal:bn_终端设备11_0" viewBox="0,0,50,100">
 <use Plane="0" x="18" xlink:href="#terminal" y="92"/>
 <circle AFMask="2147483647" Plane="0" cx="18" cy="66" fill="none" r="15" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="18" cy="39" fill="none" r="15" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="18" x2="18" y1="27" y2="39"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="18" x2="8" y1="39" y2="45"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="18" x2="28" y1="39" y2="45"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="18" x2="18" y1="55" y2="67"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="18" x2="28" y1="67" y2="73"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="18" x2="8" y1="67" y2="73"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="18" x2="18" y1="24" y2="8"/>
 <polygon AFMask="2147483647" Plane="0" fill="none" points="13,14 18,9 23,14" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="43" y1="39" y2="39"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="40" x2="48" y1="11" y2="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="42" x2="46" y1="9" y2="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="44" x2="44" y1="7" y2="7"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="44" x2="44" y1="39" y2="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="18" x2="44" y1="14" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="18" x2="18" y1="81" y2="92"/>
</symbol>
<symbol id="Terminal:bn_终端设备12_0" viewBox="0,0,50,100">
 <use Plane="0" x="32" xlink:href="#terminal" y="3"/>
 <circle AFMask="2147483647" Plane="0" cx="32" cy="29" fill="none" r="15" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="32" cy="56" fill="none" r="15" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="32" x2="32" y1="56" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="22" x2="32" y1="62" y2="56"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="42" x2="32" y1="62" y2="56"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="22" x2="32" y1="30" y2="36"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="42" x2="32" y1="30" y2="36"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="32" x2="32" y1="71" y2="95"/>
 <polygon AFMask="2147483647" Plane="0" fill="none" points="27,81 32,86 37,81" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="32" x2="7" y1="56" y2="56"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="10" x2="2" y1="84" y2="84"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="4" y1="86" y2="86"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="6" x2="6" y1="88" y2="88"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="6" x2="6" y1="56" y2="84"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="32" x2="6" y1="81" y2="67"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="32" x2="32" y1="14" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="30" x2="34" y1="97" y2="97"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="33" y1="99" y2="99"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="22" x2="42" y1="29" y2="29"/>
</symbol>
<symbol id="PT:gd_两圈互感1_0" viewBox="0,0,18,44">
 <use Plane="0" x="9" xlink:href="#terminal" y="4"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="9" y1="14" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="5" y1="21" y2="17"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="13" y1="22" y2="17"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="13" y1="36" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="5" y1="35" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="9" y1="21" y2="24"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="9" y1="35" y2="39"/>
 <circle AFMask="2147483647" Plane="0" cx="9" cy="33" fill="none" r="8" stroke="rgb(255,255,254)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="2" cy="26" fill="none" r="0" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="9" cy="22" fill="none" r="8" stroke="rgb(255,255,254)" stroke-width="1"/>
</symbol>
<symbol id="PT:gd_组合三圈互感1_0" viewBox="0,0,36,60">
 <use Plane="0" x="8" xlink:href="#terminal" y="53"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="15" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,30,34)" width="8" x="26" y="27"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="14" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,8,36)" width="8" x="4" y="29"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="6" x2="6" y1="19" y2="19"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="6" x2="6" y1="19" y2="19"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="17" x2="17" y1="10" y2="17"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="17" x2="20" y1="10" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="17" x2="20" y1="17" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="5" x2="8" y1="17" y2="20"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="8" x2="11" y1="20" y2="17"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="8" x2="8" y1="20" y2="24"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="8" x2="8" y1="8" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="8" x2="11" y1="8" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="5" x2="8" y1="5" y2="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="30" x2="30" y1="24" y2="27"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="30" x2="30" y1="42" y2="52"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="27" x2="33" y1="24" y2="24"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="28" x2="32" y1="22" y2="22"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="29" x2="31" y1="20" y2="20"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="26" x2="34" y1="30" y2="30"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="26" x2="34" y1="33" y2="33"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="26" x2="34" y1="36" y2="36"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="26" x2="34" y1="39" y2="39"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="8" x2="8" y1="26" y2="52"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="8" x2="30" y1="52" y2="52"/>
 <circle AFMask="2147483647" Plane="0" cx="8" cy="8" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="8" cy="19" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="17" cy="14" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
</symbol>
<symbol id="GZP:gg_光子牌1_0" viewBox="0,0,36,36">
 <circle AFMask="2147483647" Plane="0" cx="18" cy="18" fill="rgb(0,255,0)" r="15" stroke="rgb(0,255,0)" stroke-width="1"/>
</symbol>
<symbol id="GZP:gg_光子牌1_1" viewBox="0,0,36,36">
 <circle AFMask="2147483647" Plane="0" cx="18" cy="18" fill="rgb(255,0,0)" r="15" stroke="rgb(255,0,0)" stroke-width="1"/>
</symbol>
<symbol id="SynchronousMachine:ocs_发电机1_0" viewBox="0,0,50,50">
 <use Plane="0" x="25" xlink:href="#terminal" y="3"/>
 <circle AFMask="2147483647" Plane="0" cx="25" cy="25" fill="none" r="22" stroke="rgb(0,0,255)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="0" d="M 25 25 A 11 9 0 1 0 3 25" fill="none" stroke="rgb(0,0,255)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="0" d="M 25 25 A 11 9 0 1 0 47 25" fill="none" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Arrester:qj_ocs_blq4_0" viewBox="0,0,16,40">
 <use Plane="0" x="8" xlink:href="#terminal" y="36"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="18" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,8,22)" width="10" x="3" y="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="8" x2="8" y1="6" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="8" x2="8" y1="22" y2="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1" x2="15" y1="6" y2="6"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="4" x2="12" y1="4" y2="4"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="6" x2="10" y1="2" y2="2"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="6" x2="10" y1="21" y2="21"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="8" y1="21" y2="18"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="8" x2="6" y1="18" y2="21"/>
</symbol>
<symbol id="PT:qj_ocs线路TV上_0" viewBox="0,0,18,44">
 <use Plane="0" x="9" xlink:href="#terminal" y="40"/>
 <circle AFMask="2147483647" Plane="0" cx="9" cy="21" fill="none" r="8" stroke="rgb(255,255,254)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="9" cy="10" fill="none" r="8" stroke="rgb(255,255,254)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="9" y1="29" y2="39"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="7" x2="13" y1="10" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="7" x2="13" y1="21" y2="21"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="版纳_35kV_大沙坝电站" InitShowingPlane="0," fill="rgb(0,0,0)" height="1039" width="1920" x="0" y="0"/>
</g>
<g id="Other_Layer">
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="17" x2="365" y1="242" y2="242"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="18" x2="366" y1="576" y2="576"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="16" x2="366" y1="365" y2="365"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="16" x2="366" y1="756" y2="756"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="16" x2="366" y1="1011" y2="1011"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="17" x2="365" y1="512" y2="512"/>
 
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="104" x2="104" y1="365" y2="513"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="172" x2="172" y1="519" y2="637"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="104" x2="366" y1="416" y2="416"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="104" x2="366" y1="464" y2="464"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="298" x2="298" y1="241" y2="359"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="230" x2="230" y1="241" y2="359"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="157" x2="157" y1="241" y2="359"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="87" x2="87" y1="241" y2="359"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="18" x2="366" y1="636" y2="636"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1283" x2="1283" y1="787" y2="790"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="17" x2="365" y1="18" y2="18"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="17" x2="365" y1="295" y2="295"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="16" x2="16" y1="18" y2="1008"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="368" x2="368" y1="20" y2="1011"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="17" x2="365" y1="147" y2="147"/>
</g>
<g id="Bus_Layer">
 <g id="30000041">
  <path d="M 519 373 L 1825 373" stroke="rgb(0,0,255)" stroke-width="4"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 519 373 L 1825 373" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="30000048">
  <path d="M 521 721 L 1087 721" stroke="rgb(0,0,255)" stroke-width="4"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 521 721 L 1087 721" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="30000063">
  <path d="M 1235 721 L 1829 721" stroke="rgb(0,0,255)" stroke-width="4"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 1235 721 L 1829 721" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="Breaker_Layer">
 <g id="100000074">
  <use class="kv-1" height="50" transform="rotate(0,841,476) scale(1,1) translate(-14,-23)" width="50" x="841" xlink:href="#Breaker:0_0" y="476"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,841,476) scale(1,1) translate(-14,-23)" width="50" x="841" y="476"/></g>
 <g id="100000116">
  <use class="kv-1" height="50" transform="rotate(0,1483,476) scale(1,1) translate(-14,-23)" width="50" x="1483" xlink:href="#Breaker:0_0" y="476"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1483,476) scale(1,1) translate(-14,-23)" width="50" x="1483" y="476"/></g>
 <g id="100000463">
  <use class="kv-1" height="50" transform="rotate(0,875,261) scale(1,1) translate(-14,-23)" width="50" x="875" xlink:href="#Breaker:0_0" y="261"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,875,261) scale(1,1) translate(-14,-23)" width="50" x="875" y="261"/></g>
</g>
<g id="Disconnector_Layer">
 <g id="101000071">
  <use class="kv-1" height="40" transform="rotate(360,841,672) scale(-1,1) translate(-1690,-19)" width="20" x="841" xlink:href="#Disconnector:bn_刀闸3_0" y="672"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(360,841,672) scale(-1,1) translate(-1690,-19)" width="20" x="841" y="672"/></g>
 <g id="101000078">
  <use class="kv-1" height="40" transform="rotate(360,841,417) scale(-1,1) translate(-1690,-19)" width="20" x="841" xlink:href="#Disconnector:bn_刀闸3_0" y="417"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(360,841,417) scale(-1,1) translate(-1690,-19)" width="20" x="841" y="417"/></g>
 <g id="101000117">
  <use class="kv-1" height="40" transform="rotate(360,1483,672) scale(-1,1) translate(-2974,-19)" width="20" x="1483" xlink:href="#Disconnector:bn_刀闸3_0" y="672"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(360,1483,672) scale(-1,1) translate(-2974,-19)" width="20" x="1483" y="672"/></g>
 <g id="101000118">
  <use class="kv-1" height="40" transform="rotate(360,1483,417) scale(-1,1) translate(-2974,-19)" width="20" x="1483" xlink:href="#Disconnector:bn_刀闸3_0" y="417"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(360,1483,417) scale(-1,1) translate(-2974,-19)" width="20" x="1483" y="417"/></g>
 <g id="101000164">
  <use class="kv-1" height="40" transform="rotate(360,1264,772) scale(-1,1) translate(-2536,-19)" width="20" x="1264" xlink:href="#Disconnector:bn_刀闸3_0" y="772"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(360,1264,772) scale(-1,1) translate(-2536,-19)" width="20" x="1264" y="772"/></g>
 <g id="101000166">
  <use class="kv-1" height="40" transform="rotate(360,1067,771) scale(-1,1) translate(-2142,-19)" width="20" x="1067" xlink:href="#Disconnector:bn_刀闸3_0" y="771"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(360,1067,771) scale(-1,1) translate(-2142,-19)" width="20" x="1067" y="771"/></g>
 <g id="101000222">
  <use class="kv-1" height="40" transform="rotate(360,631,671) scale(-1,1) translate(-1270,-19)" width="20" x="631" xlink:href="#Disconnector:bn_刀闸3_0" y="671"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(360,631,671) scale(-1,1) translate(-1270,-19)" width="20" x="631" y="671"/></g>
 <g id="101000227">
  <use class="kv-1" height="40" transform="rotate(360,874,323) scale(-1,1) translate(-1756,-19)" width="20" x="874" xlink:href="#Disconnector:bn_刀闸3_0" y="323"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(360,874,323) scale(-1,1) translate(-1756,-19)" width="20" x="874" y="323"/></g>
 <g id="101000231">
  <use class="kv-1" height="40" transform="rotate(360,1642,323) scale(-1,1) translate(-3292,-19)" width="20" x="1642" xlink:href="#Disconnector:bn_刀闸3_0" y="323"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(360,1642,323) scale(-1,1) translate(-3292,-19)" width="20" x="1642" y="323"/></g>
 <g id="101000253">
  <use class="kv-1" height="40" transform="rotate(360,1321,671) scale(-1,1) translate(-2650,-19)" width="20" x="1321" xlink:href="#Disconnector:bn_刀闸3_0" y="671"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(360,1321,671) scale(-1,1) translate(-2650,-19)" width="20" x="1321" y="671"/></g>
 <g id="101000467">
  <use class="kv-1" height="40" transform="rotate(360,874,203) scale(-1,1) translate(-1756,-19)" width="20" x="874" xlink:href="#Disconnector:bn_刀闸3_0" y="203"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(360,874,203) scale(-1,1) translate(-1756,-19)" width="20" x="874" y="203"/></g>
 <g id="101000468">
  <use class="kv-1" height="20" transform="rotate(0,945,162) scale(1,1) translate(-18,-11)" width="40" x="945" xlink:href="#Disconnector:bn_刀闸1_0" y="162"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,945,162) scale(1,1) translate(-18,-11)" width="40" x="945" y="162"/></g>
 <g id="101000475">
  <use class="kv-1" height="20" transform="rotate(0,776,164) scale(-1,-1) translate(-1570,-339)" width="40" x="776" xlink:href="#Disconnector:bn_刀闸1_0" y="164"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,776,164) scale(-1,-1) translate(-1570,-339)" width="40" x="776" y="164"/></g>
</g>
<g id="GroundDisconnector_Layer">
 <g id="111000263">
  <use class="kv-1" height="20" transform="rotate(0,1679,285) scale(1,1) translate(-5,-12)" width="40" x="1679" xlink:href="#GroundDisconnector:bn_接地刀闸13_0" y="285"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1679,285) scale(1,1) translate(-5,-12)" width="40" x="1679" y="285"/></g>
</g>
<g id="Generator_Layer">
 <g id="104000120">
  <use class="kv-1" height="50" transform="rotate(0,840,921) scale(1,1) translate(-25,-3)" width="50" x="840" xlink:href="#SynchronousMachine:ocs_发电机1_0" y="921"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,840,921) scale(1,1) translate(-25,-3)" width="50" x="840" y="921"/></g>
 <g id="104000132">
  <use class="kv-1" height="50" transform="rotate(0,1482,921) scale(1,1) translate(-25,-3)" width="50" x="1482" xlink:href="#SynchronousMachine:ocs_发电机1_0" y="921"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1482,921) scale(1,1) translate(-25,-3)" width="50" x="1482" y="921"/></g>
</g>
<g id="Transformer2_Layer">
<g id="102000019">
 <g id="1020000190">
  <use class="kv-1" height="76" transform="rotate(0,842,549) scale(1,1) translate(-26,-38)" width="50" x="842" xlink:href="#Transformer2:bn_两卷变压器8_0" y="549"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="1020000191">
  <use class="kv-1" height="76" transform="rotate(0,842,549) scale(1,1) translate(-26,-38)" width="50" x="842" xlink:href="#Transformer2:bn_两卷变压器8_1" y="549"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <metadata>
  <cge:PSR_Ref ObjectID="-1" ObjectName="-1" Plane="0"/>
 </metadata>
<rect fill="white" height="76" opacity="0" stroke="white" transform="rotate(0,842,549) scale(1,1) translate(-26,-38)" width="50" x="842" y="549"/></g>
<g id="102000119">
 <g id="1020001190">
  <use class="kv-1" height="76" transform="rotate(0,1484,549) scale(1,1) translate(-26,-38)" width="50" x="1484" xlink:href="#Transformer2:bn_两卷变压器8_0" y="549"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="1020001191">
  <use class="kv-1" height="76" transform="rotate(0,1484,549) scale(1,1) translate(-26,-38)" width="50" x="1484" xlink:href="#Transformer2:bn_两卷变压器8_1" y="549"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <metadata>
  <cge:PSR_Ref ObjectID="-1" ObjectName="-1" Plane="0"/>
 </metadata>
<rect fill="white" height="76" opacity="0" stroke="white" transform="rotate(0,1484,549) scale(1,1) translate(-26,-38)" width="50" x="1484" y="549"/></g>
</g>
<g id="ACLineSegment_Layer">
 <g id="36000496">
 <path d="M 874 187 L 874 116" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="ST=-1/LN=-1" ObjectName="ST=-1/LN=-1" Plane="0"/>
   <cge:PSR_Link Pin0InfoVect0LinkObjId="101000467_0"/>
  </metadata>
 <path d="M 874 187 L 874 116" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="PT_Layer">
 <g id="107000198">
  <use class="kv-1" height="40" transform="rotate(0,1647,838) scale(2,2) translate(-836.5,-422)" width="26" x="1647" xlink:href="#PT:bn_电压互感器11_0" y="838"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1647,838) scale(2,2) translate(-836.5,-422)" width="26" x="1647" y="838"/></g>
 <g id="107000201">
  <use class="kv-1" height="44" transform="rotate(0,979,832) scale(2,2) translate(-498.5,-420)" width="18" x="979" xlink:href="#PT:gd_两圈互感1_0" y="832"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="44" opacity="0" stroke="white" transform="rotate(0,979,832) scale(2,2) translate(-498.5,-420)" width="18" x="979" y="832"/></g>
 <g id="107000210">
  <use class="kv-1" height="44" transform="rotate(0,1343,832) scale(2,2) translate(-680.5,-420)" width="18" x="1343" xlink:href="#PT:gd_两圈互感1_0" y="832"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="44" opacity="0" stroke="white" transform="rotate(0,1343,832) scale(2,2) translate(-680.5,-420)" width="18" x="1343" y="832"/></g>
 <g id="107000219">
  <use class="kv-1" height="40" transform="rotate(0,672,838) scale(2,2) translate(-349,-422)" width="26" x="672" xlink:href="#PT:bn_电压互感器11_0" y="838"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,672,838) scale(2,2) translate(-349,-422)" width="26" x="672" y="838"/></g>
 <g id="107000238">
  <use class="kv-1" height="60" transform="rotate(360,632,632) scale(-1.2,1.2) translate(-1166.67,-158.333)" width="36" x="632" xlink:href="#PT:gd_组合三圈互感1_0" y="632"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(360,632,632) scale(-1.2,1.2) translate(-1166.67,-158.333)" width="36" x="632" y="632"/></g>
 <g id="107000241">
  <use class="kv-1" height="44" transform="rotate(96,701,163) scale(1,-1) translate(-9,-367)" width="18" x="701" xlink:href="#PT:qj_ocs线路TV上_0" y="163"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="44" opacity="0" stroke="white" transform="rotate(96,701,163) scale(1,-1) translate(-9,-367)" width="18" x="701" y="163"/></g>
 <g id="107000254">
  <use class="kv-1" height="60" transform="rotate(360,1322,632) scale(-1.2,1.2) translate(-2431.67,-158.333)" width="36" x="1322" xlink:href="#PT:gd_组合三圈互感1_0" y="632"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(360,1322,632) scale(-1.2,1.2) translate(-2431.67,-158.333)" width="36" x="1322" y="632"/></g>
 <g id="107000255">
  <use class="kv-1" height="46" transform="rotate(360,1641,235) scale(1.5,-1.5) translate(-580,-394.667)" width="50" x="1641" xlink:href="#PT:bn_电压互感器009_0" y="235"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="46" opacity="0" stroke="white" transform="rotate(360,1641,235) scale(1.5,-1.5) translate(-580,-394.667)" width="50" x="1641" y="235"/></g>
</g>
<g id="GZP_Layer">
 <g id="135000453">
  <use class="kv-1" height="36" transform="rotate(0,254,694) scale(1,1) translate(-18,-18)" width="36" x="254" xlink:href="#GZP:gg_光子牌1_0" y="694"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,254,694) scale(1,1) translate(-18,-18)" width="36" x="254" y="694"/></g>
</g>
<g id="Arrester_Layer">
 <g id="133000260">
  <use class="kv-1" height="40" transform="rotate(0,1603,265) scale(1,1) translate(-8,-36)" width="16" x="1603" xlink:href="#Arrester:qj_ocs_blq4_0" y="265"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1603,265) scale(1,1) translate(-8,-36)" width="16" x="1603" y="265"/></g>
 <g id="133000476">
  <use class="kv-1" height="40" transform="rotate(0,813,231) scale(1,1) translate(-8,-5)" width="16" x="813" xlink:href="#Arrester:bn_避雷器123_0" y="231"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,813,231) scale(1,1) translate(-8,-5)" width="16" x="813" y="231"/></g>
</g>
<g id="Fuse_Layer">
 <g id="131000171">
  <use class="kv-1" height="50" transform="rotate(0,1264,827) scale(1,1) translate(-10,-23)" width="20" x="1264" xlink:href="#Fuse:bn_熔断器1_0" y="827"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1264,827) scale(1,1) translate(-10,-23)" width="20" x="1264" y="827"/></g>
 <g id="131000172">
  <use class="kv-1" height="50" transform="rotate(0,1065,827) scale(1,1) translate(-10,-23)" width="20" x="1065" xlink:href="#Fuse:bn_熔断器1_0" y="827"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1065,827) scale(1,1) translate(-10,-23)" width="20" x="1065" y="827"/></g>
 <g id="131000204">
  <use class="kv-1" height="50" transform="rotate(0,978,772) scale(1,1) translate(-10,-23)" width="20" x="978" xlink:href="#Fuse:bn_熔断器1_0" y="772"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,978,772) scale(1,1) translate(-10,-23)" width="20" x="978" y="772"/></g>
 <g id="131000211">
  <use class="kv-1" height="50" transform="rotate(0,1342,772) scale(1,1) translate(-10,-23)" width="20" x="1342" xlink:href="#Fuse:bn_熔断器1_0" y="772"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1342,772) scale(1,1) translate(-10,-23)" width="20" x="1342" y="772"/></g>
 <g id="131000212">
  <use class="kv-1" height="50" transform="rotate(0,1647,772) scale(1,1) translate(-10,-23)" width="20" x="1647" xlink:href="#Fuse:bn_熔断器1_0" y="772"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1647,772) scale(1,1) translate(-10,-23)" width="20" x="1647" y="772"/></g>
 <g id="131000220">
  <use class="kv-1" height="50" transform="rotate(0,672,772) scale(1,1) translate(-10,-23)" width="20" x="672" xlink:href="#Fuse:bn_熔断器1_0" y="772"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,672,772) scale(1,1) translate(-10,-23)" width="20" x="672" y="772"/></g>
 <g id="131000479">
  <use class="kv-1" height="16" transform="rotate(0,735,164) scale(1,1) translate(-16,-8)" width="32" x="735" xlink:href="#Fuse:bn_熔断器11_0" y="164"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(0,735,164) scale(1,1) translate(-16,-8)" width="32" x="735" y="164"/></g>
 <g id="131000509">
  <use class="kv-1" height="100" transform="rotate(0,1088,315) scale(-1,-1) translate(-2210,-664)" width="100" x="1088" xlink:href="#Fuse:11111_0" y="315"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="100" opacity="0" stroke="white" transform="rotate(0,1088,315) scale(-1,-1) translate(-2210,-664)" width="100" x="1088" y="315"/></g>
 <g id="131000524">
  <use class="kv-1" height="100" transform="rotate(0,1351,315) scale(-1,-1) translate(-2736,-664)" width="100" x="1351" xlink:href="#Fuse:11111_0" y="315"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="100" opacity="0" stroke="white" transform="rotate(0,1351,315) scale(-1,-1) translate(-2736,-664)" width="100" x="1351" y="315"/></g>
</g>
<g id="Status_Layer">
 <g id="126000448">
  <use class="kv-1" height="40" transform="rotate(0,51,268) scale(0.7,0.7) translate(-8.14285,94.8572)" width="60" x="51" xlink:href="#Status:bn_工况退出颜色显示_0" y="268"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,51,268) scale(0.7,0.7) translate(-8.14285,94.8572)" width="60" x="51" y="268"/></g>
 <g id="126000449">
  <use class="kv-1" height="40" transform="rotate(0,121,268) scale(0.7,0.7) translate(21.8571,94.8572)" width="60" x="121" xlink:href="#Status:bn_不变化颜色显示_0" y="268"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,121,268) scale(0.7,0.7) translate(21.8571,94.8572)" width="60" x="121" y="268"/></g>
 <g id="126000450">
  <use class="kv-1" height="40" transform="rotate(0,191,268) scale(0.7,0.7) translate(51.8571,94.8572)" width="60" x="191" xlink:href="#Status:bn_越限颜色显示_0" y="268"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,191,268) scale(0.7,0.7) translate(51.8571,94.8572)" width="60" x="191" y="268"/></g>
 <g id="126000451">
  <use class="kv-1" height="40" transform="rotate(0,264,268) scale(0.7,0.7) translate(83.1429,94.8572)" width="60" x="264" xlink:href="#Status:bn_非实测颜色显示_0" y="268"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,264,268) scale(0.7,0.7) translate(83.1429,94.8572)" width="60" x="264" y="268"/></g>
 <g id="126000452">
  <use class="kv-1" height="40" transform="rotate(0,331,268) scale(0.7,0.7) translate(111.857,94.8572)" width="60" x="331" xlink:href="#Status:bn_数据封锁颜色显示_0" y="268"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,331,268) scale(0.7,0.7) translate(111.857,94.8572)" width="60" x="331" y="268"/></g>
</g>
<g id="Clock_Layer">
 <g id="56000447">
  
 <metadata/></g>
</g>
<g id="Term_Layer">
 <g id="112000139">
  <use class="kv-1" height="100" transform="rotate(0,1177,893) scale(1,1) translate(-32,-3)" width="50" x="1177" xlink:href="#Terminal:bn_终端设备12_0" y="893"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="100" opacity="0" stroke="white" transform="rotate(0,1177,893) scale(1,1) translate(-32,-3)" width="50" x="1177" y="893"/></g>
 <g id="112000530">
  <use class="kv-1" height="100" transform="rotate(360,1089,236) scale(-1,1) translate(-2196,-92)" width="50" x="1089" xlink:href="#Terminal:bn_终端设备11_0" y="236"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="100" opacity="0" stroke="white" transform="rotate(360,1089,236) scale(-1,1) translate(-2196,-92)" width="50" x="1089" y="236"/></g>
 <g id="112000543">
  <use class="kv-1" height="100" transform="rotate(360,1352,236) scale(-1,1) translate(-2722,-92)" width="50" x="1352" xlink:href="#Terminal:bn_终端设备11_0" y="236"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="100" opacity="0" stroke="white" transform="rotate(360,1352,236) scale(-1,1) translate(-2722,-92)" width="50" x="1352" y="236"/></g>
</g>
<g id="Link_Layer">
 <g id="34000079">
 <path d="M 841 401 L 841 373" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000078_0" Pin1InfoVect0LinkObjId="30000041_0" Plane="0"/>
  </metadata>
 <path d="M 841 401 L 841 373" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000080">
 <path d="M 841 433 L 840 460" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000078_1" Pin1InfoVect0LinkObjId="100000074_0" Plane="0"/>
  </metadata>
 <path d="M 841 433 L 840 460" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000081">
 <path d="M 840 491 L 842 514" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000074_1" Pin1InfoVect0LinkObjId="102000019_0" Plane="0"/>
  </metadata>
 <path d="M 840 491 L 842 514" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000082">
 <path d="M 842 583 L 841 656" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="102000019_1" Pin1InfoVect0LinkObjId="101000071_0" Plane="0"/>
  </metadata>
 <path d="M 842 583 L 841 656" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000083">
 <path d="M 841 688 L 841 721" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000071_1" Pin1InfoVect0LinkObjId="30000048_0" Plane="0"/>
  </metadata>
 <path d="M 841 688 L 841 721" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000111">
 <path d="M 1483 401 L 1483 373" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000118_0" Pin1InfoVect0LinkObjId="30000041_0" Plane="0"/>
  </metadata>
 <path d="M 1483 401 L 1483 373" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000112">
 <path d="M 1483 433 L 1482 460" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000118_1" Pin1InfoVect0LinkObjId="100000116_0" Plane="0"/>
  </metadata>
 <path d="M 1483 433 L 1482 460" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000113">
 <path d="M 1482 491 L 1484 514" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000116_1" Pin1InfoVect0LinkObjId="102000119_0" Plane="0"/>
  </metadata>
 <path d="M 1482 491 L 1484 514" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000114">
 <path d="M 1484 583 L 1483 656" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="102000119_1" Pin1InfoVect0LinkObjId="101000117_0" Plane="0"/>
  </metadata>
 <path d="M 1484 583 L 1483 656" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000115">
 <path d="M 1483 688 L 1483 721" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000117_1" Pin1InfoVect0LinkObjId="30000063_0" Plane="0"/>
  </metadata>
 <path d="M 1483 688 L 1483 721" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000134">
 <path d="M 1483 688 L 1482 921" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin1InfoVect0LinkObjId="104000132_0" Plane="0"/>
  </metadata>
 <path d="M 1483 688 L 1482 921" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000143">
 <path d="M 840 721 L 840 921" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="30000048_0" Pin1InfoVect0LinkObjId="104000120_0" Plane="0"/>
  </metadata>
 <path d="M 840 721 L 840 921" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000173">
 <path d="M 1067 755 L 1067 721" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000166_0" Pin1InfoVect0LinkObjId="30000048_0" Plane="0"/>
  </metadata>
 <path d="M 1067 755 L 1067 721" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000174">
 <path d="M 1067 787 L 1065 807" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000166_1" Pin1InfoVect0LinkObjId="131000172_0" Plane="0"/>
  </metadata>
 <path d="M 1067 787 L 1065 807" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000176">
 <path d="M 1264 807 L 1264 788" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="131000171_0" Pin1InfoVect0LinkObjId="101000164_1" Plane="0"/>
  </metadata>
 <path d="M 1264 807 L 1264 788" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000177">
 <path d="M 1264 756 L 1264 721" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000164_0" Pin1InfoVect0LinkObjId="30000063_0" Plane="0"/>
  </metadata>
 <path d="M 1264 756 L 1264 721" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000179">
 <path d="M 1162 895 L 1162 870" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="112000139_0" Pin1InfoVect0LinkObjId="34000180_1" Pin1InfoVect1LinkObjId="34000181_0" Plane="0"/>
  </metadata>
 <path d="M 1162 895 L 1162 870" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000180">
 <path d="M 1065 847 L 1065 870 L 1150 870 L 1162 870" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="131000172_1" Pin1InfoVect0LinkObjId="34000179_1" Pin1InfoVect1LinkObjId="34000181_0" Plane="0"/>
  </metadata>
 <path d="M 1065 847 L 1065 870 L 1150 870 L 1162 870" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000181">
 <path d="M 1162 870 L 1167 870 L 1264 870 L 1264 847" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000179_1" Pin0InfoVect1LinkObjId="34000180_1" Pin1InfoVect0LinkObjId="131000171_1" Plane="0"/>
  </metadata>
 <path d="M 1162 870 L 1167 870 L 1264 870 L 1264 847" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000205">
 <path d="M 979 721 L 978 752" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="30000048_0" Pin1InfoVect0LinkObjId="131000204_0" Plane="0"/>
  </metadata>
 <path d="M 979 721 L 978 752" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000206">
 <path d="M 978 792 L 979 832" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="131000204_1" Pin1InfoVect0LinkObjId="107000201_0" Plane="0"/>
  </metadata>
 <path d="M 978 792 L 979 832" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000208">
 <path d="M 1343 721 L 1342 752" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="30000063_0" Pin1InfoVect0LinkObjId="131000211_0" Plane="0"/>
  </metadata>
 <path d="M 1343 721 L 1342 752" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000209">
 <path d="M 1342 792 L 1343 832" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="131000211_1" Pin1InfoVect0LinkObjId="107000210_0" Plane="0"/>
  </metadata>
 <path d="M 1342 792 L 1343 832" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000214">
 <path d="M 1647 792 L 1647 838" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="131000212_1" Pin1InfoVect0LinkObjId="107000198_0" Plane="0"/>
  </metadata>
 <path d="M 1647 792 L 1647 838" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000215">
 <path d="M 1647 752 L 1647 721" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="131000212_0" Pin1InfoVect0LinkObjId="30000063_0" Plane="0"/>
  </metadata>
 <path d="M 1647 752 L 1647 721" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000217">
 <path d="M 672 792 L 672 838" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="131000220_1" Pin1InfoVect0LinkObjId="107000219_0" Plane="0"/>
  </metadata>
 <path d="M 672 792 L 672 838" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000218">
 <path d="M 672 752 L 672 721" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="131000220_0" Pin1InfoVect0LinkObjId="30000048_0" Plane="0"/>
  </metadata>
 <path d="M 672 752 L 672 721" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000230">
 <path d="M 1642 339 L 1642 373" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000231_1" Pin1InfoVect0LinkObjId="30000041_0" Plane="0"/>
  </metadata>
 <path d="M 1642 339 L 1642 373" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000247">
 <path d="M 631 687 L 631 721" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000222_1" Pin1InfoVect0LinkObjId="30000048_0" Plane="0"/>
  </metadata>
 <path d="M 631 687 L 631 721" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000248">
 <path d="M 631 655 L 632 632" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000222_0" Pin1InfoVect0LinkObjId="107000238_0" Plane="0"/>
  </metadata>
 <path d="M 631 655 L 632 632" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000252">
 <path d="M 1321 655 L 1322 632" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000253_0" Pin1InfoVect0LinkObjId="107000254_0" Plane="0"/>
  </metadata>
 <path d="M 1321 655 L 1322 632" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000251">
 <path d="M 1321 687 L 1321 721" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000253_1" Pin1InfoVect0LinkObjId="30000063_0" Plane="0"/>
  </metadata>
 <path d="M 1321 687 L 1321 721" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000266">
 <path d="M 1603 265 L 1603 284 L 1642 284" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133000260_0" Pin1InfoVect0LinkObjId="34000268_0" Pin1InfoVect1LinkObjId="34000267_1" Pin1InfoVect2LinkObjId="34000269_0" Plane="0"/>
  </metadata>
 <path d="M 1603 265 L 1603 284 L 1642 284" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000267">
 <path d="M 1642 307 L 1642 284" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000231_0" Pin1InfoVect0LinkObjId="34000268_0" Pin1InfoVect1LinkObjId="34000266_1" Pin1InfoVect2LinkObjId="34000269_0" Plane="0"/>
  </metadata>
 <path d="M 1642 307 L 1642 284" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000268">
 <path d="M 1642 284 L 1641 235" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000266_1" Pin0InfoVect1LinkObjId="34000267_1" Pin0InfoVect2LinkObjId="34000269_0" Pin1InfoVect0LinkObjId="107000255_0" Plane="0"/>
  </metadata>
 <path d="M 1642 284 L 1641 235" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000269">
 <path d="M 1642 284 L 1679 285" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000268_0" Pin0InfoVect1LinkObjId="34000266_1" Pin0InfoVect2LinkObjId="34000267_1" Pin1InfoVect0LinkObjId="111000263_0" Plane="0"/>
  </metadata>
 <path d="M 1642 284 L 1679 285" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000482">
 <path d="M 874 307 L 874 276" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000227_0" Pin1InfoVect0LinkObjId="100000463_1" Plane="0"/>
  </metadata>
 <path d="M 874 307 L 874 276" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000483">
 <path d="M 874 245 L 874 219" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000463_0" Pin1InfoVect0LinkObjId="101000467_1" Plane="0"/>
  </metadata>
 <path d="M 874 245 L 874 219" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000490">
 <path d="M 874 339 L 874 373" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000227_1" Pin1InfoVect0LinkObjId="30000041_0" Plane="0"/>
  </metadata>
 <path d="M 874 339 L 874 373" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000502">
 <path d="M 930 162 L 876 164" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000468_1" Pin1InfoVect0LinkObjId="34000508_1" Plane="0"/>
  </metadata>
 <path d="M 930 162 L 876 164" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000503">
 <path d="M 702 164 L 722 164" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="107000241_0" Pin1InfoVect0LinkObjId="131000479_1" Plane="0"/>
  </metadata>
 <path d="M 702 164 L 722 164" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000504">
 <path d="M 748 164 L 761 164" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="131000479_0" Pin1InfoVect0LinkObjId="101000475_0" Plane="0"/>
  </metadata>
 <path d="M 748 164 L 761 164" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000506">
 <path d="M 813 231 L 813 164" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133000476_0" Pin1InfoVect0LinkObjId="34000507_1" Pin1InfoVect1LinkObjId="34000508_0" Plane="0"/>
  </metadata>
 <path d="M 813 231 L 813 164" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000507">
 <path d="M 791 164 L 813 164" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000475_1" Pin1InfoVect0LinkObjId="34000506_1" Pin1InfoVect1LinkObjId="34000508_0" Plane="0"/>
  </metadata>
 <path d="M 791 164 L 813 164" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000508">
 <path d="M 813 164 L 876 164" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000506_1" Pin0InfoVect1LinkObjId="34000507_1" Pin1InfoVect0LinkObjId="34000502_1" Plane="0"/>
  </metadata>
 <path d="M 813 164 L 876 164" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000518">
 <path d="M 1089 236 L 1089 293" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="112000530_0" Pin1InfoVect0LinkObjId="131000509_0" Plane="0"/>
  </metadata>
 <path d="M 1089 236 L 1089 293" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000519">
 <path d="M 1087 338 L 1087 373" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="131000509_1" Pin1InfoVect0LinkObjId="30000041_0" Plane="0"/>
  </metadata>
 <path d="M 1087 338 L 1087 373" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000521">
 <path d="M 1352 236 L 1352 293" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="112000543_0" Pin1InfoVect0LinkObjId="131000524_0" Plane="0"/>
  </metadata>
 <path d="M 1352 236 L 1352 293" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000522">
 <path d="M 1350 338 L 1350 373" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="131000524_1" Pin1InfoVect0LinkObjId="30000041_0" Plane="0"/>
  </metadata>
 <path d="M 1350 338 L 1350 373" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="MeasurementValue_Layer">
 <g id="33000445">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="26" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="226" xml:space="preserve" y="573">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000446">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="26" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="227" xml:space="preserve" y="624">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
</g>
<g id="Text_Layer">
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="57" font-size="32" font-width="32" stroke="rgb(0,0,0)" writing-mode="lr" x="126" xml:space="preserve" y="108">35kV大沙坝电站</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="tb" x="62" xml:space="preserve" y="403">单</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,0,0)" writing-mode="lr" x="184" xml:space="preserve" y="414">MW</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="56" xml:space="preserve" y="572">总有功</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="96" xml:space="preserve" y="710">事故总:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="117" xml:space="preserve" y="414">有功:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="222" xml:space="preserve" y="414"> 无功:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,0,0)" writing-mode="lr" x="305" xml:space="preserve" y="414">MVr</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="117" xml:space="preserve" y="460">电流:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,0,0)" writing-mode="lr" x="184" xml:space="preserve" y="460">A</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="234" xml:space="preserve" y="460">电压:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,0,0)" writing-mode="lr" x="305" xml:space="preserve" y="460">kV</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="117" xml:space="preserve" y="510">频率:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,0,0)" writing-mode="lr" x="184" xml:space="preserve" y="510">Hz</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="234" xml:space="preserve" y="510">温度:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,0,0)" writing-mode="lr" x="305" xml:space="preserve" y="511">℃</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="51" xml:space="preserve" y="627">总无功</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="tb" x="62" xml:space="preserve" y="472">位</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="28" xml:space="preserve" y="328">工况</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="28" xml:space="preserve" y="351">退出</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="111" xml:space="preserve" y="328">不</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="100" xml:space="preserve" y="353">变化</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="168" xml:space="preserve" y="342">越限</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="251" xml:space="preserve" y="328">非</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="239" xml:space="preserve" y="353">实测</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="309" xml:space="preserve" y="330">数据</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="309" xml:space="preserve" y="353">封锁</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="519" xml:space="preserve" y="368">35kV I 母</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="521" xml:space="preserve" y="716">6.3kV I 母</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1730" xml:space="preserve" y="716">6.3kV I 母</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="784" xml:space="preserve" y="682">6011</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="855" xml:space="preserve" y="486">301</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="798" xml:space="preserve" y="427">3011</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1426" xml:space="preserve" y="682">6022</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1497" xml:space="preserve" y="486">302</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1431" xml:space="preserve" y="427">3021</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="792" xml:space="preserve" y="993">1号发电机</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1437" xml:space="preserve" y="993">2号发电机</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1113" xml:space="preserve" y="1010">1号厂用变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1211" xml:space="preserve" y="782">6512</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1014" xml:space="preserve" y="781">6511</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1573" xml:space="preserve" y="938">2号发电机励磁变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="896" xml:space="preserve" y="938">1号发电机励磁变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1268" xml:space="preserve" y="938">2号发电机励磁TV</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="591" xml:space="preserve" y="938">1号发电机励磁TV</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="588" xml:space="preserve" y="681">6901</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="831" xml:space="preserve" y="333">3411</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1264" xml:space="preserve" y="681">6902</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1621" xml:space="preserve" y="88">母线TV</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1583" xml:space="preserve" y="345">3901</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1689" xml:space="preserve" y="315">39017</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="889" xml:space="preserve" y="271">341</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="820" xml:space="preserve" y="213">3416</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="927" xml:space="preserve" y="193">34167</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="759" xml:space="preserve" y="155">3419</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="854" xml:space="preserve" y="88">大腊线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="757" xml:space="preserve" y="1021">SF4000-32/4250</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1406" xml:space="preserve" y="1021">SF1600-22/2600</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1059" xml:space="preserve" y="142">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1322" xml:space="preserve" y="142">???</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href="农垦电网.other.svg"><rect fill-opacity="0" height="85" stroke-opacity="0" stroke-width="2" width="337" x="28" y="34"/></g>
</g>
</svg>