<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="sys" height="500" id="thSvg" viewBox="0 0 1155 500" width="1155">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="Protect:bn_保护图元1_0" viewBox="0,0,18,18">
 <rect AFMask="2147483647" Plane="0" fill="none" height="13" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,8,8)" width="13" x="2" y="2"/>
</symbol>
<symbol id="Protect:bn_保护图元1_1" viewBox="0,0,18,18">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="13" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,8,8)" width="13" x="2" y="2"/>
</symbol>
<symbol id="Protect:软压板投退_0" viewBox="0,0,50,50">
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,0,0)" stroke-width="3" x1="40" x2="13" y1="31" y2="4"/>
 <circle AFMask="2147483647" Plane="0" cx="9" cy="31" fill="none" r="4" stroke="rgb(255,0,0)" stroke-width="8"/>
 <circle AFMask="2147483647" Plane="0" cx="40" cy="31" fill="none" r="4" stroke="rgb(255,0,0)" stroke-width="8"/>
</symbol>
<symbol id="Protect:软压板投退_1" viewBox="0,0,50,50">
 <circle AFMask="2147483647" Plane="0" cx="9" cy="24" fill="none" r="4" stroke="rgb(0,255,0)" stroke-width="8"/>
 <circle AFMask="2147483647" Plane="0" cx="40" cy="24" fill="none" r="4" stroke="rgb(0,255,0)" stroke-width="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,255,0)" stroke-width="3" x1="9" x2="41" y1="24" y2="24"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="-1" InitShowingPlane="0," fill="rgb(0,0,0)" height="500" width="1155" x="0" y="0"/>
</g>
<g id="Other_Layer">
 
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="259" x2="833" y1="99" y2="99"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="259" x2="834" y1="129" y2="129"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="387" x2="835" y1="159" y2="159"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="258" x2="258" y1="99" y2="397"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="387" x2="387" y1="98" y2="402"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="668" x2="668" y1="99" y2="400"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="836" x2="836" y1="100" y2="401"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="387" x2="834" y1="189" y2="189"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="387" x2="834" y1="219" y2="219"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="387" x2="834" y1="249" y2="249"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="259" x2="834" y1="279" y2="279"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="387" x2="833" y1="309" y2="309"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="387" x2="833" y1="339" y2="339"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="387" x2="835" y1="369" y2="369"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="259" x2="835" y1="399" y2="399"/>
</g>
<g id="Protect_Layer">
 <g id="127000423">
  <use class="kv35kV" height="50" transform="rotate(0,750,142) scale(0.672,0.672) translate(341.071,48.3095)" width="50" x="750" xlink:href="#Protect:软压板投退_0" y="142"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892516678" ObjectName="122160139892516678" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,750,142) scale(0.672,0.672) translate(341.071,48.3095)" width="50" x="750" y="142"/></g>
 <g id="127000424">
  <use class="kv35kV" height="50" transform="rotate(0,750,172) scale(0.672,0.672) translate(341.071,62.9524)" width="50" x="750" xlink:href="#Protect:软压板投退_0" y="172"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892516688" ObjectName="122160139892516688" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,750,172) scale(0.672,0.672) translate(341.071,62.9524)" width="50" x="750" y="172"/></g>
 <g id="127000425">
  <use class="kv35kV" height="50" transform="rotate(0,750,202) scale(0.672,0.672) translate(341.071,77.5952)" width="50" x="750" xlink:href="#Protect:软压板投退_0" y="202"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892516689" ObjectName="122160139892516689" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,750,202) scale(0.672,0.672) translate(341.071,77.5952)" width="50" x="750" y="202"/></g>
 <g id="127000426">
  <use class="kv10kV" height="50" transform="rotate(0,750,232) scale(0.672,0.672) translate(341.071,92.2381)" width="50" x="750" xlink:href="#Protect:软压板投退_0" y="232"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892516708" ObjectName="122160139892516708" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,750,232) scale(0.672,0.672) translate(341.071,92.2381)" width="50" x="750" y="232"/></g>
 <g id="127000427">
  <use class="kv10kV" height="50" transform="rotate(0,750,262) scale(0.672,0.672) translate(341.071,106.881)" width="50" x="750" xlink:href="#Protect:软压板投退_0" y="262"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892516707" ObjectName="122160139892516707" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,750,262) scale(0.672,0.672) translate(341.071,106.881)" width="50" x="750" y="262"/></g>
 <g id="127000452">
  <use class="kv35kV" height="18" transform="rotate(0,751,293) scale(1.457,1.457) translate(-244.557,-100.902)" width="18" x="751" xlink:href="#Protect:bn_保护图元1_0" y="293"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892516702" ObjectName="122160139892516702" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,751,293) scale(1.457,1.457) translate(-244.557,-100.902)" width="18" x="751" y="293"/></g>
 <g id="127000453">
  <use class="kv10kV" height="18" transform="rotate(0,751,324) scale(1.457,1.457) translate(-244.557,-110.625)" width="18" x="751" xlink:href="#Protect:bn_保护图元1_0" y="324"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892516721" ObjectName="122160139892516721" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,751,324) scale(1.457,1.457) translate(-244.557,-110.625)" width="18" x="751" y="324"/></g>
 <g id="127000454">
  <use class="kv35kV" height="18" transform="rotate(0,751,354) scale(1.457,1.457) translate(-244.557,-120.035)" width="18" x="751" xlink:href="#Protect:bn_保护图元1_0" y="354"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892516684" ObjectName="122160139892516684" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,751,354) scale(1.457,1.457) translate(-244.557,-120.035)" width="18" x="751" y="354"/></g>
 <g id="127000455">
  <use class="kv35kV" height="18" transform="rotate(0,751,384) scale(1.457,1.457) translate(-244.557,-129.445)" width="18" x="751" xlink:href="#Protect:bn_保护图元1_0" y="384"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892516675" ObjectName="122160139892516675" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,751,384) scale(1.457,1.457) translate(-244.557,-129.445)" width="18" x="751" y="384"/></g>
</g>
<g id="Text_Layer">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="30" font-width="30" stroke="rgb(85,255,255)" writing-mode="lr" x="363" xml:space="preserve" y="49">巴达变继电保护远方操作</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="289" xml:space="preserve" y="346">1号主变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="398" xml:space="preserve" y="306">高后备保护信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="398" xml:space="preserve" y="336">低后备保护信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="409" xml:space="preserve" y="366">差动保护信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="398" xml:space="preserve" y="396">非电量保护信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="300" xml:space="preserve" y="126">线路</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="286" xml:space="preserve" y="217">1号主变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="464" xml:space="preserve" y="126">名称</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="412" xml:space="preserve" y="156">差动保护软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="412" xml:space="preserve" y="186">高侧电压软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="412" xml:space="preserve" y="216">间隙保护软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="412" xml:space="preserve" y="246">低侧电压软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="412" xml:space="preserve" y="276">母线保护软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="723" xml:space="preserve" y="126">软压板</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href="版纳_35kV_巴达变.fac.svg"><rect fill-opacity="0" height="46" stroke-opacity="0" stroke-width="1" width="335" x="361" y="11"/></g>
</g>
</svg>