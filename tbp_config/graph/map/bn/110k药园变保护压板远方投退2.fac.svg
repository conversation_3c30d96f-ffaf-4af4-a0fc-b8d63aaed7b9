<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="sys" height="1000" id="thSvg" viewBox="0 0 2100 1000" width="2100">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="Protect:软压板投退_0" viewBox="0,0,50,50">
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,0,0)" stroke-width="3" x1="40" x2="13" y1="31" y2="4"/>
 <circle AFMask="2147483647" Plane="0" cx="9" cy="31" fill="none" r="4" stroke="rgb(255,0,0)" stroke-width="8"/>
 <circle AFMask="2147483647" Plane="0" cx="40" cy="31" fill="none" r="4" stroke="rgb(255,0,0)" stroke-width="8"/>
</symbol>
<symbol id="Protect:软压板投退_1" viewBox="0,0,50,50">
 <circle AFMask="2147483647" Plane="0" cx="9" cy="24" fill="none" r="4" stroke="rgb(0,255,0)" stroke-width="8"/>
 <circle AFMask="2147483647" Plane="0" cx="40" cy="24" fill="none" r="4" stroke="rgb(0,255,0)" stroke-width="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,255,0)" stroke-width="3" x1="9" x2="41" y1="24" y2="24"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="-1" InitShowingPlane="0," fill="rgb(0,0,0)" height="1000" width="2100" x="0" y="0"/>
</g>
<g id="Other_Layer">
 
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="228" x2="228" y1="87" y2="851"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="596" x2="596" y1="86" y2="852"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="689" x2="689" y1="85" y2="852"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="229" x2="687" y1="164" y2="164"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="688" y1="85" y2="85"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="15" x2="15" y1="85" y2="852"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="688" y1="125" y2="125"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="230" x2="689" y1="204" y2="204"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="689" y1="244" y2="244"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="689" y1="284" y2="284"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="689" y1="324" y2="324"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="689" y1="364" y2="364"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="689" y1="404" y2="404"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="689" y1="444" y2="444"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="689" y1="484" y2="484"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="689" y1="524" y2="524"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="689" y1="564" y2="564"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="689" y1="604" y2="604"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="689" y1="644" y2="644"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="689" y1="684" y2="684"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="689" y1="724" y2="724"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="806" x2="1255" y1="85" y2="85"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="806" x2="806" y1="86" y2="245"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="807" x2="1255" y1="126" y2="126"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1109" x2="1109" y1="126" y2="245"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="808" x2="1254" y1="166" y2="166"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="808" x2="1255" y1="206" y2="206"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="807" x2="1255" y1="246" y2="246"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1255" x2="1255" y1="86" y2="245"/>
 
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="17" x2="689" y1="812" y2="812"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="689" y1="767" y2="767"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="14" x2="689" y1="853" y2="853"/>
</g>
<g id="Protect_Layer">
 <g id="127000377">
  <use class="kv10kV" height="50" transform="rotate(0,643,344) scale(0.915,0.915) translate(34.7322,10.9563)" width="50" x="643" xlink:href="#Protect:软压板投退_0" y="344"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892532528" ObjectName="122160139892532528" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,643,344) scale(0.915,0.915) translate(34.7322,10.9563)" width="50" x="643" y="344"/></g>
 <g id="127000378">
  <use class="kv10kV" height="50" transform="rotate(0,643,384) scale(0.915,0.915) translate(34.7322,14.6721)" width="50" x="643" xlink:href="#Protect:软压板投退_0" y="384"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892532562" ObjectName="122160139892532562" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,643,384) scale(0.915,0.915) translate(34.7322,14.6721)" width="50" x="643" y="384"/></g>
 <g id="127000379">
  <use class="kv10kV" height="50" transform="rotate(0,643,426) scale(0.915,0.915) translate(34.7322,18.5737)" width="50" x="643" xlink:href="#Protect:软压板投退_0" y="426"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892532596" ObjectName="122160139892532596" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,643,426) scale(0.915,0.915) translate(34.7322,18.5737)" width="50" x="643" y="426"/></g>
 <g id="127000380">
  <use class="kv10kV" height="50" transform="rotate(0,643,464) scale(0.915,0.915) translate(34.7322,22.1038)" width="50" x="643" xlink:href="#Protect:软压板投退_0" y="464"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892532630" ObjectName="122160139892532630" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,643,464) scale(0.915,0.915) translate(34.7322,22.1038)" width="50" x="643" y="464"/></g>
 <g id="127000381">
  <use class="kv10kV" height="50" transform="rotate(0,643,506) scale(0.915,0.915) translate(34.7322,26.0054)" width="50" x="643" xlink:href="#Protect:软压板投退_0" y="506"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892532664" ObjectName="122160139892532664" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,643,506) scale(0.915,0.915) translate(34.7322,26.0054)" width="50" x="643" y="506"/></g>
 <g id="127000382">
  <use class="kv10kV" height="50" transform="rotate(0,643,543) scale(0.915,0.915) translate(34.7322,29.4426)" width="50" x="643" xlink:href="#Protect:软压板投退_0" y="543"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892532698" ObjectName="122160139892532698" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,643,543) scale(0.915,0.915) translate(34.7322,29.4426)" width="50" x="643" y="543"/></g>
 <g id="127000383">
  <use class="kv10kV" height="50" transform="rotate(0,643,584) scale(0.915,0.915) translate(34.7322,33.2513)" width="50" x="643" xlink:href="#Protect:软压板投退_0" y="584"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892532725" ObjectName="122160139892532725" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,643,584) scale(0.915,0.915) translate(34.7322,33.2513)" width="50" x="643" y="584"/></g>
 <g id="127000384">
  <use class="kv10kV" height="50" transform="rotate(0,643,625) scale(0.915,0.915) translate(34.7322,37.0601)" width="50" x="643" xlink:href="#Protect:软压板投退_0" y="625"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892532750" ObjectName="122160139892532750" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,643,625) scale(0.915,0.915) translate(34.7322,37.0601)" width="50" x="643" y="625"/></g>
 <g id="127000385">
  <use class="kv10kV" height="50" transform="rotate(0,643,665) scale(0.915,0.915) translate(34.7322,40.7759)" width="50" x="643" xlink:href="#Protect:软压板投退_0" y="665"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892532777" ObjectName="122160139892532777" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,643,665) scale(0.915,0.915) translate(34.7322,40.7759)" width="50" x="643" y="665"/></g>
 <g id="127000386">
  <use class="kv10kV" height="50" transform="rotate(0,643,705) scale(0.915,0.915) translate(34.7322,44.4918)" width="50" x="643" xlink:href="#Protect:软压板投退_0" y="705"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892532804" ObjectName="122160139892532804" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,643,705) scale(0.915,0.915) translate(34.7322,44.4918)" width="50" x="643" y="705"/></g>
</g>
<g id="Text_Layer">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="39" stroke="rgb(85,255,255)" writing-mode="lr" x="668" xml:space="preserve" y="51">药园变继电保护远方操作</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="102" xml:space="preserve" y="117">线路</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="372" xml:space="preserve" y="116">名称</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="611" xml:space="preserve" y="116">软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="316" xml:space="preserve" y="356">043保护装置信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="316" xml:space="preserve" y="396">044保护装置信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="316" xml:space="preserve" y="436">045保护装置信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="316" xml:space="preserve" y="476">046保护装置信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="316" xml:space="preserve" y="516">047保护装置信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="316" xml:space="preserve" y="556">048保护装置信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="316" xml:space="preserve" y="596">049保护装置信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="316" xml:space="preserve" y="636">052保护装置信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="316" xml:space="preserve" y="676">050保护装置信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="316" xml:space="preserve" y="716">051保护装置信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="33" xml:space="preserve" y="356">10kV勐养政府线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="34" xml:space="preserve" y="396">10kV药养联络线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="33" xml:space="preserve" y="437">10kV勐养车站Ⅰ回线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="34" xml:space="preserve" y="477">10kV勐养车站Ⅱ回线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="35" xml:space="preserve" y="517">10kV煤矿线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="22" xml:space="preserve" y="558">10kV野象谷车站I回线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="34" xml:space="preserve" y="595">10kV1号站用变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="34" xml:space="preserve" y="635">10kV1号接地变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="34" xml:space="preserve" y="676">10kV1号电容器</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="34" xml:space="preserve" y="716">10kV2号电容器</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="976" xml:space="preserve" y="116">定值区切换</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="886" xml:space="preserve" y="157">线路</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1148" xml:space="preserve" y="156">定值区</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1752" xml:space="preserve" y="119">上一页</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href="版纳_110kV_药园变.fac.svg"><rect fill-opacity="0" height="56" stroke-opacity="0" stroke-width="1" width="471" x="649" y="4"/></g>
 <g ChangePicPlane="0," Plane="0" href="110kV药园变保护压板远方投退1.fac.svg"><rect fill-opacity="0" height="30" stroke-opacity="0" stroke-width="1" width="133" x="1731" y="94"/></g>
</g>
</svg>