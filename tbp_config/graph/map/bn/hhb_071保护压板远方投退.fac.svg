<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="sys" height="500" id="thSvg" viewBox="0 0 1000 500" width="1000">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="Protect:bn_保护图元1_0" viewBox="0,0,18,18">
 <rect AFMask="2147483647" Plane="0" fill="none" height="13" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,8,8)" width="13" x="2" y="2"/>
</symbol>
<symbol id="Protect:bn_保护图元1_1" viewBox="0,0,18,18">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="13" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,8,8)" width="13" x="2" y="2"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="版纳_110kV_辉凰变" InitShowingPlane="0," fill="rgb(0,0,0)" height="500" width="1000" x="0" y="0"/>
</g>
<g id="Other_Layer">
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="196" x2="761" y1="232" y2="232"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="196" x2="761" y1="282" y2="282"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="196" x2="196" y1="233" y2="332"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="576" x2="576" y1="282" y2="331"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="762" x2="762" y1="233" y2="332"/>
 
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="196" x2="761" y1="332" y2="332"/>
</g>
<g id="Protect_Layer">
 <g id="127000028">
  <use class="kv10kV" height="18" transform="rotate(0,676,307) scale(1,1) translate(-9,-9)" width="18" x="676" xlink:href="#Protect:bn_保护图元1_0" y="307"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892519591" ObjectName="122160139892519591" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,676,307) scale(1,1) translate(-9,-9)" width="18" x="676" y="307"/></g>
</g>
<g id="Text_Layer">
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(0,0,0)" writing-mode="lr" x="388" xml:space="preserve" y="106">110kV辉凰变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="368" xml:space="preserve" y="318">信号复归</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="357" xml:space="preserve" y="274">2号接地变071间隔</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href="版纳_110kV_辉凰变.fac.svg"><rect fill-opacity="0" height="61" stroke-opacity="0" stroke-width="2" width="248" x="351" y="61"/></g>
 <g ChangePicPlane="0," Plane="0" href="版纳_110kV_辉凰变071间隔接线图.bay.svg"><rect fill-opacity="0" height="34" stroke-opacity="0" stroke-width="2" width="293" x="330" y="242"/></g>
</g>
</svg>