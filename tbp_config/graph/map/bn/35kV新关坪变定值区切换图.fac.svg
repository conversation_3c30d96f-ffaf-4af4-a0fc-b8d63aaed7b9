<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1372" id="thSvg" viewBox="0 0 2820 1372" width="2820">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="Status:微机保护设备3_0" viewBox="0,0,46,26">
 <circle AFMask="2147483647" Plane="0" cx="36" cy="8" fill="rgb(0,255,0)" r="5" stroke="rgb(0,255,0)" stroke-width="1"/>
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="9" font-size="8" font-width="8" stroke="rgb(0,255,0)" writing-mode="lr" x="29" xml:space="preserve" y="23">投运</text>
</symbol>
<symbol id="Status:微机保护设备3_1" viewBox="0,0,46,26">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="9" font-size="8" font-width="8" stroke="rgb(255,255,254)" writing-mode="lr" x="29" xml:space="preserve" y="22">停运</text>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(255,255,254)" r="5" stroke="rgb(255,255,254)" stroke-width="1"/>
</symbol>
<symbol id="Status:微机保护设备3_2" viewBox="0,0,46,26">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="9" font-size="8" font-width="8" stroke="rgb(255,255,0)" writing-mode="lr" x="29" xml:space="preserve" y="21">调试</text>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Status:微机保护设备3_3" viewBox="0,0,46,26">
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(255,255,0)" r="5" stroke="rgb(255,0,0)" stroke-width="1"/>
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="8" font-size="7" font-width="7" stroke="rgb(255,0,0)" writing-mode="lr" x="29" xml:space="preserve" y="22">检修</text>
</symbol>
<symbol id="Status:微机保护设备3_4" viewBox="0,0,46,26">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="9" font-size="8" font-width="8" stroke="rgb(255,255,0)" writing-mode="lr" x="28" xml:space="preserve" y="22">动作</text>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(255,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Status:微机保护设备3_5" viewBox="0,0,46,26">
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(255,170,255)" stroke-width="1"/>
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="8" font-size="8" font-width="8" stroke="rgb(255,85,255)" writing-mode="lr" x="29" xml:space="preserve" y="20">告警</text>
</symbol>
<symbol id="Status:微机保护设备3_6" viewBox="0,0,46,26">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="10" font-size="8" font-width="8" stroke="rgb(255,255,0)" writing-mode="lr" x="29" xml:space="preserve" y="23">变位</text>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Status:微机保护设备3_7" viewBox="0,0,46,26">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="8" font-size="8" font-width="8" stroke="rgb(255,0,0)" writing-mode="lr" x="29" xml:space="preserve" y="20">中断</text>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(255,0,0)" r="5" stroke="rgb(255,0,0)" stroke-width="1"/>
</symbol>
<symbol id="Status:微机保护设备3_8" viewBox="0,0,46,26">
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(255,0,127)" r="5" stroke="rgb(255,0,127)" stroke-width="1"/>
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="4" font-size="4" font-width="5" stroke="rgb(255,0,127)" writing-mode="lr" x="30" xml:space="preserve" y="17">抑制</text>
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="4" font-size="4" font-width="5" stroke="rgb(255,0,127)" writing-mode="lr" x="30" xml:space="preserve" y="21">告警</text>
</symbol>
<symbol id="Status:微机保护设备3_9" viewBox="0,0,46,26">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="8" font-size="4" font-width="4" stroke="rgb(0,0,255)" writing-mode="lr" x="28" xml:space="preserve" y="22">告警抑制</text>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Status:微机保护设备3_10" viewBox="0,0,46,26">
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="6" font-size="2" font-width="2" stroke="rgb(0,0,255)" writing-mode="lr" x="28" xml:space="preserve" y="19">正在修改定值</text>
</symbol>
<symbol id="Status:微机保护设备3_11" viewBox="0,0,46,26">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="7" font-size="2" font-width="2" stroke="rgb(0,0,255)" writing-mode="lr" x="28" xml:space="preserve" y="20">闭锁修改定值</text>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Status:微机保护设备3_12" viewBox="0,0,46,26">
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="9" font-size="8" font-width="8" stroke="rgb(0,0,255)" writing-mode="lr" x="29" xml:space="preserve" y="21">动作</text>
</symbol>
<symbol id="Status:微机保护设备3_13" viewBox="0,0,46,26">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="9" font-size="8" font-width="8" stroke="rgb(0,0,255)" writing-mode="lr" x="29" xml:space="preserve" y="21">告警</text>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Status:微机保护设备3_14" viewBox="0,0,46,26">
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="9" font-size="8" font-width="8" stroke="rgb(0,0,255)" writing-mode="lr" x="29" xml:space="preserve" y="21">变位</text>
</symbol>
<symbol id="Status:微机保护设备3_15" viewBox="0,0,46,26">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="7" font-size="5" font-width="5" stroke="rgb(0,0,255)" writing-mode="lr" x="28" xml:space="preserve" y="21">未确认</text>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="版纳_35kV_关坪变" InitShowingPlane="0," fill="rgb(0,0,0)" height="1372" width="2820" x="0" y="0"/>
</g>
<g id="Other_Layer">
 
</g>
<g id="Status_Layer">
 <g id="126002182">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="279786126850392087" ObjectName="279786126850392087" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,127,240) scale(2.915,2.915) translate(-106.432,-171.667)" width="46" x="127" y="240"/></g>
 <g id="126002186">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="279786126850392088" ObjectName="279786126850392088" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,127,355) scale(2.915,2.915) translate(-106.432,-247.216)" width="46" x="127" y="355"/></g>
 <g id="126002187">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="279786126850392089" ObjectName="279786126850392089" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,127,470) scale(2.915,2.915) translate(-106.432,-322.765)" width="46" x="127" y="470"/></g>
 <g id="126002188">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="279786126850392090" ObjectName="279786126850392090" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,127,584) scale(2.915,2.915) translate(-106.432,-397.657)" width="46" x="127" y="584"/></g>
 <g id="126002189">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="279786126850392091" ObjectName="279786126850392091" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,127,699) scale(2.915,2.915) translate(-106.432,-473.206)" width="46" x="127" y="699"/></g>
 <g id="126002190">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="279786126850392092" ObjectName="279786126850392092" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,127,814) scale(2.915,2.915) translate(-106.432,-548.755)" width="46" x="127" y="814"/></g>
</g>
<g id="MeasurementValue_Layer">
 <g id="33002224">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="53" font-size="53" font-width="64" stroke="rgb(255,255,254)" writing-mode="lr" x="1130" xml:space="preserve" y="274">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795531650" ObjectName="122723089845858690:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002225">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="52" font-size="52" font-width="63" stroke="rgb(255,255,254)" writing-mode="lr" x="1130" xml:space="preserve" y="387">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795531651" ObjectName="122723089845858691:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002226">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="52" font-size="52" font-width="63" stroke="rgb(255,255,254)" writing-mode="lr" x="1130" xml:space="preserve" y="502">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795531652" ObjectName="122723089845858692:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002227">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="53" font-size="53" font-width="64" stroke="rgb(255,255,254)" writing-mode="lr" x="1130" xml:space="preserve" y="617">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795531653" ObjectName="122723089845858693:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002228">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="53" font-size="53" font-width="64" stroke="rgb(255,255,254)" writing-mode="lr" x="1130" xml:space="preserve" y="731">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795531623" ObjectName="122723089845858663:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002229">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="53" font-size="53" font-width="64" stroke="rgb(255,255,254)" writing-mode="lr" x="1130" xml:space="preserve" y="845">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795531627" ObjectName="122723089845858667:M" Plane="0"/>
  </metadata>
 </g>
</g>
<g id="Text_Layer">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="56" font-size="34" font-width="34" stroke="rgb(85,255,255)" writing-mode="lr" x="1120" xml:space="preserve" y="105">35kV关坪变继电保护远方操作定值区切换</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="50" font-size="50" font-width="56" stroke="rgb(255,255,254)" writing-mode="lr" x="206" xml:space="preserve" y="260">＃1主变非电量保护装置</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="50" font-size="50" font-width="56" stroke="rgb(255,255,254)" writing-mode="lr" x="206" xml:space="preserve" y="375">＃1主变差动保护装置</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="50" font-size="50" font-width="56" stroke="rgb(255,255,254)" writing-mode="lr" x="206" xml:space="preserve" y="489">＃1主变高后备保护装置</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="49" font-size="49" font-width="55" stroke="rgb(255,255,254)" writing-mode="lr" x="206" xml:space="preserve" y="603">＃1主变低后备保护装置</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="49" font-size="49" font-width="55" stroke="rgb(255,255,254)" writing-mode="lr" x="206" xml:space="preserve" y="717">10kV备用线041保护测控装置</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="49" font-size="49" font-width="55" stroke="rgb(255,255,254)" writing-mode="lr" x="206" xml:space="preserve" y="832">10kV二台坡线042保护测控装置</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="50" font-size="50" font-width="56" stroke="rgb(255,255,254)" writing-mode="lr" x="1082" xml:space="preserve" y="191">当前定值区</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href="版纳_35kV_新关坪变.fac.svg"><rect fill-opacity="0" height="68" stroke-opacity="0" stroke-width="1" width="646" x="1104" y="50"/></g>
</g>
</svg>