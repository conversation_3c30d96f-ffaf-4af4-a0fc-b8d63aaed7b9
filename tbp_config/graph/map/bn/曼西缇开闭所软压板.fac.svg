<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="sys" height="1200" id="thSvg" viewBox="0 0 1620 1200" width="1620">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="Protect:bn_保护图元1_0" viewBox="0,0,18,18">
 <rect AFMask="2147483647" Plane="0" fill="none" height="13" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,8,8)" width="13" x="2" y="2"/>
</symbol>
<symbol id="Protect:bn_保护图元1_1" viewBox="0,0,18,18">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="13" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,8,8)" width="13" x="2" y="2"/>
</symbol>
<symbol id="Protect:软压板投退_0" viewBox="0,0,50,50">
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,0,0)" stroke-width="3" x1="40" x2="13" y1="31" y2="4"/>
 <circle AFMask="2147483647" Plane="0" cx="9" cy="31" fill="none" r="4" stroke="rgb(255,0,0)" stroke-width="8"/>
 <circle AFMask="2147483647" Plane="0" cx="40" cy="31" fill="none" r="4" stroke="rgb(255,0,0)" stroke-width="8"/>
</symbol>
<symbol id="Protect:软压板投退_1" viewBox="0,0,50,50">
 <circle AFMask="2147483647" Plane="0" cx="9" cy="24" fill="none" r="4" stroke="rgb(0,255,0)" stroke-width="8"/>
 <circle AFMask="2147483647" Plane="0" cx="40" cy="24" fill="none" r="4" stroke="rgb(0,255,0)" stroke-width="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,255,0)" stroke-width="3" x1="9" x2="41" y1="24" y2="24"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="-1" InitShowingPlane="0," fill="rgb(0,0,0)" height="1200" width="1620" x="0" y="0"/>
</g>
<g id="Other_Layer">
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="776" x2="776" y1="147" y2="486"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="683" x2="683" y1="145" y2="484"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="233" x2="233" y1="485" y2="147"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="234" x2="775" y1="197" y2="197"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="235" x2="775" y1="246" y2="246"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="233" x2="775" y1="341" y2="341"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="232" x2="775" y1="434" y2="434"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="232" x2="776" y1="293" y2="293"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="68" x2="775" y1="483" y2="483"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="234" x2="773" y1="388" y2="388"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="70" x2="775" y1="146" y2="146"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="69" x2="69" y1="147" y2="485"/>
 
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1506" x2="1506" y1="150" y2="496"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1413" x2="1413" y1="148" y2="494"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="963" x2="963" y1="494" y2="150"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="964" x2="1505" y1="200" y2="200"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="965" x2="1505" y1="249" y2="249"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="963" x2="1505" y1="344" y2="344"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="962" x2="1505" y1="437" y2="437"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="962" x2="1506" y1="296" y2="296"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="964" x2="1503" y1="391" y2="391"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="800" x2="1505" y1="149" y2="149"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="799" x2="799" y1="150" y2="496"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1506" x2="1506" y1="492" y2="1080"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1413" x2="1413" y1="490" y2="1080"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="963" x2="963" y1="1078" y2="492"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="964" x2="1505" y1="542" y2="542"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="965" x2="1505" y1="591" y2="591"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="963" x2="1505" y1="686" y2="686"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="962" x2="1505" y1="779" y2="779"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="962" x2="1506" y1="638" y2="638"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="966" x2="1505" y1="828" y2="828"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="964" x2="1503" y1="733" y2="733"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="963" x2="1505" y1="491" y2="491"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="799" x2="799" y1="492" y2="1081"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="962" x2="1505" y1="879" y2="879"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="962" x2="1505" y1="979" y2="979"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="799" x2="1505" y1="1079" y2="1079"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="962" x2="1505" y1="929" y2="929"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="962" x2="1505" y1="1029" y2="1029"/>
</g>
<g id="Protect_Layer">
 <g id="127000183">
  <use class="kv10kV" height="50" transform="rotate(0,734,174) scale(0.6,0.6) translate(464.333,95)" width="50" x="734" xlink:href="#Protect:软压板投退_0" y="174"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892583137" ObjectName="122160139892583137" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,734,174) scale(0.6,0.6) translate(464.333,95)" width="50" x="734" y="174"/></g>
 <g id="127000186">
  <use class="kv10kV" height="50" transform="rotate(0,734,225) scale(0.6,0.6) translate(464.333,129)" width="50" x="734" xlink:href="#Protect:软压板投退_0" y="225"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892583138" ObjectName="122160139892583138" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,734,225) scale(0.6,0.6) translate(464.333,129)" width="50" x="734" y="225"/></g>
 <g id="127000187">
  <use class="kv10kV" height="50" transform="rotate(0,734,268) scale(0.6,0.6) translate(464.333,157.667)" width="50" x="734" xlink:href="#Protect:软压板投退_0" y="268"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892583139" ObjectName="122160139892583139" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,734,268) scale(0.6,0.6) translate(464.333,157.667)" width="50" x="734" y="268"/></g>
 <g id="127000189">
  <use class="kv10kV" height="50" transform="rotate(0,734,320) scale(0.6,0.6) translate(464.333,192.333)" width="50" x="734" xlink:href="#Protect:软压板投退_0" y="320"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892583140" ObjectName="122160139892583140" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,734,320) scale(0.6,0.6) translate(464.333,192.333)" width="50" x="734" y="320"/></g>
 <g id="127000191">
  <use class="kv10kV" height="50" transform="rotate(0,734,368) scale(0.6,0.6) translate(464.333,224.333)" width="50" x="734" xlink:href="#Protect:软压板投退_0" y="368"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892583141" ObjectName="122160139892583141" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,734,368) scale(0.6,0.6) translate(464.333,224.333)" width="50" x="734" y="368"/></g>
 <g id="127000192">
  <use class="kv10kV" height="50" transform="rotate(0,734,414) scale(0.6,0.6) translate(464.333,255)" width="50" x="734" xlink:href="#Protect:软压板投退_0" y="414"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892583142" ObjectName="122160139892583142" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,734,414) scale(0.6,0.6) translate(464.333,255)" width="50" x="734" y="414"/></g>
 <g id="127000289">
  <use class="kv10kV" height="18" transform="rotate(0,734,459) scale(1.808,1.808) translate(-337.027,-214.128)" width="18" x="734" xlink:href="#Protect:bn_保护图元1_0" y="459"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892583143" ObjectName="122160139892583143" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,734,459) scale(1.808,1.808) translate(-337.027,-214.128)" width="18" x="734" y="459"/></g>
</g>
<g id="MeasurementValue_Layer">
 <g id="33000317">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="24" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="1431" xml:space="preserve" y="186">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795532955" ObjectName="122723089845859995:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33000318">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="24" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="1431" xml:space="preserve" y="236">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795532956" ObjectName="122723089845859996:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33000319">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="24" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="1431" xml:space="preserve" y="286">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795532957" ObjectName="122723089845859997:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33000320">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="24" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="1431" xml:space="preserve" y="336">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795532958" ObjectName="122723089845859998:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33000321">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="24" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="1431" xml:space="preserve" y="386">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795532959" ObjectName="122723089845859999:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33000322">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="24" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="1431" xml:space="preserve" y="436">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795532960" ObjectName="122723089845860000:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33000323">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="24" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="1431" xml:space="preserve" y="476">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795532961" ObjectName="122723089845860001:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33000344">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="24" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="1431" xml:space="preserve" y="528">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795532962" ObjectName="122723089845860002:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33000345">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="24" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="1431" xml:space="preserve" y="578">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795532963" ObjectName="122723089845860003:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33000346">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="24" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="1431" xml:space="preserve" y="628">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795532964" ObjectName="122723089845860004:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33000347">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="24" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="1431" xml:space="preserve" y="678">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795532965" ObjectName="122723089845860005:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33000348">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="24" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="1431" xml:space="preserve" y="728">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795532966" ObjectName="122723089845860006:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33000349">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="24" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="1431" xml:space="preserve" y="778">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795532967" ObjectName="122723089845860007:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33000350">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="24" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="1431" xml:space="preserve" y="818">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795532968" ObjectName="122723089845860008:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33000352">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="24" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="1431" xml:space="preserve" y="868">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795532969" ObjectName="122723089845860009:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33000354">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="24" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="1431" xml:space="preserve" y="918">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795532970" ObjectName="122723089845860010:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33000356">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="24" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="1431" xml:space="preserve" y="968">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795532971" ObjectName="122723089845860011:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33000358">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="24" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="1431" xml:space="preserve" y="1018">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795532972" ObjectName="122723089845860012:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33000360">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="24" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="1431" xml:space="preserve" y="1068">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795532973" ObjectName="122723089845860013:M" Plane="0"/>
  </metadata>
 </g>
</g>
<g id="Text_Layer">
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="56" font-size="32" font-width="32" stroke="rgb(0,0,0)" writing-mode="lr" x="610" xml:space="preserve" y="80">10kV曼西缇开闭所</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="22" font-size="22" font-width="22" stroke="rgb(255,255,254)" writing-mode="lr" x="266" xml:space="preserve" y="182">10kV备自投自投方式1软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="21" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="266" xml:space="preserve" y="231">10kV备自投自投方式2软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="22" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="266" xml:space="preserve" y="282">10kV备自投自投方式3软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="22" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="266" xml:space="preserve" y="332">10kV备自投自投方式4软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="22" font-size="19" font-width="19" stroke="rgb(255,255,254)" writing-mode="lr" x="266" xml:space="preserve" y="382">10kV备自投电源1过负荷减载软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="22" font-size="19" font-width="19" stroke="rgb(255,255,254)" writing-mode="lr" x="266" xml:space="preserve" y="432">10kV备自投电源2过负荷减载软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="21" font-size="19" font-width="19" stroke="rgb(255,255,254)" writing-mode="lr" x="266" xml:space="preserve" y="469">10kV备自投信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="38" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="102" xml:space="preserve" y="339">备自投</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="21" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="996" xml:space="preserve" y="184">10kV果西线I回线010保护测控装置运行定值区号</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="16" font-width="16" stroke="rgb(255,255,254)" writing-mode="lr" x="996" xml:space="preserve" y="233">10kV果西线II回线020保护测控装置运行定值区号</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="22" font-size="19" font-width="19" stroke="rgb(255,255,254)" writing-mode="lr" x="996" xml:space="preserve" y="285">10kV备用七线01A_保护测控装置运行定值区号</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="22" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="996" xml:space="preserve" y="335">10kV备用八线01C_保护测控装置运行定值区号</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="22" font-size="19" font-width="19" stroke="rgb(255,255,254)" writing-mode="lr" x="996" xml:space="preserve" y="385">10kV备用九线01D_保护测控装置运行定值区号</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="22" font-size="19" font-width="19" stroke="rgb(255,255,254)" writing-mode="lr" x="996" xml:space="preserve" y="435">10kV备用十线01E_保护测控装置运行定值区号</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="22" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="996" xml:space="preserve" y="473">10kV备用十一线01F_保护测控装置运行定值区号</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="37" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="832" xml:space="preserve" y="788">定值区</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="21" font-size="16" font-width="16" stroke="rgb(255,255,254)" writing-mode="lr" x="996" xml:space="preserve" y="526">10kV3号配电室I回01G_保护测控装置运行定值区号</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="17" font-width="17" stroke="rgb(255,255,254)" writing-mode="lr" x="996" xml:space="preserve" y="575">10kV备用十二线01H_保护测控装置运行定值区号</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="21" font-size="19" font-width="19" stroke="rgb(255,255,254)" writing-mode="lr" x="996" xml:space="preserve" y="626">10kV备用一线02A_保护测控装置运行定值区号</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="21" font-size="19" font-width="19" stroke="rgb(255,255,254)" writing-mode="lr" x="996" xml:space="preserve" y="676">10kV备用二线02C_保护测控装置运行定值区号</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="996" xml:space="preserve" y="728">10kV备用三线02D_保护测控装置运行定值区号</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="996" xml:space="preserve" y="778">10kV备用四线02E_保护测控装置运行定值区号</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="21" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="996" xml:space="preserve" y="814">10kV备用五线02F_保护测控装置运行定值区号</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="21" font-size="16" font-width="16" stroke="rgb(255,255,254)" writing-mode="lr" x="996" xml:space="preserve" y="864">10kV3号配电室II回02G_保护测控装置运行定值区号</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="21" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="996" xml:space="preserve" y="914">10kV备用六线02H_保护测控装置运行定值区号</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="21" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="996" xml:space="preserve" y="964">1站用变01B保护测控装置运行定值区号</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="21" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="996" xml:space="preserve" y="1014">2站用变02B保护测控装置运行定值区号</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="21" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="996" xml:space="preserve" y="1064">10kV分段012保护测控装置运行定值区号</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href="曼西缇开闭所.fac.svg"><rect fill-opacity="0" height="63" stroke-opacity="0" stroke-width="2" width="298" x="588" y="20"/></g>
</g>
</svg>