<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1500" id="thSvg" viewBox="0 0 3000 1500" width="3000">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="SynchronousMachine:cc发电机_0" viewBox="0,0,56,54">
 <use Plane="0" x="28" xlink:href="#terminal" y="8"/>
 <circle AFMask="2147483647" Plane="0" cx="28" cy="26" fill="none" r="18" stroke="rgb(255,0,0)" stroke-width="2"/>
 <path AFMask="2147483647" Plane="0" d="M 28 27 A 9 9 0 1 0 46 27" fill="none" stroke="rgb(255,0,0)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="0" d="M 10 28 A 9 9 180 1 0 28 28" fill="none" stroke="rgb(255,0,0)" stroke-width="1"/>
</symbol>
<symbol id="Breaker:hz断路器1_0" viewBox="0,0,14,34">
 <use Plane="0" x="7" xlink:href="#terminal" y="30"/>
 <use Plane="0" x="7" xlink:href="#terminal" y="4"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="26" stroke="rgb(110,0,0)" stroke-width="1" transform="rotate(0,7,17)" width="12" x="1" y="4"/>
</symbol>
<symbol id="Breaker:hz断路器1_1" viewBox="0,0,14,34">
 <use Plane="0" x="7" xlink:href="#terminal" y="30"/>
 <use Plane="0" x="7" xlink:href="#terminal" y="4"/>
 <rect AFMask="2147483647" Plane="0" fill="rgb(110,0,0)" height="26" stroke="rgb(110,0,0)" stroke-width="1" transform="rotate(0,7,17)" width="12" x="1" y="4"/>
</symbol>
<symbol id="Status:微机保护设备3_0" viewBox="0,0,46,26">
 <circle AFMask="2147483647" Plane="0" cx="36" cy="8" fill="rgb(0,255,0)" r="5" stroke="rgb(0,255,0)" stroke-width="1"/>
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="9" font-size="8" font-width="8" stroke="rgb(0,255,0)" writing-mode="lr" x="29" xml:space="preserve" y="23">投运</text>
</symbol>
<symbol id="Status:微机保护设备3_1" viewBox="0,0,46,26">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="9" font-size="8" font-width="8" stroke="rgb(255,255,254)" writing-mode="lr" x="29" xml:space="preserve" y="22">停运</text>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(255,255,254)" r="5" stroke="rgb(255,255,254)" stroke-width="1"/>
</symbol>
<symbol id="Status:微机保护设备3_2" viewBox="0,0,46,26">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="9" font-size="8" font-width="8" stroke="rgb(255,255,0)" writing-mode="lr" x="29" xml:space="preserve" y="21">调试</text>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Status:微机保护设备3_3" viewBox="0,0,46,26">
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(255,255,0)" r="5" stroke="rgb(255,0,0)" stroke-width="1"/>
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="8" font-size="7" font-width="7" stroke="rgb(255,0,0)" writing-mode="lr" x="29" xml:space="preserve" y="22">检修</text>
</symbol>
<symbol id="Status:微机保护设备3_4" viewBox="0,0,46,26">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="9" font-size="8" font-width="8" stroke="rgb(255,255,0)" writing-mode="lr" x="28" xml:space="preserve" y="22">动作</text>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(255,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Status:微机保护设备3_5" viewBox="0,0,46,26">
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(255,170,255)" stroke-width="1"/>
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="8" font-size="8" font-width="8" stroke="rgb(255,85,255)" writing-mode="lr" x="29" xml:space="preserve" y="20">告警</text>
</symbol>
<symbol id="Status:微机保护设备3_6" viewBox="0,0,46,26">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="10" font-size="8" font-width="8" stroke="rgb(255,255,0)" writing-mode="lr" x="29" xml:space="preserve" y="23">变位</text>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Status:微机保护设备3_7" viewBox="0,0,46,26">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="8" font-size="8" font-width="8" stroke="rgb(255,0,0)" writing-mode="lr" x="29" xml:space="preserve" y="20">中断</text>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(255,0,0)" r="5" stroke="rgb(255,0,0)" stroke-width="1"/>
</symbol>
<symbol id="Status:微机保护设备3_8" viewBox="0,0,46,26">
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(255,0,127)" r="5" stroke="rgb(255,0,127)" stroke-width="1"/>
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="4" font-size="4" font-width="5" stroke="rgb(255,0,127)" writing-mode="lr" x="30" xml:space="preserve" y="17">抑制</text>
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="4" font-size="4" font-width="5" stroke="rgb(255,0,127)" writing-mode="lr" x="30" xml:space="preserve" y="21">告警</text>
</symbol>
<symbol id="Status:微机保护设备3_9" viewBox="0,0,46,26">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="8" font-size="4" font-width="4" stroke="rgb(0,0,255)" writing-mode="lr" x="28" xml:space="preserve" y="22">告警抑制</text>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Status:微机保护设备3_10" viewBox="0,0,46,26">
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="6" font-size="2" font-width="2" stroke="rgb(0,0,255)" writing-mode="lr" x="28" xml:space="preserve" y="19">正在修改定值</text>
</symbol>
<symbol id="Status:微机保护设备3_11" viewBox="0,0,46,26">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="7" font-size="2" font-width="2" stroke="rgb(0,0,255)" writing-mode="lr" x="28" xml:space="preserve" y="20">闭锁修改定值</text>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Status:微机保护设备3_12" viewBox="0,0,46,26">
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="9" font-size="8" font-width="8" stroke="rgb(0,0,255)" writing-mode="lr" x="29" xml:space="preserve" y="21">动作</text>
</symbol>
<symbol id="Status:微机保护设备3_13" viewBox="0,0,46,26">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="9" font-size="8" font-width="8" stroke="rgb(0,0,255)" writing-mode="lr" x="29" xml:space="preserve" y="21">告警</text>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Status:微机保护设备3_14" viewBox="0,0,46,26">
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="9" font-size="8" font-width="8" stroke="rgb(0,0,255)" writing-mode="lr" x="29" xml:space="preserve" y="21">变位</text>
</symbol>
<symbol id="Status:微机保护设备3_15" viewBox="0,0,46,26">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="7" font-size="5" font-width="5" stroke="rgb(0,0,255)" writing-mode="lr" x="28" xml:space="preserve" y="21">未确认</text>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="版纳_220kV_青云变" InitShowingPlane="0," fill="rgb(0,0,0)" height="1500" width="3000" x="0" y="0"/>
</g>
<g id="Breaker_Layer">
 <g id="100000012">
  <use class="kv330kV" height="34" transform="rotate(0,93,510) scale(1,1) translate(-7,-17)" width="14" x="93" xlink:href="#Breaker:hz断路器1_0" y="510"/>
  <metadata>
   <cge:PSR_Ref AFMask="34831" ObjectID="114560315521244398" ObjectName="114560315521244398" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319936238"/>
  </metadata>
 <rect fill="white" height="34" opacity="0" stroke="white" transform="rotate(0,93,510) scale(1,1) translate(-7,-17)" width="14" x="93" y="510"/></g>
</g>
<g id="Generator_Layer">
 <g id="104000013">
  <use class="kv330kV" height="54" transform="rotate(0,93,584) scale(1,1) translate(-28,-8)" width="56" x="93" xlink:href="#SynchronousMachine:cc发电机_0" y="584"/>
  <metadata>
   <cge:PSR_Ref AFMask="34831" ObjectID="115686215428079761" ObjectName="115686215428079761" Plane="0"/>
  </metadata>
 <rect fill="white" height="54" opacity="0" stroke="white" transform="rotate(0,93,584) scale(1,1) translate(-28,-8)" width="56" x="93" y="584"/></g>
</g>
<g id="ACLineSegment_Layer">
 <g id="36000011">
 <path d="M 93 428 L 91 152" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Ref AFMask="34847" ObjectID="ST=-1/LN=-1" ObjectName="ST=-1/LN=-1" Plane="0"/>
  </metadata>
 <path d="M 93 428 L 91 152" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="Status_Layer">
 <g id="126000018">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="279786126850392065" ObjectName="279786126850392065" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,553,344) scale(4.95,4.95) translate(-464.283,-288.505)" width="46" x="553" y="344"/></g>
</g>
<g id="Link_Layer">
 <g id="34000009">
 <path d="M 93 497 L 93 428" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34847" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000012_1" Pin1InfoVect0LinkObjId="36000011_0" Plane="0"/>
  </metadata>
 <path d="M 93 497 L 93 428" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000010">
 <path d="M 93 584 L 93 523" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="34847" MaxPinNum="2" Pin0InfoVect0LinkObjId="104000013_0" Pin1InfoVect0LinkObjId="100000012_0" Plane="0"/>
  </metadata>
 <path d="M 93 584 L 93 523" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="MeasurementValue_Layer">
 <g id="33000006">
 <text AFMask="34831" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(0,255,0)" writing-mode="lr" x="101" xml:space="preserve" y="192">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="34831" ObjectID="116812330083287345" ObjectName="ST=-1/LN=-1:I" Plane="0"/>
  </metadata>
 </g>
 <g id="33000007">
 <text AFMask="34831" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,127)" writing-mode="lr" x="101" xml:space="preserve" y="172">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="34831" ObjectID="116812287133614385" ObjectName="ST=-1/LN=-1:Q" Plane="0"/>
  </metadata>
 </g>
 <g id="33000008">
 <text AFMask="34831" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(0,169,254)" writing-mode="lr" x="101" xml:space="preserve" y="152">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="34831" ObjectID="116812244183941425" ObjectName="ST=-1/LN=-1:P" Plane="0"/>
  </metadata>
 </g>
</g>
<g id="Text_Layer">
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(128,128,128)" writing-mode="lr" x="65" xml:space="preserve" y="122">110kV黄湾线</text>
 <text AFMask="34847" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="60" font-size="60" font-width="60" stroke="rgb(128,128,128)" writing-mode="lr" x="23" xml:space="preserve" y="73">湾洲站</text>
</g>
<g id="Poke_Layer">
 <g Plane="0" href="HZ_110kV_石湾站_050528.fac.svg"><rect fill="none" height="85" rect-style="1" stroke="rgb(255,255,254)" stroke-width="2" width="212" x="6" y="6"/></g>
</g>
</svg>