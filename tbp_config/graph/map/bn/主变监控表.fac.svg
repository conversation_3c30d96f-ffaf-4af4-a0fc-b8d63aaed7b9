<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1650" id="thSvg" viewBox="0 0 3610 1650" width="3610">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="Status:bn_不变化颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(0,0,255)" height="1" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_不变化颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(85,0,255)" height="1" stroke="rgb(85,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_越限颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_越限颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:越下限告警_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,0,255)" height="1" stroke="rgb(170,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:越下限告警_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:正常状态显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,127)" height="1" stroke="rgb(255,255,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:正常状态显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(85,0,255)" height="1" stroke="rgb(85,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="版纳_dp" InitShowingPlane="0," fill="rgb(0,0,0)" height="1650" width="3610" x="0" y="0"/>
</g>
<g id="Other_Layer">
 
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1857" x2="3511" y1="855" y2="855"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1229" x2="1229" y1="270" y2="1579"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="636" x2="636" y1="270" y2="1577"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="413" x2="413" y1="270" y2="1581"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="91" x2="1770" y1="1434" y2="1434"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="274" x2="1624" y1="901" y2="901"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="581" x2="581" y1="512" y2="512"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="2052" x2="3366" y1="756" y2="756"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1858" x2="3511" y1="610" y2="610"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="91" x2="1770" y1="1530" y2="1530"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="91" x2="1770" y1="1240" y2="1239"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="91" x2="1770" y1="1095" y2="1095"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="91" x2="1770" y1="999" y2="999"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="274" x2="1626" y1="804" y2="804"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="274" x2="1624" y1="708" y2="708"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="274" x2="1626" y1="611" y2="611"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="91" x2="1770" y1="562" y2="562"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="274" x2="1624" y1="514" y2="514"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="91" x2="1770" y1="466" y2="466"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="274" x2="1624" y1="417" y2="417"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1625" x2="1625" y1="225" y2="1579"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1427" x2="1427" y1="222" y2="1579"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1022" x2="1022" y1="224" y2="1581"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="834" x2="834" y1="270" y2="1579"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="91" x2="1770" y1="1337" y2="1337"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="89" x2="1770" y1="368" y2="368"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="91" x2="1626" y1="269" y2="269"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="274" x2="274" y1="223" y2="1579"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="91" x2="1770" y1="221" y2="221"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="2052" x2="3368" y1="806" y2="806"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="274" x2="1626" y1="1482" y2="1482"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="91" x2="1770" y1="657" y2="657"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="2051" x2="3368" y1="466" y2="466"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="274" x2="1624" y1="1385" y2="1385"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="91" x2="1770" y1="1192" y2="1192"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="274" x2="1626" y1="1143" y2="1143"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="274" x2="1626" y1="1046" y2="1046"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="92" x2="1770" y1="853" y2="852"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="90" x2="1770" y1="756" y2="757"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="274" x2="1626" y1="1288" y2="1288"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="274" x2="1626" y1="950" y2="950"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="89" x2="89" y1="220" y2="1583"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="2187" x2="2187" y1="270" y2="1577"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="3368" x2="3368" y1="226" y2="1581"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="3173" x2="3173" y1="226" y2="1579"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="2977" x2="2977" y1="272" y2="1579"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="2775" x2="2775" y1="223" y2="1577"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="2392" x2="2392" y1="269" y2="1579"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="2587" x2="2587" y1="224" y2="1581"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1855" x2="1855" y1="224" y2="1581"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="2051" x2="2051" y1="224" y2="1583"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1856" x2="3367" y1="269" y2="269"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1861" x2="3514" y1="1580" y2="1580"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1858" x2="3511" y1="1533" y2="1533"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="2053" x2="3368" y1="1483" y2="1483"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1856" x2="3511" y1="1435" y2="1435"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="2053" x2="3369" y1="1386" y2="1386"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1857" x2="3511" y1="1337" y2="1337"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="2055" x2="3369" y1="1290" y2="1290"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1856" x2="3511" y1="1242" y2="1242"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="2052" x2="3368" y1="1193" y2="1193"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1856" x2="3511" y1="1145" y2="1145"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1856" x2="3511" y1="1048" y2="1048"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="2052" x2="3369" y1="998" y2="998"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1857" x2="3511" y1="950" y2="950"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="2053" x2="3366" y1="901" y2="901"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="2052" x2="3366" y1="1097" y2="1097"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1770" x2="1770" y1="221" y2="1581"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1855" x2="3511" y1="222" y2="222"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="3513" x2="3513" y1="226" y2="1583"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1855" x2="3511" y1="320" y2="320"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="89" x2="1770" y1="321" y2="321"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1857" x2="3511" y1="514" y2="514"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="2052" x2="3368" y1="562" y2="562"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="2052" x2="3368" y1="368" y2="368"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1858" x2="3511" y1="417" y2="417"/>
 
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="2055" x2="3374" y1="664" y2="664"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1858" x2="3516" y1="715" y2="715"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="93" x2="1772" y1="1581" y2="1581"/>
</g>
<g id="Status_Layer">
 <g id="126009324">
  <use class="kv-1" height="40" transform="rotate(0,2457,181) scale(0.857,0.857) translate(379.978,10.2019)" width="60" x="2457" xlink:href="#Status:正常状态显示_0" y="181"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,2457,181) scale(0.857,0.857) translate(379.978,10.2019)" width="60" x="2457" y="181"/></g>
 <g id="126009323">
  <use class="kv-1" height="40" transform="rotate(0,2689,180) scale(0.893,0.893) translate(292.198,1.56775)" width="60" x="2689" xlink:href="#Status:bn_不变化颜色显示_0" y="180"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,2689,180) scale(0.893,0.893) translate(292.198,1.56775)" width="60" x="2689" y="180"/></g>
 <g id="126009322">
  <use class="kv-1" height="40" transform="rotate(0,2923,180) scale(0.896,0.896) translate(309.277,0.892841)" width="60" x="2923" xlink:href="#Status:bn_越限颜色显示_0" y="180"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,2923,180) scale(0.896,0.896) translate(309.277,0.892841)" width="60" x="2923" y="180"/></g>
 <g id="126009396">
  <use class="kv-1" height="40" transform="rotate(0,3145,179) scale(1,1) translate(-30,-20)" width="60" x="3145" xlink:href="#Status:越下限告警_0" y="179"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,3145,179) scale(1,1) translate(-30,-20)" width="60" x="3145" y="179"/></g>
</g>
<g id="MeasurementValue_Layer">
 <g id="33009321">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1486" xml:space="preserve" y="360">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638279" ObjectName="版纳_500kV_版纳变\XFMR_＃1主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009320">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1090" xml:space="preserve" y="360">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709133" ObjectName="版纳_500kV_版纳变\版纳_500kV_版纳变/500kV\XF_500kV.＃1主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009319">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1486" xml:space="preserve" y="989">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638020" ObjectName="版纳_110kV_嘎栋变\XFMR_#3主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009318">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1090" xml:space="preserve" y="989">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708501" ObjectName="版纳_110kV_嘎栋变\版纳_110kV_嘎栋变-110kV\XF_#3主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009317">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="1493" xml:space="preserve" y="1568">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638143" ObjectName="版纳_35kV_景哈变\XFMR_1号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009316">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="3234" xml:space="preserve" y="1567">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638457" ObjectName="版纳_35kV_关坪变\XFMR_#1主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009315">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="3231" xml:space="preserve" y="1523">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638167" ObjectName="版纳_35kV_勐旺变\XFMR_2号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009314">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="3231" xml:space="preserve" y="1473">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638166" ObjectName="版纳_35kV_勐旺变\XFMR_1号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009313">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="3231" xml:space="preserve" y="1424">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638175" ObjectName="版纳_35kV_基诺变\XFMR_2号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009312">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="3231" xml:space="preserve" y="1375">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093826534834398" ObjectName="版纳_35kV_基诺变\XFMR_1号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009311">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="3231" xml:space="preserve" y="1326">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638190" ObjectName="版纳_35kV_景讷变\XFMR_35kV2号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009310">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="3231" xml:space="preserve" y="1280">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638185" ObjectName="版纳_35kV_景讷变\XFMR_35kV1号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009309">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="3231" xml:space="preserve" y="1230">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638241" ObjectName="版纳_35kV_普文变\XFMR_2号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009308">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="3231" xml:space="preserve" y="1182">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638240" ObjectName="版纳_35kV_普文变\XFMR_1号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009307">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="3231" xml:space="preserve" y="1135">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638179" ObjectName="版纳_35kV_茶园变\XFMR_2号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009306">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="3231" xml:space="preserve" y="1085">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638178" ObjectName="版纳_35kV_茶园变\XFMR_1号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009305">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3231" xml:space="preserve" y="1038">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638173" ObjectName="版纳_35kV_勐养变\XFMR_35kV2号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009304">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3231" xml:space="preserve" y="988">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638171" ObjectName="版纳_35kV_勐养变\XFMR_35kV1号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009303">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3231" xml:space="preserve" y="941">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638473" ObjectName="版纳_35kV_城西变\XFMR_2号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009302">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3231" xml:space="preserve" y="892">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638472" ObjectName="版纳_35kV_城西变\XFMR_1号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009301">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3231" xml:space="preserve" y="845">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795528234" ObjectName="122723089845855274:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33009300">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3231" xml:space="preserve" y="797">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795528233" ObjectName="122723089845855273:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33009299">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3231" xml:space="preserve" y="748">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795528246" ObjectName="122723089845855286:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33009298">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3231" xml:space="preserve" y="412">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304637972" ObjectName="版纳_110kV_佛海变\XFMR_#2主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009297">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3231" xml:space="preserve" y="359">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304637971" ObjectName="版纳_110kV_佛海变\XFMR_#1主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009296">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="1486" xml:space="preserve" y="1521">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638040" ObjectName="版纳_110kV_勐海变\XFMR_2号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009295">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="1486" xml:space="preserve" y="1472">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638039" ObjectName="版纳_110kV_勐海变\XFMR_1号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009294">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="1486" xml:space="preserve" y="1423">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093826534834478" ObjectName="版纳_110kV_金象变\XFMR_2号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009293">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="1486" xml:space="preserve" y="1375">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093826534834477" ObjectName="版纳_110kV_金象变\XFMR_1号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009292">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="1486" xml:space="preserve" y="1328">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638383" ObjectName="版纳_110kV_勐腊变\XFMR_2号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009291">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="1486" xml:space="preserve" y="1279">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638382" ObjectName="版纳_110kV_勐腊变\XFMR_1号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009290">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="1486" xml:space="preserve" y="1231">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638045" ObjectName="版纳_110kV_勐龙变\XFMR_2号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009289">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="1486" xml:space="preserve" y="1182">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638042" ObjectName="版纳_110kV_大渡岗变\XFMR_2号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009288">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="1486" xml:space="preserve" y="1133">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638041" ObjectName="版纳_110kV_大渡岗变\XFMR_1号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009287">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="1486" xml:space="preserve" y="1085">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304637989" ObjectName="版纳_110kV_勐罕变\XFMR_#2主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009286">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1486" xml:space="preserve" y="1036">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304637990" ObjectName="版纳_110kV_勐罕变\XFMR_#1主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009285">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1486" xml:space="preserve" y="941">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638019" ObjectName="版纳_110kV_嘎栋变\XFMR_#2主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009284">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1486" xml:space="preserve" y="892">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638018" ObjectName="版纳_110kV_嘎栋变\XFMR_#1主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009283">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1486" xml:space="preserve" y="846">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638049" ObjectName="版纳_110kV_曼弄枫变\XFMR_2号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009282">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1486" xml:space="preserve" y="796">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638048" ObjectName="版纳_110kV_曼弄枫变\XFMR_1号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009281">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1486" xml:space="preserve" y="748">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638420" ObjectName="版纳_110kV_城南变\XFMR_#2主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009280">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1486" xml:space="preserve" y="698">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638419" ObjectName="版纳_110kV_城南变\XFMR_#1主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009278">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1486" xml:space="preserve" y="602">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638369" ObjectName="版纳_220kV_傣乡变\XFMR_#1主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009277">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1486" xml:space="preserve" y="555">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795520326" ObjectName="122723089845847366:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33009276">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1486" xml:space="preserve" y="506">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795524691" ObjectName="122723089845851731:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33009275">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1486" xml:space="preserve" y="456">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795531163" ObjectName="122723089845858203:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33009274">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1486" xml:space="preserve" y="408">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795531151" ObjectName="122723089845858191:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33009273">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="1093" xml:space="preserve" y="1563">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708759" ObjectName="版纳_35kV_景哈变\版纳_35kV_景哈变-35kV\XF_1号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009272">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="2841" xml:space="preserve" y="1567">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709514" ObjectName="版纳_35kV_关坪变\版纳_35kV_关坪变/35kV\XF_#1主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009271">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="2838" xml:space="preserve" y="1523">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708808" ObjectName="版纳_35kV_勐旺变\版纳_35kV_勐旺变-35kV\XF_2号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009270">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="2838" xml:space="preserve" y="1473">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708806" ObjectName="版纳_35kV_勐旺变\版纳_35kV_勐旺变-35kV\XF_1号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009269">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="2838" xml:space="preserve" y="1424">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708824" ObjectName="版纳_35kV_基诺变\版纳_35kV_基诺变/35kV\XF_2号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009268">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="2838" xml:space="preserve" y="1375">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708822" ObjectName="版纳_35kV_基诺变\版纳_35kV_基诺变/35kV\XF_1号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009267">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="2838" xml:space="preserve" y="1326">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708854" ObjectName="版纳_35kV_景讷变\版纳_35kV_景讷变-35kV\XF_35kV.35kV2号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009266">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="2838" xml:space="preserve" y="1280">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708844" ObjectName="版纳_35kV_景讷变\版纳_35kV_景讷变-35kV\XF_35kV.35kV1号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009265">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="2838" xml:space="preserve" y="1230">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709049" ObjectName="版纳_35kV_普文变\版纳_35kV_普文变-35kV\XF_2号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009264">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="2838" xml:space="preserve" y="1182">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709047" ObjectName="版纳_35kV_普文变\版纳_35kV_普文变-35kV\XF_1号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009263">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="2838" xml:space="preserve" y="1135">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708832" ObjectName="版纳_35kV_茶园变\版纳_35kV_茶园变-35kV\XF_2号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009262">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="2838" xml:space="preserve" y="1085">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708830" ObjectName="版纳_35kV_茶园变\版纳_35kV_茶园变-35kV\XF_1号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009261">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="2838" xml:space="preserve" y="1037">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708820" ObjectName="版纳_35kV_勐养变\版纳_35kV_勐养变-35kV\XF_35kV.35kV2号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009260">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="2838" xml:space="preserve" y="988">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708816" ObjectName="版纳_35kV_勐养变\版纳_35kV_勐养变-35kV\XF_35kV.35kV1号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009259">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="2838" xml:space="preserve" y="940">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709546" ObjectName="版纳_35kV_城西变\版纳_35kV_城西变/35kV\XF_2号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009258">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="2838" xml:space="preserve" y="892">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709544" ObjectName="版纳_35kV_城西变\版纳_35kV_城西变/35kV\XF_1号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009257">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="2838" xml:space="preserve" y="845">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709252" ObjectName="版纳_35kV_城中变\版纳_35kV_城中变/35kV\XF_3号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009256">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="2838" xml:space="preserve" y="798">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709250" ObjectName="版纳_35kV_城中变\版纳_35kV_城中变/35kV\XF_2号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009255">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="2838" xml:space="preserve" y="749">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709248" ObjectName="版纳_35kV_城中变\版纳_35kV_城中变/35kV\XF_1号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009254">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="2838" xml:space="preserve" y="410">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708394" ObjectName="版纳_110kV_佛海变\版纳_110kV_佛海变-110kV\XF_#2主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009253">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="2838" xml:space="preserve" y="359">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708391" ObjectName="版纳_110kV_佛海变\版纳_110kV_佛海变-110kV\XF_#1主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009252">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="1090" xml:space="preserve" y="1521">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708545" ObjectName="版纳_110kV_勐海变\版纳_110kV_勐海变-110kV\XF_2号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009251">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="1090" xml:space="preserve" y="1472">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708542" ObjectName="版纳_110kV_勐海变\版纳_110kV_勐海变-110kV\XF_1号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009250">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="1090" xml:space="preserve" y="1423">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709080" ObjectName="版纳_110kV_金象变\版纳_110kV_金象变110kV\XF_2号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009249">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="1090" xml:space="preserve" y="1375">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709077" ObjectName="版纳_110kV_金象变\版纳_110kV_金象变110kV\XF_1号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009248">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="1090" xml:space="preserve" y="1328">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709358" ObjectName="版纳_110kV_勐腊变\版纳_110kV_勐腊变/110kV\XF_2号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009247">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="1090" xml:space="preserve" y="1279">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709355" ObjectName="版纳_110kV_勐腊变\版纳_110kV_勐腊变/110kV\XF_1号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009246">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="1090" xml:space="preserve" y="1231">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708558" ObjectName="版纳_110kV_勐龙变\版纳_110kV_勐龙变-110kV\XF_2号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009245">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="1090" xml:space="preserve" y="1182">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708551" ObjectName="版纳_110kV_大渡岗变\版纳_110kV_大渡岗变-110kV\XF_2号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009244">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="1090" xml:space="preserve" y="1133">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708548" ObjectName="版纳_110kV_大渡岗变\版纳_110kV_大渡岗变-110kV\XF_1号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009243">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="1090" xml:space="preserve" y="1085">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708431" ObjectName="版纳_110kV_勐罕变\版纳_110kV_勐罕变-110kV\XF_#2主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009242">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1090" xml:space="preserve" y="1036">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708434" ObjectName="版纳_110kV_勐罕变\版纳_110kV_勐罕变-110kV\XF_#1主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009241">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1090" xml:space="preserve" y="941">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708498" ObjectName="版纳_110kV_嘎栋变\版纳_110kV_嘎栋变-110kV\XF_#2主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009240">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1090" xml:space="preserve" y="892">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708495" ObjectName="版纳_110kV_嘎栋变\版纳_110kV_嘎栋变-110kV\XF_#1主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009239">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1090" xml:space="preserve" y="846">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708567" ObjectName="版纳_110kV_曼弄枫变\版纳_110kV_曼弄枫变-110kV\XF_2号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009238">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1090" xml:space="preserve" y="796">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708565" ObjectName="版纳_110kV_曼弄枫变\版纳_110kV_曼弄枫变-110kV\XF_1号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009237">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1090" xml:space="preserve" y="748">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709436" ObjectName="版纳_110kV_城南变\版纳_110kV_城南变/110kV\XF_#2主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009236">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1090" xml:space="preserve" y="698">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709434" ObjectName="版纳_110kV_城南变\版纳_110kV_城南变/110kV\XF_#1主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009234">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1090" xml:space="preserve" y="602">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709326" ObjectName="版纳_220kV_傣乡变\版纳_220kV_傣乡变－220kV\XF_#1主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009233">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1090" xml:space="preserve" y="555">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708466" ObjectName="版纳_220kV_黎明变\版纳_220kV_黎明变-220kV\XF_#2主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009232">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1090" xml:space="preserve" y="506">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708463" ObjectName="版纳_220kV_黎明变\版纳_220kV_黎明变-220kV\XF_#1主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009231">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1090" xml:space="preserve" y="456">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709481" ObjectName="版纳_220kV_景洪变\版纳_220kV_景洪变/220kV\XF_2号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009230">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1090" xml:space="preserve" y="408">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709478" ObjectName="版纳_220kV_景洪变\版纳_220kV_景洪变/220kV\XF_1号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009332">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1656" xml:space="preserve" y="430">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387120" ObjectName="121878664915714160:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33009334">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1656" xml:space="preserve" y="529">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387121" ObjectName="121878664915714161:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33009336">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1657" xml:space="preserve" y="625">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387263" ObjectName="121878664915714303:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33009338">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1656" xml:space="preserve" y="722">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387107" ObjectName="121878664915714147:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33009340">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1656" xml:space="preserve" y="819">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387110" ObjectName="121878664915714150:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33009342">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1656" xml:space="preserve" y="941">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387108" ObjectName="121878664915714148:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33009344">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="1656" xml:space="preserve" y="1059">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387116" ObjectName="121878664915714156:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33009346">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="1656" xml:space="preserve" y="1157">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387118" ObjectName="121878664915714158:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33009348">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="1656" xml:space="preserve" y="1231">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387114" ObjectName="121878664915714154:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33009349">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="1656" xml:space="preserve" y="1302">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387112" ObjectName="121878664915714152:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33009351">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="1656" xml:space="preserve" y="1400">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387119" ObjectName="121878664915714159:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33009353">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="1656" xml:space="preserve" y="1495">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387111" ObjectName="121878664915714151:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33009355">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3399" xml:space="preserve" y="384">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387117" ObjectName="121878664915714157:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33009392">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1655" xml:space="preserve" y="360">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387153" ObjectName="121878664915714193:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33009391">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="1661" xml:space="preserve" y="1570">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387152" ObjectName="121878664915714192:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33009390">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="3401" xml:space="preserve" y="1568">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709514" ObjectName="版纳_35kV_关坪变\版纳_35kV_关坪变/35kV\XF_#1主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009388">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="3399" xml:space="preserve" y="1496">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387150" ObjectName="121878664915714190:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33009386">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="3399" xml:space="preserve" y="1400">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387149" ObjectName="121878664915714189:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33009384">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="3399" xml:space="preserve" y="1303">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387148" ObjectName="121878664915714188:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33009382">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="3399" xml:space="preserve" y="1206">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387147" ObjectName="121878664915714187:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33009380">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="3399" xml:space="preserve" y="1111">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387146" ObjectName="121878664915714186:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33009378">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3399" xml:space="preserve" y="1012">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387145" ObjectName="121878664915714185:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33009376">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3399" xml:space="preserve" y="916">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387144" ObjectName="121878664915714184:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33009395">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3399" xml:space="preserve" y="799">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387143" ObjectName="121878664915714183:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33009410">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="2838" xml:space="preserve" y="505">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709276" ObjectName="版纳_110kV_勐仑变\版纳_110kV_勐仑变/110kV\XF_#2主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009409">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="2838" xml:space="preserve" y="461">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709279" ObjectName="版纳_110kV_勐仑变\版纳_110kV_勐仑变/110kV\XF_#1主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009411">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3231" xml:space="preserve" y="462">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795528661" ObjectName="122723089845855701:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33009412">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3231" xml:space="preserve" y="504">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638347" ObjectName="版纳_110kV_勐仑变\XFMR_#2主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009413">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3399" xml:space="preserve" y="485">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387261" ObjectName="121878664915714301:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33009436">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="2838" xml:space="preserve" y="554">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709176" ObjectName="版纳_110kV_辉凰变\版纳_110kV_辉凰变/110kV\XF_1号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009437">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3231" xml:space="preserve" y="555">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638298" ObjectName="版纳_110kV_辉凰变\XFMR_1号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009438">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3399" xml:space="preserve" y="576">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387162" ObjectName="121878664915714202:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33009444">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="2838" xml:space="preserve" y="602">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709178" ObjectName="版纳_110kV_辉凰变\版纳_110kV_辉凰变/110kV\XF_2号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009445">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3231" xml:space="preserve" y="600">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638299" ObjectName="版纳_110kV_辉凰变\XFMR_2号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009464">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3234" xml:space="preserve" y="702">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093826534834501" ObjectName="版纳_110kV_江北变\XFMR_2号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009463">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3234" xml:space="preserve" y="655">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093826534834500" ObjectName="版纳_110kV_江北变\XFMR_1号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33009462">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="2838" xml:space="preserve" y="702">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709128" ObjectName="版纳_110kV_江北变\版纳_110kV_江北变/110kV\XF_2号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009461">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="2838" xml:space="preserve" y="655">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709125" ObjectName="版纳_110kV_江北变\版纳_110kV_江北变/110kV\XF_1号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009465">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3405" xml:space="preserve" y="678">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387142" ObjectName="121878664915714182:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33009475">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1090" xml:space="preserve" y="651">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709431" ObjectName="版纳_220kV_傣乡变\版纳_220kV_傣乡变－220kV\XF_＃2主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33009476">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1486" xml:space="preserve" y="651">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795530943" ObjectName="122723089845857983:M" Plane="0"/>
  </metadata>
 </g>
</g>
<g id="Text_Layer">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="337" xml:space="preserve" y="362">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="112" xml:space="preserve" y="362">500kV版纳变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1314" xml:space="preserve" y="988">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="922" xml:space="preserve" y="988">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="725" xml:space="preserve" y="988">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="510" xml:space="preserve" y="988">40</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="337" xml:space="preserve" y="988">3</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="39" stroke="rgb(255,255,254)" writing-mode="lr" x="2496" xml:space="preserve" y="196">实时态</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="39" stroke="rgb(255,255,254)" writing-mode="lr" x="2963" xml:space="preserve" y="196">越上限</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="39" stroke="rgb(255,255,254)" writing-mode="lr" x="2733" xml:space="preserve" y="196">不变化</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="3055" xml:space="preserve" y="844">6.4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="3055" xml:space="preserve" y="796">6.4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="3055" xml:space="preserve" y="747">6.4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2674" xml:space="preserve" y="844">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2674" xml:space="preserve" y="796">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2674" xml:space="preserve" y="748">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2479" xml:space="preserve" y="844">6.4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2479" xml:space="preserve" y="796">6.4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2479" xml:space="preserve" y="748">6.4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2286" xml:space="preserve" y="844">8</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2286" xml:space="preserve" y="802">8</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2286" xml:space="preserve" y="748">8</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2115" xml:space="preserve" y="844">3</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2115" xml:space="preserve" y="802">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2115" xml:space="preserve" y="748">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="3061" xml:space="preserve" y="410">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="3061" xml:space="preserve" y="360">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2674" xml:space="preserve" y="410">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2674" xml:space="preserve" y="361">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2486" xml:space="preserve" y="410">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2486" xml:space="preserve" y="360">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2280" xml:space="preserve" y="410">40</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2280" xml:space="preserve" y="360">40</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2115" xml:space="preserve" y="410">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2115" xml:space="preserve" y="360">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1314" xml:space="preserve" y="1522">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1299" xml:space="preserve" y="1473">25.2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="922" xml:space="preserve" y="1522">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="922" xml:space="preserve" y="1473">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="725" xml:space="preserve" y="1522">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="710" xml:space="preserve" y="1473">25.2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="510" xml:space="preserve" y="1522">40</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="495" xml:space="preserve" y="1473">31.5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="337" xml:space="preserve" y="1522">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="337" xml:space="preserve" y="1473">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1314" xml:space="preserve" y="1424">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1314" xml:space="preserve" y="1376">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="922" xml:space="preserve" y="1424">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="922" xml:space="preserve" y="1376">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="725" xml:space="preserve" y="1424">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="725" xml:space="preserve" y="1376">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="510" xml:space="preserve" y="1424">40</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="510" xml:space="preserve" y="1376">40</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="337" xml:space="preserve" y="1424">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="337" xml:space="preserve" y="1376">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1888" xml:space="preserve" y="797">35kV城中变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1878" xml:space="preserve" y="380">110kV佛海变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="105" xml:space="preserve" y="1495">110kV勐海变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="105" xml:space="preserve" y="1400">110kV金象变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1299" xml:space="preserve" y="1329">25.2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="922" xml:space="preserve" y="1329">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="710" xml:space="preserve" y="1329">25.2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="495" xml:space="preserve" y="1329">31.5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="337" xml:space="preserve" y="1329">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1299" xml:space="preserve" y="1280">25.2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="922" xml:space="preserve" y="1280">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="710" xml:space="preserve" y="1280">25.2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="495" xml:space="preserve" y="1280">31.5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="337" xml:space="preserve" y="1280">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="105" xml:space="preserve" y="1302">110kV勐腊变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1314" xml:space="preserve" y="1232">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="922" xml:space="preserve" y="1232">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="725" xml:space="preserve" y="1232">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="510" xml:space="preserve" y="1232">40</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="337" xml:space="preserve" y="1232">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="105" xml:space="preserve" y="1231">110kV勐龙变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1314" xml:space="preserve" y="1183">16</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1314" xml:space="preserve" y="1134">16</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="922" xml:space="preserve" y="1183">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="922" xml:space="preserve" y="1134">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="725" xml:space="preserve" y="1183">16</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="725" xml:space="preserve" y="1134">16</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="510" xml:space="preserve" y="1183">20</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="510" xml:space="preserve" y="1134">20</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="337" xml:space="preserve" y="1183">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="337" xml:space="preserve" y="1134">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="92" xml:space="preserve" y="1157">110kV大渡岗变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1314" xml:space="preserve" y="1087">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1299" xml:space="preserve" y="1036">25.2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="922" xml:space="preserve" y="1087">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="922" xml:space="preserve" y="1036">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="725" xml:space="preserve" y="1087">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="710" xml:space="preserve" y="1036">25.2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="337" xml:space="preserve" y="1087">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="337" xml:space="preserve" y="1036">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="510" xml:space="preserve" y="1087">40</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="495" xml:space="preserve" y="1036">31.5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="105" xml:space="preserve" y="1060">110kV勐罕变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1314" xml:space="preserve" y="940">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1314" xml:space="preserve" y="891">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="922" xml:space="preserve" y="940">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="922" xml:space="preserve" y="891">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="725" xml:space="preserve" y="940">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="725" xml:space="preserve" y="891">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="510" xml:space="preserve" y="940">40</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="510" xml:space="preserve" y="891">40</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="337" xml:space="preserve" y="940">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="337" xml:space="preserve" y="891">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="105" xml:space="preserve" y="940">110kV嘎栋变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1314" xml:space="preserve" y="845">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1314" xml:space="preserve" y="795">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="922" xml:space="preserve" y="845">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="922" xml:space="preserve" y="795">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="725" xml:space="preserve" y="845">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="725" xml:space="preserve" y="795">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="510" xml:space="preserve" y="845">40</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="510" xml:space="preserve" y="795">40</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="337" xml:space="preserve" y="845">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="337" xml:space="preserve" y="795">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="92" xml:space="preserve" y="817">110kV曼弄枫变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1314" xml:space="preserve" y="747">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1299" xml:space="preserve" y="697">25.2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="922" xml:space="preserve" y="747">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="922" xml:space="preserve" y="697">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="725" xml:space="preserve" y="747">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="710" xml:space="preserve" y="697">25.2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="510" xml:space="preserve" y="747">40</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="495" xml:space="preserve" y="697">31.5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="337" xml:space="preserve" y="747">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="337" xml:space="preserve" y="697">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="105" xml:space="preserve" y="722">110kV城南变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1299" xml:space="preserve" y="648">25.2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1314" xml:space="preserve" y="601">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="922" xml:space="preserve" y="601">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="725" xml:space="preserve" y="601">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="510" xml:space="preserve" y="601">40</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="337" xml:space="preserve" y="600">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="105" xml:space="preserve" y="625">220kV傣乡变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1307" xml:space="preserve" y="550">144</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1307" xml:space="preserve" y="506">144</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="922" xml:space="preserve" y="555">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="922" xml:space="preserve" y="506">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="718" xml:space="preserve" y="555">144</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="718" xml:space="preserve" y="506">144</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="503" xml:space="preserve" y="555">180</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="503" xml:space="preserve" y="506">180</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="337" xml:space="preserve" y="555">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="337" xml:space="preserve" y="506">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="105" xml:space="preserve" y="527">220kV黎明变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1314" xml:space="preserve" y="456">96</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1314" xml:space="preserve" y="409">96</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1474" xml:space="preserve" y="308">实时油温</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="922" xml:space="preserve" y="456">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="922" xml:space="preserve" y="409">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="725" xml:space="preserve" y="456">96</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="724" xml:space="preserve" y="409">96</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="503" xml:space="preserve" y="409">120</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="503" xml:space="preserve" y="456">120</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="339" xml:space="preserve" y="456">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1232" xml:space="preserve" y="308">告警负荷（MW）</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1032" xml:space="preserve" y="308">实时负荷（MW）</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="105" xml:space="preserve" y="429">220kV景洪变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="846" xml:space="preserve" y="308">油温上限（C）</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="642" xml:space="preserve" y="308">负载上限（MW）</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="425" xml:space="preserve" y="308">主变容量（MVA）</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="337" xml:space="preserve" y="409">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="293" xml:space="preserve" y="308">主变编号</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1474" xml:space="preserve" y="261">油温监视</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1174" xml:space="preserve" y="261">负荷监控</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="126" xml:space="preserve" y="308">厂站名称</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="155" xml:space="preserve" y="261">项目</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="568" xml:space="preserve" y="261">设备及参数</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="112" xml:space="preserve" y="1570">35kV景哈变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1317" xml:space="preserve" y="1568">3.2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="3070" xml:space="preserve" y="1568">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="924" xml:space="preserve" y="1570">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2674" xml:space="preserve" y="1568">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="725" xml:space="preserve" y="1569">3.2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2491" xml:space="preserve" y="1568">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="41" stroke="rgb(255,255,254)" writing-mode="lr" x="511" xml:space="preserve" y="1567">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2286" xml:space="preserve" y="1568">5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="339" xml:space="preserve" y="1572">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2115" xml:space="preserve" y="1567">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1883" xml:space="preserve" y="1565">35kV关坪变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1884" xml:space="preserve" y="1496">35kV勐旺变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2115" xml:space="preserve" y="1473">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2115" xml:space="preserve" y="1523">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2286" xml:space="preserve" y="1474">5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2286" xml:space="preserve" y="1524">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2492" xml:space="preserve" y="1474">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2479" xml:space="preserve" y="1524">1.6</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2674" xml:space="preserve" y="1474">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2674" xml:space="preserve" y="1524">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="3068" xml:space="preserve" y="1474">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="3055" xml:space="preserve" y="1524">1.6</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="3055" xml:space="preserve" y="1425">1.6</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="3068" xml:space="preserve" y="1376">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2674" xml:space="preserve" y="1425">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2674" xml:space="preserve" y="1376">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2479" xml:space="preserve" y="1425">1.6</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2492" xml:space="preserve" y="1376">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2286" xml:space="preserve" y="1425">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2286" xml:space="preserve" y="1376">5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2115" xml:space="preserve" y="1424">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2115" xml:space="preserve" y="1375">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1884" xml:space="preserve" y="1398">35kV基诺变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="3055" xml:space="preserve" y="1327">1.6</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="3068" xml:space="preserve" y="1281">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2674" xml:space="preserve" y="1327">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2674" xml:space="preserve" y="1281">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2479" xml:space="preserve" y="1327">1.6</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2492" xml:space="preserve" y="1281">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2286" xml:space="preserve" y="1327">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2286" xml:space="preserve" y="1281">5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2115" xml:space="preserve" y="1326">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2115" xml:space="preserve" y="1280">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1884" xml:space="preserve" y="1299">35kV景讷变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="3055" xml:space="preserve" y="1234">6.4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="3055" xml:space="preserve" y="1184">3.2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2674" xml:space="preserve" y="1233">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2674" xml:space="preserve" y="1183">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2479" xml:space="preserve" y="1232">6.4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2479" xml:space="preserve" y="1184">3.2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2286" xml:space="preserve" y="1232">8</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2286" xml:space="preserve" y="1183">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2115" xml:space="preserve" y="1231">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2115" xml:space="preserve" y="1183">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1884" xml:space="preserve" y="1205">35kV普文变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1884" xml:space="preserve" y="1109">35kV茶园变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2115" xml:space="preserve" y="1086">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2115" xml:space="preserve" y="1136">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2273" xml:space="preserve" y="1089">2.5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2273" xml:space="preserve" y="1136">2.5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2492" xml:space="preserve" y="1086">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2492" xml:space="preserve" y="1136">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2674" xml:space="preserve" y="1086">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2674" xml:space="preserve" y="1136">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="3068" xml:space="preserve" y="1086">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="3068" xml:space="preserve" y="1134">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="3055" xml:space="preserve" y="1037">6.4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="3055" xml:space="preserve" y="989">6.4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2674" xml:space="preserve" y="1036">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2674" xml:space="preserve" y="989">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2479" xml:space="preserve" y="1035">6.4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2479" xml:space="preserve" y="989">6.4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2286" xml:space="preserve" y="1036">8</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2286" xml:space="preserve" y="988">8</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2115" xml:space="preserve" y="1038">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2115" xml:space="preserve" y="989">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1884" xml:space="preserve" y="1012">35kV勐养变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="3068" xml:space="preserve" y="939">8</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="3068" xml:space="preserve" y="891">8</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2674" xml:space="preserve" y="940">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2674" xml:space="preserve" y="891">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2492" xml:space="preserve" y="939">8</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2492" xml:space="preserve" y="891">8</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2280" xml:space="preserve" y="938">10</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2280" xml:space="preserve" y="891">10</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1888" xml:space="preserve" y="913">35kV城西变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2115" xml:space="preserve" y="940">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2115" xml:space="preserve" y="892">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="62" font-size="62" font-width="62" stroke="rgb(0,255,255)" writing-mode="lr" x="1708" xml:space="preserve" y="100">主变监视表</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1630" xml:space="preserve" y="266">高压侧有功</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1630" xml:space="preserve" y="293">绝对值相加</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="3219" xml:space="preserve" y="261">油温监视</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2925" xml:space="preserve" y="261">负荷监控</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1927" xml:space="preserve" y="261">项目</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2331" xml:space="preserve" y="261">设备及参数</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="3374" xml:space="preserve" y="266">高压侧有功</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="3374" xml:space="preserve" y="293">绝对值相加</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="3219" xml:space="preserve" y="309">实时油温</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2980" xml:space="preserve" y="309">告警负荷（MW）</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2780" xml:space="preserve" y="309">实时负荷（MW）</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2608" xml:space="preserve" y="309">油温上限（C）</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2396" xml:space="preserve" y="309">负载上限（MW）</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2204" xml:space="preserve" y="309">主变容量(MVA)</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2069" xml:space="preserve" y="309">主变编号</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1899" xml:space="preserve" y="309">厂站名称</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="39" stroke="rgb(255,255,254)" writing-mode="lr" x="3186" xml:space="preserve" y="196">越下限</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1878" xml:space="preserve" y="479">110kV勐仑变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2115" xml:space="preserve" y="460">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2115" xml:space="preserve" y="507">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2674" xml:space="preserve" y="460">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2674" xml:space="preserve" y="508">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="922" xml:space="preserve" y="358">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="503" xml:space="preserve" y="359">750</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2280" xml:space="preserve" y="460">20</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2280" xml:space="preserve" y="507">40</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="3061" xml:space="preserve" y="460">16</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="3061" xml:space="preserve" y="506">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="717" xml:space="preserve" y="358">610</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2486" xml:space="preserve" y="460">16</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2486" xml:space="preserve" y="506">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1306" xml:space="preserve" y="358">610</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1878" xml:space="preserve" y="575">110kV 辉凰变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2115" xml:space="preserve" y="552">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2280" xml:space="preserve" y="552">50</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2486" xml:space="preserve" y="554">40</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2674" xml:space="preserve" y="553">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="3061" xml:space="preserve" y="552">40</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2115" xml:space="preserve" y="600">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2280" xml:space="preserve" y="600">50</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2486" xml:space="preserve" y="600">40</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2674" xml:space="preserve" y="600">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="3061" xml:space="preserve" y="602">40</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="40" font-size="40" font-width="44" stroke="rgb(255,255,254)" writing-mode="lr" x="3361" xml:space="preserve" y="193">下一页</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="3047" xml:space="preserve" y="701">25.2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="3062" xml:space="preserve" y="654">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2670" xml:space="preserve" y="701">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2670" xml:space="preserve" y="654">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2458" xml:space="preserve" y="701">25.2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2473" xml:space="preserve" y="654">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2269" xml:space="preserve" y="701">31.5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2282" xml:space="preserve" y="650">40</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2114" xml:space="preserve" y="701">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2114" xml:space="preserve" y="651">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1883" xml:space="preserve" y="678">110kV江北变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="340" xml:space="preserve" y="648">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="510" xml:space="preserve" y="650">40</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="725" xml:space="preserve" y="650">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="922" xml:space="preserve" y="650">70</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href="监视列表.sys.svg"><rect fill-opacity="0" height="69" stroke-opacity="0" stroke-width="2" width="352" x="1688" y="35"/></g>
 <g ChangePicPlane="0," Plane="0" href="主变监控表1.fac.svg"><rect fill-opacity="0" height="43" stroke-opacity="0" stroke-width="1" width="146" x="3354" y="154"/></g>
</g>
</svg>