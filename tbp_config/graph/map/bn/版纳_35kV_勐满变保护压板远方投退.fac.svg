<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="sys" height="1000" id="thSvg" viewBox="0 0 1200 1000" width="1200">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="Protect:bn_保护图元1_0" viewBox="0,0,18,18">
 <rect AFMask="2147483647" Plane="0" fill="none" height="13" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,8,8)" width="13" x="2" y="2"/>
</symbol>
<symbol id="Protect:bn_保护图元1_1" viewBox="0,0,18,18">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="13" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,8,8)" width="13" x="2" y="2"/>
</symbol>
<symbol id="Protect:软压板投退_0" viewBox="0,0,50,50">
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,0,0)" stroke-width="3" x1="40" x2="13" y1="31" y2="4"/>
 <circle AFMask="2147483647" Plane="0" cx="9" cy="31" fill="none" r="4" stroke="rgb(255,0,0)" stroke-width="8"/>
 <circle AFMask="2147483647" Plane="0" cx="40" cy="31" fill="none" r="4" stroke="rgb(255,0,0)" stroke-width="8"/>
</symbol>
<symbol id="Protect:软压板投退_1" viewBox="0,0,50,50">
 <circle AFMask="2147483647" Plane="0" cx="9" cy="24" fill="none" r="4" stroke="rgb(0,255,0)" stroke-width="8"/>
 <circle AFMask="2147483647" Plane="0" cx="40" cy="24" fill="none" r="4" stroke="rgb(0,255,0)" stroke-width="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,255,0)" stroke-width="3" x1="9" x2="41" y1="24" y2="24"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="版纳_35kV_勐满变" InitShowingPlane="0," fill="rgb(0,0,0)" height="1000" width="1200" x="0" y="0"/>
</g>
<g id="Other_Layer">
 
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="82" x2="753" y1="82" y2="82"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="210" x2="754" y1="182" y2="182"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="81" x2="81" y1="83" y2="872"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="209" x2="209" y1="82" y2="872"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="754" x2="754" y1="82" y2="874"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="209" x2="753" y1="149" y2="149"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="210" x2="754" y1="117" y2="117"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="675" x2="675" y1="83" y2="872"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="209" x2="754" y1="212" y2="212"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="210" x2="754" y1="242" y2="242"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="210" x2="754" y1="272" y2="272"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="209" x2="753" y1="302" y2="302"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="210" x2="753" y1="332" y2="332"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="210" x2="753" y1="362" y2="362"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="210" x2="753" y1="392" y2="392"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="210" x2="754" y1="422" y2="422"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="210" x2="754" y1="452" y2="452"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="211" x2="754" y1="572" y2="572"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="209" x2="754" y1="602" y2="602"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="210" x2="754" y1="662" y2="662"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="210" x2="754" y1="692" y2="692"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="209" x2="753" y1="722" y2="722"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="210" x2="753" y1="782" y2="782"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="84" x2="754" y1="754" y2="754"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="81" x2="754" y1="872" y2="872"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="211" x2="755" y1="842" y2="842"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="210" x2="754" y1="812" y2="812"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="84" x2="754" y1="634" y2="634"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="84" x2="754" y1="485" y2="485"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="85" x2="755" y1="514" y2="514"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="83" x2="753" y1="543" y2="543"/>
</g>
<g id="Protect_Layer">
 <g id="127000567">
  <use class="kv-1" height="18" transform="rotate(0,711,438) scale(1.7,1.7) translate(-301.765,-189.353)" width="18" x="711" xlink:href="#Protect:bn_保护图元1_0" y="438"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892572984" ObjectName="122160139892572984" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,711,438) scale(1.7,1.7) translate(-301.765,-189.353)" width="18" x="711" y="438"/></g>
 <g id="127000568">
  <use class="kv-1" height="18" transform="rotate(0,711,377) scale(1.7,1.7) translate(-301.765,-164.235)" width="18" x="711" xlink:href="#Protect:bn_保护图元1_0" y="377"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892572982" ObjectName="122160139892572982" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,711,377) scale(1.7,1.7) translate(-301.765,-164.235)" width="18" x="711" y="377"/></g>
 <g id="127000569">
  <use class="kv-1" height="18" transform="rotate(0,710,405) scale(1.7,1.7) translate(-301.353,-175.765)" width="18" x="710" xlink:href="#Protect:bn_保护图元1_0" y="405"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892572983" ObjectName="122160139892572983" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,710,405) scale(1.7,1.7) translate(-301.353,-175.765)" width="18" x="710" y="405"/></g>
 <g id="127000570">
  <use class="kv10kV" height="18" transform="rotate(0,711,288) scale(1.7,1.7) translate(-301.765,-127.588)" width="18" x="711" xlink:href="#Protect:bn_保护图元1_0" y="288"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892572427" ObjectName="122160139892572427" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,711,288) scale(1.7,1.7) translate(-301.765,-127.588)" width="18" x="711" y="288"/></g>
 <g id="127000571">
  <use class="kv35kV" height="18" transform="rotate(0,712,199) scale(1.7,1.7) translate(-302.176,-90.9412)" width="18" x="712" xlink:href="#Protect:bn_保护图元1_0" y="199"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892572347" ObjectName="122160139892572347" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,712,199) scale(1.7,1.7) translate(-302.176,-90.9412)" width="18" x="712" y="199"/></g>
 <g id="127000572">
  <use class="kv35kV" height="18" transform="rotate(0,711,99) scale(1.7,1.7) translate(-301.765,-49.7647)" width="18" x="711" xlink:href="#Protect:bn_保护图元1_0" y="99"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892572323" ObjectName="122160139892572323" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,711,99) scale(1.7,1.7) translate(-301.765,-49.7647)" width="18" x="711" y="99"/></g>
 <g id="127000573">
  <use class="kv35kV" height="18" transform="rotate(0,713,135) scale(1.7,1.7) translate(-302.588,-64.5882)" width="18" x="713" xlink:href="#Protect:bn_保护图元1_0" y="135"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892572336" ObjectName="122160139892572336" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,713,135) scale(1.7,1.7) translate(-302.588,-64.5882)" width="18" x="713" y="135"/></g>
 <g id="127000574">
  <use class="kv10kV" height="50" transform="rotate(0,714,311) scale(0.6,0.6) translate(451,186.333)" width="50" x="714" xlink:href="#Protect:软压板投退_0" y="311"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892572442" ObjectName="122160139892572442" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,714,311) scale(0.6,0.6) translate(451,186.333)" width="50" x="714" y="311"/></g>
 <g id="127000575">
  <use class="kv10kV" height="50" transform="rotate(0,714,341) scale(0.6,0.6) translate(451,206.333)" width="50" x="714" xlink:href="#Protect:软压板投退_0" y="341"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892572441" ObjectName="122160139892572441" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,714,341) scale(0.6,0.6) translate(451,206.333)" width="50" x="714" y="341"/></g>
 <g id="127000576">
  <use class="kv35kV" height="50" transform="rotate(0,712,255) scale(0.6,0.6) translate(449.667,149)" width="50" x="712" xlink:href="#Protect:软压板投退_0" y="255"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892572364" ObjectName="122160139892572364" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,712,255) scale(0.6,0.6) translate(449.667,149)" width="50" x="712" y="255"/></g>
 <g id="127000577">
  <use class="kv35kV" height="50" transform="rotate(0,713,223) scale(0.6,0.6) translate(450.333,127.667)" width="50" x="713" xlink:href="#Protect:软压板投退_0" y="223"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892572365" ObjectName="122160139892572365" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,713,223) scale(0.6,0.6) translate(450.333,127.667)" width="50" x="713" y="223"/></g>
 <g id="127000578">
  <use class="kv35kV" height="50" transform="rotate(0,717,162) scale(0.6,0.6) translate(453,87)" width="50" x="717" xlink:href="#Protect:软压板投退_0" y="162"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892572343" ObjectName="122160139892572343" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,717,162) scale(0.6,0.6) translate(453,87)" width="50" x="717" y="162"/></g>
 <g id="127000580">
  <use class="kv35kV" height="50" transform="rotate(0,711,470) scale(0.6,0.6) translate(449,292.333)" width="50" x="711" xlink:href="#Protect:软压板投退_0" y="470"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892572985" ObjectName="122160139892572985" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,711,470) scale(0.6,0.6) translate(449,292.333)" width="50" x="711" y="470"/></g>
 <g id="127000582">
  <use class="kv35kV" height="50" transform="rotate(0,711,498) scale(0.6,0.6) translate(449,311)" width="50" x="711" xlink:href="#Protect:软压板投退_0" y="498"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892572124" ObjectName="122160139892572124" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,711,498) scale(0.6,0.6) translate(449,311)" width="50" x="711" y="498"/></g>
</g>
<g id="Text_Layer">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="30" font-width="30" stroke="rgb(85,255,255)" writing-mode="lr" x="363" xml:space="preserve" y="49">勐满变继电保护远方操作</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="133" xml:space="preserve" y="208"> 1</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="133" xml:space="preserve" y="229">号</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="133" xml:space="preserve" y="250">主</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="133" xml:space="preserve" y="271">变</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="221" xml:space="preserve" y="115">1号主变非电量保护信号复归</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="221" xml:space="preserve" y="143">1号主变差动保护信号复归</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="221" xml:space="preserve" y="173">1号主变差动保护投入 （软压板）</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="215" xml:space="preserve" y="204"> 1号主变高后备保护信号复归</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="219" xml:space="preserve" y="235">1号主变高后备保护间隙保护投（软压板）</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="215" xml:space="preserve" y="265"> 1号主变高后备保护高侧电压投（软压板）</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="220" xml:space="preserve" y="295">1号主变低后备保护信号复归</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="215" xml:space="preserve" y="326"> 1号主变低后备保护简易母线保护投（软压板）</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="215" xml:space="preserve" y="356"> 1号主变低后备保护低压侧电压投（软压板）</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="215" xml:space="preserve" y="387"> 1号主变高压侧测控装置复归</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="214" xml:space="preserve" y="417">1号主变低压侧测控装置复归</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="214" xml:space="preserve" y="445">1号主变本测控装置复归</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="214" xml:space="preserve" y="475">1号主变高压侧断路器端子箱电机总电源空开合闸</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="214" xml:space="preserve" y="512">35kV I段母线PT端子箱电机总电源空开合闸</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="95" xml:space="preserve" y="509">35kVI母TV</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href="版纳_35kV_勐满变.fac.svg"><rect fill-opacity="0" height="46" stroke-opacity="0" stroke-width="1" width="335" x="357" y="7"/></g>
</g>
</svg>