<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1055" id="thSvg" viewBox="0 0 1920 1055" width="1920">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="GZP:sz-gzp1_0" viewBox="0,0,34,36">
 <polygon AFMask="2147483647" Plane="0" fill="rgb(0,255,255)" points="1,16.5 16.5,1 32,16.5 16.5,32" stroke="rgb(43,191,255)" stroke-width="1"/>
 <rect AFMask="2147483647" Plane="0" fill="rgb(0,0,0)" height="24" stroke="rgb(0,255,255)" stroke-width="1" transform="rotate(0,16,17)" width="24" x="4" y="5"/>
 <circle AFMask="2147483647" Plane="0" cx="16" cy="18" fill="rgb(255,255,254)" r="11" stroke="rgb(0,170,0)" stroke-width="1"/>
</symbol>
<symbol id="GZP:sz-gzp1_1" viewBox="0,0,34,36">
 <rect AFMask="2147483647" Plane="0" fill="rgb(0,0,0)" height="24" stroke="rgb(0,255,255)" stroke-width="1" transform="rotate(0,16,17)" width="24" x="4" y="5"/>
 <polygon AFMask="2147483647" Plane="0" fill="rgb(0,255,255)" points="1,16.5 16.5,1 32,16.5 16.5,32" stroke="rgb(43,191,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="16" cy="18" fill="rgb(255,0,0)" r="11" stroke="rgb(0,170,0)" stroke-width="1"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="-1" InitShowingPlane="0," fill="rgb(0,0,0)" height="1055" width="1920" x="0" y="0"/>
</g>
<g id="Other_Layer">
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="203" x2="1239" y1="87" y2="87"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="203" x2="1239" y1="117" y2="117"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="331" x2="1239" y1="147" y2="147"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="331" x2="1239" y1="177" y2="177"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="331" x2="1239" y1="207" y2="207"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="331" x2="1239" y1="237" y2="237"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="203" x2="1239" y1="267" y2="267"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="331" x2="1239" y1="297" y2="297"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="331" x2="1239" y1="327" y2="327"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="331" x2="1239" y1="357" y2="357"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="203" x2="1239" y1="387" y2="387"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="331" x2="1239" y1="417" y2="417"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="331" x2="1239" y1="447" y2="447"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="331" x2="1239" y1="477" y2="477"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="203" x2="1239" y1="507" y2="507"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="331" x2="1239" y1="537" y2="537"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="331" x2="1239" y1="567" y2="567"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="331" x2="1239" y1="597" y2="597"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="203" x2="1239" y1="627" y2="627"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="331" x2="1239" y1="657" y2="657"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="331" x2="1239" y1="687" y2="687"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="331" x2="1239" y1="717" y2="717"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="203" x2="1239" y1="747" y2="747"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="202" x2="202" y1="87" y2="747"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="331" x2="331" y1="87" y2="747"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="938" x2="938" y1="87" y2="747"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1086" x2="1086" y1="87" y2="748"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1240" x2="1240" y1="87" y2="748"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="791" x2="791" y1="89" y2="745"/>
</g>
<g id="GZP_Layer">
 <g id="135000091">
  <use class="kv-1" height="36" transform="rotate(0,860,132) scale(1,1) translate(-16,-18)" width="34" x="860" xlink:href="#GZP:sz-gzp1_0" y="132"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,860,132) scale(1,1) translate(-16,-18)" width="34" x="860" y="132"/></g>
 <g id="135000092">
  <use class="kv-1" height="36" transform="rotate(0,860,162) scale(1,1) translate(-16,-18)" width="34" x="860" xlink:href="#GZP:sz-gzp1_0" y="162"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,860,162) scale(1,1) translate(-16,-18)" width="34" x="860" y="162"/></g>
 <g id="135000093">
  <use class="kv-1" height="36" transform="rotate(0,860,192) scale(1,1) translate(-16,-18)" width="34" x="860" xlink:href="#GZP:sz-gzp1_0" y="192"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,860,192) scale(1,1) translate(-16,-18)" width="34" x="860" y="192"/></g>
 <g id="135000094">
  <use class="kv-1" height="36" transform="rotate(0,860,222) scale(1,1) translate(-16,-18)" width="34" x="860" xlink:href="#GZP:sz-gzp1_0" y="222"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,860,222) scale(1,1) translate(-16,-18)" width="34" x="860" y="222"/></g>
 <g id="135000095">
  <use class="kv-1" height="36" transform="rotate(0,860,252) scale(1,1) translate(-16,-18)" width="34" x="860" xlink:href="#GZP:sz-gzp1_0" y="252"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,860,252) scale(1,1) translate(-16,-18)" width="34" x="860" y="252"/></g>
 <g id="135000096">
  <use class="kv-1" height="36" transform="rotate(0,860,282) scale(1,1) translate(-16,-18)" width="34" x="860" xlink:href="#GZP:sz-gzp1_0" y="282"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,860,282) scale(1,1) translate(-16,-18)" width="34" x="860" y="282"/></g>
 <g id="135000097">
  <use class="kv-1" height="36" transform="rotate(0,860,312) scale(1,1) translate(-16,-18)" width="34" x="860" xlink:href="#GZP:sz-gzp1_0" y="312"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,860,312) scale(1,1) translate(-16,-18)" width="34" x="860" y="312"/></g>
 <g id="135000098">
  <use class="kv-1" height="36" transform="rotate(0,860,342) scale(1,1) translate(-16,-18)" width="34" x="860" xlink:href="#GZP:sz-gzp1_0" y="342"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,860,342) scale(1,1) translate(-16,-18)" width="34" x="860" y="342"/></g>
 <g id="135000099">
  <use class="kv-1" height="36" transform="rotate(0,860,372) scale(1,1) translate(-16,-18)" width="34" x="860" xlink:href="#GZP:sz-gzp1_0" y="372"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,860,372) scale(1,1) translate(-16,-18)" width="34" x="860" y="372"/></g>
 <g id="135000100">
  <use class="kv-1" height="36" transform="rotate(0,860,402) scale(1,1) translate(-16,-18)" width="34" x="860" xlink:href="#GZP:sz-gzp1_0" y="402"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,860,402) scale(1,1) translate(-16,-18)" width="34" x="860" y="402"/></g>
 <g id="135000101">
  <use class="kv-1" height="36" transform="rotate(0,860,432) scale(1,1) translate(-16,-18)" width="34" x="860" xlink:href="#GZP:sz-gzp1_0" y="432"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,860,432) scale(1,1) translate(-16,-18)" width="34" x="860" y="432"/></g>
 <g id="135000102">
  <use class="kv-1" height="36" transform="rotate(0,860,462) scale(1,1) translate(-16,-18)" width="34" x="860" xlink:href="#GZP:sz-gzp1_0" y="462"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,860,462) scale(1,1) translate(-16,-18)" width="34" x="860" y="462"/></g>
 <g id="135000103">
  <use class="kv-1" height="36" transform="rotate(0,860,492) scale(1,1) translate(-16,-18)" width="34" x="860" xlink:href="#GZP:sz-gzp1_0" y="492"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,860,492) scale(1,1) translate(-16,-18)" width="34" x="860" y="492"/></g>
 <g id="135000104">
  <use class="kv-1" height="36" transform="rotate(0,860,522) scale(1,1) translate(-16,-18)" width="34" x="860" xlink:href="#GZP:sz-gzp1_0" y="522"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,860,522) scale(1,1) translate(-16,-18)" width="34" x="860" y="522"/></g>
 <g id="135000105">
  <use class="kv-1" height="36" transform="rotate(0,860,552) scale(1,1) translate(-16,-18)" width="34" x="860" xlink:href="#GZP:sz-gzp1_0" y="552"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,860,552) scale(1,1) translate(-16,-18)" width="34" x="860" y="552"/></g>
 <g id="135000106">
  <use class="kv-1" height="36" transform="rotate(0,860,582) scale(1,1) translate(-16,-18)" width="34" x="860" xlink:href="#GZP:sz-gzp1_0" y="582"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,860,582) scale(1,1) translate(-16,-18)" width="34" x="860" y="582"/></g>
 <g id="135000107">
  <use class="kv-1" height="36" transform="rotate(0,860,612) scale(1,1) translate(-16,-18)" width="34" x="860" xlink:href="#GZP:sz-gzp1_0" y="612"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,860,612) scale(1,1) translate(-16,-18)" width="34" x="860" y="612"/></g>
 <g id="135000108">
  <use class="kv-1" height="36" transform="rotate(0,860,642) scale(1,1) translate(-16,-18)" width="34" x="860" xlink:href="#GZP:sz-gzp1_0" y="642"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,860,642) scale(1,1) translate(-16,-18)" width="34" x="860" y="642"/></g>
 <g id="135000109">
  <use class="kv-1" height="36" transform="rotate(0,860,672) scale(1,1) translate(-16,-18)" width="34" x="860" xlink:href="#GZP:sz-gzp1_0" y="672"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,860,672) scale(1,1) translate(-16,-18)" width="34" x="860" y="672"/></g>
 <g id="135000110">
  <use class="kv-1" height="36" transform="rotate(0,860,702) scale(1,1) translate(-16,-18)" width="34" x="860" xlink:href="#GZP:sz-gzp1_0" y="702"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,860,702) scale(1,1) translate(-16,-18)" width="34" x="860" y="702"/></g>
 <g id="135000111">
  <use class="kv-1" height="36" transform="rotate(0,860,732) scale(1,1) translate(-16,-18)" width="34" x="860" xlink:href="#GZP:sz-gzp1_0" y="732"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,860,732) scale(1,1) translate(-16,-18)" width="34" x="860" y="732"/></g>
 <g id="135000112">
  <use class="kv-1" height="36" transform="rotate(0,1011,132) scale(1,1) translate(-16,-18)" width="34" x="1011" xlink:href="#GZP:sz-gzp1_0" y="132"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1011,132) scale(1,1) translate(-16,-18)" width="34" x="1011" y="132"/></g>
 <g id="135000113">
  <use class="kv-1" height="36" transform="rotate(0,1011,162) scale(1,1) translate(-16,-18)" width="34" x="1011" xlink:href="#GZP:sz-gzp1_0" y="162"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1011,162) scale(1,1) translate(-16,-18)" width="34" x="1011" y="162"/></g>
 <g id="135000114">
  <use class="kv-1" height="36" transform="rotate(0,1011,192) scale(1,1) translate(-16,-18)" width="34" x="1011" xlink:href="#GZP:sz-gzp1_0" y="192"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1011,192) scale(1,1) translate(-16,-18)" width="34" x="1011" y="192"/></g>
 <g id="135000115">
  <use class="kv-1" height="36" transform="rotate(0,1011,222) scale(1,1) translate(-16,-18)" width="34" x="1011" xlink:href="#GZP:sz-gzp1_0" y="222"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1011,222) scale(1,1) translate(-16,-18)" width="34" x="1011" y="222"/></g>
 <g id="135000116">
  <use class="kv-1" height="36" transform="rotate(0,1011,252) scale(1,1) translate(-16,-18)" width="34" x="1011" xlink:href="#GZP:sz-gzp1_0" y="252"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1011,252) scale(1,1) translate(-16,-18)" width="34" x="1011" y="252"/></g>
 <g id="135000117">
  <use class="kv-1" height="36" transform="rotate(0,1011,282) scale(1,1) translate(-16,-18)" width="34" x="1011" xlink:href="#GZP:sz-gzp1_0" y="282"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1011,282) scale(1,1) translate(-16,-18)" width="34" x="1011" y="282"/></g>
 <g id="135000118">
  <use class="kv-1" height="36" transform="rotate(0,1011,312) scale(1,1) translate(-16,-18)" width="34" x="1011" xlink:href="#GZP:sz-gzp1_0" y="312"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1011,312) scale(1,1) translate(-16,-18)" width="34" x="1011" y="312"/></g>
 <g id="135000119">
  <use class="kv-1" height="36" transform="rotate(0,1011,342) scale(1,1) translate(-16,-18)" width="34" x="1011" xlink:href="#GZP:sz-gzp1_0" y="342"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1011,342) scale(1,1) translate(-16,-18)" width="34" x="1011" y="342"/></g>
 <g id="135000120">
  <use class="kv-1" height="36" transform="rotate(0,1011,372) scale(1,1) translate(-16,-18)" width="34" x="1011" xlink:href="#GZP:sz-gzp1_0" y="372"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1011,372) scale(1,1) translate(-16,-18)" width="34" x="1011" y="372"/></g>
 <g id="135000121">
  <use class="kv-1" height="36" transform="rotate(0,1011,402) scale(1,1) translate(-16,-18)" width="34" x="1011" xlink:href="#GZP:sz-gzp1_0" y="402"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1011,402) scale(1,1) translate(-16,-18)" width="34" x="1011" y="402"/></g>
 <g id="135000122">
  <use class="kv-1" height="36" transform="rotate(0,1011,432) scale(1,1) translate(-16,-18)" width="34" x="1011" xlink:href="#GZP:sz-gzp1_0" y="432"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1011,432) scale(1,1) translate(-16,-18)" width="34" x="1011" y="432"/></g>
 <g id="135000123">
  <use class="kv-1" height="36" transform="rotate(0,1011,462) scale(1,1) translate(-16,-18)" width="34" x="1011" xlink:href="#GZP:sz-gzp1_0" y="462"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1011,462) scale(1,1) translate(-16,-18)" width="34" x="1011" y="462"/></g>
 <g id="135000124">
  <use class="kv-1" height="36" transform="rotate(0,1011,492) scale(1,1) translate(-16,-18)" width="34" x="1011" xlink:href="#GZP:sz-gzp1_0" y="492"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1011,492) scale(1,1) translate(-16,-18)" width="34" x="1011" y="492"/></g>
 <g id="135000125">
  <use class="kv-1" height="36" transform="rotate(0,1011,522) scale(1,1) translate(-16,-18)" width="34" x="1011" xlink:href="#GZP:sz-gzp1_0" y="522"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1011,522) scale(1,1) translate(-16,-18)" width="34" x="1011" y="522"/></g>
 <g id="135000126">
  <use class="kv-1" height="36" transform="rotate(0,1011,552) scale(1,1) translate(-16,-18)" width="34" x="1011" xlink:href="#GZP:sz-gzp1_0" y="552"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1011,552) scale(1,1) translate(-16,-18)" width="34" x="1011" y="552"/></g>
 <g id="135000127">
  <use class="kv-1" height="36" transform="rotate(0,1011,582) scale(1,1) translate(-16,-18)" width="34" x="1011" xlink:href="#GZP:sz-gzp1_0" y="582"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1011,582) scale(1,1) translate(-16,-18)" width="34" x="1011" y="582"/></g>
 <g id="135000128">
  <use class="kv-1" height="36" transform="rotate(0,1011,612) scale(1,1) translate(-16,-18)" width="34" x="1011" xlink:href="#GZP:sz-gzp1_0" y="612"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1011,612) scale(1,1) translate(-16,-18)" width="34" x="1011" y="612"/></g>
 <g id="135000129">
  <use class="kv-1" height="36" transform="rotate(0,1011,642) scale(1,1) translate(-16,-18)" width="34" x="1011" xlink:href="#GZP:sz-gzp1_0" y="642"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1011,642) scale(1,1) translate(-16,-18)" width="34" x="1011" y="642"/></g>
 <g id="135000130">
  <use class="kv-1" height="36" transform="rotate(0,1011,672) scale(1,1) translate(-16,-18)" width="34" x="1011" xlink:href="#GZP:sz-gzp1_0" y="672"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1011,672) scale(1,1) translate(-16,-18)" width="34" x="1011" y="672"/></g>
 <g id="135000131">
  <use class="kv-1" height="36" transform="rotate(0,1011,702) scale(1,1) translate(-16,-18)" width="34" x="1011" xlink:href="#GZP:sz-gzp1_0" y="702"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1011,702) scale(1,1) translate(-16,-18)" width="34" x="1011" y="702"/></g>
 <g id="135000132">
  <use class="kv-1" height="36" transform="rotate(0,1011,732) scale(1,1) translate(-16,-18)" width="34" x="1011" xlink:href="#GZP:sz-gzp1_0" y="732"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1011,732) scale(1,1) translate(-16,-18)" width="34" x="1011" y="732"/></g>
 <g id="135000135">
  <use class="kv-1" height="36" transform="rotate(0,1159,132) scale(1,1) translate(-16,-18)" width="34" x="1159" xlink:href="#GZP:sz-gzp1_0" y="132"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1159,132) scale(1,1) translate(-16,-18)" width="34" x="1159" y="132"/></g>
 <g id="135000136">
  <use class="kv-1" height="36" transform="rotate(0,1159,162) scale(1,1) translate(-16,-18)" width="34" x="1159" xlink:href="#GZP:sz-gzp1_0" y="162"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1159,162) scale(1,1) translate(-16,-18)" width="34" x="1159" y="162"/></g>
 <g id="135000137">
  <use class="kv-1" height="36" transform="rotate(0,1159,192) scale(1,1) translate(-16,-18)" width="34" x="1159" xlink:href="#GZP:sz-gzp1_0" y="192"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1159,192) scale(1,1) translate(-16,-18)" width="34" x="1159" y="192"/></g>
 <g id="135000138">
  <use class="kv-1" height="36" transform="rotate(0,1159,222) scale(1,1) translate(-16,-18)" width="34" x="1159" xlink:href="#GZP:sz-gzp1_0" y="222"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1159,222) scale(1,1) translate(-16,-18)" width="34" x="1159" y="222"/></g>
 <g id="135000139">
  <use class="kv-1" height="36" transform="rotate(0,1159,252) scale(1,1) translate(-16,-18)" width="34" x="1159" xlink:href="#GZP:sz-gzp1_0" y="252"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1159,252) scale(1,1) translate(-16,-18)" width="34" x="1159" y="252"/></g>
 <g id="135000140">
  <use class="kv-1" height="36" transform="rotate(0,1159,282) scale(1,1) translate(-16,-18)" width="34" x="1159" xlink:href="#GZP:sz-gzp1_0" y="282"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1159,282) scale(1,1) translate(-16,-18)" width="34" x="1159" y="282"/></g>
 <g id="135000141">
  <use class="kv-1" height="36" transform="rotate(0,1159,312) scale(1,1) translate(-16,-18)" width="34" x="1159" xlink:href="#GZP:sz-gzp1_0" y="312"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1159,312) scale(1,1) translate(-16,-18)" width="34" x="1159" y="312"/></g>
 <g id="135000142">
  <use class="kv-1" height="36" transform="rotate(0,1159,342) scale(1,1) translate(-16,-18)" width="34" x="1159" xlink:href="#GZP:sz-gzp1_0" y="342"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1159,342) scale(1,1) translate(-16,-18)" width="34" x="1159" y="342"/></g>
 <g id="135000143">
  <use class="kv-1" height="36" transform="rotate(0,1159,372) scale(1,1) translate(-16,-18)" width="34" x="1159" xlink:href="#GZP:sz-gzp1_0" y="372"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1159,372) scale(1,1) translate(-16,-18)" width="34" x="1159" y="372"/></g>
 <g id="135000144">
  <use class="kv-1" height="36" transform="rotate(0,1159,402) scale(1,1) translate(-16,-18)" width="34" x="1159" xlink:href="#GZP:sz-gzp1_0" y="402"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1159,402) scale(1,1) translate(-16,-18)" width="34" x="1159" y="402"/></g>
 <g id="135000145">
  <use class="kv-1" height="36" transform="rotate(0,1159,432) scale(1,1) translate(-16,-18)" width="34" x="1159" xlink:href="#GZP:sz-gzp1_0" y="432"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1159,432) scale(1,1) translate(-16,-18)" width="34" x="1159" y="432"/></g>
 <g id="135000146">
  <use class="kv-1" height="36" transform="rotate(0,1159,462) scale(1,1) translate(-16,-18)" width="34" x="1159" xlink:href="#GZP:sz-gzp1_0" y="462"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1159,462) scale(1,1) translate(-16,-18)" width="34" x="1159" y="462"/></g>
 <g id="135000147">
  <use class="kv-1" height="36" transform="rotate(0,1159,492) scale(1,1) translate(-16,-18)" width="34" x="1159" xlink:href="#GZP:sz-gzp1_0" y="492"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1159,492) scale(1,1) translate(-16,-18)" width="34" x="1159" y="492"/></g>
 <g id="135000148">
  <use class="kv-1" height="36" transform="rotate(0,1159,522) scale(1,1) translate(-16,-18)" width="34" x="1159" xlink:href="#GZP:sz-gzp1_0" y="522"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1159,522) scale(1,1) translate(-16,-18)" width="34" x="1159" y="522"/></g>
 <g id="135000149">
  <use class="kv-1" height="36" transform="rotate(0,1159,552) scale(1,1) translate(-16,-18)" width="34" x="1159" xlink:href="#GZP:sz-gzp1_0" y="552"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1159,552) scale(1,1) translate(-16,-18)" width="34" x="1159" y="552"/></g>
 <g id="135000150">
  <use class="kv-1" height="36" transform="rotate(0,1159,582) scale(1,1) translate(-16,-18)" width="34" x="1159" xlink:href="#GZP:sz-gzp1_0" y="582"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1159,582) scale(1,1) translate(-16,-18)" width="34" x="1159" y="582"/></g>
 <g id="135000151">
  <use class="kv-1" height="36" transform="rotate(0,1159,612) scale(1,1) translate(-16,-18)" width="34" x="1159" xlink:href="#GZP:sz-gzp1_0" y="612"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1159,612) scale(1,1) translate(-16,-18)" width="34" x="1159" y="612"/></g>
 <g id="135000152">
  <use class="kv-1" height="36" transform="rotate(0,1159,642) scale(1,1) translate(-16,-18)" width="34" x="1159" xlink:href="#GZP:sz-gzp1_0" y="642"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1159,642) scale(1,1) translate(-16,-18)" width="34" x="1159" y="642"/></g>
 <g id="135000153">
  <use class="kv-1" height="36" transform="rotate(0,1159,672) scale(1,1) translate(-16,-18)" width="34" x="1159" xlink:href="#GZP:sz-gzp1_0" y="672"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1159,672) scale(1,1) translate(-16,-18)" width="34" x="1159" y="672"/></g>
 <g id="135000154">
  <use class="kv-1" height="36" transform="rotate(0,1159,702) scale(1,1) translate(-16,-18)" width="34" x="1159" xlink:href="#GZP:sz-gzp1_0" y="702"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1159,702) scale(1,1) translate(-16,-18)" width="34" x="1159" y="702"/></g>
 <g id="135000155">
  <use class="kv-1" height="36" transform="rotate(0,1159,732) scale(1,1) translate(-16,-18)" width="34" x="1159" xlink:href="#GZP:sz-gzp1_0" y="732"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1159,732) scale(1,1) translate(-16,-18)" width="34" x="1159" y="732"/></g>
</g>
<g id="Text_Layer">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="244" xml:space="preserve" y="114">间隔</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="230" xml:space="preserve" y="205">1号主变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="210" xml:space="preserve" y="338">051备用一线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="219" xml:space="preserve" y="457">053曼皮线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="218" xml:space="preserve" y="577">054曼迈线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="207" xml:space="preserve" y="697">052备用二线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="545" xml:space="preserve" y="114">名称</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="493" xml:space="preserve" y="144">差动保护软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="493" xml:space="preserve" y="174">高侧电压软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="493" xml:space="preserve" y="204">间隙保护软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="493" xml:space="preserve" y="234">低侧电压软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="493" xml:space="preserve" y="264">母线保护软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="514" xml:space="preserve" y="294">低周软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="514" xml:space="preserve" y="324">高周软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="514" xml:space="preserve" y="354">低压软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="482" xml:space="preserve" y="384">停用重合闸软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="514" xml:space="preserve" y="414">低周软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="514" xml:space="preserve" y="444">高周软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="514" xml:space="preserve" y="474">低压软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="482" xml:space="preserve" y="504">停用重合闸软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="514" xml:space="preserve" y="534">低周软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="514" xml:space="preserve" y="564">高周软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="514" xml:space="preserve" y="594">低压软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="482" xml:space="preserve" y="624">停用重合闸软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="482" xml:space="preserve" y="744">停用重合闸软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="514" xml:space="preserve" y="714">低压软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="514" xml:space="preserve" y="684">高周软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="514" xml:space="preserve" y="654">低周软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="832" xml:space="preserve" y="114">硬压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="983" xml:space="preserve" y="114">软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1140" xml:space="preserve" y="114">功能</text>
</g>
</svg>