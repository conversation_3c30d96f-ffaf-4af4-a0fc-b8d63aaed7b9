<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1300" id="thSvg" viewBox="0 0 2600 1300" width="2600">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="Arrester:bn_避雷器3_0" viewBox="0,0,20,40">
 <use Plane="0" x="10" xlink:href="#terminal" y="4"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="22" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,10,20)" width="10" x="5" y="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="31" y2="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="5" y2="18"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="5" x2="15" y1="35" y2="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="7" x2="13" y1="37" y2="37"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="11" y1="39" y2="39"/>
 <path AFMask="2147483647" Plane="0" d="M 8 17 L 10 23 L 12 17 Z" fill="none" stroke="rgb(93,92,88)" stroke-width="1"/>
</symbol>
<symbol id="Status:bn_不变化颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(0,0,255)" height="1" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_不变化颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(85,0,255)" height="1" stroke="rgb(85,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Disconnector:bn_刀闸15_0" viewBox="0,0,18,40">
 <use Plane="0" x="9" xlink:href="#terminal" y="2"/>
 <use Plane="0" x="9" xlink:href="#terminal" y="38"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="9" y1="5" y2="2"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="16" y1="2" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="9" y1="8" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="16" y1="5" y2="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="9" y1="35" y2="38"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="16" y1="38" y2="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="9" y1="32" y2="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="16" y1="35" y2="32"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="9" y1="5" y2="35"/>
</symbol>
<symbol id="Disconnector:bn_刀闸15_1" viewBox="0,0,18,40">
 <use Plane="0" x="9" xlink:href="#terminal" y="2"/>
 <use Plane="0" x="9" xlink:href="#terminal" y="38"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="9" y1="5" y2="2"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="16" y1="2" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="9" y1="6" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="16" y1="3" y2="6"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="9" y1="35" y2="38"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="16" y1="38" y2="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="9" y1="34" y2="37"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="16" y1="37" y2="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="9" y1="3" y2="37"/>
</symbol>
<symbol id="Disconnector:bn_刀闸3_0" viewBox="0,0,20,40">
 <use Plane="0" x="7" xlink:href="#terminal" y="3"/>
 <use Plane="0" x="7" xlink:href="#terminal" y="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="35" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="7" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="1" x2="15" y1="31" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="18" y1="7" y2="31"/>
</symbol>
<symbol id="Disconnector:bn_刀闸3_1" viewBox="0,0,20,40">
 <use Plane="0" x="7" xlink:href="#terminal" y="3"/>
 <use Plane="0" x="7" xlink:href="#terminal" y="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="35" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="1" x2="15" y1="31" y2="31"/>
</symbol>
<symbol id="Reactance:bn_电抗110_0" viewBox="0,0,28,40">
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="14" x2="14" y1="5" y2="11"/>
 <path AFMask="2147483647" Plane="0" d="M 5 20 A 9 9 0 1 0 14 11" fill="none" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="5" x2="14" y1="20" y2="20"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="14" x2="14" y1="20" y2="33"/>
</symbol>
<symbol id="Reactance:bn_电抗110_1" viewBox="0,0,28,40">
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="7" x2="7" y1="5" y2="11"/>
 <path AFMask="2147483647" Plane="0" d="M 2 15 A 5 5 0 1 0 7 10" fill="none" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="7" y1="16" y2="16"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="7" x2="7" y1="16" y2="25"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="8" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,7,29)" width="4" x="5" y="25"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="7" x2="7" y1="33" y2="41"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="3" y1="37" y2="41"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="11" x2="11" y1="37" y2="41"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="11" y1="41" y2="41"/>
</symbol>
<symbol id="Compensator:bn_电容器9_0" viewBox="0,0,32,48">
 <use Plane="0" x="15" xlink:href="#terminal" y="3"/>
 <use Plane="0" x="15" xlink:href="#terminal" y="43"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="15" x2="15" y1="4" y2="25"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="11" x2="19" y1="9" y2="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="19" x2="19" y1="9" y2="21"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="11" x2="11" y1="9" y2="21"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="11" x2="19" y1="21" y2="21"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="11" x2="19" y1="25" y2="25"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="11" x2="19" y1="28" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="15" x2="15" y1="28" y2="42"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="15" x2="7" y1="7" y2="7"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="7" x2="7" y1="7" y2="11"/>
 <path AFMask="2147483647" Plane="0" d="M 7 11 L 4 14 L 7 17 L 4 20 L 7 23 L 4 26 L 7 29 " fill="none" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="7" x2="7" y1="29" y2="32"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="7" x2="15" y1="32" y2="32"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="15" x2="27" y1="7" y2="7"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="27" x2="27" y1="7" y2="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="24" x2="30" y1="11" y2="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="30" x2="30" y1="12" y2="25"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="30" x2="24" y1="25" y2="25"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="24" x2="24" y1="11" y2="24"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="27" x2="27" y1="11" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="25" x2="29" y1="13" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="29" x2="27" y1="13" y2="15"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="25" x2="27" y1="13" y2="16"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="27" x2="27" y1="25" y2="29"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="24" x2="30" y1="29" y2="29"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="25" x2="29" y1="31" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="26" x2="28" y1="33" y2="33"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="2" y1="8" y2="32"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="9" y1="35" y2="39"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="20" y1="39" y2="39"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="20" x2="20" y1="39" y2="35"/>
</symbol>
<symbol id="PT:bn_电压互感器01_0" viewBox="0,0,100,100">
 <use Plane="0" x="49" xlink:href="#terminal" y="97"/>
 <circle AFMask="2147483647" Plane="0" cx="33" cy="24" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="33" y1="17" y2="24"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="28" y1="24" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="38" y1="24" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="49" x2="54" y1="40" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="49" x2="44" y1="40" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="49" x2="49" y1="33" y2="40"/>
 <circle AFMask="2147483647" Plane="0" cx="33" cy="40" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="49" cy="40" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="49" x2="49" y1="17" y2="24"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="49" x2="44" y1="24" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="49" x2="54" y1="24" y2="28"/>
 <circle AFMask="2147483647" Plane="0" cx="49" cy="24" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="39" x2="27" y1="38" y2="38"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="49" x2="49" y1="51" y2="97"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="21" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,49,67)" width="10" x="44" y="57"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="39" x2="33" y1="38" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="27" x2="33" y1="38" y2="44"/>
</symbol>
<symbol id="PT:bn_电压互感器05_0" viewBox="0,0,20,60">
 <use Plane="0" x="10" xlink:href="#terminal" y="18"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="10" x2="10" y1="31" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="10" x2="10" y1="19" y2="31"/>
 <circle AFMask="2147483647" Plane="0" cx="10" cy="50" fill="none" r="8" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="3" cy="43" fill="none" r="0" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="10" cy="39" fill="none" r="8" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="5" x2="15" y1="38" y2="38"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="5" x2="15" y1="51" y2="51"/>
</symbol>
<symbol id="PT:bn_电压互感器06_0" viewBox="0,0,100,100">
 <use Plane="0" x="33" xlink:href="#terminal" y="97"/>
 <circle AFMask="2147483647" Plane="0" cx="33" cy="24" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="33" y1="17" y2="24"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="28" y1="24" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="38" y1="24" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="49" x2="54" y1="24" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="49" x2="44" y1="24" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="49" x2="49" y1="17" y2="24"/>
 <circle AFMask="2147483647" Plane="0" cx="33" cy="40" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="49" cy="40" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="49" x2="49" y1="33" y2="40"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="49" x2="44" y1="40" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="49" x2="54" y1="40" y2="44"/>
 <circle AFMask="2147483647" Plane="0" cx="49" cy="24" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="39" x2="27" y1="38" y2="38"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="72" x2="78" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="73" x2="77" y1="10" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="74" x2="76" y1="8" y2="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="75" x2="75" y1="47" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="61" x2="75" y1="40" y2="40"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="28" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,75,62)" width="12" x="69" y="48"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="75" x2="75" y1="65" y2="62"/>
 <polygon AFMask="2147483647" Plane="0" fill="rgb(0,0,255)" points="72,72 75,55 78,72" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="33" y1="51" y2="97"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="21" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,33,67)" width="10" x="28" y="57"/>
 <path AFMask="2147483647" Plane="0" d="M 75 66 L 75 84 L 33 84 " fill="none" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="75" x2="49" y1="24" y2="24"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="39" x2="33" y1="38" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="27" x2="33" y1="38" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="49" x2="61" y1="40" y2="40"/>
</symbol>
<symbol id="Breaker:bn_断路器2_0" viewBox="0,0,20,40">
 <use Plane="0" x="10" xlink:href="#terminal" y="4"/>
 <use Plane="0" x="10" xlink:href="#terminal" y="36"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="30" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,10,20)" width="14" x="3" y="5"/>
</symbol>
<symbol id="Breaker:bn_断路器2_1" viewBox="0,0,20,40">
 <use Plane="0" x="10" xlink:href="#terminal" y="4"/>
 <use Plane="0" x="10" xlink:href="#terminal" y="36"/>
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,254)" height="30" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,10,20)" width="14" x="3" y="5"/>
</symbol>
<symbol id="Status:bn_非实测颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,85,0)" height="1" stroke="rgb(170,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_非实测颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,85,0)" height="1" stroke="rgb(170,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_工况退出颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,170,255)" height="1" stroke="rgb(170,170,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_工况退出颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,170,255)" height="1" stroke="rgb(170,170,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸1_0" viewBox="0,0,16,32">
 <use Plane="0" x="8" xlink:href="#terminal" y="2"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="4" y2="6"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="13" y1="27" y2="27"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="4" x2="12" y1="29" y2="29"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="6" x2="10" y1="31" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="27" y2="23"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="15" y1="22" y2="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="13" y1="6" y2="6"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸1_1" viewBox="0,0,16,32">
 <use Plane="0" x="8" xlink:href="#terminal" y="2"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="27" y2="4"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="6" x2="10" y1="31" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="4" x2="12" y1="29" y2="29"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="13" y1="27" y2="27"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="13" y1="6" y2="6"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸12_0" viewBox="0,0,40,20">
 <use Plane="0" x="4" xlink:href="#terminal" y="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="31" y1="6" y2="18"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="28" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="33" y1="8" y2="16"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="35" x2="35" y1="10" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="28" y1="2" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="4" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="9" y1="5" y2="19"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸12_1" viewBox="0,0,40,20">
 <use Plane="0" x="4" xlink:href="#terminal" y="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="31" y1="6" y2="18"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="3" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="33" y1="8" y2="16"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="35" x2="35" y1="10" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="9" y1="5" y2="19"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸15_0" viewBox="0,0,16,32">
 <use Plane="0" x="8" xlink:href="#terminal" y="29"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="26" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="13" y1="5" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="4" x2="12" y1="3" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="6" x2="10" y1="1" y2="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="9" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="1" x2="8" y1="22" y2="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="13" y1="25" y2="25"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸15_1" viewBox="0,0,16,32">
 <use Plane="0" x="8" xlink:href="#terminal" y="29"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="29" y2="6"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="6" x2="10" y1="1" y2="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="4" x2="12" y1="3" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="13" y1="25" y2="25"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="13" y1="5" y2="5"/>
</symbol>
<symbol id="Transformer2:bn_两卷变压器2_0" viewBox="0,0,56,76">
 <use Plane="0" x="25" xlink:href="#terminal" y="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="25" x2="33" y1="20" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="25" x2="17" y1="20" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="25" x2="25" y1="20" y2="30"/>
 <circle AFMask="2147483647" Plane="0" cx="25" cy="21" fill="none" r="18" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="47" y1="48" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="41" x2="47" y1="6" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="45" x2="47" y1="9" y2="3"/>
</symbol>
<symbol id="Transformer2:bn_两卷变压器2_1" viewBox="0,0,56,76">
 <use Plane="1" x="25" xlink:href="#terminal" y="71"/>
 <circle AFMask="2147483647" Plane="1" cx="25" cy="53" fill="none" r="18" stroke="rgb(0,0,255)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 25 43 L 15 59 L 35 59 Z" fill="none" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Fuse:bn_熔断器11_0" viewBox="0,0,32,16">
 <use Plane="0" x="29" xlink:href="#terminal" y="7"/>
 <use Plane="0" x="2" xlink:href="#terminal" y="7"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="8" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,16,8)" width="18" x="7" y="4"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="5" x2="27" y1="8" y2="8"/>
</symbol>
<symbol id="Status:bn_数据封锁颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,85,0)" height="1" stroke="rgb(255,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_数据封锁颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,85,0)" height="1" stroke="rgb(255,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_0_0" viewBox="0,0,20,74">
 <rect AFMask="2147483647" Plane="0" fill="none" height="0" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,4,16)" width="0" x="4" y="16"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="30" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,9,37)" width="13" x="3" y="22"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_0_1" viewBox="0,0,20,74">
 <rect AFMask="2147483647" Plane="0" fill="none" height="30" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,9,36)" width="13" x="3" y="21"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_0_2" viewBox="0,0,20,74">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,254)" height="30" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,9,36)" width="13" x="3" y="21"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_0_3" viewBox="0,0,20,74">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,254)" height="30" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,9,37)" width="13" x="3" y="22"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_1_0" viewBox="0,0,20,74">
 <use Plane="1" x="9" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="9" xlink:href="#terminal" y="69"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="21" y2="10"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="52" y2="63"/>
 <path AFMask="2147483647" Plane="1" d="M 3 11 L 10 5 L 17 12 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 3 57 L 9 64 L 16 57 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 3 15 L 10 9 L 17 16 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 3 61 L 9 68 L 16 61 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_1_1" viewBox="0,0,20,74">
 <use Plane="1" x="9" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="9" xlink:href="#terminal" y="69"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="21" y2="9"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="52" y2="65"/>
 <path AFMask="2147483647" Plane="1" d="M 4 12 L 10 6 L 16 12 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
 <path AFMask="2147483647" Plane="1" d="M 4 61 L 10 67 L 16 61 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_1_2" viewBox="0,0,20,74">
 <use Plane="1" x="9" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="9" xlink:href="#terminal" y="69"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="22" y2="10"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="52" y2="63"/>
 <path AFMask="2147483647" Plane="1" d="M 3 11 L 9 4 L 16 11 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 3 15 L 9 8 L 16 15 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 2 57 L 9 63 L 16 56 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 2 61 L 9 67 L 16 60 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_1_3" viewBox="0,0,20,74">
 <use Plane="1" x="9" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="9" xlink:href="#terminal" y="69"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="21" y2="8"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="52" y2="65"/>
 <path AFMask="2147483647" Plane="1" d="M 3 11 L 9 5 L 15 11 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
 <path AFMask="2147483647" Plane="1" d="M 3 60 L 9 66 L 15 60 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
</symbol>
<symbol id="Status:bn_越限颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_越限颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Transformer2:bn_站用变2_0" viewBox="0,0,60,86">
 <use Plane="0" x="36" xlink:href="#terminal" y="22"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="36" x2="45" y1="40" y2="49"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="36" x2="29" y1="40" y2="47"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="36" x2="36" y1="24" y2="40"/>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="39" fill="none" r="15" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Transformer2:bn_站用变2_1" viewBox="0,0,60,86">
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="28" x2="43" y1="66" y2="66"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="36" x2="43" y1="58" y2="65"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="35" x2="28" y1="58" y2="65"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="10" x2="36" y1="40" y2="40"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="38" x2="38" y1="79" y2="82"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="12" x2="12" y1="29" y2="39"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="5" x2="5" y1="39" y2="42"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="7" x2="7" y1="42" y2="38"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="9" y1="37" y2="43"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="25" x2="18" y1="25" y2="25"/>
 <circle AFMask="2147483647" Plane="1" cx="36" cy="63" fill="none" r="15" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="12" x2="30" y1="40" y2="18"/>
</symbol>
<symbol id="Terminal:bn_终端设备10_0" viewBox="0,0,38,12">
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,255,0)" stroke-width="1" x1="2" x2="7" y1="6" y2="6"/>
 <polygon AFMask="2147483647" Plane="0" fill="none" points="7,3 14,5 7,8" stroke="rgb(0,255,0)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,255,0)" stroke-width="1" x1="12" x2="22" y1="6" y2="6"/>
 <polygon AFMask="2147483647" Plane="0" fill="none" points="21,5 28,3 28,8" stroke="rgb(0,255,0)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,255,0)" stroke-width="1" x1="27" x2="32" y1="6" y2="6"/>
</symbol>
<symbol id="GZP:gg_光子牌1_0" viewBox="0,0,36,36">
 <circle AFMask="2147483647" Plane="0" cx="18" cy="18" fill="rgb(0,255,0)" r="15" stroke="rgb(0,255,0)" stroke-width="1"/>
</symbol>
<symbol id="GZP:gg_光子牌1_1" viewBox="0,0,36,36">
 <circle AFMask="2147483647" Plane="0" cx="18" cy="18" fill="rgb(255,0,0)" r="15" stroke="rgb(255,0,0)" stroke-width="1"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="版纳_测试" InitShowingPlane="0," fill="rgb(0,0,0)" height="1300" width="2600" x="0" y="0"/>
</g>
<g id="Other_Layer">
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="11" x2="370" y1="148" y2="148"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="10" x2="10" y1="24" y2="1274"/>
 
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="11" x2="370" y1="1275" y2="1275"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="11" x2="370" y1="24" y2="24"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="370" x2="370" y1="24" y2="1274"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="97" x2="97" y1="370" y2="594"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="172" x2="172" y1="597" y2="748"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="97" x2="97" y1="824" y2="973"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="10" x2="369" y1="369" y2="369"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="97" x2="369" y1="444" y2="444"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="97" x2="369" y1="520" y2="520"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="9" x2="369" y1="595" y2="595"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="9" x2="369" y1="672" y2="672"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="10" x2="369" y1="748" y2="748"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="9" x2="369" y1="822" y2="822"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="98" x2="369" y1="898" y2="898"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="9" x2="369" y1="974" y2="974"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="11" x2="370" y1="242" y2="242"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="303" x2="303" y1="246" y2="369"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="230" x2="230" y1="246" y2="369"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="154" x2="154" y1="246" y2="369"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="80" x2="80" y1="246" y2="369"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="10" x2="370" y1="306" y2="306"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="9" x2="369" y1="1040" y2="1040"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="9" x2="369" y1="1106" y2="1106"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,0)" stroke-width="1" x1="1541" x2="1509" y1="181" y2="181"/>
 <rect AFMask="39039" Plane="0" fill="rgb(0,255,0)" height="332" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,574,283)" width="192" x="478" y="117"/>
</g>
<g id="Bus_Layer">
 <g id="30003825">
  <path d="M 719 798 L 2436 798" stroke="rgb(128,128,128)" stroke-width="6"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115404740451369847" ObjectName="115404740451369847" Plane="0"/>
  </metadata>
 <path d="M 719 798 L 2436 798" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="30003826">
  <path d="M 852 394 L 2274 394" stroke="rgb(128,128,128)" stroke-width="6"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115404740451369846" ObjectName="115404740451369846" Plane="0"/>
  </metadata>
 <path d="M 852 394 L 2274 394" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="Breaker_Layer">
 <g id="100003949">
  <use class="kv35kV" height="40" transform="rotate(0,1508,504) scale(1,1) translate(-10,-20)" width="20" x="1508" xlink:href="#Breaker:bn_断路器2_0" y="504"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521243032" ObjectName="114560315521243032" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319934872"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1508,504) scale(1,1) translate(-10,-20)" width="20" x="1508" y="504"/></g>
 <g id="100003950">
  <use class="kv35kV" height="40" transform="rotate(0,1508,288) scale(1,1) translate(-10,-20)" width="20" x="1508" xlink:href="#Breaker:bn_断路器2_0" y="288"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521243031" ObjectName="114560315521243031" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319934871"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1508,288) scale(1,1) translate(-10,-20)" width="20" x="1508" y="288"/></g>
</g>
<g id="Disconnector_Layer">
 <g id="101003951">
  <use class="kv35kV" height="40" transform="rotate(0,1508,356) scale(1,1) translate(-8,-19)" width="20" x="1508" xlink:href="#Disconnector:bn_刀闸3_0" y="356"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959381" ObjectName="114841790497959381" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346978261"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1508,356) scale(1,1) translate(-8,-19)" width="20" x="1508" y="356"/></g>
 <g id="101003952">
  <use class="kv35kV" height="40" transform="rotate(0,1508,435) scale(1,1) translate(-8,-19)" width="20" x="1508" xlink:href="#Disconnector:bn_刀闸3_0" y="435"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959383" ObjectName="114841790497959383" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346978263"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1508,435) scale(1,1) translate(-8,-19)" width="20" x="1508" y="435"/></g>
 <g id="101003953">
  <use class="kv35kV" height="40" transform="rotate(0,1508,216) scale(1,1) translate(-8,-19)" width="20" x="1508" xlink:href="#Disconnector:bn_刀闸3_0" y="216"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959380" ObjectName="114841790497959380" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346978260"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1508,216) scale(1,1) translate(-8,-19)" width="20" x="1508" y="216"/></g>
 <g id="101003955">
  <use class="kv10kV" height="40" transform="rotate(0,826,1025) scale(1.3,1.3) translate(-198.615,-255.538)" width="20" x="826" xlink:href="#Disconnector:bn_刀闸3_0" y="1025"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959390" ObjectName="114841790497959390" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346978270"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,826,1025) scale(1.3,1.3) translate(-198.615,-255.538)" width="20" x="826" y="1025"/></g>
 <g id="101003956">
  <use class="kv10kV" height="40" transform="rotate(0,1087,1025) scale(1.3,1.3) translate(-258.846,-255.538)" width="20" x="1087" xlink:href="#Disconnector:bn_刀闸3_0" y="1025"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959392" ObjectName="114841790497959392" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346978272"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1087,1025) scale(1.3,1.3) translate(-258.846,-255.538)" width="20" x="1087" y="1025"/></g>
 <g id="101003957">
  <use class="kv10kV" height="40" transform="rotate(0,1348,1025) scale(1.3,1.3) translate(-319.077,-255.538)" width="20" x="1348" xlink:href="#Disconnector:bn_刀闸3_0" y="1025"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959394" ObjectName="114841790497959394" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346978274"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1348,1025) scale(1.3,1.3) translate(-319.077,-255.538)" width="20" x="1348" y="1025"/></g>
 <g id="101003958">
  <use class="kv10kV" height="40" transform="rotate(0,1609,1025) scale(1.3,1.3) translate(-379.308,-255.538)" width="20" x="1609" xlink:href="#Disconnector:bn_刀闸3_0" y="1025"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959396" ObjectName="114841790497959396" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346978276"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1609,1025) scale(1.3,1.3) translate(-379.308,-255.538)" width="20" x="1609" y="1025"/></g>
 <g id="101003959">
  <use class="kv10kV" height="40" transform="rotate(0,1870,1025) scale(1.3,1.3) translate(-439.538,-255.538)" width="20" x="1870" xlink:href="#Disconnector:bn_刀闸3_0" y="1025"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959398" ObjectName="114841790497959398" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346978278"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1870,1025) scale(1.3,1.3) translate(-439.538,-255.538)" width="20" x="1870" y="1025"/></g>
 <g id="101003961">
  <use class="kv35kV" height="40" transform="rotate(0,1147,435) scale(1,1) translate(-8,-19)" width="20" x="1147" xlink:href="#Disconnector:bn_刀闸3_0" y="435"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959382" ObjectName="114841790497959382" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346978262"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1147,435) scale(1,1) translate(-8,-19)" width="20" x="1147" y="435"/></g>
 <g id="101003962">
  <use class="kv35kV" height="40" transform="rotate(0,1917,471) scale(1,1) translate(-8,-19)" width="20" x="1917" xlink:href="#Disconnector:bn_刀闸3_0" y="471"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959384" ObjectName="114841790497959384" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346978264"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1917,471) scale(1,1) translate(-8,-19)" width="20" x="1917" y="471"/></g>
 <g id="101003964">
  <use class="kv-1" height="40" transform="rotate(0,2194,340) scale(1,1) translate(-8,-19)" width="20" x="2194" xlink:href="#Disconnector:bn_刀闸3_0" y="340"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,2194,340) scale(1,1) translate(-8,-19)" width="20" x="2194" y="340"/></g>
 <g id="101003965">
  <use class="kv35kV" height="40" transform="rotate(-90,1556,181) scale(1,1) translate(-8,-19)" width="20" x="1556" xlink:href="#Disconnector:bn_刀闸3_0" y="181"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959379" ObjectName="114841790497959379" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346978259"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(-90,1556,181) scale(1,1) translate(-8,-19)" width="20" x="1556" y="181"/></g>
 <g id="101004017">
  <use class="kv-1" height="40" transform="rotate(0,2193,703) scale(1.2,1.567) translate(-374.5,-274.372)" width="18" x="2193" xlink:href="#Disconnector:bn_刀闸15_0" y="703"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,2193,703) scale(1.2,1.567) translate(-374.5,-274.372)" width="18" x="2193" y="703"/></g>
 <g id="101004023">
  <use class="kv10kV" height="40" transform="rotate(0,2132,857) scale(1.2,1.567) translate(-364.333,-330.095)" width="18" x="2132" xlink:href="#Disconnector:bn_刀闸15_0" y="857"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959399" ObjectName="114841790497959399" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346978279"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,2132,857) scale(1.2,1.567) translate(-364.333,-330.095)" width="18" x="2132" y="857"/></g>
 <g id="101004025">
  <use class="kv10kV" height="40" transform="rotate(0,985,723) scale(1.2,1.567) translate(-173.167,-281.609)" width="18" x="985" xlink:href="#Disconnector:bn_刀闸15_0" y="723"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959386" ObjectName="114841790497959386" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346978266"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,985,723) scale(1.2,1.567) translate(-173.167,-281.609)" width="18" x="985" y="723"/></g>
</g>
<g id="GroundDisconnector_Layer">
 <g id="111003979">
  <use class="kv35kV" height="20" transform="rotate(360,1477,472) scale(-1,1) translate(-2958,-12)" width="40" x="1477" xlink:href="#GroundDisconnector:bn_接地刀闸12_0" y="472"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115123265474666510" ObjectName="115123265474666510" Plane="0"/>
   <cge:Meas_Ref ObjectID="115123394323685390"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(360,1477,472) scale(-1,1) translate(-2958,-12)" width="40" x="1477" y="472"/></g>
 <g id="111003980">
  <use class="kv35kV" height="20" transform="rotate(360,1479,319) scale(-1,1) translate(-2962,-12)" width="40" x="1479" xlink:href="#GroundDisconnector:bn_接地刀闸12_0" y="319"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115123265474666508" ObjectName="115123265474666508" Plane="0"/>
   <cge:Meas_Ref ObjectID="115123394323685388"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(360,1479,319) scale(-1,1) translate(-2962,-12)" width="40" x="1479" y="319"/></g>
 <g id="111003981">
  <use class="kv35kV" height="20" transform="rotate(360,1479,254) scale(-1,1) translate(-2962,-12)" width="40" x="1479" xlink:href="#GroundDisconnector:bn_接地刀闸12_0" y="254"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115123265474666507" ObjectName="115123265474666507" Plane="0"/>
   <cge:Meas_Ref ObjectID="115123394323685387"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(360,1479,254) scale(-1,1) translate(-2962,-12)" width="40" x="1479" y="254"/></g>
 <g id="111003982">
  <use class="kv35kV" height="20" transform="rotate(360,1479,181) scale(-1,1) translate(-2962,-12)" width="40" x="1479" xlink:href="#GroundDisconnector:bn_接地刀闸12_0" y="181"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115123265474666506" ObjectName="115123265474666506" Plane="0"/>
   <cge:Meas_Ref ObjectID="115123394323685386"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(360,1479,181) scale(-1,1) translate(-2962,-12)" width="40" x="1479" y="181"/></g>
 <g id="111003983">
  <use class="kv10kV" height="32" transform="rotate(270,797,920) scale(1,-1) translate(-8,-1842)" width="16" x="797" xlink:href="#GroundDisconnector:bn_接地刀闸1_0" y="920"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115123265474666514" ObjectName="115123265474666514" Plane="0"/>
   <cge:Meas_Ref ObjectID="115123394323685394"/>
  </metadata>
 <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(270,797,920) scale(1,-1) translate(-8,-1842)" width="16" x="797" y="920"/></g>
 <g id="111003984">
  <use class="kv10kV" height="32" transform="rotate(270,1058,920) scale(1,-1) translate(-8,-1842)" width="16" x="1058" xlink:href="#GroundDisconnector:bn_接地刀闸1_0" y="920"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115123265474666515" ObjectName="115123265474666515" Plane="0"/>
   <cge:Meas_Ref ObjectID="115123394323685395"/>
  </metadata>
 <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(270,1058,920) scale(1,-1) translate(-8,-1842)" width="16" x="1058" y="920"/></g>
 <g id="111003985">
  <use class="kv10kV" height="32" transform="rotate(270,1319,920) scale(1,-1) translate(-8,-1842)" width="16" x="1319" xlink:href="#GroundDisconnector:bn_接地刀闸1_0" y="920"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115123265474666516" ObjectName="115123265474666516" Plane="0"/>
   <cge:Meas_Ref ObjectID="115123394323685396"/>
  </metadata>
 <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(270,1319,920) scale(1,-1) translate(-8,-1842)" width="16" x="1319" y="920"/></g>
 <g id="111003986">
  <use class="kv10kV" height="32" transform="rotate(270,1580,920) scale(1,-1) translate(-8,-1842)" width="16" x="1580" xlink:href="#GroundDisconnector:bn_接地刀闸1_0" y="920"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115123265474666517" ObjectName="115123265474666517" Plane="0"/>
   <cge:Meas_Ref ObjectID="115123394323685397"/>
  </metadata>
 <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(270,1580,920) scale(1,-1) translate(-8,-1842)" width="16" x="1580" y="920"/></g>
 <g id="111003987">
  <use class="kv10kV" height="32" transform="rotate(270,1842,920) scale(1,-1) translate(-8,-1842)" width="16" x="1842" xlink:href="#GroundDisconnector:bn_接地刀闸1_0" y="920"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115123265474666518" ObjectName="115123265474666518" Plane="0"/>
   <cge:Meas_Ref ObjectID="115123394323685398"/>
  </metadata>
 <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(270,1842,920) scale(1,-1) translate(-8,-1842)" width="16" x="1842" y="920"/></g>
 <g id="111003988">
  <use class="kv10kV" height="32" transform="rotate(0,1811,1062) scale(1,1) translate(-8,-29)" width="16" x="1811" xlink:href="#GroundDisconnector:bn_接地刀闸15_0" y="1062"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115123265474666519" ObjectName="115123265474666519" Plane="0"/>
   <cge:Meas_Ref ObjectID="115123394323685399"/>
  </metadata>
 <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(0,1811,1062) scale(1,1) translate(-8,-29)" width="16" x="1811" y="1062"/></g>
 <g id="111003989">
  <use class="kv35kV" height="20" transform="rotate(360,1114,472) scale(-1,1) translate(-2232,-12)" width="40" x="1114" xlink:href="#GroundDisconnector:bn_接地刀闸12_0" y="472"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115123265474666509" ObjectName="115123265474666509" Plane="0"/>
   <cge:Meas_Ref ObjectID="115123394323685389"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(360,1114,472) scale(-1,1) translate(-2232,-12)" width="40" x="1114" y="472"/></g>
 <g id="111003990">
  <use class="kv35kV" height="20" transform="rotate(180,1968,418) scale(-1,1) translate(-3940,-12)" width="40" x="1968" xlink:href="#GroundDisconnector:bn_接地刀闸12_0" y="418"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115123265474666511" ObjectName="115123265474666511" Plane="0"/>
   <cge:Meas_Ref ObjectID="115123394323685391"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1968,418) scale(-1,1) translate(-3940,-12)" width="40" x="1968" y="418"/></g>
 <g id="111003991">
  <use class="kv35kV" height="20" transform="rotate(180,1968,512) scale(-1,1) translate(-3940,-12)" width="40" x="1968" xlink:href="#GroundDisconnector:bn_接地刀闸12_0" y="512"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115123265474666512" ObjectName="115123265474666512" Plane="0"/>
   <cge:Meas_Ref ObjectID="115123394323685392"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1968,512) scale(-1,1) translate(-3940,-12)" width="40" x="1968" y="512"/></g>
 <g id="111003992">
  <use class="kv-1" height="20" transform="rotate(90,2214,251) scale(-1,1) translate(-4432,-12)" width="40" x="2214" xlink:href="#GroundDisconnector:bn_接地刀闸12_0" y="251"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,2214,251) scale(-1,1) translate(-4432,-12)" width="40" x="2214" y="251"/></g>
</g>
<g id="Transformer2_Layer">
<g id="102003966">
 <g id="1020039660">
  <use class="kv-1" height="86" transform="rotate(0,2129,1018) scale(1.3,1.3) translate(-527.308,-256.923)" width="60" x="2129" xlink:href="#Transformer2:bn_站用变2_0" y="1018"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="117375065288344740" ObjectName="117375065288344740" Plane="0"/>
  </metadata>
 </g>
 <g id="1020039661">
  <use class="kv-1" height="86" transform="rotate(0,2129,1018) scale(1.3,1.3) translate(-527.308,-256.923)" width="60" x="2129" xlink:href="#Transformer2:bn_站用变2_1" y="1018"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="117375065288344741" ObjectName="117375065288344741" Plane="0"/>
  </metadata>
 </g>
 <metadata>
  <cge:PSR_Ref ObjectID="-1" ObjectName="-1" Plane="0"/>
 </metadata>
<rect fill="white" height="86" opacity="0" stroke="white" transform="rotate(0,2129,1018) scale(1.3,1.3) translate(-527.308,-256.923)" width="60" x="2129" y="1018"/></g>
<g id="102003967">
 <g id="1020039670">
  <use class="kv-1" height="76" transform="rotate(0,1508,596) scale(1.5,1.5) translate(-527.667,-235.667)" width="56" x="1508" xlink:href="#Transformer2:bn_两卷变压器2_0" y="596"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="117375065288344742" ObjectName="117375065288344742" Plane="0"/>
  </metadata>
 </g>
 <g id="1020039671">
  <use class="kv-1" height="76" transform="rotate(0,1508,596) scale(1.5,1.5) translate(-527.667,-235.667)" width="56" x="1508" xlink:href="#Transformer2:bn_两卷变压器2_1" y="596"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="117375065288344743" ObjectName="117375065288344743" Plane="0"/>
  </metadata>
 </g>
 <metadata>
  <cge:PSR_Ref ObjectID="-1" ObjectName="-1" Plane="0"/>
 </metadata>
<rect fill="white" height="76" opacity="0" stroke="white" transform="rotate(0,1508,596) scale(1.5,1.5) translate(-527.667,-235.667)" width="56" x="1508" y="596"/></g>
<g id="102003968">
 <g id="1020039680">
  <use class="kv-1" height="86" transform="rotate(0,1144,571) scale(1.3,1.3) translate(-300,-153.769)" width="60" x="1144" xlink:href="#Transformer2:bn_站用变2_0" y="571"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="117375065288344738" ObjectName="117375065288344738" Plane="0"/>
  </metadata>
 </g>
 <g id="1020039681">
  <use class="kv-1" height="86" transform="rotate(0,1144,571) scale(1.3,1.3) translate(-300,-153.769)" width="60" x="1144" xlink:href="#Transformer2:bn_站用变2_1" y="571"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="117375065288344739" ObjectName="117375065288344739" Plane="0"/>
  </metadata>
 </g>
 <metadata>
  <cge:PSR_Ref ObjectID="-1" ObjectName="-1" Plane="0"/>
 </metadata>
<rect fill="white" height="86" opacity="0" stroke="white" transform="rotate(0,1144,571) scale(1.3,1.3) translate(-300,-153.769)" width="60" x="1144" y="571"/></g>
</g>
<g id="DollyBreaker_Layer">
 <g id="110003973">
  <use class="kv10kV" height="74" transform="rotate(0,827,864) scale(1.2,1.2) translate(-147.833,-181)" width="20" x="827" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="864"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521243034" ObjectName=" 114560315521243034" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319934874"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,827,864) scale(1.2,1.2) translate(-147.833,-181)" width="20" x="827" y="864"/></g>
 <g id="110003973">
  <use class="kv10kV" height="74" transform="rotate(0,827,864) scale(1.2,1.2) translate(-147.833,-181)" width="20" x="827" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="864"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959389" ObjectName=" 114841790497959389" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841962296651229"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,827,864) scale(1.2,1.2) translate(-147.833,-181)" width="20" x="827" y="864"/></g>
 <g id="110003974">
  <use class="kv10kV" height="74" transform="rotate(0,1088,864) scale(1.2,1.2) translate(-191.333,-181)" width="20" x="1088" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="864"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521243035" ObjectName=" 114560315521243035" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319934875"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1088,864) scale(1.2,1.2) translate(-191.333,-181)" width="20" x="1088" y="864"/></g>
 <g id="110003974">
  <use class="kv10kV" height="74" transform="rotate(0,1088,864) scale(1.2,1.2) translate(-191.333,-181)" width="20" x="1088" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="864"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959391" ObjectName=" 114841790497959391" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841962296651231"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1088,864) scale(1.2,1.2) translate(-191.333,-181)" width="20" x="1088" y="864"/></g>
 <g id="110003975">
  <use class="kv10kV" height="74" transform="rotate(0,1349,864) scale(1.2,1.2) translate(-234.833,-181)" width="20" x="1349" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="864"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521243036" ObjectName=" 114560315521243036" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319934876"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1349,864) scale(1.2,1.2) translate(-234.833,-181)" width="20" x="1349" y="864"/></g>
 <g id="110003975">
  <use class="kv10kV" height="74" transform="rotate(0,1349,864) scale(1.2,1.2) translate(-234.833,-181)" width="20" x="1349" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="864"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959393" ObjectName=" 114841790497959393" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841962296651233"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1349,864) scale(1.2,1.2) translate(-234.833,-181)" width="20" x="1349" y="864"/></g>
 <g id="110003976">
  <use class="kv10kV" height="74" transform="rotate(0,1610,864) scale(1.2,1.2) translate(-278.333,-181)" width="20" x="1610" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="864"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521243037" ObjectName=" 114560315521243037" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319934877"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1610,864) scale(1.2,1.2) translate(-278.333,-181)" width="20" x="1610" y="864"/></g>
 <g id="110003976">
  <use class="kv10kV" height="74" transform="rotate(0,1610,864) scale(1.2,1.2) translate(-278.333,-181)" width="20" x="1610" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="864"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959395" ObjectName=" 114841790497959395" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841962296651235"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1610,864) scale(1.2,1.2) translate(-278.333,-181)" width="20" x="1610" y="864"/></g>
 <g id="110003977">
  <use class="kv10kV" height="74" transform="rotate(0,1872,864) scale(1.2,1.2) translate(-322,-181)" width="20" x="1872" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="864"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521243038" ObjectName=" 114560315521243038" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319934878"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1872,864) scale(1.2,1.2) translate(-322,-181)" width="20" x="1872" y="864"/></g>
 <g id="110003977">
  <use class="kv10kV" height="74" transform="rotate(0,1872,864) scale(1.2,1.2) translate(-322,-181)" width="20" x="1872" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="864"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959397" ObjectName=" 114841790497959397" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841962296651237"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1872,864) scale(1.2,1.2) translate(-322,-181)" width="20" x="1872" y="864"/></g>
 <g id="110003978">
  <use class="kv10kV" height="74" transform="rotate(0,1508,741) scale(1.2,1.2) translate(-261.333,-160.5)" width="20" x="1508" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="741"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521243033" ObjectName=" 114560315521243033" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319934873"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1508,741) scale(1.2,1.2) translate(-261.333,-160.5)" width="20" x="1508" y="741"/></g>
 <g id="110003978">
  <use class="kv-1" height="74" transform="rotate(0,1508,741) scale(1.2,1.2) translate(-261.333,-160.5)" width="20" x="1508" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="741"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521243033" ObjectName=" 114560315521243033" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319934873"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1508,741) scale(1.2,1.2) translate(-261.333,-160.5)" width="20" x="1508" y="741"/></g>
</g>
<g id="Compensator_Layer">
 <g id="113004000">
  <use class="kv-1" height="48" transform="rotate(360,1872,1148) scale(-1,1) translate(-3759,-24)" width="32" x="1872" xlink:href="#Compensator:bn_电容器9_0" y="1148"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="48" opacity="0" stroke="white" transform="rotate(360,1872,1148) scale(-1,1) translate(-3759,-24)" width="32" x="1872" y="1148"/></g>
</g>
<g id="Reactor_Layer">
 <g id="114004001">
  <use class="kv-1" height="40" transform="rotate(0,1871,1109) scale(1,1) translate(-13,-23)" width="28" x="1871" xlink:href="#Reactance:bn_电抗110_0" y="1109"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1871,1109) scale(1,1) translate(-13,-23)" width="28" x="1871" y="1109"/></g>
</g>
<g id="Load_Layer">
 <g id="32003827">
 <path d="M 827 1109 L 827 1161" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115967690404792081" ObjectName="115967690404792081" Plane="0"/>
  </metadata>
 <path d="M 827 1109 L 827 1161" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32003828">
 <path d="M 1088 1109 L 1088 1161" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115967690404792082" ObjectName="115967690404792082" Plane="0"/>
  </metadata>
 <path d="M 1088 1109 L 1088 1161" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32003829">
 <path d="M 1349 1109 L 1349 1161" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115967690404792083" ObjectName="115967690404792083" Plane="0"/>
  </metadata>
 <path d="M 1349 1109 L 1349 1161" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32003830">
 <path d="M 1610 1109 L 1610 1161" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115967690404792084" ObjectName="115967690404792084" Plane="0"/>
  </metadata>
 <path d="M 1610 1109 L 1610 1161" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="ACLineSegment_Layer">
 <g id="36003948">
 <path d="M 1508 156 L 1508 133" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="ST=-1/LN=-1" ObjectName="ST=-1/LN=-1" Plane="0"/>
  </metadata>
 <path d="M 1508 156 L 1508 133" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="PT_Layer">
 <g id="107003969">
  <use class="kv-1" height="60" transform="rotate(0,893,961) scale(1,1) translate(-10,-19)" width="20" x="893" xlink:href="#PT:bn_电压互感器05_0" y="961"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189798" ObjectName="118500965195189798" Plane="0"/>
  </metadata>
 <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,893,961) scale(1,1) translate(-10,-19)" width="20" x="893" y="961"/></g>
 <g id="107003970">
  <use class="kv-1" height="100" transform="rotate(0,985,667) scale(1,1) translate(-33,-97)" width="100" x="985" xlink:href="#PT:bn_电压互感器06_0" y="667"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189815" ObjectName="118500965195189815" Plane="0"/>
  </metadata>
 <rect fill="white" height="100" opacity="0" stroke="white" transform="rotate(0,985,667) scale(1,1) translate(-33,-97)" width="100" x="985" y="667"/></g>
 <g id="107003971">
  <use class="kv-1" height="100" transform="rotate(180,1917,553) scale(1,1) translate(-49,-97)" width="100" x="1917" xlink:href="#PT:bn_电压互感器01_0" y="553"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189814" ObjectName="118500965195189814" Plane="0"/>
  </metadata>
 <rect fill="white" height="100" opacity="0" stroke="white" transform="rotate(180,1917,553) scale(1,1) translate(-49,-97)" width="100" x="1917" y="553"/></g>
 <g id="107003972">
  <use class="kv-1" height="60" transform="rotate(-90,1642,181) scale(1,1) translate(-10,-19)" width="20" x="1642" xlink:href="#PT:bn_电压互感器05_0" y="181"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189795" ObjectName="118500965195189795" Plane="0"/>
  </metadata>
 <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(-90,1642,181) scale(1,1) translate(-10,-19)" width="20" x="1642" y="181"/></g>
</g>
<g id="GZP_Layer">
 <g id="135003686">
  <use class="kv-1" height="36" transform="rotate(0,260,787) scale(0.9,0.9) translate(10.8889,69.4445)" width="36" x="260" xlink:href="#GZP:gg_光子牌1_0" y="787"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,260,787) scale(0.9,0.9) translate(10.8889,69.4445)" width="36" x="260" y="787"/></g>
</g>
<g id="Arrester_Layer">
 <g id="133004005">
  <use class="kv-1" height="40" transform="rotate(0,851,1096) scale(1,1) translate(-10,-5)" width="20" x="851" xlink:href="#Arrester:bn_避雷器3_0" y="1096"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189802" ObjectName="118500965195189802" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,851,1096) scale(1,1) translate(-10,-5)" width="20" x="851" y="1096"/></g>
 <g id="133004006">
  <use class="kv-1" height="40" transform="rotate(0,854,930) scale(1,1) translate(-10,-5)" width="20" x="854" xlink:href="#Arrester:bn_避雷器3_0" y="930"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189800" ObjectName="118500965195189800" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,854,930) scale(1,1) translate(-10,-5)" width="20" x="854" y="930"/></g>
 <g id="133004007">
  <use class="kv-1" height="40" transform="rotate(0,1112,1096) scale(1,1) translate(-10,-5)" width="20" x="1112" xlink:href="#Arrester:bn_避雷器3_0" y="1096"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189804" ObjectName="118500965195189804" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1112,1096) scale(1,1) translate(-10,-5)" width="20" x="1112" y="1096"/></g>
 <g id="133004008">
  <use class="kv-1" height="40" transform="rotate(270,1115,920) scale(1,1) translate(-10,-5)" width="20" x="1115" xlink:href="#Arrester:bn_避雷器3_0" y="920"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189803" ObjectName="118500965195189803" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(270,1115,920) scale(1,1) translate(-10,-5)" width="20" x="1115" y="920"/></g>
 <g id="133004010">
  <use class="kv-1" height="40" transform="rotate(270,1376,920) scale(1,1) translate(-10,-5)" width="20" x="1376" xlink:href="#Arrester:bn_避雷器3_0" y="920"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189805" ObjectName="118500965195189805" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(270,1376,920) scale(1,1) translate(-10,-5)" width="20" x="1376" y="920"/></g>
 <g id="133004009">
  <use class="kv-1" height="40" transform="rotate(0,1373,1096) scale(1,1) translate(-10,-5)" width="20" x="1373" xlink:href="#Arrester:bn_避雷器3_0" y="1096"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189806" ObjectName="118500965195189806" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1373,1096) scale(1,1) translate(-10,-5)" width="20" x="1373" y="1096"/></g>
 <g id="133004011">
  <use class="kv-1" height="40" transform="rotate(0,1634,1096) scale(1,1) translate(-10,-5)" width="20" x="1634" xlink:href="#Arrester:bn_避雷器3_0" y="1096"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189808" ObjectName="118500965195189808" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1634,1096) scale(1,1) translate(-10,-5)" width="20" x="1634" y="1096"/></g>
 <g id="133004012">
  <use class="kv-1" height="40" transform="rotate(270,1637,920) scale(1,1) translate(-10,-5)" width="20" x="1637" xlink:href="#Arrester:bn_避雷器3_0" y="920"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189807" ObjectName="118500965195189807" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(270,1637,920) scale(1,1) translate(-10,-5)" width="20" x="1637" y="920"/></g>
 <g id="133004013">
  <use class="kv-1" height="40" transform="rotate(270,1899,920) scale(1,1) translate(-10,-5)" width="20" x="1899" xlink:href="#Arrester:bn_避雷器3_0" y="920"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189809" ObjectName="118500965195189809" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(270,1899,920) scale(1,1) translate(-10,-5)" width="20" x="1899" y="920"/></g>
 <g id="133004014">
  <use class="kv-1" height="40" transform="rotate(90,1460,686) scale(1,1) translate(-10,-5)" width="20" x="1460" xlink:href="#Arrester:bn_避雷器3_0" y="686"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,1460,686) scale(1,1) translate(-10,-5)" width="20" x="1460" y="686"/></g>
 <g id="133004015">
  <use class="kv-1" height="40" transform="rotate(0,1882,546) scale(1,1) translate(-10,-5)" width="20" x="1882" xlink:href="#Arrester:bn_避雷器3_0" y="546"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1882,546) scale(1,1) translate(-10,-5)" width="20" x="1882" y="546"/></g>
 <g id="133004016">
  <use class="kv-1" height="40" transform="rotate(90,1460,156) scale(1,1) translate(-10,-5)" width="20" x="1460" xlink:href="#Arrester:bn_避雷器3_0" y="156"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189797" ObjectName="118500965195189797" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,1460,156) scale(1,1) translate(-10,-5)" width="20" x="1460" y="156"/></g>
</g>
<g id="Fuse_Layer">
 <g id="131004002">
  <use class="kv-1" height="16" transform="rotate(90,893,933) scale(1,1) translate(-16,-8)" width="32" x="893" xlink:href="#Fuse:bn_熔断器11_0" y="933"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189811" ObjectName="118500965195189811" Plane="0"/>
  </metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(90,893,933) scale(1,1) translate(-16,-8)" width="32" x="893" y="933"/></g>
 <g id="131004003">
  <use class="kv-1" height="16" transform="rotate(90,1147,513) scale(1.3,1.3) translate(-280.692,-126.385)" width="32" x="1147" xlink:href="#Fuse:bn_熔断器11_0" y="513"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189796" ObjectName="118500965195189796" Plane="0"/>
  </metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(90,1147,513) scale(1.3,1.3) translate(-280.692,-126.385)" width="32" x="1147" y="513"/></g>
 <g id="131004004">
  <use class="kv-1" height="16" transform="rotate(0,1613,181) scale(1,1) translate(-16,-8)" width="32" x="1613" xlink:href="#Fuse:bn_熔断器11_0" y="181"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189812" ObjectName="118500965195189812" Plane="0"/>
  </metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(0,1613,181) scale(1,1) translate(-16,-8)" width="32" x="1613" y="181"/></g>
</g>
<g id="Status_Layer">
 <g id="126003715">
  <use class="kv-1" height="40" transform="rotate(0,45,275) scale(0.7,0.7) translate(-10.7143,97.8572)" width="60" x="45" xlink:href="#Status:bn_工况退出颜色显示_0" y="275"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,45,275) scale(0.7,0.7) translate(-10.7143,97.8572)" width="60" x="45" y="275"/></g>
 <g id="126003716">
  <use class="kv-1" height="40" transform="rotate(0,117,275) scale(0.7,0.7) translate(20.1429,97.8572)" width="60" x="117" xlink:href="#Status:bn_不变化颜色显示_0" y="275"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,117,275) scale(0.7,0.7) translate(20.1429,97.8572)" width="60" x="117" y="275"/></g>
 <g id="126003717">
  <use class="kv-1" height="40" transform="rotate(0,192,275) scale(0.7,0.7) translate(52.2857,97.8572)" width="60" x="192" xlink:href="#Status:bn_越限颜色显示_0" y="275"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,192,275) scale(0.7,0.7) translate(52.2857,97.8572)" width="60" x="192" y="275"/></g>
 <g id="126003718">
  <use class="kv-1" height="40" transform="rotate(0,266,275) scale(0.7,0.7) translate(84,97.8572)" width="60" x="266" xlink:href="#Status:bn_非实测颜色显示_0" y="275"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,266,275) scale(0.7,0.7) translate(84,97.8572)" width="60" x="266" y="275"/></g>
 <g id="126003719">
  <use class="kv-1" height="40" transform="rotate(0,338,275) scale(0.7,0.7) translate(114.857,97.8572)" width="60" x="338" xlink:href="#Status:bn_数据封锁颜色显示_0" y="275"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,338,275) scale(0.7,0.7) translate(114.857,97.8572)" width="60" x="338" y="275"/></g>
</g>
<g id="Clock_Layer">
 <g id="56003685">
  
 <metadata/></g>
</g>
<g id="Term_Layer">
 <g id="112003993">
  <use class="kv-1" height="12" transform="rotate(90,1508,668) scale(1,1) translate(-17,-6)" width="38" x="1508" xlink:href="#Terminal:bn_终端设备10_0" y="668"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,1508,668) scale(1,1) translate(-17,-6)" width="38" x="1508" y="668"/></g>
 <g id="112003994">
  <use class="kv-1" height="12" transform="rotate(90,827,955) scale(1,1) translate(-17,-6)" width="38" x="827" xlink:href="#Terminal:bn_终端设备10_0" y="955"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,827,955) scale(1,1) translate(-17,-6)" width="38" x="827" y="955"/></g>
 <g id="112003995">
  <use class="kv-1" height="12" transform="rotate(90,1088,955) scale(1,1) translate(-17,-6)" width="38" x="1088" xlink:href="#Terminal:bn_终端设备10_0" y="955"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,1088,955) scale(1,1) translate(-17,-6)" width="38" x="1088" y="955"/></g>
 <g id="112003996">
  <use class="kv-1" height="12" transform="rotate(90,1349,955) scale(1,1) translate(-17,-6)" width="38" x="1349" xlink:href="#Terminal:bn_终端设备10_0" y="955"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,1349,955) scale(1,1) translate(-17,-6)" width="38" x="1349" y="955"/></g>
 <g id="112003997">
  <use class="kv-1" height="12" transform="rotate(90,1610,955) scale(1,1) translate(-17,-6)" width="38" x="1610" xlink:href="#Terminal:bn_终端设备10_0" y="955"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,1610,955) scale(1,1) translate(-17,-6)" width="38" x="1610" y="955"/></g>
 <g id="112003998">
  <use class="kv-1" height="12" transform="rotate(90,1872,958) scale(1,1) translate(-17,-6)" width="38" x="1872" xlink:href="#Terminal:bn_终端设备10_0" y="958"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,1872,958) scale(1,1) translate(-17,-6)" width="38" x="1872" y="958"/></g>
 <g id="112003999">
  <use class="kv-1" height="12" transform="rotate(90,2132,955) scale(1,1) translate(-17,-6)" width="38" x="2132" xlink:href="#Terminal:bn_终端设备10_0" y="955"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,2132,955) scale(1,1) translate(-17,-6)" width="38" x="2132" y="955"/></g>
</g>
<g id="Link_Layer">
 <g id="34003860">
 <path d="M 1508 520 L 1508 545" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100003949_1" Pin1InfoVect0LinkObjId="102003967_0" Plane="0"/>
  </metadata>
 <path d="M 1508 520 L 1508 545" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003861">
 <path d="M 1508 777 L 1508 798" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110003978_1" Pin1InfoVect0LinkObjId="30003825_0" Plane="0"/>
  </metadata>
 <path d="M 1508 777 L 1508 798" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003862">
 <path d="M 1477 472 L 1508 472" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111003979_0" Pin1InfoVect0LinkObjId="34003863_1" Pin1InfoVect1LinkObjId="34003864_0" Plane="0"/>
  </metadata>
 <path d="M 1477 472 L 1508 472" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003863">
 <path d="M 1508 451 L 1508 472" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101003952_1" Pin1InfoVect0LinkObjId="34003862_1" Pin1InfoVect1LinkObjId="34003864_0" Plane="0"/>
  </metadata>
 <path d="M 1508 451 L 1508 472" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003864">
 <path d="M 1508 472 L 1508 488" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34003862_1" Pin0InfoVect1LinkObjId="34003863_1" Pin1InfoVect0LinkObjId="100003949_0" Plane="0"/>
  </metadata>
 <path d="M 1508 472 L 1508 488" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003865">
 <path d="M 1479 319 L 1508 319" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111003980_0" Pin1InfoVect0LinkObjId="34004088_1" Pin1InfoVect1LinkObjId="34004089_0" Plane="0"/>
  </metadata>
 <path d="M 1479 319 L 1508 319" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003866">
 <path d="M 1508 419 L 1508 394" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101003952_0" Pin1InfoVect0LinkObjId="30003826_0" Plane="0"/>
  </metadata>
 <path d="M 1508 419 L 1508 394" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003867">
 <path d="M 1508 372 L 1508 394" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101003951_1" Pin1InfoVect0LinkObjId="30003826_0" Plane="0"/>
  </metadata>
 <path d="M 1508 372 L 1508 394" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003870">
 <path d="M 1479 254 L 1508 254" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111003981_0" Pin1InfoVect0LinkObjId="34004086_1" Pin1InfoVect1LinkObjId="34004087_0" Plane="0"/>
  </metadata>
 <path d="M 1479 254 L 1508 254" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003872">
 <path d="M 2132 829 L 2132 798" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101004023_0" Pin1InfoVect0LinkObjId="30003825_0" Plane="0"/>
  </metadata>
 <path d="M 2132 829 L 2132 798" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003873">
 <path d="M 2132 885 L 2132 994" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101004023_1" Pin1InfoVect0LinkObjId="102003966_0" Plane="0"/>
  </metadata>
 <path d="M 2132 885 L 2132 994" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003874">
 <path d="M 851 1096 L 851 1078 L 827 1078" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133004005_0" Pin1InfoVect0LinkObjId="34003876_0" Pin1InfoVect1LinkObjId="34003875_1" Plane="0"/>
  </metadata>
 <path d="M 851 1096 L 851 1078 L 827 1078" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003875">
 <path d="M 827 1047 L 827 1078" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101003955_1" Pin1InfoVect0LinkObjId="34003876_0" Pin1InfoVect1LinkObjId="34003874_1" Plane="0"/>
  </metadata>
 <path d="M 827 1047 L 827 1078" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003876">
 <path d="M 827 1078 L 827 1109" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34003874_1" Pin0InfoVect1LinkObjId="34003875_1" Pin1InfoVect0LinkObjId="32003827_0" Plane="0"/>
  </metadata>
 <path d="M 827 1078 L 827 1109" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003877">
 <path d="M 827 821 L 827 798" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110003973_0" Pin1InfoVect0LinkObjId="30003825_0" Plane="0"/>
  </metadata>
 <path d="M 827 821 L 827 798" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003878">
 <path d="M 827 1005 L 827 920" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101003955_0" Pin1InfoVect0LinkObjId="34003879_0" Pin1InfoVect1LinkObjId="34003880_1" Pin1InfoVect2LinkObjId="34003915_0" Plane="0"/>
  </metadata>
 <path d="M 827 1005 L 827 920" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003879">
 <path d="M 827 920 L 827 900" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34003878_1" Pin0InfoVect1LinkObjId="34003880_1" Pin0InfoVect2LinkObjId="34003915_0" Pin1InfoVect0LinkObjId="110003973_1" Plane="0"/>
  </metadata>
 <path d="M 827 920 L 827 900" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003880">
 <path d="M 797 920 L 827 920" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111003983_0" Pin1InfoVect0LinkObjId="34003879_0" Pin1InfoVect1LinkObjId="34003878_1" Pin1InfoVect2LinkObjId="34003915_0" Plane="0"/>
  </metadata>
 <path d="M 797 920 L 827 920" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003881">
 <path d="M 1112 1096 L 1112 1078 L 1088 1078" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133004007_0" Pin1InfoVect0LinkObjId="34003882_1" Pin1InfoVect1LinkObjId="34003883_0" Plane="0"/>
  </metadata>
 <path d="M 1112 1096 L 1112 1078 L 1088 1078" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003882">
 <path d="M 1088 1047 L 1088 1078" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101003956_1" Pin1InfoVect0LinkObjId="34003881_1" Pin1InfoVect1LinkObjId="34003883_0" Plane="0"/>
  </metadata>
 <path d="M 1088 1047 L 1088 1078" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003883">
 <path d="M 1088 1078 L 1088 1109" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34003881_1" Pin0InfoVect1LinkObjId="34003882_1" Pin1InfoVect0LinkObjId="32003828_0" Plane="0"/>
  </metadata>
 <path d="M 1088 1078 L 1088 1109" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003884">
 <path d="M 1088 821 L 1088 798" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110003974_0" Pin1InfoVect0LinkObjId="30003825_0" Plane="0"/>
  </metadata>
 <path d="M 1088 821 L 1088 798" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003885">
 <path d="M 1088 1005 L 1088 920" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101003956_0" Pin1InfoVect0LinkObjId="34003886_0" Pin1InfoVect1LinkObjId="34003887_1" Pin1InfoVect2LinkObjId="34003888_1" Plane="0"/>
  </metadata>
 <path d="M 1088 1005 L 1088 920" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003886">
 <path d="M 1088 920 L 1088 900" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34003885_1" Pin0InfoVect1LinkObjId="34003887_1" Pin0InfoVect2LinkObjId="34003888_1" Pin1InfoVect0LinkObjId="110003974_1" Plane="0"/>
  </metadata>
 <path d="M 1088 920 L 1088 900" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003887">
 <path d="M 1058 920 L 1088 920" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111003984_0" Pin1InfoVect0LinkObjId="34003885_1" Pin1InfoVect1LinkObjId="34003886_0" Pin1InfoVect2LinkObjId="34003888_1" Plane="0"/>
  </metadata>
 <path d="M 1058 920 L 1088 920" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003888">
 <path d="M 1115 920 L 1088 920" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133004008_0" Pin1InfoVect0LinkObjId="34003885_1" Pin1InfoVect1LinkObjId="34003886_0" Pin1InfoVect2LinkObjId="34003887_1" Plane="0"/>
  </metadata>
 <path d="M 1115 920 L 1088 920" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003896">
 <path d="M 1376 920 L 1349 920" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133004010_0" Pin1InfoVect0LinkObjId="34003894_0" Pin1InfoVect1LinkObjId="34003893_1" Pin1InfoVect2LinkObjId="34003895_1" Plane="0"/>
  </metadata>
 <path d="M 1376 920 L 1349 920" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003895">
 <path d="M 1319 920 L 1349 920" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111003985_0" Pin1InfoVect0LinkObjId="34003894_0" Pin1InfoVect1LinkObjId="34003893_1" Pin1InfoVect2LinkObjId="34003896_1" Plane="0"/>
  </metadata>
 <path d="M 1319 920 L 1349 920" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003894">
 <path d="M 1349 920 L 1349 900" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34003893_1" Pin0InfoVect1LinkObjId="34003895_1" Pin0InfoVect2LinkObjId="34003896_1" Pin1InfoVect0LinkObjId="110003975_1" Plane="0"/>
  </metadata>
 <path d="M 1349 920 L 1349 900" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003893">
 <path d="M 1349 1005 L 1349 920" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101003957_0" Pin1InfoVect0LinkObjId="34003894_0" Pin1InfoVect1LinkObjId="34003895_1" Pin1InfoVect2LinkObjId="34003896_1" Plane="0"/>
  </metadata>
 <path d="M 1349 1005 L 1349 920" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003892">
 <path d="M 1349 821 L 1349 798" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110003975_0" Pin1InfoVect0LinkObjId="30003825_0" Plane="0"/>
  </metadata>
 <path d="M 1349 821 L 1349 798" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003891">
 <path d="M 1349 1078 L 1349 1109" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34003889_1" Pin0InfoVect1LinkObjId="34003890_1" Pin1InfoVect0LinkObjId="32003829_0" Plane="0"/>
  </metadata>
 <path d="M 1349 1078 L 1349 1109" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003890">
 <path d="M 1349 1047 L 1349 1078" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101003957_1" Pin1InfoVect0LinkObjId="34003891_0" Pin1InfoVect1LinkObjId="34003889_1" Plane="0"/>
  </metadata>
 <path d="M 1349 1047 L 1349 1078" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003889">
 <path d="M 1373 1096 L 1373 1078 L 1349 1078" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133004009_0" Pin1InfoVect0LinkObjId="34003891_0" Pin1InfoVect1LinkObjId="34003890_1" Plane="0"/>
  </metadata>
 <path d="M 1373 1096 L 1373 1078 L 1349 1078" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003897">
 <path d="M 1634 1096 L 1634 1078 L 1610 1078" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133004011_0" Pin1InfoVect0LinkObjId="34003899_0" Pin1InfoVect1LinkObjId="34003898_1" Plane="0"/>
  </metadata>
 <path d="M 1634 1096 L 1634 1078 L 1610 1078" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003898">
 <path d="M 1610 1047 L 1610 1078" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101003958_1" Pin1InfoVect0LinkObjId="34003899_0" Pin1InfoVect1LinkObjId="34003897_1" Plane="0"/>
  </metadata>
 <path d="M 1610 1047 L 1610 1078" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003899">
 <path d="M 1610 1078 L 1610 1109" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34003897_1" Pin0InfoVect1LinkObjId="34003898_1" Pin1InfoVect0LinkObjId="32003830_0" Plane="0"/>
  </metadata>
 <path d="M 1610 1078 L 1610 1109" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003900">
 <path d="M 1610 821 L 1610 798" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110003976_0" Pin1InfoVect0LinkObjId="30003825_0" Plane="0"/>
  </metadata>
 <path d="M 1610 821 L 1610 798" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003901">
 <path d="M 1610 1005 L 1610 920" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101003958_0" Pin1InfoVect0LinkObjId="34003902_0" Pin1InfoVect1LinkObjId="34003903_1" Pin1InfoVect2LinkObjId="34003904_1" Plane="0"/>
  </metadata>
 <path d="M 1610 1005 L 1610 920" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003902">
 <path d="M 1610 920 L 1610 900" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34003901_1" Pin0InfoVect1LinkObjId="34003903_1" Pin0InfoVect2LinkObjId="34003904_1" Pin1InfoVect0LinkObjId="110003976_1" Plane="0"/>
  </metadata>
 <path d="M 1610 920 L 1610 900" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003903">
 <path d="M 1580 920 L 1610 920" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111003986_0" Pin1InfoVect0LinkObjId="34003901_1" Pin1InfoVect1LinkObjId="34003902_0" Pin1InfoVect2LinkObjId="34003904_1" Plane="0"/>
  </metadata>
 <path d="M 1580 920 L 1610 920" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003904">
 <path d="M 1637 920 L 1610 920" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133004012_0" Pin1InfoVect0LinkObjId="34003901_1" Pin1InfoVect1LinkObjId="34003902_0" Pin1InfoVect2LinkObjId="34003903_1" Plane="0"/>
  </metadata>
 <path d="M 1637 920 L 1610 920" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003905">
 <path d="M 1872 1047 L 1872 1081" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101003959_1" Pin1InfoVect0LinkObjId="34003909_0" Pin1InfoVect1LinkObjId="34003906_0" Plane="0"/>
  </metadata>
 <path d="M 1872 1047 L 1872 1081" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003906">
 <path d="M 1872 1081 L 1872 1127" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34003909_0" Pin0InfoVect1LinkObjId="34003905_1" Pin1InfoVect0LinkObjId="113004000_0" Plane="0"/>
  </metadata>
 <path d="M 1872 1081 L 1872 1127" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003907">
 <path d="M 1872 900 L 1872 920" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110003977_1" Pin1InfoVect0LinkObjId="34003913_1" Pin1InfoVect1LinkObjId="34003908_0" Pin1InfoVect2LinkObjId="34003914_1" Plane="0"/>
  </metadata>
 <path d="M 1872 900 L 1872 920" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003908">
 <path d="M 1872 920 L 1872 1005" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34003913_1" Pin0InfoVect1LinkObjId="34003907_1" Pin0InfoVect2LinkObjId="34003914_1" Pin1InfoVect0LinkObjId="101003959_0" Plane="0"/>
  </metadata>
 <path d="M 1872 920 L 1872 1005" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003909">
 <path d="M 1872 1081 L 1811 1081" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34003905_1" Pin0InfoVect1LinkObjId="34003906_0" Pin1InfoVect0LinkObjId="34003910_1" Pin1InfoVect1LinkObjId="34003911_0" Plane="0"/>
  </metadata>
 <path d="M 1872 1081 L 1811 1081" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003910">
 <path d="M 1872 1168 L 1811 1168 L 1811 1081" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="113004000_1" Pin1InfoVect0LinkObjId="34003909_1" Pin1InfoVect1LinkObjId="34003911_0" Plane="0"/>
  </metadata>
 <path d="M 1872 1168 L 1811 1168 L 1811 1081" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003911">
 <path d="M 1811 1081 L 1811 1062" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34003909_1" Pin0InfoVect1LinkObjId="34003910_1" Pin1InfoVect0LinkObjId="111003988_0" Plane="0"/>
  </metadata>
 <path d="M 1811 1081 L 1811 1062" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003912">
 <path d="M 1872 821 L 1872 798" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110003977_0" Pin1InfoVect0LinkObjId="30003825_0" Plane="0"/>
  </metadata>
 <path d="M 1872 821 L 1872 798" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003913">
 <path d="M 1842 920 L 1872 920" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111003987_0" Pin1InfoVect0LinkObjId="34003907_1" Pin1InfoVect1LinkObjId="34003908_0" Pin1InfoVect2LinkObjId="34003914_1" Plane="0"/>
  </metadata>
 <path d="M 1842 920 L 1872 920" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003914">
 <path d="M 1899 920 L 1872 920" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133004013_0" Pin1InfoVect0LinkObjId="34003913_1" Pin1InfoVect1LinkObjId="34003907_1" Pin1InfoVect2LinkObjId="34003908_0" Plane="0"/>
  </metadata>
 <path d="M 1899 920 L 1872 920" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003915">
 <path d="M 827 920 L 854 920" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34003879_0" Pin0InfoVect1LinkObjId="34003880_1" Pin0InfoVect2LinkObjId="34003878_1" Pin1InfoVect0LinkObjId="34003916_0" Pin1InfoVect1LinkObjId="34003918_0" Plane="0"/>
  </metadata>
 <path d="M 827 920 L 854 920" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003916">
 <path d="M 854 920 L 854 920 L 854 930" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34003915_1" Pin0InfoVect1LinkObjId="34003918_0" Pin1InfoVect0LinkObjId="133004006_0" Plane="0"/>
  </metadata>
 <path d="M 854 920 L 854 920 L 854 930" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003917">
 <path d="M 893 946 L 893 961" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="131004002_0" Pin1InfoVect0LinkObjId="107003969_0" Plane="0"/>
  </metadata>
 <path d="M 893 946 L 893 961" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003918">
 <path d="M 854 920 L 893 920" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34003916_0" Pin0InfoVect1LinkObjId="34003915_1" Pin1InfoVect0LinkObjId="131004002_1" Plane="0"/>
  </metadata>
 <path d="M 854 920 L 893 920" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003919">
 <path d="M 985 751 L 985 798" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101004025_1" Pin1InfoVect0LinkObjId="30003825_0" Plane="0"/>
  </metadata>
 <path d="M 985 751 L 985 798" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003920">
 <path d="M 985 667 L 985 695" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="107003970_0" Pin1InfoVect0LinkObjId="101004025_0" Plane="0"/>
  </metadata>
 <path d="M 985 667 L 985 695" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003921">
 <path d="M 1460 686 L 1508 686" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133004014_0" Pin1InfoVect0LinkObjId="34003922_1" Pin1InfoVect1LinkObjId="34003923_0" Plane="0"/>
  </metadata>
 <path d="M 1460 686 L 1508 686" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003922">
 <path d="M 1508 647 L 1508 686" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="102003967_1" Pin1InfoVect0LinkObjId="34003921_1" Pin1InfoVect1LinkObjId="34003923_0" Plane="0"/>
  </metadata>
 <path d="M 1508 647 L 1508 686" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003923">
 <path d="M 1508 686 L 1508 698" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34003921_1" Pin0InfoVect1LinkObjId="34003922_1" Pin1InfoVect0LinkObjId="110003978_0" Plane="0"/>
  </metadata>
 <path d="M 1508 686 L 1508 698" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003924">
 <path d="M 1147 530 L 1147 547" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="131004003_0" Pin1InfoVect0LinkObjId="102003968_0" Plane="0"/>
  </metadata>
 <path d="M 1147 530 L 1147 547" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003925">
 <path d="M 1147 419 L 1147 394" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101003961_0" Pin1InfoVect0LinkObjId="30003826_0" Plane="0"/>
  </metadata>
 <path d="M 1147 419 L 1147 394" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003926">
 <path d="M 1114 472 L 1147 472" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111003989_0" Pin1InfoVect0LinkObjId="34003927_1" Pin1InfoVect1LinkObjId="34003928_0" Plane="0"/>
  </metadata>
 <path d="M 1114 472 L 1147 472" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003927">
 <path d="M 1147 451 L 1147 472" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101003961_1" Pin1InfoVect0LinkObjId="34003926_1" Pin1InfoVect1LinkObjId="34003928_0" Plane="0"/>
  </metadata>
 <path d="M 1147 451 L 1147 472" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003928">
 <path d="M 1147 472 L 1147 496" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34003926_1" Pin0InfoVect1LinkObjId="34003927_1" Pin1InfoVect0LinkObjId="131004003_1" Plane="0"/>
  </metadata>
 <path d="M 1147 472 L 1147 496" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003929">
 <path d="M 1917 455 L 1917 418" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101003962_0" Pin1InfoVect0LinkObjId="34003933_1" Pin1InfoVect1LinkObjId="34003930_0" Plane="0"/>
  </metadata>
 <path d="M 1917 455 L 1917 418" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003930">
 <path d="M 1917 418 L 1917 394" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34003933_1" Pin0InfoVect1LinkObjId="34003929_1" Pin1InfoVect0LinkObjId="30003826_0" Plane="0"/>
  </metadata>
 <path d="M 1917 418 L 1917 394" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003932">
 <path d="M 1917 512 L 1917 487" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34003934_1" Pin0InfoVect1LinkObjId="34004091_1" Pin1InfoVect0LinkObjId="101003962_1" Plane="0"/>
  </metadata>
 <path d="M 1917 512 L 1917 487" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003933">
 <path d="M 1968 418 L 1917 418" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111003990_0" Pin1InfoVect0LinkObjId="34003929_1" Pin1InfoVect1LinkObjId="34003930_0" Plane="0"/>
  </metadata>
 <path d="M 1968 418 L 1917 418" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003934">
 <path d="M 1968 512 L 1917 512" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111003991_0" Pin1InfoVect0LinkObjId="34003932_0" Pin1InfoVect1LinkObjId="34004091_1" Plane="0"/>
  </metadata>
 <path d="M 1968 512 L 1917 512" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003935">
 <path d="M 1917 531 L 1882 531 L 1882 546" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34004090_1" Pin0InfoVect1LinkObjId="34004091_0" Pin1InfoVect0LinkObjId="133004015_0" Plane="0"/>
  </metadata>
 <path d="M 1917 531 L 1882 531 L 1882 546" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003936">
 <path d="M 2193 731 L 2193 798" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101004017_1" Pin1InfoVect0LinkObjId="30003825_0" Plane="0"/>
  </metadata>
 <path d="M 2193 731 L 2193 798" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003937">
 <path d="M 2193 675 L 2193 632 L 2193 633 L 2409 633" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101004017_0" Plane="0"/>
  </metadata>
 <path d="M 2193 675 L 2193 632 L 2193 633 L 2409 633" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003938">
 <path d="M 2194 356 L 2194 394" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101003964_1" Pin1InfoVect0LinkObjId="30003826_0" Plane="0"/>
  </metadata>
 <path d="M 2194 356 L 2194 394" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003939">
 <path d="M 2214 251 L 2214 270" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111003992_0" Pin1InfoVect0LinkObjId="34003941_0" Pin1InfoVect1LinkObjId="34003940_1" Plane="0"/>
  </metadata>
 <path d="M 2214 251 L 2214 270" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003940">
 <path d="M 2194 324 L 2194 269 L 2194 270 L 2214 270" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101003964_0" Pin1InfoVect0LinkObjId="34003941_0" Pin1InfoVect1LinkObjId="34003939_1" Plane="0"/>
  </metadata>
 <path d="M 2194 324 L 2194 269 L 2194 270 L 2214 270" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003941">
 <path d="M 2214 270 L 2411 270" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34003939_1" Pin0InfoVect1LinkObjId="34003940_1" Plane="0"/>
  </metadata>
 <path d="M 2214 270 L 2411 270" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003945">
 <path d="M 1572 181 L 1600 181" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101003965_1" Pin1InfoVect0LinkObjId="131004004_1" Plane="0"/>
  </metadata>
 <path d="M 1572 181 L 1600 181" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003946">
 <path d="M 1626 181 L 1642 181" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="131004004_0" Pin1InfoVect0LinkObjId="107003972_0" Plane="0"/>
  </metadata>
 <path d="M 1626 181 L 1642 181" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004086">
 <path d="M 1508 232 L 1508 254" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101003953_1" Pin1InfoVect0LinkObjId="34003870_1" Pin1InfoVect1LinkObjId="34004087_0" Plane="0"/>
  </metadata>
 <path d="M 1508 232 L 1508 254" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004087">
 <path d="M 1508 254 L 1508 272" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34004086_1" Pin0InfoVect1LinkObjId="34003870_1" Pin1InfoVect0LinkObjId="100003950_0" Plane="0"/>
  </metadata>
 <path d="M 1508 254 L 1508 272" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004088">
 <path d="M 1508 340 L 1508 319" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101003951_0" Pin1InfoVect0LinkObjId="34003865_1" Pin1InfoVect1LinkObjId="34004089_0" Plane="0"/>
  </metadata>
 <path d="M 1508 340 L 1508 319" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004089">
 <path d="M 1508 319 L 1508 304" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34004088_1" Pin0InfoVect1LinkObjId="34003865_1" Pin1InfoVect0LinkObjId="100003950_1" Plane="0"/>
  </metadata>
 <path d="M 1508 319 L 1508 304" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004090">
 <path d="M 1917 553 L 1917 531" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="107003971_0" Pin1InfoVect0LinkObjId="34003935_0" Pin1InfoVect1LinkObjId="34004091_0" Plane="0"/>
  </metadata>
 <path d="M 1917 553 L 1917 531" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004091">
 <path d="M 1917 531 L 1917 512" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34004090_1" Pin0InfoVect1LinkObjId="34003935_0" Pin1InfoVect0LinkObjId="34003932_0" Pin1InfoVect1LinkObjId="34003934_1" Plane="0"/>
  </metadata>
 <path d="M 1917 531 L 1917 512" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004093">
 <path d="M 1479 181 L 1508 181" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111003982_0" Pin1InfoVect0LinkObjId="34004094_1" Pin1InfoVect1LinkObjId="34004095_0" Plane="0"/>
  </metadata>
 <path d="M 1479 181 L 1508 181" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004094">
 <path d="M 1508 156 L 1508 181" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34004096_1" Pin0InfoVect1LinkObjId="36003948_0" Pin1InfoVect0LinkObjId="34004093_1" Pin1InfoVect1LinkObjId="34004095_0" Plane="0"/>
  </metadata>
 <path d="M 1508 156 L 1508 181" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004095">
 <path d="M 1508 181 L 1508 200" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34004093_1" Pin0InfoVect1LinkObjId="34004094_1" Pin1InfoVect0LinkObjId="101003953_0" Plane="0"/>
  </metadata>
 <path d="M 1508 181 L 1508 200" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004096">
 <path d="M 1460 156 L 1508 156" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133004016_0" Pin1InfoVect0LinkObjId="34004094_0" Pin1InfoVect1LinkObjId="36003948_0" Plane="0"/>
  </metadata>
 <path d="M 1460 156 L 1508 156" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="MeasurementValue_Layer">
 <g id="33003680">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="237" xml:space="preserve" y="726">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33003679">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="237" xml:space="preserve" y="646">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33003681">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="249" xml:space="preserve" y="878">-00000</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33003682">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="249" xml:space="preserve" y="953">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33003836">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="805" xml:space="preserve" y="1205">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967862203483921" ObjectName="115967690404792081:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33003837">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="805" xml:space="preserve" y="1231">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967905153156881" ObjectName="115967690404792081:Q" Plane="0"/>
  </metadata>
 </g>
 <g id="33003838">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="805" xml:space="preserve" y="1257">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115968034002175761" ObjectName="115967690404792081:I_B" Plane="0"/>
  </metadata>
 </g>
 <g id="33003839">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1066" xml:space="preserve" y="1205">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967862203483922" ObjectName="115967690404792082:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33003840">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1066" xml:space="preserve" y="1231">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967905153156882" ObjectName="115967690404792082:Q" Plane="0"/>
  </metadata>
 </g>
 <g id="33003841">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1066" xml:space="preserve" y="1257">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115968034002175762" ObjectName="115967690404792082:I_B" Plane="0"/>
  </metadata>
 </g>
 <g id="33003842">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1327" xml:space="preserve" y="1205">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967862203483923" ObjectName="115967690404792083:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33003843">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1327" xml:space="preserve" y="1231">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967905153156883" ObjectName="115967690404792083:Q" Plane="0"/>
  </metadata>
 </g>
 <g id="33003844">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1327" xml:space="preserve" y="1257">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115968034002175763" ObjectName="115967690404792083:I_B" Plane="0"/>
  </metadata>
 </g>
 <g id="33003845">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1588" xml:space="preserve" y="1205">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967862203483924" ObjectName="115967690404792084:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33003846">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1588" xml:space="preserve" y="1231">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967905153156884" ObjectName="115967690404792084:Q" Plane="0"/>
  </metadata>
 </g>
 <g id="33003847">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1588" xml:space="preserve" y="1257">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115968034002175764" ObjectName="115967690404792084:I_B" Plane="0"/>
  </metadata>
 </g>
 <g id="33003848">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1837" xml:space="preserve" y="1231">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33003849">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1837" xml:space="preserve" y="1257">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33003850">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1626" xml:space="preserve" y="478">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709542" ObjectName="117375065288344742:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33003852">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1626" xml:space="preserve" y="528">mmmmmm</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375108238017702" ObjectName="117375065288344742:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33003853">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1626" xml:space="preserve" y="725">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709543" ObjectName="117375065288344743:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33003854">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1626" xml:space="preserve" y="751">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375322986382503" ObjectName="117375065288344743:Q" Plane="0"/>
  </metadata>
 </g>
 <g id="33003855">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1626" xml:space="preserve" y="777">mmmmmm</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375108238017703" ObjectName="117375065288344743:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33003856">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1626" xml:space="preserve" y="554">mmmmmm</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375108238017702" ObjectName="117375065288344742:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33003857">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1487" xml:space="preserve" y="71">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33003858">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1487" xml:space="preserve" y="98">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33003859">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1487" xml:space="preserve" y="125">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33004047">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="853" xml:space="preserve" y="274">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115405084048753526" ObjectName="115404740451369846:V_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33004048">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="853" xml:space="preserve" y="208">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115404869300388726" ObjectName="115404740451369846:V" Plane="0"/>
  </metadata>
 </g>
 <g id="33004050">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="853" xml:space="preserve" y="296">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115405126998426486" ObjectName="115404740451369846:V_B" Plane="0"/>
  </metadata>
 </g>
 <g id="33004051">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="853" xml:space="preserve" y="319">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115405169948099446" ObjectName="115404740451369846:V_C" Plane="0"/>
  </metadata>
 </g>
 <g id="33004054">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="853" xml:space="preserve" y="229">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115404998149407606" ObjectName="115404740451369846:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33004057">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="853" xml:space="preserve" y="251">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115405041099080566" ObjectName="115404740451369846:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33004068">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="726" xml:space="preserve" y="671">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115405084048753527" ObjectName="115404740451369847:V_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33004069">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="726" xml:space="preserve" y="605">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115404869300388727" ObjectName="115404740451369847:V" Plane="0"/>
  </metadata>
 </g>
 <g id="33004071">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="726" xml:space="preserve" y="693">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115405126998426487" ObjectName="115404740451369847:V_B" Plane="0"/>
  </metadata>
 </g>
 <g id="33004072">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="726" xml:space="preserve" y="716">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115405169948099447" ObjectName="115404740451369847:V_C" Plane="0"/>
  </metadata>
 </g>
 <g id="33004073">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="726" xml:space="preserve" y="626">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115404998149407607" ObjectName="115404740451369847:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33004074">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="726" xml:space="preserve" y="648">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115405041099080567" ObjectName="115404740451369847:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33004076">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1626" xml:space="preserve" y="503">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375322986382502" ObjectName="117375065288344742:Q" Plane="0"/>
  </metadata>
 </g>
 <g id="33004083">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="249" xml:space="preserve" y="1021">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33004084">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="249" xml:space="preserve" y="1086">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
</g>
<g id="Text_Layer">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="1595" xml:space="preserve" y="777">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="1595" xml:space="preserve" y="725">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="1595" xml:space="preserve" y="751">Q</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,254)" writing-mode="lr" x="100" xml:space="preserve" y="798">事故总:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,254)" writing-mode="lr" x="52" xml:space="preserve" y="646">总有功</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="152" xml:space="preserve" y="878">档位</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="30" font-size="30" font-width="30" stroke="rgb(255,255,254)" writing-mode="tb" x="55" xml:space="preserve" y="830">一</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="149" xml:space="preserve" y="953">油温</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(170,0,0)" writing-mode="lr" x="179" xml:space="preserve" y="424">MW</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="108" xml:space="preserve" y="424">有功:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="229" xml:space="preserve" y="424">无功:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(170,0,0)" writing-mode="lr" x="305" xml:space="preserve" y="424">MVr</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="108" xml:space="preserve" y="499">电流:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(170,0,0)" writing-mode="lr" x="179" xml:space="preserve" y="499">A</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="229" xml:space="preserve" y="499">电压:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(170,0,0)" writing-mode="lr" x="305" xml:space="preserve" y="499">kV</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="108" xml:space="preserve" y="574">频率:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(170,0,0)" writing-mode="lr" x="179" xml:space="preserve" y="574">Hz</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="229" xml:space="preserve" y="574">温度:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(170,0,0)" writing-mode="lr" x="305" xml:space="preserve" y="574">℃</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,254)" writing-mode="lr" x="52" xml:space="preserve" y="724">总无功</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="30" font-size="30" font-width="30" stroke="rgb(255,255,254)" writing-mode="tb" x="55" xml:space="preserve" y="416">单</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="72" xml:space="preserve" y="1195">联系方式：</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="216" xml:space="preserve" y="1195">5626900</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="30" font-size="30" font-width="30" stroke="rgb(255,255,254)" writing-mode="tb" x="54" xml:space="preserve" y="512">位</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="30" font-size="30" font-width="30" stroke="rgb(255,255,254)" writing-mode="tb" x="55" xml:space="preserve" y="858">号</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="30" font-size="30" font-width="30" stroke="rgb(255,255,254)" writing-mode="tb" x="55" xml:space="preserve" y="893">主</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="30" font-size="30" font-width="30" stroke="rgb(255,255,254)" writing-mode="tb" x="55" xml:space="preserve" y="929">变</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="66" font-size="35" font-width="35" stroke="rgb(0,0,0)" writing-mode="lr" x="105" xml:space="preserve" y="118">版纳_35kV_测试</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="20" xml:space="preserve" y="336">工况</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="20" xml:space="preserve" y="359">退出</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="107" xml:space="preserve" y="336">不</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="95" xml:space="preserve" y="362">变化</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="167" xml:space="preserve" y="350">越限</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="255" xml:space="preserve" y="336">非</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="243" xml:space="preserve" y="362">实测</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="314" xml:space="preserve" y="336">数据</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="314" xml:space="preserve" y="359">封锁</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="667" xml:space="preserve" y="794">10kV I母_测试</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1522" xml:space="preserve" y="370">3511</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1522" xml:space="preserve" y="519">301</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1522" xml:space="preserve" y="449">3011</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1411" xml:space="preserve" y="509">30117</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1413" xml:space="preserve" y="356">35117</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="1595" xml:space="preserve" y="478">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="1595" xml:space="preserve" y="503">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="1595" xml:space="preserve" y="528">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1556" xml:space="preserve" y="602"> 1号主变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1556" xml:space="preserve" y="630">(S＝5MVA)</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="1595" xml:space="preserve" y="554">Cs</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="800" xml:space="preserve" y="390">35kV I母_测试</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1522" xml:space="preserve" y="303">351</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1522" xml:space="preserve" y="230">3516</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1413" xml:space="preserve" y="291">35162</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1413" xml:space="preserve" y="218">35167</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="24" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="2077" xml:space="preserve" y="1144">2号站用变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="844" xml:space="preserve" y="1043">0516</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="723" xml:space="preserve" y="962">05167</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="844" xml:space="preserve" y="875">051</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="718" xml:space="preserve" y="1296">天往南联络线_测试</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1105" xml:space="preserve" y="1043">0526</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="984" xml:space="preserve" y="962">05267</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1105" xml:space="preserve" y="875">052</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="996" xml:space="preserve" y="1296">10kV勐往街道线_测试</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="1295" xml:space="preserve" y="1296">10kV曼允线_测试</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1245" xml:space="preserve" y="962">05367</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1366" xml:space="preserve" y="1043">0536</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1366" xml:space="preserve" y="875">053</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1627" xml:space="preserve" y="875">054</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1627" xml:space="preserve" y="1043">0546</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1506" xml:space="preserve" y="962">05467</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="1556" xml:space="preserve" y="1296">10kV灰塘线_测试</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1888" xml:space="preserve" y="1043">0556</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1767" xml:space="preserve" y="962">05517</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="1771" xml:space="preserve" y="1296">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1776" xml:space="preserve" y="1027">05567</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1888" xml:space="preserve" y="875">055</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="1816" xml:space="preserve" y="1230">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="1816" xml:space="preserve" y="1256">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="24" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="933" xml:space="preserve" y="558">10kV电压互感器2_测试</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1525" xml:space="preserve" y="752">001</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1161" xml:space="preserve" y="449">3521</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1048" xml:space="preserve" y="509">35217</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1931" xml:space="preserve" y="486">3901</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="24" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="1862" xml:space="preserve" y="674">35kV电压互感器2_测试</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1953" xml:space="preserve" y="459">39010</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1953" xml:space="preserve" y="549">39017</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2208" xml:space="preserve" y="354">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2235" xml:space="preserve" y="253">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="34" font-size="34" font-width="34" stroke="rgb(230,232,254)" writing-mode="lr" x="1442" xml:space="preserve" y="40">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1528" xml:space="preserve" y="163">3519</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2203" xml:space="preserve" y="719">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2142" xml:space="preserve" y="875">0561</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="995" xml:space="preserve" y="738">0901</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="24" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="1092" xml:space="preserve" y="692">1号站用变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="1465" xml:space="preserve" y="98">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="1465" xml:space="preserve" y="71">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="1465" xml:space="preserve" y="125">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="1046" xml:space="preserve" y="1205">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="1046" xml:space="preserve" y="1231">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="1046" xml:space="preserve" y="1257">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="1308" xml:space="preserve" y="1205">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="1308" xml:space="preserve" y="1231">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="1308" xml:space="preserve" y="1257">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="1569" xml:space="preserve" y="1257">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="1569" xml:space="preserve" y="1231">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="1569" xml:space="preserve" y="1205">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="785" xml:space="preserve" y="1205">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="785" xml:space="preserve" y="1231">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="785" xml:space="preserve" y="1257">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(36,169,128)" writing-mode="lr" x="798" xml:space="preserve" y="208">Uab</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(36,169,128)" writing-mode="lr" x="798" xml:space="preserve" y="274">Ua</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(36,169,128)" writing-mode="lr" x="798" xml:space="preserve" y="296">Ub</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(36,169,128)" writing-mode="lr" x="798" xml:space="preserve" y="319">Uc</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(36,169,128)" writing-mode="lr" x="798" xml:space="preserve" y="229">Ubc</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(36,169,128)" writing-mode="lr" x="798" xml:space="preserve" y="251">Uca</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(36,169,128)" writing-mode="lr" x="671" xml:space="preserve" y="605">Uab</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(36,169,128)" writing-mode="lr" x="671" xml:space="preserve" y="671">Ua</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(36,169,128)" writing-mode="lr" x="671" xml:space="preserve" y="693">Ub</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(36,169,128)" writing-mode="lr" x="671" xml:space="preserve" y="716">Uc</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(36,169,128)" writing-mode="lr" x="671" xml:space="preserve" y="626">Ubc</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(36,169,128)" writing-mode="lr" x="671" xml:space="preserve" y="648">Uca</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="70" xml:space="preserve" y="1021">合母电压</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="70" xml:space="preserve" y="1086">控母电压</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href="勐海地区.sys.svg"><rect fill-opacity="0" height="98" stroke-opacity="0" stroke-width="1" width="341" x="24" y="32"/></g>
</g>
</svg>