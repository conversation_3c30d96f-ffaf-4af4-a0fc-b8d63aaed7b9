<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="sys" height="1020" id="thSvg" viewBox="0 0 2120 1020" width="2120">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="Protect:bn_保护图元1_0" viewBox="0,0,18,18">
 <rect AFMask="2147483647" Plane="0" fill="none" height="13" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,8,8)" width="13" x="2" y="2"/>
</symbol>
<symbol id="Protect:bn_保护图元1_1" viewBox="0,0,18,18">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="13" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,8,8)" width="13" x="2" y="2"/>
</symbol>
<symbol id="Protect:软压板投退_0" viewBox="0,0,50,50">
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,0,0)" stroke-width="3" x1="40" x2="13" y1="31" y2="4"/>
 <circle AFMask="2147483647" Plane="0" cx="9" cy="31" fill="none" r="4" stroke="rgb(255,0,0)" stroke-width="8"/>
 <circle AFMask="2147483647" Plane="0" cx="40" cy="31" fill="none" r="4" stroke="rgb(255,0,0)" stroke-width="8"/>
</symbol>
<symbol id="Protect:软压板投退_1" viewBox="0,0,50,50">
 <circle AFMask="2147483647" Plane="0" cx="9" cy="24" fill="none" r="4" stroke="rgb(0,255,0)" stroke-width="8"/>
 <circle AFMask="2147483647" Plane="0" cx="40" cy="24" fill="none" r="4" stroke="rgb(0,255,0)" stroke-width="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,255,0)" stroke-width="3" x1="9" x2="41" y1="24" y2="24"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="-1" InitShowingPlane="0," fill="rgb(0,0,0)" height="1020" width="2120" x="0" y="0"/>
</g>
<g id="Other_Layer">
 
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="355" x2="954" y1="125" y2="125"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="356" x2="954" y1="155" y2="155"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="355" x2="954" y1="185" y2="184"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="354" x2="354" y1="126" y2="634"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="520" x2="520" y1="125" y2="634"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="954" x2="954" y1="125" y2="633"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="813" x2="813" y1="127" y2="636"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="355" x2="954" y1="215" y2="216"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="520" x2="954" y1="245" y2="245"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="520" x2="954" y1="275" y2="275"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="519" x2="954" y1="305" y2="305"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="520" x2="954" y1="335" y2="334"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="520" x2="954" y1="365" y2="366"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="520" x2="954" y1="395" y2="395"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="355" x2="954" y1="425" y2="425"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="520" x2="954" y1="455" y2="455"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="520" x2="954" y1="485" y2="484"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="520" x2="954" y1="515" y2="516"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="521" x2="954" y1="545" y2="545"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="520" x2="954" y1="575" y2="575"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="520" x2="954" y1="605" y2="605"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="356" x2="954" y1="635" y2="634"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1078" x2="1656" y1="125" y2="125"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1077" x2="1656" y1="155" y2="155"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1079" x2="1656" y1="185" y2="184"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1077" x2="1077" y1="125" y2="573"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1285" x2="1285" y1="125" y2="577"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1658" x2="1658" y1="125" y2="575"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1573" x2="1573" y1="127" y2="572"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1079" x2="1656" y1="215" y2="216"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1285" x2="1656" y1="245" y2="245"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1285" x2="1656" y1="275" y2="275"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1285" x2="1656" y1="305" y2="305"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1285" x2="1656" y1="334" y2="334"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1079" x2="1656" y1="365" y2="366"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1285" x2="1656" y1="395" y2="395"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1285" x2="1656" y1="425" y2="425"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1285" x2="1656" y1="455" y2="455"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1285" x2="1656" y1="484" y2="484"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1078" x2="1656" y1="516" y2="516"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1285" x2="1656" y1="545" y2="545"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1077" x2="1657" y1="575" y2="575"/>
</g>
<g id="Protect_Layer">
 <g id="127000570">
  <use class="kv35kV" height="50" transform="rotate(0,884,169) scale(0.6,0.6) translate(564.333,91.6667)" width="50" x="884" xlink:href="#Protect:软压板投退_0" y="169"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892516939" ObjectName="122160139892516939" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,884,169) scale(0.6,0.6) translate(564.333,91.6667)" width="50" x="884" y="169"/></g>
 <g id="127000571">
  <use class="kv10kV" height="50" transform="rotate(0,884,199) scale(0.6,0.6) translate(564.333,111.667)" width="50" x="884" xlink:href="#Protect:软压板投退_0" y="199"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892516989" ObjectName="122160139892516989" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,884,199) scale(0.6,0.6) translate(564.333,111.667)" width="50" x="884" y="199"/></g>
 <g id="127000572">
  <use class="kv35kV" height="50" transform="rotate(0,884,229) scale(0.6,0.6) translate(564.333,131.667)" width="50" x="884" xlink:href="#Protect:软压板投退_0" y="229"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892517810" ObjectName="122160139892517810" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,884,229) scale(0.6,0.6) translate(564.333,131.667)" width="50" x="884" y="229"/></g>
 <g id="127000573">
  <use class="kv35kV" height="50" transform="rotate(0,884,259) scale(0.6,0.6) translate(564.333,151.667)" width="50" x="884" xlink:href="#Protect:软压板投退_0" y="259"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892517811" ObjectName="122160139892517811" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,884,259) scale(0.6,0.6) translate(564.333,151.667)" width="50" x="884" y="259"/></g>
 <g id="127000574">
  <use class="kv35kV" height="50" transform="rotate(0,884,289) scale(0.6,0.6) translate(564.333,171.667)" width="50" x="884" xlink:href="#Protect:软压板投退_0" y="289"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892517804" ObjectName="122160139892517804" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,884,289) scale(0.6,0.6) translate(564.333,171.667)" width="50" x="884" y="289"/></g>
 <g id="127000575">
  <use class="kv35kV" height="50" transform="rotate(0,884,319) scale(0.6,0.6) translate(564.333,191.667)" width="50" x="884" xlink:href="#Protect:软压板投退_0" y="319"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892517805" ObjectName="122160139892517805" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,884,319) scale(0.6,0.6) translate(564.333,191.667)" width="50" x="884" y="319"/></g>
 <g id="127000576">
  <use class="kv35kV" height="50" transform="rotate(0,884,349) scale(0.6,0.6) translate(564.333,211.667)" width="50" x="884" xlink:href="#Protect:软压板投退_0" y="349"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892517125" ObjectName="122160139892517125" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,884,349) scale(0.6,0.6) translate(564.333,211.667)" width="50" x="884" y="349"/></g>
 <g id="127000577">
  <use class="kv10kV" height="50" transform="rotate(0,884,379) scale(0.6,0.6) translate(564.333,231.667)" width="50" x="884" xlink:href="#Protect:软压板投退_0" y="379"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892517806" ObjectName="122160139892517806" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,884,379) scale(0.6,0.6) translate(564.333,231.667)" width="50" x="884" y="379"/></g>
 <g id="127000578">
  <use class="kv10kV" height="50" transform="rotate(0,884,409) scale(0.6,0.6) translate(564.333,251.667)" width="50" x="884" xlink:href="#Protect:软压板投退_0" y="409"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892517145" ObjectName="122160139892517145" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,884,409) scale(0.6,0.6) translate(564.333,251.667)" width="50" x="884" y="409"/></g>
 <g id="127000579">
  <use class="kv35kV" height="50" transform="rotate(0,884,439) scale(0.6,0.6) translate(564.333,271.667)" width="50" x="884" xlink:href="#Protect:软压板投退_0" y="439"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892517147" ObjectName="122160139892517147" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,884,439) scale(0.6,0.6) translate(564.333,271.667)" width="50" x="884" y="439"/></g>
 <g id="127000580">
  <use class="kv35kV" height="50" transform="rotate(0,884,469) scale(0.6,0.6) translate(564.333,291.667)" width="50" x="884" xlink:href="#Protect:软压板投退_0" y="469"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892517148" ObjectName="122160139892517148" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,884,469) scale(0.6,0.6) translate(564.333,291.667)" width="50" x="884" y="469"/></g>
 <g id="127000581">
  <use class="kv35kV" height="50" transform="rotate(0,884,499) scale(0.6,0.6) translate(564.333,311.667)" width="50" x="884" xlink:href="#Protect:软压板投退_0" y="499"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892517807" ObjectName="122160139892517807" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,884,499) scale(0.6,0.6) translate(564.333,311.667)" width="50" x="884" y="499"/></g>
 <g id="127000582">
  <use class="kv35kV" height="50" transform="rotate(0,884,529) scale(0.6,0.6) translate(564.333,331.667)" width="50" x="884" xlink:href="#Protect:软压板投退_0" y="529"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892517808" ObjectName="122160139892517808" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,884,529) scale(0.6,0.6) translate(564.333,331.667)" width="50" x="884" y="529"/></g>
 <g id="127000585">
  <use class="kv10kV" height="50" transform="rotate(0,884,621) scale(0.6,0.6) translate(564.333,393)" width="50" x="884" xlink:href="#Protect:软压板投退_0" y="621"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892517254" ObjectName="122160139892517254" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,884,621) scale(0.6,0.6) translate(564.333,393)" width="50" x="884" y="621"/></g>
 <g id="127000584">
  <use class="kv10kV" height="50" transform="rotate(0,884,591) scale(0.6,0.6) translate(564.333,373)" width="50" x="884" xlink:href="#Protect:软压板投退_0" y="591"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892517809" ObjectName="122160139892517809" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,884,591) scale(0.6,0.6) translate(564.333,373)" width="50" x="884" y="591"/></g>
 <g id="127000583">
  <use class="kv35kV" height="50" transform="rotate(0,884,561) scale(0.6,0.6) translate(564.333,353)" width="50" x="884" xlink:href="#Protect:软压板投退_0" y="561"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892517233" ObjectName="122160139892517233" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,884,561) scale(0.6,0.6) translate(564.333,353)" width="50" x="884" y="561"/></g>
 <g id="127000588">
  <use class="kv10kV" height="50" transform="rotate(0,1619,525) scale(0.6,0.6) translate(1054.33,329)" width="50" x="1619" xlink:href="#Protect:软压板投退_0" y="525"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892517711" ObjectName="122160139892517711" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1619,525) scale(0.6,0.6) translate(1054.33,329)" width="50" x="1619" y="525"/></g>
 <g id="127000619">
  <use class="kv-1" height="18" transform="rotate(0,1619,171) scale(1.7,1.7) translate(-675.647,-79.4118)" width="18" x="1619" xlink:href="#Protect:bn_保护图元1_0" y="171"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1619,171) scale(1.7,1.7) translate(-675.647,-79.4118)" width="18" x="1619" y="171"/></g>
 <g id="127000620">
  <use class="kv-1" height="18" transform="rotate(0,1619,201) scale(1.7,1.7) translate(-675.647,-91.7647)" width="18" x="1619" xlink:href="#Protect:bn_保护图元1_0" y="201"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1619,201) scale(1.7,1.7) translate(-675.647,-91.7647)" width="18" x="1619" y="201"/></g>
 <g id="127000621">
  <use class="kv35kV" height="18" transform="rotate(0,1619,231) scale(1.7,1.7) translate(-675.647,-104.118)" width="18" x="1619" xlink:href="#Protect:bn_保护图元1_0" y="231"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892517096" ObjectName="122160139892517096" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1619,231) scale(1.7,1.7) translate(-675.647,-104.118)" width="18" x="1619" y="231"/></g>
 <g id="127000622">
  <use class="kv35kV" height="18" transform="rotate(0,1619,261) scale(1.7,1.7) translate(-675.647,-116.471)" width="18" x="1619" xlink:href="#Protect:bn_保护图元1_0" y="261"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892517104" ObjectName="122160139892517104" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1619,261) scale(1.7,1.7) translate(-675.647,-116.471)" width="18" x="1619" y="261"/></g>
 <g id="127000623">
  <use class="kv35kV" height="18" transform="rotate(0,1619,291) scale(1.7,1.7) translate(-675.647,-128.824)" width="18" x="1619" xlink:href="#Protect:bn_保护图元1_0" y="291"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892517122" ObjectName="122160139892517122" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1619,291) scale(1.7,1.7) translate(-675.647,-128.824)" width="18" x="1619" y="291"/></g>
 <g id="127000624">
  <use class="kv10kV" height="18" transform="rotate(0,1619,321) scale(1.7,1.7) translate(-675.647,-141.176)" width="18" x="1619" xlink:href="#Protect:bn_保护图元1_0" y="321"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892517141" ObjectName="122160139892517141" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1619,321) scale(1.7,1.7) translate(-675.647,-141.176)" width="18" x="1619" y="321"/></g>
 <g id="127000625">
  <use class="kv-1" height="18" transform="rotate(0,1619,351) scale(1.7,1.7) translate(-675.647,-153.529)" width="18" x="1619" xlink:href="#Protect:bn_保护图元1_0" y="351"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1619,351) scale(1.7,1.7) translate(-675.647,-153.529)" width="18" x="1619" y="351"/></g>
 <g id="127000626">
  <use class="kv35kV" height="18" transform="rotate(0,1619,381) scale(1.7,1.7) translate(-675.647,-165.882)" width="18" x="1619" xlink:href="#Protect:bn_保护图元1_0" y="381"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892517203" ObjectName="122160139892517203" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1619,381) scale(1.7,1.7) translate(-675.647,-165.882)" width="18" x="1619" y="381"/></g>
 <g id="127000627">
  <use class="kv35kV" height="18" transform="rotate(0,1619,411) scale(1.7,1.7) translate(-675.647,-178.235)" width="18" x="1619" xlink:href="#Protect:bn_保护图元1_0" y="411"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892517211" ObjectName="122160139892517211" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1619,411) scale(1.7,1.7) translate(-675.647,-178.235)" width="18" x="1619" y="411"/></g>
 <g id="127000628">
  <use class="kv35kV" height="18" transform="rotate(0,1619,441) scale(1.7,1.7) translate(-675.647,-190.588)" width="18" x="1619" xlink:href="#Protect:bn_保护图元1_0" y="441"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892517229" ObjectName="122160139892517229" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1619,441) scale(1.7,1.7) translate(-675.647,-190.588)" width="18" x="1619" y="441"/></g>
 <g id="127000629">
  <use class="kv10kV" height="18" transform="rotate(0,1619,471) scale(1.7,1.7) translate(-675.647,-202.941)" width="18" x="1619" xlink:href="#Protect:bn_保护图元1_0" y="471"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892517249" ObjectName="122160139892517249" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1619,471) scale(1.7,1.7) translate(-675.647,-202.941)" width="18" x="1619" y="471"/></g>
 <g id="127000630">
  <use class="kv-1" height="18" transform="rotate(0,1619,501) scale(1.7,1.7) translate(-675.647,-215.294)" width="18" x="1619" xlink:href="#Protect:bn_保护图元1_0" y="501"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1619,501) scale(1.7,1.7) translate(-675.647,-215.294)" width="18" x="1619" y="501"/></g>
 <g id="127000631">
  <use class="kv10kV" height="18" transform="rotate(0,1619,561) scale(1.7,1.7) translate(-675.647,-240)" width="18" x="1619" xlink:href="#Protect:bn_保护图元1_0" y="561"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892517728" ObjectName="122160139892517728" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1619,561) scale(1.7,1.7) translate(-675.647,-240)" width="18" x="1619" y="561"/></g>
</g>
<g id="Text_Layer">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="30" font-width="30" stroke="rgb(85,255,255)" writing-mode="lr" x="858" xml:space="preserve" y="60">曼庄变继电保护远方操作</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="420" xml:space="preserve" y="152">线路</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="403" xml:space="preserve" y="329">1号主变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="403" xml:space="preserve" y="541">2号主变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="596" xml:space="preserve" y="152">名称</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="532" xml:space="preserve" y="183">控制逻辑投入压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="543" xml:space="preserve" y="242">测控分接头调节压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="532" xml:space="preserve" y="272">测控控制逻辑投入压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="553" xml:space="preserve" y="303">差动保护差动保护投入</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="553" xml:space="preserve" y="332">高后备保护间隙保护投</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="553" xml:space="preserve" y="362">高后备保护高侧电压投</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="553" xml:space="preserve" y="392">低后备保护母线保护投</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="553" xml:space="preserve" y="422">低后备保护低侧电压投</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="853" xml:space="preserve" y="152">软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="387" xml:space="preserve" y="182">公用测控一</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="387" xml:space="preserve" y="212">公用测控二</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="532" xml:space="preserve" y="213">控制逻辑投入压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="543" xml:space="preserve" y="453">测控分接头调节压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="532" xml:space="preserve" y="483">测控控制逻辑投入压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="553" xml:space="preserve" y="514">差动保护投入(软压板）</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="553" xml:space="preserve" y="543">高后备保护间隙保护投</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="553" xml:space="preserve" y="573">高后备保护高侧电压投</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="553" xml:space="preserve" y="603">低后备保护母线保护投</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="553" xml:space="preserve" y="633">低后备保护低侧电压投</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1172" xml:space="preserve" y="152">线路</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1393" xml:space="preserve" y="152">名称</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1592" xml:space="preserve" y="152">软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1115" xml:space="preserve" y="554">10kV#1电容器组</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1307" xml:space="preserve" y="539">10kV#1电容器组低电压保护</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1307" xml:space="preserve" y="573">信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1307" xml:space="preserve" y="361">装置复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1307" xml:space="preserve" y="331">低后备保护信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1307" xml:space="preserve" y="301">高后备保护信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1307" xml:space="preserve" y="271">差动保护信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1307" xml:space="preserve" y="241">非电量保护信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1307" xml:space="preserve" y="181">装置复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1160" xml:space="preserve" y="452">#2主变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1160" xml:space="preserve" y="303">#1主变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1139" xml:space="preserve" y="181">公用测控一</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1139" xml:space="preserve" y="211">公用测控二</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1307" xml:space="preserve" y="211">装置复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1307" xml:space="preserve" y="392">非电量保护信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1307" xml:space="preserve" y="422">差动保护信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1307" xml:space="preserve" y="452">高后备保护信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1307" xml:space="preserve" y="482">低后备保护信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1307" xml:space="preserve" y="512">装置复归</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href="35kV曼庄变.fac.svg"><rect fill-opacity="0" height="46" stroke-opacity="0" stroke-width="1" width="335" x="862" y="21"/></g>
</g>
</svg>