<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="sys" height="500" id="thSvg" viewBox="0 0 1000 500" width="1000">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="Protect:bn_保护图元1_0" viewBox="0,0,18,18">
 <rect AFMask="2147483647" Plane="0" fill="none" height="13" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,8,8)" width="13" x="2" y="2"/>
</symbol>
<symbol id="Protect:bn_保护图元1_1" viewBox="0,0,18,18">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="13" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,8,8)" width="13" x="2" y="2"/>
</symbol>
<symbol id="Protect:软压板投退_0" viewBox="0,0,50,50">
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,0,0)" stroke-width="3" x1="40" x2="13" y1="31" y2="4"/>
 <circle AFMask="2147483647" Plane="0" cx="9" cy="31" fill="none" r="4" stroke="rgb(255,0,0)" stroke-width="8"/>
 <circle AFMask="2147483647" Plane="0" cx="40" cy="31" fill="none" r="4" stroke="rgb(255,0,0)" stroke-width="8"/>
</symbol>
<symbol id="Protect:软压板投退_1" viewBox="0,0,50,50">
 <circle AFMask="2147483647" Plane="0" cx="9" cy="24" fill="none" r="4" stroke="rgb(0,255,0)" stroke-width="8"/>
 <circle AFMask="2147483647" Plane="0" cx="40" cy="24" fill="none" r="4" stroke="rgb(0,255,0)" stroke-width="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,255,0)" stroke-width="3" x1="9" x2="41" y1="24" y2="24"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="版纳_35kV_巴达变" InitShowingPlane="0," fill="rgb(0,0,0)" height="500" width="1000" x="0" y="0"/>
</g>
<g id="Other_Layer">
 
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="78" x2="930" y1="128" y2="128"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="78" x2="930" y1="178" y2="178"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="78" x2="930" y1="208" y2="208"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="78" x2="930" y1="238" y2="238"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="78" x2="930" y1="268" y2="268"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="78" x2="930" y1="298" y2="298"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="78" x2="930" y1="328" y2="328"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="78" x2="930" y1="358" y2="358"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="78" x2="930" y1="388" y2="388"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="78" x2="930" y1="418" y2="418"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="78" x2="930" y1="448" y2="448"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="78" x2="78" y1="128" y2="448"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="930" x2="930" y1="128" y2="446"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="652" x2="652" y1="178" y2="447"/>
</g>
<g id="Protect_Layer">
 <g id="127000082">
  <use class="kv35kV" height="50" transform="rotate(0,795,190) scale(0.6,0.6) translate(505,105.667)" width="50" x="795" xlink:href="#Protect:软压板投退_0" y="190"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892516678" ObjectName="122160139892516678" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,795,190) scale(0.6,0.6) translate(505,105.667)" width="50" x="795" y="190"/></g>
 <g id="127000083">
  <use class="kv35kV" height="50" transform="rotate(0,795,220) scale(0.6,0.6) translate(505,125.667)" width="50" x="795" xlink:href="#Protect:软压板投退_0" y="220"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892516688" ObjectName="122160139892516688" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,795,220) scale(0.6,0.6) translate(505,125.667)" width="50" x="795" y="220"/></g>
 <g id="127000084">
  <use class="kv35kV" height="50" transform="rotate(0,795,250) scale(0.6,0.6) translate(505,145.667)" width="50" x="795" xlink:href="#Protect:软压板投退_0" y="250"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892516689" ObjectName="122160139892516689" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,795,250) scale(0.6,0.6) translate(505,145.667)" width="50" x="795" y="250"/></g>
 <g id="127000085">
  <use class="kv10kV" height="50" transform="rotate(0,795,280) scale(0.6,0.6) translate(505,165.667)" width="50" x="795" xlink:href="#Protect:软压板投退_0" y="280"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892516708" ObjectName="122160139892516708" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,795,280) scale(0.6,0.6) translate(505,165.667)" width="50" x="795" y="280"/></g>
 <g id="127000086">
  <use class="kv10kV" height="50" transform="rotate(0,795,310) scale(0.6,0.6) translate(505,185.667)" width="50" x="795" xlink:href="#Protect:软压板投退_0" y="310"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892516707" ObjectName="122160139892516707" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,795,310) scale(0.6,0.6) translate(505,185.667)" width="50" x="795" y="310"/></g>
 <g id="127000107">
  <use class="kv35kV" height="18" transform="rotate(0,795,344) scale(1,1) translate(-9,-9)" width="18" x="795" xlink:href="#Protect:bn_保护图元1_0" y="344"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892516702" ObjectName="122160139892516702" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,795,344) scale(1,1) translate(-9,-9)" width="18" x="795" y="344"/></g>
 <g id="127000108">
  <use class="kv10kV" height="18" transform="rotate(0,795,374) scale(1,1) translate(-9,-9)" width="18" x="795" xlink:href="#Protect:bn_保护图元1_0" y="374"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892516721" ObjectName="122160139892516721" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,795,374) scale(1,1) translate(-9,-9)" width="18" x="795" y="374"/></g>
 <g id="127000109">
  <use class="kv35kV" height="18" transform="rotate(0,795,404) scale(1,1) translate(-9,-9)" width="18" x="795" xlink:href="#Protect:bn_保护图元1_0" y="404"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892516684" ObjectName="122160139892516684" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,795,404) scale(1,1) translate(-9,-9)" width="18" x="795" y="404"/></g>
 <g id="127000110">
  <use class="kv35kV" height="18" transform="rotate(0,795,434) scale(1,1) translate(-9,-9)" width="18" x="795" xlink:href="#Protect:bn_保护图元1_0" y="434"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892516675" ObjectName="122160139892516675" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,795,434) scale(1,1) translate(-9,-9)" width="18" x="795" y="434"/></g>
</g>
<g id="Text_Layer">
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="42" font-size="42" font-width="42" stroke="rgb(0,0,0)" writing-mode="lr" x="405" xml:space="preserve" y="69">35kV巴达变</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="425" xml:space="preserve" y="169">1号主变间隔</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="287" xml:space="preserve" y="205">差动保护软压板</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="277" xml:space="preserve" y="235">高压侧电压软压板</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="282" xml:space="preserve" y="265"> 间隙保护软压板</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="287" xml:space="preserve" y="295">低侧电压软压板</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="287" xml:space="preserve" y="325">母线保护软压板</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="266" xml:space="preserve" y="355">高后备保护信号复归</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="266" xml:space="preserve" y="385">低后备保护信号复归</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="277" xml:space="preserve" y="415">差动保护信号复归</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="266" xml:space="preserve" y="445">非电量保护信号复归</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href="版纳_35kV_巴达变.fac.svg"><rect fill-opacity="0" height="61" stroke-opacity="0" stroke-width="2" width="326" x="342" y="20"/></g>
 <g ChangePicPlane="0," Plane="0" href="版纳_35kV_巴达变1号主变间隔接线图.bay.svg"><rect fill-opacity="0" height="34" stroke-opacity="0" stroke-width="2" width="293" x="361" y="137"/></g>
</g>
</svg>