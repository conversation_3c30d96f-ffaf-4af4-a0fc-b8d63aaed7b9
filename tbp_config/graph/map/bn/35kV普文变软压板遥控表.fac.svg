<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="sys" height="950" id="thSvg" viewBox="0 0 1800 950" width="1800">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="Protect:软压板投退_0" viewBox="0,0,50,50">
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,0,0)" stroke-width="3" x1="40" x2="13" y1="31" y2="4"/>
 <circle AFMask="2147483647" Plane="0" cx="9" cy="31" fill="none" r="4" stroke="rgb(255,0,0)" stroke-width="8"/>
 <circle AFMask="2147483647" Plane="0" cx="40" cy="31" fill="none" r="4" stroke="rgb(255,0,0)" stroke-width="8"/>
</symbol>
<symbol id="Protect:软压板投退_1" viewBox="0,0,50,50">
 <circle AFMask="2147483647" Plane="0" cx="9" cy="24" fill="none" r="4" stroke="rgb(0,255,0)" stroke-width="8"/>
 <circle AFMask="2147483647" Plane="0" cx="40" cy="24" fill="none" r="4" stroke="rgb(0,255,0)" stroke-width="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,255,0)" stroke-width="3" x1="9" x2="41" y1="24" y2="24"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="版纳_35kV_普文变" InitShowingPlane="0," fill="rgb(0,0,0)" height="950" width="1800" x="0" y="0"/>
</g>
<g id="Other_Layer">
 
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="89" x2="853" y1="151" y2="151"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="90" x2="855" y1="114" y2="114"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="345" x2="855" y1="193" y2="193"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="88" x2="88" y1="114" y2="895"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="344" x2="344" y1="113" y2="895"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="855" x2="855" y1="113" y2="895"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="629" x2="629" y1="115" y2="895"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="344" x2="855" y1="237" y2="237"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="345" x2="855" y1="275" y2="275"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="88" x2="855" y1="311" y2="311"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="345" x2="855" y1="352" y2="352"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="344" x2="855" y1="391" y2="391"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="344" x2="855" y1="429" y2="429"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="88" x2="855" y1="470" y2="470"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="344" x2="855" y1="505" y2="505"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="344" x2="855" y1="539" y2="539"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="345" x2="855" y1="576" y2="576"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="344" x2="855" y1="614" y2="614"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="89" x2="855" y1="652" y2="652"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="89" x2="855" y1="895" y2="895"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="897" x2="1661" y1="152" y2="152"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="898" x2="1663" y1="115" y2="115"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1153" x2="1663" y1="194" y2="194"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="896" x2="896" y1="115" y2="896"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1152" x2="1152" y1="114" y2="896"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1663" x2="1663" y1="114" y2="896"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1437" x2="1437" y1="116" y2="896"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1152" x2="1663" y1="238" y2="238"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1153" x2="1663" y1="276" y2="276"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="896" x2="1663" y1="312" y2="312"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1153" x2="1663" y1="353" y2="353"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1152" x2="1663" y1="392" y2="392"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1152" x2="1663" y1="430" y2="430"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="896" x2="1663" y1="471" y2="471"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1152" x2="1663" y1="506" y2="506"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1152" x2="1663" y1="540" y2="540"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1153" x2="1663" y1="577" y2="577"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1152" x2="1663" y1="615" y2="615"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="897" x2="1663" y1="653" y2="653"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="897" x2="1663" y1="896" y2="896"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="345" x2="855" y1="693" y2="693"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="344" x2="855" y1="731" y2="731"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="88" x2="855" y1="768" y2="768"/>
</g>
<g id="Protect_Layer">
 <g id="127001176">
  <use class="kv35kV" height="50" transform="rotate(0,754,172) scale(0.863,0.863) translate(94.6965,6.30476)" width="50" x="754" xlink:href="#Protect:软压板投退_0" y="172"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892552099" ObjectName="122160139892552099" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,754,172) scale(0.863,0.863) translate(94.6965,6.30476)" width="50" x="754" y="172"/></g>
</g>
<g id="Text_Layer">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="30" font-width="30" stroke="rgb(85,255,255)" writing-mode="lr" x="664" xml:space="preserve" y="53">普文变继电保护远方操作</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="22" font-width="22" stroke="rgb(255,255,254)" writing-mode="lr" x="380" xml:space="preserve" y="186">保护停用重合闸软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="22" font-width="22" stroke="rgb(255,255,254)" writing-mode="lr" x="129" xml:space="preserve" y="238">35kV青云站用变线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="22" font-width="22" stroke="rgb(255,255,254)" writing-mode="lr" x="198" xml:space="preserve" y="148">线路</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="22" font-width="22" stroke="rgb(255,255,254)" writing-mode="lr" x="430" xml:space="preserve" y="148">名称</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="22" font-width="22" stroke="rgb(255,255,254)" writing-mode="lr" x="719" xml:space="preserve" y="146">软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="22" font-width="22" stroke="rgb(255,255,254)" writing-mode="lr" x="1006" xml:space="preserve" y="149">线路</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="22" font-width="22" stroke="rgb(255,255,254)" writing-mode="lr" x="1238" xml:space="preserve" y="149">名称</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="22" font-width="22" stroke="rgb(255,255,254)" writing-mode="lr" x="1527" xml:space="preserve" y="147">软压板</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href="版纳_35kV_普文变.fac.svg"><rect fill-opacity="0" height="37" stroke-opacity="0" stroke-width="1" width="354" x="652" y="19"/></g>
</g>
</svg>