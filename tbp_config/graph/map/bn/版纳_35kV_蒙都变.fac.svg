<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1055" id="thSvg" viewBox="0 0 2216 1055" width="2216">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="Breaker:0_0" viewBox="0,0,50,50">
 <use Plane="0" x="14" xlink:href="#terminal" y="7"/>
 <use Plane="0" x="14" xlink:href="#terminal" y="38"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="31" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,14,22)" width="16" x="6" y="7"/>
</symbol>
<symbol id="Breaker:0_1" viewBox="0,0,50,50">
 <use Plane="0" x="14" xlink:href="#terminal" y="7"/>
 <use Plane="0" x="14" xlink:href="#terminal" y="38"/>
 <rect AFMask="2147483647" Plane="0" fill="rgb(0,0,255)" height="31" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,15,22)" width="16" x="7" y="7"/>
</symbol>
<symbol id="Terminal:bn_35kV电缆_0" viewBox="0,0,38,12">
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,0)" stroke-width="1" x1="11" x2="25" y1="6" y2="6"/>
 <path AFMask="2147483647" Plane="0" d="M 8 2 L 11 5 L 8 8 " fill="none" stroke="rgb(255,255,0)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,0)" stroke-width="1" x1="7" x2="7" y1="3" y2="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,0)" stroke-width="1" x1="29" x2="29" y1="2" y2="8"/>
 <path AFMask="2147483647" Plane="0" d="M 28 8 L 25 5 L 28 2 " fill="none" stroke="rgb(255,255,0)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,0)" stroke-width="1" x1="2" x2="7" y1="6" y2="6"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,0)" stroke-width="1" x1="29" x2="34" y1="6" y2="6"/>
</symbol>
<symbol id="Arrester:bn_避雷器_0" viewBox="0,0,18,40">
 <use Plane="0" x="9" xlink:href="#terminal" y="3"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="18" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,9,18)" width="10" x="4" y="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="9" y1="27" y2="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="9" y1="4" y2="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="2" x2="16" y1="34" y2="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="5" x2="13" y1="36" y2="36"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="7" x2="11" y1="38" y2="38"/>
</symbol>
<symbol id="Arrester:bn_避雷器4_0" viewBox="0,0,20,40">
 <use Plane="0" x="10" xlink:href="#terminal" y="35"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="22" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,10,20)" width="10" x="5" y="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="5" y2="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="21" y2="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="5" x2="15" y1="5" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="7" x2="13" y1="3" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="11" y1="1" y2="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="8" x2="12" y1="20" y2="20"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="12" x2="10" y1="20" y2="17"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="8" y1="17" y2="20"/>
</symbol>
<symbol id="Status:bn_不变化颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(0,0,255)" height="1" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_不变化颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(85,0,255)" height="1" stroke="rgb(85,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Disconnector:bn_刀闸10_0" viewBox="0,0,40,20">
 <use Plane="0" x="5" xlink:href="#terminal" y="11"/>
 <use Plane="0" x="34" xlink:href="#terminal" y="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="30" x2="30" y1="19" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="35" x2="31" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="5" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="30" y1="12" y2="2"/>
</symbol>
<symbol id="Disconnector:bn_刀闸10_1" viewBox="0,0,40,20">
 <use Plane="0" x="5" xlink:href="#terminal" y="11"/>
 <use Plane="0" x="34" xlink:href="#terminal" y="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="30" x2="30" y1="19" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="5" x2="35" y1="12" y2="12"/>
</symbol>
<symbol id="PT:bn_电压互感器_0" viewBox="0,0,60,60">
 <use Plane="0" x="30" xlink:href="#terminal" y="5"/>
 <circle AFMask="2147483647" Plane="0" cx="35" cy="35" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="35" cy="25" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="25" cy="25" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="26" x2="26" y1="32" y2="36"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="23" x2="26" y1="39" y2="36"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="26" x2="29" y1="36" y2="39"/>
 <circle AFMask="2147483647" Plane="0" cx="25" cy="35" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="26" x2="29" y1="25" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="26" x2="26" y1="21" y2="25"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="23" x2="26" y1="28" y2="25"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="33" x2="36" y1="28" y2="25"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="36" x2="36" y1="21" y2="25"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="36" x2="39" y1="25" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="7" x2="13" y1="47" y2="47"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="8" x2="12" y1="49" y2="49"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="11" y1="51" y2="51"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="11" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,10,38)" width="6" x="7" y="33"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="5" x2="15" y1="34" y2="44"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="0" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,28,11)" width="0" x="28" y="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="44" y2="47"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="32" x2="39" y1="37" y2="37"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="32" x2="34" y1="37" y2="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="39" x2="37" y1="37" y2="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="30" x2="30" y1="20" y2="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="19" x2="10" y1="25" y2="25"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="25" y2="32"/>
</symbol>
<symbol id="Status:bn_非实测颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,85,0)" height="1" stroke="rgb(170,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_非实测颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,85,0)" height="1" stroke="rgb(170,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_工况退出颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,170,255)" height="1" stroke="rgb(170,170,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_工况退出颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,170,255)" height="1" stroke="rgb(170,170,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸1_0" viewBox="0,0,16,32">
 <use Plane="0" x="8" xlink:href="#terminal" y="2"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="4" y2="6"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="13" y1="27" y2="27"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="4" x2="12" y1="29" y2="29"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="6" x2="10" y1="31" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="27" y2="23"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="15" y1="22" y2="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="13" y1="6" y2="6"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸1_1" viewBox="0,0,16,32">
 <use Plane="0" x="8" xlink:href="#terminal" y="2"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="27" y2="4"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="6" x2="10" y1="31" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="4" x2="12" y1="29" y2="29"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="13" y1="27" y2="27"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="13" y1="6" y2="6"/>
</symbol>
<symbol id="Terminal:bn_景仰变_0" viewBox="0,0,120,70">
 <use Plane="0" x="69" xlink:href="#terminal" y="5"/>
 <circle AFMask="2147483647" Plane="0" cx="68" cy="39" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="60" x2="77" y1="31" y2="46"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="77" x2="60" y1="32" y2="47"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="69" x2="69" y1="28" y2="23"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="58" x2="80" y1="23" y2="23"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="58" x2="80" y1="18" y2="18"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="69" x2="69" y1="18" y2="7"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="69" x2="69" y1="51" y2="53"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="63" x2="75" y1="60" y2="60"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="65" x2="73" y1="62" y2="62"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="67" x2="71" y1="64" y2="64"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="69" x2="69" y1="53" y2="60"/>
</symbol>
<symbol id="Transformer2:bn_两卷变压器1_0" viewBox="0,0,40,80">
 <use Plane="0" x="20" xlink:href="#terminal" y="3"/>
 <circle AFMask="2147483647" Plane="0" cx="19" cy="21" fill="none" r="18" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="20" x2="27" y1="53" y2="45"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="20" x2="12" y1="53" y2="46"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="20" x2="20" y1="53" y2="63"/>
</symbol>
<symbol id="Transformer2:bn_两卷变压器1_1" viewBox="0,0,40,80">
 <use Plane="1" x="20" xlink:href="#terminal" y="72"/>
 <circle AFMask="2147483647" Plane="1" cx="19" cy="53" fill="none" r="18" stroke="rgb(0,0,255)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 19 27 L 28 14 L 9 14 Z" fill="none" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Fuse:bn_熔断器1_0" viewBox="0,0,20,50">
 <use Plane="0" x="10" xlink:href="#terminal" y="3"/>
 <use Plane="0" x="10" xlink:href="#terminal" y="43"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="3" y2="43"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="30" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,10,23)" width="14" x="3" y="8"/>
</symbol>
<symbol id="Status:bn_数据封锁颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,85,0)" height="1" stroke="rgb(255,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_数据封锁颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,85,0)" height="1" stroke="rgb(255,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_越限颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_越限颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Terminal:bn_终端设备10_0" viewBox="0,0,38,12">
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,255,0)" stroke-width="1" x1="2" x2="7" y1="6" y2="6"/>
 <polygon AFMask="2147483647" Plane="0" fill="none" points="7,3 14,5 7,8" stroke="rgb(0,255,0)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,255,0)" stroke-width="1" x1="12" x2="22" y1="6" y2="6"/>
 <polygon AFMask="2147483647" Plane="0" fill="none" points="21,5 28,3 28,8" stroke="rgb(0,255,0)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,255,0)" stroke-width="1" x1="27" x2="32" y1="6" y2="6"/>
</symbol>
<symbol id="GZP:gg_光子牌1_0" viewBox="0,0,36,36">
 <circle AFMask="2147483647" Plane="0" cx="18" cy="18" fill="rgb(0,255,0)" r="15" stroke="rgb(0,255,0)" stroke-width="1"/>
</symbol>
<symbol id="GZP:gg_光子牌1_1" viewBox="0,0,36,36">
 <circle AFMask="2147483647" Plane="0" cx="18" cy="18" fill="rgb(255,0,0)" r="15" stroke="rgb(255,0,0)" stroke-width="1"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="-1" InitShowingPlane="0," fill="rgb(0,0,0)" height="1055" width="2216" x="0" y="0"/>
</g>
<g id="Other_Layer">
 
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="33" x2="327" y1="235" y2="235"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="86" x2="86" y1="186" y2="282"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="148" x2="148" y1="186" y2="282"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="213" x2="213" y1="186" y2="282"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="273" x2="273" y1="186" y2="282"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="33" x2="327" y1="184" y2="184"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="32" x2="327" y1="959" y2="959"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="32" x2="327" y1="903" y2="903"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="32" x2="327" y1="696" y2="696"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="100" x2="327" y1="659" y2="659"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="32" x2="327" y1="621" y2="621"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="32" x2="327" y1="566" y2="566"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="32" x2="327" y1="513" y2="513"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="32" x2="327" y1="451" y2="451"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="100" x2="327" y1="394" y2="394"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="100" x2="327" y1="339" y2="339"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="32" x2="327" y1="282" y2="282"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="100" x2="100" y1="621" y2="846"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="169" x2="169" y1="452" y2="566"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="100" x2="100" y1="282" y2="451"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="32" x2="32" y1="26" y2="1023"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="32" x2="327" y1="122" y2="122"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="327" x2="327" y1="27" y2="1024"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="32" x2="327" y1="1023" y2="1023"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="32" x2="327" y1="26" y2="26"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="32" x2="327" y1="847" y2="847"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="100" x2="327" y1="734" y2="734"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="32" x2="327" y1="772" y2="772"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="100" x2="327" y1="809" y2="809"/>
 <rect AFMask="39039" Plane="0" fill="rgb(0,255,0)" height="29" stroke="rgb(0,0,255)" stroke-width="3" transform="rotate(0,376,79)" width="38" x="357" y="65"/>
</g>
<g id="Bus_Layer">
 <g id="30000000">
  <path d="M 781 480 L 1782 480" stroke="rgb(0,0,255)" stroke-width="4"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 781 480 L 1782 480" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="Breaker_Layer">
 <g id="100000010">
  <use class="kv-1" height="50" transform="rotate(0,896,614) scale(1,1) translate(-14,-23)" width="50" x="896" xlink:href="#Breaker:0_0" y="614"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,896,614) scale(1,1) translate(-14,-23)" width="50" x="896" y="614"/></g>
 <g id="100000526">
  <use class="kv-1" height="50" transform="rotate(0,1096,365) scale(1,1) translate(-14,-23)" width="50" x="1096" xlink:href="#Breaker:0_0" y="365"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1096,365) scale(1,1) translate(-14,-23)" width="50" x="1096" y="365"/></g>
 <g id="100000656">
  <use class="kv-1" height="50" transform="rotate(0,1271,614) scale(1,1) translate(-14,-23)" width="50" x="1271" xlink:href="#Breaker:0_0" y="614"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1271,614) scale(1,1) translate(-14,-23)" width="50" x="1271" y="614"/></g>
 <g id="100000679">
  <use class="kv-1" height="50" transform="rotate(0,1663,614) scale(1,1) translate(-14,-23)" width="50" x="1663" xlink:href="#Breaker:0_0" y="614"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1663,614) scale(1,1) translate(-14,-23)" width="50" x="1663" y="614"/></g>
</g>
<g id="Disconnector_Layer">
 <g id="101000633">
  <use class="kv-1" height="20" transform="rotate(90,895,547) scale(1,1) translate(-20,-12)" width="40" x="895" xlink:href="#Disconnector:bn_刀闸10_0" y="547"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,895,547) scale(1,1) translate(-20,-12)" width="40" x="895" y="547"/></g>
 <g id="101000637">
  <use class="kv-1" height="20" transform="rotate(90,895,677) scale(1,1) translate(-20,-12)" width="40" x="895" xlink:href="#Disconnector:bn_刀闸10_0" y="677"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,895,677) scale(1,1) translate(-20,-12)" width="40" x="895" y="677"/></g>
 <g id="101000657">
  <use class="kv-1" height="20" transform="rotate(90,1270,547) scale(1,1) translate(-20,-12)" width="40" x="1270" xlink:href="#Disconnector:bn_刀闸10_0" y="547"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1270,547) scale(1,1) translate(-20,-12)" width="40" x="1270" y="547"/></g>
 <g id="101000658">
  <use class="kv-1" height="20" transform="rotate(90,1270,677) scale(1,1) translate(-20,-12)" width="40" x="1270" xlink:href="#Disconnector:bn_刀闸10_0" y="677"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1270,677) scale(1,1) translate(-20,-12)" width="40" x="1270" y="677"/></g>
 <g id="101000681">
  <use class="kv-1" height="20" transform="rotate(90,1662,677) scale(1,1) translate(-20,-12)" width="40" x="1662" xlink:href="#Disconnector:bn_刀闸10_0" y="677"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1662,677) scale(1,1) translate(-20,-12)" width="40" x="1662" y="677"/></g>
 <g id="101000680">
  <use class="kv-1" height="20" transform="rotate(90,1662,547) scale(1,1) translate(-20,-12)" width="40" x="1662" xlink:href="#Disconnector:bn_刀闸10_0" y="547"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1662,547) scale(1,1) translate(-20,-12)" width="40" x="1662" y="547"/></g>
 <g id="101000687">
  <use class="kv-1" height="20" transform="rotate(90,1095,435) scale(1,1) translate(-20,-12)" width="40" x="1095" xlink:href="#Disconnector:bn_刀闸10_0" y="435"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1095,435) scale(1,1) translate(-20,-12)" width="40" x="1095" y="435"/></g>
 <g id="101000689">
  <use class="kv-1" height="20" transform="rotate(90,1095,296) scale(1,1) translate(-20,-12)" width="40" x="1095" xlink:href="#Disconnector:bn_刀闸10_0" y="296"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1095,296) scale(1,1) translate(-20,-12)" width="40" x="1095" y="296"/></g>
 <g id="101000691">
  <use class="kv-1" height="20" transform="rotate(90,1482,435) scale(1,1) translate(-20,-12)" width="40" x="1482" xlink:href="#Disconnector:bn_刀闸10_0" y="435"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1482,435) scale(1,1) translate(-20,-12)" width="40" x="1482" y="435"/></g>
</g>
<g id="GroundDisconnector_Layer">
 <g id="111000627">
  <use class="kv-1" height="32" transform="rotate(-90,1526,394) scale(1,1) translate(-8,-2)" width="16" x="1526" xlink:href="#GroundDisconnector:bn_接地刀闸1_0" y="394"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(-90,1526,394) scale(1,1) translate(-8,-2)" width="16" x="1526" y="394"/></g>
</g>
<g id="Transformer2_Layer">
<g id="102000101">
 <g id="1020001010">
  <use class="kv-1" height="80" transform="rotate(0,895,857) scale(1,1) translate(-20,-38)" width="40" x="895" xlink:href="#Transformer2:bn_两卷变压器1_0" y="857"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="1020001011">
  <use class="kv-1" height="80" transform="rotate(0,895,857) scale(1,1) translate(-20,-38)" width="40" x="895" xlink:href="#Transformer2:bn_两卷变压器1_1" y="857"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <metadata>
  <cge:PSR_Ref ObjectID="-1" ObjectName="-1" Plane="0"/>
 </metadata>
<rect fill="white" height="80" opacity="0" stroke="white" transform="rotate(0,895,857) scale(1,1) translate(-20,-38)" width="40" x="895" y="857"/></g>
<g id="102000659">
 <g id="1020006590">
  <use class="kv-1" height="80" transform="rotate(0,1270,857) scale(1,1) translate(-20,-38)" width="40" x="1270" xlink:href="#Transformer2:bn_两卷变压器1_0" y="857"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="1020006591">
  <use class="kv-1" height="80" transform="rotate(0,1270,857) scale(1,1) translate(-20,-38)" width="40" x="1270" xlink:href="#Transformer2:bn_两卷变压器1_1" y="857"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <metadata>
  <cge:PSR_Ref ObjectID="-1" ObjectName="-1" Plane="0"/>
 </metadata>
<rect fill="white" height="80" opacity="0" stroke="white" transform="rotate(0,1270,857) scale(1,1) translate(-20,-38)" width="40" x="1270" y="857"/></g>
<g id="102000682">
 <g id="1020006820">
  <use class="kv-1" height="80" transform="rotate(0,1662,857) scale(1,1) translate(-20,-38)" width="40" x="1662" xlink:href="#Transformer2:bn_两卷变压器1_0" y="857"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="1020006821">
  <use class="kv-1" height="80" transform="rotate(0,1662,857) scale(1,1) translate(-20,-38)" width="40" x="1662" xlink:href="#Transformer2:bn_两卷变压器1_1" y="857"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <metadata>
  <cge:PSR_Ref ObjectID="-1" ObjectName="-1" Plane="0"/>
 </metadata>
<rect fill="white" height="80" opacity="0" stroke="white" transform="rotate(0,1662,857) scale(1,1) translate(-20,-38)" width="40" x="1662" y="857"/></g>
</g>
<g id="PT_Layer">
 <g id="107000623">
  <use class="kv-1" height="60" transform="rotate(180,1482,258) scale(1.5,1.5) translate(-524,-92)" width="60" x="1482" xlink:href="#PT:bn_电压互感器_0" y="258"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(180,1482,258) scale(1.5,1.5) translate(-524,-92)" width="60" x="1482" y="258"/></g>
</g>
<g id="GZP_Layer">
 <g id="135000477">
  <use class="kv-1" height="36" transform="rotate(0,242,597) scale(0.9,0.9) translate(8.8889,48.3334)" width="36" x="242" xlink:href="#GZP:gg_光子牌1_0" y="597"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,242,597) scale(0.9,0.9) translate(8.8889,48.3334)" width="36" x="242" y="597"/></g>
</g>
<g id="Arrester_Layer">
 <g id="133000047">
  <use class="kv-1" height="40" transform="rotate(180,1420,342) scale(1.2,1.2) translate(-245.667,-61)" width="18" x="1420" xlink:href="#Arrester:bn_避雷器_0" y="342"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(180,1420,342) scale(1.2,1.2) translate(-245.667,-61)" width="18" x="1420" y="342"/></g>
 <g id="133000529">
  <use class="kv-1" height="40" transform="rotate(180,972,765) scale(1.2,1.2) translate(-172,-162.5)" width="20" x="972" xlink:href="#Arrester:bn_避雷器4_0" y="765"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(180,972,765) scale(1.2,1.2) translate(-172,-162.5)" width="20" x="972" y="765"/></g>
 <g id="133000606">
  <use class="kv-1" height="40" transform="rotate(0,1166,193) scale(1.2,1.2) translate(-204.333,-67.1667)" width="20" x="1166" xlink:href="#Arrester:bn_避雷器4_0" y="193"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1166,193) scale(1.2,1.2) translate(-204.333,-67.1667)" width="20" x="1166" y="193"/></g>
 <g id="133000662">
  <use class="kv-1" height="40" transform="rotate(180,1347,765) scale(1.2,1.2) translate(-234.5,-162.5)" width="20" x="1347" xlink:href="#Arrester:bn_避雷器4_0" y="765"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(180,1347,765) scale(1.2,1.2) translate(-234.5,-162.5)" width="20" x="1347" y="765"/></g>
 <g id="133000685">
  <use class="kv-1" height="40" transform="rotate(180,1739,765) scale(1.2,1.2) translate(-299.833,-162.5)" width="20" x="1739" xlink:href="#Arrester:bn_避雷器4_0" y="765"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(180,1739,765) scale(1.2,1.2) translate(-299.833,-162.5)" width="20" x="1739" y="765"/></g>
</g>
<g id="Fuse_Layer">
 <g id="131000063">
  <use class="kv-1" height="50" transform="rotate(0,1482,297) scale(1,1) translate(-10,-23)" width="20" x="1482" xlink:href="#Fuse:bn_熔断器1_0" y="297"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1482,297) scale(1,1) translate(-10,-23)" width="20" x="1482" y="297"/></g>
</g>
<g id="Status_Layer">
 <g id="126000476">
  <use class="kv-1" height="40" transform="rotate(0,306,211) scale(0.7,0.7) translate(101.143,70.4286)" width="60" x="306" xlink:href="#Status:bn_数据封锁颜色显示_0" y="211"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,306,211) scale(0.7,0.7) translate(101.143,70.4286)" width="60" x="306" y="211"/></g>
 <g id="126000475">
  <use class="kv-1" height="40" transform="rotate(0,243,211) scale(0.7,0.7) translate(74.1429,70.4286)" width="60" x="243" xlink:href="#Status:bn_非实测颜色显示_0" y="211"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,243,211) scale(0.7,0.7) translate(74.1429,70.4286)" width="60" x="243" y="211"/></g>
 <g id="126000474">
  <use class="kv-1" height="40" transform="rotate(0,183,211) scale(0.7,0.7) translate(48.4286,70.4286)" width="60" x="183" xlink:href="#Status:bn_越限颜色显示_0" y="211"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,183,211) scale(0.7,0.7) translate(48.4286,70.4286)" width="60" x="183" y="211"/></g>
 <g id="126000473">
  <use class="kv-1" height="40" transform="rotate(0,124,211) scale(0.7,0.7) translate(23.1429,70.4286)" width="60" x="124" xlink:href="#Status:bn_不变化颜色显示_0" y="211"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,124,211) scale(0.7,0.7) translate(23.1429,70.4286)" width="60" x="124" y="211"/></g>
 <g id="126000472">
  <use class="kv-1" height="40" transform="rotate(0,59,211) scale(0.7,0.7) translate(-4.71428,70.4286)" width="60" x="59" xlink:href="#Status:bn_工况退出颜色显示_0" y="211"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,59,211) scale(0.7,0.7) translate(-4.71428,70.4286)" width="60" x="59" y="211"/></g>
</g>
<g id="Clock_Layer">
 <g id="56000471">
  
 <metadata/></g>
</g>
<g id="Term_Layer">
 <g id="112000046">
  <use class="kv-1" height="70" transform="rotate(180,1519,362) scale(1,1) translate(-69,-5)" width="120" x="1519" xlink:href="#Terminal:bn_景仰变_0" y="362"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(180,1519,362) scale(1,1) translate(-69,-5)" width="120" x="1519" y="362"/></g>
 <g id="112000111">
  <use class="kv-1" height="70" transform="rotate(0,835,723) scale(1.2,1.2) translate(-208.167,-125.5)" width="120" x="835" xlink:href="#Terminal:bn_景仰变_0" y="723"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,835,723) scale(1.2,1.2) translate(-208.167,-125.5)" width="120" x="835" y="723"/></g>
 <g id="112000605">
  <use class="kv-1" height="70" transform="rotate(180,1053,227) scale(-1.2,1.2) translate(-1999.5,-42.8333)" width="120" x="1053" xlink:href="#Terminal:bn_景仰变_0" y="227"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(180,1053,227) scale(-1.2,1.2) translate(-1999.5,-42.8333)" width="120" x="1053" y="227"/></g>
 <g id="112000616">
  <use class="kv-1" height="12" transform="rotate(90,1095,169) scale(1,1) translate(-18,-6)" width="38" x="1095" xlink:href="#Terminal:bn_35kV电缆_0" y="169"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,1095,169) scale(1,1) translate(-18,-6)" width="38" x="1095" y="169"/></g>
 <g id="112000661">
  <use class="kv-1" height="70" transform="rotate(0,1208,723) scale(1.2,1.2) translate(-270.333,-125.5)" width="120" x="1208" xlink:href="#Terminal:bn_景仰变_0" y="723"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,1208,723) scale(1.2,1.2) translate(-270.333,-125.5)" width="120" x="1208" y="723"/></g>
 <g id="112000684">
  <use class="kv-1" height="70" transform="rotate(0,1602,723) scale(1.2,1.2) translate(-336,-125.5)" width="120" x="1602" xlink:href="#Terminal:bn_景仰变_0" y="723"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,1602,723) scale(1.2,1.2) translate(-336,-125.5)" width="120" x="1602" y="723"/></g>
 <g id="112000693">
  <use class="kv-1" height="12" transform="rotate(90,895,780) scale(1,1) translate(-17,-6)" width="38" x="895" xlink:href="#Terminal:bn_终端设备10_0" y="780"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,895,780) scale(1,1) translate(-17,-6)" width="38" x="895" y="780"/></g>
 <g id="112000696">
  <use class="kv-1" height="12" transform="rotate(90,1270,780) scale(1,1) translate(-17,-6)" width="38" x="1270" xlink:href="#Terminal:bn_终端设备10_0" y="780"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,1270,780) scale(1,1) translate(-17,-6)" width="38" x="1270" y="780"/></g>
 <g id="112000697">
  <use class="kv-1" height="12" transform="rotate(90,1662,780) scale(1,1) translate(-17,-6)" width="38" x="1662" xlink:href="#Terminal:bn_终端设备10_0" y="780"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,1662,780) scale(1,1) translate(-17,-6)" width="38" x="1662" y="780"/></g>
</g>
<g id="Link_Layer">
 <g id="34000058">
 <path d="M 1482 450 L 1482 480" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000691_1" Pin1InfoVect0LinkObjId="30000000_0" Plane="0"/>
  </metadata>
 <path d="M 1482 450 L 1482 480" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000062">
 <path d="M 1482 360 L 1521 360" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000064_1" Pin0InfoVect1LinkObjId="34000083_0" Pin1InfoVect0LinkObjId="112000046_0" Plane="0"/>
  </metadata>
 <path d="M 1482 360 L 1521 360" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000064">
 <path d="M 1482 277 L 1482 360" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin1InfoVect0LinkObjId="34000062_0" Pin1InfoVect1LinkObjId="34000083_0" Plane="0"/>
  </metadata>
 <path d="M 1482 277 L 1482 360" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000083">
 <path d="M 1482 360 L 1482 360" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000064_1" Pin0InfoVect1LinkObjId="34000062_0" Pin1InfoVect0LinkObjId="34000626_1" Pin1InfoVect1LinkObjId="34000631_0" Plane="0"/>
  </metadata>
 <path d="M 1482 360 L 1482 360" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000113">
 <path d="M 895 562 L 895 598" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000633_1" Pin1InfoVect0LinkObjId="100000010_0" Plane="0"/>
  </metadata>
 <path d="M 895 562 L 895 598" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000116">
 <path d="M 833 726 L 972 726" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="112000111_0" Pin1InfoVect0LinkObjId="34000532_1" Plane="0"/>
  </metadata>
 <path d="M 833 726 L 972 726" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000117">
 <path d="M 895 692 L 895 822" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000637_1" Pin1InfoVect0LinkObjId="102000101_0" Plane="0"/>
  </metadata>
 <path d="M 895 692 L 895 822" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000236">
 <path d="M 1482 258 L 1482 277" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="107000623_0" Pin1InfoVect0LinkObjId="131000063_0" Plane="0"/>
  </metadata>
 <path d="M 1482 258 L 1482 277" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000532">
 <path d="M 972 765 L 972 726" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133000529_0" Pin1InfoVect0LinkObjId="34000116_1" Plane="0"/>
  </metadata>
 <path d="M 972 765 L 972 726" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000607">
 <path d="M 1095 420 L 1095 380" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000687_0" Pin1InfoVect0LinkObjId="100000526_1" Plane="0"/>
  </metadata>
 <path d="M 1095 420 L 1095 380" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000608">
 <path d="M 1095 450 L 1095 480" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000687_1" Pin1InfoVect0LinkObjId="30000000_0" Plane="0"/>
  </metadata>
 <path d="M 1095 450 L 1095 480" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000609">
 <path d="M 1095 349 L 1095 311" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000526_0" Pin1InfoVect0LinkObjId="101000689_1" Plane="0"/>
  </metadata>
 <path d="M 1095 349 L 1095 311" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000611">
 <path d="M 1051 225 L 1095 225" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="112000605_0" Pin1InfoVect0LinkObjId="34000612_1" Pin1InfoVect1LinkObjId="34000613_0" Pin1InfoVect2LinkObjId="34000615_1" Plane="0"/>
  </metadata>
 <path d="M 1051 225 L 1095 225" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000612">
 <path d="M 1095 281 L 1095 225" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000689_0" Pin1InfoVect0LinkObjId="34000611_1" Pin1InfoVect1LinkObjId="34000613_0" Pin1InfoVect2LinkObjId="34000615_1" Plane="0"/>
  </metadata>
 <path d="M 1095 281 L 1095 225" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000613">
 <path d="M 1095 225 L 1095 122" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000611_1" Pin0InfoVect1LinkObjId="34000612_1" Pin0InfoVect2LinkObjId="34000615_1" Plane="0"/>
  </metadata>
 <path d="M 1095 225 L 1095 122" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000615">
 <path d="M 1166 193 L 1166 225 L 1095 225" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133000606_0" Pin1InfoVect0LinkObjId="34000611_1" Pin1InfoVect1LinkObjId="34000612_1" Pin1InfoVect2LinkObjId="34000613_0" Plane="0"/>
  </metadata>
 <path d="M 1166 193 L 1166 225 L 1095 225" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000626">
 <path d="M 1419 341 L 1419 360 L 1482 360" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133000047_0" Pin1InfoVect0LinkObjId="34000083_1" Pin1InfoVect1LinkObjId="34000631_0" Plane="0"/>
  </metadata>
 <path d="M 1419 341 L 1419 360 L 1482 360" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000630">
 <path d="M 1526 394 L 1482 394" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111000627_0" Pin1InfoVect0LinkObjId="34000631_1" Pin1InfoVect1LinkObjId="34000632_0" Plane="0"/>
  </metadata>
 <path d="M 1526 394 L 1482 394" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000631">
 <path d="M 1482 360 L 1482 394" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000083_1" Pin0InfoVect1LinkObjId="34000626_1" Pin1InfoVect0LinkObjId="34000630_1" Pin1InfoVect1LinkObjId="34000632_0" Plane="0"/>
  </metadata>
 <path d="M 1482 360 L 1482 394" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000632">
 <path d="M 1482 394 L 1482 420" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000631_1" Pin0InfoVect1LinkObjId="34000630_1" Pin1InfoVect0LinkObjId="101000691_0" Plane="0"/>
  </metadata>
 <path d="M 1482 394 L 1482 420" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000638">
 <path d="M 895 662 L 895 629" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000637_0" Pin1InfoVect0LinkObjId="100000010_1" Plane="0"/>
  </metadata>
 <path d="M 895 662 L 895 629" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000639">
 <path d="M 895 532 L 895 480" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000633_0" Pin1InfoVect0LinkObjId="30000000_0" Plane="0"/>
  </metadata>
 <path d="M 895 532 L 895 480" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000650">
 <path d="M 1270 562 L 1270 598" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000657_1" Pin1InfoVect0LinkObjId="100000656_0" Plane="0"/>
  </metadata>
 <path d="M 1270 562 L 1270 598" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000651">
 <path d="M 1206 726 L 1347 726" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="112000661_0" Pin1InfoVect0LinkObjId="34000653_1" Plane="0"/>
  </metadata>
 <path d="M 1206 726 L 1347 726" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000652">
 <path d="M 1270 692 L 1270 822" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000658_1" Pin1InfoVect0LinkObjId="102000659_0" Plane="0"/>
  </metadata>
 <path d="M 1270 692 L 1270 822" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000653">
 <path d="M 1347 765 L 1347 726" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133000662_0" Pin1InfoVect0LinkObjId="34000651_1" Plane="0"/>
  </metadata>
 <path d="M 1347 765 L 1347 726" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000654">
 <path d="M 1270 662 L 1270 629" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000658_0" Pin1InfoVect0LinkObjId="100000656_1" Plane="0"/>
  </metadata>
 <path d="M 1270 662 L 1270 629" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000655">
 <path d="M 1270 532 L 1270 480" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000657_0" Pin1InfoVect0LinkObjId="30000000_0" Plane="0"/>
  </metadata>
 <path d="M 1270 532 L 1270 480" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000678">
 <path d="M 1662 532 L 1662 480" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000680_0" Pin1InfoVect0LinkObjId="30000000_0" Plane="0"/>
  </metadata>
 <path d="M 1662 532 L 1662 480" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000677">
 <path d="M 1662 662 L 1662 629" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000681_0" Pin1InfoVect0LinkObjId="100000679_1" Plane="0"/>
  </metadata>
 <path d="M 1662 662 L 1662 629" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000676">
 <path d="M 1739 765 L 1739 726" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133000685_0" Pin1InfoVect0LinkObjId="34000674_1" Plane="0"/>
  </metadata>
 <path d="M 1739 765 L 1739 726" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000675">
 <path d="M 1662 692 L 1662 822" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000681_1" Pin1InfoVect0LinkObjId="102000682_0" Plane="0"/>
  </metadata>
 <path d="M 1662 692 L 1662 822" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000674">
 <path d="M 1600 726 L 1739 726" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="112000684_0" Pin1InfoVect0LinkObjId="34000676_1" Plane="0"/>
  </metadata>
 <path d="M 1600 726 L 1739 726" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000673">
 <path d="M 1662 562 L 1662 598" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000680_1" Pin1InfoVect0LinkObjId="100000679_0" Plane="0"/>
  </metadata>
 <path d="M 1662 562 L 1662 598" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="MeasurementValue_Layer">
 <g id="33000281">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="772" xml:space="preserve" y="441">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000280">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="772" xml:space="preserve" y="416">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000279">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="772" xml:space="preserve" y="392">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000278">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="772" xml:space="preserve" y="343">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000277">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="772" xml:space="preserve" y="318">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000276">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="772" xml:space="preserve" y="292">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000275">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="772" xml:space="preserve" y="368">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000291">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="875" xml:space="preserve" y="940">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000292">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="875" xml:space="preserve" y="965">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000293">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="875" xml:space="preserve" y="991">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000468">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="23" stroke="rgb(255,255,0)" writing-mode="lr" x="227" xml:space="preserve" y="888">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000467">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="23" stroke="rgb(255,255,0)" writing-mode="lr" x="227" xml:space="preserve" y="947">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000466">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="19" font-size="19" font-width="22" stroke="rgb(255,255,0)" writing-mode="lr" x="227" xml:space="preserve" y="685">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000465">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="19" font-size="19" font-width="22" stroke="rgb(255,255,0)" writing-mode="lr" x="227" xml:space="preserve" y="647">-00000</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000463">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="19" font-size="19" font-width="22" stroke="rgb(255,255,0)" writing-mode="lr" x="212" xml:space="preserve" y="488">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000464">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="23" stroke="rgb(255,255,0)" writing-mode="lr" x="212" xml:space="preserve" y="549">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000470">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="19" font-size="19" font-width="22" stroke="rgb(255,255,0)" writing-mode="lr" x="227" xml:space="preserve" y="761">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000469">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="19" font-size="19" font-width="22" stroke="rgb(255,255,0)" writing-mode="lr" x="227" xml:space="preserve" y="723">-00000</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000620">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1075" xml:space="preserve" y="64">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000621">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1075" xml:space="preserve" y="89">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000622">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1075" xml:space="preserve" y="115">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000647">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1250" xml:space="preserve" y="940">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000648">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1250" xml:space="preserve" y="965">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000649">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1250" xml:space="preserve" y="991">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000672">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1642" xml:space="preserve" y="991">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000671">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1642" xml:space="preserve" y="965">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000670">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1642" xml:space="preserve" y="940">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000703">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="19" font-size="19" font-width="22" stroke="rgb(255,255,0)" writing-mode="lr" x="227" xml:space="preserve" y="799">-00000</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000704">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="19" font-size="19" font-width="22" stroke="rgb(255,255,0)" writing-mode="lr" x="227" xml:space="preserve" y="837">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
</g>
<g id="Text_Layer">
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="50" font-size="35" font-width="35" stroke="rgb(0,0,0)" writing-mode="lr" x="132" xml:space="preserve" y="98">35kV蒙都变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="738" xml:space="preserve" y="475">35kVⅠ母线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="915" xml:space="preserve" y="624">301</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="730" xml:space="preserve" y="441">3U0</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="730" xml:space="preserve" y="416">Uc</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="730" xml:space="preserve" y="392">Ub</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="730" xml:space="preserve" y="342">Uca</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="730" xml:space="preserve" y="292">Uab</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="730" xml:space="preserve" y="318">Ubc</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="730" xml:space="preserve" y="368">Ua</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="852" xml:space="preserve" y="939">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="852" xml:space="preserve" y="964">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="852" xml:space="preserve" y="990">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="17" font-size="17" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="282" xml:space="preserve" y="254">数据</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="17" font-size="17" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="282" xml:space="preserve" y="271">封锁</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="17" font-size="17" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="226" xml:space="preserve" y="276">实测</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="17" font-size="17" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="233" xml:space="preserve" y="254">非</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="17" font-size="17" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="163" xml:space="preserve" y="267">越限</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="17" font-size="17" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="100" xml:space="preserve" y="276">变化</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="17" font-size="17" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="108" xml:space="preserve" y="254">不</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="17" font-size="17" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="38" xml:space="preserve" y="254">工况</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="17" font-size="17" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="38" xml:space="preserve" y="271">退出</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="22" font-size="22" font-width="25" stroke="rgb(255,255,254)" writing-mode="tb" x="57" xml:space="preserve" y="389">位</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="20" font-size="20" font-width="23" stroke="rgb(255,255,254)" writing-mode="lr" x="188" xml:space="preserve" y="997">5120036</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="20" font-size="20" font-width="23" stroke="rgb(255,255,254)" writing-mode="lr" x="77" xml:space="preserve" y="999">联系方式：</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="22" font-size="22" font-width="25" stroke="rgb(255,255,254)" writing-mode="tb" x="57" xml:space="preserve" y="316">单</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="18" font-size="18" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="62" xml:space="preserve" y="546">总无功</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="24" stroke="rgb(170,0,0)" writing-mode="lr" x="277" xml:space="preserve" y="434">℃</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="212" xml:space="preserve" y="434">温度:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="24" stroke="rgb(170,0,0)" writing-mode="lr" x="170" xml:space="preserve" y="434">Hz</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="108" xml:space="preserve" y="434">频率:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="24" stroke="rgb(170,0,0)" writing-mode="lr" x="277" xml:space="preserve" y="380">kV</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="212" xml:space="preserve" y="380">电压:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="24" stroke="rgb(170,0,0)" writing-mode="lr" x="170" xml:space="preserve" y="380">A</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="108" xml:space="preserve" y="380">电流:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="24" stroke="rgb(170,0,0)" writing-mode="lr" x="277" xml:space="preserve" y="322">MVr</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="212" xml:space="preserve" y="322">无功:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="108" xml:space="preserve" y="322">有功:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="24" stroke="rgb(170,0,0)" writing-mode="lr" x="170" xml:space="preserve" y="322">MW</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="20" font-size="20" font-width="23" stroke="rgb(255,255,254)" writing-mode="lr" x="59" xml:space="preserve" y="947">合闸母线电压</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="20" font-size="20" font-width="23" stroke="rgb(255,255,254)" writing-mode="lr" x="59" xml:space="preserve" y="888">控制母线电压</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="19" font-size="19" font-width="22" stroke="rgb(255,255,254)" writing-mode="lr" x="147" xml:space="preserve" y="685">温度</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="22" font-size="22" font-width="25" stroke="rgb(255,255,254)" writing-mode="lr" x="40" xml:space="preserve" y="661">一号</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="22" font-size="22" font-width="25" stroke="rgb(255,255,254)" writing-mode="lr" x="40" xml:space="preserve" y="683">主变</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="19" font-size="19" font-width="22" stroke="rgb(255,255,254)" writing-mode="lr" x="147" xml:space="preserve" y="647">档位</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="18" font-size="18" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="62" xml:space="preserve" y="488">总有功</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="18" font-size="18" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="105" xml:space="preserve" y="600">事故总:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="19" font-size="19" font-width="22" stroke="rgb(255,255,254)" writing-mode="lr" x="147" xml:space="preserve" y="761">温度</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="19" font-size="19" font-width="22" stroke="rgb(255,255,254)" writing-mode="lr" x="147" xml:space="preserve" y="723">档位</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="22" font-size="22" font-width="25" stroke="rgb(255,255,254)" writing-mode="lr" x="40" xml:space="preserve" y="735">二号</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="22" font-size="22" font-width="25" stroke="rgb(255,255,254)" writing-mode="lr" x="40" xml:space="preserve" y="757">主变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1115" xml:space="preserve" y="375">353</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="854" xml:space="preserve" y="1038">1#变压器</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="1052" xml:space="preserve" y="63">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="1052" xml:space="preserve" y="88">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="1052" xml:space="preserve" y="114">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1430" xml:space="preserve" y="172">35kV母线PT</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1565" xml:space="preserve" y="403">39017</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="915" xml:space="preserve" y="566">3011</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="915" xml:space="preserve" y="696">3016</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1290" xml:space="preserve" y="624">302</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="1227" xml:space="preserve" y="939">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="1227" xml:space="preserve" y="964">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="1227" xml:space="preserve" y="990">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1229" xml:space="preserve" y="1038">2#变压器</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1290" xml:space="preserve" y="566">3021</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1290" xml:space="preserve" y="696">3026</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1682" xml:space="preserve" y="696">3036</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1682" xml:space="preserve" y="566">3031</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1621" xml:space="preserve" y="1038">3#变压器</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="1619" xml:space="preserve" y="990">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="1619" xml:space="preserve" y="964">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="1619" xml:space="preserve" y="939">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1682" xml:space="preserve" y="624">303</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1115" xml:space="preserve" y="454">3531</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1115" xml:space="preserve" y="315">3536</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1502" xml:space="preserve" y="454">3901</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1027" xml:space="preserve" y="29">35kV电源进线</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="22" font-size="22" font-width="25" stroke="rgb(255,255,254)" writing-mode="lr" x="40" xml:space="preserve" y="811">三号</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="22" font-size="22" font-width="25" stroke="rgb(255,255,254)" writing-mode="lr" x="40" xml:space="preserve" y="833">主变</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="19" font-size="19" font-width="22" stroke="rgb(255,255,254)" writing-mode="lr" x="147" xml:space="preserve" y="799">档位</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="19" font-size="19" font-width="22" stroke="rgb(255,255,254)" writing-mode="lr" x="147" xml:space="preserve" y="837">温度</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href="监视列表new.sys.svg"><rect fill-opacity="0" height="78" stroke-opacity="0" stroke-width="1" width="290" x="36" y="34"/></g>
</g>
</svg>