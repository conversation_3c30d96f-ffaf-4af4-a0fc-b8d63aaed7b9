<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1055" id="thSvg" viewBox="0 0 1920 1055" width="1920">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="Breaker:0_0" viewBox="0,0,50,50">
 <use Plane="0" x="14" xlink:href="#terminal" y="7"/>
 <use Plane="0" x="14" xlink:href="#terminal" y="38"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="31" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,14,22)" width="16" x="6" y="7"/>
</symbol>
<symbol id="Breaker:0_1" viewBox="0,0,50,50">
 <use Plane="0" x="14" xlink:href="#terminal" y="7"/>
 <use Plane="0" x="14" xlink:href="#terminal" y="38"/>
 <rect AFMask="2147483647" Plane="0" fill="rgb(0,0,255)" height="31" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,15,22)" width="16" x="7" y="7"/>
</symbol>
<symbol id="Fuse:11111_0" viewBox="0,0,100,100">
 <use Plane="0" x="32" xlink:href="#terminal" y="56"/>
 <use Plane="0" x="35" xlink:href="#terminal" y="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="48" x2="33" y1="16" y2="54"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="30" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(22,41,35)" width="14" x="34" y="20"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="24" x2="48" y1="14" y2="14"/>
</symbol>
<symbol id="Arrester:bn_避雷器11_0" viewBox="0,0,40,20">
 <use Plane="0" x="36" xlink:href="#terminal" y="10"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="10" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,20,10)" width="22" x="9" y="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="29" x2="23" y1="10" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="5" x2="5" y1="6" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="3" x2="3" y1="7" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="22" x2="22" y1="8" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="6" x2="9" y1="10" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="29" x2="35" y1="10" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="22" x2="19" y1="8" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="19" x2="22" y1="10" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1" x2="1" y1="8" y2="12"/>
</symbol>
<symbol id="Status:bn_不变化颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(0,0,255)" height="1" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_不变化颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(85,0,255)" height="1" stroke="rgb(85,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Disconnector:bn_刀闸5_0" viewBox="0,0,20,40">
 <use Plane="0" x="7" xlink:href="#terminal" y="4"/>
 <use Plane="0" x="7" xlink:href="#terminal" y="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="35" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="9" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="1" x2="15" y1="10" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="18" x2="8" y1="10" y2="30"/>
</symbol>
<symbol id="Disconnector:bn_刀闸5_1" viewBox="0,0,20,40">
 <use Plane="0" x="7" xlink:href="#terminal" y="4"/>
 <use Plane="0" x="7" xlink:href="#terminal" y="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="35" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="1" x2="15" y1="10" y2="10"/>
</symbol>
<symbol id="PT:bn_电压互感器05_0" viewBox="0,0,20,60">
 <use Plane="0" x="10" xlink:href="#terminal" y="18"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="10" x2="10" y1="31" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="10" x2="10" y1="19" y2="31"/>
 <circle AFMask="2147483647" Plane="0" cx="10" cy="50" fill="none" r="8" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="3" cy="43" fill="none" r="0" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="10" cy="39" fill="none" r="8" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="5" x2="15" y1="38" y2="38"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="5" x2="15" y1="51" y2="51"/>
</symbol>
<symbol id="Status:bn_非实测颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,85,0)" height="1" stroke="rgb(170,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_非实测颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,85,0)" height="1" stroke="rgb(170,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_工况退出颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,170,255)" height="1" stroke="rgb(170,170,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_工况退出颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,170,255)" height="1" stroke="rgb(170,170,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Transformer2:bn_两卷变压器8_0" viewBox="0,0,50,76">
 <use Plane="0" x="26" xlink:href="#terminal" y="3"/>
 <circle AFMask="2147483647" Plane="0" cx="25" cy="21" fill="none" r="18" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="25" x2="33" y1="21" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="26" x2="18" y1="21" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="25" x2="25" y1="21" y2="31"/>
</symbol>
<symbol id="Transformer2:bn_两卷变压器8_1" viewBox="0,0,50,76">
 <use Plane="1" x="26" xlink:href="#terminal" y="72"/>
 <circle AFMask="2147483647" Plane="1" cx="25" cy="53" fill="none" r="18" stroke="rgb(0,0,255)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 25 47 L 16 60 L 35 60 Z" fill="none" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Status:bn_数据封锁颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,85,0)" height="1" stroke="rgb(255,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_数据封锁颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,85,0)" height="1" stroke="rgb(255,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_越限颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_越限颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="GZP:gg_光子牌1_0" viewBox="0,0,36,36">
 <circle AFMask="2147483647" Plane="0" cx="18" cy="18" fill="rgb(0,255,0)" r="15" stroke="rgb(0,255,0)" stroke-width="1"/>
</symbol>
<symbol id="GZP:gg_光子牌1_1" viewBox="0,0,36,36">
 <circle AFMask="2147483647" Plane="0" cx="18" cy="18" fill="rgb(255,0,0)" r="15" stroke="rgb(255,0,0)" stroke-width="1"/>
</symbol>
<symbol id="Arrester:qj_ocs_blq3_0" viewBox="0,0,16,40">
 <use Plane="0" x="8" xlink:href="#terminal" y="2"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="18" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,8,19)" width="10" x="3" y="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="8" x2="8" y1="28" y2="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="8" x2="8" y1="5" y2="18"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1" x2="15" y1="35" y2="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="4" x2="12" y1="37" y2="37"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="6" x2="10" y1="39" y2="39"/>
 <path AFMask="2147483647" Plane="0" d="M 6 17 L 8 23 L 10 17 Z" fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
</symbol>
<symbol id="Other:电线杆_0" viewBox="0,0,20,22">
 <rect AFMask="2147483647" Plane="0" fill="none" height="15" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,10,13)" width="16" x="2" y="6"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="17" y1="6" y2="20"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="17" y1="21" y2="6"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="10" x2="10" y1="12" y2="1"/>
</symbol>
<symbol id="Other:电线杆_1" viewBox="0,0,20,22">
 <rect AFMask="2147483647" Plane="0" fill="none" height="14" stroke="rgb(53,255,188)" stroke-width="2" transform="rotate(0,9,9)" width="14" x="2" y="2"/>
</symbol>
<symbol id="Other:电线杆_2" viewBox="0,0,20,22">
 <rect AFMask="2147483647" Plane="0" fill="none" height="14" stroke="rgb(255,211,33)" stroke-width="2" transform="rotate(0,9,9)" width="14" x="2" y="2"/>
</symbol>
<symbol id="Other:电线杆_3" viewBox="0,0,20,22">
</symbol>
<symbol id="Other:电线杆_4" viewBox="0,0,20,22">
</symbol>
<symbol id="Other:电线杆_5" viewBox="0,0,20,22">
</symbol>
<symbol id="Other:电线杆_6" viewBox="0,0,20,22">
</symbol>
<symbol id="Other:电线杆_7" viewBox="0,0,20,22">
</symbol>
<symbol id="Other:电线杆_8" viewBox="0,0,20,22">
</symbol>
<symbol id="Other:电线杆_9" viewBox="0,0,20,22">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="17" font-size="17" font-width="26" stroke="rgb(53,255,188)" writing-mode="lr" x="3" xml:space="preserve" y="20">F</text>
 <rect AFMask="2147483647" Plane="0" fill="none" height="14" stroke="rgb(53,255,188)" stroke-width="1" transform="rotate(0,9,9)" width="14" x="2" y="2"/>
</symbol>
<symbol id="Other:电线杆_10" viewBox="0,0,20,22">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="26" stroke="rgb(255,211,33)" writing-mode="lr" x="3" xml:space="preserve" y="20">F</text>
 <rect AFMask="2147483647" Plane="0" fill="none" height="14" stroke="rgb(255,211,33)" stroke-width="1" transform="rotate(0,9,9)" width="14" x="2" y="2"/>
</symbol>
<symbol id="Other:电线杆_11" viewBox="0,0,20,22">
</symbol>
<symbol id="Other:电线杆_12" viewBox="0,0,20,22">
 <rect AFMask="2147483647" Plane="0" fill="none" height="14" stroke="rgb(255,47,82)" stroke-width="1" transform="rotate(0,9,9)" width="14" x="2" y="2"/>
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="15" font-size="15" font-width="15" stroke="rgb(255,47,82)" writing-mode="lr" x="6" xml:space="preserve" y="18">F</text>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="-1" InitShowingPlane="0," fill="rgb(0,0,0)" height="1055" width="1920" x="0" y="0"/>
</g>
<g id="Other_Layer">
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="22" x2="372" y1="1015" y2="1015"/>
 
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="110" x2="110" y1="369" y2="517"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="178" x2="178" y1="523" y2="641"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="110" x2="372" y1="420" y2="420"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="110" x2="372" y1="468" y2="468"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="304" x2="304" y1="245" y2="363"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="236" x2="236" y1="245" y2="363"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="163" x2="163" y1="245" y2="363"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="93" x2="93" y1="245" y2="363"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="23" x2="371" y1="22" y2="22"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="22" x2="22" y1="22" y2="1012"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="374" x2="374" y1="24" y2="1015"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="23" x2="371" y1="156" y2="156"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="23" x2="371" y1="242" y2="242"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="23" x2="371" y1="297" y2="297"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="23" x2="371" y1="364" y2="364"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="23" x2="371" y1="521" y2="521"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="23" x2="371" y1="580" y2="580"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="23" x2="371" y1="644" y2="644"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="24" x2="372" y1="730" y2="730"/>
 <use AFMask="39039" Plane="0" fill="none" height="22" stroke="rgb(0,0,255)" transform="rotate(0,1105,191) scale(1,1) translate(-10,-11)" width="20" x="1105" xlink:href="#Other:电线杆_0" y="191"/>
</g>
<g id="Bus_Layer">
 <g id="30000000">
  <path d="M 892 194 L 1317 194" stroke="rgb(0,0,255)" stroke-width="3"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 892 194 L 1317 194" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="Breaker_Layer">
 <g id="100000012">
  <use class="kv-1" height="50" transform="rotate(0,1104,417) scale(1,1) translate(-14,-23)" width="50" x="1104" xlink:href="#Breaker:0_0" y="417"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1104,417) scale(1,1) translate(-14,-23)" width="50" x="1104" y="417"/></g>
 <g id="100000023">
  <use class="kv-1" height="50" transform="rotate(0,1104,582) scale(1,1) translate(-14,-23)" width="50" x="1104" xlink:href="#Breaker:0_0" y="582"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1104,582) scale(1,1) translate(-14,-23)" width="50" x="1104" y="582"/></g>
</g>
<g id="Disconnector_Layer">
 <g id="101000009">
  <use class="kv-1" height="40" transform="rotate(0,1103,360) scale(1,1) translate(-8,-20)" width="20" x="1103" xlink:href="#Disconnector:bn_刀闸5_0" y="360"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1103,360) scale(1,1) translate(-8,-20)" width="20" x="1103" y="360"/></g>
 <g id="101000026">
  <use class="kv-1" height="40" transform="rotate(0,1103,645) scale(1,1) translate(-8,-20)" width="20" x="1103" xlink:href="#Disconnector:bn_刀闸5_0" y="645"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1103,645) scale(1,1) translate(-8,-20)" width="20" x="1103" y="645"/></g>
</g>
<g id="Transformer2_Layer">
<g id="102000015">
 <g id="1020000150">
  <use class="kv-1" height="76" transform="rotate(0,1103,503) scale(1,1) translate(-26,-38)" width="50" x="1103" xlink:href="#Transformer2:bn_两卷变压器8_0" y="503"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="1020000151">
  <use class="kv-1" height="76" transform="rotate(0,1103,503) scale(1,1) translate(-26,-38)" width="50" x="1103" xlink:href="#Transformer2:bn_两卷变压器8_1" y="503"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <metadata>
  <cge:PSR_Ref ObjectID="-1" ObjectName="-1" Plane="0"/>
 </metadata>
<rect fill="white" height="76" opacity="0" stroke="white" transform="rotate(0,1103,503) scale(1,1) translate(-26,-38)" width="50" x="1103" y="503"/></g>
</g>
<g id="Load_Layer">
 <g id="32000037">
 <path d="M 1103 671 L 1103 740" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 1103 671 L 1103 740" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="PT_Layer">
 <g id="107000112">
  <use class="kv-1" height="60" transform="rotate(272,1167,300) scale(1,1) translate(-10,-19)" width="20" x="1167" xlink:href="#PT:bn_电压互感器05_0" y="300"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(272,1167,300) scale(1,1) translate(-10,-19)" width="20" x="1167" y="300"/></g>
</g>
<g id="GZP_Layer">
 <g id="135000097">
  <use class="kv-1" height="36" transform="rotate(0,260,698) scale(1,1) translate(-18,-18)" width="36" x="260" xlink:href="#GZP:gg_光子牌1_0" y="698"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,260,698) scale(1,1) translate(-18,-18)" width="36" x="260" y="698"/></g>
</g>
<g id="Arrester_Layer">
 <g id="133000044">
  <use class="kv-1" height="40" transform="rotate(0,1077,695) scale(1,1) translate(-8,-3)" width="16" x="1077" xlink:href="#Arrester:qj_ocs_blq3_0" y="695"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1077,695) scale(1,1) translate(-8,-3)" width="16" x="1077" y="695"/></g>
 <g id="133000109">
  <use class="kv-1" height="20" transform="rotate(0,1047,300) scale(1,1) translate(-36,-10)" width="40" x="1047" xlink:href="#Arrester:bn_避雷器11_0" y="300"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1047,300) scale(1,1) translate(-36,-10)" width="40" x="1047" y="300"/></g>
</g>
<g id="Fuse_Layer">
 <g id="131000007">
  <use class="kv-1" height="100" transform="rotate(1,1102,243) scale(0.739,0.643) translate(355.204,100.916)" width="100" x="1102" xlink:href="#Fuse:11111_0" y="243"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="100" opacity="0" stroke="white" transform="rotate(1,1102,243) scale(0.739,0.643) translate(355.204,100.916)" width="100" x="1102" y="243"/></g>
</g>
<g id="Status_Layer">
 <g id="126000092">
  <use class="kv-1" height="40" transform="rotate(0,57,272) scale(0.7,0.7) translate(-5.57143,96.5714)" width="60" x="57" xlink:href="#Status:bn_工况退出颜色显示_0" y="272"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,57,272) scale(0.7,0.7) translate(-5.57143,96.5714)" width="60" x="57" y="272"/></g>
 <g id="126000093">
  <use class="kv-1" height="40" transform="rotate(0,127,272) scale(0.7,0.7) translate(24.4286,96.5714)" width="60" x="127" xlink:href="#Status:bn_不变化颜色显示_0" y="272"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,127,272) scale(0.7,0.7) translate(24.4286,96.5714)" width="60" x="127" y="272"/></g>
 <g id="126000094">
  <use class="kv-1" height="40" transform="rotate(0,197,272) scale(0.7,0.7) translate(54.4286,96.5714)" width="60" x="197" xlink:href="#Status:bn_越限颜色显示_0" y="272"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,197,272) scale(0.7,0.7) translate(54.4286,96.5714)" width="60" x="197" y="272"/></g>
 <g id="126000095">
  <use class="kv-1" height="40" transform="rotate(0,270,272) scale(0.7,0.7) translate(85.7143,96.5714)" width="60" x="270" xlink:href="#Status:bn_非实测颜色显示_0" y="272"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,270,272) scale(0.7,0.7) translate(85.7143,96.5714)" width="60" x="270" y="272"/></g>
 <g id="126000096">
  <use class="kv-1" height="40" transform="rotate(0,337,272) scale(0.7,0.7) translate(114.429,96.5714)" width="60" x="337" xlink:href="#Status:bn_数据封锁颜色显示_0" y="272"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,337,272) scale(0.7,0.7) translate(114.429,96.5714)" width="60" x="337" y="272"/></g>
</g>
<g id="Clock_Layer">
 <g id="56000091">
  
 <metadata/></g>
</g>
<g id="Link_Layer">
 <g id="34000008">
 <path d="M 1105 229 L 1105 194" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="131000007_1" Pin1InfoVect0LinkObjId="30000000_0" Plane="0"/>
  </metadata>
 <path d="M 1105 229 L 1105 194" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000030">
 <path d="M 1103 375 L 1103 401" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000009_1" Pin1InfoVect0LinkObjId="100000012_0" Plane="0"/>
  </metadata>
 <path d="M 1103 375 L 1103 401" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000031">
 <path d="M 1103 432 L 1103 468" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000012_1" Pin1InfoVect0LinkObjId="102000015_0" Plane="0"/>
  </metadata>
 <path d="M 1103 432 L 1103 468" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000032">
 <path d="M 1103 537 L 1103 566" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="102000015_1" Pin1InfoVect0LinkObjId="100000023_0" Plane="0"/>
  </metadata>
 <path d="M 1103 537 L 1103 566" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000033">
 <path d="M 1103 597 L 1103 630" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000023_1" Pin1InfoVect0LinkObjId="101000026_0" Plane="0"/>
  </metadata>
 <path d="M 1103 597 L 1103 630" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000042">
 <path d="M 1103 660 L 1103 671" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000026_1" Pin1InfoVect0LinkObjId="34000048_0" Pin1InfoVect1LinkObjId="32000037_0" Plane="0"/>
  </metadata>
 <path d="M 1103 660 L 1103 671" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000047">
 <path d="M 1077 695 L 1077 682 L 1103 682" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133000044_0" Pin1InfoVect0LinkObjId="34000048_1" Pin1InfoVect1LinkObjId="34000049_0" Plane="0"/>
  </metadata>
 <path d="M 1077 695 L 1077 682 L 1103 682" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000048">
 <path d="M 1103 671 L 1103 682" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="32000037_0" Pin0InfoVect1LinkObjId="34000042_1" Pin1InfoVect0LinkObjId="34000047_1" Pin1InfoVect1LinkObjId="34000049_0" Plane="0"/>
  </metadata>
 <path d="M 1103 671 L 1103 682" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000049">
 <path d="M 1103 682 L 1103 704" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000048_1" Pin0InfoVect1LinkObjId="34000047_1" Plane="0"/>
  </metadata>
 <path d="M 1103 682 L 1103 704" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000115">
 <path d="M 1047 300 L 1103 300" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133000109_0" Pin1InfoVect0LinkObjId="34000118_1" Pin1InfoVect1LinkObjId="34000116_1" Pin1InfoVect2LinkObjId="34000117_0" Plane="0"/>
  </metadata>
 <path d="M 1047 300 L 1103 300" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000116">
 <path d="M 1103 258 L 1103 300" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="131000007_0" Pin1InfoVect0LinkObjId="34000118_1" Pin1InfoVect1LinkObjId="34000115_1" Pin1InfoVect2LinkObjId="34000117_0" Plane="0"/>
  </metadata>
 <path d="M 1103 258 L 1103 300" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000117">
 <path d="M 1103 300 L 1103 345" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000118_1" Pin0InfoVect1LinkObjId="34000115_1" Pin0InfoVect2LinkObjId="34000116_1" Pin1InfoVect0LinkObjId="101000009_0" Plane="0"/>
  </metadata>
 <path d="M 1103 300 L 1103 345" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000118">
 <path d="M 1168 300 L 1103 300" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="107000112_0" Pin1InfoVect0LinkObjId="34000115_1" Pin1InfoVect1LinkObjId="34000116_1" Pin1InfoVect2LinkObjId="34000117_0" Plane="0"/>
  </metadata>
 <path d="M 1168 300 L 1103 300" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="MeasurementValue_Layer">
 <g id="33000089">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="26" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="232" xml:space="preserve" y="577">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000090">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="26" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="233" xml:space="preserve" y="628">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
</g>
<g id="Text_Layer">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1020" xml:space="preserve" y="172">35kV远腊线N35塔</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1113" xml:space="preserve" y="370">3011</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1118" xml:space="preserve" y="427">301</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1118" xml:space="preserve" y="592">101</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1113" xml:space="preserve" y="655">1011</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="13" font-width="13" stroke="rgb(255,255,254)" writing-mode="lr" x="1129" xml:space="preserve" y="253">3001G</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1070" xml:space="preserve" y="782">龙林线</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="60" font-size="24" font-width="24" stroke="rgb(0,0,0)" writing-mode="lr" x="138" xml:space="preserve" y="122">35kV龙林简易变电站</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="tb" x="68" xml:space="preserve" y="407">单</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,0,0)" writing-mode="lr" x="190" xml:space="preserve" y="418">MW</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="62" xml:space="preserve" y="576">总有功</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="102" xml:space="preserve" y="714">事故总:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="123" xml:space="preserve" y="418">有功:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="228" xml:space="preserve" y="418"> 无功:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,0,0)" writing-mode="lr" x="311" xml:space="preserve" y="418">MVr</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="123" xml:space="preserve" y="464">电流:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,0,0)" writing-mode="lr" x="190" xml:space="preserve" y="464">A</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="240" xml:space="preserve" y="464">电压:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,0,0)" writing-mode="lr" x="311" xml:space="preserve" y="464">kV</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="123" xml:space="preserve" y="514">频率:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,0,0)" writing-mode="lr" x="190" xml:space="preserve" y="514">Hz</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="240" xml:space="preserve" y="514">温度:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,0,0)" writing-mode="lr" x="311" xml:space="preserve" y="515">℃</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="57" xml:space="preserve" y="631">总无功</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="tb" x="68" xml:space="preserve" y="476">位</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="34" xml:space="preserve" y="332">工况</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="34" xml:space="preserve" y="355">退出</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="117" xml:space="preserve" y="332">不</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="106" xml:space="preserve" y="357">变化</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="174" xml:space="preserve" y="346">越限</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="257" xml:space="preserve" y="332">非</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="245" xml:space="preserve" y="357">实测</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="315" xml:space="preserve" y="334">数据</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="315" xml:space="preserve" y="357">封锁</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1137" xml:space="preserve" y="508">(S=4WVA)</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href="农垦电网new.sys.svg"><rect fill-opacity="0" height="112" stroke-opacity="0" stroke-width="2" width="337" x="35" y="30"/></g>
</g>
</svg>