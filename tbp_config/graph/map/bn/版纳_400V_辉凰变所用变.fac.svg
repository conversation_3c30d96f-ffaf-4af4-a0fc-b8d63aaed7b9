<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="113997365567815891" height="1055" id="thSvg" viewBox="0 0 1920 1055" width="1920">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="Breaker:bn_断路器2_0" viewBox="0,0,20,40">
 <use Plane="0" x="10" xlink:href="#terminal" y="4"/>
 <use Plane="0" x="10" xlink:href="#terminal" y="36"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="30" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,10,20)" width="14" x="3" y="5"/>
</symbol>
<symbol id="Breaker:bn_断路器2_1" viewBox="0,0,20,40">
 <use Plane="0" x="10" xlink:href="#terminal" y="4"/>
 <use Plane="0" x="10" xlink:href="#terminal" y="36"/>
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,254)" height="30" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,10,20)" width="14" x="3" y="5"/>
</symbol>
<symbol id="Transformer2:bn_两卷变压器003_0" viewBox="0,0,34,64">
 <use Plane="0" x="17" xlink:href="#terminal" y="4"/>
 <circle AFMask="2147483647" Plane="0" cx="17" cy="20" fill="none" r="15" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="17" x2="24" y1="19" y2="26"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="17" x2="10" y1="19" y2="26"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="17" x2="17" y1="11" y2="19"/>
</symbol>
<symbol id="Transformer2:bn_两卷变压器003_1" viewBox="0,0,34,64">
 <use Plane="1" x="16" xlink:href="#terminal" y="59"/>
 <circle AFMask="2147483647" Plane="1" cx="17" cy="44" fill="none" r="15" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="17" x2="17" y1="43" y2="35"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="17" x2="24" y1="43" y2="50"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="17" x2="10" y1="43" y2="50"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="-1" InitShowingPlane="0," fill="rgb(0,0,0)" height="1055" width="1920" x="0" y="0"/>
</g>
<g id="Other_Layer">
 
</g>
<g id="Bus_Layer">
 <g id="30000034">
  <path d="M 194 963 L 819 963" stroke="rgb(0,0,255)" stroke-width="6"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115404740451369690" ObjectName="版纳_110kV_辉凰变\版纳_110kV_辉凰变/0.4kV\BS_0.4kVI母" Plane="0"/>
  <cge:TPSR_Ref TObjectID="115404740451369690"/></metadata>
 <path d="M 194 963 L 819 963" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="30000035">
  <path d="M 1002 963 L 1627 963" stroke="rgb(0,0,255)" stroke-width="6"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115404740451369691" ObjectName="版纳_110kV_辉凰变\版纳_110kV_辉凰变/0.4kV\BS_0.4kVII母" Plane="0"/>
  <cge:TPSR_Ref TObjectID="115404740451369691"/></metadata>
 <path d="M 1002 963 L 1627 963" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="Breaker_Layer">
 <g id="100000027">
  <use class="kv-1" height="40" transform="rotate(0,441,818) scale(2,2) translate(-230.5,-429)" width="20" x="441" xlink:href="#Breaker:bn_断路器2_0" y="818"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,441,818) scale(2,2) translate(-230.5,-429)" width="20" x="441" y="818"/></g>
 <g id="100000029">
  <use class="kv-1" height="40" transform="rotate(0,612,818) scale(2,2) translate(-316,-429)" width="20" x="612" xlink:href="#Breaker:bn_断路器2_0" y="818"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,612,818) scale(2,2) translate(-316,-429)" width="20" x="612" y="818"/></g>
 <g id="100000033">
  <use class="kv-1" height="40" transform="rotate(0,1370,818) scale(2,2) translate(-695,-429)" width="20" x="1370" xlink:href="#Breaker:bn_断路器2_0" y="818"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1370,818) scale(2,2) translate(-695,-429)" width="20" x="1370" y="818"/></g>
 <g id="100000032">
  <use class="kv-1" height="40" transform="rotate(0,1199,818) scale(2,2) translate(-609.5,-429)" width="20" x="1199" xlink:href="#Breaker:bn_断路器2_0" y="818"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1199,818) scale(2,2) translate(-609.5,-429)" width="20" x="1199" y="818"/></g>
</g>
<g id="Transformer2_Layer">
<g id="102000016">
 <g id="1020000160">
  <use class="kv10kV" height="64" transform="rotate(0,442,416) scale(2.5,2.58) translate(-282.2,-286.76)" width="34" x="442" xlink:href="#Transformer2:bn_两卷变压器003_0" y="416"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="117375065288344380" ObjectName="版纳_110kV_辉凰变\版纳_110kV_辉凰变/10kV\XF_1号站用变-高" Plane="0"/>
  </metadata>
 </g>
 <g id="1020000161">
  <use class="kv0" height="64" transform="rotate(0,442,416) scale(2.5,2.58) translate(-282.2,-286.76)" width="34" x="442" xlink:href="#Transformer2:bn_两卷变压器003_1" y="416"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="117375065288344381" ObjectName="版纳_110kV_辉凰变\版纳_110kV_辉凰变/0.4kV\XF_1号站用变-低" Plane="0"/>
  </metadata>
 </g>
 <metadata>
  <cge:PSR_Ref ObjectID="117093590311633244" ObjectName="版纳_110kV_辉凰变\XFMR_1号站用变" Plane="0"/>
 <cge:TPSR_Ref TObjectID="117093590311633244"/></metadata>
<rect fill="white" height="64" opacity="0" stroke="white" transform="rotate(0,442,416) scale(2.5,2.58) translate(-282.2,-286.76)" width="34" x="442" y="416"/></g>
<g id="102000025">
 <g id="1020000250">
  <use class="kv10kV" height="64" transform="rotate(0,1371,416) scale(2.5,2.58) translate(-839.6,-286.76)" width="34" x="1371" xlink:href="#Transformer2:bn_两卷变压器003_0" y="416"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="117375065288344382" ObjectName="版纳_110kV_辉凰变\版纳_110kV_辉凰变/10kV\XF_2号站用变-高" Plane="0"/>
  </metadata>
 </g>
 <g id="1020000251">
  <use class="kv0" height="64" transform="rotate(0,1371,416) scale(2.5,2.58) translate(-839.6,-286.76)" width="34" x="1371" xlink:href="#Transformer2:bn_两卷变压器003_1" y="416"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="117375065288344383" ObjectName="版纳_110kV_辉凰变\版纳_110kV_辉凰变/0.4kV\XF_2号站用变-低" Plane="0"/>
  </metadata>
 </g>
 <metadata>
  <cge:PSR_Ref ObjectID="117093590311633245" ObjectName="版纳_110kV_辉凰变\XFMR_2号站用变" Plane="0"/>
 <cge:TPSR_Ref TObjectID="117093590311633245"/></metadata>
<rect fill="white" height="64" opacity="0" stroke="white" transform="rotate(0,1371,416) scale(2.5,2.58) translate(-839.6,-286.76)" width="34" x="1371" y="416"/></g>
</g>
<g id="Link_Layer">
 <g id="34000037">
 <path d="M 441 850 L 441 963" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000027_1" Pin1InfoVect0LinkObjId="30000034_0" Plane="0"/>
  </metadata>
 <path d="M 441 850 L 441 963" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000038">
 <path d="M 612 850 L 612 963" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000029_1" Pin1InfoVect0LinkObjId="30000034_0" Plane="0"/>
  </metadata>
 <path d="M 612 850 L 612 963" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000039">
 <path d="M 1199 850 L 1199 963" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000032_1" Pin1InfoVect0LinkObjId="30000035_0" Plane="0"/>
  </metadata>
 <path d="M 1199 850 L 1199 963" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000040">
 <path d="M 1370 850 L 1370 963" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000033_1" Pin1InfoVect0LinkObjId="30000035_0" Plane="0"/>
  </metadata>
 <path d="M 1370 850 L 1370 963" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000042">
 <path d="M 441 697 L 1199 697 L 1199 786" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000043_1" Pin0InfoVect1LinkObjId="34000044_0" Pin1InfoVect0LinkObjId="100000032_0" Plane="0"/>
  </metadata>
 <path d="M 441 697 L 1199 697 L 1199 786" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000043">
 <path d="M 441 486 L 441 697" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="102000016_1" Pin1InfoVect0LinkObjId="34000042_0" Pin1InfoVect1LinkObjId="34000044_0" Plane="0"/>
  </metadata>
 <path d="M 441 486 L 441 697" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000044">
 <path d="M 441 697 L 441 786" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000043_1" Pin0InfoVect1LinkObjId="34000042_0" Pin1InfoVect0LinkObjId="100000027_0" Plane="0"/>
  </metadata>
 <path d="M 441 697 L 441 786" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000045">
 <path d="M 612 786 L 612 627 L 1370 627" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000029_0" Pin1InfoVect0LinkObjId="34000047_0" Pin1InfoVect1LinkObjId="34000046_1" Plane="0"/>
  </metadata>
 <path d="M 612 786 L 612 627 L 1370 627" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000046">
 <path d="M 1370 486 L 1370 627" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="102000025_1" Pin1InfoVect0LinkObjId="34000047_0" Pin1InfoVect1LinkObjId="34000045_1" Plane="0"/>
  </metadata>
 <path d="M 1370 486 L 1370 627" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000047">
 <path d="M 1370 627 L 1370 786" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000045_1" Pin0InfoVect1LinkObjId="34000046_1" Pin1InfoVect0LinkObjId="100000033_0" Plane="0"/>
  </metadata>
 <path d="M 1370 627 L 1370 786" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="MeasurementValue_Layer">
 <g id="33000000">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(255,255,0)" writing-mode="lr" x="580" xml:space="preserve" y="286">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795527171" ObjectName="122723089845854211:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33000001">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(255,255,0)" writing-mode="lr" x="580" xml:space="preserve" y="318">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795527172" ObjectName="122723089845854212:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33000002">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(255,255,0)" writing-mode="lr" x="580" xml:space="preserve" y="445">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795527177" ObjectName="122723089845854217:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33000003">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(255,255,0)" writing-mode="lr" x="580" xml:space="preserve" y="478">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795527178" ObjectName="122723089845854218:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33000004">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(255,255,0)" writing-mode="lr" x="580" xml:space="preserve" y="510">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795527179" ObjectName="122723089845854219:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33000005">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(255,255,0)" writing-mode="lr" x="580" xml:space="preserve" y="542">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795527173" ObjectName="122723089845854213:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33000012">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(255,255,0)" writing-mode="lr" x="580" xml:space="preserve" y="349">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795527168" ObjectName="122723089845854208:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33000013">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(255,255,0)" writing-mode="lr" x="580" xml:space="preserve" y="412">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795527170" ObjectName="122723089845854210:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33000072">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(255,255,0)" writing-mode="lr" x="580" xml:space="preserve" y="381">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795527169" ObjectName="122723089845854209:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33000100">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(255,255,0)" writing-mode="lr" x="1521" xml:space="preserve" y="286">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795527183" ObjectName="122723089845854223:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33000101">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(255,255,0)" writing-mode="lr" x="1521" xml:space="preserve" y="318">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795527184" ObjectName="122723089845854224:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33000102">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(255,255,0)" writing-mode="lr" x="1521" xml:space="preserve" y="445">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795527189" ObjectName="122723089845854229:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33000103">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(255,255,0)" writing-mode="lr" x="1521" xml:space="preserve" y="478">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795527190" ObjectName="122723089845854230:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33000104">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(255,255,0)" writing-mode="lr" x="1521" xml:space="preserve" y="510">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795527191" ObjectName="122723089845854231:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33000105">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(255,255,0)" writing-mode="lr" x="1521" xml:space="preserve" y="542">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795527185" ObjectName="122723089845854225:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33000106">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(255,255,0)" writing-mode="lr" x="1521" xml:space="preserve" y="349">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795527180" ObjectName="122723089845854220:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33000107">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(255,255,0)" writing-mode="lr" x="1521" xml:space="preserve" y="412">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795527182" ObjectName="122723089845854222:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33000108">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(255,255,0)" writing-mode="lr" x="1521" xml:space="preserve" y="381">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795527181" ObjectName="122723089845854221:M" Plane="0"/>
  </metadata>
 </g>
</g>
<g id="Text_Layer">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="39" stroke="rgb(255,255,254)" writing-mode="lr" x="468" xml:space="preserve" y="837">11QF</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="39" stroke="rgb(255,255,254)" writing-mode="lr" x="639" xml:space="preserve" y="837">12QF</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="39" stroke="rgb(255,255,254)" writing-mode="lr" x="1397" xml:space="preserve" y="837">22QF</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="39" stroke="rgb(255,255,254)" writing-mode="lr" x="1226" xml:space="preserve" y="837">21QF</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="39" stroke="rgb(255,255,254)" writing-mode="lr" x="118" xml:space="preserve" y="939">0.4kVI母</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="39" stroke="rgb(255,255,254)" writing-mode="lr" x="1550" xml:space="preserve" y="939">0.4kVII母</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="39" stroke="rgb(255,255,254)" writing-mode="lr" x="217" xml:space="preserve" y="438">#1站用变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="39" stroke="rgb(255,255,254)" writing-mode="lr" x="1139" xml:space="preserve" y="438">#2站用变</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="66" font-size="45" font-width="45" stroke="rgb(0,0,0)" writing-mode="lr" x="890" xml:space="preserve" y="135">辉凰变所用变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(36,169,128)" writing-mode="lr" x="521" xml:space="preserve" y="286">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(36,169,128)" writing-mode="lr" x="521" xml:space="preserve" y="318">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(36,169,128)" writing-mode="lr" x="521" xml:space="preserve" y="510">Uca</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(36,169,128)" writing-mode="lr" x="521" xml:space="preserve" y="445">Uab</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(36,169,128)" writing-mode="lr" x="521" xml:space="preserve" y="478">Ubc</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(36,169,128)" writing-mode="lr" x="521" xml:space="preserve" y="542">Cs</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(36,169,128)" writing-mode="lr" x="521" xml:space="preserve" y="349">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(36,169,128)" writing-mode="lr" x="521" xml:space="preserve" y="412">Ic</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(36,169,128)" writing-mode="lr" x="521" xml:space="preserve" y="381">Ib</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(36,169,128)" writing-mode="lr" x="1462" xml:space="preserve" y="286">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(36,169,128)" writing-mode="lr" x="1462" xml:space="preserve" y="318">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(36,169,128)" writing-mode="lr" x="1462" xml:space="preserve" y="510">Uca</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(36,169,128)" writing-mode="lr" x="1462" xml:space="preserve" y="445">Uab</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(36,169,128)" writing-mode="lr" x="1462" xml:space="preserve" y="478">Ubc</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(36,169,128)" writing-mode="lr" x="1462" xml:space="preserve" y="542">Cs</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(36,169,128)" writing-mode="lr" x="1462" xml:space="preserve" y="349">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(36,169,128)" writing-mode="lr" x="1462" xml:space="preserve" y="412">Ic</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(36,169,128)" writing-mode="lr" x="1462" xml:space="preserve" y="381">Ib</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href="版纳_110kV_辉凰变.fac.svg"><rect fill-opacity="0" height="123" stroke-opacity="0" stroke-width="1" width="462" x="716" y="45"/></g>
</g>
</svg>