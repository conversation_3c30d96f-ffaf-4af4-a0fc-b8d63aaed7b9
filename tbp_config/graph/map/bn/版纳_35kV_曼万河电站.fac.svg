<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1039" id="thSvg" viewBox="0 0 1920 1039" width="1920">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="PT:#5开闭所_0" viewBox="0,0,50,50">
 <use Plane="0" x="24" xlink:href="#terminal" y="6"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="24" x2="24" y1="5" y2="27"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="21" x2="21" y1="12" y2="25"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="21" x2="27" y1="25" y2="25"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="21" x2="27" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="27" x2="27" y1="12" y2="25"/>
 <circle AFMask="2147483647" Plane="0" cx="21.5" cy="32.5" fill="none" r="5.5" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="30.5" cy="31.5" fill="none" r="5.5" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="21.5" cy="41.5" fill="none" r="5.5" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="30.5" cy="41.5" fill="none" r="5.5" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="19" x2="21" y1="29" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="24" x2="22" y1="29" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="22" x2="22" y1="31" y2="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="28" x2="30" y1="29" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="31" y1="29" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="31" y1="31" y2="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="19" x2="21" y1="40" y2="42"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="23" x2="21" y1="40" y2="42"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="21" x2="21" y1="42" y2="45"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="30" x2="30" y1="40" y2="45"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="32" y1="40" y2="41"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="32" x2="31" y1="43" y2="44"/>
</symbol>
<symbol id="Breaker:0_0" viewBox="0,0,50,50">
 <use Plane="0" x="14" xlink:href="#terminal" y="7"/>
 <use Plane="0" x="14" xlink:href="#terminal" y="38"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="31" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,14,22)" width="16" x="6" y="7"/>
</symbol>
<symbol id="Breaker:0_1" viewBox="0,0,50,50">
 <use Plane="0" x="14" xlink:href="#terminal" y="7"/>
 <use Plane="0" x="14" xlink:href="#terminal" y="38"/>
 <rect AFMask="2147483647" Plane="0" fill="rgb(0,0,255)" height="31" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,15,22)" width="16" x="7" y="7"/>
</symbol>
<symbol id="Arrester:bn_避雷器4_0" viewBox="0,0,20,40">
 <use Plane="0" x="10" xlink:href="#terminal" y="35"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="22" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,10,20)" width="10" x="5" y="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="5" y2="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="21" y2="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="5" x2="15" y1="5" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="7" x2="13" y1="3" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="11" y1="1" y2="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="8" x2="12" y1="20" y2="20"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="12" x2="10" y1="20" y2="17"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="8" y1="17" y2="20"/>
</symbol>
<symbol id="Status:bn_不变化颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(0,0,255)" height="1" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_不变化颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(85,0,255)" height="1" stroke="rgb(85,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Disconnector:bn_刀闸17_0" viewBox="0,0,10,16">
 <use Plane="0" x="2" xlink:href="#terminal" y="8"/>
 <use Plane="0" x="9" xlink:href="#terminal" y="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="5" x2="2" y1="2" y2="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="5" y1="8" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="6" y1="2" y2="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="6" x2="9" y1="8" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="6" x2="9" y1="8" y2="8"/>
</symbol>
<symbol id="Disconnector:bn_刀闸17_1" viewBox="0,0,10,16">
 <use Plane="0" x="2" xlink:href="#terminal" y="8"/>
 <use Plane="0" x="9" xlink:href="#terminal" y="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="5" x2="2" y1="2" y2="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="5" y1="8" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="6" x2="3" y1="2" y2="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="6" y1="8" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="9" y1="8" y2="8"/>
</symbol>
<symbol id="Disconnector:bn_刀闸18_0" viewBox="0,0,16,10">
 <use Plane="0" x="8" xlink:href="#terminal" y="2"/>
 <use Plane="0" x="8" xlink:href="#terminal" y="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="8" y1="5" y2="2"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="14" y1="2" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="8" y1="9" y2="6"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="14" y1="6" y2="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="6" y2="9"/>
</symbol>
<symbol id="Disconnector:bn_刀闸18_1" viewBox="0,0,16,10">
 <use Plane="0" x="8" xlink:href="#terminal" y="2"/>
 <use Plane="0" x="8" xlink:href="#terminal" y="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="8" y1="5" y2="2"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="14" y1="2" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="8" y1="6" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="14" y1="3" y2="6"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="3" y2="9"/>
</symbol>
<symbol id="Disconnector:bn_刀闸3_0" viewBox="0,0,20,40">
 <use Plane="0" x="7" xlink:href="#terminal" y="3"/>
 <use Plane="0" x="7" xlink:href="#terminal" y="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="35" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="7" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="1" x2="15" y1="31" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="18" y1="7" y2="31"/>
</symbol>
<symbol id="Disconnector:bn_刀闸3_1" viewBox="0,0,20,40">
 <use Plane="0" x="7" xlink:href="#terminal" y="3"/>
 <use Plane="0" x="7" xlink:href="#terminal" y="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="35" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="1" x2="15" y1="31" y2="31"/>
</symbol>
<symbol id="Status:bn_非实测颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,85,0)" height="1" stroke="rgb(170,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_非实测颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,85,0)" height="1" stroke="rgb(170,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_工况退出颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,170,255)" height="1" stroke="rgb(170,170,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_工况退出颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,170,255)" height="1" stroke="rgb(170,170,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸111_0" viewBox="0,0,34,16">
 <use Plane="0" x="31" xlink:href="#terminal" y="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="27" x2="27" y1="3" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="16" x2="6" y1="8" y2="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="3" y1="4" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="1" x2="1" y1="6" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="30" x2="27" y1="8" y2="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="5" x2="5" y1="3" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="16" x2="25" y1="8" y2="2"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸111_1" viewBox="0,0,34,16">
 <use Plane="0" x="31" xlink:href="#terminal" y="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="27" x2="27" y1="3" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="30" x2="6" y1="8" y2="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="3" y1="4" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="1" x2="1" y1="6" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="5" x2="5" y1="3" y2="13"/>
</symbol>
<symbol id="Transformer2:bn_两卷变压器8_0" viewBox="0,0,50,76">
 <use Plane="0" x="26" xlink:href="#terminal" y="3"/>
 <circle AFMask="2147483647" Plane="0" cx="25" cy="21" fill="none" r="18" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="25" x2="33" y1="21" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="26" x2="18" y1="21" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="25" x2="25" y1="21" y2="31"/>
</symbol>
<symbol id="Transformer2:bn_两卷变压器8_1" viewBox="0,0,50,76">
 <use Plane="1" x="26" xlink:href="#terminal" y="72"/>
 <circle AFMask="2147483647" Plane="1" cx="25" cy="53" fill="none" r="18" stroke="rgb(0,0,255)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 25 47 L 16 60 L 35 60 Z" fill="none" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Status:bn_数据封锁颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,85,0)" height="1" stroke="rgb(255,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_数据封锁颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,85,0)" height="1" stroke="rgb(255,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_0_0" viewBox="0,0,20,74">
 <rect AFMask="2147483647" Plane="0" fill="none" height="0" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,4,16)" width="0" x="4" y="16"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="30" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,9,37)" width="13" x="3" y="22"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_0_1" viewBox="0,0,20,74">
 <rect AFMask="2147483647" Plane="0" fill="none" height="30" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,9,36)" width="13" x="3" y="21"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_0_2" viewBox="0,0,20,74">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,254)" height="30" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,9,36)" width="13" x="3" y="21"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_0_3" viewBox="0,0,20,74">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,254)" height="30" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,9,37)" width="13" x="3" y="22"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_1_0" viewBox="0,0,20,74">
 <use Plane="1" x="9" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="9" xlink:href="#terminal" y="69"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="21" y2="10"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="52" y2="63"/>
 <path AFMask="2147483647" Plane="1" d="M 3 11 L 10 5 L 17 12 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 3 57 L 9 64 L 16 57 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 3 15 L 10 9 L 17 16 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 3 61 L 9 68 L 16 61 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_1_1" viewBox="0,0,20,74">
 <use Plane="1" x="9" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="9" xlink:href="#terminal" y="69"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="21" y2="9"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="52" y2="65"/>
 <path AFMask="2147483647" Plane="1" d="M 4 12 L 10 6 L 16 12 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
 <path AFMask="2147483647" Plane="1" d="M 4 61 L 10 67 L 16 61 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_1_2" viewBox="0,0,20,74">
 <use Plane="1" x="9" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="9" xlink:href="#terminal" y="69"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="22" y2="10"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="52" y2="63"/>
 <path AFMask="2147483647" Plane="1" d="M 3 11 L 9 4 L 16 11 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 3 15 L 9 8 L 16 15 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 2 57 L 9 63 L 16 56 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 2 61 L 9 67 L 16 60 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_1_3" viewBox="0,0,20,74">
 <use Plane="1" x="9" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="9" xlink:href="#terminal" y="69"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="21" y2="8"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="52" y2="65"/>
 <path AFMask="2147483647" Plane="1" d="M 3 11 L 9 5 L 15 11 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
 <path AFMask="2147483647" Plane="1" d="M 3 60 L 9 66 L 15 60 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
</symbol>
<symbol id="Status:bn_越限颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_越限颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Terminal:bn_终端设备10_0" viewBox="0,0,38,12">
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,255,0)" stroke-width="1" x1="2" x2="7" y1="6" y2="6"/>
 <polygon AFMask="2147483647" Plane="0" fill="none" points="7,3 14,5 7,8" stroke="rgb(0,255,0)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,255,0)" stroke-width="1" x1="12" x2="22" y1="6" y2="6"/>
 <polygon AFMask="2147483647" Plane="0" fill="none" points="21,5 28,3 28,8" stroke="rgb(0,255,0)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,255,0)" stroke-width="1" x1="27" x2="32" y1="6" y2="6"/>
</symbol>
<symbol id="Terminal:bn_终端设备12_0" viewBox="0,0,50,100">
 <use Plane="0" x="32" xlink:href="#terminal" y="3"/>
 <circle AFMask="2147483647" Plane="0" cx="32" cy="29" fill="none" r="15" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="32" cy="56" fill="none" r="15" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="32" x2="32" y1="56" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="22" x2="32" y1="62" y2="56"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="42" x2="32" y1="62" y2="56"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="22" x2="32" y1="30" y2="36"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="42" x2="32" y1="30" y2="36"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="32" x2="32" y1="71" y2="95"/>
 <polygon AFMask="2147483647" Plane="0" fill="none" points="27,81 32,86 37,81" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="32" x2="7" y1="56" y2="56"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="10" x2="2" y1="84" y2="84"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="4" y1="86" y2="86"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="6" x2="6" y1="88" y2="88"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="6" x2="6" y1="56" y2="84"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="32" x2="6" y1="81" y2="67"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="32" x2="32" y1="14" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="30" x2="34" y1="97" y2="97"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="33" y1="99" y2="99"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="22" x2="42" y1="29" y2="29"/>
</symbol>
<symbol id="PT:gd_两卷01_0" viewBox="0,0,20,60">
 <use Plane="0" x="10" xlink:href="#terminal" y="4"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="16" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,10,17)" width="10" x="5" y="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="10" x2="10" y1="31" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="6" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="6" y1="38" y2="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="14" y1="39" y2="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="14" y1="53" y2="48"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="6" y1="52" y2="48"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="38" y2="41"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="52" y2="56"/>
 <circle AFMask="2147483647" Plane="0" cx="10" cy="50" fill="none" r="8" stroke="rgb(255,255,254)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="3" cy="43" fill="none" r="0" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="10" cy="39" fill="none" r="8" stroke="rgb(255,255,254)" stroke-width="1"/>
</symbol>
<symbol id="Fuse:gd_熔丝_0" viewBox="0,0,16,32">
 <use Plane="0" x="8" xlink:href="#terminal" y="3"/>
 <use Plane="0" x="8" xlink:href="#terminal" y="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="8" x2="8" y1="2" y2="28"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="17" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,8,15)" width="12" x="2" y="7"/>
</symbol>
<symbol id="PT:gd_三圈带熔断1_0" viewBox="0,0,26,56">
 <use Plane="0" x="8" xlink:href="#terminal" y="4"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="14" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,8,18)" width="8" x="4" y="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="6" x2="6" y1="47" y2="47"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="6" x2="6" y1="47" y2="47"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="17" x2="17" y1="38" y2="45"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="17" x2="20" y1="38" y2="41"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="17" x2="20" y1="45" y2="42"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="5" x2="8" y1="45" y2="48"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="8" x2="11" y1="48" y2="45"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="8" x2="8" y1="48" y2="52"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="8" x2="8" y1="36" y2="40"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="8" x2="11" y1="36" y2="33"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="5" x2="8" y1="33" y2="36"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="8" x2="8" y1="4" y2="30"/>
 <circle AFMask="2147483647" Plane="0" cx="8" cy="36" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="8" cy="47" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="17" cy="42" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
</symbol>
<symbol id="GZP:gg_光子牌1_0" viewBox="0,0,36,36">
 <circle AFMask="2147483647" Plane="0" cx="18" cy="18" fill="rgb(0,255,0)" r="15" stroke="rgb(0,255,0)" stroke-width="1"/>
</symbol>
<symbol id="GZP:gg_光子牌1_1" viewBox="0,0,36,36">
 <circle AFMask="2147483647" Plane="0" cx="18" cy="18" fill="rgb(255,0,0)" r="15" stroke="rgb(255,0,0)" stroke-width="1"/>
</symbol>
<symbol id="SynchronousMachine:ocs_发电机1_0" viewBox="0,0,50,50">
 <use Plane="0" x="25" xlink:href="#terminal" y="3"/>
 <circle AFMask="2147483647" Plane="0" cx="25" cy="25" fill="none" r="22" stroke="rgb(0,0,255)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="0" d="M 25 25 A 11 9 0 1 0 3 25" fill="none" stroke="rgb(0,0,255)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="0" d="M 25 25 A 11 9 0 1 0 47 25" fill="none" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="DollyBreaker:qj_ocs_xckg3_0_0" viewBox="0,0,20,70">
 <rect AFMask="2147483647" Plane="0" fill="none" height="26" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,10,34)" width="12" x="4" y="21"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="0" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,4,14)" width="0" x="4" y="14"/>
</symbol>
<symbol id="DollyBreaker:qj_ocs_xckg3_0_1" viewBox="0,0,20,70">
 <rect AFMask="2147483647" Plane="0" fill="none" height="29" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,10,34)" width="12" x="4" y="20"/>
</symbol>
<symbol id="DollyBreaker:qj_ocs_xckg3_0_2" viewBox="0,0,20,70">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,254)" height="27" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,10,34)" width="12" x="4" y="21"/>
</symbol>
<symbol id="DollyBreaker:qj_ocs_xckg3_0_3" viewBox="0,0,20,70">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,254)" height="27" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,10,34)" width="12" x="4" y="21"/>
</symbol>
<symbol id="DollyBreaker:qj_ocs_xckg3_1_0" viewBox="0,0,20,70">
 <use Plane="1" x="10" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="10" xlink:href="#terminal" y="66"/>
 <path AFMask="2147483647" Plane="1" d="M 4 17 L 9 11 L 16 17 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="57" y2="12"/>
 <path AFMask="2147483647" Plane="1" d="M 4 11 L 9 5 L 16 11 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 4 51 L 9 57 L 16 51 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 4 57 L 9 63 L 16 57 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
</symbol>
<symbol id="DollyBreaker:qj_ocs_xckg3_1_1" viewBox="0,0,20,70">
 <use Plane="1" x="10" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="10" xlink:href="#terminal" y="66"/>
 <path AFMask="2147483647" Plane="1" d="M 5 11 L 10 6 L 15 11 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="19" y2="8"/>
 <path AFMask="2147483647" Plane="1" d="M 5 57 L 10 62 L 15 57 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="49" y2="60"/>
</symbol>
<symbol id="DollyBreaker:qj_ocs_xckg3_1_2" viewBox="0,0,20,70">
 <use Plane="1" x="10" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="10" xlink:href="#terminal" y="66"/>
 <path AFMask="2147483647" Plane="1" d="M 4 16 L 9 11 L 16 17 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="20" y2="12"/>
 <path AFMask="2147483647" Plane="1" d="M 4 11 L 9 5 L 16 11 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 4 51 L 9 57 L 16 51 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 4 57 L 9 63 L 16 57 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="49" y2="57"/>
</symbol>
<symbol id="DollyBreaker:qj_ocs_xckg3_1_3" viewBox="0,0,20,70">
 <use Plane="1" x="10" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="10" xlink:href="#terminal" y="66"/>
 <path AFMask="2147483647" Plane="1" d="M 5 11 L 10 6 L 15 11 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="20" y2="9"/>
 <path AFMask="2147483647" Plane="1" d="M 5 57 L 10 62 L 15 57 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="49" y2="60"/>
</symbol>
<symbol id="PT:电压互感器1_0" viewBox="0,0,18,44">
 <use Plane="0" x="9" xlink:href="#terminal" y="4"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="9" y1="14" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="5" y1="21" y2="17"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="13" y1="22" y2="17"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="9" y1="21" y2="24"/>
 <circle AFMask="2147483647" Plane="0" cx="9" cy="33" fill="none" r="8" stroke="rgb(255,255,254)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="2" cy="26" fill="none" r="0" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="9" cy="22" fill="none" r="8" stroke="rgb(255,255,254)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="5" x2="14" y1="32" y2="32"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="5" x2="9" y1="32" y2="36"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="14" x2="10" y1="32" y2="36"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="版纳_35kV_曼万河电站" InitShowingPlane="0," fill="rgb(0,0,0)" height="1039" width="1920" x="0" y="0"/>
</g>
<g id="Other_Layer">
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="17" x2="365" y1="242" y2="242"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="18" x2="366" y1="576" y2="576"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="16" x2="366" y1="365" y2="365"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="16" x2="366" y1="756" y2="756"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="16" x2="366" y1="1011" y2="1011"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="17" x2="365" y1="512" y2="512"/>
 
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="104" x2="104" y1="365" y2="513"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="172" x2="172" y1="519" y2="637"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="104" x2="366" y1="416" y2="416"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="104" x2="366" y1="464" y2="464"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="298" x2="298" y1="241" y2="359"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="230" x2="230" y1="241" y2="359"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="157" x2="157" y1="241" y2="359"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="87" x2="87" y1="241" y2="359"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="18" x2="366" y1="636" y2="636"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="17" x2="365" y1="18" y2="18"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="17" x2="365" y1="295" y2="295"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="16" x2="16" y1="18" y2="1008"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="368" x2="368" y1="20" y2="1011"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="17" x2="365" y1="160" y2="160"/>
</g>
<g id="Bus_Layer">
 <g id="30000041">
  <path d="M 598 321 L 1629 321" stroke="rgb(0,0,255)" stroke-width="4"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 598 321 L 1629 321" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="30001260">
  <path d="M 516 685 L 1827 685" stroke="rgb(0,0,255)" stroke-width="4"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 516 685 L 1827 685" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="Breaker_Layer">
 <g id="100001252">
  <use class="kv-1" height="50" transform="rotate(0,873,593) scale(1,1) translate(-14,-23)" width="50" x="873" xlink:href="#Breaker:0_0" y="593"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,873,593) scale(1,1) translate(-14,-23)" width="50" x="873" y="593"/></g>
 <g id="100001321">
  <use class="kv-1" height="50" transform="rotate(0,755,800) scale(1,1) translate(-14,-23)" width="50" x="755" xlink:href="#Breaker:0_0" y="800"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,755,800) scale(1,1) translate(-14,-23)" width="50" x="755" y="800"/></g>
 <g id="100001425">
  <use class="kv-1" height="50" transform="rotate(0,1627,800) scale(1,1) translate(-14,-23)" width="50" x="1627" xlink:href="#Breaker:0_0" y="800"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1627,800) scale(1,1) translate(-14,-23)" width="50" x="1627" y="800"/></g>
</g>
<g id="Disconnector_Layer">
 <g id="101000467">
  <use class="kv-1" height="40" transform="rotate(360,760,116) scale(-1,1) translate(-1528,-19)" width="20" x="760" xlink:href="#Disconnector:bn_刀闸3_0" y="116"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(360,760,116) scale(-1,1) translate(-1528,-19)" width="20" x="760" y="116"/></g>
 <g id="101001229">
  <use class="kv-1" height="16" transform="rotate(0,800,158) scale(1,1) translate(-6,-8)" width="10" x="800" xlink:href="#Disconnector:bn_刀闸17_0" y="158"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(0,800,158) scale(1,1) translate(-6,-8)" width="10" x="800" y="158"/></g>
 <g id="101001258">
  <use class="kv-1" height="40" transform="rotate(360,872,654) scale(-1,1) translate(-1752,-19)" width="20" x="872" xlink:href="#Disconnector:bn_刀闸3_0" y="654"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(360,872,654) scale(-1,1) translate(-1752,-19)" width="20" x="872" y="654"/></g>
 <g id="101001281">
  <use class="kv-1" height="10" transform="rotate(0,1211,348) scale(1,1) translate(-8,-6)" width="16" x="1211" xlink:href="#Disconnector:bn_刀闸18_0" y="348"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="10" opacity="0" stroke="white" transform="rotate(0,1211,348) scale(1,1) translate(-8,-6)" width="16" x="1211" y="348"/></g>
 <g id="101001320">
  <use class="kv-1" height="40" transform="rotate(360,754,731) scale(-1,1) translate(-1516,-19)" width="20" x="754" xlink:href="#Disconnector:bn_刀闸3_0" y="731"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(360,754,731) scale(-1,1) translate(-1516,-19)" width="20" x="754" y="731"/></g>
 <g id="101001330">
  <use class="kv-1" height="40" transform="rotate(360,654,874) scale(-1,1) translate(-1316,-19)" width="20" x="654" xlink:href="#Disconnector:bn_刀闸3_0" y="874"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(360,654,874) scale(-1,1) translate(-1316,-19)" width="20" x="654" y="874"/></g>
 <g id="101001334">
  <use class="kv-1" height="40" transform="rotate(360,554,874) scale(-1,1) translate(-1116,-19)" width="20" x="554" xlink:href="#Disconnector:bn_刀闸3_0" y="874"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(360,554,874) scale(-1,1) translate(-1116,-19)" width="20" x="554" y="874"/></g>
 <g id="101001382">
  <use class="kv-1" height="40" transform="rotate(360,990,731) scale(-1,1) translate(-1988,-19)" width="20" x="990" xlink:href="#Disconnector:bn_刀闸3_0" y="731"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(360,990,731) scale(-1,1) translate(-1988,-19)" width="20" x="990" y="731"/></g>
 <g id="101001391">
  <use class="kv-1" height="40" transform="rotate(360,1184,731) scale(-1,1) translate(-2376,-19)" width="20" x="1184" xlink:href="#Disconnector:bn_刀闸3_0" y="731"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(360,1184,731) scale(-1,1) translate(-2376,-19)" width="20" x="1184" y="731"/></g>
 <g id="101001426">
  <use class="kv-1" height="40" transform="rotate(360,1626,731) scale(-1,1) translate(-3260,-19)" width="20" x="1626" xlink:href="#Disconnector:bn_刀闸3_0" y="731"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(360,1626,731) scale(-1,1) translate(-3260,-19)" width="20" x="1626" y="731"/></g>
 <g id="101001427">
  <use class="kv-1" height="40" transform="rotate(360,1526,874) scale(-1,1) translate(-3060,-19)" width="20" x="1526" xlink:href="#Disconnector:bn_刀闸3_0" y="874"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(360,1526,874) scale(-1,1) translate(-3060,-19)" width="20" x="1526" y="874"/></g>
 <g id="101001428">
  <use class="kv-1" height="40" transform="rotate(360,1426,874) scale(-1,1) translate(-2860,-19)" width="20" x="1426" xlink:href="#Disconnector:bn_刀闸3_0" y="874"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(360,1426,874) scale(-1,1) translate(-2860,-19)" width="20" x="1426" y="874"/></g>
</g>
<g id="GroundDisconnector_Layer">
 <g id="111000527">
  <use class="kv-1" height="16" transform="rotate(0,724,79) scale(1,1) translate(-31,-8)" width="34" x="724" xlink:href="#GroundDisconnector:bn_接地刀闸111_0" y="79"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(0,724,79) scale(1,1) translate(-31,-8)" width="34" x="724" y="79"/></g>
 <g id="111001225">
  <use class="kv-1" height="16" transform="rotate(0,724,158) scale(1,1) translate(-31,-8)" width="34" x="724" xlink:href="#GroundDisconnector:bn_接地刀闸111_0" y="158"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(0,724,158) scale(1,1) translate(-31,-8)" width="34" x="724" y="158"/></g>
 <g id="111001228">
  <use class="kv-1" height="16" transform="rotate(0,724,200) scale(1,1) translate(-31,-8)" width="34" x="724" xlink:href="#GroundDisconnector:bn_接地刀闸111_0" y="200"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(0,724,200) scale(1,1) translate(-31,-8)" width="34" x="724" y="200"/></g>
 <g id="111001251">
  <use class="kv-1" height="16" transform="rotate(0,907,433) scale(-1,-1) translate(-1845,-874)" width="34" x="907" xlink:href="#GroundDisconnector:bn_接地刀闸111_0" y="433"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(0,907,433) scale(-1,-1) translate(-1845,-874)" width="34" x="907" y="433"/></g>
 <g id="111001256">
  <use class="kv-1" height="16" transform="rotate(0,908,635) scale(-1,-1) translate(-1847,-1278)" width="34" x="908" xlink:href="#GroundDisconnector:bn_接地刀闸111_0" y="635"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(0,908,635) scale(-1,-1) translate(-1847,-1278)" width="34" x="908" y="635"/></g>
 <g id="111001316">
  <use class="kv-1" height="16" transform="rotate(0,1497,426) scale(-1,-1) translate(-3025,-860)" width="34" x="1497" xlink:href="#GroundDisconnector:bn_接地刀闸111_0" y="426"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(0,1497,426) scale(-1,-1) translate(-3025,-860)" width="34" x="1497" y="426"/></g>
 <g id="111001325">
  <use class="kv-1" height="16" transform="rotate(0,725,761) scale(1,1) translate(-31,-8)" width="34" x="725" xlink:href="#GroundDisconnector:bn_接地刀闸111_0" y="761"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(0,725,761) scale(1,1) translate(-31,-8)" width="34" x="725" y="761"/></g>
 <g id="111001331">
  <use class="kv-1" height="16" transform="rotate(0,625,904) scale(1,1) translate(-31,-8)" width="34" x="625" xlink:href="#GroundDisconnector:bn_接地刀闸111_0" y="904"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(0,625,904) scale(1,1) translate(-31,-8)" width="34" x="625" y="904"/></g>
 <g id="111001335">
  <use class="kv-1" height="16" transform="rotate(0,525,904) scale(1,1) translate(-31,-8)" width="34" x="525" xlink:href="#GroundDisconnector:bn_接地刀闸111_0" y="904"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(0,525,904) scale(1,1) translate(-31,-8)" width="34" x="525" y="904"/></g>
 <g id="111001383">
  <use class="kv-1" height="16" transform="rotate(0,961,761) scale(1,1) translate(-31,-8)" width="34" x="961" xlink:href="#GroundDisconnector:bn_接地刀闸111_0" y="761"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(0,961,761) scale(1,1) translate(-31,-8)" width="34" x="961" y="761"/></g>
 <g id="111001392">
  <use class="kv-1" height="16" transform="rotate(0,1155,761) scale(1,1) translate(-31,-8)" width="34" x="1155" xlink:href="#GroundDisconnector:bn_接地刀闸111_0" y="761"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(0,1155,761) scale(1,1) translate(-31,-8)" width="34" x="1155" y="761"/></g>
 <g id="111001433">
  <use class="kv-1" height="16" transform="rotate(0,1597,761) scale(1,1) translate(-31,-8)" width="34" x="1597" xlink:href="#GroundDisconnector:bn_接地刀闸111_0" y="761"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(0,1597,761) scale(1,1) translate(-31,-8)" width="34" x="1597" y="761"/></g>
 <g id="111001434">
  <use class="kv-1" height="16" transform="rotate(0,1497,904) scale(1,1) translate(-31,-8)" width="34" x="1497" xlink:href="#GroundDisconnector:bn_接地刀闸111_0" y="904"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(0,1497,904) scale(1,1) translate(-31,-8)" width="34" x="1497" y="904"/></g>
 <g id="111001435">
  <use class="kv-1" height="16" transform="rotate(0,1397,904) scale(1,1) translate(-31,-8)" width="34" x="1397" xlink:href="#GroundDisconnector:bn_接地刀闸111_0" y="904"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(0,1397,904) scale(1,1) translate(-31,-8)" width="34" x="1397" y="904"/></g>
</g>
<g id="Generator_Layer">
 <g id="104000120">
  <use class="kv-1" height="50" transform="rotate(0,754,897) scale(1,1) translate(-25,-3)" width="50" x="754" xlink:href="#SynchronousMachine:ocs_发电机1_0" y="897"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,754,897) scale(1,1) translate(-25,-3)" width="50" x="754" y="897"/></g>
 <g id="104001429">
  <use class="kv-1" height="50" transform="rotate(0,1626,897) scale(1,1) translate(-25,-3)" width="50" x="1626" xlink:href="#SynchronousMachine:ocs_发电机1_0" y="897"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1626,897) scale(1,1) translate(-25,-3)" width="50" x="1626" y="897"/></g>
</g>
<g id="Transformer2_Layer">
<g id="102000019">
 <g id="1020000190">
  <use class="kv-1" height="76" transform="rotate(0,872,496) scale(1,1) translate(-26,-38)" width="50" x="872" xlink:href="#Transformer2:bn_两卷变压器8_0" y="496"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="1020000191">
  <use class="kv-1" height="76" transform="rotate(0,872,496) scale(1,1) translate(-26,-38)" width="50" x="872" xlink:href="#Transformer2:bn_两卷变压器8_1" y="496"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <metadata>
  <cge:PSR_Ref ObjectID="-1" ObjectName="-1" Plane="0"/>
 </metadata>
<rect fill="white" height="76" opacity="0" stroke="white" transform="rotate(0,872,496) scale(1,1) translate(-26,-38)" width="50" x="872" y="496"/></g>
</g>
<g id="DollyBreaker_Layer">
 <g id="110001213">
  <use class="kv-1" height="74" transform="rotate(0,760,256) scale(1,1) translate(-10,-37)" width="20" x="760" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="256"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,760,256) scale(1,1) translate(-10,-37)" width="20" x="760" y="256"/></g>
 <g id="110001213">
  <use class="kv-1" height="74" transform="rotate(0,760,256) scale(1,1) translate(-10,-37)" width="20" x="760" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="256"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,760,256) scale(1,1) translate(-10,-37)" width="20" x="760" y="256"/></g>
 <g id="110001249">
  <use class="kv-1" height="74" transform="rotate(0,872,373) scale(1,1) translate(-10,-37)" width="20" x="872" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="373"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,872,373) scale(1,1) translate(-10,-37)" width="20" x="872" y="373"/></g>
 <g id="110001249">
  <use class="kv-1" height="74" transform="rotate(0,872,373) scale(1,1) translate(-10,-37)" width="20" x="872" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="373"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,872,373) scale(1,1) translate(-10,-37)" width="20" x="872" y="373"/></g>
 <g id="110001305">
  <use class="kv-1" height="70" transform="rotate(0,1462,369) scale(1,1) translate(-10,-35)" width="20" x="1462" xlink:href="#DollyBreaker:qj_ocs_xckg3_0_0" y="369"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,1462,369) scale(1,1) translate(-10,-35)" width="20" x="1462" y="369"/></g>
 <g id="110001305">
  <use class="kv-1" height="70" transform="rotate(0,1462,369) scale(1,1) translate(-10,-35)" width="20" x="1462" xlink:href="#DollyBreaker:qj_ocs_xckg3_1_0" y="369"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,1462,369) scale(1,1) translate(-10,-35)" width="20" x="1462" y="369"/></g>
</g>
<g id="ACLineSegment_Layer">
 <g id="36000496">
 <path d="M 760 100 L 760 55" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="ST=-1/LN=-1" ObjectName="ST=-1/LN=-1" Plane="0"/>
   <cge:PSR_Link Pin0InfoVect0LinkObjId="101000467_0"/>
  </metadata>
 <path d="M 760 100 L 760 55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="PT_Layer">
 <g id="107001235">
  <use class="kv-1" height="60" transform="rotate(270,824,157) scale(1,1) translate(-10,-4)" width="20" x="824" xlink:href="#PT:gd_两卷01_0" y="157"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(270,824,157) scale(1,1) translate(-10,-4)" width="20" x="824" y="157"/></g>
 <g id="107001284">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="107001350">
  <use class="kv-1" height="56" transform="rotate(0,554,944) scale(1,1) translate(-8,-4)" width="26" x="554" xlink:href="#PT:gd_三圈带熔断1_0" y="944"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="56" opacity="0" stroke="white" transform="rotate(0,554,944) scale(1,1) translate(-8,-4)" width="26" x="554" y="944"/></g>
 <g id="107001353">
  <use class="kv-1" height="60" transform="rotate(0,655,938) scale(1,1) translate(-10,-4)" width="20" x="655" xlink:href="#PT:gd_两卷01_0" y="938"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,655,938) scale(1,1) translate(-10,-4)" width="20" x="655" y="938"/></g>
 <g id="107001356">
  <use class="kv-1" height="44" transform="rotate(0,684,953) scale(1,1) translate(-9,-4)" width="18" x="684" xlink:href="#PT:电压互感器1_0" y="953"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="44" opacity="0" stroke="white" transform="rotate(0,684,953) scale(1,1) translate(-9,-4)" width="18" x="684" y="953"/></g>
 <g id="107001385">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="107001430">
  <use class="kv-1" height="56" transform="rotate(0,1426,944) scale(1,1) translate(-8,-4)" width="26" x="1426" xlink:href="#PT:gd_三圈带熔断1_0" y="944"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="56" opacity="0" stroke="white" transform="rotate(0,1426,944) scale(1,1) translate(-8,-4)" width="26" x="1426" y="944"/></g>
 <g id="107001431">
  <use class="kv-1" height="60" transform="rotate(0,1527,938) scale(1,1) translate(-10,-4)" width="20" x="1527" xlink:href="#PT:gd_两卷01_0" y="938"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1527,938) scale(1,1) translate(-10,-4)" width="20" x="1527" y="938"/></g>
 <g id="107001432">
  <use class="kv-1" height="44" transform="rotate(0,1556,953) scale(1,1) translate(-9,-4)" width="18" x="1556" xlink:href="#PT:电压互感器1_0" y="953"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="44" opacity="0" stroke="white" transform="rotate(0,1556,953) scale(1,1) translate(-9,-4)" width="18" x="1556" y="953"/></g>
</g>
<g id="GZP_Layer">
 <g id="135000453">
  <use class="kv-1" height="36" transform="rotate(0,254,694) scale(1,1) translate(-18,-18)" width="36" x="254" xlink:href="#GZP:gg_光子牌1_0" y="694"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,254,694) scale(1,1) translate(-18,-18)" width="36" x="254" y="694"/></g>
</g>
<g id="Arrester_Layer">
 <g id="133001287">
  <use class="kv-1" height="40" transform="rotate(360,1242,381) scale(1,-1) translate(-10,-797)" width="20" x="1242" xlink:href="#Arrester:bn_避雷器4_0" y="381"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(360,1242,381) scale(1,-1) translate(-10,-797)" width="20" x="1242" y="381"/></g>
</g>
<g id="Fuse_Layer">
 <g id="131001395">
  <use class="kv-1" height="32" transform="rotate(0,1185,787) scale(1,1) translate(-8,-16)" width="16" x="1185" xlink:href="#Fuse:gd_熔丝_0" y="787"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(0,1185,787) scale(1,1) translate(-8,-16)" width="16" x="1185" y="787"/></g>
</g>
<g id="Status_Layer">
 <g id="126000448">
  <use class="kv-1" height="40" transform="rotate(0,51,268) scale(0.7,0.7) translate(-8.14285,94.8572)" width="60" x="51" xlink:href="#Status:bn_工况退出颜色显示_0" y="268"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,51,268) scale(0.7,0.7) translate(-8.14285,94.8572)" width="60" x="51" y="268"/></g>
 <g id="126000449">
  <use class="kv-1" height="40" transform="rotate(0,121,268) scale(0.7,0.7) translate(21.8571,94.8572)" width="60" x="121" xlink:href="#Status:bn_不变化颜色显示_0" y="268"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,121,268) scale(0.7,0.7) translate(21.8571,94.8572)" width="60" x="121" y="268"/></g>
 <g id="126000450">
  <use class="kv-1" height="40" transform="rotate(0,191,268) scale(0.7,0.7) translate(51.8571,94.8572)" width="60" x="191" xlink:href="#Status:bn_越限颜色显示_0" y="268"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,191,268) scale(0.7,0.7) translate(51.8571,94.8572)" width="60" x="191" y="268"/></g>
 <g id="126000451">
  <use class="kv-1" height="40" transform="rotate(0,264,268) scale(0.7,0.7) translate(83.1429,94.8572)" width="60" x="264" xlink:href="#Status:bn_非实测颜色显示_0" y="268"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,264,268) scale(0.7,0.7) translate(83.1429,94.8572)" width="60" x="264" y="268"/></g>
 <g id="126000452">
  <use class="kv-1" height="40" transform="rotate(0,331,268) scale(0.7,0.7) translate(111.857,94.8572)" width="60" x="331" xlink:href="#Status:bn_数据封锁颜色显示_0" y="268"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,331,268) scale(0.7,0.7) translate(111.857,94.8572)" width="60" x="331" y="268"/></g>
</g>
<g id="Clock_Layer">
 <g id="56000447">
  
 <metadata/></g>
</g>
<g id="Term_Layer">
 <g id="112001295">
  <use class="kv-1" height="12" transform="rotate(269,759,181) scale(1,1) translate(-17,-6)" width="38" x="759" xlink:href="#Terminal:bn_终端设备10_0" y="181"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(269,759,181) scale(1,1) translate(-17,-6)" width="38" x="759" y="181"/></g>
 <g id="112001302">
  <use class="kv-1" height="100" transform="rotate(360,1460,476) scale(-0.8,0.8) translate(-3317,116)" width="50" x="1460" xlink:href="#Terminal:bn_终端设备12_0" y="476"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="100" opacity="0" stroke="white" transform="rotate(360,1460,476) scale(-0.8,0.8) translate(-3317,116)" width="50" x="1460" y="476"/></g>
 <g id="112001308">
  <use class="kv-1" height="12" transform="rotate(269,1461,449) scale(1,1) translate(-17,-6)" width="38" x="1461" xlink:href="#Terminal:bn_终端设备10_0" y="449"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(269,1461,449) scale(1,1) translate(-17,-6)" width="38" x="1461" y="449"/></g>
 <g id="112001326">
  <use class="kv-1" height="12" transform="rotate(269,870,552) scale(1,1) translate(-17,-6)" width="38" x="870" xlink:href="#Terminal:bn_终端设备10_0" y="552"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(269,870,552) scale(1,1) translate(-17,-6)" width="38" x="870" y="552"/></g>
 <g id="112001327">
  <use class="kv-1" height="12" transform="rotate(269,870,438) scale(1,1) translate(-17,-6)" width="38" x="870" xlink:href="#Terminal:bn_终端设备10_0" y="438"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(269,870,438) scale(1,1) translate(-17,-6)" width="38" x="870" y="438"/></g>
 <g id="112001343">
  <use class="kv-1" height="12" transform="rotate(269,753,867) scale(1,1) translate(-17,-6)" width="38" x="753" xlink:href="#Terminal:bn_终端设备10_0" y="867"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(269,753,867) scale(1,1) translate(-17,-6)" width="38" x="753" y="867"/></g>
 <g id="112001376">
  <use class="kv-1" height="12" transform="rotate(269,684,929) scale(1,1) translate(-17,-6)" width="38" x="684" xlink:href="#Terminal:bn_终端设备10_0" y="929"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(269,684,929) scale(1,1) translate(-17,-6)" width="38" x="684" y="929"/></g>
 <g id="112001394">
  <use class="kv-1" height="100" transform="rotate(360,1184,819) scale(-0.8,0.8) translate(-2696,201.75)" width="50" x="1184" xlink:href="#Terminal:bn_终端设备12_0" y="819"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="100" opacity="0" stroke="white" transform="rotate(360,1184,819) scale(-0.8,0.8) translate(-2696,201.75)" width="50" x="1184" y="819"/></g>
 <g id="112001436">
  <use class="kv-1" height="12" transform="rotate(269,1625,867) scale(1,1) translate(-17,-6)" width="38" x="1625" xlink:href="#Terminal:bn_终端设备10_0" y="867"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(269,1625,867) scale(1,1) translate(-17,-6)" width="38" x="1625" y="867"/></g>
 <g id="112001437">
  <use class="kv-1" height="12" transform="rotate(269,1556,929) scale(1,1) translate(-17,-6)" width="38" x="1556" xlink:href="#Terminal:bn_终端设备10_0" y="929"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(269,1556,929) scale(1,1) translate(-17,-6)" width="38" x="1556" y="929"/></g>
</g>
<g id="Link_Layer">
 <g id="34000538">
 <path d="M 724 79 L 760 79" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111000527_0" Plane="0"/>
  </metadata>
 <path d="M 724 79 L 760 79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001216">
 <path d="M 760 223 L 760 132" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001213_0" Pin1InfoVect0LinkObjId="101000467_1" Plane="0"/>
  </metadata>
 <path d="M 760 223 L 760 132" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001227">
 <path d="M 724 200 L 760 200" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111001228_0" Plane="0"/>
  </metadata>
 <path d="M 724 200 L 760 200" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001272">
 <path d="M 872 670 L 872 685" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101001258_1" Pin1InfoVect0LinkObjId="30001260_0" Plane="0"/>
  </metadata>
 <path d="M 872 670 L 872 685" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001273">
 <path d="M 908 635 L 872 635" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111001256_0" Pin1InfoVect0LinkObjId="34001275_0" Pin1InfoVect1LinkObjId="34001274_1" Plane="0"/>
  </metadata>
 <path d="M 908 635 L 872 635" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001274">
 <path d="M 872 608 L 872 635" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100001252_1" Pin1InfoVect0LinkObjId="34001275_0" Pin1InfoVect1LinkObjId="34001273_1" Plane="0"/>
  </metadata>
 <path d="M 872 608 L 872 635" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001275">
 <path d="M 872 635 L 872 638" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001273_1" Pin0InfoVect1LinkObjId="34001274_1" Pin1InfoVect0LinkObjId="101001258_0" Plane="0"/>
  </metadata>
 <path d="M 872 635 L 872 638" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001276">
 <path d="M 907 433 L 872 433" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111001251_0" Pin1InfoVect0LinkObjId="34001277_1" Pin1InfoVect1LinkObjId="34001278_0" Plane="0"/>
  </metadata>
 <path d="M 907 433 L 872 433" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001277">
 <path d="M 872 405 L 872 433" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001249_1" Pin1InfoVect0LinkObjId="34001276_1" Pin1InfoVect1LinkObjId="34001278_0" Plane="0"/>
  </metadata>
 <path d="M 872 405 L 872 433" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001278">
 <path d="M 872 433 L 872 461" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001276_1" Pin0InfoVect1LinkObjId="34001277_1" Pin1InfoVect0LinkObjId="102000019_0" Plane="0"/>
  </metadata>
 <path d="M 872 433 L 872 461" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001279">
 <path d="M 872 340 L 872 321" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001249_0" Pin1InfoVect0LinkObjId="30000041_0" Plane="0"/>
  </metadata>
 <path d="M 872 340 L 872 321" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001280">
 <path d="M 872 530 L 872 577" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="102000019_1" Pin1InfoVect0LinkObjId="100001252_0" Plane="0"/>
  </metadata>
 <path d="M 872 530 L 872 577" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001290">
 <path d="M 1211 344 L 1211 321" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101001281_0" Pin1InfoVect0LinkObjId="30000041_0" Plane="0"/>
  </metadata>
 <path d="M 1211 344 L 1211 321" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001292">
 <path d="M 1242 381 L 1242 361 L 1211 361" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133001287_0" Pin1InfoVect0LinkObjId="34001293_1" Pin1InfoVect1LinkObjId="34001294_0" Plane="0"/>
  </metadata>
 <path d="M 1242 381 L 1242 361 L 1211 361" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001293">
 <path d="M 1211 351 L 1211 361" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101001281_1" Pin1InfoVect0LinkObjId="34001292_1" Pin1InfoVect1LinkObjId="34001294_0" Plane="0"/>
  </metadata>
 <path d="M 1211 351 L 1211 361" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001294">
 <path d="M 1211 361 L 1211 398" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001292_1" Pin0InfoVect1LinkObjId="34001293_1" Pin1InfoVect0LinkObjId="107001284_0" Plane="0"/>
  </metadata>
 <path d="M 1211 361 L 1211 398" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001298">
 <path d="M 796 158 L 760 158" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101001229_0" Pin1InfoVect0LinkObjId="34001300_1" Plane="0"/>
  </metadata>
 <path d="M 796 158 L 760 158" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001299">
 <path d="M 796 158 L 824 158" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin1InfoVect0LinkObjId="107001235_0" Plane="0"/>
  </metadata>
 <path d="M 796 158 L 824 158" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001300">
 <path d="M 724 158 L 760 158" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111001225_0" Pin1InfoVect0LinkObjId="34001298_1" Plane="0"/>
  </metadata>
 <path d="M 724 158 L 760 158" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001301">
 <path d="M 760 288 L 760 321" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001213_1" Pin1InfoVect0LinkObjId="30000041_0" Plane="0"/>
  </metadata>
 <path d="M 760 288 L 760 321" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001315">
 <path d="M 1497 426 L 1462 426" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111001316_0" Plane="0"/>
  </metadata>
 <path d="M 1497 426 L 1462 426" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001317">
 <path d="M 1462 337 L 1462 321" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001305_0" Pin1InfoVect0LinkObjId="30000041_0" Plane="0"/>
  </metadata>
 <path d="M 1462 337 L 1462 321" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001318">
 <path d="M 1462 400 L 1461 476" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001305_1" Pin1InfoVect0LinkObjId="112001302_0" Plane="0"/>
  </metadata>
 <path d="M 1462 400 L 1461 476" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001336">
 <path d="M 754 715 L 754 685" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101001320_0" Pin1InfoVect0LinkObjId="30001260_0" Plane="0"/>
  </metadata>
 <path d="M 754 715 L 754 685" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001340">
 <path d="M 725 761 L 754 761" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111001325_0" Pin1InfoVect0LinkObjId="34001341_1" Pin1InfoVect1LinkObjId="34001342_0" Plane="0"/>
  </metadata>
 <path d="M 725 761 L 754 761" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001341">
 <path d="M 754 747 L 754 761" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101001320_1" Pin1InfoVect0LinkObjId="34001340_1" Pin1InfoVect1LinkObjId="34001342_0" Plane="0"/>
  </metadata>
 <path d="M 754 747 L 754 761" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001342">
 <path d="M 754 761 L 754 784" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001341_1" Pin0InfoVect1LinkObjId="34001340_1" Pin1InfoVect0LinkObjId="100001321_0" Plane="0"/>
  </metadata>
 <path d="M 754 761 L 754 784" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001345">
 <path d="M 754 815 L 754 835" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100001321_1" Pin1InfoVect0LinkObjId="34001346_0" Pin1InfoVect1LinkObjId="34001349_1" Plane="0"/>
  </metadata>
 <path d="M 754 815 L 754 835" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001346">
 <path d="M 754 835 L 754 897" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001345_1" Pin0InfoVect1LinkObjId="34001349_1" Pin1InfoVect0LinkObjId="104000120_0" Plane="0"/>
  </metadata>
 <path d="M 754 835 L 754 897" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001347">
 <path d="M 654 858 L 654 835" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101001330_0" Pin1InfoVect0LinkObjId="34001348_1" Pin1InfoVect1LinkObjId="34001349_0" Plane="0"/>
  </metadata>
 <path d="M 654 858 L 654 835" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001348">
 <path d="M 554 858 L 554 835 L 654 835" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101001334_0" Pin1InfoVect0LinkObjId="34001347_1" Pin1InfoVect1LinkObjId="34001349_0" Plane="0"/>
  </metadata>
 <path d="M 554 858 L 554 835 L 654 835" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001349">
 <path d="M 654 835 L 754 835" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001348_1" Pin0InfoVect1LinkObjId="34001347_1" Pin1InfoVect0LinkObjId="34001346_0" Pin1InfoVect1LinkObjId="34001345_1" Plane="0"/>
  </metadata>
 <path d="M 654 835 L 754 835" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001363">
 <path d="M 525 904 L 554 904" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111001335_0" Pin1InfoVect0LinkObjId="34001364_1" Pin1InfoVect1LinkObjId="34001365_0" Plane="0"/>
  </metadata>
 <path d="M 525 904 L 554 904" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001364">
 <path d="M 554 890 L 554 904" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101001334_1" Pin1InfoVect0LinkObjId="34001363_1" Pin1InfoVect1LinkObjId="34001365_0" Plane="0"/>
  </metadata>
 <path d="M 554 890 L 554 904" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001365">
 <path d="M 554 904 L 554 944" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001363_1" Pin0InfoVect1LinkObjId="34001364_1" Pin1InfoVect0LinkObjId="107001350_0" Plane="0"/>
  </metadata>
 <path d="M 554 904 L 554 944" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001366">
 <path d="M 625 904 L 655 904" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111001331_0" Pin1InfoVect0LinkObjId="34001375_1" Pin1InfoVect1LinkObjId="34001367_1" Plane="0"/>
  </metadata>
 <path d="M 625 904 L 655 904" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001367">
 <path d="M 654 890 L 655 904" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101001330_1" Pin1InfoVect0LinkObjId="34001375_1" Pin1InfoVect1LinkObjId="34001366_1" Plane="0"/>
  </metadata>
 <path d="M 654 890 L 655 904" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001373">
 <path d="M 684 953 L 684 911 L 655 911" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="107001356_0" Pin1InfoVect0LinkObjId="34001375_0" Pin1InfoVect1LinkObjId="34001374_1" Plane="0"/>
  </metadata>
 <path d="M 684 953 L 684 911 L 655 911" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001374">
 <path d="M 655 938 L 655 911" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="107001353_0" Pin1InfoVect0LinkObjId="34001375_0" Pin1InfoVect1LinkObjId="34001373_1" Plane="0"/>
  </metadata>
 <path d="M 655 938 L 655 911" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001375">
 <path d="M 655 911 L 655 904" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001373_1" Pin0InfoVect1LinkObjId="34001374_1" Pin1InfoVect0LinkObjId="34001366_1" Pin1InfoVect1LinkObjId="34001367_1" Plane="0"/>
  </metadata>
 <path d="M 655 911 L 655 904" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001380">
 <path d="M 961 761 L 990 761" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111001383_0" Plane="0"/>
  </metadata>
 <path d="M 961 761 L 990 761" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001379">
 <path d="M 990 715 L 990 685" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101001382_0" Pin1InfoVect0LinkObjId="30001260_0" Plane="0"/>
  </metadata>
 <path d="M 990 715 L 990 685" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001386">
 <path d="M 990 808 L 990 747" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="107001385_0" Pin1InfoVect0LinkObjId="101001382_1" Plane="0"/>
  </metadata>
 <path d="M 990 808 L 990 747" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001390">
 <path d="M 1155 761 L 1184 761" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111001392_0" Plane="0"/>
  </metadata>
 <path d="M 1155 761 L 1184 761" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001389">
 <path d="M 1184 715 L 1184 685" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101001391_0" Pin1InfoVect0LinkObjId="30001260_0" Plane="0"/>
  </metadata>
 <path d="M 1184 715 L 1184 685" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001396">
 <path d="M 1184 747 L 1185 774" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101001391_1" Pin1InfoVect0LinkObjId="131001395_0" Plane="0"/>
  </metadata>
 <path d="M 1184 747 L 1185 774" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001397">
 <path d="M 1185 799 L 1185 820" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="131001395_1" Pin1InfoVect0LinkObjId="112001394_0" Plane="0"/>
  </metadata>
 <path d="M 1185 799 L 1185 820" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001409">
 <path d="M 1597 761 L 1626 761" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111001433_0" Pin1InfoVect0LinkObjId="34001411_0" Pin1InfoVect1LinkObjId="34001410_1" Plane="0"/>
  </metadata>
 <path d="M 1597 761 L 1626 761" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001410">
 <path d="M 1626 747 L 1626 761" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101001426_1" Pin1InfoVect0LinkObjId="34001411_0" Pin1InfoVect1LinkObjId="34001409_1" Plane="0"/>
  </metadata>
 <path d="M 1626 747 L 1626 761" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001411">
 <path d="M 1626 761 L 1626 784" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001409_1" Pin0InfoVect1LinkObjId="34001410_1" Pin1InfoVect0LinkObjId="100001425_0" Plane="0"/>
  </metadata>
 <path d="M 1626 761 L 1626 784" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001412">
 <path d="M 1626 815 L 1626 835" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100001425_1" Pin1InfoVect0LinkObjId="34001413_0" Pin1InfoVect1LinkObjId="34001416_1" Plane="0"/>
  </metadata>
 <path d="M 1626 815 L 1626 835" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001413">
 <path d="M 1626 835 L 1626 897" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001412_1" Pin0InfoVect1LinkObjId="34001416_1" Pin1InfoVect0LinkObjId="104001429_0" Plane="0"/>
  </metadata>
 <path d="M 1626 835 L 1626 897" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001414">
 <path d="M 1526 858 L 1526 835" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101001427_0" Pin1InfoVect0LinkObjId="34001415_1" Pin1InfoVect1LinkObjId="34001416_0" Plane="0"/>
  </metadata>
 <path d="M 1526 858 L 1526 835" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001415">
 <path d="M 1426 858 L 1426 835 L 1526 835" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101001428_0" Pin1InfoVect0LinkObjId="34001414_1" Pin1InfoVect1LinkObjId="34001416_0" Plane="0"/>
  </metadata>
 <path d="M 1426 858 L 1426 835 L 1526 835" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001416">
 <path d="M 1526 835 L 1626 835" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001414_1" Pin0InfoVect1LinkObjId="34001415_1" Pin1InfoVect0LinkObjId="34001413_0" Pin1InfoVect1LinkObjId="34001412_1" Plane="0"/>
  </metadata>
 <path d="M 1526 835 L 1626 835" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001417">
 <path d="M 1397 904 L 1426 904" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111001435_0" Pin1InfoVect0LinkObjId="34001418_1" Pin1InfoVect1LinkObjId="34001419_0" Plane="0"/>
  </metadata>
 <path d="M 1397 904 L 1426 904" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001418">
 <path d="M 1426 890 L 1426 904" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101001428_1" Pin1InfoVect0LinkObjId="34001417_1" Pin1InfoVect1LinkObjId="34001419_0" Plane="0"/>
  </metadata>
 <path d="M 1426 890 L 1426 904" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001419">
 <path d="M 1426 904 L 1426 944" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001417_1" Pin0InfoVect1LinkObjId="34001418_1" Pin1InfoVect0LinkObjId="107001430_0" Plane="0"/>
  </metadata>
 <path d="M 1426 904 L 1426 944" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001420">
 <path d="M 1497 904 L 1527 904" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111001434_0" Pin1InfoVect0LinkObjId="34001424_1" Pin1InfoVect1LinkObjId="34001421_1" Plane="0"/>
  </metadata>
 <path d="M 1497 904 L 1527 904" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001421">
 <path d="M 1526 890 L 1527 904" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101001427_1" Pin1InfoVect0LinkObjId="34001424_1" Pin1InfoVect1LinkObjId="34001420_1" Plane="0"/>
  </metadata>
 <path d="M 1526 890 L 1527 904" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001422">
 <path d="M 1556 953 L 1556 911 L 1527 911" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="107001432_0" Pin1InfoVect0LinkObjId="34001423_1" Pin1InfoVect1LinkObjId="34001424_0" Plane="0"/>
  </metadata>
 <path d="M 1556 953 L 1556 911 L 1527 911" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001423">
 <path d="M 1527 938 L 1527 911" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="107001431_0" Pin1InfoVect0LinkObjId="34001422_1" Pin1InfoVect1LinkObjId="34001424_0" Plane="0"/>
  </metadata>
 <path d="M 1527 938 L 1527 911" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001424">
 <path d="M 1527 911 L 1527 904" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001423_1" Pin0InfoVect1LinkObjId="34001422_1" Pin1InfoVect0LinkObjId="34001420_1" Pin1InfoVect1LinkObjId="34001421_1" Plane="0"/>
  </metadata>
 <path d="M 1527 911 L 1527 904" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001438">
 <path d="M 1626 715 L 1626 685" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101001426_0" Pin1InfoVect0LinkObjId="30001260_0" Plane="0"/>
  </metadata>
 <path d="M 1626 715 L 1626 685" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001439">
 <path d="M 1662 735 L 1662 735" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 1662 735 L 1662 735" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="MeasurementValue_Layer">
 <g id="33000445">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="26" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="226" xml:space="preserve" y="573">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000446">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="26" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="227" xml:space="preserve" y="624">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
</g>
<g id="Text_Layer">
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="56" font-size="32" font-width="32" stroke="rgb(0,0,0)" writing-mode="lr" x="128" xml:space="preserve" y="117">35kV曼万河电站</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="tb" x="62" xml:space="preserve" y="403">单</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,0,0)" writing-mode="lr" x="184" xml:space="preserve" y="414">MW</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="56" xml:space="preserve" y="572">总有功</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="96" xml:space="preserve" y="710">事故总:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="117" xml:space="preserve" y="414">有功:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="222" xml:space="preserve" y="414"> 无功:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,0,0)" writing-mode="lr" x="305" xml:space="preserve" y="414">MVr</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="117" xml:space="preserve" y="460">电流:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,0,0)" writing-mode="lr" x="184" xml:space="preserve" y="460">A</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="234" xml:space="preserve" y="460">电压:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,0,0)" writing-mode="lr" x="305" xml:space="preserve" y="460">kV</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="117" xml:space="preserve" y="510">频率:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,0,0)" writing-mode="lr" x="184" xml:space="preserve" y="510">Hz</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="234" xml:space="preserve" y="510">温度:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,0,0)" writing-mode="lr" x="305" xml:space="preserve" y="511">℃</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="51" xml:space="preserve" y="627">总无功</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="tb" x="62" xml:space="preserve" y="472">位</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="28" xml:space="preserve" y="328">工况</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="28" xml:space="preserve" y="351">退出</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="111" xml:space="preserve" y="328">不</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="100" xml:space="preserve" y="353">变化</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="168" xml:space="preserve" y="342">越限</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="251" xml:space="preserve" y="328">非</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="239" xml:space="preserve" y="353">实测</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="309" xml:space="preserve" y="330">数据</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="309" xml:space="preserve" y="353">封锁</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="598" xml:space="preserve" y="316">35kVI母</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="715" xml:space="preserve" y="969">1号发电机</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="771" xml:space="preserve" y="126">3716</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="740" xml:space="preserve" y="46">三万线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="697" xml:space="preserve" y="109">37167</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="742" xml:space="preserve" y="996">2000kV</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="775" xml:space="preserve" y="266">371</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="697" xml:space="preserve" y="188">37160</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="697" xml:space="preserve" y="230">37117</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="788" xml:space="preserve" y="148">3711</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="887" xml:space="preserve" y="383">301</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="900" xml:space="preserve" y="422">30117</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="887" xml:space="preserve" y="602">601</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="901" xml:space="preserve" y="624">60117</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="883" xml:space="preserve" y="664">6011</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="537" xml:space="preserve" y="680">6.3kVI母</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1221" xml:space="preserve" y="358">3901</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1201" xml:space="preserve" y="475">I母TV</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1448" xml:space="preserve" y="597">2号厂用变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1477" xml:space="preserve" y="379">3001</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1490" xml:space="preserve" y="415">30017</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="765" xml:space="preserve" y="741">6101</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="769" xml:space="preserve" y="810">610</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="692" xml:space="preserve" y="791">61017</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="665" xml:space="preserve" y="884">6102</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="592" xml:space="preserve" y="934">61027</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="492" xml:space="preserve" y="934">61037</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="565" xml:space="preserve" y="884">6103</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="635" xml:space="preserve" y="1038">1号励磁变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1001" xml:space="preserve" y="741">6901</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="928" xml:space="preserve" y="791">69017</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="970" xml:space="preserve" y="908">I母TV</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1195" xml:space="preserve" y="741">6001</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1122" xml:space="preserve" y="791">60017</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1172" xml:space="preserve" y="941">1号厂用变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1587" xml:space="preserve" y="969">2号发电机</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1604" xml:space="preserve" y="996">2000kV</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1637" xml:space="preserve" y="741">6201</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1641" xml:space="preserve" y="810">620</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1564" xml:space="preserve" y="791">62017</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1537" xml:space="preserve" y="884">6202</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1464" xml:space="preserve" y="934">62027</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1437" xml:space="preserve" y="884">6203</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1498" xml:space="preserve" y="1038">2号励磁变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1364" xml:space="preserve" y="934">62037</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href="农垦电网.other.svg"><rect fill-opacity="0" height="112" stroke-opacity="0" stroke-width="2" width="337" x="25" y="34"/></g>
</g>
</svg>