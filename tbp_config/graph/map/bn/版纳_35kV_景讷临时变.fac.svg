<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="113997365567815950" height="1600" id="thSvg" viewBox="0 0 3000 1600" width="3000">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="Transformer2:232323_0" viewBox="0,0,40,80">
 <use Plane="0" x="20" xlink:href="#terminal" y="3"/>
 <circle AFMask="2147483647" Plane="0" cx="19" cy="21" fill="none" r="18" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="20" x2="27" y1="53" y2="45"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="20" x2="12" y1="53" y2="46"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="20" x2="20" y1="53" y2="63"/>
</symbol>
<symbol id="Transformer2:232323_1" viewBox="0,0,40,80">
 <use Plane="1" x="20" xlink:href="#terminal" y="72"/>
 <circle AFMask="2147483647" Plane="1" cx="19" cy="53" fill="none" r="18" stroke="rgb(0,0,255)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 19 27 L 28 14 L 9 14 Z" fill="none" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="PT:44551_0" viewBox="0,0,60,60">
 <use Plane="0" x="30" xlink:href="#terminal" y="5"/>
 <circle AFMask="2147483647" Plane="0" cx="35" cy="35" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="35" cy="25" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="25" cy="25" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="25" cy="35" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="35" x2="32" y1="35" y2="32"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="35" x2="35" y1="39" y2="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="38" x2="35" y1="32" y2="35"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="11" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,52,33)" width="6" x="49" y="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="47" x2="57" y1="29" y2="39"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="0" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,28,11)" width="0" x="28" y="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="52" x2="52" y1="39" y2="45"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="22" x2="29" y1="25" y2="25"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="22" x2="24" y1="25" y2="22"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="29" x2="27" y1="25" y2="22"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="30" x2="30" y1="20" y2="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="52" x2="35" y1="25" y2="25"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="52" x2="52" y1="25" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="38" x2="35" y1="21" y2="24"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="35" x2="32" y1="24" y2="21"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="35" x2="35" y1="28" y2="24"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="28" x2="25" y1="32" y2="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="25" x2="22" y1="35" y2="32"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="25" x2="25" y1="39" y2="35"/>
 <circle AFMask="2147483647" Plane="0" cx="30" cy="44" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="30" x2="30" y1="49" y2="45"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="30" x2="27" y1="45" y2="42"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="33" x2="30" y1="42" y2="45"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="49" x2="52" y1="42" y2="45"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="52" x2="55" y1="45" y2="42"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="49" x2="52" y1="45" y2="48"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="52" x2="55" y1="48" y2="45"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="52" x2="52" y1="48" y2="51"/>
 <polygon AFMask="2147483647" Plane="0" fill="none" points="51,52 52,54 54,52" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="PT:457_0" viewBox="0,0,100,100">
 <use Plane="0" x="33" xlink:href="#terminal" y="76"/>
 <circle AFMask="2147483647" Plane="0" cx="33" cy="47" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="33" y1="40" y2="47"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="28" y1="47" y2="51"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="38" y1="47" y2="51"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="38" y1="63" y2="67"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="28" y1="63" y2="67"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="33" y1="56" y2="63"/>
 <circle AFMask="2147483647" Plane="0" cx="33" cy="63" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="49" cy="63" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="51" x2="51" y1="37" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="51" x2="46" y1="44" y2="48"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="51" x2="56" y1="44" y2="48"/>
 <circle AFMask="2147483647" Plane="0" cx="49" cy="47" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="52" x2="52" y1="59" y2="70"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="7" x2="13" y1="13" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="12" y1="11" y2="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="11" y1="9" y2="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="10" x2="10" y1="63" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="10" x2="22" y1="63" y2="63"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="10" y1="46" y2="46"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="52" x2="48" y1="59" y2="63"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="48" x2="52" y1="67" y2="71"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="41" x2="34" y1="40" y2="33"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="34" x2="10" y1="33" y2="33"/>
</symbol>
<symbol id="Terminal:bn_35kV电缆_0" viewBox="0,0,38,12">
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,0)" stroke-width="1" x1="11" x2="25" y1="6" y2="6"/>
 <path AFMask="2147483647" Plane="0" d="M 8 2 L 11 5 L 8 8 " fill="none" stroke="rgb(255,255,0)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,0)" stroke-width="1" x1="7" x2="7" y1="3" y2="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,0)" stroke-width="1" x1="29" x2="29" y1="2" y2="8"/>
 <path AFMask="2147483647" Plane="0" d="M 28 8 L 25 5 L 28 2 " fill="none" stroke="rgb(255,255,0)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,0)" stroke-width="1" x1="2" x2="7" y1="6" y2="6"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,0)" stroke-width="1" x1="29" x2="34" y1="6" y2="6"/>
</symbol>
<symbol id="Status:bn_不变化颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(0,0,255)" height="1" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_不变化颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(85,0,255)" height="1" stroke="rgb(85,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Disconnector:bn_刀闸25_0" viewBox="0,0,16,30">
 <use Plane="0" x="7" xlink:href="#terminal" y="3"/>
 <use Plane="0" x="7" xlink:href="#terminal" y="26"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="26" y2="22"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="7" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="13" y1="22" y2="22"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="13" y1="8" y2="20"/>
</symbol>
<symbol id="Disconnector:bn_刀闸25_1" viewBox="0,0,16,30">
 <use Plane="0" x="7" xlink:href="#terminal" y="3"/>
 <use Plane="0" x="7" xlink:href="#terminal" y="26"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="26" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="13" y1="22" y2="22"/>
</symbol>
<symbol id="Breaker:bn_断路器2_0" viewBox="0,0,20,40">
 <use Plane="0" x="10" xlink:href="#terminal" y="4"/>
 <use Plane="0" x="10" xlink:href="#terminal" y="36"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="30" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,10,20)" width="14" x="3" y="5"/>
</symbol>
<symbol id="Breaker:bn_断路器2_1" viewBox="0,0,20,40">
 <use Plane="0" x="10" xlink:href="#terminal" y="4"/>
 <use Plane="0" x="10" xlink:href="#terminal" y="36"/>
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,254)" height="30" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,10,20)" width="14" x="3" y="5"/>
</symbol>
<symbol id="Status:bn_非实测颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,85,0)" height="1" stroke="rgb(170,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_非实测颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,85,0)" height="1" stroke="rgb(170,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_工况退出颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,170,255)" height="1" stroke="rgb(170,170,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_工况退出颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,170,255)" height="1" stroke="rgb(170,170,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸12_0" viewBox="0,0,40,20">
 <use Plane="0" x="4" xlink:href="#terminal" y="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="31" y1="6" y2="18"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="28" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="33" y1="8" y2="16"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="35" x2="35" y1="10" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="28" y1="2" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="4" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="9" y1="5" y2="19"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸12_1" viewBox="0,0,40,20">
 <use Plane="0" x="4" xlink:href="#terminal" y="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="31" y1="6" y2="18"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="3" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="33" y1="8" y2="16"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="35" x2="35" y1="10" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="9" y1="5" y2="19"/>
</symbol>
<symbol id="Transformer2:bn_两卷变压器2_0" viewBox="0,0,56,76">
 <use Plane="0" x="25" xlink:href="#terminal" y="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="25" x2="33" y1="20" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="25" x2="17" y1="20" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="25" x2="25" y1="20" y2="30"/>
 <circle AFMask="2147483647" Plane="0" cx="25" cy="21" fill="none" r="18" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="47" y1="48" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="41" x2="47" y1="6" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="45" x2="47" y1="9" y2="3"/>
</symbol>
<symbol id="Transformer2:bn_两卷变压器2_1" viewBox="0,0,56,76">
 <use Plane="1" x="25" xlink:href="#terminal" y="71"/>
 <circle AFMask="2147483647" Plane="1" cx="25" cy="53" fill="none" r="18" stroke="rgb(0,0,255)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 25 43 L 15 59 L 35 59 Z" fill="none" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Fuse:bn_熔断器1_0" viewBox="0,0,20,50">
 <use Plane="0" x="10" xlink:href="#terminal" y="3"/>
 <use Plane="0" x="10" xlink:href="#terminal" y="43"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="3" y2="43"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="30" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,10,23)" width="14" x="3" y="8"/>
</symbol>
<symbol id="Disconnector:bn_手车熔断器_0" viewBox="0,0,20,70">
 <use Plane="0" x="9" xlink:href="#terminal" y="66"/>
 <use Plane="0" x="9" xlink:href="#terminal" y="3"/>
 <path AFMask="2147483647" Plane="0" d="M 3 17 L 9 11 L 15 17 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="57" y2="12"/>
 <path AFMask="2147483647" Plane="0" d="M 3 11 L 9 5 L 15 12 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="0" d="M 3 51 L 9 57 L 15 51 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="0" d="M 3 57 L 9 63 L 15 57 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="26" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,10,34)" width="12" x="4" y="21"/>
</symbol>
<symbol id="Disconnector:bn_手车熔断器_1" viewBox="0,0,20,70">
 <use Plane="0" x="9" xlink:href="#terminal" y="66"/>
 <use Plane="0" x="9" xlink:href="#terminal" y="3"/>
 <path AFMask="2147483647" Plane="0" d="M 4 57 L 9 62 L 14 57 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
 <path AFMask="2147483647" Plane="0" d="M 4 11 L 9 6 L 14 11 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="26" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,10,34)" width="12" x="4" y="21"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="9" y1="9" y2="60"/>
</symbol>
<symbol id="Status:bn_数据封锁颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,85,0)" height="1" stroke="rgb(255,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_数据封锁颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,85,0)" height="1" stroke="rgb(255,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_0_0" viewBox="0,0,20,74">
 <rect AFMask="2147483647" Plane="0" fill="none" height="0" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,4,16)" width="0" x="4" y="16"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="30" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,9,37)" width="13" x="3" y="22"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_0_1" viewBox="0,0,20,74">
 <rect AFMask="2147483647" Plane="0" fill="none" height="30" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,9,36)" width="13" x="3" y="21"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_0_2" viewBox="0,0,20,74">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,254)" height="30" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,9,36)" width="13" x="3" y="21"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_0_3" viewBox="0,0,20,74">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,254)" height="30" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,9,37)" width="13" x="3" y="22"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_1_0" viewBox="0,0,20,74">
 <use Plane="1" x="9" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="9" xlink:href="#terminal" y="69"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="21" y2="10"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="52" y2="63"/>
 <path AFMask="2147483647" Plane="1" d="M 3 11 L 10 5 L 17 12 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 3 57 L 9 64 L 16 57 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 3 15 L 10 9 L 17 16 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 3 61 L 9 68 L 16 61 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_1_1" viewBox="0,0,20,74">
 <use Plane="1" x="9" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="9" xlink:href="#terminal" y="69"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="21" y2="9"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="52" y2="65"/>
 <path AFMask="2147483647" Plane="1" d="M 4 12 L 10 6 L 16 12 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
 <path AFMask="2147483647" Plane="1" d="M 4 61 L 10 67 L 16 61 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_1_2" viewBox="0,0,20,74">
 <use Plane="1" x="9" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="9" xlink:href="#terminal" y="69"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="22" y2="10"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="52" y2="63"/>
 <path AFMask="2147483647" Plane="1" d="M 3 11 L 9 4 L 16 11 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 3 15 L 9 8 L 16 15 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 2 57 L 9 63 L 16 56 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 2 61 L 9 67 L 16 60 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_1_3" viewBox="0,0,20,74">
 <use Plane="1" x="9" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="9" xlink:href="#terminal" y="69"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="21" y2="8"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="52" y2="65"/>
 <path AFMask="2147483647" Plane="1" d="M 3 11 L 9 5 L 15 11 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
 <path AFMask="2147483647" Plane="1" d="M 3 60 L 9 66 L 15 60 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
</symbol>
<symbol id="Status:bn_越限颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_越限颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Terminal:bn_终端设备9_0" viewBox="0,0,38,12">
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,255,0)" stroke-width="1" x1="11" x2="25" y1="6" y2="6"/>
 <path AFMask="2147483647" Plane="0" d="M 8 2 L 11 5 L 8 8 " fill="none" stroke="rgb(0,255,0)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,255,0)" stroke-width="1" x1="7" x2="7" y1="3" y2="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,255,0)" stroke-width="1" x1="29" x2="29" y1="2" y2="8"/>
 <path AFMask="2147483647" Plane="0" d="M 28 8 L 25 5 L 28 2 " fill="none" stroke="rgb(0,255,0)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,255,0)" stroke-width="1" x1="2" x2="7" y1="6" y2="6"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,255,0)" stroke-width="1" x1="29" x2="34" y1="6" y2="6"/>
 <circle AFMask="2147483647" Plane="0" cx="18" cy="5" fill="none" r="2" stroke="rgb(0,255,0)" stroke-width="1"/>
</symbol>
<symbol id="GZP:gg_光子牌1_0" viewBox="0,0,36,36">
 <circle AFMask="2147483647" Plane="0" cx="18" cy="18" fill="rgb(0,255,0)" r="15" stroke="rgb(0,255,0)" stroke-width="1"/>
</symbol>
<symbol id="GZP:gg_光子牌1_1" viewBox="0,0,36,36">
 <circle AFMask="2147483647" Plane="0" cx="18" cy="18" fill="rgb(255,0,0)" r="15" stroke="rgb(255,0,0)" stroke-width="1"/>
</symbol>
<symbol id="Arrester:qj_ocs_blq2_0" viewBox="0,0,40,20">
 <use Plane="0" x="2" xlink:href="#terminal" y="9"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="10" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,18,10)" width="23" x="7" y="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="10" y1="10" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="36" x2="36" y1="7" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="34" x2="34" y1="15" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="38" x2="38" y1="9" y2="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="30" x2="33" y1="10" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="4" x2="10" y1="10" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="17" x2="17" y1="7" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="17" x2="17" y1="11" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="17" x2="21" y1="7" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="21" x2="17" y1="10" y2="13"/>
</symbol>
<symbol id="Disconnector:qj_ocs_xc5_0" viewBox="0,0,16,10">
 <use Plane="0" x="8" xlink:href="#terminal" y="2"/>
 <use Plane="0" x="8" xlink:href="#terminal" y="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="8" y1="5" y2="2"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="14" y1="2" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="8" y1="9" y2="6"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="14" y1="6" y2="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="6" y2="9"/>
</symbol>
<symbol id="Disconnector:qj_ocs_xc5_1" viewBox="0,0,16,10">
 <use Plane="0" x="8" xlink:href="#terminal" y="2"/>
 <use Plane="0" x="8" xlink:href="#terminal" y="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="8" y1="5" y2="2"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="14" y1="2" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="8" y1="6" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="14" y1="3" y2="6"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="3" y2="9"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="版纳_35kV_景讷临时变" InitShowingPlane="0," fill="rgb(0,0,0)" height="1600" width="3000" x="0" y="0"/>
</g>
<g id="Other_Layer">
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="35" x2="485" y1="105" y2="105"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="485" x2="485" y1="107" y2="1487"/>
 
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="35" x2="484" y1="225" y2="225"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="34" x2="34" y1="107" y2="1487"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="125" x2="125" y1="459" y2="701"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="197" x2="197" y1="704" y2="864"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="122" x2="122" y1="947" y2="1193"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="35" x2="486" y1="1486" y2="1486"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="35" x2="484" y1="458" y2="458"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="122" x2="484" y1="540" y2="540"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="122" x2="485" y1="621" y2="621"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="34" x2="483" y1="702" y2="702"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="34" x2="483" y1="782" y2="782"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="35" x2="485" y1="864" y2="864"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="36" x2="484" y1="948" y2="948"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="122" x2="485" y1="1026" y2="1026"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="37" x2="488" y1="1196" y2="1196"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="34" x2="484" y1="1283" y2="1283"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="34" x2="484" y1="1364" y2="1364"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="36" x2="485" y1="324" y2="324"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="391" x2="391" y1="328" y2="455"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="301" x2="301" y1="328" y2="455"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="213" x2="213" y1="328" y2="455"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="125" x2="125" y1="328" y2="455"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="36" x2="485" y1="389" y2="389"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="120" x2="487" y1="1115" y2="1115"/>
</g>
<g id="Bus_Layer">
 <g id="30003576">
  <path d="M 1064 994 L 2369 994" stroke="rgb(128,128,128)" stroke-width="6"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115404740451369845" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\BS_10kVI母" Plane="0"/>
  <cge:TPSR_Ref TObjectID="115404740451369845"/></metadata>
 <path d="M 1064 994 L 2369 994" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="Breaker_Layer">
 <g id="100003750">
  <use class="kv35kV" height="40" transform="rotate(0,1729,643) scale(1.2,1.2) translate(-298.167,-127.167)" width="20" x="1729" xlink:href="#Breaker:bn_断路器2_0" y="643"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521243024" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变35kV\CB_＃1主变高压侧301开关" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319934864"/>
  <cge:TPSR_Ref TObjectID="114560315521243024"/></metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1729,643) scale(1.2,1.2) translate(-298.167,-127.167)" width="20" x="1729" y="643"/></g>
</g>
<g id="Disconnector_Layer">
 <g id="101003841">
  <use class="kv10kV" height="70" transform="rotate(0,1366,1067) scale(1.2,1.2) translate(-237.667,-212.833)" width="20" x="1366" xlink:href="#Disconnector:bn_手车熔断器_0" y="1067"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959374" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\SW_10kV站用变熔断器手车工作位置" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346978254"/>
  <cge:TPSR_Ref TObjectID="114841790497959374"/></metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,1366,1067) scale(1.2,1.2) translate(-237.667,-212.833)" width="20" x="1366" y="1067"/></g>
 <g id="101003979">
  <use class="kv10kV" height="10" transform="rotate(0,1161,1032) scale(2.076,2.076) translate(-609.751,-540.89)" width="16" x="1161" xlink:href="#Disconnector:qj_ocs_xc5_0" y="1032"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959373" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\SW_PT熔断器手车工作位置" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346978253"/>
  <cge:TPSR_Ref TObjectID="114841790497959373"/></metadata>
 <rect fill="white" height="10" opacity="0" stroke="white" transform="rotate(0,1161,1032) scale(2.076,2.076) translate(-609.751,-540.89)" width="16" x="1161" y="1032"/></g>
 <g id="101004029">
  <use class="kv35kV" height="30" transform="rotate(0,1730,549) scale(1.778,1.778) translate(-764.997,-255.226)" width="16" x="1730" xlink:href="#Disconnector:bn_刀闸25_0" y="549"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959367" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变35kV\SW_3011刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346978247"/>
  <cge:TPSR_Ref TObjectID="114841790497959367"/></metadata>
 <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1730,549) scale(1.778,1.778) translate(-764.997,-255.226)" width="16" x="1730" y="549"/></g>
 <g id="101004063">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101004064">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101004066">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101004068">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101004070">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101004072">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101004074">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101004076">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101004078">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101004080">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101004082">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101004084">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 </g>
 <g id="101004200">
  <use class="kv35kV" height="30" transform="rotate(0,1730,370) scale(1.998,1.998) translate(-872.134,-199.815)" width="16" x="1730" xlink:href="#Disconnector:bn_刀闸25_0" y="370"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959375" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变35kV\SW_35kV茶讷线3016刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346978255"/>
  <cge:TPSR_Ref TObjectID="114841790497959375"/></metadata>
 <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1730,370) scale(1.998,1.998) translate(-872.134,-199.815)" width="16" x="1730" y="370"/></g>
</g>
<g id="GroundDisconnector_Layer">
 <g id="111003809">
  <use class="kv10kV" height="20" transform="rotate(360,2012,1143) scale(-1,1) translate(-4028,-12)" width="40" x="2012" xlink:href="#GroundDisconnector:bn_接地刀闸12_0" y="1143"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115123265474666501" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\GRNDSW_10kV盘江线0537接地刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="115123394323685381"/>
  <cge:TPSR_Ref TObjectID="115123265474666501"/></metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(360,2012,1143) scale(-1,1) translate(-4028,-12)" width="40" x="2012" y="1143"/></g>
 <g id="111004096">
  <use class="kv35kV" height="20" transform="rotate(360,1675,598) scale(-1,1) translate(-3354,-12)" width="40" x="1675" xlink:href="#GroundDisconnector:bn_接地刀闸12_0" y="598"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115123265474666498" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变35kV\GRNDSW_30117接地刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="115123394323685378"/>
  <cge:TPSR_Ref TObjectID="115123265474666498"/></metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(360,1675,598) scale(-1,1) translate(-3354,-12)" width="40" x="1675" y="598"/></g>
 <g id="111004158">
  <use class="kv10kV" height="20" transform="rotate(360,1540,1143) scale(-1,1) translate(-3084,-12)" width="40" x="1540" xlink:href="#GroundDisconnector:bn_接地刀闸12_0" y="1143"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115123265474666499" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\GRNDSW_10kV景讷政府线0517接地刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="115123394323685379"/>
  <cge:TPSR_Ref TObjectID="115123265474666499"/></metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(360,1540,1143) scale(-1,1) translate(-3084,-12)" width="40" x="1540" y="1143"/></g>
 <g id="111004177">
  <use class="kv10kV" height="20" transform="rotate(360,1769,1144) scale(-1,1) translate(-3542,-12)" width="40" x="1769" xlink:href="#GroundDisconnector:bn_接地刀闸12_0" y="1144"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115123265474666500" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\GRNDSW_10kV南肯河线0527接地刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="115123394323685380"/>
  <cge:TPSR_Ref TObjectID="115123265474666500"/></metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(360,1769,1144) scale(-1,1) translate(-3542,-12)" width="40" x="1769" y="1144"/></g>
 <g id="111004196">
  <use class="kv10kV" height="20" transform="rotate(360,2250,1144) scale(-1,1) translate(-4504,-12)" width="40" x="2250" xlink:href="#GroundDisconnector:bn_接地刀闸12_0" y="1144"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115123265474666502" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\GRNDSW_10kV云盘水、黄草岭水库线0547接地刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="115123394323685382"/>
  <cge:TPSR_Ref TObjectID="115123265474666502"/></metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(360,2250,1144) scale(-1,1) translate(-4504,-12)" width="40" x="2250" y="1144"/></g>
</g>
<g id="Transformer2_Layer">
<g id="102003792">
 <g id="1020037920">
  <use class="kv35kV" height="76" transform="rotate(0,1729,750) scale(1.5,1.5) translate(-601.333,-287)" width="56" x="1729" xlink:href="#Transformer2:bn_两卷变压器2_0" y="750"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="117375065288344732" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变35kV\XF_＃1主变-高" Plane="0"/>
  </metadata>
 </g>
 <g id="1020037921">
  <use class="kv10kV" height="76" transform="rotate(0,1729,750) scale(1.5,1.5) translate(-601.333,-287)" width="56" x="1729" xlink:href="#Transformer2:bn_两卷变压器2_1" y="750"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="117375065288344733" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\XF_＃1主变-低" Plane="0"/>
  </metadata>
 </g>
 <metadata>
  <cge:PSR_Ref ObjectID="117093590311633410" ObjectName="版纳_35kV_景讷临时变\XFMR_＃1主变" Plane="0"/>
 <cge:TPSR_Ref TObjectID="117093590311633410"/></metadata>
<rect fill="white" height="76" opacity="0" stroke="white" transform="rotate(0,1729,750) scale(1.5,1.5) translate(-601.333,-287)" width="56" x="1729" y="750"/></g>
<g id="102003855">
 <g id="1020038550">
  <use class="kv10kV" height="80" transform="rotate(0,1366,1242) scale(1,1) translate(-20,-38)" width="40" x="1366" xlink:href="#Transformer2:232323_0" y="1242"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="117375065288344734" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\XF_＃2站用变-高" Plane="0"/>
  </metadata>
 </g>
 <g id="1020038551">
  <use class="kv10kV" height="80" transform="rotate(0,1366,1242) scale(1,1) translate(-20,-38)" width="40" x="1366" xlink:href="#Transformer2:232323_1" y="1242"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="117375065288344735" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\XF_＃2站用变-低" Plane="0"/>
  </metadata>
 </g>
 <metadata>
  <cge:PSR_Ref ObjectID="117093590311633411" ObjectName="版纳_35kV_景讷临时变\XFMR_＃2站用变" Plane="0"/>
 <cge:TPSR_Ref TObjectID="117093590311633411"/></metadata>
<rect fill="white" height="80" opacity="0" stroke="white" transform="rotate(0,1366,1242) scale(1,1) translate(-20,-38)" width="40" x="1366" y="1242"/></g>
</g>
<g id="DollyBreaker_Layer">
 <g id="110003880">
  <use class="kv10kV" height="74" transform="rotate(360,2039,1065) scale(1.2,1.2) translate(-349.833,-214.5)" width="20" x="2039" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="1065"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521243028" ObjectName=" 版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\CB_10kV盘江线053开关" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319934868"/>
  <cge:TPSR_Ref TObjectID="114560315521243028"/></metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,2039,1065) scale(1.2,1.2) translate(-349.833,-214.5)" width="20" x="2039" y="1065"/></g>
 <g id="110003880">
  <use class="kv10kV" height="74" transform="rotate(360,2039,1065) scale(1.2,1.2) translate(-349.833,-214.5)" width="20" x="2039" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="1065"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959371" ObjectName=" 版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\SW_10kV盘江线053手车工作位置" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841962296651211"/>
  <cge:TPSR_Ref TObjectID="114841790497959371"/></metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,2039,1065) scale(1.2,1.2) translate(-349.833,-214.5)" width="20" x="2039" y="1065"/></g>
 <g id="110004048">
  <use class="kv10kV" height="74" transform="rotate(360,1729,918) scale(1.2,1.2) translate(-298.167,-190)" width="20" x="1729" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="918"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521243025" ObjectName=" 版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\CB_＃1主变低压侧001开关" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319934865"/>
  <cge:TPSR_Ref TObjectID="114560315521243025"/></metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,1729,918) scale(1.2,1.2) translate(-298.167,-190)" width="20" x="1729" y="918"/></g>
 <g id="110004048">
  <use class="kv10kV" height="74" transform="rotate(360,1729,918) scale(1.2,1.2) translate(-298.167,-190)" width="20" x="1729" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="918"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959368" ObjectName=" 版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\SW_001手车工作位置" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841962296651208"/>
  <cge:TPSR_Ref TObjectID="114841790497959368"/></metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,1729,918) scale(1.2,1.2) translate(-298.167,-190)" width="20" x="1729" y="918"/></g>
 <g id="110004157">
  <use class="kv10kV" height="74" transform="rotate(360,1567,1065) scale(1.2,1.2) translate(-271.167,-214.5)" width="20" x="1567" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="1065"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521243026" ObjectName=" 版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\CB_10kV景讷政府线051开关" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319934866"/>
  <cge:TPSR_Ref TObjectID="114560315521243026"/></metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,1567,1065) scale(1.2,1.2) translate(-271.167,-214.5)" width="20" x="1567" y="1065"/></g>
 <g id="110004157">
  <use class="kv10kV" height="74" transform="rotate(360,1567,1065) scale(1.2,1.2) translate(-271.167,-214.5)" width="20" x="1567" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="1065"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959369" ObjectName=" 版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\SW_10kV景讷政府线051手车工作位置" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841962296651209"/>
  <cge:TPSR_Ref TObjectID="114841790497959369"/></metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,1567,1065) scale(1.2,1.2) translate(-271.167,-214.5)" width="20" x="1567" y="1065"/></g>
 <g id="110004176">
  <use class="kv10kV" height="74" transform="rotate(360,1796,1065) scale(1.2,1.2) translate(-309.333,-214.5)" width="20" x="1796" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="1065"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521243027" ObjectName=" 版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\CB_10kV南肯河线052开关" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319934867"/>
  <cge:TPSR_Ref TObjectID="114560315521243027"/></metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,1796,1065) scale(1.2,1.2) translate(-309.333,-214.5)" width="20" x="1796" y="1065"/></g>
 <g id="110004176">
  <use class="kv10kV" height="74" transform="rotate(360,1796,1065) scale(1.2,1.2) translate(-309.333,-214.5)" width="20" x="1796" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="1065"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959370" ObjectName=" 版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\SW_10kV南肯河线052手车工作位置" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841962296651210"/>
  <cge:TPSR_Ref TObjectID="114841790497959370"/></metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,1796,1065) scale(1.2,1.2) translate(-309.333,-214.5)" width="20" x="1796" y="1065"/></g>
 <g id="110004195">
  <use class="kv10kV" height="74" transform="rotate(360,2277,1065) scale(1.2,1.2) translate(-389.5,-214.5)" width="20" x="2277" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="1065"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521243029" ObjectName=" 版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\CB_10kV云盘水、黄草岭水库线054开关" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319934869"/>
  <cge:TPSR_Ref TObjectID="114560315521243029"/></metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,2277,1065) scale(1.2,1.2) translate(-389.5,-214.5)" width="20" x="2277" y="1065"/></g>
 <g id="110004195">
  <use class="kv10kV" height="74" transform="rotate(360,2277,1065) scale(1.2,1.2) translate(-389.5,-214.5)" width="20" x="2277" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="1065"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959372" ObjectName=" 版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\SW_10kV云盘水、黄草岭水库线054手车工作位置" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841962296651212"/>
  <cge:TPSR_Ref TObjectID="114841790497959372"/></metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,2277,1065) scale(1.2,1.2) translate(-389.5,-214.5)" width="20" x="2277" y="1065"/></g>
</g>
<g id="Load_Layer">
 <g id="32003579">
 <path d="M 2039 1283 L 2039 1308" stroke="rgb(128,128,128)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115967690404792072" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\LD_10kV盘江线" Plane="0"/>
  <cge:TPSR_Ref TObjectID="115967690404792072"/></metadata>
 <path d="M 2039 1283 L 2039 1308" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32004148">
 <path d="M 1567 1283 L 1567 1308" stroke="rgb(128,128,128)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115967690404792070" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\LD_10kV景讷政府线" Plane="0"/>
  <cge:TPSR_Ref TObjectID="115967690404792070"/></metadata>
 <path d="M 1567 1283 L 1567 1308" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32004167">
 <path d="M 1796 1284 L 1796 1309" stroke="rgb(128,128,128)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115967690404792071" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\LD_10kV南肯河线" Plane="0"/>
  <cge:TPSR_Ref TObjectID="115967690404792071"/></metadata>
 <path d="M 1796 1284 L 1796 1309" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32004186">
 <path d="M 2277 1284 L 2277 1309" stroke="rgb(128,128,128)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115967690404792073" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\LD_10kV云盘水、黄草岭水库线" Plane="0"/>
  <cge:TPSR_Ref TObjectID="115967690404792073"/></metadata>
 <path d="M 2277 1284 L 2277 1309" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="ACLineSegment_Layer">
 <g id="36004219">
 <path d="M 1729 346 L 1729 247" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="ST=113997365567815950/LN=116530640358212508" ObjectName="ST=版纳_35kV_景讷临时变/LN=ACLN_茶讷线" Plane="0"/>
   <cge:PSR_Link Pin0InfoVect0LinkObjId="101004200_0"/>
  <cge:TPSR_Ref TObjectID="116530640358212508_113997365567815950"/></metadata>
 <path d="M 1729 346 L 1729 247" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="PT_Layer">
 <g id="107003999">
  <use class="kv10kV" height="60" transform="rotate(0,1161,1213) scale(1.594,1.594) translate(-462.644,-458.021)" width="60" x="1161" xlink:href="#PT:44551_0" y="1213"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189789" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\TERM_10kVI母TV电压互感器" Plane="0"/>
  </metadata>
 <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1161,1213) scale(1.594,1.594) translate(-462.644,-458.021)" width="60" x="1161" y="1213"/></g>
 <g id="107004022">
  <use class="kv35kV" height="100" transform="rotate(90,1785,488) scale(1,1) translate(-33,-76)" width="100" x="1785" xlink:href="#PT:457_0" y="488"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189785" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变35kV\TERM_茶讷线电压互感器" Plane="0"/>
  </metadata>
 <rect fill="white" height="100" opacity="0" stroke="white" transform="rotate(90,1785,488) scale(1,1) translate(-33,-76)" width="100" x="1785" y="488"/></g>
</g>
<g id="GZP_Layer">
 <g id="135002812">
  <use class="kv35kV" height="36" transform="rotate(0,311,910) scale(1.011,1.011) translate(-21.3838,-27.9011)" width="36" x="311" xlink:href="#GZP:gg_光子牌1_0" y="910"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892568345" ObjectName="122160139892568345" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,311,910) scale(1.011,1.011) translate(-21.3838,-27.9011)" width="36" x="311" y="910"/></g>
</g>
<g id="Arrester_Layer">
 <g id="133003878">
  <use class="kv10kV" height="20" transform="rotate(0,2088,1143) scale(1.5,1.5) translate(-698,-391)" width="40" x="2088" xlink:href="#Arrester:qj_ocs_blq2_0" y="1143"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189792" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\TERM_盘江线避雷器" Plane="0"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,2088,1143) scale(1.5,1.5) translate(-698,-391)" width="40" x="2088" y="1143"/></g>
 <g id="133003994">
  <use class="kv10kV" height="20" transform="rotate(90,1233,1103) scale(1.5,1.5) translate(-413,-377.667)" width="40" x="1233" xlink:href="#Arrester:qj_ocs_blq2_0" y="1103"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189788" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\TERM_0901避雷器" Plane="0"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1233,1103) scale(1.5,1.5) translate(-413,-377.667)" width="40" x="1233" y="1103"/></g>
 <g id="133004125">
  <use class="kv10kV" height="20" transform="rotate(360,1686,847) scale(-1.5,1.5) translate(-2812,-292.333)" width="40" x="1686" xlink:href="#Arrester:qj_ocs_blq2_0" y="847"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189786" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\TERM_001避雷器" Plane="0"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(360,1686,847) scale(-1.5,1.5) translate(-2812,-292.333)" width="40" x="1686" y="847"/></g>
 <g id="133004139">
  <use class="kv35kV" height="20" transform="rotate(360,1635,498) scale(-1.5,1.5) translate(-2727,-176)" width="40" x="1635" xlink:href="#Arrester:qj_ocs_blq2_0" y="498"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189784" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变35kV\TERM_茶讷线避雷器" Plane="0"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(360,1635,498) scale(-1.5,1.5) translate(-2727,-176)" width="40" x="1635" y="498"/></g>
 <g id="133004160">
  <use class="kv10kV" height="20" transform="rotate(0,1616,1143) scale(1.5,1.5) translate(-540.667,-391)" width="40" x="1616" xlink:href="#Arrester:qj_ocs_blq2_0" y="1143"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189790" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\TERM_景讷政府线避雷器" Plane="0"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1616,1143) scale(1.5,1.5) translate(-540.667,-391)" width="40" x="1616" y="1143"/></g>
 <g id="133004179">
  <use class="kv10kV" height="20" transform="rotate(0,1845,1144) scale(1.5,1.5) translate(-617,-391.333)" width="40" x="1845" xlink:href="#Arrester:qj_ocs_blq2_0" y="1144"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189791" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\TERM_南肯河线避雷器" Plane="0"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1845,1144) scale(1.5,1.5) translate(-617,-391.333)" width="40" x="1845" y="1144"/></g>
 <g id="133004198">
  <use class="kv10kV" height="20" transform="rotate(0,2326,1144) scale(1.5,1.5) translate(-777.333,-391.333)" width="40" x="2326" xlink:href="#Arrester:qj_ocs_blq2_0" y="1144"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189793" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\TERM_云盘水/黄草岭水库线避雷器" Plane="0"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,2326,1144) scale(1.5,1.5) translate(-777.333,-391.333)" width="40" x="2326" y="1144"/></g>
</g>
<g id="Fuse_Layer">
 <g id="131003989">
  <use class="kv10kV" height="50" transform="rotate(0,1161,1132) scale(1.2,1.2) translate(-203.5,-211.667)" width="20" x="1161" xlink:href="#Fuse:bn_熔断器1_0" y="1132"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189787" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\TERM_0901熔断器" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1161,1132) scale(1.2,1.2) translate(-203.5,-211.667)" width="20" x="1161" y="1132"/></g>
</g>
<g id="Status_Layer">
 <g id="126002833">
  <use class="kv-1" height="40" transform="rotate(0,70,360) scale(0.7,0.7) translate(1.90735e-06,134.286)" width="60" x="70" xlink:href="#Status:bn_工况退出颜色显示_0" y="360"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,70,360) scale(0.7,0.7) translate(1.90735e-06,134.286)" width="60" x="70" y="360"/></g>
 <g id="126002834">
  <use class="kv-1" height="40" transform="rotate(0,173,360) scale(0.7,0.7) translate(44.1429,134.286)" width="60" x="173" xlink:href="#Status:bn_不变化颜色显示_0" y="360"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,173,360) scale(0.7,0.7) translate(44.1429,134.286)" width="60" x="173" y="360"/></g>
 <g id="126002835">
  <use class="kv-1" height="40" transform="rotate(0,255,360) scale(0.7,0.7) translate(79.2857,134.286)" width="60" x="255" xlink:href="#Status:bn_越限颜色显示_0" y="360"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,255,360) scale(0.7,0.7) translate(79.2857,134.286)" width="60" x="255" y="360"/></g>
 <g id="126002836">
  <use class="kv-1" height="40" transform="rotate(0,340,360) scale(0.7,0.7) translate(115.714,134.286)" width="60" x="340" xlink:href="#Status:bn_非实测颜色显示_0" y="360"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,340,360) scale(0.7,0.7) translate(115.714,134.286)" width="60" x="340" y="360"/></g>
 <g id="126002837">
  <use class="kv-1" height="40" transform="rotate(0,434,360) scale(0.7,0.7) translate(156,134.286)" width="60" x="434" xlink:href="#Status:bn_数据封锁颜色显示_0" y="360"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,434,360) scale(0.7,0.7) translate(156,134.286)" width="60" x="434" y="360"/></g>
</g>
<g id="Clock_Layer">
 <g id="56002811">
  
 <metadata/></g>
</g>
<g id="Term_Layer">
 <g id="112003818">
  <use class="kv-1" height="12" transform="rotate(90,2039,1217) scale(1,1) translate(-18,-6)" width="38" x="2039" xlink:href="#Terminal:bn_终端设备9_0" y="1217"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,2039,1217) scale(1,1) translate(-18,-6)" width="38" x="2039" y="1217"/></g>
 <g id="112004059">
  <use class="kv-1" height="12" transform="rotate(90,1728,823) scale(0.618,1) translate(1050.12,-6)" width="38" x="1728" xlink:href="#Terminal:bn_终端设备9_0" y="823"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,1728,823) scale(0.618,1) translate(1050.12,-6)" width="38" x="1728" y="823"/></g>
 <g id="112004159">
  <use class="kv-1" height="12" transform="rotate(90,1567,1217) scale(1,1) translate(-18,-6)" width="38" x="1567" xlink:href="#Terminal:bn_终端设备9_0" y="1217"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,1567,1217) scale(1,1) translate(-18,-6)" width="38" x="1567" y="1217"/></g>
 <g id="112004178">
  <use class="kv-1" height="12" transform="rotate(90,1796,1218) scale(1,1) translate(-18,-6)" width="38" x="1796" xlink:href="#Terminal:bn_终端设备9_0" y="1218"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,1796,1218) scale(1,1) translate(-18,-6)" width="38" x="1796" y="1218"/></g>
 <g id="112004197">
  <use class="kv-1" height="12" transform="rotate(90,2277,1218) scale(1,1) translate(-18,-6)" width="38" x="2277" xlink:href="#Terminal:bn_终端设备9_0" y="1218"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,2277,1218) scale(1,1) translate(-18,-6)" width="38" x="2277" y="1218"/></g>
 <g id="112004236">
  <use class="kv-1" height="12" transform="rotate(89,1729,440) scale(1,1) translate(-18,-6)" width="38" x="1729" xlink:href="#Terminal:bn_35kV电缆_0" y="440"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(89,1729,440) scale(1,1) translate(-18,-6)" width="38" x="1729" y="440"/></g>
</g>
<g id="Link_Layer">
 <g id="34003728">
 <path d="M 2039 1283 L 2039 1216" stroke="rgb(128,128,128)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="32003579_0" Pin1InfoVect0LinkObjId="34003693_1" Plane="0"/>
  </metadata>
 <path d="M 2039 1283 L 2039 1216" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003693">
 <path d="M 2039 1104 L 2039 1216" stroke="rgb(128,128,128)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110003880_1" Pin1InfoVect0LinkObjId="34003728_1" Plane="0"/>
  </metadata>
 <path d="M 2039 1104 L 2039 1216" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003690">
 <path d="M 2012 1143 L 2039 1143" stroke="rgb(128,128,128)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111003809_0" Pin1InfoVect0LinkObjId="34003877_0" Plane="0"/>
  </metadata>
 <path d="M 2012 1143 L 2039 1143" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003641">
 <path d="M 1161 994 L 1161 1025" stroke="rgb(128,128,128)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="30003576_0" Pin1InfoVect0LinkObjId="101003979_0" Plane="0"/>
  </metadata>
 <path d="M 1161 994 L 1161 1025" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003638">
 <path d="M 1729 957 L 1729 994" stroke="rgb(128,128,128)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110004048_1" Pin1InfoVect0LinkObjId="30003576_0" Plane="0"/>
  </metadata>
 <path d="M 1729 957 L 1729 994" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003839">
 <path d="M 1366 994 L 1366 1029" stroke="rgb(255,255,254)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="30003576_0" Pin1InfoVect0LinkObjId="101003841_1" Plane="0"/>
  </metadata>
 <path d="M 1366 994 L 1366 1029" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003842">
 <path d="M 1366 1104 L 1366 1207" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101003841_0" Pin1InfoVect0LinkObjId="102003855_0" Plane="0"/>
  </metadata>
 <path d="M 1366 1104 L 1366 1207" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003864">
 <path d="M 1366 1276 L 1366 1310" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="102003855_1" Plane="0"/>
  </metadata>
 <path d="M 1366 1276 L 1366 1310" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003877">
 <path d="M 2039 1143 L 2088 1143" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34003690_1" Pin1InfoVect0LinkObjId="133003878_0" Plane="0"/>
  </metadata>
 <path d="M 2039 1143 L 2088 1143" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003883">
 <path d="M 2039 994 L 2039 1025" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="30003576_0" Pin1InfoVect0LinkObjId="110003880_0" Plane="0"/>
  </metadata>
 <path d="M 2039 994 L 2039 1025" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003991">
 <path d="M 1161 1077 L 1233 1077 L 1233 1103" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34003993_0" Pin0InfoVect1LinkObjId="34003992_1" Pin1InfoVect0LinkObjId="133003994_0" Plane="0"/>
  </metadata>
 <path d="M 1161 1077 L 1233 1077 L 1233 1103" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003992">
 <path d="M 1161 1039 L 1161 1077" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101003979_1" Pin1InfoVect0LinkObjId="34003993_0" Pin1InfoVect1LinkObjId="34003991_0" Plane="0"/>
  </metadata>
 <path d="M 1161 1039 L 1161 1077" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003993">
 <path d="M 1161 1077 L 1161 1108" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34003991_0" Pin0InfoVect1LinkObjId="34003992_1" Pin1InfoVect0LinkObjId="131003989_0" Plane="0"/>
  </metadata>
 <path d="M 1161 1077 L 1161 1108" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004019">
 <path d="M 1729 392 L 1729 498" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101004200_1" Pin1InfoVect0LinkObjId="34004027_0" Pin1InfoVect1LinkObjId="34004141_1" Pin1InfoVect2LinkObjId="34004099_0" Plane="0"/>
  </metadata>
 <path d="M 1729 392 L 1729 498" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004027">
 <path d="M 1729 498 L 1729 497" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34004019_1" Pin0InfoVect1LinkObjId="34004099_0" Pin0InfoVect2LinkObjId="34004141_1" Pin1InfoVect0LinkObjId="34004028_0" Plane="0"/>
  </metadata>
 <path d="M 1729 498 L 1729 497" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004028">
 <path d="M 1729 497 L 1729 497 L 1789 497 L 1789 496" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34004027_1" Plane="0"/>
  </metadata>
 <path d="M 1729 497 L 1729 497 L 1789 497 L 1789 496" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004050">
 <path d="M 1729 878 L 1729 847" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110004048_0" Pin1InfoVect0LinkObjId="34004060_1" Pin1InfoVect1LinkObjId="34004129_1" Plane="0"/>
  </metadata>
 <path d="M 1729 878 L 1729 847" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004056">
 <path d="M 1161 1156 L 1161 1214" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="131003989_1" Pin1InfoVect0LinkObjId="107003999_0" Plane="0"/>
  </metadata>
 <path d="M 1161 1156 L 1161 1214" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004060">
 <path d="M 1729 801 L 1729 847" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="102003792_1" Pin1InfoVect0LinkObjId="34004050_1" Pin1InfoVect1LinkObjId="34004129_1" Plane="0"/>
  </metadata>
 <path d="M 1729 801 L 1729 847" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004091">
 <path d="M 1729 662 L 1729 699" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100003750_1" Pin1InfoVect0LinkObjId="102003792_0" Plane="0"/>
  </metadata>
 <path d="M 1729 662 L 1729 699" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004099">
 <path d="M 1729 498 L 1729 528" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34004027_0" Pin0InfoVect1LinkObjId="34004141_1" Pin0InfoVect2LinkObjId="34004019_1" Pin1InfoVect0LinkObjId="101004029_0" Plane="0"/>
  </metadata>
 <path d="M 1729 498 L 1729 528" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004128">
 <path d="M 1688 847 L 1688 847" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 1688 847 L 1688 847" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004129">
 <path d="M 1686 847 L 1729 847" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133004125_0" Pin1InfoVect0LinkObjId="34004050_1" Pin1InfoVect1LinkObjId="34004060_1" Plane="0"/>
  </metadata>
 <path d="M 1686 847 L 1729 847" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004136">
 <path d="M 1675 598 L 1729 598" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111004096_0" Pin1InfoVect0LinkObjId="34004137_1" Pin1InfoVect1LinkObjId="34004138_0" Plane="0"/>
  </metadata>
 <path d="M 1675 598 L 1729 598" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004137">
 <path d="M 1729 569 L 1729 598" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101004029_1" Pin1InfoVect0LinkObjId="34004136_1" Pin1InfoVect1LinkObjId="34004138_0" Plane="0"/>
  </metadata>
 <path d="M 1729 569 L 1729 598" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004138">
 <path d="M 1729 598 L 1729 624" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34004137_1" Pin0InfoVect1LinkObjId="34004136_1" Pin1InfoVect0LinkObjId="100003750_0" Plane="0"/>
  </metadata>
 <path d="M 1729 598 L 1729 624" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004140">
 <path d="M 1635 484 L 1635 484" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 1635 484 L 1635 484" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004141">
 <path d="M 1635 498 L 1729 498" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133004139_0" Pin1InfoVect0LinkObjId="34004027_0" Pin1InfoVect1LinkObjId="34004019_1" Pin1InfoVect2LinkObjId="34004099_0" Plane="0"/>
  </metadata>
 <path d="M 1635 498 L 1729 498" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004154">
 <path d="M 1567 1283 L 1567 1216" stroke="rgb(128,128,128)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="32004148_0" Pin1InfoVect0LinkObjId="34004153_1" Plane="0"/>
  </metadata>
 <path d="M 1567 1283 L 1567 1216" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004153">
 <path d="M 1567 1104 L 1567 1216" stroke="rgb(128,128,128)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110004157_1" Pin1InfoVect0LinkObjId="34004154_1" Plane="0"/>
  </metadata>
 <path d="M 1567 1104 L 1567 1216" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004152">
 <path d="M 1540 1143 L 1567 1143" stroke="rgb(128,128,128)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111004158_0" Pin1InfoVect0LinkObjId="34004155_0" Plane="0"/>
  </metadata>
 <path d="M 1540 1143 L 1567 1143" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004155">
 <path d="M 1567 1143 L 1616 1143" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34004152_1" Pin1InfoVect0LinkObjId="133004160_0" Plane="0"/>
  </metadata>
 <path d="M 1567 1143 L 1616 1143" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004156">
 <path d="M 1567 994 L 1567 1025" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="30003576_0" Pin1InfoVect0LinkObjId="110004157_0" Plane="0"/>
  </metadata>
 <path d="M 1567 994 L 1567 1025" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004173">
 <path d="M 1796 1284 L 1796 1217" stroke="rgb(128,128,128)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="32004167_0" Pin1InfoVect0LinkObjId="34004172_1" Plane="0"/>
  </metadata>
 <path d="M 1796 1284 L 1796 1217" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004172">
 <path d="M 1796 1104 L 1796 1217" stroke="rgb(128,128,128)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110004176_1" Pin1InfoVect0LinkObjId="34004173_1" Plane="0"/>
  </metadata>
 <path d="M 1796 1104 L 1796 1217" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004171">
 <path d="M 1769 1144 L 1796 1144" stroke="rgb(128,128,128)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111004177_0" Pin1InfoVect0LinkObjId="34004174_0" Plane="0"/>
  </metadata>
 <path d="M 1769 1144 L 1796 1144" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004174">
 <path d="M 1796 1144 L 1845 1144" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34004171_1" Pin1InfoVect0LinkObjId="133004179_0" Plane="0"/>
  </metadata>
 <path d="M 1796 1144 L 1845 1144" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004175">
 <path d="M 1796 994 L 1796 1025" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="30003576_0" Pin1InfoVect0LinkObjId="110004176_0" Plane="0"/>
  </metadata>
 <path d="M 1796 994 L 1796 1025" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004192">
 <path d="M 2277 1284 L 2277 1217" stroke="rgb(128,128,128)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="32004186_0" Pin1InfoVect0LinkObjId="34004191_1" Plane="0"/>
  </metadata>
 <path d="M 2277 1284 L 2277 1217" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004191">
 <path d="M 2277 1104 L 2277 1217" stroke="rgb(128,128,128)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110004195_1" Pin1InfoVect0LinkObjId="34004192_1" Plane="0"/>
  </metadata>
 <path d="M 2277 1104 L 2277 1217" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004190">
 <path d="M 2250 1144 L 2277 1144" stroke="rgb(128,128,128)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111004196_0" Pin1InfoVect0LinkObjId="34004193_0" Plane="0"/>
  </metadata>
 <path d="M 2250 1144 L 2277 1144" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004193">
 <path d="M 2277 1144 L 2326 1144" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34004190_1" Pin1InfoVect0LinkObjId="133004198_0" Plane="0"/>
  </metadata>
 <path d="M 2277 1144 L 2326 1144" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34004194">
 <path d="M 2277 994 L 2277 1025" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="30003576_0" Pin1InfoVect0LinkObjId="110004195_0" Plane="0"/>
  </metadata>
 <path d="M 2277 994 L 2277 1025" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="MeasurementValue_Layer">
 <g id="33002806">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="40" font-size="32" font-width="32" stroke="rgb(255,255,0)" writing-mode="lr" x="300" xml:space="preserve" y="842">-0.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="113997515891671310" ObjectName="版纳_35kV_景讷临时变:Q_LOAD" Plane="0"/>
  </metadata>
 </g>
 <g id="33002805">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="40" font-size="32" font-width="32" stroke="rgb(255,255,0)" writing-mode="lr" x="299" xml:space="preserve" y="767">-0.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="113997494416834830" ObjectName="版纳_35kV_景讷临时变:P_LOAD" Plane="0"/>
  </metadata>
 </g>
 <g id="33003449">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="40" font-size="32" font-width="32" stroke="rgb(255,255,0)" writing-mode="lr" x="274" xml:space="preserve" y="1091">-0.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="117093826534834690" ObjectName="版纳_35kV_景讷临时变\XFMR_＃1主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33003450">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="40" font-size="32" font-width="32" stroke="rgb(255,255,0)" writing-mode="lr" x="274" xml:space="preserve" y="1013">-0.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="117375408885728412" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变35kV\XF_＃1主变-高:TAP" Plane="0"/>
  </metadata>
 </g>
 <g id="33003595">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="2005" xml:space="preserve" y="1358">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967862203483912" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\LD_10kV盘江线:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33003596">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="2005" xml:space="preserve" y="1388">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967905153156872" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\LD_10kV盘江线:Q" Plane="0"/>
  </metadata>
 </g>
 <g id="33003597">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="2005" xml:space="preserve" y="1418">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052502792" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\LD_10kV盘江线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33004149">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1533" xml:space="preserve" y="1358">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967862203483910" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\LD_10kV景讷政府线:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33004150">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1535" xml:space="preserve" y="1388">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967905153156870" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\LD_10kV景讷政府线:Q" Plane="0"/>
  </metadata>
 </g>
 <g id="33004151">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1537" xml:space="preserve" y="1418">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052502790" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\LD_10kV景讷政府线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33004168">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1762" xml:space="preserve" y="1359">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967862203483911" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\LD_10kV南肯河线:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33004169">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1762" xml:space="preserve" y="1389">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967905153156871" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\LD_10kV南肯河线:Q" Plane="0"/>
  </metadata>
 </g>
 <g id="33004170">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1762" xml:space="preserve" y="1419">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052502791" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\LD_10kV南肯河线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33004187">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="2243" xml:space="preserve" y="1359">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967862203483913" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\LD_10kV云盘水、黄草岭水库线:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33004188">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="2243" xml:space="preserve" y="1389">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967905153156873" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\LD_10kV云盘水、黄草岭水库线:Q" Plane="0"/>
  </metadata>
 </g>
 <g id="33004189">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="2243" xml:space="preserve" y="1419">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052502793" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\LD_10kV云盘水、黄草岭水库线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33004203">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1896" xml:space="preserve" y="874">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709533" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\XF_＃1主变-低:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33004204">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1896" xml:space="preserve" y="904">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375322986382493" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\XF_＃1主变-低:Q" Plane="0"/>
  </metadata>
 </g>
 <g id="33004205">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1896" xml:space="preserve" y="934">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375451835401373" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变10kV\XF_＃1主变-低:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33004208">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1895" xml:space="preserve" y="677">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375451835401372" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变35kV\XF_＃1主变-高:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33004207">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1895" xml:space="preserve" y="647">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375322986382492" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变35kV\XF_＃1主变-高:Q" Plane="0"/>
  </metadata>
 </g>
 <g id="33004206">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1895" xml:space="preserve" y="617">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709532" ObjectName="版纳_35kV_景讷临时变\版纳_35kV_景讷临时变35kV\XF_＃1主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33004209">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1080" xml:space="preserve" y="806">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795531685" ObjectName="122723089845858725:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33004210">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1080" xml:space="preserve" y="835">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795531682" ObjectName="122723089845858722:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33004211">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1080" xml:space="preserve" y="864">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795531683" ObjectName="122723089845858723:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33004212">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1080" xml:space="preserve" y="893">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795531684" ObjectName="122723089845858724:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33004213">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1080" xml:space="preserve" y="922">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795531686" ObjectName="122723089845858726:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33004227">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="40" font-size="32" font-width="32" stroke="rgb(255,255,0)" writing-mode="lr" x="298" xml:space="preserve" y="1262">-0.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="122723132795531678" ObjectName="122723089845858718:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33004228">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="40" font-size="32" font-width="32" stroke="rgb(255,255,0)" writing-mode="lr" x="298" xml:space="preserve" y="1349">-0.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="122723132795531679" ObjectName="122723089845858719:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33004232">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="40" font-size="32" font-width="32" stroke="rgb(255,255,0)" writing-mode="lr" x="272" xml:space="preserve" y="1177">-0.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="117093852304638466" ObjectName="版纳_35kV_景讷临时变\XFMR_＃1主变:other" Plane="0"/>
  </metadata>
 </g>
</g>
<g id="Text_Layer">
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="76" font-size="40" font-width="40" stroke="rgb(0,0,0)" writing-mode="lr" x="25" xml:space="preserve" y="195">35kV象明变</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="40" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="125" xml:space="preserve" y="924">事故总:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="65" font-size="40" font-width="40" stroke="rgb(0,0,0)" writing-mode="lr" x="174" xml:space="preserve" y="192">35kV景讷临时变</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="40" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="66" xml:space="preserve" y="767">总有功</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="40" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="168" xml:space="preserve" y="1013">档位</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="40" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="tb" x="81" xml:space="preserve" y="984">一</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="40" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="168" xml:space="preserve" y="1092">温度</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="40" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="47" xml:space="preserve" y="1266">控制母线电压</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="40" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="45" xml:space="preserve" y="1351">合闸母线电压</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="40" font-size="32" font-width="32" stroke="rgb(170,0,0)" writing-mode="lr" x="222" xml:space="preserve" y="526">MW</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="41" font-size="33" font-width="33" stroke="rgb(255,255,254)" writing-mode="lr" x="133" xml:space="preserve" y="527">有功:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="40" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="320" xml:space="preserve" y="520">无功:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="41" font-size="35" font-width="35" stroke="rgb(170,0,0)" writing-mode="lr" x="396" xml:space="preserve" y="527">MVr</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="40" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="133" xml:space="preserve" y="608">电流:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="40" font-size="32" font-width="32" stroke="rgb(170,0,0)" writing-mode="lr" x="241" xml:space="preserve" y="608">A</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="40" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="317" xml:space="preserve" y="608">电压:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="40" font-size="32" font-width="32" stroke="rgb(170,0,0)" writing-mode="lr" x="410" xml:space="preserve" y="608">kV</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="40" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="133" xml:space="preserve" y="689">频率:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="40" font-size="32" font-width="32" stroke="rgb(170,0,0)" writing-mode="lr" x="234" xml:space="preserve" y="689">Hz</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="40" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="320" xml:space="preserve" y="689">温度:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="40" font-size="32" font-width="32" stroke="rgb(170,0,0)" writing-mode="lr" x="406" xml:space="preserve" y="689">℃</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="40" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="66" xml:space="preserve" y="850">总无功</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="40" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="tb" x="81" xml:space="preserve" y="517">单</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="40" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="100" xml:space="preserve" y="1466">联系方式：</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="40" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="tb" x="80" xml:space="preserve" y="618">位</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="40" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="tb" x="81" xml:space="preserve" y="1014">号</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="40" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="tb" x="81" xml:space="preserve" y="1052">主</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="40" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="tb" x="81" xml:space="preserve" y="1096">变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,254)" writing-mode="lr" x="46" xml:space="preserve" y="421">工况</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,254)" writing-mode="lr" x="46" xml:space="preserve" y="446">退出</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,254)" writing-mode="lr" x="161" xml:space="preserve" y="421">不</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,254)" writing-mode="lr" x="149" xml:space="preserve" y="449">变化</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,254)" writing-mode="lr" x="230" xml:space="preserve" y="433">越限</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,254)" writing-mode="lr" x="330" xml:space="preserve" y="421">非</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,254)" writing-mode="lr" x="321" xml:space="preserve" y="449">实测</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,254)" writing-mode="lr" x="408" xml:space="preserve" y="421">数据</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,254)" writing-mode="lr" x="408" xml:space="preserve" y="446">封锁</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1824" xml:space="preserve" y="727"> 1号主变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1824" xml:space="preserve" y="755">SZ11-10000/35</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(35,169,128)" writing-mode="lr" x="1034" xml:space="preserve" y="920">3U0</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(35,169,128)" writing-mode="lr" x="1034" xml:space="preserve" y="893">Uc</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(35,169,128)" writing-mode="lr" x="1034" xml:space="preserve" y="866">Ub</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(35,169,128)" writing-mode="lr" x="1034" xml:space="preserve" y="839">Ua</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(35,169,128)" writing-mode="lr" x="1034" xml:space="preserve" y="807">Uab</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(35,169,128)" writing-mode="lr" x="1981" xml:space="preserve" y="1413">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(35,169,128)" writing-mode="lr" x="1981" xml:space="preserve" y="1384">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(35,169,128)" writing-mode="lr" x="1981" xml:space="preserve" y="1354">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="36" font-size="36" font-width="36" stroke="rgb(255,255,254)" writing-mode="lr" x="1990" xml:space="preserve" y="1465">盘江线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="34" font-size="34" font-width="34" stroke="rgb(255,255,254)" writing-mode="lr" x="1953" xml:space="preserve" y="1195">0537</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1008" xml:space="preserve" y="985">10kVI母</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="34" font-size="34" font-width="34" stroke="rgb(255,255,254)" writing-mode="lr" x="1746" xml:space="preserve" y="663">301</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="34" font-size="34" font-width="34" stroke="rgb(255,255,254)" writing-mode="lr" x="2057" xml:space="preserve" y="1079">053</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="33" font-size="33" font-width="33" stroke="rgb(255,255,254)" writing-mode="lr" x="1084" xml:space="preserve" y="1372">10kVI母TV</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="34" font-size="34" font-width="34" stroke="rgb(255,255,254)" writing-mode="lr" x="1760" xml:space="preserve" y="570">3011</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="63550" xml:space="preserve" y="15241">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="841857" xml:space="preserve" y="205737">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="856040" xml:space="preserve" y="210271">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="857815" xml:space="preserve" y="210836">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="887950" xml:space="preserve" y="220464">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="905668" xml:space="preserve" y="226154">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="962391" xml:space="preserve" y="246927">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="2236403" xml:space="preserve" y="743405">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="2240393" xml:space="preserve" y="749715">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="2265304" xml:space="preserve" y="785079">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="2291413" xml:space="preserve" y="822306">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="2335620" xml:space="preserve" y="883380">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="34" font-size="34" font-width="34" stroke="rgb(255,255,254)" writing-mode="lr" x="1594" xml:space="preserve" y="645">30117</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(35,169,128)" writing-mode="lr" x="1870" xml:space="preserve" y="675">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(35,169,128)" writing-mode="lr" x="1871" xml:space="preserve" y="646">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(35,169,128)" writing-mode="lr" x="1874" xml:space="preserve" y="617">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(35,169,128)" writing-mode="lr" x="1870" xml:space="preserve" y="875">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(35,169,128)" writing-mode="lr" x="1870" xml:space="preserve" y="904">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(35,169,128)" writing-mode="lr" x="1870" xml:space="preserve" y="933">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="33" font-size="33" font-width="33" stroke="rgb(255,255,254)" writing-mode="lr" x="1292" xml:space="preserve" y="1375">2号站用变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="34" font-size="34" font-width="34" stroke="rgb(255,255,254)" writing-mode="lr" x="1752" xml:space="preserve" y="940">001</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(35,169,128)" writing-mode="lr" x="1509" xml:space="preserve" y="1413">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(35,169,128)" writing-mode="lr" x="1509" xml:space="preserve" y="1384">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(35,169,128)" writing-mode="lr" x="1509" xml:space="preserve" y="1354">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="36" font-size="36" font-width="36" stroke="rgb(255,255,254)" writing-mode="lr" x="1480" xml:space="preserve" y="1465">景讷政府线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="34" font-size="34" font-width="34" stroke="rgb(255,255,254)" writing-mode="lr" x="1477" xml:space="preserve" y="1194">0517</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="34" font-size="34" font-width="34" stroke="rgb(255,255,254)" writing-mode="lr" x="1585" xml:space="preserve" y="1079">051</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(35,169,128)" writing-mode="lr" x="1738" xml:space="preserve" y="1414">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(35,169,128)" writing-mode="lr" x="1738" xml:space="preserve" y="1385">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(35,169,128)" writing-mode="lr" x="1738" xml:space="preserve" y="1355">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="36" font-size="36" font-width="36" stroke="rgb(255,255,254)" writing-mode="lr" x="1740" xml:space="preserve" y="1466">南肯河线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="34" font-size="34" font-width="34" stroke="rgb(255,255,254)" writing-mode="lr" x="1711" xml:space="preserve" y="1195">0527</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="34" font-size="34" font-width="34" stroke="rgb(255,255,254)" writing-mode="lr" x="1814" xml:space="preserve" y="1079">052</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(35,169,128)" writing-mode="lr" x="2219" xml:space="preserve" y="1414">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(35,169,128)" writing-mode="lr" x="2219" xml:space="preserve" y="1385">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(35,169,128)" writing-mode="lr" x="2219" xml:space="preserve" y="1355">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="36" font-size="36" font-width="36" stroke="rgb(255,255,254)" writing-mode="lr" x="2171" xml:space="preserve" y="1466">云盘水、黄草岭水库线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="33" font-size="33" font-width="33" stroke="rgb(255,255,254)" writing-mode="lr" x="2196" xml:space="preserve" y="1194">0547</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="34" font-size="34" font-width="34" stroke="rgb(255,255,254)" writing-mode="lr" x="2295" xml:space="preserve" y="1079">054</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="34" font-size="34" font-width="34" stroke="rgb(255,255,254)" writing-mode="lr" x="1757" xml:space="preserve" y="392">3016</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="35" font-size="35" font-width="35" stroke="rgb(230,232,254)" writing-mode="lr" x="1679" xml:space="preserve" y="196">茶讷线</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="40" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="166" xml:space="preserve" y="1178">油温</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1079" xml:space="preserve" y="1145">0901</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1387" xml:space="preserve" y="1082">0R2</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href="综自改造勐仑变.other.svg"><rect fill-opacity="0" height="106" stroke-opacity="0" stroke-width="2" width="440" x="42" y="110"/></g>
</g>
</svg>