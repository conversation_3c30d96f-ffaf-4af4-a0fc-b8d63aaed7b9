<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="sys" height="1000" id="thSvg" viewBox="0 0 2100 1000" width="2100">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="Protect:bn_保护图元1_0" viewBox="0,0,18,18">
 <rect AFMask="2147483647" Plane="0" fill="none" height="13" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,8,8)" width="13" x="2" y="2"/>
</symbol>
<symbol id="Protect:bn_保护图元1_1" viewBox="0,0,18,18">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="13" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,8,8)" width="13" x="2" y="2"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="-1" InitShowingPlane="0," fill="rgb(0,0,0)" height="1000" width="2100" x="0" y="0"/>
</g>
<g id="Other_Layer">
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="688" y1="87" y2="87"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="688" y1="128" y2="128"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="15" x2="15" y1="87" y2="972"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="180" x2="180" y1="89" y2="965"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="596" x2="596" y1="88" y2="964"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="689" x2="689" y1="88" y2="969"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="180" x2="689" y1="166" y2="166"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="621" y1="326" y2="326"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="621" y1="966" y2="966"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="214" x2="621" y1="326" y2="326"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="15" x2="621" y1="526" y2="526"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="180" x2="689" y1="206" y2="206"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="180" x2="689" y1="246" y2="246"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="180" x2="689" y1="286" y2="286"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="180" x2="689" y1="326" y2="326"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="180" x2="689" y1="366" y2="366"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="180" x2="689" y1="406" y2="406"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="180" x2="689" y1="446" y2="446"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="180" x2="689" y1="486" y2="486"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="180" x2="689" y1="526" y2="526"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="180" x2="689" y1="566" y2="566"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="180" x2="689" y1="606" y2="606"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="180" x2="689" y1="646" y2="646"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="180" x2="689" y1="686" y2="686"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="180" x2="689" y1="726" y2="726"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="180" x2="689" y1="766" y2="766"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="180" x2="689" y1="806" y2="806"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="180" x2="689" y1="846" y2="846"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="180" x2="689" y1="886" y2="886"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="180" x2="689" y1="926" y2="926"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="180" x2="689" y1="966" y2="966"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="716" x2="1388" y1="87" y2="87"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="716" x2="716" y1="88" y2="964"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="877" x2="877" y1="88" y2="964"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1296" x2="1296" y1="88" y2="964"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1388" x2="1388" y1="88" y2="964"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="716" x2="1388" y1="128" y2="128"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="877" x2="1386" y1="166" y2="166"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="877" x2="1386" y1="206" y2="206"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="877" x2="1386" y1="246" y2="246"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="877" x2="1386" y1="286" y2="286"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="877" x2="1386" y1="326" y2="326"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="877" x2="1386" y1="366" y2="366"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="716" x2="1386" y1="406" y2="406"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="877" x2="1386" y1="446" y2="446"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="877" x2="1386" y1="486" y2="486"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="877" x2="1386" y1="526" y2="526"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="716" x2="1386" y1="566" y2="566"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="877" x2="1386" y1="606" y2="606"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="877" x2="1386" y1="646" y2="646"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="877" x2="1386" y1="686" y2="686"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="718" x2="1386" y1="726" y2="726"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="877" x2="1386" y1="766" y2="766"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="877" x2="1386" y1="806" y2="806"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="877" x2="1386" y1="846" y2="846"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="716" x2="1386" y1="886" y2="886"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="877" x2="1386" y1="926" y2="926"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="715" x2="1386" y1="966" y2="966"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1416" x2="2088" y1="87" y2="87"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1416" x2="1416" y1="88" y2="929"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1576" x2="1576" y1="88" y2="927"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="2086" x2="2086" y1="88" y2="927"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1414" x2="2086" y1="128" y2="128"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1576" x2="2085" y1="166" y2="166"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1992" x2="1992" y1="88" y2="927"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1419" x2="2085" y1="206" y2="206"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1576" x2="2085" y1="246" y2="246"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1576" x2="2085" y1="286" y2="286"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1576" x2="2085" y1="326" y2="326"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1415" x2="2086" y1="366" y2="366"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1576" x2="2085" y1="406" y2="406"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1576" x2="2085" y1="446" y2="446"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1576" x2="2085" y1="486" y2="486"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1417" x2="2085" y1="526" y2="526"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1576" x2="2085" y1="566" y2="566"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1576" x2="2085" y1="606" y2="606"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1576" x2="2085" y1="646" y2="646"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1417" x2="2085" y1="686" y2="686"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1576" x2="2085" y1="726" y2="726"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1576" x2="2085" y1="766" y2="766"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1576" x2="2085" y1="806" y2="806"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1419" x2="2085" y1="846" y2="846"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1418" x2="2085" y1="886" y2="886"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1417" x2="2085" y1="928" y2="928"/>
 
</g>
<g id="Protect_Layer">
 <g id="127000717">
  <use class="kv-1" height="18" transform="rotate(0,642,148) scale(1,1) translate(-9,-9)" width="18" x="642" xlink:href="#Protect:bn_保护图元1_0" y="148"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,642,148) scale(1,1) translate(-9,-9)" width="18" x="642" y="148"/></g>
 <g id="127000718">
  <use class="kv-1" height="18" transform="rotate(0,642,188) scale(1,1) translate(-9,-9)" width="18" x="642" xlink:href="#Protect:bn_保护图元1_0" y="188"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,642,188) scale(1,1) translate(-9,-9)" width="18" x="642" y="188"/></g>
 <g id="127000719">
  <use class="kv-1" height="18" transform="rotate(0,642,228) scale(1,1) translate(-9,-9)" width="18" x="642" xlink:href="#Protect:bn_保护图元1_0" y="228"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,642,228) scale(1,1) translate(-9,-9)" width="18" x="642" y="228"/></g>
 <g id="127000720">
  <use class="kv-1" height="18" transform="rotate(0,642,268) scale(1,1) translate(-9,-9)" width="18" x="642" xlink:href="#Protect:bn_保护图元1_0" y="268"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,642,268) scale(1,1) translate(-9,-9)" width="18" x="642" y="268"/></g>
 <g id="127000721">
  <use class="kv-1" height="18" transform="rotate(0,642,308) scale(1,1) translate(-9,-9)" width="18" x="642" xlink:href="#Protect:bn_保护图元1_0" y="308"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,642,308) scale(1,1) translate(-9,-9)" width="18" x="642" y="308"/></g>
 <g id="127000722">
  <use class="kv-1" height="18" transform="rotate(0,642,348) scale(1,1) translate(-9,-9)" width="18" x="642" xlink:href="#Protect:bn_保护图元1_0" y="348"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,642,348) scale(1,1) translate(-9,-9)" width="18" x="642" y="348"/></g>
 <g id="127000723">
  <use class="kv-1" height="18" transform="rotate(0,642,388) scale(1,1) translate(-9,-9)" width="18" x="642" xlink:href="#Protect:bn_保护图元1_0" y="388"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,642,388) scale(1,1) translate(-9,-9)" width="18" x="642" y="388"/></g>
 <g id="127000724">
  <use class="kv-1" height="18" transform="rotate(0,642,428) scale(1,1) translate(-9,-9)" width="18" x="642" xlink:href="#Protect:bn_保护图元1_0" y="428"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,642,428) scale(1,1) translate(-9,-9)" width="18" x="642" y="428"/></g>
 <g id="127000725">
  <use class="kv-1" height="18" transform="rotate(0,642,468) scale(1,1) translate(-9,-9)" width="18" x="642" xlink:href="#Protect:bn_保护图元1_0" y="468"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,642,468) scale(1,1) translate(-9,-9)" width="18" x="642" y="468"/></g>
 <g id="127000726">
  <use class="kv-1" height="18" transform="rotate(0,642,508) scale(1,1) translate(-9,-9)" width="18" x="642" xlink:href="#Protect:bn_保护图元1_0" y="508"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,642,508) scale(1,1) translate(-9,-9)" width="18" x="642" y="508"/></g>
 <g id="127000727">
  <use class="kv-1" height="18" transform="rotate(0,642,548) scale(1,1) translate(-9,-9)" width="18" x="642" xlink:href="#Protect:bn_保护图元1_0" y="548"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,642,548) scale(1,1) translate(-9,-9)" width="18" x="642" y="548"/></g>
 <g id="127000728">
  <use class="kv-1" height="18" transform="rotate(0,642,588) scale(1,1) translate(-9,-9)" width="18" x="642" xlink:href="#Protect:bn_保护图元1_0" y="588"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,642,588) scale(1,1) translate(-9,-9)" width="18" x="642" y="588"/></g>
 <g id="127000729">
  <use class="kv-1" height="18" transform="rotate(0,642,628) scale(1,1) translate(-9,-9)" width="18" x="642" xlink:href="#Protect:bn_保护图元1_0" y="628"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,642,628) scale(1,1) translate(-9,-9)" width="18" x="642" y="628"/></g>
 <g id="127000730">
  <use class="kv-1" height="18" transform="rotate(0,642,668) scale(1,1) translate(-9,-9)" width="18" x="642" xlink:href="#Protect:bn_保护图元1_0" y="668"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,642,668) scale(1,1) translate(-9,-9)" width="18" x="642" y="668"/></g>
 <g id="127000731">
  <use class="kv-1" height="18" transform="rotate(0,642,708) scale(1,1) translate(-9,-9)" width="18" x="642" xlink:href="#Protect:bn_保护图元1_0" y="708"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,642,708) scale(1,1) translate(-9,-9)" width="18" x="642" y="708"/></g>
 <g id="127000732">
  <use class="kv-1" height="18" transform="rotate(0,642,748) scale(1,1) translate(-9,-9)" width="18" x="642" xlink:href="#Protect:bn_保护图元1_0" y="748"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,642,748) scale(1,1) translate(-9,-9)" width="18" x="642" y="748"/></g>
 <g id="127000733">
  <use class="kv-1" height="18" transform="rotate(0,642,788) scale(1,1) translate(-9,-9)" width="18" x="642" xlink:href="#Protect:bn_保护图元1_0" y="788"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,642,788) scale(1,1) translate(-9,-9)" width="18" x="642" y="788"/></g>
 <g id="127000734">
  <use class="kv-1" height="18" transform="rotate(0,642,828) scale(1,1) translate(-9,-9)" width="18" x="642" xlink:href="#Protect:bn_保护图元1_0" y="828"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,642,828) scale(1,1) translate(-9,-9)" width="18" x="642" y="828"/></g>
 <g id="127000735">
  <use class="kv-1" height="18" transform="rotate(0,642,868) scale(1,1) translate(-9,-9)" width="18" x="642" xlink:href="#Protect:bn_保护图元1_0" y="868"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,642,868) scale(1,1) translate(-9,-9)" width="18" x="642" y="868"/></g>
 <g id="127000736">
  <use class="kv-1" height="18" transform="rotate(0,642,908) scale(1,1) translate(-9,-9)" width="18" x="642" xlink:href="#Protect:bn_保护图元1_0" y="908"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,642,908) scale(1,1) translate(-9,-9)" width="18" x="642" y="908"/></g>
 <g id="127000737">
  <use class="kv-1" height="18" transform="rotate(0,642,948) scale(1,1) translate(-9,-9)" width="18" x="642" xlink:href="#Protect:bn_保护图元1_0" y="948"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,642,948) scale(1,1) translate(-9,-9)" width="18" x="642" y="948"/></g>
 <g id="127000766">
  <use class="kv-1" height="18" transform="rotate(0,1340,148) scale(1,1) translate(-9,-9)" width="18" x="1340" xlink:href="#Protect:bn_保护图元1_0" y="148"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1340,148) scale(1,1) translate(-9,-9)" width="18" x="1340" y="148"/></g>
 <g id="127000795">
  <use class="kv-1" height="18" transform="rotate(0,1340,188) scale(1,1) translate(-9,-9)" width="18" x="1340" xlink:href="#Protect:bn_保护图元1_0" y="188"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1340,188) scale(1,1) translate(-9,-9)" width="18" x="1340" y="188"/></g>
 <g id="127000796">
  <use class="kv-1" height="18" transform="rotate(0,1340,228) scale(1,1) translate(-9,-9)" width="18" x="1340" xlink:href="#Protect:bn_保护图元1_0" y="228"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1340,228) scale(1,1) translate(-9,-9)" width="18" x="1340" y="228"/></g>
 <g id="127000797">
  <use class="kv-1" height="18" transform="rotate(0,1340,268) scale(1,1) translate(-9,-9)" width="18" x="1340" xlink:href="#Protect:bn_保护图元1_0" y="268"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1340,268) scale(1,1) translate(-9,-9)" width="18" x="1340" y="268"/></g>
 <g id="127000798">
  <use class="kv-1" height="18" transform="rotate(0,1340,308) scale(1,1) translate(-9,-9)" width="18" x="1340" xlink:href="#Protect:bn_保护图元1_0" y="308"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1340,308) scale(1,1) translate(-9,-9)" width="18" x="1340" y="308"/></g>
 <g id="127000799">
  <use class="kv-1" height="18" transform="rotate(0,1340,348) scale(1,1) translate(-9,-9)" width="18" x="1340" xlink:href="#Protect:bn_保护图元1_0" y="348"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1340,348) scale(1,1) translate(-9,-9)" width="18" x="1340" y="348"/></g>
 <g id="127000800">
  <use class="kv-1" height="18" transform="rotate(0,1340,388) scale(1,1) translate(-9,-9)" width="18" x="1340" xlink:href="#Protect:bn_保护图元1_0" y="388"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1340,388) scale(1,1) translate(-9,-9)" width="18" x="1340" y="388"/></g>
 <g id="127000801">
  <use class="kv-1" height="18" transform="rotate(0,1340,428) scale(1,1) translate(-9,-9)" width="18" x="1340" xlink:href="#Protect:bn_保护图元1_0" y="428"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1340,428) scale(1,1) translate(-9,-9)" width="18" x="1340" y="428"/></g>
 <g id="127000802">
  <use class="kv-1" height="18" transform="rotate(0,1340,468) scale(1,1) translate(-9,-9)" width="18" x="1340" xlink:href="#Protect:bn_保护图元1_0" y="468"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1340,468) scale(1,1) translate(-9,-9)" width="18" x="1340" y="468"/></g>
 <g id="127000803">
  <use class="kv-1" height="18" transform="rotate(0,1340,508) scale(1,1) translate(-9,-9)" width="18" x="1340" xlink:href="#Protect:bn_保护图元1_0" y="508"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1340,508) scale(1,1) translate(-9,-9)" width="18" x="1340" y="508"/></g>
 <g id="127000804">
  <use class="kv-1" height="18" transform="rotate(0,1340,548) scale(1,1) translate(-9,-9)" width="18" x="1340" xlink:href="#Protect:bn_保护图元1_0" y="548"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1340,548) scale(1,1) translate(-9,-9)" width="18" x="1340" y="548"/></g>
 <g id="127000805">
  <use class="kv-1" height="18" transform="rotate(0,1340,588) scale(1,1) translate(-9,-9)" width="18" x="1340" xlink:href="#Protect:bn_保护图元1_0" y="588"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1340,588) scale(1,1) translate(-9,-9)" width="18" x="1340" y="588"/></g>
 <g id="127000806">
  <use class="kv-1" height="18" transform="rotate(0,1340,628) scale(1,1) translate(-9,-9)" width="18" x="1340" xlink:href="#Protect:bn_保护图元1_0" y="628"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1340,628) scale(1,1) translate(-9,-9)" width="18" x="1340" y="628"/></g>
 <g id="127000807">
  <use class="kv-1" height="18" transform="rotate(0,1340,668) scale(1,1) translate(-9,-9)" width="18" x="1340" xlink:href="#Protect:bn_保护图元1_0" y="668"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1340,668) scale(1,1) translate(-9,-9)" width="18" x="1340" y="668"/></g>
 <g id="127000808">
  <use class="kv-1" height="18" transform="rotate(0,1340,708) scale(1,1) translate(-9,-9)" width="18" x="1340" xlink:href="#Protect:bn_保护图元1_0" y="708"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1340,708) scale(1,1) translate(-9,-9)" width="18" x="1340" y="708"/></g>
 <g id="127000809">
  <use class="kv-1" height="18" transform="rotate(0,1340,748) scale(1,1) translate(-9,-9)" width="18" x="1340" xlink:href="#Protect:bn_保护图元1_0" y="748"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1340,748) scale(1,1) translate(-9,-9)" width="18" x="1340" y="748"/></g>
 <g id="127000810">
  <use class="kv-1" height="18" transform="rotate(0,1340,788) scale(1,1) translate(-9,-9)" width="18" x="1340" xlink:href="#Protect:bn_保护图元1_0" y="788"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1340,788) scale(1,1) translate(-9,-9)" width="18" x="1340" y="788"/></g>
 <g id="127000811">
  <use class="kv-1" height="18" transform="rotate(0,1340,828) scale(1,1) translate(-9,-9)" width="18" x="1340" xlink:href="#Protect:bn_保护图元1_0" y="828"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1340,828) scale(1,1) translate(-9,-9)" width="18" x="1340" y="828"/></g>
 <g id="127000812">
  <use class="kv-1" height="18" transform="rotate(0,1340,868) scale(1,1) translate(-9,-9)" width="18" x="1340" xlink:href="#Protect:bn_保护图元1_0" y="868"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1340,868) scale(1,1) translate(-9,-9)" width="18" x="1340" y="868"/></g>
 <g id="127000813">
  <use class="kv-1" height="18" transform="rotate(0,1340,908) scale(1,1) translate(-9,-9)" width="18" x="1340" xlink:href="#Protect:bn_保护图元1_0" y="908"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1340,908) scale(1,1) translate(-9,-9)" width="18" x="1340" y="908"/></g>
 <g id="127000814">
  <use class="kv-1" height="18" transform="rotate(0,1340,948) scale(1,1) translate(-9,-9)" width="18" x="1340" xlink:href="#Protect:bn_保护图元1_0" y="948"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1340,948) scale(1,1) translate(-9,-9)" width="18" x="1340" y="948"/></g>
 <g id="127000815">
  <use class="kv-1" height="18" transform="rotate(0,2041,147) scale(1,1) translate(-9,-9)" width="18" x="2041" xlink:href="#Protect:bn_保护图元1_0" y="147"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,2041,147) scale(1,1) translate(-9,-9)" width="18" x="2041" y="147"/></g>
 <g id="127000816">
  <use class="kv-1" height="18" transform="rotate(0,2041,187) scale(1,1) translate(-9,-9)" width="18" x="2041" xlink:href="#Protect:bn_保护图元1_0" y="187"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,2041,187) scale(1,1) translate(-9,-9)" width="18" x="2041" y="187"/></g>
 <g id="127000817">
  <use class="kv-1" height="18" transform="rotate(0,2041,227) scale(1,1) translate(-9,-9)" width="18" x="2041" xlink:href="#Protect:bn_保护图元1_0" y="227"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,2041,227) scale(1,1) translate(-9,-9)" width="18" x="2041" y="227"/></g>
 <g id="127000818">
  <use class="kv-1" height="18" transform="rotate(0,2041,267) scale(1,1) translate(-9,-9)" width="18" x="2041" xlink:href="#Protect:bn_保护图元1_0" y="267"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,2041,267) scale(1,1) translate(-9,-9)" width="18" x="2041" y="267"/></g>
 <g id="127000819">
  <use class="kv-1" height="18" transform="rotate(0,2041,307) scale(1,1) translate(-9,-9)" width="18" x="2041" xlink:href="#Protect:bn_保护图元1_0" y="307"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,2041,307) scale(1,1) translate(-9,-9)" width="18" x="2041" y="307"/></g>
 <g id="127000820">
  <use class="kv-1" height="18" transform="rotate(0,2041,347) scale(1,1) translate(-9,-9)" width="18" x="2041" xlink:href="#Protect:bn_保护图元1_0" y="347"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,2041,347) scale(1,1) translate(-9,-9)" width="18" x="2041" y="347"/></g>
 <g id="127000821">
  <use class="kv-1" height="18" transform="rotate(0,2041,387) scale(1,1) translate(-9,-9)" width="18" x="2041" xlink:href="#Protect:bn_保护图元1_0" y="387"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,2041,387) scale(1,1) translate(-9,-9)" width="18" x="2041" y="387"/></g>
 <g id="127000822">
  <use class="kv-1" height="18" transform="rotate(0,2041,427) scale(1,1) translate(-9,-9)" width="18" x="2041" xlink:href="#Protect:bn_保护图元1_0" y="427"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,2041,427) scale(1,1) translate(-9,-9)" width="18" x="2041" y="427"/></g>
 <g id="127000823">
  <use class="kv-1" height="18" transform="rotate(0,2041,467) scale(1,1) translate(-9,-9)" width="18" x="2041" xlink:href="#Protect:bn_保护图元1_0" y="467"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,2041,467) scale(1,1) translate(-9,-9)" width="18" x="2041" y="467"/></g>
 <g id="127000824">
  <use class="kv-1" height="18" transform="rotate(0,2041,507) scale(1,1) translate(-9,-9)" width="18" x="2041" xlink:href="#Protect:bn_保护图元1_0" y="507"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,2041,507) scale(1,1) translate(-9,-9)" width="18" x="2041" y="507"/></g>
 <g id="127000825">
  <use class="kv-1" height="18" transform="rotate(0,2041,547) scale(1,1) translate(-9,-9)" width="18" x="2041" xlink:href="#Protect:bn_保护图元1_0" y="547"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,2041,547) scale(1,1) translate(-9,-9)" width="18" x="2041" y="547"/></g>
 <g id="127000826">
  <use class="kv-1" height="18" transform="rotate(0,2041,587) scale(1,1) translate(-9,-9)" width="18" x="2041" xlink:href="#Protect:bn_保护图元1_0" y="587"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,2041,587) scale(1,1) translate(-9,-9)" width="18" x="2041" y="587"/></g>
 <g id="127000827">
  <use class="kv-1" height="18" transform="rotate(0,2041,627) scale(1,1) translate(-9,-9)" width="18" x="2041" xlink:href="#Protect:bn_保护图元1_0" y="627"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,2041,627) scale(1,1) translate(-9,-9)" width="18" x="2041" y="627"/></g>
 <g id="127000828">
  <use class="kv-1" height="18" transform="rotate(0,2041,667) scale(1,1) translate(-9,-9)" width="18" x="2041" xlink:href="#Protect:bn_保护图元1_0" y="667"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,2041,667) scale(1,1) translate(-9,-9)" width="18" x="2041" y="667"/></g>
 <g id="127000829">
  <use class="kv-1" height="18" transform="rotate(0,2041,707) scale(1,1) translate(-9,-9)" width="18" x="2041" xlink:href="#Protect:bn_保护图元1_0" y="707"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,2041,707) scale(1,1) translate(-9,-9)" width="18" x="2041" y="707"/></g>
 <g id="127000830">
  <use class="kv-1" height="18" transform="rotate(0,2041,747) scale(1,1) translate(-9,-9)" width="18" x="2041" xlink:href="#Protect:bn_保护图元1_0" y="747"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,2041,747) scale(1,1) translate(-9,-9)" width="18" x="2041" y="747"/></g>
 <g id="127000831">
  <use class="kv-1" height="18" transform="rotate(0,2041,787) scale(1,1) translate(-9,-9)" width="18" x="2041" xlink:href="#Protect:bn_保护图元1_0" y="787"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,2041,787) scale(1,1) translate(-9,-9)" width="18" x="2041" y="787"/></g>
 <g id="127000832">
  <use class="kv-1" height="18" transform="rotate(0,2041,827) scale(1,1) translate(-9,-9)" width="18" x="2041" xlink:href="#Protect:bn_保护图元1_0" y="827"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,2041,827) scale(1,1) translate(-9,-9)" width="18" x="2041" y="827"/></g>
 <g id="127000833">
  <use class="kv-1" height="18" transform="rotate(0,2041,867) scale(1,1) translate(-9,-9)" width="18" x="2041" xlink:href="#Protect:bn_保护图元1_0" y="867"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,2041,867) scale(1,1) translate(-9,-9)" width="18" x="2041" y="867"/></g>
 <g id="127000834">
  <use class="kv-1" height="18" transform="rotate(0,2041,907) scale(1,1) translate(-9,-9)" width="18" x="2041" xlink:href="#Protect:bn_保护图元1_0" y="907"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,2041,907) scale(1,1) translate(-9,-9)" width="18" x="2041" y="907"/></g>
</g>
<g id="Text_Layer">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="39" stroke="rgb(85,255,255)" writing-mode="lr" x="634" xml:space="preserve" y="56">药园变保护压板远方投退及信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="53" xml:space="preserve" y="235">163景药线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="72" xml:space="preserve" y="119">线路</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="372" xml:space="preserve" y="118">名称</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="611" xml:space="preserve" y="118">软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="303" xml:space="preserve" y="158">差动保护软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="302" xml:space="preserve" y="198">距离保护软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="278" xml:space="preserve" y="238">零序过流保护软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="288" xml:space="preserve" y="278">停用重合闸软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="265" xml:space="preserve" y="318">双回线相继速动软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="301" xml:space="preserve" y="358">差动保护软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="301" xml:space="preserve" y="398">距离保护软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="275" xml:space="preserve" y="438">零序过流保护软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="288" xml:space="preserve" y="478">停用重合闸软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="265" xml:space="preserve" y="518">双回线相继速动软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="234" xml:space="preserve" y="558">第Ⅰ套保护高压侧电压投入软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="234" xml:space="preserve" y="598">第Ⅰ套保护中压侧电压投入软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="50" xml:space="preserve" y="440">164景大线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="234" xml:space="preserve" y="638">第Ⅰ套保护低压侧电压投入软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="242" xml:space="preserve" y="678">第Ⅰ套保护差动保护投入软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="215" xml:space="preserve" y="718">第Ⅰ保护高压侧后备保护投入软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="233" xml:space="preserve" y="758">第Ⅰ套保护间隙保护投入软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="206" xml:space="preserve" y="798">第Ⅰ套保护中压侧过流保护投入软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="187" xml:space="preserve" y="838">第Ⅰ套保护低压侧1分支过流保护投入软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="190" xml:space="preserve" y="878">第Ⅰ套保护低压侧简易母线保护投入软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="234" xml:space="preserve" y="918">第Ⅱ套保护高压侧电压投入软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="232" xml:space="preserve" y="958">第Ⅱ套保护中压侧电压投入软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="50" xml:space="preserve" y="766">1号主变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="783" xml:space="preserve" y="119">线路</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1047" xml:space="preserve" y="119">名称</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1312" xml:space="preserve" y="119">软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="926" xml:space="preserve" y="158">第Ⅱ套保护低压侧电压投入软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="938" xml:space="preserve" y="198">第Ⅱ套保护差动保护投入软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="903" xml:space="preserve" y="238">第Ⅱ套保护高压侧后备保护投入软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="939" xml:space="preserve" y="278">第Ⅱ套保护间隙保护投入软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="915" xml:space="preserve" y="318">Ⅱ套保护中压侧过流保护投入软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="881" xml:space="preserve" y="358">第Ⅱ套保护低压侧1分支过流保护投入软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="886" xml:space="preserve" y="398">第Ⅱ套保护低压侧简易母线保护投入软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="965" xml:space="preserve" y="438">保护测控低周减载软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="957" xml:space="preserve" y="478">保护测控停用重合闸软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="963" xml:space="preserve" y="518">保护测控低压解列软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="972" xml:space="preserve" y="558">保护测控高周解列软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="965" xml:space="preserve" y="598">保护测控低周减载软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="955" xml:space="preserve" y="638">保护测控停用重合闸软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="763" xml:space="preserve" y="265">1号主变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="966" xml:space="preserve" y="678">保护测控低压解列软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="965" xml:space="preserve" y="718">保护测控高周解列软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="968" xml:space="preserve" y="758">保护测控低周减载软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="965" xml:space="preserve" y="798">保护测控停用重合闸软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="975" xml:space="preserve" y="838">保护测控低压解列软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="969" xml:space="preserve" y="878">保护测控高周解列软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="974" xml:space="preserve" y="918">保护测控低周减载软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="957" xml:space="preserve" y="958">保护测控停用重合闸软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="754" xml:space="preserve" y="499">351药基线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="757" xml:space="preserve" y="655">352药养线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="730" xml:space="preserve" y="810">043勐养政府线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1470" xml:space="preserve" y="119">线路</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1741" xml:space="preserve" y="119"> 名称</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="2011" xml:space="preserve" y="119">软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1611" xml:space="preserve" y="158">044保护测控低压解列软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1429" xml:space="preserve" y="179">044药养联络线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1611" xml:space="preserve" y="198">044保护测控高周解列软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1611" xml:space="preserve" y="238">保护测控低周减载软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1611" xml:space="preserve" y="278">保护测控停用重合闸软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1611" xml:space="preserve" y="318">保护测控低压解列软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1611" xml:space="preserve" y="358">保护测控高周解列软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1611" xml:space="preserve" y="398">保护测控低周减载软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1611" xml:space="preserve" y="438">保护测控停用重合闸软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1611" xml:space="preserve" y="478">保护测控低压解列软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1611" xml:space="preserve" y="518">保护测控高周解列软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1611" xml:space="preserve" y="558">保护测控低周减载软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1611" xml:space="preserve" y="598">保护测控停用重合闸软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1611" xml:space="preserve" y="638">保护测控低压解列软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1611" xml:space="preserve" y="678">保护测控高周解列软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1611" xml:space="preserve" y="718">保护测控低周减载软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1611" xml:space="preserve" y="758">保护测控停用重合闸软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1611" xml:space="preserve" y="798">保护测控低压解列软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1611" xml:space="preserve" y="838">保护测控高周解列软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="730" xml:space="preserve" y="938">044药养联络线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1447" xml:space="preserve" y="918">2号电容器</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1432" xml:space="preserve" y="304">045勐养车站</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1432" xml:space="preserve" y="324">    Ⅰ回线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1445" xml:space="preserve" y="878">1号电容器</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1457" xml:space="preserve" y="762">048河边线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1611" xml:space="preserve" y="878">保护测控低电压保护软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1611" xml:space="preserve" y="918">保护测控低电压保护软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1439" xml:space="preserve" y="456">046勐养车站</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1439" xml:space="preserve" y="476">    Ⅱ回线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1445" xml:space="preserve" y="621">047煤矿线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1953" xml:space="preserve" y="76">下一页</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href="110k药园变保护压板远方投退2.fac.svg"><rect fill-opacity="0" height="30" stroke-opacity="0" stroke-width="1" width="133" x="1931" y="50"/></g>
 <g ChangePicPlane="0," Plane="0" href="版纳_110kV_药园变.fac.svg"><rect fill-opacity="0" height="56" stroke-opacity="0" stroke-width="1" width="623" x="637" y="7"/></g>
</g>
</svg>