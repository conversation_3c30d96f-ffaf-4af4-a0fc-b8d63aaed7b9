<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="sys" height="950" id="thSvg" viewBox="0 0 1800 950" width="1800">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="Protect:bn_保护图元1_0" viewBox="0,0,18,18">
 <rect AFMask="2147483647" Plane="0" fill="none" height="13" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,8,8)" width="13" x="2" y="2"/>
</symbol>
<symbol id="Protect:bn_保护图元1_1" viewBox="0,0,18,18">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="13" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,8,8)" width="13" x="2" y="2"/>
</symbol>
<symbol id="Protect:软压板投退_0" viewBox="0,0,50,50">
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,0,0)" stroke-width="3" x1="40" x2="13" y1="31" y2="4"/>
 <circle AFMask="2147483647" Plane="0" cx="9" cy="31" fill="none" r="4" stroke="rgb(255,0,0)" stroke-width="8"/>
 <circle AFMask="2147483647" Plane="0" cx="40" cy="31" fill="none" r="4" stroke="rgb(255,0,0)" stroke-width="8"/>
</symbol>
<symbol id="Protect:软压板投退_1" viewBox="0,0,50,50">
 <circle AFMask="2147483647" Plane="0" cx="9" cy="24" fill="none" r="4" stroke="rgb(0,255,0)" stroke-width="8"/>
 <circle AFMask="2147483647" Plane="0" cx="40" cy="24" fill="none" r="4" stroke="rgb(0,255,0)" stroke-width="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,255,0)" stroke-width="3" x1="9" x2="41" y1="24" y2="24"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="版纳_35kV_基诺变" InitShowingPlane="0," fill="rgb(0,0,0)" height="950" width="1800" x="0" y="0"/>
</g>
<g id="Other_Layer">
 
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="61" x2="825" y1="334" y2="334"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="960" x2="1725" y1="299" y2="299"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="960" x2="1724" y1="329" y2="329"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="959" x2="959" y1="299" y2="569"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1215" x2="1725" y1="359" y2="359"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1215" x2="1725" y1="389" y2="389"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1215" x2="1725" y1="419" y2="419"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="960" x2="1725" y1="449" y2="449"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1216" x2="1725" y1="479" y2="479"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1215" x2="1725" y1="509" y2="509"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1215" x2="1725" y1="540" y2="540"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1214" x2="1214" y1="299" y2="569"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1727" x2="1727" y1="299" y2="572"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1469" x2="1469" y1="299" y2="573"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="62" x2="827" y1="297" y2="297"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="317" x2="827" y1="357" y2="357"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="60" x2="60" y1="297" y2="627"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="316" x2="316" y1="296" y2="627"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="827" x2="827" y1="296" y2="628"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="572" x2="572" y1="298" y2="625"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="317" x2="827" y1="387" y2="387"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="316" x2="827" y1="417" y2="417"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="316" x2="827" y1="447" y2="447"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="61" x2="827" y1="477" y2="477"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="317" x2="827" y1="507" y2="507"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="317" x2="827" y1="537" y2="537"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="316" x2="827" y1="567" y2="567"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="317" x2="827" y1="597" y2="597"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="61" x2="827" y1="627" y2="627"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="960" x2="1725" y1="570" y2="570"/>
</g>
<g id="Protect_Layer">
 <g id="127000801">
  <use class="kv35kV" height="50" transform="rotate(0,700,344) scale(0.6,0.6) translate(441.667,208.333)" width="50" x="700" xlink:href="#Protect:软压板投退_0" y="344"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892520372" ObjectName="122160139892520372" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,700,344) scale(0.6,0.6) translate(441.667,208.333)" width="50" x="700" y="344"/></g>
 <g id="127000802">
  <use class="kv35kV" height="50" transform="rotate(0,700,374) scale(0.6,0.6) translate(441.667,228.333)" width="50" x="700" xlink:href="#Protect:软压板投退_0" y="374"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892520373" ObjectName="122160139892520373" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,700,374) scale(0.6,0.6) translate(441.667,228.333)" width="50" x="700" y="374"/></g>
 <g id="127000803">
  <use class="kv35kV" height="50" transform="rotate(0,700,404) scale(0.6,0.6) translate(441.667,248.333)" width="50" x="700" xlink:href="#Protect:软压板投退_0" y="404"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892520386" ObjectName="122160139892520386" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,700,404) scale(0.6,0.6) translate(441.667,248.333)" width="50" x="700" y="404"/></g>
 <g id="127000804">
  <use class="kv35kV" height="50" transform="rotate(0,700,434) scale(0.6,0.6) translate(441.667,268.333)" width="50" x="700" xlink:href="#Protect:软压板投退_0" y="434"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892520387" ObjectName="122160139892520387" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,700,434) scale(0.6,0.6) translate(441.667,268.333)" width="50" x="700" y="434"/></g>
 <g id="127000805">
  <use class="kv35kV" height="50" transform="rotate(0,700,464) scale(0.6,0.6) translate(441.667,288.333)" width="50" x="700" xlink:href="#Protect:软压板投退_0" y="464"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892520404" ObjectName="122160139892520404" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,700,464) scale(0.6,0.6) translate(441.667,288.333)" width="50" x="700" y="464"/></g>
 <g id="127000806">
  <use class="kv35kV" height="50" transform="rotate(0,700,494) scale(0.6,0.6) translate(441.667,308.333)" width="50" x="700" xlink:href="#Protect:软压板投退_0" y="494"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892520466" ObjectName="122160139892520466" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,700,494) scale(0.6,0.6) translate(441.667,308.333)" width="50" x="700" y="494"/></g>
 <g id="127000807">
  <use class="kv35kV" height="50" transform="rotate(0,700,524) scale(0.6,0.6) translate(441.667,328.333)" width="50" x="700" xlink:href="#Protect:软压板投退_0" y="524"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892520467" ObjectName="122160139892520467" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,700,524) scale(0.6,0.6) translate(441.667,328.333)" width="50" x="700" y="524"/></g>
 <g id="127000808">
  <use class="kv35kV" height="50" transform="rotate(0,700,555) scale(0.6,0.6) translate(441.667,349)" width="50" x="700" xlink:href="#Protect:软压板投退_0" y="555"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892520480" ObjectName="122160139892520480" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,700,555) scale(0.6,0.6) translate(441.667,349)" width="50" x="700" y="555"/></g>
 <g id="127000809">
  <use class="kv35kV" height="50" transform="rotate(0,700,585) scale(0.6,0.6) translate(441.667,369)" width="50" x="700" xlink:href="#Protect:软压板投退_0" y="585"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892520481" ObjectName="122160139892520481" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,700,585) scale(0.6,0.6) translate(441.667,369)" width="50" x="700" y="585"/></g>
 <g id="127000810">
  <use class="kv35kV" height="50" transform="rotate(0,700,615) scale(0.6,0.6) translate(441.667,389)" width="50" x="700" xlink:href="#Protect:软压板投退_0" y="615"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892520498" ObjectName="122160139892520498" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,700,615) scale(0.6,0.6) translate(441.667,389)" width="50" x="700" y="615"/></g>
 <g id="127000833">
  <use class="kv35kV" height="18" transform="rotate(0,1604,348) scale(1.5,1.5) translate(-543.667,-125)" width="18" x="1604" xlink:href="#Protect:bn_保护图元1_0" y="348"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892520370" ObjectName="122160139892520370" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1604,348) scale(1.5,1.5) translate(-543.667,-125)" width="18" x="1604" y="348"/></g>
 <g id="127000834">
  <use class="kv35kV" height="18" transform="rotate(0,1604,378) scale(1.5,1.5) translate(-543.667,-135)" width="18" x="1604" xlink:href="#Protect:bn_保护图元1_0" y="378"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892520384" ObjectName="122160139892520384" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1604,378) scale(1.5,1.5) translate(-543.667,-135)" width="18" x="1604" y="378"/></g>
 <g id="127000835">
  <use class="kv35kV" height="18" transform="rotate(0,1604,408) scale(1.5,1.5) translate(-543.667,-145)" width="18" x="1604" xlink:href="#Protect:bn_保护图元1_0" y="408"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892520403" ObjectName="122160139892520403" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1604,408) scale(1.5,1.5) translate(-543.667,-145)" width="18" x="1604" y="408"/></g>
 <g id="127000836">
  <use class="kv35kV" height="18" transform="rotate(0,1604,438) scale(1.5,1.5) translate(-543.667,-155)" width="18" x="1604" xlink:href="#Protect:bn_保护图元1_0" y="438"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892520411" ObjectName="122160139892520411" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1604,438) scale(1.5,1.5) translate(-543.667,-155)" width="18" x="1604" y="438"/></g>
 <g id="127000837">
  <use class="kv35kV" height="18" transform="rotate(0,1604,468) scale(1.5,1.5) translate(-543.667,-165)" width="18" x="1604" xlink:href="#Protect:bn_保护图元1_0" y="468"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892520464" ObjectName="122160139892520464" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1604,468) scale(1.5,1.5) translate(-543.667,-165)" width="18" x="1604" y="468"/></g>
 <g id="127000838">
  <use class="kv35kV" height="18" transform="rotate(0,1604,498) scale(1.5,1.5) translate(-543.667,-175)" width="18" x="1604" xlink:href="#Protect:bn_保护图元1_0" y="498"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892520478" ObjectName="122160139892520478" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1604,498) scale(1.5,1.5) translate(-543.667,-175)" width="18" x="1604" y="498"/></g>
 <g id="127000839">
  <use class="kv35kV" height="18" transform="rotate(0,1604,528) scale(1.5,1.5) translate(-543.667,-185)" width="18" x="1604" xlink:href="#Protect:bn_保护图元1_0" y="528"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892520497" ObjectName="122160139892520497" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1604,528) scale(1.5,1.5) translate(-543.667,-185)" width="18" x="1604" y="528"/></g>
 <g id="127000840">
  <use class="kv35kV" height="18" transform="rotate(0,1604,558) scale(1.5,1.5) translate(-543.667,-195)" width="18" x="1604" xlink:href="#Protect:bn_保护图元1_0" y="558"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892520505" ObjectName="122160139892520505" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1604,558) scale(1.5,1.5) translate(-543.667,-195)" width="18" x="1604" y="558"/></g>
</g>
<g id="Text_Layer">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1246" xml:space="preserve" y="567">非电量保护信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1246" xml:space="preserve" y="507">低后备保护信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1256" xml:space="preserve" y="537">差动保护信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1246" xml:space="preserve" y="477">高后备保护信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1246" xml:space="preserve" y="447">非电量保护信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1256" xml:space="preserve" y="417">差动保护信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1246" xml:space="preserve" y="387">低后备保护信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1246" xml:space="preserve" y="357">高后备保护信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="30" font-width="30" stroke="rgb(85,255,255)" writing-mode="lr" x="664" xml:space="preserve" y="53">基诺变继电保护远方操作</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1070" xml:space="preserve" y="326">线路</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1055" xml:space="preserve" y="401">1号主变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1053" xml:space="preserve" y="520">2号主变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1318" xml:space="preserve" y="326">名称</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1548" xml:space="preserve" y="326">信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="172" xml:space="preserve" y="324">线路</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="150" xml:space="preserve" y="416">1号主变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="153" xml:space="preserve" y="563">2号主变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="427" xml:space="preserve" y="324">名称</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="375" xml:space="preserve" y="354">间隙保护软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="375" xml:space="preserve" y="384">高侧电压软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="375" xml:space="preserve" y="414">母线保护软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="375" xml:space="preserve" y="444">低侧电压软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="375" xml:space="preserve" y="474">差动保护软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="375" xml:space="preserve" y="504">间隙保护软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="375" xml:space="preserve" y="534">高侧电压软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="375" xml:space="preserve" y="564">母线保护软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="375" xml:space="preserve" y="594">低侧电压软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="375" xml:space="preserve" y="624">差动保护软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="673" xml:space="preserve" y="324">软压板</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href="版纳_35kV_基诺山变.fac.svg"><rect fill-opacity="0" height="37" stroke-opacity="0" stroke-width="1" width="354" x="656" y="19"/></g>
</g>
</svg>