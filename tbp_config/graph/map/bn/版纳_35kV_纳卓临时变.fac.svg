<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="113997365567815915" height="1500" id="thSvg" viewBox="0 0 2900 1500" width="2900">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="Arrester:bn_避雷器3_0" viewBox="0,0,20,40">
 <use Plane="0" x="10" xlink:href="#terminal" y="4"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="22" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,10,20)" width="10" x="5" y="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="31" y2="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="5" y2="18"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="5" x2="15" y1="35" y2="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="7" x2="13" y1="37" y2="37"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="11" y1="39" y2="39"/>
 <path AFMask="2147483647" Plane="0" d="M 8 17 L 10 23 L 12 17 Z" fill="none" stroke="rgb(93,92,88)" stroke-width="1"/>
</symbol>
<symbol id="Status:bn_不变化颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(0,0,255)" height="1" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_不变化颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(85,0,255)" height="1" stroke="rgb(85,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="PT:bn_电压互感器_移动变_0" viewBox="0,0,100,70">
 <use Plane="0" x="21" xlink:href="#terminal" y="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="52" x2="52" y1="52" y2="44"/>
 <circle AFMask="2147483647" Plane="0" cx="63" cy="34" fill="none" r="8" stroke="rgb(255,255,254)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="49" cy="34" fill="none" r="8" stroke="rgb(255,255,254)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="58" x2="64" y1="35" y2="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="64" x2="68" y1="35" y2="40"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="42" x2="48" y1="35" y2="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="48" x2="51" y1="35" y2="29"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="48" x2="52" y1="35" y2="40"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="41" x2="22" y1="35" y2="35"/>
 <circle AFMask="2147483647" Plane="0" cx="49" cy="48" fill="none" r="8" stroke="rgb(255,255,254)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="63" cy="48" fill="none" r="8" stroke="rgb(255,255,254)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="63" x2="67" y1="49" y2="54"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="63" x2="66" y1="49" y2="43"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="57" x2="63" y1="49" y2="49"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="64" x2="67" y1="35" y2="29"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="45" x2="52" y1="46" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="45" x2="52" y1="50" y2="52"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="48" x2="48" y1="35" y2="19"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="63" x2="63" y1="35" y2="19"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="75" x2="75" y1="40" y2="19"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="64" x2="75" y1="49" y2="40"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="82" x2="48" y1="19" y2="19"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="83" x2="83" y1="15" y2="23"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="86" x2="86" y1="16" y2="22"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="89" x2="89" y1="18" y2="20"/>
</symbol>
<symbol id="Breaker:bn_断路器1_0" viewBox="0,0,20,40">
 <use Plane="0" x="9" xlink:href="#terminal" y="4"/>
 <use Plane="0" x="9" xlink:href="#terminal" y="34"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="30" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,10,20)" width="14" x="3" y="5"/>
</symbol>
<symbol id="Breaker:bn_断路器1_1" viewBox="0,0,20,40">
 <use Plane="0" x="9" xlink:href="#terminal" y="4"/>
 <use Plane="0" x="9" xlink:href="#terminal" y="34"/>
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,254)" height="30" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,10,20)" width="14" x="3" y="5"/>
</symbol>
<symbol id="Status:bn_非实测颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,85,0)" height="1" stroke="rgb(170,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_非实测颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,85,0)" height="1" stroke="rgb(170,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_工况退出颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,170,255)" height="1" stroke="rgb(170,170,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_工况退出颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,170,255)" height="1" stroke="rgb(170,170,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸12_0" viewBox="0,0,40,20">
 <use Plane="0" x="4" xlink:href="#terminal" y="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="31" y1="6" y2="18"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="28" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="33" y1="8" y2="16"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="35" x2="35" y1="10" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="28" y1="2" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="4" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="9" y1="5" y2="19"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸12_1" viewBox="0,0,40,20">
 <use Plane="0" x="4" xlink:href="#terminal" y="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="31" y1="6" y2="18"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="3" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="33" y1="8" y2="16"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="35" x2="35" y1="10" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="9" y1="5" y2="19"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸22_0" viewBox="0,0,34,16">
 <use Plane="0" x="31" xlink:href="#terminal" y="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="27" x2="27" y1="3" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="6" y1="8" y2="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="3" y1="4" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="1" x2="1" y1="6" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="30" x2="27" y1="8" y2="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="5" x2="5" y1="3" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="25" y1="8" y2="2"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸22_1" viewBox="0,0,34,16">
 <use Plane="0" x="31" xlink:href="#terminal" y="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="27" x2="27" y1="3" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="30" x2="6" y1="8" y2="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="3" y1="4" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="1" x2="1" y1="6" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="5" x2="5" y1="3" y2="13"/>
</symbol>
<symbol id="Transformer2:bn_两卷变压器004_0" viewBox="0,0,40,80">
 <use Plane="0" x="19" xlink:href="#terminal" y="3"/>
 <circle AFMask="2147483647" Plane="0" cx="19" cy="21" fill="none" r="18" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="20" x2="27" y1="21" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="20" x2="12" y1="21" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="20" x2="20" y1="21" y2="31"/>
</symbol>
<symbol id="Transformer2:bn_两卷变压器004_1" viewBox="0,0,40,80">
 <circle AFMask="2147483647" Plane="1" cx="19" cy="53" fill="none" r="18" stroke="rgb(0,0,255)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 19 47 L 10 60 L 29 60 Z" fill="none" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Transformer2:bn_两卷变压器004_2" viewBox="0,0,40,80">
 <use Plane="2" x="20" xlink:href="#terminal" y="21"/>
</symbol>
<symbol id="Transformer2:bn_两卷变压器10_0" viewBox="0,0,50,76">
 <use Plane="0" x="26" xlink:href="#terminal" y="3"/>
 <circle AFMask="2147483647" Plane="0" cx="25" cy="21" fill="none" r="18" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="25" x2="33" y1="21" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="26" x2="18" y1="21" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="25" x2="25" y1="21" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="4" x2="45" y1="44" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="45" x2="45" y1="3" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="45" x2="43" y1="3" y2="3"/>
</symbol>
<symbol id="Transformer2:bn_两卷变压器10_1" viewBox="0,0,50,76">
 <use Plane="1" x="26" xlink:href="#terminal" y="72"/>
 <circle AFMask="2147483647" Plane="1" cx="25" cy="53" fill="none" r="18" stroke="rgb(0,0,255)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 25 47 L 16 60 L 35 60 Z" fill="none" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Transformer2:bn_两卷变压器10_2" viewBox="0,0,50,76">
 <use Plane="2" x="25" xlink:href="#terminal" y="21"/>
</symbol>
<symbol id="Fuse:bn_熔断器11_0" viewBox="0,0,32,16">
 <use Plane="0" x="29" xlink:href="#terminal" y="7"/>
 <use Plane="0" x="2" xlink:href="#terminal" y="7"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="8" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,16,8)" width="18" x="7" y="4"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="5" x2="27" y1="8" y2="8"/>
</symbol>
<symbol id="Disconnector:bn_手车熔断器_0" viewBox="0,0,20,70">
 <use Plane="0" x="9" xlink:href="#terminal" y="66"/>
 <use Plane="0" x="9" xlink:href="#terminal" y="3"/>
 <path AFMask="2147483647" Plane="0" d="M 3 17 L 9 11 L 15 17 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="57" y2="12"/>
 <path AFMask="2147483647" Plane="0" d="M 3 11 L 9 5 L 15 12 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="0" d="M 3 51 L 9 57 L 15 51 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="0" d="M 3 57 L 9 63 L 15 57 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="26" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,10,34)" width="12" x="4" y="21"/>
</symbol>
<symbol id="Disconnector:bn_手车熔断器_1" viewBox="0,0,20,70">
 <use Plane="0" x="9" xlink:href="#terminal" y="66"/>
 <use Plane="0" x="9" xlink:href="#terminal" y="3"/>
 <path AFMask="2147483647" Plane="0" d="M 4 57 L 9 62 L 14 57 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
 <path AFMask="2147483647" Plane="0" d="M 4 11 L 9 6 L 14 11 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="26" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,10,34)" width="12" x="4" y="21"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="9" y1="9" y2="60"/>
</symbol>
<symbol id="Status:bn_数据封锁颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,85,0)" height="1" stroke="rgb(255,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_数据封锁颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,85,0)" height="1" stroke="rgb(255,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关1_0_0" viewBox="0,0,74,22">
 <use Plane="0" x="4" xlink:href="#terminal" y="10"/>
 <use Plane="0" x="68" xlink:href="#terminal" y="10"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="12" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,37,11)" width="27" x="24" y="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="11" x2="23" y1="11" y2="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="51" x2="63" y1="11" y2="11"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关1_0_1" viewBox="0,0,74,22">
 <use Plane="0" x="4" xlink:href="#terminal" y="10"/>
 <use Plane="0" x="68" xlink:href="#terminal" y="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="51" x2="63" y1="11" y2="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="11" x2="23" y1="11" y2="11"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="12" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,36,11)" width="27" x="23" y="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="3" x1="8" x2="16" y1="11" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="3" x1="8" x2="16" y1="11" y2="19"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="3" x1="56" x2="64" y1="19" y2="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="3" x1="56" x2="64" y1="3" y2="11"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关1_0_2" viewBox="0,0,74,22">
 <use Plane="0" x="4" xlink:href="#terminal" y="10"/>
 <use Plane="0" x="68" xlink:href="#terminal" y="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="49" x2="63" y1="11" y2="11"/>
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,254)" height="12" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,36,11)" width="27" x="23" y="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="11" x2="23" y1="11" y2="11"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关1_0_3" viewBox="0,0,74,22">
 <use Plane="0" x="4" xlink:href="#terminal" y="10"/>
 <use Plane="0" x="68" xlink:href="#terminal" y="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="11" x2="25" y1="11" y2="11"/>
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,254)" height="12" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,36,11)" width="26" x="23" y="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="49" x2="63" y1="11" y2="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="3" x1="9" x2="17" y1="11" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="3" x1="57" x2="65" y1="3" y2="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="3" x1="9" x2="17" y1="11" y2="19"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="3" x1="57" x2="65" y1="19" y2="11"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关1_1_0" viewBox="0,0,74,22">
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="13" x2="7" y1="5" y2="11"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="7" x2="13" y1="11" y2="17"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="10" y1="5" y2="11"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="16" y1="11" y2="17"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="63" x2="57" y1="11" y2="17"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="58" x2="64" y1="5" y2="11"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="61" x2="67" y1="5" y2="11"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="66" x2="60" y1="11" y2="17"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关1_1_1" viewBox="0,0,74,22">
</symbol>
<symbol id="DollyBreaker:bn_小车开关1_1_2" viewBox="0,0,74,22">
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="13" x2="7" y1="5" y2="11"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="7" x2="13" y1="11" y2="17"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="10" y1="5" y2="11"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="16" y1="11" y2="17"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="57" x2="63" y1="5" y2="11"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="60" x2="66" y1="5" y2="11"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="66" x2="60" y1="11" y2="17"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="63" x2="57" y1="11" y2="17"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关1_1_3" viewBox="0,0,74,22">
</symbol>
<symbol id="PT:bn_移动变电压互感器111_0" viewBox="0,0,60,60">
 <use Plane="0" x="28" xlink:href="#terminal" y="5"/>
 <circle AFMask="2147483647" Plane="0" cx="33" cy="43" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="33" cy="33" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="23" cy="33" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="23" cy="43" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="34" x2="37" y1="33" y2="30"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="33" x2="33" y1="34" y2="38"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="30" x2="33" y1="30" y2="33"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="18" x2="26" y1="35" y2="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="24" x2="26" y1="31" y2="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="21" x2="18" y1="31" y2="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="40" x2="46" y1="33" y2="33"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="47" x2="47" y1="33" y2="55"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="42" x2="52" y1="55" y2="55"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="44" x2="50" y1="57" y2="57"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="46" x2="48" y1="59" y2="59"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="11" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,47,40)" width="4" x="45" y="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="42" x2="51" y1="38" y2="44"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="12" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,29,15)" width="6" x="26" y="9"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="0" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,26,19)" width="0" x="26" y="19"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="29" x2="29" y1="30" y2="7"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="45" x2="47" y1="48" y2="50"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="47" x2="49" y1="50" y2="48"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="44" x2="47" y1="50" y2="52"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="50" x2="47" y1="49" y2="52"/>
 <circle AFMask="2147483647" Plane="0" cx="28" cy="51" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="28" x2="28" y1="52" y2="56"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="29" x2="32" y1="51" y2="48"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="25" x2="28" y1="48" y2="51"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="23" x2="23" y1="43" y2="47"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="24" x2="27" y1="42" y2="39"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="20" x2="23" y1="39" y2="42"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="33" x2="33" y1="42" y2="46"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="34" x2="37" y1="41" y2="38"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="30" x2="33" y1="38" y2="41"/>
</symbol>
<symbol id="Status:bn_越限颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_越限颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Terminal:bn_终端设备10_0" viewBox="0,0,38,12">
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,255,0)" stroke-width="1" x1="2" x2="7" y1="6" y2="6"/>
 <polygon AFMask="2147483647" Plane="0" fill="none" points="7,3 14,5 7,8" stroke="rgb(0,255,0)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,255,0)" stroke-width="1" x1="12" x2="22" y1="6" y2="6"/>
 <polygon AFMask="2147483647" Plane="0" fill="none" points="21,5 28,3 28,8" stroke="rgb(0,255,0)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,255,0)" stroke-width="1" x1="27" x2="32" y1="6" y2="6"/>
</symbol>
<symbol id="GZP:gg_光子牌1_0" viewBox="0,0,36,36">
 <circle AFMask="2147483647" Plane="0" cx="18" cy="18" fill="rgb(0,255,0)" r="15" stroke="rgb(0,255,0)" stroke-width="1"/>
</symbol>
<symbol id="GZP:gg_光子牌1_1" viewBox="0,0,36,36">
 <circle AFMask="2147483647" Plane="0" cx="18" cy="18" fill="rgb(255,0,0)" r="15" stroke="rgb(255,0,0)" stroke-width="1"/>
</symbol>
<symbol id="Disconnector:n_刀闸11_0" viewBox="0,0,16,30">
 <use Plane="0" x="7" xlink:href="#terminal" y="3"/>
 <use Plane="0" x="7" xlink:href="#terminal" y="26"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="26" y2="22"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="7" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="13" y1="22" y2="22"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="1" y1="7" y2="19"/>
</symbol>
<symbol id="Disconnector:n_刀闸11_1" viewBox="0,0,16,30">
 <use Plane="0" x="7" xlink:href="#terminal" y="3"/>
 <use Plane="0" x="7" xlink:href="#terminal" y="26"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="26" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="13" y1="22" y2="22"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="-1" InitShowingPlane="0," fill="rgb(0,0,0)" height="1500" width="2900" x="0" y="0"/>
</g>
<g id="Other_Layer">
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="35" x2="392" y1="28" y2="28"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="392" x2="392" y1="30" y2="1469"/>
 
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="35" x2="392" y1="148" y2="148"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="34" x2="34" y1="30" y2="1469"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="122" x2="122" y1="382" y2="624"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="197" x2="197" y1="627" y2="787"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="122" x2="122" y1="870" y2="1029"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="35" x2="392" y1="1470" y2="1470"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="35" x2="392" y1="381" y2="381"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="122" x2="392" y1="463" y2="463"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="122" x2="392" y1="544" y2="544"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="34" x2="392" y1="625" y2="625"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="34" x2="392" y1="705" y2="705"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="35" x2="392" y1="787" y2="787"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="34" x2="392" y1="868" y2="868"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="122" x2="392" y1="949" y2="949"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="34" x2="392" y1="1030" y2="1030"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="34" x2="392" y1="1112" y2="1112"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="34" x2="392" y1="1193" y2="1193"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="36" x2="388" y1="247" y2="247"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="324" x2="324" y1="251" y2="378"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="252" x2="252" y1="251" y2="378"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="179" x2="179" y1="251" y2="378"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="106" x2="106" y1="251" y2="378"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="36" x2="388" y1="312" y2="312"/>
 
</g>
<g id="Bus_Layer">
 <g id="30003227">
  <path d="M 863 1000 L 2642 1000" stroke="rgb(0,0,255)" stroke-width="4"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115404740451369760" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\BS_10kVI母" Plane="0"/>
  <cge:TPSR_Ref TObjectID="115404740451369760"/></metadata>
 <path d="M 863 1000 L 2642 1000" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="Breaker_Layer">
 <g id="100003357">
  <use class="kv35kV" height="40" transform="rotate(0,1777,561) scale(1.675,1.5) translate(-726.104,-207)" width="20" x="1777" xlink:href="#Breaker:bn_断路器1_0" y="561"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521242451" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/35kV\CB_1号主变高压侧301断路器开关" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319934291"/>
  <cge:TPSR_Ref TObjectID="114560315521242451"/></metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1777,561) scale(1.675,1.5) translate(-726.104,-207)" width="20" x="1777" y="561"/></g>
</g>
<g id="Disconnector_Layer">
 <g id="101003311">
  <use class="kv35kV" height="30" transform="rotate(0,1778,434) scale(1.799,1.799) translate(-797.673,-207.755)" width="16" x="1778" xlink:href="#Disconnector:n_刀闸11_0" y="434"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497958452" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/35kV\SW_3011刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346977332"/>
  <cge:TPSR_Ref TObjectID="114841790497958452"/></metadata>
 <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1778,434) scale(1.799,1.799) translate(-797.673,-207.755)" width="16" x="1778" y="434"/></g>
 <g id="101003400">
  <use class="kv10kV" height="70" transform="rotate(0,1370,1073) scale(1.3,1.3) translate(-326.154,-282.615)" width="20" x="1370" xlink:href="#Disconnector:bn_手车熔断器_0" y="1073"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497958454" ObjectName="114841790497958454" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346977334"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,1370,1073) scale(1.3,1.3) translate(-326.154,-282.615)" width="20" x="1370" y="1073"/></g>
</g>
<g id="GroundDisconnector_Layer">
 <g id="111003114">
  <use class="kv35kV" height="20" transform="rotate(360,1637,372) scale(-1.497,1.497) translate(-2734.52,-135.503)" width="40" x="1637" xlink:href="#GroundDisconnector:bn_接地刀闸12_0" y="372"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115123265474665840" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/35kV\GRNDSW_30117接地刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="115123394323684720"/>
  <cge:TPSR_Ref TObjectID="115123265474665840"/></metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(360,1637,372) scale(-1.497,1.497) translate(-2734.52,-135.503)" width="40" x="1637" y="372"/></g>
 <g id="111003316">
  <use class="kv10kV" height="16" transform="rotate(0,1560,1158) scale(2,2) translate(-811,-587)" width="34" x="1560" xlink:href="#GroundDisconnector:bn_接地刀闸22_0" y="1158"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115123265474665841" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\GRNDSW_0517接地刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="115123394323684721"/>
  <cge:TPSR_Ref TObjectID="115123265474665841"/></metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(0,1560,1158) scale(2,2) translate(-811,-587)" width="34" x="1560" y="1158"/></g>
 <g id="111003332">
  <use class="kv10kV" height="16" transform="rotate(0,2139,1164) scale(2,2) translate(-1100.5,-590)" width="34" x="2139" xlink:href="#GroundDisconnector:bn_接地刀闸22_0" y="1164"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115123265474665843" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\GRNDSW_0537接地刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="115123394323684723"/>
  <cge:TPSR_Ref TObjectID="115123265474665843"/></metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(0,2139,1164) scale(2,2) translate(-1100.5,-590)" width="34" x="2139" y="1164"/></g>
 <g id="111003337">
  <use class="kv10kV" height="16" transform="rotate(0,2419,1167) scale(2,2) translate(-1240.5,-591.5)" width="34" x="2419" xlink:href="#GroundDisconnector:bn_接地刀闸22_0" y="1167"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115123265474665844" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\GRNDSW_0527接地刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="115123394323684724"/>
  <cge:TPSR_Ref TObjectID="115123265474665844"/></metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(0,2419,1167) scale(2,2) translate(-1240.5,-591.5)" width="34" x="2419" y="1167"/></g>
 <g id="111003348">
  <use class="kv10kV" height="16" transform="rotate(0,1833,1163) scale(2,2) translate(-947.5,-589.5)" width="34" x="1833" xlink:href="#GroundDisconnector:bn_接地刀闸22_0" y="1163"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115123265474665842" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\GRNDSW_0557接地刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="115123394323684722"/>
  <cge:TPSR_Ref TObjectID="115123265474665842"/></metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(0,1833,1163) scale(2,2) translate(-947.5,-589.5)" width="34" x="1833" y="1163"/></g>
</g>
<g id="Transformer2_Layer">
<g id="102003015">
 <g id="1020030150">
  <use class="kv10kV" height="80" transform="rotate(0,1370,1193) scale(1.817,1.817) translate(-636.01,-548.423)" width="40" x="1370" xlink:href="#Transformer2:bn_两卷变压器004_0" y="1193"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="117375065288344537" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\XF_10kV.10kV3号占用变-高" Plane="0"/>
  </metadata>
 </g>
 <g id="1020030151">
  <use class="kv10kV" height="80" transform="rotate(0,1370,1193) scale(1.817,1.817) translate(-636.01,-548.423)" width="40" x="1370" xlink:href="#Transformer2:bn_两卷变压器004_1" y="1193"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="117375065288344538" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\XF_10kV.10kV3号占用变-低" Plane="0"/>
  </metadata>
 </g>
 <metadata>
  <cge:PSR_Ref ObjectID="117093590311633318" ObjectName="版纳_35kV_磨憨临时变\XFMR_10kV3号占用变" Plane="0"/>
 <cge:TPSR_Ref TObjectID="117093590311633318"/></metadata>
<rect fill="white" height="80" opacity="0" stroke="white" transform="rotate(0,1370,1193) scale(1.817,1.817) translate(-636.01,-548.423)" width="40" x="1370" y="1193"/></g>
<g id="102003296">
 <g id="1020032960">
  <use class="kv35kV" height="76" transform="rotate(0,1776,712) scale(2.658,2.658) translate(-1133.83,-476.129)" width="50" x="1776" xlink:href="#Transformer2:bn_两卷变压器10_0" y="712"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="117375065288344535" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/35kV\XF_35kV1号主变-高" Plane="0"/>
  </metadata>
 </g>
 <g id="1020032961">
  <use class="kv10kV" height="76" transform="rotate(0,1776,712) scale(2.658,2.658) translate(-1133.83,-476.129)" width="50" x="1776" xlink:href="#Transformer2:bn_两卷变压器10_1" y="712"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="117375065288344536" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\XF_35kV1号主变-低" Plane="0"/>
  </metadata>
 </g>
 <metadata>
  <cge:PSR_Ref ObjectID="117093590311633317" ObjectName="版纳_35kV_磨憨临时变\XFMR_35kV1号主变" Plane="0"/>
 <cge:TPSR_Ref TObjectID="117093590311633317"/></metadata>
<rect fill="white" height="76" opacity="0" stroke="white" transform="rotate(0,1776,712) scale(2.658,2.658) translate(-1133.83,-476.129)" width="50" x="1776" y="712"/></g>
</g>
<g id="DollyBreaker_Layer">
 <g id="110002430">
  <use class="kv10kV" height="22" transform="rotate(90,1640,1070) scale(1.3,1.3) translate(-415.462,-257.923)" width="74" x="1640" xlink:href="#DollyBreaker:bn_小车开关1_0_0" y="1070"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521242454" ObjectName=" 版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\CB_水库线051开关" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319934294"/>
  <cge:TPSR_Ref TObjectID="114560315521242454"/></metadata>
 <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(90,1640,1070) scale(1.3,1.3) translate(-415.462,-257.923)" width="74" x="1640" y="1070"/></g>
 <g id="110002430">
  <use class="kv10kV" height="22" transform="rotate(90,1640,1070) scale(1.3,1.3) translate(-415.462,-257.923)" width="74" x="1640" xlink:href="#DollyBreaker:bn_小车开关1_1_0" y="1070"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497958455" ObjectName=" 版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\SW_051手车工作位置" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841962296650295"/>
  <cge:TPSR_Ref TObjectID="114841790497958455"/></metadata>
 <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(90,1640,1070) scale(1.3,1.3) translate(-415.462,-257.923)" width="74" x="1640" y="1070"/></g>
 <g id="110002432">
  <use class="kv10kV" height="22" transform="rotate(90,1922,1070) scale(1.3,1.3) translate(-480.538,-257.923)" width="74" x="1922" xlink:href="#DollyBreaker:bn_小车开关1_0_0" y="1070"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521242455" ObjectName=" 版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\CB_35kV磨整线055开关" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319934295"/>
  <cge:TPSR_Ref TObjectID="114560315521242455"/></metadata>
 <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(90,1922,1070) scale(1.3,1.3) translate(-480.538,-257.923)" width="74" x="1922" y="1070"/></g>
 <g id="110002432">
  <use class="kv10kV" height="22" transform="rotate(90,1922,1070) scale(1.3,1.3) translate(-480.538,-257.923)" width="74" x="1922" xlink:href="#DollyBreaker:bn_小车开关1_1_0" y="1070"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497958456" ObjectName=" 版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\SW_055手车工作位置" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841962296650296"/>
  <cge:TPSR_Ref TObjectID="114841790497958456"/></metadata>
 <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(90,1922,1070) scale(1.3,1.3) translate(-480.538,-257.923)" width="74" x="1922" y="1070"/></g>
 <g id="110002484">
  <use class="kv10kV" height="22" transform="rotate(90,2199,1070) scale(1.3,1.3) translate(-544.462,-257.923)" width="74" x="2199" xlink:href="#DollyBreaker:bn_小车开关1_0_0" y="1070"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521242456" ObjectName=" 版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\CB_35kV磨憨线053开关" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319934296"/>
  <cge:TPSR_Ref TObjectID="114560315521242456"/></metadata>
 <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(90,2199,1070) scale(1.3,1.3) translate(-544.462,-257.923)" width="74" x="2199" y="1070"/></g>
 <g id="110002484">
  <use class="kv10kV" height="22" transform="rotate(90,2199,1070) scale(1.3,1.3) translate(-544.462,-257.923)" width="74" x="2199" xlink:href="#DollyBreaker:bn_小车开关1_1_0" y="1070"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497958457" ObjectName=" 版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\SW_053手车工作位置" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841962296650297"/>
  <cge:TPSR_Ref TObjectID="114841790497958457"/></metadata>
 <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(90,2199,1070) scale(1.3,1.3) translate(-544.462,-257.923)" width="74" x="2199" y="1070"/></g>
 <g id="110002502">
  <use class="kv10kV" height="22" transform="rotate(90,2471,1070) scale(1.3,1.3) translate(-607.231,-257.923)" width="74" x="2471" xlink:href="#DollyBreaker:bn_小车开关1_0_0" y="1070"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521242457" ObjectName=" 版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\CB_35kV磨龙线052开关" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319934297"/>
  <cge:TPSR_Ref TObjectID="114560315521242457"/></metadata>
 <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(90,2471,1070) scale(1.3,1.3) translate(-607.231,-257.923)" width="74" x="2471" y="1070"/></g>
 <g id="110002502">
  <use class="kv10kV" height="22" transform="rotate(90,2471,1070) scale(1.3,1.3) translate(-607.231,-257.923)" width="74" x="2471" xlink:href="#DollyBreaker:bn_小车开关1_1_0" y="1070"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497958458" ObjectName=" 版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\SW_052手车工作位置" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841962296650298"/>
  <cge:TPSR_Ref TObjectID="114841790497958458"/></metadata>
 <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(90,2471,1070) scale(1.3,1.3) translate(-607.231,-257.923)" width="74" x="2471" y="1070"/></g>
 <g id="110003252">
  <use class="kv10kV" height="22" transform="rotate(90,1777,909) scale(1.3,1.3) translate(-447.077,-220.769)" width="74" x="1777" xlink:href="#DollyBreaker:bn_小车开关1_0_0" y="909"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521242452" ObjectName=" 版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\CB_1号主变低压侧001断路器开关" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319934292"/>
  <cge:TPSR_Ref TObjectID="114560315521242452"/></metadata>
 <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(90,1777,909) scale(1.3,1.3) translate(-447.077,-220.769)" width="74" x="1777" y="909"/></g>
 <g id="110003252">
  <use class="kv10kV" height="22" transform="rotate(90,1777,909) scale(1.3,1.3) translate(-447.077,-220.769)" width="74" x="1777" xlink:href="#DollyBreaker:bn_小车开关1_1_0" y="909"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497958453" ObjectName=" 版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\SW_001手车工作位置" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841962296650293"/>
  <cge:TPSR_Ref TObjectID="114841790497958453"/></metadata>
 <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(90,1777,909) scale(1.3,1.3) translate(-447.077,-220.769)" width="74" x="1777" y="909"/></g>
</g>
<g id="Load_Layer">
 <g id="32003257">
 <path d="M 2199 1303 L 2199 1340" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115967690404791895" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\LD_磨憨线" Plane="0"/>
  <cge:TPSR_Ref TObjectID="115967690404791895"/></metadata>
 <path d="M 2199 1303 L 2199 1340" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32003262">
 <path d="M 1922 1301 L 1922 1342" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115967690404791894" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\LD_磨整线" Plane="0"/>
  <cge:TPSR_Ref TObjectID="115967690404791894"/></metadata>
 <path d="M 1922 1301 L 1922 1342" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32003269">
 <path d="M 1640 1307 L 1640 1346" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115967690404791893" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\LD_水库线" Plane="0"/>
  <cge:TPSR_Ref TObjectID="115967690404791893"/></metadata>
 <path d="M 1640 1307 L 1640 1346" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32003276">
 <path d="M 2471 1302 L 2471 1347" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115967690404791896" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\LD_磨龙线" Plane="0"/>
  <cge:TPSR_Ref TObjectID="115967690404791896"/></metadata>
 <path d="M 2471 1302 L 2471 1347" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="PT_Layer">
 <g id="107003378">
  <use class="kv35kV" height="70" transform="rotate(0,1973,373) scale(2,2) translate(-1008.5,-221.5)" width="100" x="1973" xlink:href="#PT:bn_电压互感器_移动变_0" y="373"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189056" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/35kV\TERM_1号主变电压互感器" Plane="0"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,1973,373) scale(2,2) translate(-1008.5,-221.5)" width="100" x="1973" y="373"/></g>
 <g id="107003383">
  <use class="kv10kV" height="60" transform="rotate(0,1036,1227) scale(2.774,2.774) translate(-691.532,-789.678)" width="60" x="1036" xlink:href="#PT:bn_移动变电压互感器111_0" y="1227"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189057" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\TERM_10kVI母电压互感器" Plane="0"/>
  </metadata>
 <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1036,1227) scale(2.774,2.774) translate(-691.532,-789.678)" width="60" x="1036" y="1227"/></g>
</g>
<g id="GZP_Layer">
 <g id="135002812">
  <use class="kv-1" height="36" transform="rotate(0,292,828) scale(0.8,0.8) translate(55,189)" width="36" x="292" xlink:href="#GZP:gg_光子牌1_0" y="828"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,292,828) scale(0.8,0.8) translate(55,189)" width="36" x="292" y="828"/></g>
</g>
<g id="Arrester_Layer">
 <g id="133002848">
  <use class="kv35kV" height="40" transform="rotate(-90,1887,279) scale(-1.3,1.3) translate(-3348.54,-69.3846)" width="20" x="1887" xlink:href="#Arrester:bn_避雷器3_0" y="279"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189064" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\TERM_35kV001避雷器2" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(-90,1887,279) scale(-1.3,1.3) translate(-3348.54,-69.3846)" width="20" x="1887" y="279"/></g>
 <g id="133003046">
  <use class="kv10kV" height="40" transform="rotate(-90,1693,1158) scale(-1.3,1.3) translate(-3005.31,-272.231)" width="20" x="1693" xlink:href="#Arrester:bn_避雷器3_0" y="1158"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189059" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\TERM_10kV磨龙线避雷器" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(-90,1693,1158) scale(-1.3,1.3) translate(-3005.31,-272.231)" width="20" x="1693" y="1158"/></g>
 <g id="133003119">
  <use class="kv35kV" height="40" transform="rotate(0,1882,160) scale(1,1) translate(-10,-5)" width="20" x="1882" xlink:href="#Arrester:bn_避雷器3_0" y="160"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189055" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/35kV\TERM_35kV301避雷器1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1882,160) scale(1,1) translate(-10,-5)" width="20" x="1882" y="160"/></g>
 <g id="133003132">
  <use class="kv10kV" height="40" transform="rotate(0,1129,1135) scale(1,1) translate(-10,-5)" width="20" x="1129" xlink:href="#Arrester:bn_避雷器3_0" y="1135"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189063" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\TERM_10kVI母避雷器" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1129,1135) scale(1,1) translate(-10,-5)" width="20" x="1129" y="1135"/></g>
 <g id="133003169">
  <use class="kv10kV" height="40" transform="rotate(-90,1996,1163) scale(-1.3,1.3) translate(-3541.38,-273.385)" width="20" x="1996" xlink:href="#Arrester:bn_避雷器3_0" y="1163"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189060" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\TERM_10kV磨整线避雷器" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(-90,1996,1163) scale(-1.3,1.3) translate(-3541.38,-273.385)" width="20" x="1996" y="1163"/></g>
 <g id="133003172">
  <use class="kv10kV" height="40" transform="rotate(-90,2267,1164) scale(-1.3,1.3) translate(-4020.85,-273.615)" width="20" x="2267" xlink:href="#Arrester:bn_避雷器3_0" y="1164"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189061" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\TERM_10kV磨憨线避雷器" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(-90,2267,1164) scale(-1.3,1.3) translate(-4020.85,-273.615)" width="20" x="2267" y="1164"/></g>
 <g id="133003176">
  <use class="kv10kV" height="40" transform="rotate(-90,2553,1167) scale(-1.3,1.3) translate(-4526.85,-274.308)" width="20" x="2553" xlink:href="#Arrester:bn_避雷器3_0" y="1167"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189062" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\TERM_10kV水库线避雷器" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(-90,2553,1167) scale(-1.3,1.3) translate(-4526.85,-274.308)" width="20" x="2553" y="1167"/></g>
</g>
<g id="Fuse_Layer">
 <g id="131003122">
  <use class="kv35kV" height="16" transform="rotate(90,1777,201) scale(-1.831,-1.831) translate(-2763.51,-318.776)" width="32" x="1777" xlink:href="#Fuse:bn_熔断器11_0" y="201"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189054" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/35kV\TERM_35kV曼纳线熔断器" Plane="0"/>
  </metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(90,1777,201) scale(-1.831,-1.831) translate(-2763.51,-318.776)" width="32" x="1777" y="201"/></g>
 <g id="131003130">
  <use class="kv10kV" height="16" transform="rotate(90,1036,1138) scale(-1.831,-1.831) translate(-1617.81,-1767.52)" width="32" x="1036" xlink:href="#Fuse:bn_熔断器11_0" y="1138"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189058" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\TERM_10kVI母熔断器" Plane="0"/>
  </metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(90,1036,1138) scale(-1.831,-1.831) translate(-1617.81,-1767.52)" width="32" x="1036" y="1138"/></g>
</g>
<g id="Status_Layer">
 <g id="126002833">
  <use class="kv-1" height="40" transform="rotate(0,70,283) scale(0.7,0.7) translate(1.90735e-06,101.286)" width="60" x="70" xlink:href="#Status:bn_工况退出颜色显示_0" y="283"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,70,283) scale(0.7,0.7) translate(1.90735e-06,101.286)" width="60" x="70" y="283"/></g>
 <g id="126002834">
  <use class="kv-1" height="40" transform="rotate(0,143,283) scale(0.7,0.7) translate(31.2857,101.286)" width="60" x="143" xlink:href="#Status:bn_不变化颜色显示_0" y="283"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,143,283) scale(0.7,0.7) translate(31.2857,101.286)" width="60" x="143" y="283"/></g>
 <g id="126002835">
  <use class="kv-1" height="40" transform="rotate(0,216,283) scale(0.7,0.7) translate(62.5714,101.286)" width="60" x="216" xlink:href="#Status:bn_越限颜色显示_0" y="283"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,216,283) scale(0.7,0.7) translate(62.5714,101.286)" width="60" x="216" y="283"/></g>
 <g id="126002836">
  <use class="kv-1" height="40" transform="rotate(0,288,283) scale(0.7,0.7) translate(93.4286,101.286)" width="60" x="288" xlink:href="#Status:bn_非实测颜色显示_0" y="283"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,288,283) scale(0.7,0.7) translate(93.4286,101.286)" width="60" x="288" y="283"/></g>
 <g id="126002837">
  <use class="kv-1" height="40" transform="rotate(0,359,283) scale(0.7,0.7) translate(123.857,101.286)" width="60" x="359" xlink:href="#Status:bn_数据封锁颜色显示_0" y="283"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,359,283) scale(0.7,0.7) translate(123.857,101.286)" width="60" x="359" y="283"/></g>
</g>
<g id="Clock_Layer">
 <g id="56002811">
  
 <metadata/></g>
</g>
<g id="Term_Layer">
 <g id="112003059">
  <use class="kv-1" height="12" transform="rotate(90,1640,1190) scale(1,1) translate(-17,-6)" width="38" x="1640" xlink:href="#Terminal:bn_终端设备10_0" y="1190"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,1640,1190) scale(1,1) translate(-17,-6)" width="38" x="1640" y="1190"/></g>
 <g id="112003166">
  <use class="kv-1" height="12" transform="rotate(90,1923,1193) scale(1,1) translate(-17,-6)" width="38" x="1923" xlink:href="#Terminal:bn_终端设备10_0" y="1193"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,1923,1193) scale(1,1) translate(-17,-6)" width="38" x="1923" y="1193"/></g>
 <g id="112003175">
  <use class="kv-1" height="12" transform="rotate(90,2469,1186) scale(1,1) translate(-17,-6)" width="38" x="2469" xlink:href="#Terminal:bn_终端设备10_0" y="1186"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,2469,1186) scale(1,1) translate(-17,-6)" width="38" x="2469" y="1186"/></g>
 <g id="112003177">
  <use class="kv-1" height="12" transform="rotate(90,2198,1198) scale(1,1) translate(-17,-6)" width="38" x="2198" xlink:href="#Terminal:bn_终端设备10_0" y="1198"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,2198,1198) scale(1,1) translate(-17,-6)" width="38" x="2198" y="1198"/></g>
</g>
<g id="Link_Layer">
 <g id="34003116">
 <path d="M 1887 279 L 1777 279" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133002848_0" Pin1InfoVect0LinkObjId="34003118_0" Pin1InfoVect1LinkObjId="34003368_1" Plane="0"/>
  </metadata>
 <path d="M 1887 279 L 1777 279" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003118">
 <path d="M 1777 279 L 1777 225" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34003116_1" Pin0InfoVect1LinkObjId="34003368_1" Pin1InfoVect0LinkObjId="131003122_1" Plane="0"/>
  </metadata>
 <path d="M 1777 279 L 1777 225" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003120">
 <path d="M 1882 160 L 1882 124 L 1777 124 L 1777 131" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133003119_0" Plane="0"/>
  </metadata>
 <path d="M 1882 160 L 1882 124 L 1777 124 L 1777 131" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003121">
 <path d="M 1777 91 L 1777 122 L 1777 177" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin1InfoVect0LinkObjId="131003122_0" Plane="0"/>
  </metadata>
 <path d="M 1777 91 L 1777 122 L 1777 177" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003133">
 <path d="M 1129 1135 L 1129 1090 L 1036 1090" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133003132_0" Plane="0"/>
  </metadata>
 <path d="M 1129 1135 L 1129 1090 L 1036 1090" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003138">
 <path d="M 1036 1229 L 1036 1162" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="107003383_0" Pin1InfoVect0LinkObjId="131003130_1" Plane="0"/>
  </metadata>
 <path d="M 1036 1229 L 1036 1162" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003208">
 <path d="M 1640 1111 L 1640 1307" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110002430_1" Pin1InfoVect0LinkObjId="32003269_0" Plane="0"/>
  </metadata>
 <path d="M 1640 1111 L 1640 1307" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003211">
 <path d="M 1560 1158 L 1693 1158" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111003316_0" Pin1InfoVect0LinkObjId="133003046_0" Plane="0"/>
  </metadata>
 <path d="M 1560 1158 L 1693 1158" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003219">
 <path d="M 2199 1111 L 2199 1303" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110002484_1" Pin1InfoVect0LinkObjId="32003257_0" Plane="0"/>
  </metadata>
 <path d="M 2199 1111 L 2199 1303" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003220">
 <path d="M 2139 1164 L 2267 1164" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111003332_0" Pin1InfoVect0LinkObjId="133003172_0" Plane="0"/>
  </metadata>
 <path d="M 2139 1164 L 2267 1164" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003221">
 <path d="M 2419 1167 L 2553 1167" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111003337_0" Pin1InfoVect0LinkObjId="133003176_0" Plane="0"/>
  </metadata>
 <path d="M 2419 1167 L 2553 1167" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003223">
 <path d="M 1640 1028 L 1640 1000" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110002430_0" Pin1InfoVect0LinkObjId="30003227_0" Plane="0"/>
  </metadata>
 <path d="M 1640 1028 L 1640 1000" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003224">
 <path d="M 1922 1028 L 1922 1000" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110002432_0" Pin1InfoVect0LinkObjId="30003227_0" Plane="0"/>
  </metadata>
 <path d="M 1922 1028 L 1922 1000" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003225">
 <path d="M 2199 1028 L 2199 1000" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110002484_0" Pin1InfoVect0LinkObjId="30003227_0" Plane="0"/>
  </metadata>
 <path d="M 2199 1028 L 2199 1000" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003226">
 <path d="M 2471 1028 L 2471 1000" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110002502_0" Pin1InfoVect0LinkObjId="30003227_0" Plane="0"/>
  </metadata>
 <path d="M 2471 1028 L 2471 1000" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003253">
 <path d="M 1777 819 L 1777 867" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="102003296_1" Pin1InfoVect0LinkObjId="110003252_0" Plane="0"/>
  </metadata>
 <path d="M 1777 819 L 1777 867" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003254">
 <path d="M 1777 950 L 1777 1000" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110003252_1" Pin1InfoVect0LinkObjId="30003227_0" Plane="0"/>
  </metadata>
 <path d="M 1777 950 L 1777 1000" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003267">
 <path d="M 1922 1111 L 1922 1301" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110002432_1" Pin1InfoVect0LinkObjId="34003268_0" Pin1InfoVect1LinkObjId="32003262_0" Plane="0"/>
  </metadata>
 <path d="M 1922 1111 L 1922 1301" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003268">
 <path d="M 1922 1301 L 1922 1317" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="32003262_0" Pin0InfoVect1LinkObjId="34003267_1" Plane="0"/>
  </metadata>
 <path d="M 1922 1301 L 1922 1317" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003275">
 <path d="M 2471 1111 L 2471 1302" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110002502_1" Pin1InfoVect0LinkObjId="32003276_0" Plane="0"/>
  </metadata>
 <path d="M 2471 1111 L 2471 1302" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003281">
 <path d="M 1777 538 L 1777 454" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100003357_0" Pin1InfoVect0LinkObjId="101003311_1" Plane="0"/>
  </metadata>
 <path d="M 1777 538 L 1777 454" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003353">
 <path d="M 1833 1163 L 1996 1163" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111003348_0" Pin1InfoVect0LinkObjId="133003169_0" Plane="0"/>
  </metadata>
 <path d="M 1833 1163 L 1996 1163" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003360">
 <path d="M 1777 583 L 1777 636" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100003357_1" Pin1InfoVect0LinkObjId="102003296_0" Plane="0"/>
  </metadata>
 <path d="M 1777 583 L 1777 636" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003366">
 <path d="M 1638 373 L 1777 373" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111003114_0" Pin1InfoVect0LinkObjId="34003382_1" Pin1InfoVect1LinkObjId="34003367_1" Pin1InfoVect2LinkObjId="34003368_0" Plane="0"/>
  </metadata>
 <path d="M 1638 373 L 1777 373" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003367">
 <path d="M 1777 413 L 1777 373" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101003311_0" Pin1InfoVect0LinkObjId="34003382_1" Pin1InfoVect1LinkObjId="34003366_1" Pin1InfoVect2LinkObjId="34003368_0" Plane="0"/>
  </metadata>
 <path d="M 1777 413 L 1777 373" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003368">
 <path d="M 1777 373 L 1777 279" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34003382_1" Pin0InfoVect1LinkObjId="34003367_1" Pin0InfoVect2LinkObjId="34003366_1" Pin1InfoVect0LinkObjId="34003116_1" Pin1InfoVect1LinkObjId="34003118_0" Plane="0"/>
  </metadata>
 <path d="M 1777 373 L 1777 279" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003382">
 <path d="M 1972 373 L 1777 373" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="107003378_0" Pin1InfoVect0LinkObjId="34003366_1" Pin1InfoVect1LinkObjId="34003367_1" Pin1InfoVect2LinkObjId="34003368_0" Plane="0"/>
  </metadata>
 <path d="M 1972 373 L 1777 373" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003387">
 <path d="M 1372 1302 L 1372 1382" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 1372 1302 L 1372 1382" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003406">
 <path d="M 1369 1032 L 1369 1000" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101003400_1" Pin1InfoVect0LinkObjId="30003227_0" Plane="0"/>
  </metadata>
 <path d="M 1369 1032 L 1369 1000" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003407">
 <path d="M 1370 1177 L 1370 1114" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="102003015_0" Pin1InfoVect0LinkObjId="101003400_0" Plane="0"/>
  </metadata>
 <path d="M 1370 1177 L 1370 1114" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003438">
 <path d="M 1036 1114 L 1036 1000" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="131003130_0" Pin1InfoVect0LinkObjId="30003227_0" Plane="0"/>
  </metadata>
 <path d="M 1036 1114 L 1036 1000" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003439">
 <path d="M 1199 709 L 1199 709" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 1199 709 L 1199 709" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="MeasurementValue_Layer">
 <g id="33002806">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="258" xml:space="preserve" y="765">-0.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002805">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="258" xml:space="preserve" y="679">-0.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33003228">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2570" xml:space="preserve" y="948">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115405169948099360" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\BS_10kVI母:V_C" Plane="0"/>
  </metadata>
 </g>
 <g id="33003230">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2570" xml:space="preserve" y="873">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115404869300388640" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\BS_10kVI母:V" Plane="0"/>
  </metadata>
 </g>
 <g id="33003231">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2570" xml:space="preserve" y="899">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115405084048753440" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\BS_10kVI母:V_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33003232">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2570" xml:space="preserve" y="924">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115405126998426400" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\BS_10kVI母:V_B" Plane="0"/>
  </metadata>
 </g>
 <g id="33003233">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2570" xml:space="preserve" y="973">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795529158" ObjectName="122723089845856198:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33003258">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2154" xml:space="preserve" y="1375">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967862203483735" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\LD_磨憨线:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33003259">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2155" xml:space="preserve" y="1403">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967905153156695" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\LD_磨憨线:Q" Plane="0"/>
  </metadata>
 </g>
 <g id="33003261">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2156" xml:space="preserve" y="1435">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052502615" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\LD_磨憨线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33003263">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="1877" xml:space="preserve" y="1378">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967862203483734" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\LD_磨整线:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33003264">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="1877" xml:space="preserve" y="1407">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967905153156694" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\LD_磨整线:Q" Plane="0"/>
  </metadata>
 </g>
 <g id="33003266">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="1878" xml:space="preserve" y="1437">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052502614" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\LD_磨整线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33003270">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="1595" xml:space="preserve" y="1378">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967862203483733" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\LD_水库线:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33003271">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="1597" xml:space="preserve" y="1407">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967905153156693" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\LD_水库线:Q" Plane="0"/>
  </metadata>
 </g>
 <g id="33003273">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="1597" xml:space="preserve" y="1441">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052502613" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\LD_水库线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33003277">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2431" xml:space="preserve" y="1381">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967862203483736" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\LD_磨龙线:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33003278">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2430" xml:space="preserve" y="1411">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967905153156696" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\LD_磨龙线:Q" Plane="0"/>
  </metadata>
 </g>
 <g id="33003280">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2433" xml:space="preserve" y="1440">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052502616" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\LD_磨龙线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33003298">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="1633" xml:space="preserve" y="615">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709335" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/35kV\XF_35kV1号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33003299">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="1633" xml:space="preserve" y="642">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375322986382295" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/35kV\XF_35kV1号主变-高:Q" Plane="0"/>
  </metadata>
 </g>
 <g id="33003300">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="1634" xml:space="preserve" y="667">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375451835401175" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/35kV\XF_35kV1号主变-高:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33003301">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="1634" xml:space="preserve" y="804">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709336" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\XF_35kV1号主变-低:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33003302">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="1634" xml:space="preserve" y="829">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375322986382296" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\XF_35kV1号主变-低:Q" Plane="0"/>
  </metadata>
 </g>
 <g id="33003303">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="1634" xml:space="preserve" y="856">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375451835401176" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/10kV\XF_35kV1号主变-低:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33003449">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="30" font-width="30" stroke="rgb(255,255,0)" writing-mode="lr" x="274" xml:space="preserve" y="1004">-0.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="117093852304638373" ObjectName="版纳_35kV_磨憨临时变\XFMR_35kV1号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33003450">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="30" font-width="30" stroke="rgb(255,255,0)" writing-mode="lr" x="278" xml:space="preserve" y="926">-0.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="117375408885728215" ObjectName="版纳_35kV_磨憨临时变\版纳_35kV_磨憨临时变/35kV\XF_35kV1号主变-高:TAP" Plane="0"/>
  </metadata>
 </g>
</g>
<g id="Text_Layer">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="39" stroke="rgb(255,255,254)" writing-mode="lr" x="2493" xml:space="preserve" y="1090">054</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="40" font-size="40" font-width="40" stroke="rgb(255,255,254)" writing-mode="lr" x="2227" xml:space="preserve" y="1093">053</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="40" font-size="40" font-width="40" stroke="rgb(255,255,254)" writing-mode="lr" x="1945" xml:space="preserve" y="1095">052</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="39" stroke="rgb(255,255,254)" writing-mode="lr" x="1675" xml:space="preserve" y="1094">051</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(35,169,128)" writing-mode="lr" x="2511" xml:space="preserve" y="867">Uab</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(35,169,128)" writing-mode="lr" x="2516" xml:space="preserve" y="895">Ua</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(35,169,128)" writing-mode="lr" x="2516" xml:space="preserve" y="922">Ub</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="76" font-size="40" font-width="40" stroke="rgb(0,0,0)" writing-mode="lr" x="25" xml:space="preserve" y="118">35kV象明变</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="125" xml:space="preserve" y="846">事故总:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="66" font-size="32" font-width="32" stroke="rgb(0,0,0)" writing-mode="lr" x="152" xml:space="preserve" y="118">35kV纳卓临时变</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="66" xml:space="preserve" y="682">总有功</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="168" xml:space="preserve" y="928">档位</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="30" font-width="30" stroke="rgb(255,255,254)" writing-mode="tb" x="80" xml:space="preserve" y="873">一</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="168" xml:space="preserve" y="1007">温度</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="57" xml:space="preserve" y="1087">控制母线电压</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="57" xml:space="preserve" y="1172">合闸母线电压</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="30" font-size="28" font-width="28" stroke="rgb(170,0,0)" writing-mode="lr" x="203" xml:space="preserve" y="439">MW</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="30" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="133" xml:space="preserve" y="439">有功:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="30" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="255" xml:space="preserve" y="439">无功:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="30" font-size="28" font-width="28" stroke="rgb(170,0,0)" writing-mode="lr" x="331" xml:space="preserve" y="439">MVr</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="30" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="133" xml:space="preserve" y="521">电流:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="30" font-size="28" font-width="28" stroke="rgb(170,0,0)" writing-mode="lr" x="203" xml:space="preserve" y="521">A</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="30" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="255" xml:space="preserve" y="521">电压:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="30" font-size="28" font-width="28" stroke="rgb(170,0,0)" writing-mode="lr" x="331" xml:space="preserve" y="521">kV</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="30" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="133" xml:space="preserve" y="602">频率:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="30" font-size="28" font-width="28" stroke="rgb(170,0,0)" writing-mode="lr" x="203" xml:space="preserve" y="602">Hz</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="30" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="255" xml:space="preserve" y="602">温度:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="30" font-size="28" font-width="28" stroke="rgb(170,0,0)" writing-mode="lr" x="331" xml:space="preserve" y="602">℃</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="66" xml:space="preserve" y="765">总无功</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="tb" x="81" xml:space="preserve" y="440">单</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="100" xml:space="preserve" y="1428">联系方式：</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="256" xml:space="preserve" y="1426">66751</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="tb" x="80" xml:space="preserve" y="541">位</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="30" font-width="30" stroke="rgb(255,255,254)" writing-mode="tb" x="80" xml:space="preserve" y="903">号</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="30" font-width="30" stroke="rgb(255,255,254)" writing-mode="tb" x="80" xml:space="preserve" y="941">主</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="30" font-width="30" stroke="rgb(255,255,254)" writing-mode="tb" x="80" xml:space="preserve" y="978">变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="24" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="46" xml:space="preserve" y="343">工况</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="24" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="46" xml:space="preserve" y="367">退出</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="24" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="131" xml:space="preserve" y="343">不</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="24" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="119" xml:space="preserve" y="371">变化</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="24" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="191" xml:space="preserve" y="355">越限</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="24" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="276" xml:space="preserve" y="343">非</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="24" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="267" xml:space="preserve" y="371">实测</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="24" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="335" xml:space="preserve" y="343">数据</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="24" font-size="24" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="335" xml:space="preserve" y="367">封锁</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="50" font-size="40" font-width="40" stroke="rgb(0,0,0)" writing-mode="lr" x="55" xml:space="preserve" y="1263">保护软压板遥控表</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="52" font-size="36" font-width="36" stroke="rgb(0,0,0)" writing-mode="lr" x="134" xml:space="preserve" y="1296">信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="39" stroke="rgb(255,255,254)" writing-mode="lr" x="1528" xml:space="preserve" y="434">30117</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="50" font-size="42" font-width="42" stroke="rgb(255,255,254)" writing-mode="lr" x="855" xml:space="preserve" y="989">10kVI母</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="39" stroke="rgb(255,255,254)" writing-mode="lr" x="1810" xml:space="preserve" y="928">001</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="38" font-size="38" font-width="39" stroke="rgb(255,255,254)" writing-mode="lr" x="2145" xml:space="preserve" y="1487">勐伴线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="38" font-size="38" font-width="39" stroke="rgb(255,255,254)" writing-mode="lr" x="1864" xml:space="preserve" y="1487">瑶区线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="38" font-size="38" font-width="39" stroke="rgb(255,255,254)" writing-mode="lr" x="1570" xml:space="preserve" y="1487">备用线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="38" font-size="38" font-width="39" stroke="rgb(255,255,254)" writing-mode="lr" x="2396" xml:space="preserve" y="1487">望天树线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="40" font-size="40" font-width="40" stroke="rgb(255,255,254)" writing-mode="lr" x="1804" xml:space="preserve" y="460">3011</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="39" stroke="rgb(255,255,254)" writing-mode="lr" x="1491" xml:space="preserve" y="1218">0517</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="39" stroke="rgb(255,255,254)" writing-mode="lr" x="2076" xml:space="preserve" y="1219">0537</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="39" stroke="rgb(255,255,254)" writing-mode="lr" x="2351" xml:space="preserve" y="1216">0547</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="39" stroke="rgb(255,255,254)" writing-mode="lr" x="1765" xml:space="preserve" y="1218">0527</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="39" stroke="rgb(255,255,254)" writing-mode="lr" x="1822" xml:space="preserve" y="588">301</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="38" font-size="38" font-width="39" stroke="rgb(255,255,254)" writing-mode="lr" x="1283" xml:space="preserve" y="1484">1号站用变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="40" stroke="rgb(255,255,254)" writing-mode="lr" x="1888" xml:space="preserve" y="680"> 1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="40" stroke="rgb(255,255,254)" writing-mode="lr" x="1888" xml:space="preserve" y="719">号</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="40" stroke="rgb(255,255,254)" writing-mode="lr" x="1888" xml:space="preserve" y="758">主</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="40" stroke="rgb(255,255,254)" writing-mode="lr" x="1888" xml:space="preserve" y="797">变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(35,169,128)" writing-mode="lr" x="2516" xml:space="preserve" y="948">Uc</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(35,169,128)" writing-mode="lr" x="2510" xml:space="preserve" y="973">3U0</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="40" stroke="rgb(255,255,254)" writing-mode="lr" x="1704" xml:space="preserve" y="71">曼纳线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="38" font-size="38" font-width="39" stroke="rgb(255,255,254)" writing-mode="lr" x="833" xml:space="preserve" y="1484">10kV I母电压互感器</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="39" stroke="rgb(255,255,254)" writing-mode="lr" x="1388" xml:space="preserve" y="1096">050</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(35,169,128)" writing-mode="lr" x="2397" xml:space="preserve" y="1409">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(35,169,128)" writing-mode="lr" x="2399" xml:space="preserve" y="1437">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(35,169,128)" writing-mode="lr" x="2400" xml:space="preserve" y="1380">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="1605" xml:space="preserve" y="666">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(35,169,128)" writing-mode="lr" x="1602" xml:space="preserve" y="638">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(35,169,128)" writing-mode="lr" x="1605" xml:space="preserve" y="612">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(35,169,128)" writing-mode="lr" x="1602" xml:space="preserve" y="856">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(35,169,128)" writing-mode="lr" x="1602" xml:space="preserve" y="827">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(35,169,128)" writing-mode="lr" x="1605" xml:space="preserve" y="802">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(35,169,128)" writing-mode="lr" x="1839" xml:space="preserve" y="1404">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(35,169,128)" writing-mode="lr" x="1841" xml:space="preserve" y="1438">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(35,169,128)" writing-mode="lr" x="1841" xml:space="preserve" y="1374">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(35,169,128)" writing-mode="lr" x="2124" xml:space="preserve" y="1375">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(35,169,128)" writing-mode="lr" x="2123" xml:space="preserve" y="1432">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(35,169,128)" writing-mode="lr" x="2121" xml:space="preserve" y="1404">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(35,169,128)" writing-mode="lr" x="1562" xml:space="preserve" y="1375">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(35,169,128)" writing-mode="lr" x="1561" xml:space="preserve" y="1405">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(35,169,128)" writing-mode="lr" x="1563" xml:space="preserve" y="1439">Ia</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href="综自改造勐仑变.other.svg"><rect fill-opacity="0" height="100" stroke-opacity="0" stroke-width="2" width="340" x="44" y="40"/></g>
 <g ChangePicPlane="0," Plane="0" href="纳卓临时变.other.svg"><rect fill-opacity="0" height="70" stroke-opacity="0" stroke-width="1" width="335" x="44" y="1242"/></g>
</g>
</svg>