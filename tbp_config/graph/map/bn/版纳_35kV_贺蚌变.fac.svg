<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="113997365567815943" height="978" id="thSvg" viewBox="0 0 1700 978" width="1700">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="Terminal:bn_35kV电缆_0" viewBox="0,0,38,12">
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,0)" stroke-width="1" x1="11" x2="25" y1="6" y2="6"/>
 <path AFMask="2147483647" Plane="0" d="M 8 2 L 11 5 L 8 8 " fill="none" stroke="rgb(255,255,0)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,0)" stroke-width="1" x1="7" x2="7" y1="3" y2="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,0)" stroke-width="1" x1="29" x2="29" y1="2" y2="8"/>
 <path AFMask="2147483647" Plane="0" d="M 28 8 L 25 5 L 28 2 " fill="none" stroke="rgb(255,255,0)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,0)" stroke-width="1" x1="2" x2="7" y1="6" y2="6"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,0)" stroke-width="1" x1="29" x2="34" y1="6" y2="6"/>
</symbol>
<symbol id="Status:bn_不变化颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(0,0,255)" height="1" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_不变化颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(85,0,255)" height="1" stroke="rgb(85,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Disconnector:bn_刀闸1_0" viewBox="0,0,40,20">
 <use Plane="0" x="32" xlink:href="#terminal" y="10"/>
 <use Plane="0" x="2" xlink:href="#terminal" y="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="29" y1="1" y2="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="29" y1="11" y2="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="4" y1="11" y2="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="18" y2="4"/>
</symbol>
<symbol id="Disconnector:bn_刀闸1_1" viewBox="0,0,40,20">
 <use Plane="0" x="32" xlink:href="#terminal" y="10"/>
 <use Plane="0" x="2" xlink:href="#terminal" y="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="33" y1="11" y2="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="18" y2="4"/>
</symbol>
<symbol id="Disconnector:bn_刀闸5_0" viewBox="0,0,20,40">
 <use Plane="0" x="7" xlink:href="#terminal" y="4"/>
 <use Plane="0" x="7" xlink:href="#terminal" y="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="35" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="9" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="1" x2="15" y1="10" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="18" x2="8" y1="10" y2="30"/>
</symbol>
<symbol id="Disconnector:bn_刀闸5_1" viewBox="0,0,20,40">
 <use Plane="0" x="7" xlink:href="#terminal" y="4"/>
 <use Plane="0" x="7" xlink:href="#terminal" y="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="35" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="1" x2="15" y1="10" y2="10"/>
</symbol>
<symbol id="Breaker:bn_断路器1_0" viewBox="0,0,20,40">
 <use Plane="0" x="9" xlink:href="#terminal" y="4"/>
 <use Plane="0" x="9" xlink:href="#terminal" y="34"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="30" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,10,20)" width="14" x="3" y="5"/>
</symbol>
<symbol id="Breaker:bn_断路器1_1" viewBox="0,0,20,40">
 <use Plane="0" x="9" xlink:href="#terminal" y="4"/>
 <use Plane="0" x="9" xlink:href="#terminal" y="34"/>
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,254)" height="30" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,10,20)" width="14" x="3" y="5"/>
</symbol>
<symbol id="Breaker:bn_断路器2_0" viewBox="0,0,20,40">
 <use Plane="0" x="10" xlink:href="#terminal" y="4"/>
 <use Plane="0" x="10" xlink:href="#terminal" y="36"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="30" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,10,20)" width="14" x="3" y="5"/>
</symbol>
<symbol id="Breaker:bn_断路器2_1" viewBox="0,0,20,40">
 <use Plane="0" x="10" xlink:href="#terminal" y="4"/>
 <use Plane="0" x="10" xlink:href="#terminal" y="36"/>
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,254)" height="30" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,10,20)" width="14" x="3" y="5"/>
</symbol>
<symbol id="Status:bn_非实测颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,85,0)" height="1" stroke="rgb(170,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_非实测颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,85,0)" height="1" stroke="rgb(170,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_工况退出颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,170,255)" height="1" stroke="rgb(170,170,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_工况退出颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,170,255)" height="1" stroke="rgb(170,170,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸1_0" viewBox="0,0,16,32">
 <use Plane="0" x="8" xlink:href="#terminal" y="2"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="4" y2="6"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="13" y1="27" y2="27"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="4" x2="12" y1="29" y2="29"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="6" x2="10" y1="31" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="27" y2="23"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="15" y1="22" y2="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="13" y1="6" y2="6"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸1_1" viewBox="0,0,16,32">
 <use Plane="0" x="8" xlink:href="#terminal" y="2"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="27" y2="4"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="6" x2="10" y1="31" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="4" x2="12" y1="29" y2="29"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="13" y1="27" y2="27"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="13" y1="6" y2="6"/>
</symbol>
<symbol id="Terminal:bn_景仰变_0" viewBox="0,0,120,70">
 <use Plane="0" x="69" xlink:href="#terminal" y="5"/>
 <circle AFMask="2147483647" Plane="0" cx="68" cy="39" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="60" x2="77" y1="31" y2="46"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="77" x2="60" y1="32" y2="47"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="69" x2="69" y1="28" y2="23"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="58" x2="80" y1="23" y2="23"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="58" x2="80" y1="18" y2="18"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="69" x2="69" y1="18" y2="7"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="69" x2="69" y1="51" y2="53"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="63" x2="75" y1="60" y2="60"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="65" x2="73" y1="62" y2="62"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="67" x2="71" y1="64" y2="64"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="69" x2="69" y1="53" y2="60"/>
</symbol>
<symbol id="Transformer2:bn_两卷变压器1_0" viewBox="0,0,40,80">
 <use Plane="0" x="20" xlink:href="#terminal" y="3"/>
 <circle AFMask="2147483647" Plane="0" cx="19" cy="21" fill="none" r="18" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="20" x2="27" y1="53" y2="45"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="20" x2="12" y1="53" y2="46"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="20" x2="20" y1="53" y2="63"/>
</symbol>
<symbol id="Transformer2:bn_两卷变压器1_1" viewBox="0,0,40,80">
 <use Plane="1" x="20" xlink:href="#terminal" y="72"/>
 <circle AFMask="2147483647" Plane="1" cx="19" cy="53" fill="none" r="18" stroke="rgb(0,0,255)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 19 27 L 28 14 L 9 14 Z" fill="none" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Fuse:bn_熔断器7_0" viewBox="0,0,32,16">
 <use Plane="0" x="5" xlink:href="#terminal" y="12"/>
 <use Plane="0" x="28" xlink:href="#terminal" y="12"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="5" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(23,16,7)" width="18" x="7" y="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="5" x2="27" y1="3" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="7" x2="7" y1="9" y2="15"/>
</symbol>
<symbol id="Status:bn_数据封锁颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,85,0)" height="1" stroke="rgb(255,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_数据封锁颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,85,0)" height="1" stroke="rgb(255,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_越限颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_越限颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="GZP:gg_光子牌1_0" viewBox="0,0,36,36">
 <circle AFMask="2147483647" Plane="0" cx="18" cy="18" fill="rgb(0,255,0)" r="15" stroke="rgb(0,255,0)" stroke-width="1"/>
</symbol>
<symbol id="GZP:gg_光子牌1_1" viewBox="0,0,36,36">
 <circle AFMask="2147483647" Plane="0" cx="18" cy="18" fill="rgb(255,0,0)" r="15" stroke="rgb(255,0,0)" stroke-width="1"/>
</symbol>
<symbol id="PT:gpb_0" viewBox="0,0,70,70">
 <use Plane="0" x="33" xlink:href="#terminal" y="4"/>
 <circle AFMask="2147483647" Plane="0" cx="37" cy="50" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="37" cy="40" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="27" cy="40" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="28" x2="28" y1="47" y2="51"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="25" x2="28" y1="54" y2="51"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="28" x2="31" y1="51" y2="54"/>
 <circle AFMask="2147483647" Plane="0" cx="27" cy="50" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="28" x2="31" y1="40" y2="43"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="28" x2="28" y1="36" y2="40"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="25" x2="28" y1="43" y2="40"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="35" x2="38" y1="43" y2="40"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="38" x2="38" y1="36" y2="40"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="38" x2="41" y1="40" y2="43"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="38" x2="38" y1="46" y2="55"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="41" x2="38" y1="51" y2="54"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="41" x2="38" y1="49" y2="46"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="43" x2="52" y1="40" y2="40"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="51" x2="51" y1="56" y2="61"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="46" x2="56" y1="62" y2="62"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="48" x2="54" y1="64" y2="64"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="50" x2="52" y1="66" y2="66"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="11" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,51,50)" width="4" x="49" y="45"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="46" x2="55" y1="48" y2="54"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="12" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,33,22)" width="6" x="30" y="16"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="0" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,30,26)" width="0" x="30" y="26"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="33" x2="33" y1="37" y2="6"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="8" x2="63" y1="10" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="8" x2="8" y1="11" y2="19"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="4" x2="13" y1="20" y2="20"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="4" x2="13" y1="22" y2="22"/>
 <circle AFMask="2147483647" Plane="0" cx="8.5" cy="28.5" fill="none" r="4.5" stroke="rgb(255,255,254)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="6" x2="11" y1="26" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="6" x2="11" y1="31" y2="26"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="9" y1="34" y2="36"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="5" x2="12" y1="37" y2="37"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="6" x2="11" y1="39" y2="39"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="7" x2="10" y1="41" y2="41"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="63" x2="63" y1="11" y2="19"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="14" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,63,27)" width="8" x="59" y="20"/>
 <polygon AFMask="2147483647" Plane="0" fill="none" points="62,33 63,25 65,33" stroke="rgb(255,255,254)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="61" x2="64" y1="42" y2="42"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="60" x2="65" y1="40" y2="40"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="59" x2="66" y1="38" y2="38"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="63" x2="63" y1="35" y2="37"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="52" x2="52" y1="41" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="63" x2="63" y1="28" y2="32"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="64" x2="64" y1="30" y2="32"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="62" x2="62" y1="30" y2="32"/>
</symbol>
<symbol id="Arrester:qj_ocs_blq2_0" viewBox="0,0,40,20">
 <use Plane="0" x="2" xlink:href="#terminal" y="9"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="10" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,18,10)" width="23" x="7" y="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="10" y1="10" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="36" x2="36" y1="7" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="34" x2="34" y1="15" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="38" x2="38" y1="9" y2="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="30" x2="33" y1="10" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="4" x2="10" y1="10" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="17" x2="17" y1="7" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="17" x2="17" y1="11" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="17" x2="21" y1="7" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="21" x2="17" y1="10" y2="13"/>
</symbol>
<symbol id="Transformer2:立新变站用变_0" viewBox="0,0,50,60">
 <use Plane="0" x="22" xlink:href="#terminal" y="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="22" x2="22" y1="5" y2="11"/>
 <circle AFMask="2147483647" Plane="0" cx="22" cy="17" fill="none" r="6" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="22" x2="22" y1="13" y2="16"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="22" x2="19" y1="16" y2="19"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="22" x2="25" y1="16" y2="19"/>
 <circle AFMask="2147483647" Plane="0" cx="22" cy="26" fill="none" r="6" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="22" x2="22" y1="22" y2="25"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="22" x2="19" y1="25" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="22" x2="25" y1="25" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="22" x2="22" y1="32" y2="48"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="22" x2="40" y1="25" y2="25"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="41" x2="41" y1="21" y2="29"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="43" x2="43" y1="24" y2="27"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="45" x2="45" y1="25" y2="26"/>
 <circle AFMask="2147483647" Plane="0" cx="32.5" cy="24.5" fill="none" r="2.5" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="37" x2="37" y1="25" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="37" x2="22" y1="28" y2="43"/>
 <polygon AFMask="2147483647" Plane="0" fill="none" points="22,44 22,44 22,44" stroke="rgb(0,0,255)" stroke-width="1"/>
 <polygon AFMask="2147483647" Plane="0" fill="none" points="20,41 21,44 23,41" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="PT:立新贺蚌_0" viewBox="0,0,120,70">
 <use Plane="0" x="48" xlink:href="#terminal" y="2"/>
 <use Plane="0" x="48" xlink:href="#terminal" y="65"/>
 <circle AFMask="2147483647" Plane="0" cx="14" cy="39" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="6" x2="23" y1="31" y2="46"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="23" x2="6" y1="32" y2="47"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="15" x2="15" y1="28" y2="23"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="4" x2="26" y1="23" y2="23"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="4" x2="26" y1="18" y2="18"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="15" x2="15" y1="18" y2="7"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="15" x2="15" y1="51" y2="53"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="21" y1="60" y2="60"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="11" x2="19" y1="62" y2="62"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="13" x2="17" y1="64" y2="64"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="15" x2="15" y1="53" y2="60"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="14" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,77,48)" width="8" x="73" y="41"/>
 <polygon AFMask="2147483647" Plane="0" fill="none" points="75,54 76,46 78,54" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="75" x2="78" y1="63" y2="63"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="74" x2="79" y1="61" y2="61"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="73" x2="80" y1="59" y2="59"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="77" x2="77" y1="56" y2="58"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="77" x2="77" y1="49" y2="53"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="78" x2="78" y1="51" y2="53"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="76" x2="76" y1="51" y2="53"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="98" x2="104" y1="44" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="98" x2="104" y1="55" y2="55"/>
 <circle AFMask="2147483647" Plane="0" cx="100" cy="44" fill="none" r="8" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="100" cy="55" fill="none" r="8" stroke="rgb(0,0,255)" stroke-width="1"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="16" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,99,21)" width="9" x="95" y="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="100" x2="100" y1="35" y2="7"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="16" x2="99" y1="7" y2="7"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="77" x2="77" y1="40" y2="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="48" x2="48" y1="3" y2="62"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="版纳_35kV_贺蚌变" InitShowingPlane="0," fill="rgb(0,0,0)" height="978" width="1700" x="0" y="0"/>
</g>
<g id="Other_Layer">
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="21" x2="338" y1="29" y2="29"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="337" x2="337" y1="30" y2="874"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="20" x2="336" y1="126" y2="126"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="19" x2="19" y1="32" y2="871"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="83" x2="83" y1="269" y2="390"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="145" x2="145" y1="390" y2="482"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="103" x2="103" y1="549" y2="655"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="20" x2="338" y1="267" y2="267"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="84" x2="338" y1="304" y2="304"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="83" x2="336" y1="348" y2="348"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="19" x2="338" y1="391" y2="391"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="19" x2="335" y1="438" y2="438"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="20" x2="340" y1="485" y2="485"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="20" x2="336" y1="545" y2="545"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="103" x2="338" y1="607" y2="607"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="19" x2="335" y1="659" y2="659"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="19" x2="338" y1="727" y2="727"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="19" x2="336" y1="781" y2="781"/>
 
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="21" x2="338" y1="179" y2="179"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="246" x2="246" y1="180" y2="267"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="186" x2="186" y1="180" y2="264"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="127" x2="127" y1="180" y2="267"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="73" x2="73" y1="180" y2="266"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="21" x2="336" y1="219" y2="219"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="18" x2="338" y1="875" y2="875"/>
</g>
<g id="Bus_Layer">
 <g id="30002986">
  <path d="M 817 483 L 1532 483" stroke="rgb(128,128,128)" stroke-width="6"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115404740451369833" ObjectName="版纳_35kV_贺蚌变\版纳_35kV_贺蚌变/35kV\BS_35kVⅠ段母线" Plane="0"/>
  <cge:TPSR_Ref TObjectID="115404740451369833"/></metadata>
 <path d="M 817 483 L 1532 483" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="Breaker_Layer">
 <g id="100003012">
  <use class="kv35kV" height="40" transform="rotate(0,1157,531) scale(1,1) translate(-10,-20)" width="20" x="1157" xlink:href="#Breaker:bn_断路器2_0" y="531"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521243006" ObjectName="版纳_35kV_贺蚌变\版纳_35kV_贺蚌变/35kV\CB_＃1主变高侧362开关" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319934846"/>
  <cge:TPSR_Ref TObjectID="114560315521243006"/></metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1157,531) scale(1,1) translate(-10,-20)" width="20" x="1157" y="531"/></g>
 <g id="100003013">
  <use class="kv35kV" height="40" transform="rotate(0,1274,435) scale(1,1) translate(-10,-20)" width="20" x="1274" xlink:href="#Breaker:bn_断路器1_0" y="435"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521243005" ObjectName="版纳_35kV_贺蚌变\版纳_35kV_贺蚌变/35kV\CB_35kV佛满II回线361开关" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319934845"/>
  <cge:TPSR_Ref TObjectID="114560315521243005"/></metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1274,435) scale(1,1) translate(-10,-20)" width="20" x="1274" y="435"/></g>
 <g id="100003014">
  <use class="kv10kV" height="40" transform="rotate(0,1157,789) scale(1,1) translate(-10,-20)" width="20" x="1157" xlink:href="#Breaker:bn_断路器2_0" y="789"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521243007" ObjectName="版纳_35kV_贺蚌变\版纳_35kV_贺蚌变/10kV\CB_＃1主变低侧011开关" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319934847"/>
  <cge:TPSR_Ref TObjectID="114560315521243007"/></metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1157,789) scale(1,1) translate(-10,-20)" width="20" x="1157" y="789"/></g>
</g>
<g id="Disconnector_Layer">
 <g id="101003015">
  <use class="kv35kV" height="40" transform="rotate(0,1156,585) scale(-1.287,-1.287) translate(-2062.21,-1059.55)" width="20" x="1156" xlink:href="#Disconnector:bn_刀闸5_0" y="585"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959338" ObjectName="版纳_35kV_贺蚌变\版纳_35kV_贺蚌变/35kV\SW_＃1主变高侧3626刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346978218"/>
  <cge:TPSR_Ref TObjectID="114841790497959338"/></metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1156,585) scale(-1.287,-1.287) translate(-2062.21,-1059.55)" width="20" x="1156" y="585"/></g>
 <g id="101003016">
  <use class="kv35kV" height="20" transform="rotate(90,1274,380) scale(1.3,1.3) translate(-312,-98.6923)" width="40" x="1274" xlink:href="#Disconnector:bn_刀闸1_0" y="380"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959337" ObjectName="版纳_35kV_贺蚌变\版纳_35kV_贺蚌变/35kV\SW_35kV佛满II回线3616刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346978217"/>
  <cge:TPSR_Ref TObjectID="114841790497959337"/></metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1274,380) scale(1.3,1.3) translate(-312,-98.6923)" width="40" x="1274" y="380"/></g>
 <g id="101003017">
  <use class="kv10kV" height="40" transform="rotate(360,1156,853) scale(-1.287,1.287) translate(-2062.21,-210.218)" width="20" x="1156" xlink:href="#Disconnector:bn_刀闸5_0" y="853"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959339" ObjectName="版纳_35kV_贺蚌变\版纳_35kV_贺蚌变/10kV\SW_＃1主变低侧0111刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346978219"/>
  <cge:TPSR_Ref TObjectID="114841790497959339"/></metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(360,1156,853) scale(-1.287,1.287) translate(-2062.21,-210.218)" width="20" x="1156" y="853"/></g>
</g>
<g id="GroundDisconnector_Layer">
 <g id="111003020">
  <use class="kv35kV" height="32" transform="rotate(90,1101,622) scale(-1.5,1.5) translate(-1843,-209.333)" width="16" x="1101" xlink:href="#GroundDisconnector:bn_接地刀闸1_0" y="622"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115123265474666476" ObjectName="版纳_35kV_贺蚌变\版纳_35kV_贺蚌变/35kV\GRNDSW_＃1主变高侧36267接地刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="115123394323685356"/>
  <cge:TPSR_Ref TObjectID="115123265474666476"/></metadata>
 <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(90,1101,622) scale(-1.5,1.5) translate(-1843,-209.333)" width="16" x="1101" y="622"/></g>
</g>
<g id="Transformer2_Layer">
<g id="102003018">
 <g id="1020030180">
  <use class="kv35kV" height="80" transform="rotate(0,1157,713) scale(1.126,1.126) translate(-149.469,-117.785)" width="40" x="1157" xlink:href="#Transformer2:bn_两卷变压器1_0" y="713"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="117375065288344710" ObjectName="版纳_35kV_贺蚌变\版纳_35kV_贺蚌变/35kV\XF_＃1主变-高" Plane="0"/>
  </metadata>
 </g>
 <g id="1020030181">
  <use class="kv10kV" height="80" transform="rotate(0,1157,713) scale(1.126,1.126) translate(-149.469,-117.785)" width="40" x="1157" xlink:href="#Transformer2:bn_两卷变压器1_1" y="713"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="117375065288344711" ObjectName="版纳_35kV_贺蚌变\版纳_35kV_贺蚌变/10kV\XF_＃1主变-低" Plane="0"/>
  </metadata>
 </g>
 <metadata>
  <cge:PSR_Ref ObjectID="117093590311633399" ObjectName="版纳_35kV_贺蚌变\XFMR_＃1主变" Plane="0"/>
 <cge:TPSR_Ref TObjectID="117093590311633399"/></metadata>
<rect fill="white" height="80" opacity="0" stroke="white" transform="rotate(0,1157,713) scale(1.126,1.126) translate(-149.469,-117.785)" width="40" x="1157" y="713"/></g>
<g id="102003048">
 <g id="1020030480">
  <use class="kv35kV" height="60" transform="rotate(270,1390,227) scale(-2.146,2.146) translate(-2059.72,-126.222)" width="50" x="1390" xlink:href="#Transformer2:立新变站用变_0" y="227"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="117375065288344724" ObjectName="版纳_35kV_贺蚌变\版纳_35kV_贺蚌变/35kV\XF_1号站用变-高" Plane="0"/>
  </metadata>
 </g>
 <g id="1020030481">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="117375065288344725" ObjectName="版纳_35kV_贺蚌变\版纳_35kV_贺蚌变/35kV\XF_1号站用变-低" Plane="0"/>
  </metadata>
 </g>
 <metadata>
  <cge:PSR_Ref ObjectID="117093590311633406" ObjectName="版纳_35kV_贺蚌变\XFMR_1号站用变" Plane="0"/>
 <cge:TPSR_Ref TObjectID="117093590311633406"/></metadata>
<rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(270,1390,227) scale(-2.146,2.146) translate(-2059.72,-126.222)" width="50" x="1390" y="227"/></g>
</g>
<g id="ACLineSegment_Layer">
 <g id="36003011">
 <path d="M 1274 171 L 1274 116" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="ST=113997365567815943/LN=116530640358212502" ObjectName="ST=版纳_35kV_贺蚌变/LN=ACLN_佛满II回线" Plane="0"/>
  <cge:TPSR_Ref TObjectID="116530640358212502_113997365567815943"/></metadata>
 <path d="M 1274 171 L 1274 116" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="PT_Layer">
 <g id="107003019">
  <use class="kv35kV" height="70" transform="rotate(0,1066,411) scale(-1.665,-1.665) translate(-1739.24,-661.847)" width="70" x="1066" xlink:href="#PT:gpb_0" y="411"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189754" ObjectName="版纳_35kV_贺蚌变\版纳_35kV_贺蚌变/35kV\TERM_35kVI母PT" Plane="0"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,1066,411) scale(-1.665,-1.665) translate(-1739.24,-661.847)" width="70" x="1066" y="411"/></g>
 <g id="107003049">
  <use class="kv35kV" height="70" transform="rotate(0,1273,303) scale(0.968,-0.968) translate(-5.91741,-650.016)" width="120" x="1273" xlink:href="#PT:立新贺蚌_0" y="303"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189755" ObjectName="版纳_35kV_贺蚌变\版纳_35kV_贺蚌变/35kV\TERM_35kV361线路PT" Plane="0"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,1273,303) scale(0.968,-0.968) translate(-5.91741,-650.016)" width="120" x="1273" y="303"/></g>
</g>
<g id="GZP_Layer">
 <g id="135002725">
  <use class="kv35kV" height="36" transform="rotate(0,215,516) scale(0.697,0.697) translate(75.4648,206.316)" width="36" x="215" xlink:href="#GZP:gg_光子牌1_0" y="516"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892567938" ObjectName="122160139892567938" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,215,516) scale(0.697,0.697) translate(75.4648,206.316)" width="36" x="215" y="516"/></g>
</g>
<g id="Arrester_Layer">
 <g id="133003023">
  <use class="kv35kV" height="20" transform="rotate(0,1205,655) scale(1.3,-1.3) translate(-280.077,-1168.85)" width="40" x="1205" xlink:href="#Arrester:qj_ocs_blq2_0" y="655"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189756" ObjectName="版纳_35kV_贺蚌变\版纳_35kV_贺蚌变/35kV\TERM_1号主变避雷器" Plane="0"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1205,655) scale(1.3,-1.3) translate(-280.077,-1168.85)" width="40" x="1205" y="655"/></g>
</g>
<g id="Fuse_Layer">
 <g id="131003050">
  <use class="kv35kV" height="16" transform="rotate(0,1330,224) scale(-1.547,-1.547) translate(-2206.73,-381.796)" width="32" x="1330" xlink:href="#Fuse:bn_熔断器7_0" y="224"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189763" ObjectName="版纳_35kV_贺蚌变\版纳_35kV_贺蚌变/35kV\TERM_熔断器" Plane="0"/>
  </metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(0,1330,224) scale(-1.547,-1.547) translate(-2206.73,-381.796)" width="32" x="1330" y="224"/></g>
</g>
<g id="Status_Layer">
 <g id="126002742">
  <use class="kv-1" height="40" transform="rotate(0,48,199) scale(0.45,0.45) translate(28.6667,223.222)" width="60" x="48" xlink:href="#Status:bn_工况退出颜色显示_0" y="199"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,48,199) scale(0.45,0.45) translate(28.6667,223.222)" width="60" x="48" y="199"/></g>
 <g id="126002743">
  <use class="kv-1" height="40" transform="rotate(0,102,199) scale(0.45,0.45) translate(94.6667,223.222)" width="60" x="102" xlink:href="#Status:bn_不变化颜色显示_0" y="199"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,102,199) scale(0.45,0.45) translate(94.6667,223.222)" width="60" x="102" y="199"/></g>
 <g id="126002744">
  <use class="kv-1" height="40" transform="rotate(0,157,199) scale(0.45,0.45) translate(161.889,223.222)" width="60" x="157" xlink:href="#Status:bn_越限颜色显示_0" y="199"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,157,199) scale(0.45,0.45) translate(161.889,223.222)" width="60" x="157" y="199"/></g>
 <g id="126002745">
  <use class="kv-1" height="40" transform="rotate(0,215,199) scale(0.45,0.45) translate(232.778,223.222)" width="60" x="215" xlink:href="#Status:bn_非实测颜色显示_0" y="199"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,215,199) scale(0.45,0.45) translate(232.778,223.222)" width="60" x="215" y="199"/></g>
 <g id="126002746">
  <use class="kv-1" height="40" transform="rotate(0,274,199) scale(0.45,0.45) translate(304.889,223.222)" width="60" x="274" xlink:href="#Status:bn_数据封锁颜色显示_0" y="199"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,274,199) scale(0.45,0.45) translate(304.889,223.222)" width="60" x="274" y="199"/></g>
</g>
<g id="Clock_Layer">
 <g id="56002724">
  
 <metadata/></g>
</g>
<g id="Term_Layer">
 <g id="112003021">
  <use class="kv35kV" height="70" transform="rotate(0,1071,654) scale(0.937,1.343) translate(3.00957,-172.031)" width="120" x="1071" xlink:href="#Terminal:bn_景仰变_0" y="654"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195189757" ObjectName="版纳_35kV_贺蚌变\版纳_35kV_贺蚌变/35kV\TERM_电容器" Plane="0"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,1071,654) scale(0.937,1.343) translate(3.00957,-172.031)" width="120" x="1071" y="654"/></g>
 <g id="112003054">
  <use class="kv-1" height="12" transform="rotate(179,1375,226) scale(0.968,0.968) translate(27.4545,1.47106)" width="38" x="1375" xlink:href="#Terminal:bn_35kV电缆_0" y="226"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(179,1375,226) scale(0.968,0.968) translate(27.4545,1.47106)" width="38" x="1375" y="226"/></g>
</g>
<g id="Link_Layer">
 <g id="34002999">
 <path d="M 1101 622 L 1157 622" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111003020_0" Plane="0"/>
  </metadata>
 <path d="M 1101 622 L 1157 622" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003000">
 <path d="M 1157 483 L 1157 515" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="30002986_0" Pin1InfoVect0LinkObjId="100003012_0" Plane="0"/>
  </metadata>
 <path d="M 1157 483 L 1157 515" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003001">
 <path d="M 1157 547 L 1157 566" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100003012_1" Pin1InfoVect0LinkObjId="101003015_1" Plane="0"/>
  </metadata>
 <path d="M 1157 547 L 1157 566" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003004">
 <path d="M 1072 655 L 1157 655" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="112003021_0" Pin1InfoVect0LinkObjId="34003006_0" Pin1InfoVect1LinkObjId="34003057_1" Pin1InfoVect2LinkObjId="34003005_1" Plane="0"/>
  </metadata>
 <path d="M 1072 655 L 1157 655" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003005">
 <path d="M 1157 604 L 1157 655" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101003015_0" Pin1InfoVect0LinkObjId="34003006_0" Pin1InfoVect1LinkObjId="34003057_1" Pin1InfoVect2LinkObjId="34003004_1" Plane="0"/>
  </metadata>
 <path d="M 1157 604 L 1157 655" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003006">
 <path d="M 1157 655 L 1157 675" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34003004_1" Pin0InfoVect1LinkObjId="34003005_1" Pin0InfoVect2LinkObjId="34003057_1" Pin1InfoVect0LinkObjId="102003018_0" Plane="0"/>
  </metadata>
 <path d="M 1157 655 L 1157 675" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003008">
 <path d="M 1157 752 L 1157 773" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="102003018_1" Pin1InfoVect0LinkObjId="100003014_0" Plane="0"/>
  </metadata>
 <path d="M 1157 752 L 1157 773" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003009">
 <path d="M 1157 805 L 1157 834" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100003014_1" Pin1InfoVect0LinkObjId="101003017_0" Plane="0"/>
  </metadata>
 <path d="M 1157 805 L 1157 834" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003010">
 <path d="M 1157 872 L 1157 915" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101003017_1" Plane="0"/>
  </metadata>
 <path d="M 1157 872 L 1157 915" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003024">
 <path d="M 1066 412 L 1066 483" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="107003019_0" Pin1InfoVect0LinkObjId="30002986_0" Plane="0"/>
  </metadata>
 <path d="M 1066 412 L 1066 483" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003042">
 <path d="M 1274 399 L 1274 420" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101003016_0" Pin1InfoVect0LinkObjId="100003013_0" Plane="0"/>
  </metadata>
 <path d="M 1274 399 L 1274 420" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003043">
 <path d="M 1274 450 L 1274 483" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100003013_1" Pin1InfoVect0LinkObjId="30002986_0" Plane="0"/>
  </metadata>
 <path d="M 1274 450 L 1274 483" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003046">
 <path d="M 1350 227 L 1390 227" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="131003050_0" Pin1InfoVect0LinkObjId="102003048_0" Plane="0"/>
  </metadata>
 <path d="M 1350 227 L 1390 227" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003044">
 <path d="M 1274 225 L 1314 225" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34003047_0" Pin0InfoVect1LinkObjId="34003045_1" Pin1InfoVect0LinkObjId="131003050_1" Plane="0"/>
  </metadata>
 <path d="M 1274 225 L 1314 225" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003045">
 <path d="M 1274 171 L 1274 225" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="36003011_0" Pin1InfoVect0LinkObjId="34003047_0" Pin1InfoVect1LinkObjId="34003044_0" Plane="0"/>
  </metadata>
 <path d="M 1274 171 L 1274 225" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003047">
 <path d="M 1274 225 L 1274 274" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34003044_0" Pin0InfoVect1LinkObjId="34003045_1" Pin1InfoVect0LinkObjId="107003049_1" Plane="0"/>
  </metadata>
 <path d="M 1274 225 L 1274 274" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003051">
 <path d="M 1274 335 L 1274 360" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="107003049_0" Pin1InfoVect0LinkObjId="101003016_1" Plane="0"/>
  </metadata>
 <path d="M 1274 335 L 1274 360" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34003057">
 <path d="M 1205 655 L 1157 655" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133003023_0" Pin1InfoVect0LinkObjId="34003006_0" Pin1InfoVect1LinkObjId="34003004_1" Pin1InfoVect2LinkObjId="34003005_1" Plane="0"/>
  </metadata>
 <path d="M 1205 655 L 1157 655" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="MeasurementValue_Layer">
 <g id="33002719">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,0)" writing-mode="lr" x="175" xml:space="preserve" y="469">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="113997515891671303" ObjectName="版纳_35kV_贺蚌变:Q_LOAD" Plane="0"/>
  </metadata>
 </g>
 <g id="33002718">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,0)" writing-mode="lr" x="174" xml:space="preserve" y="423">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="113997494416834823" ObjectName="版纳_35kV_贺蚌变:P_LOAD" Plane="0"/>
  </metadata>
 </g>
 <g id="33002722">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,0)" writing-mode="lr" x="218" xml:space="preserve" y="758">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002723">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,0)" writing-mode="lr" x="218" xml:space="preserve" y="701">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002856">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,0)" writing-mode="lr" x="192" xml:space="preserve" y="583">-00000</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="117375408885728390" ObjectName="版纳_35kV_贺蚌变\版纳_35kV_贺蚌变/35kV\XF_＃1主变-高:TAP" Plane="0"/>
  </metadata>
 </g>
 <g id="33002855">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,0)" writing-mode="lr" x="192" xml:space="preserve" y="639">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="122723132795531657" ObjectName="122723089845858697:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002987">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,0)" writing-mode="lr" x="854" xml:space="preserve" y="363">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115405084048753513" ObjectName="版纳_35kV_贺蚌变\版纳_35kV_贺蚌变/35kV\BS_35kVⅠ段母线:V_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33002988">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,0)" writing-mode="lr" x="854" xml:space="preserve" y="292">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115404869300388713" ObjectName="版纳_35kV_贺蚌变\版纳_35kV_贺蚌变/35kV\BS_35kVⅠ段母线:V" Plane="0"/>
  </metadata>
 </g>
 <g id="33002989">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,0)" writing-mode="lr" x="854" xml:space="preserve" y="316">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115404998149407593" ObjectName="版纳_35kV_贺蚌变\版纳_35kV_贺蚌变/35kV\BS_35kVⅠ段母线:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33002990">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,0)" writing-mode="lr" x="854" xml:space="preserve" y="339">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115405041099080553" ObjectName="版纳_35kV_贺蚌变\版纳_35kV_贺蚌变/35kV\BS_35kVⅠ段母线:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33002991">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,0)" writing-mode="lr" x="854" xml:space="preserve" y="433">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795531581" ObjectName="122723089845858621:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002992">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,0)" writing-mode="lr" x="854" xml:space="preserve" y="386">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115405126998426473" ObjectName="版纳_35kV_贺蚌变\版纳_35kV_贺蚌变/35kV\BS_35kVⅠ段母线:V_B" Plane="0"/>
  </metadata>
 </g>
 <g id="33002993">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,0)" writing-mode="lr" x="854" xml:space="preserve" y="410">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115405169948099433" ObjectName="版纳_35kV_贺蚌变\版纳_35kV_贺蚌变/35kV\BS_35kVⅠ段母线:V_C" Plane="0"/>
  </metadata>
 </g>
 <g id="33002994">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,0)" writing-mode="lr" x="1258" xml:space="preserve" y="59">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="116812244183943001" ObjectName="ST=版纳_35kV_贺蚌变/LN=ACLN_佛满II回线:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33002995">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,0)" writing-mode="lr" x="1258" xml:space="preserve" y="81">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="116812287133615961" ObjectName="ST=版纳_35kV_贺蚌变/LN=ACLN_佛满II回线:Q" Plane="0"/>
  </metadata>
 </g>
 <g id="33002996">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,0)" writing-mode="lr" x="1258" xml:space="preserve" y="103">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="116812415982634841" ObjectName="ST=版纳_35kV_贺蚌变/LN=ACLN_佛满II回线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33003029">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,0)" writing-mode="lr" x="1194" xml:space="preserve" y="529">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709510" ObjectName="版纳_35kV_贺蚌变\版纳_35kV_贺蚌变/35kV\XF_＃1主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33003030">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,0)" writing-mode="lr" x="1194" xml:space="preserve" y="554">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375322986382470" ObjectName="版纳_35kV_贺蚌变\版纳_35kV_贺蚌变/35kV\XF_＃1主变-高:Q" Plane="0"/>
  </metadata>
 </g>
 <g id="33003031">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,0)" writing-mode="lr" x="1194" xml:space="preserve" y="579">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375451835401350" ObjectName="版纳_35kV_贺蚌变\版纳_35kV_贺蚌变/35kV\XF_＃1主变-高:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33003035">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,0)" writing-mode="lr" x="1192" xml:space="preserve" y="780">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709511" ObjectName="版纳_35kV_贺蚌变\版纳_35kV_贺蚌变/10kV\XF_＃1主变-低:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33003036">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,0)" writing-mode="lr" x="1192" xml:space="preserve" y="805">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375322986382471" ObjectName="版纳_35kV_贺蚌变\版纳_35kV_贺蚌变/10kV\XF_＃1主变-低:Q" Plane="0"/>
  </metadata>
 </g>
 <g id="33003037">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,0)" writing-mode="lr" x="1192" xml:space="preserve" y="830">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375451835401351" ObjectName="版纳_35kV_贺蚌变\版纳_35kV_贺蚌变/10kV\XF_＃1主变-低:I_A" Plane="0"/>
  </metadata>
 </g>
</g>
<g id="Text_Layer">
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="88" xml:space="preserve" y="521">事故总:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="53" xml:space="preserve" y="421">总有功</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="116" xml:space="preserve" y="583">档位</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="tb" x="62" xml:space="preserve" y="559">一</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="116" xml:space="preserve" y="638">油温</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="57" xml:space="preserve" y="705">控制母线电压</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="57" xml:space="preserve" y="761">合闸母线电压</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(170,0,0)" writing-mode="lr" x="148" xml:space="preserve" y="295">MW</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="91" xml:space="preserve" y="294">有功:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="186" xml:space="preserve" y="294">无功:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(170,0,0)" writing-mode="lr" x="239" xml:space="preserve" y="295">MVr</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="91" xml:space="preserve" y="338">电流:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(170,0,0)" writing-mode="lr" x="148" xml:space="preserve" y="339">A</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="186" xml:space="preserve" y="338">电压:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(170,0,0)" writing-mode="lr" x="239" xml:space="preserve" y="339">kV</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="91" xml:space="preserve" y="381">频率:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(170,0,0)" writing-mode="lr" x="148" xml:space="preserve" y="382">Hz</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="186" xml:space="preserve" y="381">温度:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(170,0,0)" writing-mode="lr" x="239" xml:space="preserve" y="383">℃</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="54" xml:space="preserve" y="466">总无功</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="tb" x="49" xml:space="preserve" y="303">单</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="tb" x="48" xml:space="preserve" y="353">位</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="tb" x="62" xml:space="preserve" y="577">号</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="tb" x="62" xml:space="preserve" y="601">主</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="tb" x="62" xml:space="preserve" y="627">变</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="46" font-size="31" font-width="31" stroke="rgb(0,0,0)" writing-mode="lr" x="146" xml:space="preserve" y="90">35kV贺蚌变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="13" font-size="13" font-width="13" stroke="rgb(255,255,254)" writing-mode="lr" x="31" xml:space="preserve" y="241">工况</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="13" font-size="13" font-width="13" stroke="rgb(255,255,254)" writing-mode="lr" x="31" xml:space="preserve" y="254">退出</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="13" font-size="13" font-width="13" stroke="rgb(255,255,254)" writing-mode="lr" x="92" xml:space="preserve" y="240">不</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="13" font-size="13" font-width="13" stroke="rgb(255,255,254)" writing-mode="lr" x="85" xml:space="preserve" y="260">变化</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="13" font-size="13" font-width="13" stroke="rgb(255,255,254)" writing-mode="lr" x="144" xml:space="preserve" y="251">越限</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="13" font-size="13" font-width="13" stroke="rgb(255,255,254)" writing-mode="lr" x="207" xml:space="preserve" y="242">非</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="13" font-size="13" font-width="13" stroke="rgb(255,255,254)" writing-mode="lr" x="199" xml:space="preserve" y="259">实测</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="13" font-size="13" font-width="13" stroke="rgb(255,255,254)" writing-mode="lr" x="261" xml:space="preserve" y="242">数据</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="13" font-size="13" font-width="13" stroke="rgb(255,255,254)" writing-mode="lr" x="261" xml:space="preserve" y="255">封锁</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(35,169,128)" writing-mode="lr" x="821" xml:space="preserve" y="366">Ua</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(35,169,128)" writing-mode="lr" x="821" xml:space="preserve" y="319">Ubc</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(35,169,128)" writing-mode="lr" x="821" xml:space="preserve" y="295">Uab</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(35,169,128)" writing-mode="lr" x="821" xml:space="preserve" y="342">Uca</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="844" xml:space="preserve" y="469">35kVⅠ母</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="1090" xml:space="preserve" y="535">362</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="1072" xml:space="preserve" y="592">3626</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(35,169,128)" writing-mode="lr" x="820" xml:space="preserve" y="436">3U0</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(35,169,128)" writing-mode="lr" x="820" xml:space="preserve" y="413">Uc</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(35,169,128)" writing-mode="lr" x="820" xml:space="preserve" y="389">Ub</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(35,169,128)" writing-mode="lr" x="1237" xml:space="preserve" y="104">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(35,169,128)" writing-mode="lr" x="1237" xml:space="preserve" y="82">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(35,169,128)" writing-mode="lr" x="1237" xml:space="preserve" y="60">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="1290" xml:space="preserve" y="382">3616</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="1291" xml:space="preserve" y="438">361</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="1034" xml:space="preserve" y="653">36267</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="1087" xml:space="preserve" y="793">011</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="1075" xml:space="preserve" y="851">0111</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="995" xml:space="preserve" y="283">35kVI母PT</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(230,232,254)" writing-mode="lr" x="1232" xml:space="preserve" y="37">佛满II回线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(35,169,128)" writing-mode="lr" x="1179" xml:space="preserve" y="579">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(35,169,128)" writing-mode="lr" x="1179" xml:space="preserve" y="552">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(35,169,128)" writing-mode="lr" x="1179" xml:space="preserve" y="526">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(35,169,128)" writing-mode="lr" x="1181" xml:space="preserve" y="830">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(35,169,128)" writing-mode="lr" x="1181" xml:space="preserve" y="803">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(35,169,128)" writing-mode="lr" x="1181" xml:space="preserve" y="777">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="1390" xml:space="preserve" y="325">1号站用变压器</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="1400" xml:space="preserve" y="305">SCB-50/35</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="1192" xml:space="preserve" y="701">(S=10MVA)</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="1214" xml:space="preserve" y="721">1号主变</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href="勐海地区new.sys.svg"><rect fill-opacity="0" height="65" stroke-opacity="0" stroke-width="1" width="286" x="44" y="44"/></g>
</g>
</svg>