<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1372" id="thSvg" viewBox="0 0 2820 1372" width="2820">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="Status:微机保护设备3_0" viewBox="0,0,46,26">
 <circle AFMask="2147483647" Plane="0" cx="36" cy="8" fill="rgb(0,255,0)" r="5" stroke="rgb(0,255,0)" stroke-width="1"/>
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="9" font-size="8" font-width="8" stroke="rgb(0,255,0)" writing-mode="lr" x="29" xml:space="preserve" y="23">投运</text>
</symbol>
<symbol id="Status:微机保护设备3_1" viewBox="0,0,46,26">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="9" font-size="8" font-width="8" stroke="rgb(255,255,254)" writing-mode="lr" x="29" xml:space="preserve" y="22">停运</text>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(255,255,254)" r="5" stroke="rgb(255,255,254)" stroke-width="1"/>
</symbol>
<symbol id="Status:微机保护设备3_2" viewBox="0,0,46,26">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="9" font-size="8" font-width="8" stroke="rgb(255,255,0)" writing-mode="lr" x="29" xml:space="preserve" y="21">调试</text>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Status:微机保护设备3_3" viewBox="0,0,46,26">
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(255,255,0)" r="5" stroke="rgb(255,0,0)" stroke-width="1"/>
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="8" font-size="7" font-width="7" stroke="rgb(255,0,0)" writing-mode="lr" x="29" xml:space="preserve" y="22">检修</text>
</symbol>
<symbol id="Status:微机保护设备3_4" viewBox="0,0,46,26">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="9" font-size="8" font-width="8" stroke="rgb(255,255,0)" writing-mode="lr" x="28" xml:space="preserve" y="22">动作</text>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(255,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Status:微机保护设备3_5" viewBox="0,0,46,26">
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(255,170,255)" stroke-width="1"/>
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="8" font-size="8" font-width="8" stroke="rgb(255,85,255)" writing-mode="lr" x="29" xml:space="preserve" y="20">告警</text>
</symbol>
<symbol id="Status:微机保护设备3_6" viewBox="0,0,46,26">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="10" font-size="8" font-width="8" stroke="rgb(255,255,0)" writing-mode="lr" x="29" xml:space="preserve" y="23">变位</text>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Status:微机保护设备3_7" viewBox="0,0,46,26">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="8" font-size="8" font-width="8" stroke="rgb(255,0,0)" writing-mode="lr" x="29" xml:space="preserve" y="20">中断</text>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(255,0,0)" r="5" stroke="rgb(255,0,0)" stroke-width="1"/>
</symbol>
<symbol id="Status:微机保护设备3_8" viewBox="0,0,46,26">
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(255,0,127)" r="5" stroke="rgb(255,0,127)" stroke-width="1"/>
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="4" font-size="4" font-width="5" stroke="rgb(255,0,127)" writing-mode="lr" x="30" xml:space="preserve" y="17">抑制</text>
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="4" font-size="4" font-width="5" stroke="rgb(255,0,127)" writing-mode="lr" x="30" xml:space="preserve" y="21">告警</text>
</symbol>
<symbol id="Status:微机保护设备3_9" viewBox="0,0,46,26">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="8" font-size="4" font-width="4" stroke="rgb(0,0,255)" writing-mode="lr" x="28" xml:space="preserve" y="22">告警抑制</text>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Status:微机保护设备3_10" viewBox="0,0,46,26">
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="6" font-size="2" font-width="2" stroke="rgb(0,0,255)" writing-mode="lr" x="28" xml:space="preserve" y="19">正在修改定值</text>
</symbol>
<symbol id="Status:微机保护设备3_11" viewBox="0,0,46,26">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="7" font-size="2" font-width="2" stroke="rgb(0,0,255)" writing-mode="lr" x="28" xml:space="preserve" y="20">闭锁修改定值</text>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Status:微机保护设备3_12" viewBox="0,0,46,26">
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="9" font-size="8" font-width="8" stroke="rgb(0,0,255)" writing-mode="lr" x="29" xml:space="preserve" y="21">动作</text>
</symbol>
<symbol id="Status:微机保护设备3_13" viewBox="0,0,46,26">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="9" font-size="8" font-width="8" stroke="rgb(0,0,255)" writing-mode="lr" x="29" xml:space="preserve" y="21">告警</text>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Status:微机保护设备3_14" viewBox="0,0,46,26">
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="9" font-size="8" font-width="8" stroke="rgb(0,0,255)" writing-mode="lr" x="29" xml:space="preserve" y="21">变位</text>
</symbol>
<symbol id="Status:微机保护设备3_15" viewBox="0,0,46,26">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="7" font-size="5" font-width="5" stroke="rgb(0,0,255)" writing-mode="lr" x="28" xml:space="preserve" y="21">未确认</text>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="版纳_35kV_勐满变" InitShowingPlane="0," fill="rgb(0,0,0)" height="1372" width="2820" x="0" y="0"/>
</g>
<g id="Other_Layer">
 
 <rect AFMask="39039" Plane="0" fill="none" height="1047" stroke="rgb(85,255,255)" stroke-width="1" transform="rotate(0,1396,724)" width="2666" x="63" y="201"/>
</g>
<g id="Status_Layer">
 <g id="126002182">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="279786126850392093" ObjectName="279786126850392093" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,151,274) scale(2.865,2.865) translate(-121.295,-192.363)" width="46" x="151" y="274"/></g>
 <g id="126002325">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="279786126850392094" ObjectName="279786126850392094" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,151,374) scale(2.865,2.865) translate(-121.295,-257.459)" width="46" x="151" y="374"/></g>
 <g id="126002330">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="279786126850392095" ObjectName="279786126850392095" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,151,474) scale(2.865,2.865) translate(-121.295,-322.555)" width="46" x="151" y="474"/></g>
 <g id="126002335">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="279786126850392096" ObjectName="279786126850392096" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,151,574) scale(2.865,2.865) translate(-121.295,-387.651)" width="46" x="151" y="574"/></g>
 <g id="126002340">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="279786126850392097" ObjectName="279786126850392097" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,151,674) scale(2.865,2.865) translate(-121.295,-452.747)" width="46" x="151" y="674"/></g>
 <g id="126002345">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="279786126850392098" ObjectName="279786126850392098" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,151,774) scale(2.865,2.865) translate(-121.295,-517.843)" width="46" x="151" y="774"/></g>
 <g id="126002350">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="279786126850392099" ObjectName="279786126850392099" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,151,874) scale(2.865,2.865) translate(-121.295,-582.939)" width="46" x="151" y="874"/></g>
 <g id="126002355">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="279786126850392100" ObjectName="279786126850392100" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,151,974) scale(2.865,2.865) translate(-121.295,-648.035)" width="46" x="151" y="974"/></g>
 <g id="126002360">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="279786126850392101" ObjectName="279786126850392101" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,151,1074) scale(2.865,2.865) translate(-121.295,-713.131)" width="46" x="151" y="1074"/></g>
 <g id="126002365">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="279786126850392102" ObjectName="279786126850392102" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,151,1174) scale(2.865,2.865) translate(-121.295,-778.227)" width="46" x="151" y="1174"/></g>
 <g id="126002375">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="279786126850392103" ObjectName="279786126850392103" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1506,277) scale(2.865,2.865) translate(-1003.35,-194.316)" width="46" x="1506" y="277"/></g>
</g>
<g id="MeasurementValue_Layer">
 <g id="33002224">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="53" font-size="53" font-width="64" stroke="rgb(255,255,254)" writing-mode="lr" x="1193" xml:space="preserve" y="301">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795532170" ObjectName="122723089845859210:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002324">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="52" font-size="52" font-width="63" stroke="rgb(255,255,254)" writing-mode="lr" x="1193" xml:space="preserve" y="400">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795532174" ObjectName="122723089845859214:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002329">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="52" font-size="52" font-width="63" stroke="rgb(255,255,254)" writing-mode="lr" x="1193" xml:space="preserve" y="500">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795532175" ObjectName="122723089845859215:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002334">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="52" font-size="52" font-width="63" stroke="rgb(255,255,254)" writing-mode="lr" x="1193" xml:space="preserve" y="600">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795532176" ObjectName="122723089845859216:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002339">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="52" font-size="52" font-width="63" stroke="rgb(255,255,254)" writing-mode="lr" x="1193" xml:space="preserve" y="700">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795532177" ObjectName="122723089845859217:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002344">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="52" font-size="52" font-width="63" stroke="rgb(255,255,254)" writing-mode="lr" x="1193" xml:space="preserve" y="800">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795532178" ObjectName="122723089845859218:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002349">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="52" font-size="52" font-width="63" stroke="rgb(255,255,254)" writing-mode="lr" x="1193" xml:space="preserve" y="900">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795532171" ObjectName="122723089845859211:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002354">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="52" font-size="52" font-width="63" stroke="rgb(255,255,254)" writing-mode="lr" x="1193" xml:space="preserve" y="1000">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795532172" ObjectName="122723089845859212:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002359">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="52" font-size="52" font-width="63" stroke="rgb(255,255,254)" writing-mode="lr" x="1193" xml:space="preserve" y="1100">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795532173" ObjectName="122723089845859213:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002364">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="52" font-size="52" font-width="63" stroke="rgb(255,255,254)" writing-mode="lr" x="1193" xml:space="preserve" y="1200">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795532179" ObjectName="122723089845859219:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002374">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="53" font-size="53" font-width="64" stroke="rgb(255,255,254)" writing-mode="lr" x="2548" xml:space="preserve" y="304">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795532180" ObjectName="122723089845859220:M" Plane="0"/>
  </metadata>
 </g>
</g>
<g id="Text_Layer">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="46" font-size="34" font-width="34" stroke="rgb(85,255,255)" writing-mode="lr" x="1178" xml:space="preserve" y="105">35kV勐满变定值区切换图</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="43" font-size="43" font-width="43" stroke="rgb(255,255,254)" writing-mode="lr" x="237" xml:space="preserve" y="296">35kV佛满线保护测控装置</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="49" font-size="49" font-width="49" stroke="rgb(0,255,255)" writing-mode="lr" x="907" xml:space="preserve" y="299">当前定值区</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="43" font-size="43" font-width="43" stroke="rgb(255,255,254)" writing-mode="lr" x="237" xml:space="preserve" y="396">10kV备用线保护测控装置</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="49" font-size="49" font-width="49" stroke="rgb(0,255,255)" writing-mode="lr" x="907" xml:space="preserve" y="399">当前定值区</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="43" font-size="43" font-width="43" stroke="rgb(255,255,254)" writing-mode="lr" x="237" xml:space="preserve" y="496">10kV城子线保护测控装置</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="49" font-size="49" font-width="49" stroke="rgb(0,255,255)" writing-mode="lr" x="907" xml:space="preserve" y="499">当前定值区</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="43" font-size="43" font-width="43" stroke="rgb(255,255,254)" writing-mode="lr" x="237" xml:space="preserve" y="596">10kV南罕线保护测控装置</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="49" font-size="49" font-width="49" stroke="rgb(0,255,255)" writing-mode="lr" x="907" xml:space="preserve" y="599">当前定值区</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="43" font-size="43" font-width="43" stroke="rgb(255,255,254)" writing-mode="lr" x="237" xml:space="preserve" y="696">10kV2号电容器组保护测控装置</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="49" font-size="49" font-width="49" stroke="rgb(0,255,255)" writing-mode="lr" x="907" xml:space="preserve" y="699">当前定值区</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="43" font-size="43" font-width="43" stroke="rgb(255,255,254)" writing-mode="lr" x="237" xml:space="preserve" y="796">10kV分段保护测控装置</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="49" font-size="49" font-width="49" stroke="rgb(0,255,255)" writing-mode="lr" x="907" xml:space="preserve" y="799">当前定值区</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="43" font-size="43" font-width="43" stroke="rgb(255,255,254)" writing-mode="lr" x="237" xml:space="preserve" y="896">10kV金矿线保护测控装置</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="49" font-size="49" font-width="49" stroke="rgb(0,255,255)" writing-mode="lr" x="907" xml:space="preserve" y="899">当前定值区</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="43" font-size="43" font-width="43" stroke="rgb(255,255,254)" writing-mode="lr" x="237" xml:space="preserve" y="996">10kV佛满联络线保护测控装置</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="49" font-size="49" font-width="49" stroke="rgb(0,255,255)" writing-mode="lr" x="907" xml:space="preserve" y="999">当前定值区</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="43" font-size="43" font-width="43" stroke="rgb(255,255,254)" writing-mode="lr" x="237" xml:space="preserve" y="1096">10kV勐满政府线保护测控装置</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="49" font-size="49" font-width="49" stroke="rgb(0,255,255)" writing-mode="lr" x="907" xml:space="preserve" y="1099">当前定值区</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="43" font-size="43" font-width="43" stroke="rgb(255,255,254)" writing-mode="lr" x="237" xml:space="preserve" y="1196">10kV2号站用变保护测控装置</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="49" font-size="49" font-width="49" stroke="rgb(0,255,255)" writing-mode="lr" x="907" xml:space="preserve" y="1199">当前定值区</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="43" font-size="43" font-width="43" stroke="rgb(255,255,254)" writing-mode="lr" x="1592" xml:space="preserve" y="299">低频低压保护测控装置</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="49" font-size="49" font-width="49" stroke="rgb(0,255,255)" writing-mode="lr" x="2262" xml:space="preserve" y="302">当前定值区</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href="版纳_35kV_勐满变.fac.svg"><rect fill-opacity="0" height="68" stroke-opacity="0" stroke-width="1" width="407" x="1170" y="44"/></g>
</g>
</svg>