<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="sys" height="1000" id="thSvg" viewBox="0 0 2100 1000" width="2100">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="Protect:bn_保护图元1_0" viewBox="0,0,18,18">
 <rect AFMask="2147483647" Plane="0" fill="none" height="13" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,8,8)" width="13" x="2" y="2"/>
</symbol>
<symbol id="Protect:bn_保护图元1_1" viewBox="0,0,18,18">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="13" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,8,8)" width="13" x="2" y="2"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="-1" InitShowingPlane="0," fill="rgb(0,0,0)" height="1000" width="2100" x="0" y="0"/>
</g>
<g id="Other_Layer">
 
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="228" x2="228" y1="87" y2="724"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="596" x2="596" y1="86" y2="724"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="689" x2="689" y1="84" y2="725"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="229" x2="687" y1="164" y2="164"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="688" y1="85" y2="85"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="15" x2="15" y1="85" y2="726"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="688" y1="125" y2="125"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="230" x2="689" y1="204" y2="204"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="689" y1="244" y2="244"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="689" y1="284" y2="284"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="689" y1="324" y2="324"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="689" y1="364" y2="364"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="689" y1="404" y2="404"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="689" y1="444" y2="444"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="689" y1="484" y2="484"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="689" y1="524" y2="524"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="689" y1="564" y2="564"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="689" y1="604" y2="604"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="689" y1="644" y2="644"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="689" y1="684" y2="684"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="689" y1="724" y2="724"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="806" x2="1216" y1="85" y2="85"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="806" x2="806" y1="86" y2="245"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="807" x2="1213" y1="126" y2="126"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1020" x2="1020" y1="126" y2="247"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="808" x2="1212" y1="166" y2="166"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="808" x2="1213" y1="206" y2="206"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="807" x2="1216" y1="246" y2="246"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1215" x2="1215" y1="86" y2="245"/>
</g>
<g id="Protect_Layer">
 <g id="127000274">
  <use class="kv-1" height="18" transform="rotate(0,642,146) scale(1,1) translate(-9,-9)" width="18" x="642" xlink:href="#Protect:bn_保护图元1_0" y="146"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,642,146) scale(1,1) translate(-9,-9)" width="18" x="642" y="146"/></g>
 <g id="127000275">
  <use class="kv-1" height="18" transform="rotate(0,642,186) scale(1,1) translate(-9,-9)" width="18" x="642" xlink:href="#Protect:bn_保护图元1_0" y="186"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,642,186) scale(1,1) translate(-9,-9)" width="18" x="642" y="186"/></g>
 <g id="127000276">
  <use class="kv-1" height="18" transform="rotate(0,642,226) scale(1,1) translate(-9,-9)" width="18" x="642" xlink:href="#Protect:bn_保护图元1_0" y="226"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,642,226) scale(1,1) translate(-9,-9)" width="18" x="642" y="226"/></g>
 <g id="127000277">
  <use class="kv-1" height="18" transform="rotate(0,642,266) scale(1,1) translate(-9,-9)" width="18" x="642" xlink:href="#Protect:bn_保护图元1_0" y="266"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,642,266) scale(1,1) translate(-9,-9)" width="18" x="642" y="266"/></g>
 <g id="127000278">
  <use class="kv-1" height="18" transform="rotate(0,642,306) scale(1,1) translate(-9,-9)" width="18" x="642" xlink:href="#Protect:bn_保护图元1_0" y="306"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,642,306) scale(1,1) translate(-9,-9)" width="18" x="642" y="306"/></g>
 <g id="127000279">
  <use class="kv-1" height="18" transform="rotate(0,642,346) scale(1,1) translate(-9,-9)" width="18" x="642" xlink:href="#Protect:bn_保护图元1_0" y="346"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,642,346) scale(1,1) translate(-9,-9)" width="18" x="642" y="346"/></g>
 <g id="127000280">
  <use class="kv-1" height="18" transform="rotate(0,642,386) scale(1,1) translate(-9,-9)" width="18" x="642" xlink:href="#Protect:bn_保护图元1_0" y="386"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,642,386) scale(1,1) translate(-9,-9)" width="18" x="642" y="386"/></g>
 <g id="127000281">
  <use class="kv-1" height="18" transform="rotate(0,642,426) scale(1,1) translate(-9,-9)" width="18" x="642" xlink:href="#Protect:bn_保护图元1_0" y="426"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,642,426) scale(1,1) translate(-9,-9)" width="18" x="642" y="426"/></g>
 <g id="127000282">
  <use class="kv-1" height="18" transform="rotate(0,642,466) scale(1,1) translate(-9,-9)" width="18" x="642" xlink:href="#Protect:bn_保护图元1_0" y="466"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,642,466) scale(1,1) translate(-9,-9)" width="18" x="642" y="466"/></g>
 <g id="127000283">
  <use class="kv-1" height="18" transform="rotate(0,642,506) scale(1,1) translate(-9,-9)" width="18" x="642" xlink:href="#Protect:bn_保护图元1_0" y="506"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,642,506) scale(1,1) translate(-9,-9)" width="18" x="642" y="506"/></g>
 <g id="127000284">
  <use class="kv-1" height="18" transform="rotate(0,642,546) scale(1,1) translate(-9,-9)" width="18" x="642" xlink:href="#Protect:bn_保护图元1_0" y="546"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,642,546) scale(1,1) translate(-9,-9)" width="18" x="642" y="546"/></g>
 <g id="127000285">
  <use class="kv-1" height="18" transform="rotate(0,642,586) scale(1,1) translate(-9,-9)" width="18" x="642" xlink:href="#Protect:bn_保护图元1_0" y="586"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,642,586) scale(1,1) translate(-9,-9)" width="18" x="642" y="586"/></g>
 <g id="127000286">
  <use class="kv-1" height="18" transform="rotate(0,642,626) scale(1,1) translate(-9,-9)" width="18" x="642" xlink:href="#Protect:bn_保护图元1_0" y="626"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,642,626) scale(1,1) translate(-9,-9)" width="18" x="642" y="626"/></g>
 <g id="127000287">
  <use class="kv-1" height="18" transform="rotate(0,642,666) scale(1,1) translate(-9,-9)" width="18" x="642" xlink:href="#Protect:bn_保护图元1_0" y="666"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,642,666) scale(1,1) translate(-9,-9)" width="18" x="642" y="666"/></g>
 <g id="127000288">
  <use class="kv-1" height="18" transform="rotate(0,642,706) scale(1,1) translate(-9,-9)" width="18" x="642" xlink:href="#Protect:bn_保护图元1_0" y="706"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,642,706) scale(1,1) translate(-9,-9)" width="18" x="642" y="706"/></g>
</g>
<g id="MeasurementValue_Layer">
 <g id="33000346">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1099" xml:space="preserve" y="199">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000351">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1099" xml:space="preserve" y="235">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
</g>
<g id="Text_Layer">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="39" stroke="rgb(85,255,255)" writing-mode="lr" x="668" xml:space="preserve" y="44">药园变保护压板远方投退及信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="102" xml:space="preserve" y="117">线路</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="372" xml:space="preserve" y="116">名称</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="611" xml:space="preserve" y="116">软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="262" xml:space="preserve" y="156">1号主变非电量保护装置信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="262" xml:space="preserve" y="196">1号主变第Ⅰ套保护装置信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="259" xml:space="preserve" y="236">1号主变第Ⅱ套保护装置信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="316" xml:space="preserve" y="276">351保护装置信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="316" xml:space="preserve" y="316">352保护装置信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="316" xml:space="preserve" y="356">043保护装置信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="316" xml:space="preserve" y="396">044保护装置信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="316" xml:space="preserve" y="436">045保护装置信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="316" xml:space="preserve" y="476">046保护装置信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="316" xml:space="preserve" y="516">047保护装置信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="316" xml:space="preserve" y="556">048保护装置信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="316" xml:space="preserve" y="596">049保护装置信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="316" xml:space="preserve" y="636">052保护装置信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="316" xml:space="preserve" y="676">050保护装置信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="316" xml:space="preserve" y="716">051保护装置信号复归</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="89" xml:space="preserve" y="196">1号主变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="70" xml:space="preserve" y="276">35kV药基线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="69" xml:space="preserve" y="316">35kV药养线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="53" xml:space="preserve" y="356">10kV勐养政府线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="53" xml:space="preserve" y="396">10kV药养联络线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="33" xml:space="preserve" y="437">10kV勐养车站Ⅰ回线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="34" xml:space="preserve" y="477">10kV勐养车站Ⅱ回线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="66" xml:space="preserve" y="517">10kV煤矿线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="66" xml:space="preserve" y="558">10kV河边线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="52" xml:space="preserve" y="595">10kV1号站用变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="52" xml:space="preserve" y="635">10kV1号接地变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="52" xml:space="preserve" y="676">10kV1号电容器</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="52" xml:space="preserve" y="716">10kV2号电容器</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="976" xml:space="preserve" y="116">定值区切换</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="886" xml:space="preserve" y="157">线路</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1082" xml:space="preserve" y="156">定值区</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="826" xml:space="preserve" y="197">1号主变第Ⅰ套保护</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="824" xml:space="preserve" y="237">1号主变第Ⅱ套保护</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href="版纳_110kV_药园变.fac.svg"><rect fill-opacity="0" height="56" stroke-opacity="0" stroke-width="1" width="623" x="669" y="2"/></g>
</g>
</svg>