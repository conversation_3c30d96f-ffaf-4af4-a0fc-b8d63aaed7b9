<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1800" id="thSvg" viewBox="0 0 3850 1800" width="3850">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="Arrester:bn_避雷器123_0" viewBox="0,0,16,40">
 <use Plane="0" x="8" xlink:href="#terminal" y="4"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="18" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,8,19)" width="10" x="3" y="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="8" x2="8" y1="28" y2="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="8" x2="8" y1="5" y2="18"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1" x2="15" y1="35" y2="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="4" x2="12" y1="37" y2="37"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="6" x2="10" y1="39" y2="39"/>
 <path AFMask="2147483647" Plane="0" d="M 6 17 L 8 23 L 10 17 Z" fill="none" stroke="rgb(93,92,88)" stroke-width="1"/>
</symbol>
<symbol id="Arrester:bn_避雷器4_0" viewBox="0,0,20,40">
 <use Plane="0" x="10" xlink:href="#terminal" y="35"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="22" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,10,20)" width="10" x="5" y="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="5" y2="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="21" y2="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="5" x2="15" y1="5" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="7" x2="13" y1="3" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="11" y1="1" y2="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="8" x2="12" y1="20" y2="20"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="12" x2="10" y1="20" y2="17"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="8" y1="17" y2="20"/>
</symbol>
<symbol id="Status:bn_不变化颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(0,0,255)" height="1" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_不变化颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(85,0,255)" height="1" stroke="rgb(85,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Disconnector:bn_刀闸1_0" viewBox="0,0,40,20">
 <use Plane="0" x="32" xlink:href="#terminal" y="10"/>
 <use Plane="0" x="2" xlink:href="#terminal" y="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="29" y1="1" y2="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="29" y1="11" y2="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="4" y1="11" y2="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="18" y2="4"/>
</symbol>
<symbol id="Disconnector:bn_刀闸1_1" viewBox="0,0,40,20">
 <use Plane="0" x="32" xlink:href="#terminal" y="10"/>
 <use Plane="0" x="2" xlink:href="#terminal" y="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="33" y1="11" y2="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="18" y2="4"/>
</symbol>
<symbol id="Disconnector:bn_刀闸21_0" viewBox="0,0,40,40">
 <use Plane="0" x="21" xlink:href="#terminal" y="6"/>
 <use Plane="0" x="21" xlink:href="#terminal" y="33"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="16" x2="26" y1="11" y2="30"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="15" x2="15" y1="9" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="16" x2="26" y1="32" y2="32"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="14" x2="5" y1="20" y2="20"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="5" x2="5" y1="20" y2="26"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="1" x2="9" y1="26" y2="26"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="8" y1="29" y2="29"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="7" y1="32" y2="32"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="26" x2="16" y1="8" y2="8"/>
</symbol>
<symbol id="Disconnector:bn_刀闸21_1" viewBox="0,0,40,40">
 <use Plane="0" x="21" xlink:href="#terminal" y="6"/>
 <use Plane="0" x="21" xlink:href="#terminal" y="33"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="16" x2="26" y1="8" y2="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="16" x2="26" y1="32" y2="32"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="14" x2="14" y1="31" y2="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="21" x2="21" y1="32" y2="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="14" x2="5" y1="19" y2="19"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="5" x2="5" y1="19" y2="24"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="1" x2="9" y1="25" y2="25"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="8" y1="28" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="7" y1="31" y2="31"/>
</symbol>
<symbol id="PT:bn_电压互感器004_0" viewBox="0,0,100,100">
 <use Plane="0" x="33" xlink:href="#terminal" y="97"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="28" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,75,51)" width="12" x="69" y="37"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="21" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,33,67)" width="10" x="28" y="57"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="33" y1="17" y2="24"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="28" y1="24" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="38" y1="24" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="38" y1="40" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="28" y1="40" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="33" y1="33" y2="40"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="49" x2="49" y1="33" y2="40"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="49" x2="44" y1="40" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="49" x2="54" y1="40" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="52" x2="52" y1="17" y2="30"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="52" x2="45" y1="30" y2="27"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="52" x2="45" y1="17" y2="21"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="5" x2="15" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="7" x2="13" y1="10" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="11" y1="8" y2="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="10" x2="10" y1="40" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="10" x2="22" y1="40" y2="40"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="70" x2="80" y1="32" y2="32"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="72" x2="78" y1="30" y2="30"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="74" x2="76" y1="28" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="75" x2="75" y1="32" y2="37"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="75" x2="75" y1="65" y2="62"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="33" y1="51" y2="97"/>
 <path AFMask="2147483647" Plane="0" d="M 72 61 L 75 44 L 78 61 Z" fill="rgb(0,0,255)" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="33" cy="24" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="33" cy="40" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="49" cy="40" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="49" cy="24" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="0" d="M 75 66 L 75 84 L 33 84 " fill="none" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="PT:bn_电压互感器008_0" viewBox="0,0,18,44">
 <use Plane="0" x="8" xlink:href="#terminal" y="40"/>
 <circle AFMask="2147483647" Plane="0" cx="8" cy="21" fill="none" r="8" stroke="rgb(255,255,254)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="8" cy="10" fill="none" r="8" stroke="rgb(255,255,254)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="9" y1="29" y2="39"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="9" y1="4" y2="7"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="5" y1="8" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="13" y1="8" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="9" y1="4" y2="4"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="9" y1="19" y2="22"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="5" y1="22" y2="26"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="13" y1="22" y2="26"/>
</symbol>
<symbol id="PT:bn_电压互感器46_0" viewBox="0,0,18,44">
 <use Plane="0" x="9" xlink:href="#terminal" y="40"/>
 <circle AFMask="2147483647" Plane="0" cx="9" cy="21" fill="none" r="8" stroke="rgb(255,255,254)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="9" cy="10" fill="none" r="8" stroke="rgb(255,255,254)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="9" y1="29" y2="39"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="7" x2="13" y1="10" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="7" x2="13" y1="21" y2="21"/>
</symbol>
<symbol id="Breaker:bn_断路器2_0" viewBox="0,0,20,40">
 <use Plane="0" x="10" xlink:href="#terminal" y="4"/>
 <use Plane="0" x="10" xlink:href="#terminal" y="36"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="30" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,10,20)" width="14" x="3" y="5"/>
</symbol>
<symbol id="Breaker:bn_断路器2_1" viewBox="0,0,20,40">
 <use Plane="0" x="10" xlink:href="#terminal" y="4"/>
 <use Plane="0" x="10" xlink:href="#terminal" y="36"/>
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,254)" height="30" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,10,20)" width="14" x="3" y="5"/>
</symbol>
<symbol id="Breaker:bn_断路器3_0" viewBox="0,0,38,18">
 <use Plane="0" x="3" xlink:href="#terminal" y="7"/>
 <use Plane="0" x="33" xlink:href="#terminal" y="7"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="15" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,18,8)" width="30" x="3" y="1"/>
</symbol>
<symbol id="Breaker:bn_断路器3_1" viewBox="0,0,38,18">
 <use Plane="0" x="3" xlink:href="#terminal" y="7"/>
 <use Plane="0" x="33" xlink:href="#terminal" y="7"/>
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,254)" height="15" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,18,8)" width="30" x="3" y="1"/>
</symbol>
<symbol id="SynchronousMachine:bn_发电机1_0" viewBox="0,0,40,40">
 <use Plane="0" x="19" xlink:href="#terminal" y="3"/>
 <use Plane="0" x="19" xlink:href="#terminal" y="36"/>
 <circle AFMask="2147483647" Plane="0" cx="19" cy="20" fill="none" r="15" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="0" d="M 19 19 A 4 4 0 1 0 11 19" fill="none" stroke="rgb(0,0,255)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="0" d="M 20 19 A 4 4 0 1 0 28 19" fill="none" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Status:bn_非实测颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,85,0)" height="1" stroke="rgb(170,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_非实测颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,85,0)" height="1" stroke="rgb(170,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_工况退出颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,170,255)" height="1" stroke="rgb(170,170,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_工况退出颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,170,255)" height="1" stroke="rgb(170,170,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸1_0" viewBox="0,0,16,32">
 <use Plane="0" x="8" xlink:href="#terminal" y="2"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="4" y2="6"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="13" y1="27" y2="27"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="4" x2="12" y1="29" y2="29"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="6" x2="10" y1="31" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="27" y2="23"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="15" y1="22" y2="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="13" y1="6" y2="6"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸1_1" viewBox="0,0,16,32">
 <use Plane="0" x="8" xlink:href="#terminal" y="2"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="27" y2="4"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="6" x2="10" y1="31" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="4" x2="12" y1="29" y2="29"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="13" y1="27" y2="27"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="13" y1="6" y2="6"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸15_0" viewBox="0,0,16,32">
 <use Plane="0" x="8" xlink:href="#terminal" y="29"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="26" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="13" y1="5" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="4" x2="12" y1="3" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="6" x2="10" y1="1" y2="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="9" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="1" x2="8" y1="22" y2="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="13" y1="25" y2="25"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸15_1" viewBox="0,0,16,32">
 <use Plane="0" x="8" xlink:href="#terminal" y="29"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="29" y2="6"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="6" x2="10" y1="1" y2="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="4" x2="12" y1="3" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="13" y1="25" y2="25"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="13" y1="5" y2="5"/>
</symbol>
<symbol id="Transformer2:bn_两卷变压器1_0" viewBox="0,0,40,80">
 <use Plane="0" x="20" xlink:href="#terminal" y="3"/>
 <circle AFMask="2147483647" Plane="0" cx="19" cy="21" fill="none" r="18" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="20" x2="27" y1="53" y2="45"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="20" x2="12" y1="53" y2="46"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="20" x2="20" y1="53" y2="63"/>
</symbol>
<symbol id="Transformer2:bn_两卷变压器1_1" viewBox="0,0,40,80">
 <use Plane="1" x="20" xlink:href="#terminal" y="72"/>
 <circle AFMask="2147483647" Plane="1" cx="19" cy="53" fill="none" r="18" stroke="rgb(0,0,255)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 19 27 L 28 14 L 9 14 Z" fill="none" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Status:bn_数据封锁颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,85,0)" height="1" stroke="rgb(255,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_数据封锁颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,85,0)" height="1" stroke="rgb(255,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_0_0" viewBox="0,0,20,74">
 <rect AFMask="2147483647" Plane="0" fill="none" height="0" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,4,16)" width="0" x="4" y="16"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="30" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,9,37)" width="13" x="3" y="22"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_0_1" viewBox="0,0,20,74">
 <rect AFMask="2147483647" Plane="0" fill="none" height="30" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,9,36)" width="13" x="3" y="21"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_0_2" viewBox="0,0,20,74">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,254)" height="30" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,9,36)" width="13" x="3" y="21"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_0_3" viewBox="0,0,20,74">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,254)" height="30" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,9,37)" width="13" x="3" y="22"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_1_0" viewBox="0,0,20,74">
 <use Plane="1" x="9" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="9" xlink:href="#terminal" y="69"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="21" y2="10"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="52" y2="63"/>
 <path AFMask="2147483647" Plane="1" d="M 3 11 L 10 5 L 17 12 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 3 57 L 9 64 L 16 57 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 3 15 L 10 9 L 17 16 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 3 61 L 9 68 L 16 61 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_1_1" viewBox="0,0,20,74">
 <use Plane="1" x="9" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="9" xlink:href="#terminal" y="69"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="21" y2="9"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="52" y2="65"/>
 <path AFMask="2147483647" Plane="1" d="M 4 12 L 10 6 L 16 12 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
 <path AFMask="2147483647" Plane="1" d="M 4 61 L 10 67 L 16 61 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_1_2" viewBox="0,0,20,74">
 <use Plane="1" x="9" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="9" xlink:href="#terminal" y="69"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="22" y2="10"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="52" y2="63"/>
 <path AFMask="2147483647" Plane="1" d="M 3 11 L 9 4 L 16 11 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 3 15 L 9 8 L 16 15 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 2 57 L 9 63 L 16 56 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 2 61 L 9 67 L 16 60 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_1_3" viewBox="0,0,20,74">
 <use Plane="1" x="9" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="9" xlink:href="#terminal" y="69"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="21" y2="8"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="52" y2="65"/>
 <path AFMask="2147483647" Plane="1" d="M 3 11 L 9 5 L 15 11 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
 <path AFMask="2147483647" Plane="1" d="M 3 60 L 9 66 L 15 60 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关7_0_0" viewBox="0,0,20,70">
 <rect AFMask="2147483647" Plane="0" fill="none" height="0" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,4,14)" width="0" x="4" y="14"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关7_0_1" viewBox="0,0,20,70">
 <rect AFMask="2147483647" Plane="0" fill="none" height="29" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,10,34)" width="12" x="4" y="20"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关7_0_2" viewBox="0,0,20,70">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,254)" height="27" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,10,34)" width="12" x="4" y="21"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关7_0_3" viewBox="0,0,20,70">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,254)" height="27" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,10,34)" width="12" x="4" y="21"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关7_1_0" viewBox="0,0,20,70">
 <use Plane="1" x="10" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="10" xlink:href="#terminal" y="66"/>
 <path AFMask="2147483647" Plane="1" d="M 4 17 L 9 11 L 16 17 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="63" y2="12"/>
 <path AFMask="2147483647" Plane="1" d="M 4 11 L 9 5 L 16 11 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关7_1_1" viewBox="0,0,20,70">
 <use Plane="1" x="10" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="10" xlink:href="#terminal" y="66"/>
 <path AFMask="2147483647" Plane="1" d="M 5 11 L 10 6 L 15 11 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="19" y2="8"/>
 <path AFMask="2147483647" Plane="1" d="M 5 57 L 10 62 L 15 57 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="49" y2="60"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关7_1_2" viewBox="0,0,20,70">
 <use Plane="1" x="10" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="10" xlink:href="#terminal" y="66"/>
 <path AFMask="2147483647" Plane="1" d="M 4 16 L 9 11 L 16 17 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="20" y2="12"/>
 <path AFMask="2147483647" Plane="1" d="M 4 11 L 9 5 L 16 11 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 4 51 L 9 57 L 16 51 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 4 57 L 9 63 L 16 57 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="49" y2="57"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关7_1_3" viewBox="0,0,20,70">
 <use Plane="1" x="10" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="10" xlink:href="#terminal" y="66"/>
 <path AFMask="2147483647" Plane="1" d="M 5 11 L 10 6 L 15 11 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="20" y2="9"/>
 <path AFMask="2147483647" Plane="1" d="M 5 57 L 10 62 L 15 57 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="49" y2="60"/>
</symbol>
<symbol id="Status:bn_越限颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_越限颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="GZP:gg_光子牌1_0" viewBox="0,0,36,36">
 <circle AFMask="2147483647" Plane="0" cx="18" cy="18" fill="rgb(0,255,0)" r="15" stroke="rgb(0,255,0)" stroke-width="1"/>
</symbol>
<symbol id="GZP:gg_光子牌1_1" viewBox="0,0,36,36">
 <circle AFMask="2147483647" Plane="0" cx="18" cy="18" fill="rgb(255,0,0)" r="15" stroke="rgb(255,0,0)" stroke-width="1"/>
</symbol>
<symbol id="PT:葫芦岛电压互感器_0" viewBox="0,0,100,100">
 <use Plane="0" x="35" xlink:href="#terminal" y="94"/>
 <path AFMask="2147483647" Plane="0" d="M 77 73 L 77 89 L 35 89 " fill="none" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="36" x2="36" y1="68" y2="93"/>
 <polygon AFMask="2147483647" Plane="0" fill="rgb(0,0,255)" points="75,69 78,52 81,69" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="78" x2="78" y1="73" y2="70"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="78" x2="78" y1="40" y2="45"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="77" x2="79" y1="36" y2="36"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="75" x2="81" y1="38" y2="38"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="73" x2="83" y1="40" y2="40"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="28" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,78,59)" width="12" x="72" y="45"/>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="57" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="36" x2="40" y1="41" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="36" x2="32" y1="41" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="36" x2="36" y1="36" y2="41"/>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="41" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="36" x2="62" y1="40" y2="40"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="62" x2="62" y1="37" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="64" x2="64" y1="38" y2="43"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="66" x2="66" y1="39" y2="42"/>
 <polygon AFMask="2147483647" Plane="0" fill="none" points="34,14 36,7 38,14" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="36" x2="36" y1="30" y2="14"/>
 <polygon AFMask="2147483647" Plane="0" fill="none" points="32,61 36,53 40,61" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="55" x2="55" y1="40" y2="22"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="36" x2="55" y1="22" y2="22"/>
</symbol>
<symbol id="PT:葫芦岛开关站电压互感器_0" viewBox="0,0,100,100">
 <use Plane="0" x="26" xlink:href="#terminal" y="96"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="25" x2="31" y1="23" y2="29"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="25" y1="17" y2="23"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="61" x2="54" y1="43" y2="43"/>
 <path AFMask="2147483647" Plane="0" d="M 76 57 L 76 83 L 27 83 " fill="none" stroke="rgb(0,0,255)" stroke-width="1"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="21" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,27,67)" width="10" x="22" y="57"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="27" x2="27" y1="51" y2="97"/>
 <polygon AFMask="2147483647" Plane="0" fill="rgb(0,0,255)" points="73,56 76,39 79,56" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="76" x2="76" y1="27" y2="32"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="75" x2="77" y1="23" y2="23"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="73" x2="79" y1="25" y2="25"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="71" x2="81" y1="27" y2="27"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="28" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,76,46)" width="12" x="70" y="32"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="61" x2="61" y1="43" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="60" x2="62" y1="23" y2="23"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="59" x2="63" y1="25" y2="25"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="58" x2="64" y1="27" y2="27"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="31" y1="17" y2="28"/>
 <circle AFMask="2147483647" Plane="0" cx="28" cy="24" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="43" x2="48" y1="40" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="43" x2="38" y1="40" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="43" x2="43" y1="33" y2="40"/>
 <circle AFMask="2147483647" Plane="0" cx="43" cy="40" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="27" cy="40" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="27" x2="27" y1="33" y2="40"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="27" x2="22" y1="40" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="27" x2="32" y1="40" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="43" x2="48" y1="24" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="43" x2="38" y1="24" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="43" x2="43" y1="17" y2="24"/>
 <circle AFMask="2147483647" Plane="0" cx="43" cy="24" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="14" cy="31" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="14" x2="14" y1="24" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="14" x2="9" y1="31" y2="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="14" x2="19" y1="31" y2="35"/>
</symbol>
<symbol id="GroundDisconnector:葫芦岛开关站接地刀闸_0" viewBox="0,0,60,60">
 <use Plane="0" x="35" xlink:href="#terminal" y="46"/>
 <use Plane="0" x="35" xlink:href="#terminal" y="6"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="41" x2="31" y1="21" y2="21"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="18" x2="22" y1="45" y2="45"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="17" x2="23" y1="42" y2="42"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="16" x2="24" y1="39" y2="39"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="20" x2="20" y1="33" y2="39"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="29" x2="20" y1="33" y2="33"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="41" y1="45" y2="45"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="30" x2="30" y1="22" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="41" y1="24" y2="43"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="36" x2="36" y1="20" y2="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="36" x2="32" y1="15" y2="15"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="32" x2="27" y1="15" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="20" x2="20" y1="33" y2="15"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="20" x2="25" y1="15" y2="15"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="25" x2="25" y1="15" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="25" x2="25" y1="15" y2="16"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="-1" InitShowingPlane="0," fill="rgb(0,0,0)" height="1800" width="3850" x="0" y="0"/>
</g>
<g id="Other_Layer">
 
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="28" x2="28" y1="17" y2="1780"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="559" x2="559" y1="17" y2="1780"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="31" x2="556" y1="198" y2="198"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="33" x2="559" y1="472" y2="472"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="30" x2="557" y1="1780" y2="1780"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="33" x2="558" y1="770" y2="770"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="33" x2="559" y1="870" y2="870"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="33" x2="557" y1="968" y2="968"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="33" x2="558" y1="1058" y2="1058"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="28" x2="557" y1="299" y2="299"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="28" x2="557" y1="377" y2="377"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="29" x2="558" y1="17" y2="17"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="145" x2="145" y1="472" y2="770"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="145" x2="558" y1="576" y2="576"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="244" x2="244" y1="772" y2="962"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="145" x2="559" y1="669" y2="669"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="446" x2="446" y1="300" y2="470"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="343" x2="343" y1="302" y2="470"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="241" x2="241" y1="303" y2="470"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="138" x2="138" y1="301" y2="470"/>
</g>
<g id="Bus_Layer">
 <g id="30000000">
  <path d="M 782 748 L 3613 748" stroke="rgb(0,0,255)" stroke-width="4"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 782 748 L 3613 748" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="30000535">
  <path d="M 780 1220 L 3619 1220" stroke="rgb(0,0,255)" stroke-width="4"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 780 1220 L 3619 1220" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="Breaker_Layer">
 <g id="100000015">
  <use class="kv-1" height="40" transform="rotate(0,1263,599) scale(1.5,1.5) translate(-431,-219.667)" width="20" x="1263" xlink:href="#Breaker:bn_断路器2_0" y="599"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1263,599) scale(1.5,1.5) translate(-431,-219.667)" width="20" x="1263" y="599"/></g>
 <g id="100000018">
  <use class="kv-1" height="40" transform="rotate(0,1003,854) scale(1.5,1.5) translate(-344.333,-304.667)" width="20" x="1003" xlink:href="#Breaker:bn_断路器2_0" y="854"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1003,854) scale(1.5,1.5) translate(-344.333,-304.667)" width="20" x="1003" y="854"/></g>
 <g id="100000084">
  <use class="kv-1" height="40" transform="rotate(0,1536,595) scale(1.5,1.5) translate(-522,-218.333)" width="20" x="1536" xlink:href="#Breaker:bn_断路器2_0" y="595"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1536,595) scale(1.5,1.5) translate(-522,-218.333)" width="20" x="1536" y="595"/></g>
 <g id="100000096">
  <use class="kv-1" height="18" transform="rotate(0,2120,115) scale(1.5,1.5) translate(-724.667,-46.3333)" width="38" x="2120" xlink:href="#Breaker:bn_断路器3_0" y="115"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,2120,115) scale(1.5,1.5) translate(-724.667,-46.3333)" width="38" x="2120" y="115"/></g>
 <g id="100000604">
  <use class="kv-1" height="40" transform="rotate(0,1797,595) scale(1.5,1.5) translate(-609,-218.333)" width="20" x="1797" xlink:href="#Breaker:bn_断路器2_0" y="595"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1797,595) scale(1.5,1.5) translate(-609,-218.333)" width="20" x="1797" y="595"/></g>
 <g id="100000697">
  <use class="kv-1" height="40" transform="rotate(0,2066,595) scale(1.5,1.5) translate(-698.667,-218.333)" width="20" x="2066" xlink:href="#Breaker:bn_断路器2_0" y="595"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,2066,595) scale(1.5,1.5) translate(-698.667,-218.333)" width="20" x="2066" y="595"/></g>
 <g id="100000714">
  <use class="kv-1" height="40" transform="rotate(0,2336,595) scale(1.5,1.5) translate(-788.667,-218.333)" width="20" x="2336" xlink:href="#Breaker:bn_断路器2_0" y="595"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,2336,595) scale(1.5,1.5) translate(-788.667,-218.333)" width="20" x="2336" y="595"/></g>
 <g id="100000731">
  <use class="kv-1" height="40" transform="rotate(0,2606,595) scale(1.5,1.5) translate(-878.667,-218.333)" width="20" x="2606" xlink:href="#Breaker:bn_断路器2_0" y="595"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,2606,595) scale(1.5,1.5) translate(-878.667,-218.333)" width="20" x="2606" y="595"/></g>
 <g id="100000748">
  <use class="kv-1" height="40" transform="rotate(0,2875,595) scale(1.5,1.5) translate(-968.333,-218.333)" width="20" x="2875" xlink:href="#Breaker:bn_断路器2_0" y="595"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,2875,595) scale(1.5,1.5) translate(-968.333,-218.333)" width="20" x="2875" y="595"/></g>
 <g id="100000765">
  <use class="kv-1" height="40" transform="rotate(0,3142,595) scale(1.5,1.5) translate(-1057.33,-218.333)" width="20" x="3142" xlink:href="#Breaker:bn_断路器2_0" y="595"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,3142,595) scale(1.5,1.5) translate(-1057.33,-218.333)" width="20" x="3142" y="595"/></g>
 <g id="100000799">
  <use class="kv-1" height="40" transform="rotate(0,3411,595) scale(1.5,1.5) translate(-1147,-218.333)" width="20" x="3411" xlink:href="#Breaker:bn_断路器2_0" y="595"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,3411,595) scale(1.5,1.5) translate(-1147,-218.333)" width="20" x="3411" y="595"/></g>
</g>
<g id="Disconnector_Layer">
 <g id="101000102">
  <use class="kv-1" height="20" transform="rotate(0,2373,116) scale(1.5,1.5) translate(-809,-49.6667)" width="40" x="2373" xlink:href="#Disconnector:bn_刀闸1_0" y="116"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,2373,116) scale(1.5,1.5) translate(-809,-49.6667)" width="40" x="2373" y="116"/></g>
 <g id="101000406">
  <use class="kv-1" height="40" transform="rotate(0,1004,788) scale(1.5,1.5) translate(-355.667,-282.667)" width="40" x="1004" xlink:href="#Disconnector:bn_刀闸21_0" y="788"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1004,788) scale(1.5,1.5) translate(-355.667,-282.667)" width="40" x="1004" y="788"/></g>
 <g id="101000408">
  <use class="kv-1" height="40" transform="rotate(0,1004,917) scale(1.5,1.5) translate(-355.667,-325.667)" width="40" x="1004" xlink:href="#Disconnector:bn_刀闸21_0" y="917"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1004,917) scale(1.5,1.5) translate(-355.667,-325.667)" width="40" x="1004" y="917"/></g>
 <g id="101000605">
  <use class="kv-1" height="40" transform="rotate(0,1798,668) scale(1.5,1.5) translate(-620.333,-242.667)" width="40" x="1798" xlink:href="#Disconnector:bn_刀闸21_0" y="668"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1798,668) scale(1.5,1.5) translate(-620.333,-242.667)" width="40" x="1798" y="668"/></g>
 <g id="101000698">
  <use class="kv-1" height="40" transform="rotate(0,2067,668) scale(1.5,1.5) translate(-710,-242.667)" width="40" x="2067" xlink:href="#Disconnector:bn_刀闸21_0" y="668"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,2067,668) scale(1.5,1.5) translate(-710,-242.667)" width="40" x="2067" y="668"/></g>
 <g id="101000715">
  <use class="kv-1" height="40" transform="rotate(0,2337,665) scale(1.5,1.5) translate(-800,-241.667)" width="40" x="2337" xlink:href="#Disconnector:bn_刀闸21_0" y="665"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,2337,665) scale(1.5,1.5) translate(-800,-241.667)" width="40" x="2337" y="665"/></g>
 <g id="101000732">
  <use class="kv-1" height="40" transform="rotate(0,2607,668) scale(1.5,1.5) translate(-890,-242.667)" width="40" x="2607" xlink:href="#Disconnector:bn_刀闸21_0" y="668"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,2607,668) scale(1.5,1.5) translate(-890,-242.667)" width="40" x="2607" y="668"/></g>
 <g id="101000749">
  <use class="kv-1" height="40" transform="rotate(0,2876,669) scale(1.5,1.5) translate(-979.667,-243)" width="40" x="2876" xlink:href="#Disconnector:bn_刀闸21_0" y="669"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,2876,669) scale(1.5,1.5) translate(-979.667,-243)" width="40" x="2876" y="669"/></g>
 <g id="101000766">
  <use class="kv-1" height="40" transform="rotate(0,3143,668) scale(1.5,1.5) translate(-1068.67,-242.667)" width="40" x="3143" xlink:href="#Disconnector:bn_刀闸21_0" y="668"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,3143,668) scale(1.5,1.5) translate(-1068.67,-242.667)" width="40" x="3143" y="668"/></g>
 <g id="101000800">
  <use class="kv-1" height="40" transform="rotate(0,3412,668) scale(1.5,1.5) translate(-1158.33,-242.667)" width="40" x="3412" xlink:href="#Disconnector:bn_刀闸21_0" y="668"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,3412,668) scale(1.5,1.5) translate(-1158.33,-242.667)" width="40" x="3412" y="668"/></g>
 <g id="101001475">
  <use class="kv-1" height="40" transform="rotate(0,1263,680) scale(1.5,1.5) translate(-442,-246.667)" width="40" x="1263" xlink:href="#Disconnector:bn_刀闸21_0" y="680"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1263,680) scale(1.5,1.5) translate(-442,-246.667)" width="40" x="1263" y="680"/></g>
 <g id="101001477">
  <use class="kv-1" height="40" transform="rotate(0,1536,663) scale(1.5,1.5) translate(-533,-241)" width="40" x="1536" xlink:href="#Disconnector:bn_刀闸21_0" y="663"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1536,663) scale(1.5,1.5) translate(-533,-241)" width="40" x="1536" y="663"/></g>
 <g id="101001480">
  <use class="kv-1" height="40" transform="rotate(0,1263,519) scale(1.5,1.5) translate(-442,-193)" width="40" x="1263" xlink:href="#Disconnector:bn_刀闸21_0" y="519"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1263,519) scale(1.5,1.5) translate(-442,-193)" width="40" x="1263" y="519"/></g>
 <g id="101001488">
  <use class="kv-1" height="40" transform="rotate(0,1536,519) scale(1.5,1.5) translate(-533,-193)" width="40" x="1536" xlink:href="#Disconnector:bn_刀闸21_0" y="519"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1536,519) scale(1.5,1.5) translate(-533,-193)" width="40" x="1536" y="519"/></g>
</g>
<g id="GroundDisconnector_Layer">
 <g id="111000093">
  <use class="kv-1" height="32" transform="rotate(180,1919,97) scale(1.5,1.5) translate(-647.667,-34.3333)" width="16" x="1919" xlink:href="#GroundDisconnector:bn_接地刀闸1_0" y="97"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(180,1919,97) scale(1.5,1.5) translate(-647.667,-34.3333)" width="16" x="1919" y="97"/></g>
 <g id="111000606">
  <use class="kv-1" height="32" transform="rotate(0,1763,449) scale(1.5,1.5) translate(-595.667,-178.667)" width="16" x="1763" xlink:href="#GroundDisconnector:bn_接地刀闸15_0" y="449"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(0,1763,449) scale(1.5,1.5) translate(-595.667,-178.667)" width="16" x="1763" y="449"/></g>
 <g id="111000699">
  <use class="kv-1" height="32" transform="rotate(0,2032,449) scale(1.5,1.5) translate(-685.333,-178.667)" width="16" x="2032" xlink:href="#GroundDisconnector:bn_接地刀闸15_0" y="449"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(0,2032,449) scale(1.5,1.5) translate(-685.333,-178.667)" width="16" x="2032" y="449"/></g>
 <g id="111000716">
  <use class="kv-1" height="32" transform="rotate(0,2302,449) scale(1.5,1.5) translate(-775.333,-178.667)" width="16" x="2302" xlink:href="#GroundDisconnector:bn_接地刀闸15_0" y="449"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(0,2302,449) scale(1.5,1.5) translate(-775.333,-178.667)" width="16" x="2302" y="449"/></g>
 <g id="111000733">
  <use class="kv-1" height="32" transform="rotate(0,2572,449) scale(1.5,1.5) translate(-865.333,-178.667)" width="16" x="2572" xlink:href="#GroundDisconnector:bn_接地刀闸15_0" y="449"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(0,2572,449) scale(1.5,1.5) translate(-865.333,-178.667)" width="16" x="2572" y="449"/></g>
 <g id="111000750">
  <use class="kv-1" height="32" transform="rotate(0,2841,449) scale(1.5,1.5) translate(-955,-178.667)" width="16" x="2841" xlink:href="#GroundDisconnector:bn_接地刀闸15_0" y="449"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(0,2841,449) scale(1.5,1.5) translate(-955,-178.667)" width="16" x="2841" y="449"/></g>
 <g id="111000767">
  <use class="kv-1" height="32" transform="rotate(0,3108,449) scale(1.5,1.5) translate(-1044,-178.667)" width="16" x="3108" xlink:href="#GroundDisconnector:bn_接地刀闸15_0" y="449"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(0,3108,449) scale(1.5,1.5) translate(-1044,-178.667)" width="16" x="3108" y="449"/></g>
 <g id="111000801">
  <use class="kv-1" height="32" transform="rotate(0,3377,449) scale(1.5,1.5) translate(-1133.67,-178.667)" width="16" x="3377" xlink:href="#GroundDisconnector:bn_接地刀闸15_0" y="449"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(0,3377,449) scale(1.5,1.5) translate(-1133.67,-178.667)" width="16" x="3377" y="449"/></g>
 <g id="111001438">
  <use class="kv-1" height="60" transform="rotate(0,1002,659) scale(1.5,1.5) translate(-370,-246.667)" width="60" x="1002" xlink:href="#GroundDisconnector:葫芦岛开关站接地刀闸_0" y="659"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1002,659) scale(1.5,1.5) translate(-370,-246.667)" width="60" x="1002" y="659"/></g>
</g>
<g id="Generator_Layer">
 <g id="104000865">
  <use class="kv-1" height="40" transform="rotate(0,1005,1584) scale(1.5,1.5) translate(-354,-548)" width="40" x="1005" xlink:href="#SynchronousMachine:bn_发电机1_0" y="1584"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1005,1584) scale(1.5,1.5) translate(-354,-548)" width="40" x="1005" y="1584"/></g>
 <g id="104000901">
  <use class="kv-1" height="40" transform="rotate(0,1714,1579) scale(1.5,1.5) translate(-590.333,-546.333)" width="40" x="1714" xlink:href="#SynchronousMachine:bn_发电机1_0" y="1579"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1714,1579) scale(1.5,1.5) translate(-590.333,-546.333)" width="40" x="1714" y="1579"/></g>
 <g id="104000916">
  <use class="kv-1" height="40" transform="rotate(0,2427,1574) scale(1.5,1.5) translate(-828,-544.667)" width="40" x="2427" xlink:href="#SynchronousMachine:bn_发电机1_0" y="1574"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,2427,1574) scale(1.5,1.5) translate(-828,-544.667)" width="40" x="2427" y="1574"/></g>
</g>
<g id="Transformer2_Layer">
<g id="102000439">
 <g id="1020004390">
  <use class="kv-1" height="80" transform="rotate(0,1003,1017) scale(1.5,1.5) translate(-354.333,-377)" width="40" x="1003" xlink:href="#Transformer2:bn_两卷变压器1_0" y="1017"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="1020004391">
  <use class="kv-1" height="80" transform="rotate(0,1003,1017) scale(1.5,1.5) translate(-354.333,-377)" width="40" x="1003" xlink:href="#Transformer2:bn_两卷变压器1_1" y="1017"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <metadata>
  <cge:PSR_Ref ObjectID="-1" ObjectName="-1" Plane="0"/>
 </metadata>
<rect fill="white" height="80" opacity="0" stroke="white" transform="rotate(0,1003,1017) scale(1.5,1.5) translate(-354.333,-377)" width="40" x="1003" y="1017"/></g>
</g>
<g id="DollyBreaker_Layer">
 <g id="110000448">
  <use class="kv-1" height="74" transform="rotate(0,1003,1154) scale(1.5,1.5) translate(-344.333,-421.667)" width="20" x="1003" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="1154"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1003,1154) scale(1.5,1.5) translate(-344.333,-421.667)" width="20" x="1003" y="1154"/></g>
 <g id="110000448">
  <use class="kv-1" height="74" transform="rotate(0,1003,1154) scale(1.5,1.5) translate(-344.333,-421.667)" width="20" x="1003" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="1154"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1003,1154) scale(1.5,1.5) translate(-344.333,-421.667)" width="20" x="1003" y="1154"/></g>
 <g id="110000855">
  <use class="kv-1" height="74" transform="rotate(0,1003,1306) scale(1.5,1.5) translate(-344.333,-472.333)" width="20" x="1003" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="1306"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1003,1306) scale(1.5,1.5) translate(-344.333,-472.333)" width="20" x="1003" y="1306"/></g>
 <g id="110000855">
  <use class="kv-1" height="74" transform="rotate(0,1003,1306) scale(1.5,1.5) translate(-344.333,-472.333)" width="20" x="1003" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="1306"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1003,1306) scale(1.5,1.5) translate(-344.333,-472.333)" width="20" x="1003" y="1306"/></g>
 <g id="110000862">
  <use class="kv-1" height="70" transform="rotate(0,880,1421) scale(1.5,1.5) translate(-303.333,-508.667)" width="20" x="880" xlink:href="#DollyBreaker:bn_小车开关7_0_0" y="1421"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,880,1421) scale(1.5,1.5) translate(-303.333,-508.667)" width="20" x="880" y="1421"/></g>
 <g id="110000862">
  <use class="kv-1" height="70" transform="rotate(0,880,1421) scale(1.5,1.5) translate(-303.333,-508.667)" width="20" x="880" xlink:href="#DollyBreaker:bn_小车开关7_1_0" y="1421"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,880,1421) scale(1.5,1.5) translate(-303.333,-508.667)" width="20" x="880" y="1421"/></g>
 <g id="110000903">
  <use class="kv-1" height="74" transform="rotate(0,1714,1301) scale(1.5,1.5) translate(-581.333,-470.667)" width="20" x="1714" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="1301"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1714,1301) scale(1.5,1.5) translate(-581.333,-470.667)" width="20" x="1714" y="1301"/></g>
 <g id="110000903">
  <use class="kv-1" height="74" transform="rotate(0,1714,1301) scale(1.5,1.5) translate(-581.333,-470.667)" width="20" x="1714" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="1301"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1714,1301) scale(1.5,1.5) translate(-581.333,-470.667)" width="20" x="1714" y="1301"/></g>
 <g id="110000918">
  <use class="kv-1" height="74" transform="rotate(0,2427,1296) scale(1.5,1.5) translate(-819,-469)" width="20" x="2427" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="1296"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,2427,1296) scale(1.5,1.5) translate(-819,-469)" width="20" x="2427" y="1296"/></g>
 <g id="110000918">
  <use class="kv-1" height="74" transform="rotate(0,2427,1296) scale(1.5,1.5) translate(-819,-469)" width="20" x="2427" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="1296"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,2427,1296) scale(1.5,1.5) translate(-819,-469)" width="20" x="2427" y="1296"/></g>
 <g id="110000936">
  <use class="kv-1" height="74" transform="rotate(0,3132,1326) scale(1.5,1.5) translate(-1054,-479)" width="20" x="3132" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="1326"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,3132,1326) scale(1.5,1.5) translate(-1054,-479)" width="20" x="3132" y="1326"/></g>
 <g id="110000936">
  <use class="kv-1" height="74" transform="rotate(0,3132,1326) scale(1.5,1.5) translate(-1054,-479)" width="20" x="3132" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="1326"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,3132,1326) scale(1.5,1.5) translate(-1054,-479)" width="20" x="3132" y="1326"/></g>
 <g id="110000938">
  <use class="kv-1" height="74" transform="rotate(0,1714,1140) scale(1.5,1.5) translate(-581.333,-417)" width="20" x="1714" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="1140"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1714,1140) scale(1.5,1.5) translate(-581.333,-417)" width="20" x="1714" y="1140"/></g>
 <g id="110000938">
  <use class="kv-1" height="74" transform="rotate(0,1714,1140) scale(1.5,1.5) translate(-581.333,-417)" width="20" x="1714" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="1140"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1714,1140) scale(1.5,1.5) translate(-581.333,-417)" width="20" x="1714" y="1140"/></g>
 <g id="110000956">
  <use class="kv-1" height="70" transform="rotate(180,2427,1123) scale(1.5,1.5) translate(-819,-409.333)" width="20" x="2427" xlink:href="#DollyBreaker:bn_小车开关7_0_0" y="1123"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(180,2427,1123) scale(1.5,1.5) translate(-819,-409.333)" width="20" x="2427" y="1123"/></g>
 <g id="110000956">
  <use class="kv-1" height="70" transform="rotate(180,2427,1123) scale(1.5,1.5) translate(-819,-409.333)" width="20" x="2427" xlink:href="#DollyBreaker:bn_小车开关7_1_0" y="1123"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(180,2427,1123) scale(1.5,1.5) translate(-819,-409.333)" width="20" x="2427" y="1123"/></g>
 <g id="110001411">
  <use class="kv-1" height="70" transform="rotate(0,1598,1421) scale(1.5,1.5) translate(-542.667,-508.667)" width="20" x="1598" xlink:href="#DollyBreaker:bn_小车开关7_0_0" y="1421"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,1598,1421) scale(1.5,1.5) translate(-542.667,-508.667)" width="20" x="1598" y="1421"/></g>
 <g id="110001411">
  <use class="kv-1" height="70" transform="rotate(0,1598,1421) scale(1.5,1.5) translate(-542.667,-508.667)" width="20" x="1598" xlink:href="#DollyBreaker:bn_小车开关7_1_0" y="1421"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,1598,1421) scale(1.5,1.5) translate(-542.667,-508.667)" width="20" x="1598" y="1421"/></g>
 <g id="110001434">
  <use class="kv-1" height="70" transform="rotate(0,2299,1421) scale(1.5,1.5) translate(-776.333,-508.667)" width="20" x="2299" xlink:href="#DollyBreaker:bn_小车开关7_0_0" y="1421"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,2299,1421) scale(1.5,1.5) translate(-776.333,-508.667)" width="20" x="2299" y="1421"/></g>
 <g id="110001434">
  <use class="kv-1" height="70" transform="rotate(0,2299,1421) scale(1.5,1.5) translate(-776.333,-508.667)" width="20" x="2299" xlink:href="#DollyBreaker:bn_小车开关7_1_0" y="1421"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,2299,1421) scale(1.5,1.5) translate(-776.333,-508.667)" width="20" x="2299" y="1421"/></g>
</g>
<g id="Load_Layer">
 <g id="32000041">
 <path d="M 1263 479 L 1263 218" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 1263 479 L 1263 218" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32000595">
 <path d="M 1797 470 L 1797 300" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 1797 470 L 1797 300" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32000688">
 <path d="M 2066 470 L 2066 300" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 2066 470 L 2066 300" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32000705">
 <path d="M 2336 470 L 2336 297" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 2336 470 L 2336 297" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32000722">
 <path d="M 2606 470 L 2606 295" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 2606 470 L 2606 295" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32000739">
 <path d="M 2875 470 L 2875 300" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 2875 470 L 2875 300" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32000756">
 <path d="M 3142 470 L 3142 302" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 3142 470 L 3142 302" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32000790">
 <path d="M 3411 470 L 3411 302" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 3411 470 L 3411 302" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32000844">
 <path d="M 2395 116 L 3550 116" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:PSR_Link Pin0InfoVect0LinkObjId="101000102_0"/>
  </metadata>
 <path d="M 2395 116 L 3550 116" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32000950">
 <path d="M 1714 1014 L 1714 916" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 1714 1014 L 1714 916" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32001483">
 <path d="M 1263 498 L 1263 471" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:PSR_Link Pin0InfoVect0LinkObjId="101001480_0"/>
  </metadata>
 <path d="M 1263 498 L 1263 471" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="PT_Layer">
 <g id="107000962">
  <use class="kv-1" height="100" transform="rotate(0,2426,1043) scale(1.5,1.5) translate(-841.667,-444.667)" width="100" x="2426" xlink:href="#PT:bn_电压互感器004_0" y="1043"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="100" opacity="0" stroke="white" transform="rotate(0,2426,1043) scale(1.5,1.5) translate(-841.667,-444.667)" width="100" x="2426" y="1043"/></g>
 <g id="107001059">
  <use class="kv-1" height="44" transform="rotate(180,1126,1502) scale(2,2) translate(-572,-792)" width="18" x="1126" xlink:href="#PT:bn_电压互感器008_0" y="1502"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="44" opacity="0" stroke="white" transform="rotate(180,1126,1502) scale(2,2) translate(-572,-792)" width="18" x="1126" y="1502"/></g>
 <g id="107001063">
  <use class="kv-1" height="44" transform="rotate(180,1842,1511) scale(2,2) translate(-930,-796.5)" width="18" x="1842" xlink:href="#PT:bn_电压互感器008_0" y="1511"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="44" opacity="0" stroke="white" transform="rotate(180,1842,1511) scale(2,2) translate(-930,-796.5)" width="18" x="1842" y="1511"/></g>
 <g id="107001065">
  <use class="kv-1" height="44" transform="rotate(180,2549,1514) scale(2,2) translate(-1283.5,-798)" width="18" x="2549" xlink:href="#PT:bn_电压互感器008_0" y="1514"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="44" opacity="0" stroke="white" transform="rotate(180,2549,1514) scale(2,2) translate(-1283.5,-798)" width="18" x="2549" y="1514"/></g>
 <g id="107001355">
  <use class="kv-1" height="100" transform="rotate(0,1001,267) scale(1.5,1.5) translate(-360.667,-186)" width="100" x="1001" xlink:href="#PT:葫芦岛开关站电压互感器_0" y="267"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="100" opacity="0" stroke="white" transform="rotate(0,1001,267) scale(1.5,1.5) translate(-360.667,-186)" width="100" x="1001" y="267"/></g>
 <g id="107001356">
  <use class="kv-1" height="100" transform="rotate(180,880,1490) scale(1.5,1.5) translate(-320.333,-593.667)" width="100" x="880" xlink:href="#PT:葫芦岛开关站电压互感器_0" y="1490"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="100" opacity="0" stroke="white" transform="rotate(180,880,1490) scale(1.5,1.5) translate(-320.333,-593.667)" width="100" x="880" y="1490"/></g>
 <g id="107001410">
  <use class="kv-1" height="100" transform="rotate(180,1598,1490) scale(1.5,1.5) translate(-559.667,-593.667)" width="100" x="1598" xlink:href="#PT:葫芦岛开关站电压互感器_0" y="1490"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="100" opacity="0" stroke="white" transform="rotate(180,1598,1490) scale(1.5,1.5) translate(-559.667,-593.667)" width="100" x="1598" y="1490"/></g>
 <g id="107001433">
  <use class="kv-1" height="100" transform="rotate(180,2299,1490) scale(1.5,1.5) translate(-793.333,-593.667)" width="100" x="2299" xlink:href="#PT:葫芦岛开关站电压互感器_0" y="1490"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="100" opacity="0" stroke="white" transform="rotate(180,2299,1490) scale(1.5,1.5) translate(-793.333,-593.667)" width="100" x="2299" y="1490"/></g>
 <g id="107001455">
  <use class="kv-1" height="100" transform="rotate(180,3132,1484) scale(1.5,1.5) translate(-1080,-589.667)" width="100" x="3132" xlink:href="#PT:葫芦岛电压互感器_0" y="1484"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="100" opacity="0" stroke="white" transform="rotate(180,3132,1484) scale(1.5,1.5) translate(-1080,-589.667)" width="100" x="3132" y="1484"/></g>
 <g id="107001508">
  <use class="kv-1" height="44" transform="rotate(0,1493,438) scale(1,1) translate(-9,-41)" width="18" x="1493" xlink:href="#PT:bn_电压互感器46_0" y="438"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="44" opacity="0" stroke="white" transform="rotate(0,1493,438) scale(1,1) translate(-9,-41)" width="18" x="1493" y="438"/></g>
 <g id="107001514">
  <use class="kv-1" height="44" transform="rotate(0,1211,442) scale(1,1) translate(-9,-41)" width="18" x="1211" xlink:href="#PT:bn_电压互感器46_0" y="442"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="44" opacity="0" stroke="white" transform="rotate(0,1211,442) scale(1,1) translate(-9,-41)" width="18" x="1211" y="442"/></g>
</g>
<g id="GZP_Layer">
 <g id="135001049">
  <use class="kv-1" height="36" transform="rotate(0,326,1014) scale(1,1) translate(-18,-18)" width="36" x="326" xlink:href="#GZP:gg_光子牌1_0" y="1014"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,326,1014) scale(1,1) translate(-18,-18)" width="36" x="326" y="1014"/></g>
</g>
<g id="Arrester_Layer">
 <g id="133000024">
  <use class="kv-1" height="40" transform="rotate(0,1291,447) scale(1.5,1.5) translate(-440.333,-184)" width="20" x="1291" xlink:href="#Arrester:bn_避雷器4_0" y="447"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1291,447) scale(1.5,1.5) translate(-440.333,-184)" width="20" x="1291" y="447"/></g>
 <g id="133000025">
  <use class="kv-1" height="40" transform="rotate(180,1618,1081) scale(1.5,1.5) translate(-549.333,-395.333)" width="20" x="1618" xlink:href="#Arrester:bn_避雷器4_0" y="1081"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(180,1618,1081) scale(1.5,1.5) translate(-549.333,-395.333)" width="20" x="1618" y="1081"/></g>
 <g id="133000027">
  <use class="kv-1" height="40" transform="rotate(0,1047,449) scale(1.5,1.5) translate(-359,-184.667)" width="20" x="1047" xlink:href="#Arrester:bn_避雷器4_0" y="449"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1047,449) scale(1.5,1.5) translate(-359,-184.667)" width="20" x="1047" y="449"/></g>
 <g id="133000029">
  <use class="kv-1" height="40" transform="rotate(0,1066,1058) scale(1.5,1.5) translate(-365.333,-387.667)" width="20" x="1066" xlink:href="#Arrester:bn_避雷器4_0" y="1058"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1066,1058) scale(1.5,1.5) translate(-365.333,-387.667)" width="20" x="1066" y="1058"/></g>
 <g id="133000088">
  <use class="kv-1" height="40" transform="rotate(0,1565,444) scale(1.5,1.5) translate(-531.667,-183)" width="20" x="1565" xlink:href="#Arrester:bn_避雷器4_0" y="444"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1565,444) scale(1.5,1.5) translate(-531.667,-183)" width="20" x="1565" y="444"/></g>
 <g id="133000090">
  <use class="kv-1" height="40" transform="rotate(0,1586,142) scale(1.5,1.5) translate(-536.667,-52.3333)" width="16" x="1586" xlink:href="#Arrester:bn_避雷器123_0" y="142"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1586,142) scale(1.5,1.5) translate(-536.667,-52.3333)" width="16" x="1586" y="142"/></g>
 <g id="133000409">
  <use class="kv-1" height="40" transform="rotate(0,1066,940) scale(1.5,1.5) translate(-365.333,-348.333)" width="20" x="1066" xlink:href="#Arrester:bn_避雷器4_0" y="940"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1066,940) scale(1.5,1.5) translate(-365.333,-348.333)" width="20" x="1066" y="940"/></g>
 <g id="133000607">
  <use class="kv-1" height="40" transform="rotate(0,1829,449) scale(1.5,1.5) translate(-619.667,-184.667)" width="20" x="1829" xlink:href="#Arrester:bn_避雷器4_0" y="449"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1829,449) scale(1.5,1.5) translate(-619.667,-184.667)" width="20" x="1829" y="449"/></g>
 <g id="133000700">
  <use class="kv-1" height="40" transform="rotate(0,2098,449) scale(1.5,1.5) translate(-709.333,-184.667)" width="20" x="2098" xlink:href="#Arrester:bn_避雷器4_0" y="449"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,2098,449) scale(1.5,1.5) translate(-709.333,-184.667)" width="20" x="2098" y="449"/></g>
 <g id="133000717">
  <use class="kv-1" height="40" transform="rotate(0,2368,449) scale(1.5,1.5) translate(-799.333,-184.667)" width="20" x="2368" xlink:href="#Arrester:bn_避雷器4_0" y="449"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,2368,449) scale(1.5,1.5) translate(-799.333,-184.667)" width="20" x="2368" y="449"/></g>
 <g id="133000734">
  <use class="kv-1" height="40" transform="rotate(0,2638,449) scale(1.5,1.5) translate(-889.333,-184.667)" width="20" x="2638" xlink:href="#Arrester:bn_避雷器4_0" y="449"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,2638,449) scale(1.5,1.5) translate(-889.333,-184.667)" width="20" x="2638" y="449"/></g>
 <g id="133000751">
  <use class="kv-1" height="40" transform="rotate(0,2907,449) scale(1.5,1.5) translate(-979,-184.667)" width="20" x="2907" xlink:href="#Arrester:bn_避雷器4_0" y="449"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,2907,449) scale(1.5,1.5) translate(-979,-184.667)" width="20" x="2907" y="449"/></g>
 <g id="133000768">
  <use class="kv-1" height="40" transform="rotate(0,3174,449) scale(1.5,1.5) translate(-1068,-184.667)" width="20" x="3174" xlink:href="#Arrester:bn_避雷器4_0" y="449"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,3174,449) scale(1.5,1.5) translate(-1068,-184.667)" width="20" x="3174" y="449"/></g>
 <g id="133000802">
  <use class="kv-1" height="40" transform="rotate(0,3443,449) scale(1.5,1.5) translate(-1157.67,-184.667)" width="20" x="3443" xlink:href="#Arrester:bn_避雷器4_0" y="449"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,3443,449) scale(1.5,1.5) translate(-1157.67,-184.667)" width="20" x="3443" y="449"/></g>
</g>
<g id="Status_Layer">
 <g id="126001044">
  <use class="kv-1" height="40" transform="rotate(0,85,338) scale(0.7,0.7) translate(6.42857,124.857)" width="60" x="85" xlink:href="#Status:bn_工况退出颜色显示_0" y="338"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,85,338) scale(0.7,0.7) translate(6.42857,124.857)" width="60" x="85" y="338"/></g>
 <g id="126001045">
  <use class="kv-1" height="40" transform="rotate(0,188,338) scale(0.7,0.7) translate(50.5714,124.857)" width="60" x="188" xlink:href="#Status:bn_不变化颜色显示_0" y="338"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,188,338) scale(0.7,0.7) translate(50.5714,124.857)" width="60" x="188" y="338"/></g>
 <g id="126001046">
  <use class="kv-1" height="40" transform="rotate(0,292,338) scale(0.7,0.7) translate(95.1429,124.857)" width="60" x="292" xlink:href="#Status:bn_越限颜色显示_0" y="338"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,292,338) scale(0.7,0.7) translate(95.1429,124.857)" width="60" x="292" y="338"/></g>
 <g id="126001047">
  <use class="kv-1" height="40" transform="rotate(0,395,338) scale(0.7,0.7) translate(139.286,124.857)" width="60" x="395" xlink:href="#Status:bn_非实测颜色显示_0" y="338"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,395,338) scale(0.7,0.7) translate(139.286,124.857)" width="60" x="395" y="338"/></g>
 <g id="126001048">
  <use class="kv-1" height="40" transform="rotate(0,502,338) scale(0.7,0.7) translate(185.143,124.857)" width="60" x="502" xlink:href="#Status:bn_数据封锁颜色显示_0" y="338"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,502,338) scale(0.7,0.7) translate(185.143,124.857)" width="60" x="502" y="338"/></g>
</g>
<g id="Clock_Layer">
 <g id="56001043">
  
 <metadata/></g>
</g>
<g id="Link_Layer">
 <g id="34000083">
 <path d="M 1536 469 L 1538 469 L 1565 469 L 1565 444" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001486_0" Pin0InfoVect1LinkObjId="34001485_1" Pin0InfoVect2LinkObjId="34001512_1" Pin1InfoVect0LinkObjId="133000088_0" Plane="0"/>
  </metadata>
 <path d="M 1536 469 L 1538 469 L 1565 469 L 1565 444" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000601">
 <path d="M 1797 571 L 1797 470" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000604_0" Pin1InfoVect0LinkObjId="32000595_0" Pin1InfoVect1LinkObjId="34000602_1" Pin1InfoVect2LinkObjId="34000603_0" Plane="0"/>
  </metadata>
 <path d="M 1797 571 L 1797 470" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000602">
 <path d="M 1763 450 L 1763 470 L 1797 470" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111000606_0" Pin1InfoVect0LinkObjId="34000601_1" Pin1InfoVect1LinkObjId="32000595_0" Pin1InfoVect2LinkObjId="34000603_0" Plane="0"/>
  </metadata>
 <path d="M 1763 450 L 1763 470 L 1797 470" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000603">
 <path d="M 1797 470 L 1829 470 L 1829 449" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000601_1" Pin0InfoVect1LinkObjId="34000602_1" Pin0InfoVect2LinkObjId="32000595_0" Pin1InfoVect0LinkObjId="133000607_0" Plane="0"/>
  </metadata>
 <path d="M 1797 470 L 1829 470 L 1829 449" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000694">
 <path d="M 2066 571 L 2066 470" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000697_0" Pin1InfoVect0LinkObjId="32000688_0" Pin1InfoVect1LinkObjId="34000695_1" Pin1InfoVect2LinkObjId="34000696_0" Plane="0"/>
  </metadata>
 <path d="M 2066 571 L 2066 470" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000695">
 <path d="M 2032 450 L 2032 470 L 2066 470" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111000699_0" Pin1InfoVect0LinkObjId="34000694_1" Pin1InfoVect1LinkObjId="32000688_0" Pin1InfoVect2LinkObjId="34000696_0" Plane="0"/>
  </metadata>
 <path d="M 2032 450 L 2032 470 L 2066 470" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000696">
 <path d="M 2066 470 L 2098 470 L 2098 449" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000694_1" Pin0InfoVect1LinkObjId="34000695_1" Pin0InfoVect2LinkObjId="32000688_0" Pin1InfoVect0LinkObjId="133000700_0" Plane="0"/>
  </metadata>
 <path d="M 2066 470 L 2098 470 L 2098 449" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000713">
 <path d="M 2336 470 L 2368 470 L 2368 449" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000711_1" Pin0InfoVect1LinkObjId="32000705_0" Pin0InfoVect2LinkObjId="34000712_1" Pin1InfoVect0LinkObjId="133000717_0" Plane="0"/>
  </metadata>
 <path d="M 2336 470 L 2368 470 L 2368 449" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000712">
 <path d="M 2302 450 L 2302 470 L 2336 470" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111000716_0" Pin1InfoVect0LinkObjId="34000711_1" Pin1InfoVect1LinkObjId="34000713_0" Pin1InfoVect2LinkObjId="32000705_0" Plane="0"/>
  </metadata>
 <path d="M 2302 450 L 2302 470 L 2336 470" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000711">
 <path d="M 2336 571 L 2336 470" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000714_0" Pin1InfoVect0LinkObjId="32000705_0" Pin1InfoVect1LinkObjId="34000712_1" Pin1InfoVect2LinkObjId="34000713_0" Plane="0"/>
  </metadata>
 <path d="M 2336 571 L 2336 470" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000728">
 <path d="M 2606 571 L 2606 470" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000731_0" Pin1InfoVect0LinkObjId="32000722_0" Pin1InfoVect1LinkObjId="34000729_1" Pin1InfoVect2LinkObjId="34000730_0" Plane="0"/>
  </metadata>
 <path d="M 2606 571 L 2606 470" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000729">
 <path d="M 2572 450 L 2572 470 L 2606 470" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111000733_0" Pin1InfoVect0LinkObjId="34000728_1" Pin1InfoVect1LinkObjId="32000722_0" Pin1InfoVect2LinkObjId="34000730_0" Plane="0"/>
  </metadata>
 <path d="M 2572 450 L 2572 470 L 2606 470" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000730">
 <path d="M 2606 470 L 2638 470 L 2638 449" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000728_1" Pin0InfoVect1LinkObjId="34000729_1" Pin0InfoVect2LinkObjId="32000722_0" Pin1InfoVect0LinkObjId="133000734_0" Plane="0"/>
  </metadata>
 <path d="M 2606 470 L 2638 470 L 2638 449" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000747">
 <path d="M 2875 470 L 2907 470 L 2907 449" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="32000739_0" Pin0InfoVect1LinkObjId="34000745_1" Pin0InfoVect2LinkObjId="34000746_1" Pin1InfoVect0LinkObjId="133000751_0" Plane="0"/>
  </metadata>
 <path d="M 2875 470 L 2907 470 L 2907 449" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000746">
 <path d="M 2841 450 L 2841 470 L 2875 470" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111000750_0" Pin1InfoVect0LinkObjId="34000747_0" Pin1InfoVect1LinkObjId="32000739_0" Pin1InfoVect2LinkObjId="34000745_1" Plane="0"/>
  </metadata>
 <path d="M 2841 450 L 2841 470 L 2875 470" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000745">
 <path d="M 2875 571 L 2875 470" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000748_0" Pin1InfoVect0LinkObjId="34000747_0" Pin1InfoVect1LinkObjId="34000746_1" Pin1InfoVect2LinkObjId="32000739_0" Plane="0"/>
  </metadata>
 <path d="M 2875 571 L 2875 470" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000762">
 <path d="M 3142 571 L 3142 470" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000765_0" Pin1InfoVect0LinkObjId="34000764_0" Pin1InfoVect1LinkObjId="34000763_1" Pin1InfoVect2LinkObjId="32000756_0" Plane="0"/>
  </metadata>
 <path d="M 3142 571 L 3142 470" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000763">
 <path d="M 3108 450 L 3108 470 L 3142 470" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111000767_0" Pin1InfoVect0LinkObjId="34000764_0" Pin1InfoVect1LinkObjId="32000756_0" Pin1InfoVect2LinkObjId="34000762_1" Plane="0"/>
  </metadata>
 <path d="M 3108 450 L 3108 470 L 3142 470" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000764">
 <path d="M 3142 470 L 3174 470 L 3174 449" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="32000756_0" Pin0InfoVect1LinkObjId="34000762_1" Pin0InfoVect2LinkObjId="34000763_1" Pin1InfoVect0LinkObjId="133000768_0" Plane="0"/>
  </metadata>
 <path d="M 3142 470 L 3174 470 L 3174 449" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000796">
 <path d="M 3411 571 L 3411 470" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000799_0" Pin1InfoVect0LinkObjId="32000790_0" Pin1InfoVect1LinkObjId="34000797_1" Pin1InfoVect2LinkObjId="34000798_0" Plane="0"/>
  </metadata>
 <path d="M 3411 571 L 3411 470" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000797">
 <path d="M 3377 450 L 3377 470 L 3411 470" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111000801_0" Pin1InfoVect0LinkObjId="34000796_1" Pin1InfoVect1LinkObjId="32000790_0" Pin1InfoVect2LinkObjId="34000798_0" Plane="0"/>
  </metadata>
 <path d="M 3377 450 L 3377 470 L 3411 470" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000798">
 <path d="M 3411 470 L 3443 470 L 3443 449" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000796_1" Pin0InfoVect1LinkObjId="34000797_1" Pin0InfoVect2LinkObjId="32000790_0" Pin1InfoVect0LinkObjId="133000802_0" Plane="0"/>
  </metadata>
 <path d="M 3411 470 L 3443 470 L 3443 449" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000897">
 <path d="M 1714 1555 L 1714 1349" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="104000901_0" Pin1InfoVect0LinkObjId="110000903_1" Plane="0"/>
  </metadata>
 <path d="M 1714 1555 L 1714 1349" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000912">
 <path d="M 2427 1550 L 2427 1344" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="104000916_0" Pin1InfoVect0LinkObjId="110000918_1" Plane="0"/>
  </metadata>
 <path d="M 2427 1550 L 2427 1344" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000939">
 <path d="M 1714 1188 L 1714 1251" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000938_1" Pin1InfoVect0LinkObjId="110000903_0" Plane="0"/>
  </metadata>
 <path d="M 1714 1188 L 1714 1251" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000947">
 <path d="M 1618 1081 L 1618 1051 L 1714 1051" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133000025_0" Pin1InfoVect0LinkObjId="34000949_0" Pin1InfoVect1LinkObjId="34000948_1" Plane="0"/>
  </metadata>
 <path d="M 1618 1081 L 1618 1051 L 1714 1051" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000948">
 <path d="M 1714 1090 L 1714 1051" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000938_0" Pin1InfoVect0LinkObjId="34000949_0" Pin1InfoVect1LinkObjId="34000947_1" Plane="0"/>
  </metadata>
 <path d="M 1714 1090 L 1714 1051" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000949">
 <path d="M 1714 1051 L 1714 1014" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000947_1" Pin0InfoVect1LinkObjId="34000948_1" Pin1InfoVect0LinkObjId="32000950_0" Plane="0"/>
  </metadata>
 <path d="M 1714 1051 L 1714 1014" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000974">
 <path d="M 3132 1484 L 3132 1374" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="107001455_0" Pin1InfoVect0LinkObjId="110000936_1" Plane="0"/>
  </metadata>
 <path d="M 3132 1484 L 3132 1374" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000975">
 <path d="M 3132 1276 L 3132 1220" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000936_0" Pin1InfoVect0LinkObjId="30000535_0" Plane="0"/>
  </metadata>
 <path d="M 3132 1276 L 3132 1220" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000982">
 <path d="M 1664 119 L 1919 119" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000988_1" Pin1InfoVect0LinkObjId="34001227_1" Pin1InfoVect1LinkObjId="34001070_0" Plane="0"/>
  </metadata>
 <path d="M 1664 119 L 1919 119" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000986">
 <path d="M 1586 142 L 1586 119" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133000090_0" Pin1InfoVect0LinkObjId="34001486_1" Pin1InfoVect1LinkObjId="34000988_0" Plane="0"/>
  </metadata>
 <path d="M 1586 142 L 1586 119" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000988">
 <path d="M 1586 119 L 1664 119" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001486_1" Pin0InfoVect1LinkObjId="34000986_1" Pin1InfoVect0LinkObjId="34000982_0" Plane="0"/>
  </metadata>
 <path d="M 1586 119 L 1664 119" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001050">
 <path d="M 1047 449 L 1047 478 L 1002 478" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133000027_0" Pin1InfoVect0LinkObjId="34001446_1" Pin1InfoVect1LinkObjId="34001447_0" Plane="0"/>
  </metadata>
 <path d="M 1047 449 L 1047 478 L 1002 478" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001069">
 <path d="M 2143 115 L 2350 115" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000096_1" Pin1InfoVect0LinkObjId="101000102_1" Plane="0"/>
  </metadata>
 <path d="M 2143 115 L 2350 115" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001070">
 <path d="M 1919 119 L 2098 119" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001227_1" Pin0InfoVect1LinkObjId="34000982_1" Pin1InfoVect0LinkObjId="100000096_0" Plane="0"/>
  </metadata>
 <path d="M 1919 119 L 2098 119" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001073">
 <path d="M 1263 701 L 1263 748" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101001475_1" Pin1InfoVect0LinkObjId="30000000_0" Plane="0"/>
  </metadata>
 <path d="M 1263 701 L 1263 748" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001078">
 <path d="M 1798 689 L 1798 748" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000605_1" Pin1InfoVect0LinkObjId="30000000_0" Plane="0"/>
  </metadata>
 <path d="M 1798 689 L 1798 748" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001080">
 <path d="M 2067 689 L 2067 748" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000698_1" Pin1InfoVect0LinkObjId="30000000_0" Plane="0"/>
  </metadata>
 <path d="M 2067 689 L 2067 748" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001082">
 <path d="M 2337 686 L 2337 748" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000715_1" Pin1InfoVect0LinkObjId="30000000_0" Plane="0"/>
  </metadata>
 <path d="M 2337 686 L 2337 748" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001084">
 <path d="M 2607 689 L 2607 748" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000732_1" Pin1InfoVect0LinkObjId="30000000_0" Plane="0"/>
  </metadata>
 <path d="M 2607 689 L 2607 748" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001085">
 <path d="M 2876 690 L 2876 748" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000749_1" Pin1InfoVect0LinkObjId="30000000_0" Plane="0"/>
  </metadata>
 <path d="M 2876 690 L 2876 748" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001086">
 <path d="M 3143 689 L 3143 748" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000766_1" Pin1InfoVect0LinkObjId="30000000_0" Plane="0"/>
  </metadata>
 <path d="M 3143 689 L 3143 748" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001087">
 <path d="M 3412 689 L 3412 748" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000800_1" Pin1InfoVect0LinkObjId="30000000_0" Plane="0"/>
  </metadata>
 <path d="M 3412 689 L 3412 748" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001089">
 <path d="M 1536 684 L 1536 748" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101001477_1" Pin1InfoVect0LinkObjId="30000000_0" Plane="0"/>
  </metadata>
 <path d="M 1536 684 L 1536 748" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001090">
 <path d="M 1536 488 L 1536 498" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001485_0" Pin1InfoVect0LinkObjId="101001488_0" Plane="0"/>
  </metadata>
 <path d="M 1536 488 L 1536 498" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001093">
 <path d="M 1797 619 L 1797 647" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000604_1" Pin1InfoVect0LinkObjId="101000605_0" Plane="0"/>
  </metadata>
 <path d="M 1797 619 L 1797 647" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001094">
 <path d="M 2066 619 L 2066 647" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000697_1" Pin1InfoVect0LinkObjId="101000698_0" Plane="0"/>
  </metadata>
 <path d="M 2066 619 L 2066 647" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001095">
 <path d="M 2336 619 L 2336 644" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000714_1" Pin1InfoVect0LinkObjId="101000715_0" Plane="0"/>
  </metadata>
 <path d="M 2336 619 L 2336 644" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001096">
 <path d="M 2606 619 L 2606 647" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000731_1" Pin1InfoVect0LinkObjId="101000732_0" Plane="0"/>
  </metadata>
 <path d="M 2606 619 L 2606 647" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001097">
 <path d="M 2875 619 L 2875 648" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000748_1" Pin1InfoVect0LinkObjId="101000749_0" Plane="0"/>
  </metadata>
 <path d="M 2875 619 L 2875 648" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001098">
 <path d="M 3142 619 L 3142 647" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000765_1" Pin1InfoVect0LinkObjId="101000766_0" Plane="0"/>
  </metadata>
 <path d="M 3142 619 L 3142 647" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001099">
 <path d="M 3411 619 L 3411 647" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000799_1" Pin1InfoVect0LinkObjId="101000800_0" Plane="0"/>
  </metadata>
 <path d="M 3411 619 L 3411 647" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001119">
 <path d="M 1066 1058 L 1066 1080 L 1066 1092 L 1003 1092" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133000029_0" Pin1InfoVect0LinkObjId="34001120_1" Pin1InfoVect1LinkObjId="34001121_0" Plane="0"/>
  </metadata>
 <path d="M 1066 1058 L 1066 1080 L 1066 1092 L 1003 1092" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001120">
 <path d="M 1003 1068 L 1003 1092" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="102000439_1" Pin1InfoVect0LinkObjId="34001119_1" Pin1InfoVect1LinkObjId="34001121_0" Plane="0"/>
  </metadata>
 <path d="M 1003 1068 L 1003 1092" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001121">
 <path d="M 1003 1092 L 1003 1104" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001119_1" Pin0InfoVect1LinkObjId="34001120_1" Pin1InfoVect0LinkObjId="110000448_0" Plane="0"/>
  </metadata>
 <path d="M 1003 1092 L 1003 1104" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001122">
 <path d="M 1003 1202 L 1003 1256" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000448_1" Pin1InfoVect0LinkObjId="110000855_0" Plane="0"/>
  </metadata>
 <path d="M 1003 1202 L 1003 1256" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001129">
 <path d="M 2427 1171 L 2427 1246" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000956_0" Pin1InfoVect0LinkObjId="110000918_0" Plane="0"/>
  </metadata>
 <path d="M 2427 1171 L 2427 1246" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001130">
 <path d="M 2426 1043 L 2426 1077" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="107000962_0" Pin1InfoVect0LinkObjId="110000956_1" Plane="0"/>
  </metadata>
 <path d="M 2426 1043 L 2426 1077" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001227">
 <path d="M 1919 97 L 1919 119" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111000093_0" Pin1InfoVect0LinkObjId="34000982_1" Pin1InfoVect1LinkObjId="34001070_0" Plane="0"/>
  </metadata>
 <path d="M 1919 97 L 1919 119" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001368">
 <path d="M 880 1468 L 880 1490" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000862_1" Pin1InfoVect0LinkObjId="107001356_0" Plane="0"/>
  </metadata>
 <path d="M 880 1468 L 880 1490" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001389">
 <path d="M 1066 940 L 1066 952 L 1004 952" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133000409_0" Pin1InfoVect0LinkObjId="34001390_1" Pin1InfoVect1LinkObjId="34001391_0" Plane="0"/>
  </metadata>
 <path d="M 1066 940 L 1066 952 L 1004 952" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001390">
 <path d="M 1004 938 L 1004 952" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000408_1" Pin1InfoVect0LinkObjId="34001389_1" Pin1InfoVect1LinkObjId="34001391_0" Plane="0"/>
  </metadata>
 <path d="M 1004 938 L 1004 952" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001391">
 <path d="M 1004 952 L 1004 965" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001390_1" Pin0InfoVect1LinkObjId="34001389_1" Pin1InfoVect0LinkObjId="102000439_0" Plane="0"/>
  </metadata>
 <path d="M 1004 952 L 1004 965" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001409">
 <path d="M 1598 1468 L 1598 1490" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001411_1" Pin1InfoVect0LinkObjId="107001410_0" Plane="0"/>
  </metadata>
 <path d="M 1598 1468 L 1598 1490" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001432">
 <path d="M 2299 1468 L 2299 1490" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001434_1" Pin1InfoVect0LinkObjId="107001433_0" Plane="0"/>
  </metadata>
 <path d="M 2299 1468 L 2299 1490" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001436">
 <path d="M 2299 1373 L 2548 1373 L 2548 1514" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001434_0" Pin1InfoVect0LinkObjId="107001065_0" Plane="0"/>
  </metadata>
 <path d="M 2299 1373 L 2548 1373 L 2548 1514" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001437">
 <path d="M 1598 1373 L 1845 1373 L 1845 1511" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001411_0" Pin1InfoVect0LinkObjId="107001063_0" Plane="0"/>
  </metadata>
 <path d="M 1598 1373 L 1845 1373 L 1845 1511" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001446">
 <path d="M 1002 629 L 1002 478" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111001438_1" Pin1InfoVect0LinkObjId="34001050_1" Pin1InfoVect1LinkObjId="34001447_0" Plane="0"/>
  </metadata>
 <path d="M 1002 629 L 1002 478" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001447">
 <path d="M 1002 478 L 1002 267" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001446_1" Pin0InfoVect1LinkObjId="34001050_1" Pin1InfoVect0LinkObjId="107001355_0" Plane="0"/>
  </metadata>
 <path d="M 1002 478 L 1002 267" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001451">
 <path d="M 1003 878 L 1003 896" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000018_1" Pin1InfoVect0LinkObjId="101000408_0" Plane="0"/>
  </metadata>
 <path d="M 1003 878 L 1003 896" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001453">
 <path d="M 1004 767 L 1004 689" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000406_0" Pin1InfoVect0LinkObjId="111001438_0" Plane="0"/>
  </metadata>
 <path d="M 1004 767 L 1004 689" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001454">
 <path d="M 1004 809 L 1004 830" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000406_1" Pin1InfoVect0LinkObjId="100000018_0" Plane="0"/>
  </metadata>
 <path d="M 1004 809 L 1004 830" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001459">
 <path d="M 1004 1373 L 1004 1560" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001462_0" Pin1InfoVect0LinkObjId="104000865_0" Plane="0"/>
  </metadata>
 <path d="M 1004 1373 L 1004 1560" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001462">
 <path d="M 1004 1373 L 1004 1354" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001459_0" Pin1InfoVect0LinkObjId="110000855_1" Plane="0"/>
  </metadata>
 <path d="M 1004 1373 L 1004 1354" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001463">
 <path d="M 880 1373 L 1129 1373 L 1129 1502" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000862_0" Pin1InfoVect0LinkObjId="107001059_0" Plane="0"/>
  </metadata>
 <path d="M 880 1373 L 1129 1373 L 1129 1502" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001466">
 <path d="M 1263 479 L 1291 479 L 1291 447" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="32000041_0" Pin0InfoVect1LinkObjId="34001513_1" Pin1InfoVect0LinkObjId="133000024_0" Plane="0"/>
  </metadata>
 <path d="M 1263 479 L 1291 479 L 1291 447" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001473">
 <path d="M 1263 623 L 1263 659" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000015_1" Pin1InfoVect0LinkObjId="101001475_0" Plane="0"/>
  </metadata>
 <path d="M 1263 623 L 1263 659" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001478">
 <path d="M 1536 619 L 1536 642" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000084_1" Pin1InfoVect0LinkObjId="101001477_0" Plane="0"/>
  </metadata>
 <path d="M 1536 619 L 1536 642" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001484">
 <path d="M 1263 540 L 1263 575" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101001480_1" Pin1InfoVect0LinkObjId="100000015_0" Plane="0"/>
  </metadata>
 <path d="M 1263 540 L 1263 575" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001485">
 <path d="M 1536 488 L 1536 469" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001090_0" Pin1InfoVect0LinkObjId="34001486_0" Pin1InfoVect1LinkObjId="34000083_0" Pin1InfoVect2LinkObjId="34001512_1" Plane="0"/>
  </metadata>
 <path d="M 1536 488 L 1536 469" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001486">
 <path d="M 1536 469 L 1536 119 L 1586 119" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000083_0" Pin0InfoVect1LinkObjId="34001485_1" Pin0InfoVect2LinkObjId="34001512_1" Pin1InfoVect0LinkObjId="34000986_1" Pin1InfoVect1LinkObjId="34000988_0" Plane="0"/>
  </metadata>
 <path d="M 1536 469 L 1536 119 L 1586 119" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001489">
 <path d="M 1536 540 L 1536 571" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101001488_1" Pin1InfoVect0LinkObjId="100000084_0" Plane="0"/>
  </metadata>
 <path d="M 1536 540 L 1536 571" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001512">
 <path d="M 1493 438 L 1493 469 L 1536 469" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="107001508_0" Pin1InfoVect0LinkObjId="34001486_0" Pin1InfoVect1LinkObjId="34000083_0" Pin1InfoVect2LinkObjId="34001485_1" Plane="0"/>
  </metadata>
 <path d="M 1493 438 L 1493 469 L 1536 469" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001513">
 <path d="M 1211 442 L 1211 479 L 1263 479" stroke="rgb(0,0,255)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="107001514_0" Pin1InfoVect0LinkObjId="34001466_0" Pin1InfoVect1LinkObjId="32000041_0" Plane="0"/>
  </metadata>
 <path d="M 1211 442 L 1211 479 L 1263 479" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="MeasurementValue_Layer">
 <g id="33001042">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="317" xml:space="preserve" y="933">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001041">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="317" xml:space="preserve" y="834">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001141">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3542" xml:space="preserve" y="492">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001142">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3542" xml:space="preserve" y="521">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001143">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3542" xml:space="preserve" y="550">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001144">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3542" xml:space="preserve" y="578">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001145">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3542" xml:space="preserve" y="691">-0.000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001146">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3542" xml:space="preserve" y="605">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001147">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3542" xml:space="preserve" y="634">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001148">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3542" xml:space="preserve" y="662">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001157">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3552" xml:space="preserve" y="967">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001158">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3552" xml:space="preserve" y="996">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001159">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3552" xml:space="preserve" y="1025">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001160">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3552" xml:space="preserve" y="1053">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001161">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3552" xml:space="preserve" y="1166">-0.000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001162">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3552" xml:space="preserve" y="1080">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001163">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3552" xml:space="preserve" y="1109">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001164">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3552" xml:space="preserve" y="1137">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001261">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1228" xml:space="preserve" y="103">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001262">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1228" xml:space="preserve" y="129">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001263">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1228" xml:space="preserve" y="155">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001269">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1763" xml:space="preserve" y="234">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001268">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1763" xml:space="preserve" y="208">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001267">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1763" xml:space="preserve" y="182">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001275">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="2032" xml:space="preserve" y="230">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001274">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="2032" xml:space="preserve" y="204">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001273">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="2032" xml:space="preserve" y="178">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001279">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="2303" xml:space="preserve" y="178">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001280">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="2303" xml:space="preserve" y="204">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001281">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="2303" xml:space="preserve" y="230">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001287">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="2575" xml:space="preserve" y="230">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001286">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="2575" xml:space="preserve" y="204">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001285">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="2575" xml:space="preserve" y="178">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001291">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="2845" xml:space="preserve" y="178">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001292">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="2845" xml:space="preserve" y="204">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001293">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="2845" xml:space="preserve" y="230">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001299">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3114" xml:space="preserve" y="230">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001298">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3114" xml:space="preserve" y="204">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001297">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3114" xml:space="preserve" y="178">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001303">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3378" xml:space="preserve" y="178">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001304">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3378" xml:space="preserve" y="204">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001305">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3378" xml:space="preserve" y="230">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001311">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1676" xml:space="preserve" y="861">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001310">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1676" xml:space="preserve" y="835">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001309">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1676" xml:space="preserve" y="809">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001315">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3534" xml:space="preserve" y="49">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001316">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3534" xml:space="preserve" y="75">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001317">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="3534" xml:space="preserve" y="101">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001323">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="2406" xml:space="preserve" y="1784">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001322">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="2406" xml:space="preserve" y="1758">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001321">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="2406" xml:space="preserve" y="1732">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001327">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1677" xml:space="preserve" y="1732">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001328">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1677" xml:space="preserve" y="1758">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001329">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1677" xml:space="preserve" y="1784">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001335">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="969" xml:space="preserve" y="1784">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001334">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="969" xml:space="preserve" y="1758">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001333">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="969" xml:space="preserve" y="1732">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001339">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1125" xml:space="preserve" y="841">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001340">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1125" xml:space="preserve" y="867">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001341">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1125" xml:space="preserve" y="893">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001347">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1123" xml:space="preserve" y="1191">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001346">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1123" xml:space="preserve" y="1165">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001345">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="1123" xml:space="preserve" y="1139">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
</g>
<g id="Text_Layer">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="3558" xml:space="preserve" y="734">10kV母线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="1161" xml:space="preserve" y="620">021</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="1035" xml:space="preserve" y="872">001</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="1123" xml:space="preserve" y="192">10kV植物园I回线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="1440" xml:space="preserve" y="616">022</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="1938" xml:space="preserve" y="88">01A7</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="2095" xml:space="preserve" y="94">01A</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="2333" xml:space="preserve" y="94">01A1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="892" xml:space="preserve" y="809">0011</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="888" xml:space="preserve" y="938">0016</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="1018" xml:space="preserve" y="1175">601</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="3555" xml:space="preserve" y="1206">6.3kV母线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="1689" xml:space="preserve" y="689">0231</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="1714" xml:space="preserve" y="616">023</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="1649" xml:space="preserve" y="464">02317</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="1718" xml:space="preserve" y="280">西区I回线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="1958" xml:space="preserve" y="689">0241</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="1983" xml:space="preserve" y="616">024</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="1911" xml:space="preserve" y="456">02417</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="1986" xml:space="preserve" y="278">东区II回线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="2252" xml:space="preserve" y="277">西区III回线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="2186" xml:space="preserve" y="462">02517</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="2253" xml:space="preserve" y="616">025</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="2225" xml:space="preserve" y="686">0251</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="2496" xml:space="preserve" y="689">0261</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="2523" xml:space="preserve" y="616">026</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="2457" xml:space="preserve" y="462">02617</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="2521" xml:space="preserve" y="277">东区IV回线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="2796" xml:space="preserve" y="280">西区V回线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="2726" xml:space="preserve" y="462">02717</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="2792" xml:space="preserve" y="616">027</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="2762" xml:space="preserve" y="690">0271</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="3029" xml:space="preserve" y="689">0281</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="3059" xml:space="preserve" y="616">028</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="2985" xml:space="preserve" y="462">02817</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="3058" xml:space="preserve" y="280">西区VI回线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="3298" xml:space="preserve" y="689">0291</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="3328" xml:space="preserve" y="616">029</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="3259" xml:space="preserve" y="462">02917</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="3364" xml:space="preserve" y="279">备用线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="2885" xml:space="preserve" y="97">10kV中国科学院西双版纳热带植物园支线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="1018" xml:space="preserve" y="1327">611</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="783" xml:space="preserve" y="1407">6111</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(230,232,254)" writing-mode="lr" x="902" xml:space="preserve" y="1662">1号发电机</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="1729" xml:space="preserve" y="1322">612</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(230,232,254)" writing-mode="lr" x="1648" xml:space="preserve" y="1661">2号发电机</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(230,232,254)" writing-mode="lr" x="2361" xml:space="preserve" y="1658">3号发电机</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="2442" xml:space="preserve" y="1317">613</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="3202" xml:space="preserve" y="1347">615</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="1729" xml:space="preserve" y="1161">614</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="1602" xml:space="preserve" y="903">科研中心I回线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="2442" xml:space="preserve" y="1144">6901</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="2356" xml:space="preserve" y="900">6.3kV母线PT</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="1523" xml:space="preserve" y="103">10kV植物园II回线</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="106" font-size="36" font-width="36" stroke="rgb(0,0,0)" writing-mode="lr" x="186" xml:space="preserve" y="151">葫芦岛电站10kV开关站</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="112" xml:space="preserve" y="1026">事故总:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="82" xml:space="preserve" y="833">总有功</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(170,0,0)" writing-mode="lr" x="249" xml:space="preserve" y="536">MW</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="152" xml:space="preserve" y="534">有功:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="318" xml:space="preserve" y="534">无功:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(170,0,0)" writing-mode="lr" x="410" xml:space="preserve" y="536">MVr</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="152" xml:space="preserve" y="637">电流:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(170,0,0)" writing-mode="lr" x="249" xml:space="preserve" y="637">A</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="318" xml:space="preserve" y="637">电压:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(170,0,0)" writing-mode="lr" x="410" xml:space="preserve" y="637">kV</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="152" xml:space="preserve" y="732">频率:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(170,0,0)" writing-mode="lr" x="249" xml:space="preserve" y="734">Hz</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="318" xml:space="preserve" y="732">温度:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(170,0,0)" writing-mode="lr" x="410" xml:space="preserve" y="734">℃</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="82" xml:space="preserve" y="930">总无功</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="tb" x="82" xml:space="preserve" y="542">单</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="46" xml:space="preserve" y="1151">联系方式：</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="tb" x="82" xml:space="preserve" y="661">位</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="49" xml:space="preserve" y="421">工况</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="49" xml:space="preserve" y="452">退出</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="173" xml:space="preserve" y="421">不</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="158" xml:space="preserve" y="454">变化</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="260" xml:space="preserve" y="440">越限</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="378" xml:space="preserve" y="422">非</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="363" xml:space="preserve" y="455">实测</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="468" xml:space="preserve" y="421">数据</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="468" xml:space="preserve" y="452">封锁</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="788" xml:space="preserve" y="1025">1号主变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="1161" xml:space="preserve" y="1518">1号发电机励磁变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="1877" xml:space="preserve" y="1518">2号发电机励磁变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="2584" xml:space="preserve" y="1552">3号发电机励磁变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="3491" xml:space="preserve" y="492">Uab</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="3491" xml:space="preserve" y="521">Ubc</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="3491" xml:space="preserve" y="550">Uca</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="3491" xml:space="preserve" y="578">Ua</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="3491" xml:space="preserve" y="693">F</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="3491" xml:space="preserve" y="634">Uc</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="3491" xml:space="preserve" y="605">Ub</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="3491" xml:space="preserve" y="662">3U0</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="3501" xml:space="preserve" y="967">Uab</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="3501" xml:space="preserve" y="996">Ubc</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="3501" xml:space="preserve" y="1025">Uca</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="3501" xml:space="preserve" y="1053">Ua</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="3501" xml:space="preserve" y="1168">F</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="3501" xml:space="preserve" y="1109">Uc</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="3501" xml:space="preserve" y="1080">Ub</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="3501" xml:space="preserve" y="1137">3U0</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="1205" xml:space="preserve" y="129">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="1204" xml:space="preserve" y="155">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="1205" xml:space="preserve" y="103">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="1740" xml:space="preserve" y="182">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="1739" xml:space="preserve" y="234">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="1740" xml:space="preserve" y="208">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="2009" xml:space="preserve" y="178">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="2008" xml:space="preserve" y="230">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="2009" xml:space="preserve" y="204">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="2280" xml:space="preserve" y="204">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="2279" xml:space="preserve" y="230">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="2280" xml:space="preserve" y="178">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="2552" xml:space="preserve" y="178">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="2551" xml:space="preserve" y="230">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="2552" xml:space="preserve" y="204">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="2822" xml:space="preserve" y="204">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="2821" xml:space="preserve" y="230">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="2822" xml:space="preserve" y="178">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="3091" xml:space="preserve" y="178">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="3090" xml:space="preserve" y="230">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="3091" xml:space="preserve" y="204">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="3355" xml:space="preserve" y="204">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="3354" xml:space="preserve" y="230">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="3355" xml:space="preserve" y="178">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="1653" xml:space="preserve" y="809">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="1652" xml:space="preserve" y="861">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="1653" xml:space="preserve" y="835">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="3511" xml:space="preserve" y="75">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="3510" xml:space="preserve" y="101">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="3511" xml:space="preserve" y="49">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="2383" xml:space="preserve" y="1732">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="2382" xml:space="preserve" y="1784">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="2383" xml:space="preserve" y="1758">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="1654" xml:space="preserve" y="1758">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="1653" xml:space="preserve" y="1784">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="1654" xml:space="preserve" y="1732">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="946" xml:space="preserve" y="1732">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="945" xml:space="preserve" y="1784">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="946" xml:space="preserve" y="1758">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="1102" xml:space="preserve" y="867">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="1101" xml:space="preserve" y="893">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="1102" xml:space="preserve" y="841">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="1100" xml:space="preserve" y="1139">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="1099" xml:space="preserve" y="1191">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(35,169,128)" writing-mode="lr" x="1100" xml:space="preserve" y="1165">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="947" xml:space="preserve" y="123">10kV母线PT</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="626" xml:space="preserve" y="1555">1号发电机PT</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="1501" xml:space="preserve" y="1407">6121</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="1323" xml:space="preserve" y="1588">2号发电机PT</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="2202" xml:space="preserve" y="1407">6131</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2040" xml:space="preserve" y="1594">3号发电机PT</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="30" font-width="30" stroke="rgb(255,255,254)" writing-mode="lr" x="895" xml:space="preserve" y="687">0901</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="30" font-width="30" stroke="rgb(255,255,254)" writing-mode="lr" x="3090" xml:space="preserve" y="1669">厂用变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="1154" xml:space="preserve" y="701">0211</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="1427" xml:space="preserve" y="684">0221</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="1154" xml:space="preserve" y="540">0216</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="1427" xml:space="preserve" y="540">0226</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href="景洪地区.sys.svg"><rect fill-opacity="0" height="169" stroke-opacity="0" stroke-width="1" width="524" x="29" y="22"/></g>
</g>
</svg>