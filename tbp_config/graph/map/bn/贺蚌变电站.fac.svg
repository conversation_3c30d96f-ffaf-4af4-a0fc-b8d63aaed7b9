<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1240" id="thSvg" viewBox="0 0 2950 1240" width="2950">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="Status:bn_不变化颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(0,0,255)" height="1" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_不变化颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(85,0,255)" height="1" stroke="rgb(85,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Disconnector:bn_刀闸110_0" viewBox="0,0,40,70">
 <use Plane="0" x="25" xlink:href="#terminal" y="63"/>
 <use Plane="0" x="24" xlink:href="#terminal" y="5"/>
 <path AFMask="2147483647" Plane="0" d="M 19 16 L 25 10 L 31 16 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="25" x2="25" y1="59" y2="11"/>
 <path AFMask="2147483647" Plane="0" d="M 19 11 L 25 5 L 31 12 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="0" d="M 19 53 L 25 59 L 31 53 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="0" d="M 19 57 L 25 63 L 31 57 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="26" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,25,37)" width="12" x="19" y="24"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="25" x2="10" y1="20" y2="20"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="20" y2="35"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="9" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,10,34)" width="6" x="7" y="30"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="11" y1="35" y2="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="10" y1="35" y2="36"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="11" x2="10" y1="35" y2="36"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="39" y2="42"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="8" x2="12" y1="43" y2="43"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="11" y1="44" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="46" y2="46"/>
</symbol>
<symbol id="Disconnector:bn_刀闸110_1" viewBox="0,0,40,70">
 <use Plane="0" x="25" xlink:href="#terminal" y="63"/>
 <use Plane="0" x="24" xlink:href="#terminal" y="5"/>
 <path AFMask="2147483647" Plane="0" d="M 19 57 L 24 62 L 29 57 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
 <path AFMask="2147483647" Plane="0" d="M 19 11 L 24 6 L 29 11 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="25" x2="25" y1="9" y2="60"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="26" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,25,37)" width="12" x="19" y="24"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="25" x2="10" y1="20" y2="20"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="20" y2="35"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="9" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,10,34)" width="6" x="7" y="30"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="11" y1="35" y2="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="10" y1="35" y2="36"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="11" x2="10" y1="35" y2="36"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="39" y2="42"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="8" x2="12" y1="43" y2="43"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="11" y1="44" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="45" y2="45"/>
</symbol>
<symbol id="PT:bn_电压互感器008_0" viewBox="0,0,18,44">
 <use Plane="0" x="8" xlink:href="#terminal" y="40"/>
 <circle AFMask="2147483647" Plane="0" cx="8" cy="21" fill="none" r="8" stroke="rgb(255,255,254)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="8" cy="10" fill="none" r="8" stroke="rgb(255,255,254)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="9" y1="29" y2="39"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="9" y1="4" y2="7"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="5" y1="8" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="13" y1="8" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="9" y1="4" y2="4"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="9" y1="19" y2="22"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="5" y1="22" y2="26"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="13" y1="22" y2="26"/>
</symbol>
<symbol id="Status:bn_非实测颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,85,0)" height="1" stroke="rgb(170,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_非实测颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,85,0)" height="1" stroke="rgb(170,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_工况退出颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,170,255)" height="1" stroke="rgb(170,170,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_工况退出颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,170,255)" height="1" stroke="rgb(170,170,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸12_0" viewBox="0,0,40,20">
 <use Plane="0" x="4" xlink:href="#terminal" y="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="31" y1="6" y2="18"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="28" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="33" y1="8" y2="16"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="35" x2="35" y1="10" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="28" y1="2" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="4" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="9" y1="5" y2="19"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸12_1" viewBox="0,0,40,20">
 <use Plane="0" x="4" xlink:href="#terminal" y="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="31" y1="6" y2="18"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="3" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="33" y1="8" y2="16"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="35" x2="35" y1="10" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="9" y1="5" y2="19"/>
</symbol>
<symbol id="Status:bn_数据封锁颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,85,0)" height="1" stroke="rgb(255,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_数据封锁颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,85,0)" height="1" stroke="rgb(255,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_0_0" viewBox="0,0,20,74">
 <rect AFMask="2147483647" Plane="0" fill="none" height="0" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,4,16)" width="0" x="4" y="16"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="30" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,9,37)" width="13" x="3" y="22"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_0_1" viewBox="0,0,20,74">
 <rect AFMask="2147483647" Plane="0" fill="none" height="30" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,9,36)" width="13" x="3" y="21"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_0_2" viewBox="0,0,20,74">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,254)" height="30" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,9,36)" width="13" x="3" y="21"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_0_3" viewBox="0,0,20,74">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,254)" height="30" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,9,37)" width="13" x="3" y="22"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_1_0" viewBox="0,0,20,74">
 <use Plane="1" x="9" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="9" xlink:href="#terminal" y="69"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="21" y2="10"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="52" y2="63"/>
 <path AFMask="2147483647" Plane="1" d="M 3 11 L 10 5 L 17 12 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 3 57 L 9 64 L 16 57 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 3 15 L 10 9 L 17 16 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 3 61 L 9 68 L 16 61 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_1_1" viewBox="0,0,20,74">
 <use Plane="1" x="9" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="9" xlink:href="#terminal" y="69"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="21" y2="9"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="52" y2="65"/>
 <path AFMask="2147483647" Plane="1" d="M 4 12 L 10 6 L 16 12 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
 <path AFMask="2147483647" Plane="1" d="M 4 61 L 10 67 L 16 61 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_1_2" viewBox="0,0,20,74">
 <use Plane="1" x="9" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="9" xlink:href="#terminal" y="69"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="22" y2="10"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="52" y2="63"/>
 <path AFMask="2147483647" Plane="1" d="M 3 11 L 9 4 L 16 11 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 3 15 L 9 8 L 16 15 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 2 57 L 9 63 L 16 56 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 2 61 L 9 67 L 16 60 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_1_3" viewBox="0,0,20,74">
 <use Plane="1" x="9" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="9" xlink:href="#terminal" y="69"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="21" y2="8"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="52" y2="65"/>
 <path AFMask="2147483647" Plane="1" d="M 3 11 L 9 5 L 15 11 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
 <path AFMask="2147483647" Plane="1" d="M 3 60 L 9 66 L 15 60 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
</symbol>
<symbol id="Transformer2:bn_益州升压站_0" viewBox="0,0,60,86">
 <use Plane="0" x="38" xlink:href="#terminal" y="3"/>
 <circle AFMask="2147483647" Plane="0" cx="38" cy="20" fill="none" r="15" stroke="rgb(0,0,255)" stroke-width="1"/>
 <polygon AFMask="2147483647" Plane="0" fill="none" points="31,24 38,11 45,24" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Transformer2:bn_益州升压站_1" viewBox="0,0,60,86">
 <circle AFMask="2147483647" Plane="1" cx="38" cy="44" fill="none" r="15" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="38" x2="38" y1="45" y2="35"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="38" x2="45" y1="45" y2="52"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="38" x2="31" y1="45" y2="52"/>
</symbol>
<symbol id="Transformer2:bn_益州升压站_2" viewBox="0,0,60,86">
</symbol>
<symbol id="Transformer2:bn_益州升压站_3" viewBox="0,0,60,86">
</symbol>
<symbol id="Transformer2:bn_益州升压站_4" viewBox="0,0,60,86">
 <use Plane="4" x="38" xlink:href="#terminal" y="76"/>
 <line AFMask="2147483647" Plane="4" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="24" x2="38" y1="45" y2="45"/>
 <line AFMask="2147483647" Plane="4" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="18" x2="23" y1="45" y2="45"/>
 <line AFMask="2147483647" Plane="4" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="20" x2="13" y1="45" y2="45"/>
 <line AFMask="2147483647" Plane="4" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="13" x2="13" y1="45" y2="74"/>
 <line AFMask="2147483647" Plane="4" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="11" x2="17" y1="74" y2="74"/>
 <line AFMask="2147483647" Plane="4" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="11" x2="11" y1="74" y2="74"/>
 <line AFMask="2147483647" Plane="4" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="13" x2="9" y1="74" y2="74"/>
 <line AFMask="2147483647" Plane="4" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="11" x2="15" y1="76" y2="76"/>
 <line AFMask="2147483647" Plane="4" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="12" x2="14" y1="78" y2="78"/>
 <line AFMask="2147483647" Plane="4" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="38" x2="38" y1="60" y2="76"/>
 <line AFMask="2147483647" Plane="4" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="13" x2="38" y1="48" y2="73"/>
</symbol>
<symbol id="Status:bn_越限颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_越限颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="GZP:gg_光子牌1_0" viewBox="0,0,36,36">
 <circle AFMask="2147483647" Plane="0" cx="18" cy="18" fill="rgb(0,255,0)" r="15" stroke="rgb(0,255,0)" stroke-width="1"/>
</symbol>
<symbol id="GZP:gg_光子牌1_1" viewBox="0,0,36,36">
 <circle AFMask="2147483647" Plane="0" cx="18" cy="18" fill="rgb(255,0,0)" r="15" stroke="rgb(255,0,0)" stroke-width="1"/>
</symbol>
<symbol id="PT:告庄1号电压互感器_0" viewBox="0,0,100,100">
 <use Plane="0" x="33" xlink:href="#terminal" y="74"/>
 <circle AFMask="2147483647" Plane="0" cx="33" cy="47" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="33" y1="40" y2="47"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="28" y1="47" y2="51"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="38" y1="47" y2="51"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="38" y1="63" y2="67"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="28" y1="63" y2="67"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="33" y1="56" y2="63"/>
 <circle AFMask="2147483647" Plane="0" cx="33" cy="63" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="49" cy="63" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="49" x2="49" y1="56" y2="63"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="49" x2="44" y1="63" y2="67"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="49" x2="54" y1="63" y2="67"/>
 <circle AFMask="2147483647" Plane="0" cx="49" cy="47" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="52" x2="52" y1="40" y2="51"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="52" x2="46" y1="40" y2="46"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="46" x2="52" y1="46" y2="52"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="版纳_35kV_贺蚌变" InitShowingPlane="0," fill="rgb(0,0,0)" height="1240" width="2950" x="0" y="0"/>
</g>
<g id="Other_Layer">
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="5" x2="411" y1="301" y2="301"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="85" x2="85" y1="249" y2="367"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="160" x2="160" y1="249" y2="367"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="238" x2="238" y1="247" y2="365"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="310" x2="310" y1="247" y2="365"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="6" x2="412" y1="608" y2="608"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="5" x2="413" y1="548" y2="548"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="5" x2="412" y1="665" y2="665"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="4" x2="412" y1="732" y2="732"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="103" x2="413" y1="487" y2="487"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="103" x2="412" y1="422" y2="422"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="211" x2="211" y1="547" y2="665"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="103" x2="103" y1="371" y2="540"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="4" x2="414" y1="366" y2="366"/>
 
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="6" x2="413" y1="145" y2="145"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="6" x2="412" y1="891" y2="891"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="5" x2="409" y1="803" y2="803"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="413" x2="413" y1="18" y2="1218"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="5" x2="412" y1="18" y2="18"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="5" x2="5" y1="18" y2="1216"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="5" x2="411" y1="245" y2="245"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="4" x1="5" x2="411" y1="1219" y2="1219"/>
 
</g>
<g id="Bus_Layer">
 <g id="30000019">
  <path d="M 488 386 L 1635 386" stroke="rgb(128,128,128)" stroke-width="6"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 488 386 L 1635 386" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="30000175">
  <path d="M 1706 386 L 2900 386" stroke="rgb(128,128,128)" stroke-width="6"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 1706 386 L 2900 386" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="Disconnector_Layer">
 <g id="101001090">
  <use class="kv-1" height="70" transform="rotate(0,1135,316) scale(1,1) translate(-25,-35)" width="40" x="1135" xlink:href="#Disconnector:bn_刀闸110_0" y="316"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,1135,316) scale(1,1) translate(-25,-35)" width="40" x="1135" y="316"/></g>
 <g id="101001092">
  <use class="kv-1" height="70" transform="rotate(0,1435,314) scale(1,1) translate(-25,-35)" width="40" x="1435" xlink:href="#Disconnector:bn_刀闸110_0" y="314"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,1435,314) scale(1,1) translate(-25,-35)" width="40" x="1435" y="314"/></g>
 <g id="101001098">
  <use class="kv-1" height="70" transform="rotate(0,2412,322) scale(1,1) translate(-25,-35)" width="40" x="2412" xlink:href="#Disconnector:bn_刀闸110_0" y="322"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,2412,322) scale(1,1) translate(-25,-35)" width="40" x="2412" y="322"/></g>
 <g id="101001474">
  <use class="kv-1" height="70" transform="rotate(0,2116,314) scale(1,1) translate(-25,-35)" width="40" x="2116" xlink:href="#Disconnector:bn_刀闸110_0" y="314"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,2116,314) scale(1,1) translate(-25,-35)" width="40" x="2116" y="314"/></g>
 <g id="101001480">
  <use class="kv-1" height="70" transform="rotate(0,1752,496) scale(1,1) translate(-25,-35)" width="40" x="1752" xlink:href="#Disconnector:bn_刀闸110_0" y="496"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,1752,496) scale(1,1) translate(-25,-35)" width="40" x="1752" y="496"/></g>
 <g id="101001487">
  <use class="kv-1" height="70" transform="rotate(0,1225,642) scale(1,1) translate(-25,-35)" width="40" x="1225" xlink:href="#Disconnector:bn_刀闸110_0" y="642"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,1225,642) scale(1,1) translate(-25,-35)" width="40" x="1225" y="642"/></g>
 <g id="101001493">
  <use class="kv-1" height="70" transform="rotate(0,2196,640) scale(1,1) translate(-25,-35)" width="40" x="2196" xlink:href="#Disconnector:bn_刀闸110_0" y="640"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,2196,640) scale(1,1) translate(-25,-35)" width="40" x="2196" y="640"/></g>
</g>
<g id="GroundDisconnector_Layer">
 <g id="111000069">
  <use class="kv-1" height="20" transform="rotate(90,823,615) scale(1,1) translate(-4,-12)" width="40" x="823" xlink:href="#GroundDisconnector:bn_接地刀闸12_0" y="615"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,823,615) scale(1,1) translate(-4,-12)" width="40" x="823" y="615"/></g>
 <g id="111000071">
  <use class="kv-1" height="20" transform="rotate(90,587,615) scale(1,1) translate(-4,-12)" width="40" x="587" xlink:href="#GroundDisconnector:bn_接地刀闸12_0" y="615"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,587,615) scale(1,1) translate(-4,-12)" width="40" x="587" y="615"/></g>
 <g id="111000089">
  <use class="kv-1" height="20" transform="rotate(90,711,615) scale(1,1) translate(-4,-12)" width="40" x="711" xlink:href="#GroundDisconnector:bn_接地刀闸12_0" y="615"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,711,615) scale(1,1) translate(-4,-12)" width="40" x="711" y="615"/></g>
 <g id="111001114">
  <use class="kv-1" height="20" transform="rotate(90,946,615) scale(1,1) translate(-4,-12)" width="40" x="946" xlink:href="#GroundDisconnector:bn_接地刀闸12_0" y="615"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,946,615) scale(1,1) translate(-4,-12)" width="40" x="946" y="615"/></g>
 <g id="111001162">
  <use class="kv-1" height="20" transform="rotate(90,1061,615) scale(1,1) translate(-4,-12)" width="40" x="1061" xlink:href="#GroundDisconnector:bn_接地刀闸12_0" y="615"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1061,615) scale(1,1) translate(-4,-12)" width="40" x="1061" y="615"/></g>
 <g id="111001193">
  <use class="kv-1" height="20" transform="rotate(90,1290,615) scale(1,1) translate(-4,-12)" width="40" x="1290" xlink:href="#GroundDisconnector:bn_接地刀闸12_0" y="615"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1290,615) scale(1,1) translate(-4,-12)" width="40" x="1290" y="615"/></g>
 <g id="111001194">
  <use class="kv-1" height="20" transform="rotate(90,1403,615) scale(1,1) translate(-4,-12)" width="40" x="1403" xlink:href="#GroundDisconnector:bn_接地刀闸12_0" y="615"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1403,615) scale(1,1) translate(-4,-12)" width="40" x="1403" y="615"/></g>
 <g id="111001210">
  <use class="kv-1" height="20" transform="rotate(90,1539,615) scale(1,1) translate(-4,-12)" width="40" x="1539" xlink:href="#GroundDisconnector:bn_接地刀闸12_0" y="615"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1539,615) scale(1,1) translate(-4,-12)" width="40" x="1539" y="615"/></g>
 <g id="111001461">
  <use class="kv-1" height="20" transform="rotate(90,2272,614) scale(1,1) translate(-4,-12)" width="40" x="2272" xlink:href="#GroundDisconnector:bn_接地刀闸12_0" y="614"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,2272,614) scale(1,1) translate(-4,-12)" width="40" x="2272" y="614"/></g>
 <g id="111001462">
  <use class="kv-1" height="20" transform="rotate(90,1913,615) scale(1,1) translate(-4,-12)" width="40" x="1913" xlink:href="#GroundDisconnector:bn_接地刀闸12_0" y="615"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1913,615) scale(1,1) translate(-4,-12)" width="40" x="1913" y="615"/></g>
 <g id="111001463">
  <use class="kv-1" height="20" transform="rotate(90,2037,615) scale(1,1) translate(-4,-12)" width="40" x="2037" xlink:href="#GroundDisconnector:bn_接地刀闸12_0" y="615"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,2037,615) scale(1,1) translate(-4,-12)" width="40" x="2037" y="615"/></g>
 <g id="111001464">
  <use class="kv-1" height="20" transform="rotate(90,2395,614) scale(1,1) translate(-4,-12)" width="40" x="2395" xlink:href="#GroundDisconnector:bn_接地刀闸12_0" y="614"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,2395,614) scale(1,1) translate(-4,-12)" width="40" x="2395" y="614"/></g>
 <g id="111001465">
  <use class="kv-1" height="20" transform="rotate(90,2510,614) scale(1,1) translate(-4,-12)" width="40" x="2510" xlink:href="#GroundDisconnector:bn_接地刀闸12_0" y="614"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,2510,614) scale(1,1) translate(-4,-12)" width="40" x="2510" y="614"/></g>
 <g id="111001466">
  <use class="kv-1" height="20" transform="rotate(90,2632,614) scale(1,1) translate(-4,-12)" width="40" x="2632" xlink:href="#GroundDisconnector:bn_接地刀闸12_0" y="614"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,2632,614) scale(1,1) translate(-4,-12)" width="40" x="2632" y="614"/></g>
 <g id="111001467">
  <use class="kv-1" height="20" transform="rotate(90,2745,614) scale(1,1) translate(-4,-12)" width="40" x="2745" xlink:href="#GroundDisconnector:bn_接地刀闸12_0" y="614"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,2745,614) scale(1,1) translate(-4,-12)" width="40" x="2745" y="614"/></g>
 <g id="111001468">
  <use class="kv-1" height="20" transform="rotate(90,2881,614) scale(1,1) translate(-4,-12)" width="40" x="2881" xlink:href="#GroundDisconnector:bn_接地刀闸12_0" y="614"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,2881,614) scale(1,1) translate(-4,-12)" width="40" x="2881" y="614"/></g>
</g>
<g id="Transformer2_Layer">
<g id="102000121">
 <g id="1020001210">
  <use class="kv-1" height="86" transform="rotate(0,1120,214) scale(1,1) translate(-38,-40)" width="60" x="1120" xlink:href="#Transformer2:bn_益州升压站_0" y="214"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="1020001211">
  <use class="kv-1" height="86" transform="rotate(0,1120,214) scale(1,1) translate(-38,-40)" width="60" x="1120" xlink:href="#Transformer2:bn_益州升压站_1" y="214"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <metadata>
  <cge:PSR_Ref ObjectID="-1" ObjectName="-1" Plane="0"/>
 </metadata>
<rect fill="white" height="86" opacity="0" stroke="white" transform="rotate(0,1120,214) scale(1,1) translate(-38,-40)" width="60" x="1120" y="214"/></g>
<g id="102000250">
 <g id="1020002500">
  <use class="kv-1" height="86" transform="rotate(0,2397,230) scale(1,1) translate(-38,-40)" width="60" x="2397" xlink:href="#Transformer2:bn_益州升压站_0" y="230"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="1020002501">
  <use class="kv-1" height="86" transform="rotate(0,2397,230) scale(1,1) translate(-38,-40)" width="60" x="2397" xlink:href="#Transformer2:bn_益州升压站_1" y="230"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <metadata>
  <cge:PSR_Ref ObjectID="-1" ObjectName="-1" Plane="0"/>
 </metadata>
<rect fill="white" height="86" opacity="0" stroke="white" transform="rotate(0,2397,230) scale(1,1) translate(-38,-40)" width="60" x="2397" y="230"/></g>
</g>
<g id="DollyBreaker_Layer">
 <g id="110000066">
  <use class="kv-1" height="74" transform="rotate(360,775,503) scale(1.15,1.133) translate(-111.087,-96.0459)" width="20" x="775" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="503"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,775,503) scale(1.15,1.133) translate(-111.087,-96.0459)" width="20" x="775" y="503"/></g>
 <g id="110000066">
  <use class="kv-1" height="74" transform="rotate(360,775,503) scale(1.15,1.133) translate(-111.087,-96.0459)" width="20" x="775" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="503"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,775,503) scale(1.15,1.133) translate(-111.087,-96.0459)" width="20" x="775" y="503"/></g>
 <g id="110000079">
  <use class="kv-1" height="74" transform="rotate(360,534,503) scale(1.15,1.133) translate(-79.6522,-96.0459)" width="20" x="534" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="503"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,534,503) scale(1.15,1.133) translate(-79.6522,-96.0459)" width="20" x="534" y="503"/></g>
 <g id="110000079">
  <use class="kv-1" height="74" transform="rotate(360,534,503) scale(1.15,1.133) translate(-79.6522,-96.0459)" width="20" x="534" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="503"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,534,503) scale(1.15,1.133) translate(-79.6522,-96.0459)" width="20" x="534" y="503"/></g>
 <g id="110000088">
  <use class="kv-1" height="74" transform="rotate(360,658,503) scale(1.15,1.133) translate(-95.8261,-96.0459)" width="20" x="658" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="503"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,658,503) scale(1.15,1.133) translate(-95.8261,-96.0459)" width="20" x="658" y="503"/></g>
 <g id="110000088">
  <use class="kv-1" height="74" transform="rotate(360,658,503) scale(1.15,1.133) translate(-95.8261,-96.0459)" width="20" x="658" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="503"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,658,503) scale(1.15,1.133) translate(-95.8261,-96.0459)" width="20" x="658" y="503"/></g>
 <g id="110001113">
  <use class="kv-1" height="74" transform="rotate(360,898,503) scale(1.15,1.133) translate(-127.13,-96.0459)" width="20" x="898" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="503"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,898,503) scale(1.15,1.133) translate(-127.13,-96.0459)" width="20" x="898" y="503"/></g>
 <g id="110001113">
  <use class="kv-1" height="74" transform="rotate(360,898,503) scale(1.15,1.133) translate(-127.13,-96.0459)" width="20" x="898" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="503"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,898,503) scale(1.15,1.133) translate(-127.13,-96.0459)" width="20" x="898" y="503"/></g>
 <g id="110001161">
  <use class="kv-1" height="74" transform="rotate(360,1013,503) scale(1.15,1.133) translate(-142.13,-96.0459)" width="20" x="1013" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="503"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,1013,503) scale(1.15,1.133) translate(-142.13,-96.0459)" width="20" x="1013" y="503"/></g>
 <g id="110001161">
  <use class="kv-1" height="74" transform="rotate(360,1013,503) scale(1.15,1.133) translate(-142.13,-96.0459)" width="20" x="1013" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="503"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,1013,503) scale(1.15,1.133) translate(-142.13,-96.0459)" width="20" x="1013" y="503"/></g>
 <g id="110001191">
  <use class="kv-1" height="74" transform="rotate(360,1242,503) scale(1.15,1.133) translate(-172,-96.0459)" width="20" x="1242" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="503"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,1242,503) scale(1.15,1.133) translate(-172,-96.0459)" width="20" x="1242" y="503"/></g>
 <g id="110001191">
  <use class="kv-1" height="74" transform="rotate(360,1242,503) scale(1.15,1.133) translate(-172,-96.0459)" width="20" x="1242" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="503"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,1242,503) scale(1.15,1.133) translate(-172,-96.0459)" width="20" x="1242" y="503"/></g>
 <g id="110001192">
  <use class="kv-1" height="74" transform="rotate(360,1355,503) scale(1.15,1.133) translate(-186.739,-96.0459)" width="20" x="1355" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="503"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,1355,503) scale(1.15,1.133) translate(-186.739,-96.0459)" width="20" x="1355" y="503"/></g>
 <g id="110001192">
  <use class="kv-1" height="74" transform="rotate(360,1355,503) scale(1.15,1.133) translate(-186.739,-96.0459)" width="20" x="1355" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="503"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,1355,503) scale(1.15,1.133) translate(-186.739,-96.0459)" width="20" x="1355" y="503"/></g>
 <g id="110001209">
  <use class="kv-1" height="74" transform="rotate(360,1491,503) scale(1.15,1.133) translate(-204.478,-96.0459)" width="20" x="1491" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="503"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,1491,503) scale(1.15,1.133) translate(-204.478,-96.0459)" width="20" x="1491" y="503"/></g>
 <g id="110001209">
  <use class="kv-1" height="74" transform="rotate(360,1491,503) scale(1.15,1.133) translate(-204.478,-96.0459)" width="20" x="1491" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="503"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,1491,503) scale(1.15,1.133) translate(-204.478,-96.0459)" width="20" x="1491" y="503"/></g>
 <g id="110001453">
  <use class="kv-1" height="74" transform="rotate(360,2224,502) scale(1.15,1.133) translate(-300.087,-95.9285)" width="20" x="2224" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="502"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,2224,502) scale(1.15,1.133) translate(-300.087,-95.9285)" width="20" x="2224" y="502"/></g>
 <g id="110001453">
  <use class="kv-1" height="74" transform="rotate(360,2224,502) scale(1.15,1.133) translate(-300.087,-95.9285)" width="20" x="2224" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="502"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,2224,502) scale(1.15,1.133) translate(-300.087,-95.9285)" width="20" x="2224" y="502"/></g>
 <g id="110001454">
  <use class="kv-1" height="74" transform="rotate(360,1860,503) scale(1.15,1.133) translate(-252.609,-96.0459)" width="20" x="1860" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="503"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,1860,503) scale(1.15,1.133) translate(-252.609,-96.0459)" width="20" x="1860" y="503"/></g>
 <g id="110001454">
  <use class="kv-1" height="74" transform="rotate(360,1860,503) scale(1.15,1.133) translate(-252.609,-96.0459)" width="20" x="1860" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="503"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,1860,503) scale(1.15,1.133) translate(-252.609,-96.0459)" width="20" x="1860" y="503"/></g>
 <g id="110001455">
  <use class="kv-1" height="74" transform="rotate(360,1984,503) scale(1.15,1.133) translate(-268.783,-96.0459)" width="20" x="1984" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="503"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,1984,503) scale(1.15,1.133) translate(-268.783,-96.0459)" width="20" x="1984" y="503"/></g>
 <g id="110001455">
  <use class="kv-1" height="74" transform="rotate(360,1984,503) scale(1.15,1.133) translate(-268.783,-96.0459)" width="20" x="1984" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="503"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,1984,503) scale(1.15,1.133) translate(-268.783,-96.0459)" width="20" x="1984" y="503"/></g>
 <g id="110001456">
  <use class="kv-1" height="74" transform="rotate(360,2347,502) scale(1.15,1.133) translate(-316.13,-95.9285)" width="20" x="2347" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="502"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,2347,502) scale(1.15,1.133) translate(-316.13,-95.9285)" width="20" x="2347" y="502"/></g>
 <g id="110001456">
  <use class="kv-1" height="74" transform="rotate(360,2347,502) scale(1.15,1.133) translate(-316.13,-95.9285)" width="20" x="2347" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="502"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,2347,502) scale(1.15,1.133) translate(-316.13,-95.9285)" width="20" x="2347" y="502"/></g>
 <g id="110001457">
  <use class="kv-1" height="74" transform="rotate(360,2462,502) scale(1.15,1.133) translate(-331.13,-95.9285)" width="20" x="2462" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="502"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,2462,502) scale(1.15,1.133) translate(-331.13,-95.9285)" width="20" x="2462" y="502"/></g>
 <g id="110001457">
  <use class="kv-1" height="74" transform="rotate(360,2462,502) scale(1.15,1.133) translate(-331.13,-95.9285)" width="20" x="2462" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="502"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,2462,502) scale(1.15,1.133) translate(-331.13,-95.9285)" width="20" x="2462" y="502"/></g>
 <g id="110001458">
  <use class="kv-1" height="74" transform="rotate(360,2584,502) scale(1.15,1.133) translate(-347.043,-95.9285)" width="20" x="2584" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="502"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,2584,502) scale(1.15,1.133) translate(-347.043,-95.9285)" width="20" x="2584" y="502"/></g>
 <g id="110001458">
  <use class="kv-1" height="74" transform="rotate(360,2584,502) scale(1.15,1.133) translate(-347.043,-95.9285)" width="20" x="2584" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="502"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,2584,502) scale(1.15,1.133) translate(-347.043,-95.9285)" width="20" x="2584" y="502"/></g>
 <g id="110001459">
  <use class="kv-1" height="74" transform="rotate(360,2697,502) scale(1.15,1.133) translate(-361.783,-95.9285)" width="20" x="2697" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="502"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,2697,502) scale(1.15,1.133) translate(-361.783,-95.9285)" width="20" x="2697" y="502"/></g>
 <g id="110001459">
  <use class="kv-1" height="74" transform="rotate(360,2697,502) scale(1.15,1.133) translate(-361.783,-95.9285)" width="20" x="2697" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="502"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,2697,502) scale(1.15,1.133) translate(-361.783,-95.9285)" width="20" x="2697" y="502"/></g>
 <g id="110001460">
  <use class="kv-1" height="74" transform="rotate(360,2833,502) scale(1.15,1.133) translate(-379.522,-95.9285)" width="20" x="2833" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="502"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,2833,502) scale(1.15,1.133) translate(-379.522,-95.9285)" width="20" x="2833" y="502"/></g>
 <g id="110001460">
  <use class="kv-1" height="74" transform="rotate(360,2833,502) scale(1.15,1.133) translate(-379.522,-95.9285)" width="20" x="2833" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="502"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,2833,502) scale(1.15,1.133) translate(-379.522,-95.9285)" width="20" x="2833" y="502"/></g>
 <g id="110001482">
  <use class="kv-1" height="74" transform="rotate(360,1618,503) scale(1.15,1.133) translate(-221.043,-96.0459)" width="20" x="1618" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="503"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,1618,503) scale(1.15,1.133) translate(-221.043,-96.0459)" width="20" x="1618" y="503"/></g>
 <g id="110001482">
  <use class="kv-1" height="74" transform="rotate(360,1618,503) scale(1.15,1.133) translate(-221.043,-96.0459)" width="20" x="1618" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="503"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,1618,503) scale(1.15,1.133) translate(-221.043,-96.0459)" width="20" x="1618" y="503"/></g>
</g>
<g id="Load_Layer">
 <g id="32000946">
 <path d="M 658 741 L 658 809" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 658 741 L 658 809" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32000951">
 <path d="M 775 736 L 775 810" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 775 736 L 775 810" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32001020">
 <path d="M 534 741 L 534 810" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 534 741 L 534 810" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32001105">
 <path d="M 898 738 L 898 812" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 898 738 L 898 812" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32001153">
 <path d="M 1013 736 L 1013 810" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 1013 736 L 1013 810" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32001175">
 <path d="M 1242 738 L 1242 812" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 1242 738 L 1242 812" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32001176">
 <path d="M 1355 736 L 1355 810" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 1355 736 L 1355 810" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32001201">
 <path d="M 1491 734 L 1491 808" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 1491 734 L 1491 808" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32001389">
 <path d="M 1984 743 L 1984 811" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 1984 743 L 1984 811" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32001390">
 <path d="M 2224 738 L 2224 812" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 2224 738 L 2224 812" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32001391">
 <path d="M 1860 743 L 1860 812" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 1860 743 L 1860 812" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32001392">
 <path d="M 2347 740 L 2347 814" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 2347 740 L 2347 814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32001393">
 <path d="M 2462 738 L 2462 812" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 2462 738 L 2462 812" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32001394">
 <path d="M 2584 740 L 2584 814" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 2584 740 L 2584 814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32001395">
 <path d="M 2697 738 L 2697 812" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 2697 738 L 2697 812" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32001396">
 <path d="M 2833 736 L 2833 810" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 2833 736 L 2833 810" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="PT_Layer">
 <g id="107001469">
  <use class="kv-1" height="100" transform="rotate(0,1420,250) scale(1,1) translate(-33,-74)" width="100" x="1420" xlink:href="#PT:告庄1号电压互感器_0" y="250"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="100" opacity="0" stroke="white" transform="rotate(0,1420,250) scale(1,1) translate(-33,-74)" width="100" x="1420" y="250"/></g>
 <g id="107001475">
  <use class="kv-1" height="100" transform="rotate(0,2101,250) scale(1,1) translate(-33,-74)" width="100" x="2101" xlink:href="#PT:告庄1号电压互感器_0" y="250"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="100" opacity="0" stroke="white" transform="rotate(0,2101,250) scale(1,1) translate(-33,-74)" width="100" x="2101" y="250"/></g>
 <g id="107001485">
  <use class="kv-1" height="44" transform="rotate(180,1210,696) scale(1.116,1.116) translate(-134.771,-113.344)" width="18" x="1210" xlink:href="#PT:bn_电压互感器008_0" y="696"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="44" opacity="0" stroke="white" transform="rotate(180,1210,696) scale(1.116,1.116) translate(-134.771,-113.344)" width="18" x="1210" y="696"/></g>
 <g id="107001494">
  <use class="kv-1" height="44" transform="rotate(180,2181,694) scale(1.116,1.116) translate(-235.699,-113.136)" width="18" x="2181" xlink:href="#PT:bn_电压互感器008_0" y="694"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="44" opacity="0" stroke="white" transform="rotate(180,2181,694) scale(1.116,1.116) translate(-235.699,-113.136)" width="18" x="2181" y="694"/></g>
</g>
<g id="GZP_Layer">
 <g id="135000442">
  <use class="kv-1" height="36" transform="rotate(0,271,693) scale(1,1) translate(-18,-18)" width="36" x="271" xlink:href="#GZP:gg_光子牌1_0" y="693"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,271,693) scale(1,1) translate(-18,-18)" width="36" x="271" y="693"/></g>
</g>
<g id="Status_Layer">
 <g id="126000441">
  <use class="kv-1" height="40" transform="rotate(0,346,266) scale(0.7,0.7) translate(118.286,94)" width="60" x="346" xlink:href="#Status:bn_数据封锁颜色显示_0" y="266"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,346,266) scale(0.7,0.7) translate(118.286,94)" width="60" x="346" y="266"/></g>
 <g id="126000440">
  <use class="kv-1" height="40" transform="rotate(0,274,266) scale(0.7,0.7) translate(87.4286,94)" width="60" x="274" xlink:href="#Status:bn_非实测颜色显示_0" y="266"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,274,266) scale(0.7,0.7) translate(87.4286,94)" width="60" x="274" y="266"/></g>
 <g id="126000439">
  <use class="kv-1" height="40" transform="rotate(0,199,266) scale(0.7,0.7) translate(55.2857,94)" width="60" x="199" xlink:href="#Status:bn_越限颜色显示_0" y="266"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,199,266) scale(0.7,0.7) translate(55.2857,94)" width="60" x="199" y="266"/></g>
 <g id="126000438">
  <use class="kv-1" height="40" transform="rotate(0,121,266) scale(0.7,0.7) translate(21.8571,94)" width="60" x="121" xlink:href="#Status:bn_不变化颜色显示_0" y="266"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,121,266) scale(0.7,0.7) translate(21.8571,94)" width="60" x="121" y="266"/></g>
 <g id="126000437">
  <use class="kv-1" height="40" transform="rotate(0,46,266) scale(0.7,0.7) translate(-10.2857,94)" width="60" x="46" xlink:href="#Status:bn_工况退出颜色显示_0" y="266"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,46,266) scale(0.7,0.7) translate(-10.2857,94)" width="60" x="46" y="266"/></g>
</g>
<g id="Clock_Layer">
 <g id="56000436">
  
 <metadata/></g>
</g>
<g id="Link_Layer">
 <g id="34000046">
 <path d="M 775 465 L 775 386" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000066_0" Pin1InfoVect0LinkObjId="30000019_0" Plane="0"/>
  </metadata>
 <path d="M 775 465 L 775 386" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000047">
 <path d="M 1420 282 L 1420 250" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101001092_1" Pin1InfoVect0LinkObjId="107001469_0" Plane="0"/>
  </metadata>
 <path d="M 1420 282 L 1420 250" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000048">
 <path d="M 775 585 L 775 736" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000051_1" Pin0InfoVect1LinkObjId="34000050_0" Pin1InfoVect0LinkObjId="32000951_0" Plane="0"/>
  </metadata>
 <path d="M 775 585 L 775 736" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000050">
 <path d="M 775 585 L 823 585 L 823 615" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000051_1" Pin0InfoVect1LinkObjId="34000048_0" Pin1InfoVect0LinkObjId="111000069_0" Plane="0"/>
  </metadata>
 <path d="M 775 585 L 823 585 L 823 615" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000051">
 <path d="M 775 539 L 775 585" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000066_1" Pin1InfoVect0LinkObjId="34000048_0" Pin1InfoVect1LinkObjId="34000050_0" Plane="0"/>
  </metadata>
 <path d="M 775 539 L 775 585" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000055">
 <path d="M 1420 345 L 1420 325" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101001092_0" Pin1InfoVect0LinkObjId="34000309_0" Plane="0"/>
  </metadata>
 <path d="M 1420 345 L 1420 325" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000056">
 <path d="M 534 465 L 534 386" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000079_0" Pin1InfoVect0LinkObjId="30000019_0" Plane="0"/>
  </metadata>
 <path d="M 534 465 L 534 386" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000057">
 <path d="M 534 587 L 534 741" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000059_0" Pin0InfoVect1LinkObjId="34000060_1" Pin1InfoVect0LinkObjId="32001020_0" Plane="0"/>
  </metadata>
 <path d="M 534 587 L 534 741" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000059">
 <path d="M 534 587 L 587 587 L 587 615" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000057_0" Pin0InfoVect1LinkObjId="34000060_1" Pin1InfoVect0LinkObjId="111000071_0" Plane="0"/>
  </metadata>
 <path d="M 534 587 L 587 587 L 587 615" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000060">
 <path d="M 534 539 L 534 587" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000079_1" Pin1InfoVect0LinkObjId="34000059_0" Pin1InfoVect1LinkObjId="34000057_0" Plane="0"/>
  </metadata>
 <path d="M 534 539 L 534 587" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000084">
 <path d="M 658 465 L 658 386" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000088_0" Pin1InfoVect0LinkObjId="30000019_0" Plane="0"/>
  </metadata>
 <path d="M 658 465 L 658 386" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000085">
 <path d="M 658 585 L 658 741" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000086_0" Pin0InfoVect1LinkObjId="34000087_1" Pin1InfoVect0LinkObjId="32000946_0" Plane="0"/>
  </metadata>
 <path d="M 658 585 L 658 741" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000086">
 <path d="M 658 585 L 711 585 L 711 615" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000085_0" Pin0InfoVect1LinkObjId="34000087_1" Pin1InfoVect0LinkObjId="111000089_0" Plane="0"/>
  </metadata>
 <path d="M 658 585 L 711 585 L 711 615" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000087">
 <path d="M 658 539 L 658 585" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000088_1" Pin1InfoVect0LinkObjId="34000085_0" Pin1InfoVect1LinkObjId="34000086_0" Plane="0"/>
  </metadata>
 <path d="M 658 539 L 658 585" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000104">
 <path d="M 1120 347 L 1120 386" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101001090_0" Pin1InfoVect0LinkObjId="30000019_0" Plane="0"/>
  </metadata>
 <path d="M 1120 347 L 1120 386" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000165">
 <path d="M 1618 465 L 1618 386" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001482_0" Pin1InfoVect0LinkObjId="30000019_0" Plane="0"/>
  </metadata>
 <path d="M 1618 465 L 1618 386" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000166">
 <path d="M 1618 539 L 1618 621" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001482_1" Pin1InfoVect0LinkObjId="34000182_0" Plane="0"/>
  </metadata>
 <path d="M 1618 539 L 1618 621" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000171">
 <path d="M 1737 527 L 1737 621" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101001480_0" Pin1InfoVect0LinkObjId="34000182_1" Plane="0"/>
  </metadata>
 <path d="M 1737 527 L 1737 621" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000184">
 <path d="M 1737 464 L 1737 386" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101001480_1" Pin1InfoVect0LinkObjId="30000175_0" Plane="0"/>
  </metadata>
 <path d="M 1737 464 L 1737 386" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000182">
 <path d="M 1618 621 L 1737 621" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000166_1" Pin1InfoVect0LinkObjId="34000171_1" Plane="0"/>
  </metadata>
 <path d="M 1618 621 L 1737 621" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000240">
 <path d="M 2397 353 L 2397 386" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101001098_0" Pin1InfoVect0LinkObjId="30000175_0" Plane="0"/>
  </metadata>
 <path d="M 2397 353 L 2397 386" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000309">
 <path d="M 1420 325 L 1420 386" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000055_1" Pin1InfoVect0LinkObjId="30000019_0" Plane="0"/>
  </metadata>
 <path d="M 1420 325 L 1420 386" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001109">
 <path d="M 898 465 L 898 386" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001113_0" Pin1InfoVect0LinkObjId="30000019_0" Plane="0"/>
  </metadata>
 <path d="M 898 465 L 898 386" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001110">
 <path d="M 898 587 L 898 738" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001112_1" Pin0InfoVect1LinkObjId="34001111_0" Pin1InfoVect0LinkObjId="32001105_0" Plane="0"/>
  </metadata>
 <path d="M 898 587 L 898 738" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001111">
 <path d="M 898 587 L 946 587 L 946 615" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001112_1" Pin0InfoVect1LinkObjId="34001110_0" Pin1InfoVect0LinkObjId="111001114_0" Plane="0"/>
  </metadata>
 <path d="M 898 587 L 946 587 L 946 615" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001112">
 <path d="M 898 539 L 898 587" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001113_1" Pin1InfoVect0LinkObjId="34001110_0" Pin1InfoVect1LinkObjId="34001111_0" Plane="0"/>
  </metadata>
 <path d="M 898 539 L 898 587" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001157">
 <path d="M 1013 465 L 1013 386" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001161_0" Pin1InfoVect0LinkObjId="30000019_0" Plane="0"/>
  </metadata>
 <path d="M 1013 465 L 1013 386" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001158">
 <path d="M 1013 585 L 1013 736" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001159_0" Pin0InfoVect1LinkObjId="34001160_1" Pin1InfoVect0LinkObjId="32001153_0" Plane="0"/>
  </metadata>
 <path d="M 1013 585 L 1013 736" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001159">
 <path d="M 1013 585 L 1061 585 L 1061 615" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001158_0" Pin0InfoVect1LinkObjId="34001160_1" Pin1InfoVect0LinkObjId="111001162_0" Plane="0"/>
  </metadata>
 <path d="M 1013 585 L 1061 585 L 1061 615" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001160">
 <path d="M 1013 539 L 1013 585" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001161_1" Pin1InfoVect0LinkObjId="34001159_0" Pin1InfoVect1LinkObjId="34001158_0" Plane="0"/>
  </metadata>
 <path d="M 1013 539 L 1013 585" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001183">
 <path d="M 1242 465 L 1242 386" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001191_0" Pin1InfoVect0LinkObjId="30000019_0" Plane="0"/>
  </metadata>
 <path d="M 1242 465 L 1242 386" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001184">
 <path d="M 1242 587 L 1242 738" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001185_0" Pin0InfoVect1LinkObjId="34001186_1" Pin0InfoVect2LinkObjId="34001488_0" Pin1InfoVect0LinkObjId="32001175_0" Plane="0"/>
  </metadata>
 <path d="M 1242 587 L 1242 738" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001185">
 <path d="M 1242 587 L 1290 587 L 1290 615" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001184_0" Pin0InfoVect1LinkObjId="34001186_1" Pin0InfoVect2LinkObjId="34001488_0" Pin1InfoVect0LinkObjId="111001193_0" Plane="0"/>
  </metadata>
 <path d="M 1242 587 L 1290 587 L 1290 615" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001186">
 <path d="M 1242 539 L 1242 587" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001191_1" Pin1InfoVect0LinkObjId="34001184_0" Pin1InfoVect1LinkObjId="34001185_0" Pin1InfoVect2LinkObjId="34001488_0" Plane="0"/>
  </metadata>
 <path d="M 1242 539 L 1242 587" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001187">
 <path d="M 1355 465 L 1355 386" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001192_0" Pin1InfoVect0LinkObjId="30000019_0" Plane="0"/>
  </metadata>
 <path d="M 1355 465 L 1355 386" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001188">
 <path d="M 1355 585 L 1355 736" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001189_0" Pin0InfoVect1LinkObjId="34001190_1" Pin1InfoVect0LinkObjId="32001176_0" Plane="0"/>
  </metadata>
 <path d="M 1355 585 L 1355 736" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001189">
 <path d="M 1355 585 L 1403 585 L 1403 615" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001188_0" Pin0InfoVect1LinkObjId="34001190_1" Pin1InfoVect0LinkObjId="111001194_0" Plane="0"/>
  </metadata>
 <path d="M 1355 585 L 1403 585 L 1403 615" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001190">
 <path d="M 1355 539 L 1355 585" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001192_1" Pin1InfoVect0LinkObjId="34001189_0" Pin1InfoVect1LinkObjId="34001188_0" Plane="0"/>
  </metadata>
 <path d="M 1355 539 L 1355 585" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001205">
 <path d="M 1491 465 L 1491 386" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001209_0" Pin1InfoVect0LinkObjId="30000019_0" Plane="0"/>
  </metadata>
 <path d="M 1491 465 L 1491 386" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001206">
 <path d="M 1491 583 L 1491 734" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001207_0" Pin0InfoVect1LinkObjId="34001208_1" Pin1InfoVect0LinkObjId="32001201_0" Plane="0"/>
  </metadata>
 <path d="M 1491 583 L 1491 734" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001207">
 <path d="M 1491 583 L 1539 583 L 1539 615" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001206_0" Pin0InfoVect1LinkObjId="34001208_1" Pin1InfoVect0LinkObjId="111001210_0" Plane="0"/>
  </metadata>
 <path d="M 1491 583 L 1539 583 L 1539 615" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001208">
 <path d="M 1491 539 L 1491 583" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001209_1" Pin1InfoVect0LinkObjId="34001207_0" Pin1InfoVect1LinkObjId="34001206_0" Plane="0"/>
  </metadata>
 <path d="M 1491 539 L 1491 583" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001421">
 <path d="M 2224 464 L 2224 386" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001453_0" Pin1InfoVect0LinkObjId="30000175_0" Plane="0"/>
  </metadata>
 <path d="M 2224 464 L 2224 386" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001422">
 <path d="M 2224 587 L 2224 738" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001423_0" Pin0InfoVect1LinkObjId="34001424_1" Pin0InfoVect2LinkObjId="34001495_0" Pin1InfoVect0LinkObjId="32001390_0" Plane="0"/>
  </metadata>
 <path d="M 2224 587 L 2224 738" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001423">
 <path d="M 2224 587 L 2272 587 L 2272 614" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001422_0" Pin0InfoVect1LinkObjId="34001424_1" Pin0InfoVect2LinkObjId="34001495_0" Pin1InfoVect0LinkObjId="111001461_0" Plane="0"/>
  </metadata>
 <path d="M 2224 587 L 2272 587 L 2272 614" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001424">
 <path d="M 2224 538 L 2224 587" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001453_1" Pin1InfoVect0LinkObjId="34001422_0" Pin1InfoVect1LinkObjId="34001423_0" Pin1InfoVect2LinkObjId="34001495_0" Plane="0"/>
  </metadata>
 <path d="M 2224 538 L 2224 587" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001425">
 <path d="M 1860 465 L 1860 386" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001454_0" Pin1InfoVect0LinkObjId="30000175_0" Plane="0"/>
  </metadata>
 <path d="M 1860 465 L 1860 386" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001426">
 <path d="M 1860 589 L 1860 743" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001427_0" Pin0InfoVect1LinkObjId="34001428_1" Pin1InfoVect0LinkObjId="32001391_0" Plane="0"/>
  </metadata>
 <path d="M 1860 589 L 1860 743" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001427">
 <path d="M 1860 589 L 1913 589 L 1913 615" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001426_0" Pin0InfoVect1LinkObjId="34001428_1" Pin1InfoVect0LinkObjId="111001462_0" Plane="0"/>
  </metadata>
 <path d="M 1860 589 L 1913 589 L 1913 615" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001428">
 <path d="M 1860 539 L 1860 589" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001454_1" Pin1InfoVect0LinkObjId="34001426_0" Pin1InfoVect1LinkObjId="34001427_0" Plane="0"/>
  </metadata>
 <path d="M 1860 539 L 1860 589" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001429">
 <path d="M 1984 465 L 1984 386" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001455_0" Pin1InfoVect0LinkObjId="30000175_0" Plane="0"/>
  </metadata>
 <path d="M 1984 465 L 1984 386" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001430">
 <path d="M 1984 587 L 1984 743" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001431_0" Pin0InfoVect1LinkObjId="34001432_1" Pin1InfoVect0LinkObjId="32001389_0" Plane="0"/>
  </metadata>
 <path d="M 1984 587 L 1984 743" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001431">
 <path d="M 1984 587 L 2037 587 L 2037 615" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001430_0" Pin0InfoVect1LinkObjId="34001432_1" Pin1InfoVect0LinkObjId="111001463_0" Plane="0"/>
  </metadata>
 <path d="M 1984 587 L 2037 587 L 2037 615" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001432">
 <path d="M 1984 539 L 1984 587" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001455_1" Pin1InfoVect0LinkObjId="34001430_0" Pin1InfoVect1LinkObjId="34001431_0" Plane="0"/>
  </metadata>
 <path d="M 1984 539 L 1984 587" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001433">
 <path d="M 2347 464 L 2347 386" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001456_0" Pin1InfoVect0LinkObjId="30000175_0" Plane="0"/>
  </metadata>
 <path d="M 2347 464 L 2347 386" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001434">
 <path d="M 2347 589 L 2347 740" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001435_0" Pin0InfoVect1LinkObjId="34001436_1" Pin1InfoVect0LinkObjId="32001392_0" Plane="0"/>
  </metadata>
 <path d="M 2347 589 L 2347 740" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001435">
 <path d="M 2347 589 L 2395 589 L 2395 614" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001434_0" Pin0InfoVect1LinkObjId="34001436_1" Pin1InfoVect0LinkObjId="111001464_0" Plane="0"/>
  </metadata>
 <path d="M 2347 589 L 2395 589 L 2395 614" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001436">
 <path d="M 2347 538 L 2347 589" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001456_1" Pin1InfoVect0LinkObjId="34001434_0" Pin1InfoVect1LinkObjId="34001435_0" Plane="0"/>
  </metadata>
 <path d="M 2347 538 L 2347 589" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001437">
 <path d="M 2462 464 L 2462 386" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001457_0" Pin1InfoVect0LinkObjId="30000175_0" Plane="0"/>
  </metadata>
 <path d="M 2462 464 L 2462 386" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001438">
 <path d="M 2462 587 L 2462 738" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001440_1" Pin0InfoVect1LinkObjId="34001439_0" Pin1InfoVect0LinkObjId="32001393_0" Plane="0"/>
  </metadata>
 <path d="M 2462 587 L 2462 738" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001439">
 <path d="M 2462 587 L 2510 587 L 2510 614" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001440_1" Pin0InfoVect1LinkObjId="34001438_0" Pin1InfoVect0LinkObjId="111001465_0" Plane="0"/>
  </metadata>
 <path d="M 2462 587 L 2510 587 L 2510 614" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001440">
 <path d="M 2462 538 L 2462 587" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001457_1" Pin1InfoVect0LinkObjId="34001438_0" Pin1InfoVect1LinkObjId="34001439_0" Plane="0"/>
  </metadata>
 <path d="M 2462 538 L 2462 587" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001441">
 <path d="M 2584 464 L 2584 386" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001458_0" Pin1InfoVect0LinkObjId="30000175_0" Plane="0"/>
  </metadata>
 <path d="M 2584 464 L 2584 386" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001442">
 <path d="M 2584 589 L 2584 740" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001443_0" Pin0InfoVect1LinkObjId="34001444_1" Pin1InfoVect0LinkObjId="32001394_0" Plane="0"/>
  </metadata>
 <path d="M 2584 589 L 2584 740" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001443">
 <path d="M 2584 589 L 2632 589 L 2632 614" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001442_0" Pin0InfoVect1LinkObjId="34001444_1" Pin1InfoVect0LinkObjId="111001466_0" Plane="0"/>
  </metadata>
 <path d="M 2584 589 L 2632 589 L 2632 614" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001444">
 <path d="M 2584 538 L 2584 589" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001458_1" Pin1InfoVect0LinkObjId="34001442_0" Pin1InfoVect1LinkObjId="34001443_0" Plane="0"/>
  </metadata>
 <path d="M 2584 538 L 2584 589" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001445">
 <path d="M 2697 464 L 2697 386" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001459_0" Pin1InfoVect0LinkObjId="30000175_0" Plane="0"/>
  </metadata>
 <path d="M 2697 464 L 2697 386" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001446">
 <path d="M 2697 587 L 2697 738" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001447_0" Pin0InfoVect1LinkObjId="34001448_1" Pin1InfoVect0LinkObjId="32001395_0" Plane="0"/>
  </metadata>
 <path d="M 2697 587 L 2697 738" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001447">
 <path d="M 2697 587 L 2745 587 L 2745 614" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001446_0" Pin0InfoVect1LinkObjId="34001448_1" Pin1InfoVect0LinkObjId="111001467_0" Plane="0"/>
  </metadata>
 <path d="M 2697 587 L 2745 587 L 2745 614" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001448">
 <path d="M 2697 538 L 2697 587" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001459_1" Pin1InfoVect0LinkObjId="34001446_0" Pin1InfoVect1LinkObjId="34001447_0" Plane="0"/>
  </metadata>
 <path d="M 2697 538 L 2697 587" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001449">
 <path d="M 2833 464 L 2833 386" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001460_0" Pin1InfoVect0LinkObjId="30000175_0" Plane="0"/>
  </metadata>
 <path d="M 2833 464 L 2833 386" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001450">
 <path d="M 2833 585 L 2833 736" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001451_0" Pin0InfoVect1LinkObjId="34001452_1" Pin1InfoVect0LinkObjId="32001396_0" Plane="0"/>
  </metadata>
 <path d="M 2833 585 L 2833 736" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001451">
 <path d="M 2833 585 L 2881 585 L 2881 614" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001450_0" Pin0InfoVect1LinkObjId="34001452_1" Pin1InfoVect0LinkObjId="111001468_0" Plane="0"/>
  </metadata>
 <path d="M 2833 585 L 2881 585 L 2881 614" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001452">
 <path d="M 2833 538 L 2833 585" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001460_1" Pin1InfoVect0LinkObjId="34001450_0" Pin1InfoVect1LinkObjId="34001451_0" Plane="0"/>
  </metadata>
 <path d="M 2833 538 L 2833 585" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001471">
 <path d="M 2101 282 L 2101 250" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101001474_1" Pin1InfoVect0LinkObjId="107001475_0" Plane="0"/>
  </metadata>
 <path d="M 2101 282 L 2101 250" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001472">
 <path d="M 2101 345 L 2101 325" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101001474_0" Pin1InfoVect0LinkObjId="34001473_0" Plane="0"/>
  </metadata>
 <path d="M 2101 345 L 2101 325" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001473">
 <path d="M 2101 325 L 2101 386" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001472_1" Pin1InfoVect0LinkObjId="30000175_0" Plane="0"/>
  </metadata>
 <path d="M 2101 325 L 2101 386" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001483">
 <path d="M 1120 250 L 1120 284" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="102000121_1" Pin1InfoVect0LinkObjId="101001090_1" Plane="0"/>
  </metadata>
 <path d="M 1120 250 L 1120 284" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001484">
 <path d="M 2397 266 L 2397 290" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="102000250_1" Pin1InfoVect0LinkObjId="101001098_1" Plane="0"/>
  </metadata>
 <path d="M 2397 266 L 2397 290" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001488">
 <path d="M 1242 587 L 1210 587" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001184_0" Pin0InfoVect1LinkObjId="34001185_0" Pin0InfoVect2LinkObjId="34001186_1" Pin1InfoVect0LinkObjId="34001489_0" Plane="0"/>
  </metadata>
 <path d="M 1242 587 L 1210 587" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001489">
 <path d="M 1210 587 L 1210 610" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001488_1" Pin1InfoVect0LinkObjId="101001487_1" Plane="0"/>
  </metadata>
 <path d="M 1210 587 L 1210 610" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001490">
 <path d="M 1210 673 L 1210 696" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101001487_0" Pin1InfoVect0LinkObjId="107001485_0" Plane="0"/>
  </metadata>
 <path d="M 1210 673 L 1210 696" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001492">
 <path d="M 2181 671 L 2181 694" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101001493_0" Pin1InfoVect0LinkObjId="107001494_0" Plane="0"/>
  </metadata>
 <path d="M 2181 671 L 2181 694" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001495">
 <path d="M 2224 587 L 2181 587 L 2181 608" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34001422_0" Pin0InfoVect1LinkObjId="34001423_0" Pin0InfoVect2LinkObjId="34001424_1" Pin1InfoVect0LinkObjId="101001493_1" Plane="0"/>
  </metadata>
 <path d="M 2224 587 L 2181 587 L 2181 608" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="MeasurementValue_Layer">
 <g id="33000435">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="28" stroke="rgb(255,255,0)" writing-mode="lr" x="249" xml:space="preserve" y="653">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000434">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="28" stroke="rgb(255,255,0)" writing-mode="lr" x="249" xml:space="preserve" y="588">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000433">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="28" stroke="rgb(255,255,0)" writing-mode="lr" x="246" xml:space="preserve" y="851">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000432">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="28" stroke="rgb(255,255,0)" writing-mode="lr" x="246" xml:space="preserve" y="781">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000947">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="625" xml:space="preserve" y="837">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000948">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="625" xml:space="preserve" y="863">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000949">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="625" xml:space="preserve" y="889">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000952">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="765" xml:space="preserve" y="838">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000953">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="765" xml:space="preserve" y="864">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000954">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="765" xml:space="preserve" y="890">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001021">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="504" xml:space="preserve" y="838">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001022">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="504" xml:space="preserve" y="864">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001023">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="504" xml:space="preserve" y="890">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001062">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="562" xml:space="preserve" y="236">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001063">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="562" xml:space="preserve" y="162">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001064">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="562" xml:space="preserve" y="187">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001065">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="562" xml:space="preserve" y="210">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001066">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="562" xml:space="preserve" y="286">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001067">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="562" xml:space="preserve" y="260">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001069">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="564" xml:space="preserve" y="312">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001068">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="562" xml:space="preserve" y="337">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001078">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="2773" xml:space="preserve" y="227">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001079">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="2773" xml:space="preserve" y="153">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001080">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="2773" xml:space="preserve" y="178">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001081">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="2773" xml:space="preserve" y="201">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001082">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="2773" xml:space="preserve" y="277">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001083">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="2773" xml:space="preserve" y="251">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001085">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="2775" xml:space="preserve" y="303">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001084">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="2773" xml:space="preserve" y="328">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001106">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="888" xml:space="preserve" y="840">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001107">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="888" xml:space="preserve" y="866">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001108">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="888" xml:space="preserve" y="892">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001154">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1003" xml:space="preserve" y="838">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001155">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1003" xml:space="preserve" y="864">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001156">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1003" xml:space="preserve" y="890">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001177">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1232" xml:space="preserve" y="840">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001178">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1232" xml:space="preserve" y="866">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001179">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1232" xml:space="preserve" y="892">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001180">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1345" xml:space="preserve" y="838">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001181">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1345" xml:space="preserve" y="864">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001182">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1345" xml:space="preserve" y="890">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001202">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1481" xml:space="preserve" y="836">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001203">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1481" xml:space="preserve" y="862">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001204">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1481" xml:space="preserve" y="888">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001397">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1951" xml:space="preserve" y="839">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001398">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1951" xml:space="preserve" y="865">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001399">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1951" xml:space="preserve" y="891">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001400">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="2214" xml:space="preserve" y="840">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001401">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="2214" xml:space="preserve" y="866">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001402">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="2214" xml:space="preserve" y="892">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001403">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1805" xml:space="preserve" y="840">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001404">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1805" xml:space="preserve" y="866">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001405">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="1805" xml:space="preserve" y="892">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001406">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="2337" xml:space="preserve" y="842">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001407">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="2337" xml:space="preserve" y="868">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001408">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="2337" xml:space="preserve" y="894">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001409">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="2452" xml:space="preserve" y="840">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001410">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="2452" xml:space="preserve" y="866">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001411">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="2452" xml:space="preserve" y="892">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001412">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="2574" xml:space="preserve" y="842">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001413">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="2574" xml:space="preserve" y="868">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001414">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="2574" xml:space="preserve" y="894">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001415">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="2687" xml:space="preserve" y="840">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001416">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="2687" xml:space="preserve" y="866">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001417">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="2687" xml:space="preserve" y="892">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001418">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="2823" xml:space="preserve" y="838">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001419">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="2823" xml:space="preserve" y="864">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001420">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,0)" writing-mode="lr" x="2823" xml:space="preserve" y="890">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
</g>
<g id="Text_Layer">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="793" xml:space="preserve" y="522">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="796" xml:space="preserve" y="692">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="559" xml:space="preserve" y="688">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="552" xml:space="preserve" y="515">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="675" xml:space="preserve" y="688">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="675" xml:space="preserve" y="516">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="2714" xml:space="preserve" y="373">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="22" font-size="22" font-width="25" stroke="rgb(255,255,254)" writing-mode="lr" x="321" xml:space="preserve" y="335">数据</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="22" font-size="22" font-width="25" stroke="rgb(255,255,254)" writing-mode="lr" x="321" xml:space="preserve" y="357">封锁</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="22" font-size="22" font-width="25" stroke="rgb(255,255,254)" writing-mode="lr" x="247" xml:space="preserve" y="358">实测</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="22" font-size="22" font-width="25" stroke="rgb(255,255,254)" writing-mode="lr" x="263" xml:space="preserve" y="333">非</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="22" font-size="22" font-width="25" stroke="rgb(255,255,254)" writing-mode="lr" x="171" xml:space="preserve" y="339">越限</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="22" font-size="22" font-width="25" stroke="rgb(255,255,254)" writing-mode="lr" x="102" xml:space="preserve" y="355">变化</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="22" font-size="22" font-width="25" stroke="rgb(255,255,254)" writing-mode="lr" x="116" xml:space="preserve" y="330">不</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="22" font-size="22" font-width="25" stroke="rgb(255,255,254)" writing-mode="lr" x="18" xml:space="preserve" y="333">工况</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="22" font-size="22" font-width="25" stroke="rgb(255,255,254)" writing-mode="lr" x="18" xml:space="preserve" y="355">退出</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="30" font-size="30" font-width="33" stroke="rgb(255,255,254)" writing-mode="tb" x="58" xml:space="preserve" y="470">位</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="27" font-size="27" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="51" xml:space="preserve" y="652">总无功</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="27" font-size="27" font-width="29" stroke="rgb(255,0,0)" writing-mode="lr" x="318" xml:space="preserve" y="526">℃</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="27" font-size="27" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="242" xml:space="preserve" y="525">温度:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="27" font-size="27" font-width="29" stroke="rgb(255,0,0)" writing-mode="lr" x="189" xml:space="preserve" y="525">Hz</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="27" font-size="27" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="117" xml:space="preserve" y="525">频率:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="27" font-size="27" font-width="29" stroke="rgb(255,0,0)" writing-mode="lr" x="318" xml:space="preserve" y="465">kV</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="27" font-size="27" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="242" xml:space="preserve" y="465">电压:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="27" font-size="27" font-width="29" stroke="rgb(255,0,0)" writing-mode="lr" x="189" xml:space="preserve" y="465">A</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="27" font-size="27" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="117" xml:space="preserve" y="465">电流:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="27" font-size="27" font-width="29" stroke="rgb(255,0,0)" writing-mode="lr" x="318" xml:space="preserve" y="406">MVr</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="27" font-size="27" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="229" xml:space="preserve" y="406"> 无功:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="27" font-size="27" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="117" xml:space="preserve" y="406">有功:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="27" font-size="27" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="89" xml:space="preserve" y="708">事故总:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="27" font-size="27" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="55" xml:space="preserve" y="586">总有功</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="27" font-size="27" font-width="29" stroke="rgb(255,0,0)" writing-mode="lr" x="189" xml:space="preserve" y="406">MW</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="30" font-size="30" font-width="33" stroke="rgb(255,255,254)" writing-mode="tb" x="58" xml:space="preserve" y="401">单</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="56" font-size="37" font-width="37" stroke="rgb(0,0,0)" writing-mode="lr" x="121" xml:space="preserve" y="109">10kV4号开闭所</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="75" xml:space="preserve" y="783">合母电压</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="75" xml:space="preserve" y="849">控母电压</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="67" font-size="54" font-width="54" stroke="rgb(0,0,0)" writing-mode="lr" x="132" xml:space="preserve" y="114">35kV贺蚌变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="492" xml:space="preserve" y="374">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="607" xml:space="preserve" y="866">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="607" xml:space="preserve" y="840">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="607" xml:space="preserve" y="892">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="619" xml:space="preserve" y="949">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="737" xml:space="preserve" y="949">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="747" xml:space="preserve" y="840">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="747" xml:space="preserve" y="866">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="747" xml:space="preserve" y="892">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="464" xml:space="preserve" y="949">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="486" xml:space="preserve" y="837">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="486" xml:space="preserve" y="863">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="486" xml:space="preserve" y="889">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="516" xml:space="preserve" y="311">3U0</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="516" xml:space="preserve" y="260">Ub</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="516" xml:space="preserve" y="286">Uc</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="516" xml:space="preserve" y="236">Ua </text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="516" xml:space="preserve" y="210">Uca</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="516" xml:space="preserve" y="163">Uab</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="516" xml:space="preserve" y="188">Ubc</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="516" xml:space="preserve" y="337">F</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="2727" xml:space="preserve" y="302">3U0</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="2727" xml:space="preserve" y="251">Ub</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="2727" xml:space="preserve" y="277">Uc</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="2727" xml:space="preserve" y="227">Ua </text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="2727" xml:space="preserve" y="201">Uca</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="2727" xml:space="preserve" y="154">Uab</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="2727" xml:space="preserve" y="179">Ubc</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="2727" xml:space="preserve" y="328">F</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="1137" xml:space="preserve" y="335">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="1437" xml:space="preserve" y="333">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="2414" xml:space="preserve" y="341">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="916" xml:space="preserve" y="522">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="919" xml:space="preserve" y="692">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="887" xml:space="preserve" y="949">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="870" xml:space="preserve" y="842">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="870" xml:space="preserve" y="868">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="870" xml:space="preserve" y="894">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="1031" xml:space="preserve" y="522">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="1034" xml:space="preserve" y="692">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="995" xml:space="preserve" y="949">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="985" xml:space="preserve" y="840">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="985" xml:space="preserve" y="866">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="985" xml:space="preserve" y="892">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="1260" xml:space="preserve" y="522">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="1263" xml:space="preserve" y="692">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="1229" xml:space="preserve" y="949">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="1214" xml:space="preserve" y="842">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="1214" xml:space="preserve" y="868">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="1214" xml:space="preserve" y="894">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="1373" xml:space="preserve" y="522">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="1376" xml:space="preserve" y="692">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="1342" xml:space="preserve" y="949">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="1327" xml:space="preserve" y="840">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="1327" xml:space="preserve" y="866">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="1327" xml:space="preserve" y="892">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="1509" xml:space="preserve" y="522">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="1512" xml:space="preserve" y="692">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="1455" xml:space="preserve" y="949">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="1463" xml:space="preserve" y="838">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="1463" xml:space="preserve" y="864">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="1463" xml:space="preserve" y="890">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="2242" xml:space="preserve" y="521">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="2245" xml:space="preserve" y="691">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="1885" xml:space="preserve" y="688">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="1878" xml:space="preserve" y="515">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="2001" xml:space="preserve" y="688">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="2001" xml:space="preserve" y="516">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="1933" xml:space="preserve" y="868">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="1933" xml:space="preserve" y="842">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="1933" xml:space="preserve" y="894">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="1960" xml:space="preserve" y="949">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2187" xml:space="preserve" y="949">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="2196" xml:space="preserve" y="842">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="2196" xml:space="preserve" y="868">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="2196" xml:space="preserve" y="894">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="1751" xml:space="preserve" y="949">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="1787" xml:space="preserve" y="839">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="1787" xml:space="preserve" y="865">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="1787" xml:space="preserve" y="891">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="2365" xml:space="preserve" y="521">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="2368" xml:space="preserve" y="691">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2279" xml:space="preserve" y="949">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="2319" xml:space="preserve" y="844">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="2319" xml:space="preserve" y="870">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="2319" xml:space="preserve" y="896">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="2480" xml:space="preserve" y="521">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="2483" xml:space="preserve" y="691">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2435" xml:space="preserve" y="949">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="2434" xml:space="preserve" y="842">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="2434" xml:space="preserve" y="868">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="2434" xml:space="preserve" y="894">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="2602" xml:space="preserve" y="521">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="2605" xml:space="preserve" y="691">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2557" xml:space="preserve" y="949">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="2556" xml:space="preserve" y="844">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="2556" xml:space="preserve" y="870">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="2556" xml:space="preserve" y="896">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="2715" xml:space="preserve" y="521">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="2718" xml:space="preserve" y="691">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2709" xml:space="preserve" y="949">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="2669" xml:space="preserve" y="842">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="2669" xml:space="preserve" y="868">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="2669" xml:space="preserve" y="894">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="2851" xml:space="preserve" y="521">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="2854" xml:space="preserve" y="691">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2807" xml:space="preserve" y="949">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="2805" xml:space="preserve" y="840">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="2805" xml:space="preserve" y="866">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(35,169,128)" writing-mode="lr" x="2805" xml:space="preserve" y="892">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="2118" xml:space="preserve" y="333">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="1754" xml:space="preserve" y="515">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="1635" xml:space="preserve" y="515">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="1120" xml:space="preserve" y="659">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="2088" xml:space="preserve" y="659">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="489" xml:space="preserve" y="991">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="598" xml:space="preserve" y="991">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="757" xml:space="preserve" y="991">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="858" xml:space="preserve" y="991">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="1016" xml:space="preserve" y="991">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="1221" xml:space="preserve" y="991">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="1339" xml:space="preserve" y="991">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="1936" xml:space="preserve" y="991">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2184" xml:space="preserve" y="991">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2313" xml:space="preserve" y="991">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2415" xml:space="preserve" y="991">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2580" xml:space="preserve" y="991">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2690" xml:space="preserve" y="991">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="2833" xml:space="preserve" y="991">???</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(0,0,0)" writing-mode="lr" x="42" xml:space="preserve" y="1057">公共间隔继电保护远方操作</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="1051" xml:space="preserve" y="140">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,254)" writing-mode="lr" x="1050" xml:space="preserve" y="171">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="31" font-size="31" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="2324" xml:space="preserve" y="142">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,254)" writing-mode="lr" x="2340" xml:space="preserve" y="173">???</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href="勐海地区.sys.svg"><rect fill-opacity="0" height="108" stroke-opacity="0" stroke-width="1" width="396" x="13" y="27"/></g>
 <g ChangePicPlane="0," Plane="0" href="＃1告庄开闭所公共间隔软压板.fac.svg"><rect fill-opacity="0" height="48" stroke-opacity="0" stroke-width="1" width="382" x="21" y="1021"/></g>
</g>
</svg>