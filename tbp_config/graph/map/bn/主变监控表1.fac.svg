<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1600" id="thSvg" viewBox="0 0 3610 1600" width="3610">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="Status:bn_不变化颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(0,0,255)" height="1" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_不变化颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(85,0,255)" height="1" stroke="rgb(85,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_越限颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_越限颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:越下限告警_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,0,255)" height="1" stroke="rgb(170,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:越下限告警_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:正常状态显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,127)" height="1" stroke="rgb(255,255,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:正常状态显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(85,0,255)" height="1" stroke="rgb(85,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="-1" InitShowingPlane="0," fill="rgb(0,0,0)" height="1600" width="3610" x="0" y="0"/>
</g>
<g id="Other_Layer">
 
 
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1198" x2="1198" y1="308" y2="1539"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="602" x2="602" y1="306" y2="1539"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="382" x2="382" y1="308" y2="1541"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="500" x2="500" y1="631" y2="631"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1594" x2="1594" y1="245" y2="1539"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1396" x2="1396" y1="242" y2="1541"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="991" x2="991" y1="244" y2="1541"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="803" x2="803" y1="375" y2="1541"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="243" x2="243" y1="243" y2="1541"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="60" x2="1739" y1="241" y2="241"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="58" x2="58" y1="240" y2="1541"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1739" x2="1739" y1="241" y2="1541"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="60" x2="1594" y1="306" y2="306"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="60" x2="1741" y1="371" y2="371"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="245" x2="1594" y1="436" y2="436"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="60" x2="1741" y1="501" y2="501"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="243" x2="1594" y1="566" y2="566"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="60" x2="1738" y1="631" y2="631"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="246" x2="1594" y1="696" y2="696"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="60" x2="1736" y1="761" y2="761"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="244" x2="1594" y1="826" y2="826"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="60" x2="1738" y1="891" y2="891"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="244" x2="1595" y1="956" y2="956"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="60" x2="1738" y1="1021" y2="1021"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="60" x2="1738" y1="1086" y2="1086"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="60" x2="1740" y1="1151" y2="1151"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="60" x2="1738" y1="1216" y2="1216"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="60" x2="1738" y1="1281" y2="1281"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="60" x2="1740" y1="1346" y2="1346"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="60" x2="1738" y1="1411" y2="1411"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="245" x2="1594" y1="1476" y2="1476"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="2948" x2="2948" y1="308" y2="1539"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="2352" x2="2352" y1="306" y2="1541"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="2132" x2="2132" y1="308" y2="1541"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="2247" x2="2247" y1="631" y2="631"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="3344" x2="3344" y1="245" y2="1541"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="3146" x2="3146" y1="242" y2="1539"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="2741" x2="2741" y1="244" y2="1539"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="2553" x2="2553" y1="310" y2="1541"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1993" x2="1993" y1="243" y2="1539"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1810" x2="3489" y1="241" y2="241"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1808" x2="1808" y1="240" y2="1537"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="3489" x2="3489" y1="241" y2="1537"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1810" x2="3344" y1="306" y2="306"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1810" x2="3491" y1="371" y2="371"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1995" x2="3344" y1="436" y2="436"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1810" x2="3491" y1="501" y2="501"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1993" x2="3344" y1="566" y2="566"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1810" x2="3491" y1="631" y2="631"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1996" x2="3344" y1="696" y2="696"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1810" x2="3491" y1="761" y2="761"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1994" x2="3344" y1="826" y2="826"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1810" x2="3491" y1="891" y2="891"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1994" x2="3345" y1="956" y2="956"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1810" x2="3491" y1="1021" y2="1021"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1997" x2="3344" y1="1086" y2="1086"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1810" x2="3491" y1="1151" y2="1151"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1992" x2="3345" y1="1216" y2="1216"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1810" x2="3491" y1="1281" y2="1281"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1810" x2="3491" y1="1346" y2="1346"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1810" x2="3491" y1="1411" y2="1411"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1810" x2="3491" y1="1476" y2="1476"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="60" x2="1739" y1="1541" y2="1541"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1809" x2="3490" y1="1541" y2="1541"/>
</g>
<g id="Status_Layer">
 <g id="126000518">
  <use class="kv-1" height="40" transform="rotate(0,2431,188) scale(0.857,0.857) translate(375.64,11.3699)" width="60" x="2431" xlink:href="#Status:正常状态显示_0" y="188"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,2431,188) scale(0.857,0.857) translate(375.64,11.3699)" width="60" x="2431" y="188"/></g>
 <g id="126000517">
  <use class="kv-1" height="40" transform="rotate(0,2663,187) scale(0.893,0.893) translate(289.083,2.4065)" width="60" x="2663" xlink:href="#Status:bn_不变化颜色显示_0" y="187"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,2663,187) scale(0.893,0.893) translate(289.083,2.4065)" width="60" x="2663" y="187"/></g>
 <g id="126000516">
  <use class="kv-1" height="40" transform="rotate(0,2897,187) scale(0.896,0.896) translate(306.259,1.70534)" width="60" x="2897" xlink:href="#Status:bn_越限颜色显示_0" y="187"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,2897,187) scale(0.896,0.896) translate(306.259,1.70534)" width="60" x="2897" y="187"/></g>
 <g id="126000519">
  <use class="kv-1" height="40" transform="rotate(0,3119,186) scale(1,1) translate(-30,-20)" width="60" x="3119" xlink:href="#Status:越下限告警_0" y="186"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,3119,186) scale(1,1) translate(-30,-20)" width="60" x="3119" y="186"/></g>
</g>
<g id="MeasurementValue_Layer">
 <g id="33000481">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1454" xml:space="preserve" y="421">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638378" ObjectName="版纳_35kV_中心变\XFMR_＃1主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000480">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1056" xml:space="preserve" y="421">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709345" ObjectName="版纳_35kV_中心变\版纳_35kV_中心变/35kV\XF_＃1主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000445">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1454" xml:space="preserve" y="1201">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638112" ObjectName="版纳_35kV_巴达变\XFMR_＃1主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000444">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1454" xml:space="preserve" y="1134">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093826534834235" ObjectName="版纳_35kV_西定变老\XFMR_#1主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000443">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1454" xml:space="preserve" y="1069">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093826534834354" ObjectName="版纳_35kV_勐宋变\XFMR_＃1主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000442">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1454" xml:space="preserve" y="1003">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093826534834519" ObjectName="版纳_35kV_勐混变\XFMR_＃2主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000441">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1454" xml:space="preserve" y="939">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093826534834347" ObjectName="版纳_35kV_勐混变\XFMR_＃1主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000440">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1454" xml:space="preserve" y="874">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093826534834344" ObjectName="版纳_35kV_打洛变(旧)\XFMR_＃2主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000439">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1454" xml:space="preserve" y="807">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093826534834343" ObjectName="版纳_35kV_打洛变(旧)\XFMR_＃1主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000438">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1454" xml:space="preserve" y="746">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638015" ObjectName="版纳_35kV_勐遮变\XFMR_#2主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000437">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1454" xml:space="preserve" y="678">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638014" ObjectName="版纳_35kV_勐遮变\XFMR_#1主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000436">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1454" xml:space="preserve" y="615">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093826534834248" ObjectName="版纳_35kV_勐阿变\XFMR_#2主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000435">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1454" xml:space="preserve" y="553">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093826534834247" ObjectName="版纳_35kV_勐阿变\XFMR_#1主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000401">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1056" xml:space="preserve" y="1201">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708697" ObjectName="版纳_35kV_巴达变\版纳_35kV_巴达变-35kV\XF_＃1主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000400">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1056" xml:space="preserve" y="1134">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708481" ObjectName="版纳_35kV_西定变老\版纳_35kV_西定变老/35kV\XF_#1主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000399">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1056" xml:space="preserve" y="1069">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708733" ObjectName="版纳_35kV_勐宋变\版纳_35kV_勐宋变-35kV\XF_＃1主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000398">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1056" xml:space="preserve" y="1003">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709169" ObjectName="版纳_35kV_勐混变\版纳_35kV_勐混变-35kV\XF_＃2主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000397">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1056" xml:space="preserve" y="939">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708719" ObjectName="版纳_35kV_勐混变\版纳_35kV_勐混变-35kV\XF_＃1主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000396">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1056" xml:space="preserve" y="874">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708713" ObjectName="版纳_35kV_打洛变(旧)\版纳_35kV_打洛变(旧)/35kV\XF_＃2主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000395">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1056" xml:space="preserve" y="807">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708711" ObjectName="版纳_35kV_打洛变(旧)\版纳_35kV_打洛变(旧)/35kV\XF_＃1主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000394">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1056" xml:space="preserve" y="745">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708489" ObjectName="版纳_35kV_勐遮变\版纳_35kV_勐遮变-35kV\XF_#2主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000393">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1056" xml:space="preserve" y="678">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708487" ObjectName="版纳_35kV_勐遮变\版纳_35kV_勐遮变-35kV\XF_#1主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000392">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1056" xml:space="preserve" y="615">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708510" ObjectName="版纳_35kV_勐阿变\版纳_35kV_勐阿变-35kV\XF_#2主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000391">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1056" xml:space="preserve" y="553">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708508" ObjectName="版纳_35kV_勐阿变\版纳_35kV_勐阿变-35kV\XF_#1主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000483">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1623" xml:space="preserve" y="579">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387166" ObjectName="121878664915714206:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33000484">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1623" xml:space="preserve" y="711">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387171" ObjectName="121878664915714211:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33000485">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1623" xml:space="preserve" y="842">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387172" ObjectName="121878664915714212:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33000486">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1623" xml:space="preserve" y="973">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387175" ObjectName="121878664915714215:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33000504">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1623" xml:space="preserve" y="454">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387165" ObjectName="121878664915714205:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33000612">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1056" xml:space="preserve" y="485">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709339" ObjectName="版纳_35kV_中心变\版纳_35kV_中心变/35kV\XF_＃2主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000613">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1454" xml:space="preserve" y="485">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638375" ObjectName="版纳_35kV_中心变\XFMR_＃2主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000628">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1623" xml:space="preserve" y="1072">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387167" ObjectName="121878664915714207:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33000629">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1623" xml:space="preserve" y="1136">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387168" ObjectName="121878664915714208:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33000630">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1623" xml:space="preserve" y="1203">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387169" ObjectName="121878664915714209:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33000631">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1623" xml:space="preserve" y="1267">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387170" ObjectName="121878664915714210:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33000632">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1623" xml:space="preserve" y="1332">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387173" ObjectName="121878664915714213:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33000633">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1623" xml:space="preserve" y="1396">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387174" ObjectName="121878664915714214:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33000634">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="1623" xml:space="preserve" y="1492">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387177" ObjectName="121878664915714217:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33000652">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1055" xml:space="preserve" y="1266">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709140" ObjectName="版纳_35kV_勐往变\版纳_35kV_勐往变/35kV\XF_#1主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000653">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1454" xml:space="preserve" y="1266">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638282" ObjectName="版纳_35kV_勐往变\XFMR_#1主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000660">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1454" xml:space="preserve" y="1331">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093826534834359" ObjectName="版纳_35kV_布朗山变\XFMR_＃1主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000659">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1055" xml:space="preserve" y="1331">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708743" ObjectName="版纳_35kV_布朗山变\版纳_35kV_布朗山变-35kV\XF_＃1主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000666">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1055" xml:space="preserve" y="1396">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708703" ObjectName="117375065288343903:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000667">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1454" xml:space="preserve" y="1396">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093826534834339" ObjectName="117093590311633059:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000674">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1454" xml:space="preserve" y="1461">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093826534834302" ObjectName="版纳_35kV_岔河变\XFMR_1号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000673">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="1055" xml:space="preserve" y="1461">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708629" ObjectName="版纳_35kV_岔河变\版纳_35kV_岔河变-35kV\XF_1号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000839">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3204" xml:space="preserve" y="421">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093826534834350" ObjectName="版纳_35kV_弘源变\XFMR_＃1主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000838">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="2806" xml:space="preserve" y="421">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708725" ObjectName="版纳_35kV_弘源变\版纳_35kV_弘源变-35kV\XF_＃1主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000837">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="3200" xml:space="preserve" y="1200">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638384" ObjectName="版纳_35kV_纳卓变\XFMR_1号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000836">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3204" xml:space="preserve" y="1134">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093826534834613" ObjectName="版纳_35kV_藤蔑山变\XFMR_#2主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000835">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3204" xml:space="preserve" y="1069">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093826534834612" ObjectName="版纳_35kV_藤蔑山变\XFMR_#1主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000834">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3199" xml:space="preserve" y="1007">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093826534834595" ObjectName="版纳_35kV_龙林变\XFMR_＃2主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000833">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3204" xml:space="preserve" y="939">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093826534834594" ObjectName="版纳_35kV_龙林变\XFMR_＃1主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000832">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3204" xml:space="preserve" y="874">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093826534834306" ObjectName="版纳_35kV_老尚岗变\XFMR_2号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000831">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3204" xml:space="preserve" y="807">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093826534834305" ObjectName="版纳_35kV_老尚岗变\XFMR_1号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000830">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3204" xml:space="preserve" y="746">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093826534834207" ObjectName="版纳_35kV_易武变\XFMR_#2主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000829">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3204" xml:space="preserve" y="678">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093826534834206" ObjectName="版纳_35kV_易武变\XFMR_#1主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000828">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3204" xml:space="preserve" y="615">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638302" ObjectName="版纳_35kV_曼庄变\XFMR_#2主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000827">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3204" xml:space="preserve" y="553">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304637964" ObjectName="版纳_35kV_曼庄变\XFMR_#1主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000826">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="2802" xml:space="preserve" y="1200">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709361" ObjectName="版纳_35kV_纳卓变\版纳_35kV_纳卓变/35kV\XF_1号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000825">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="2806" xml:space="preserve" y="1134">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709371" ObjectName="版纳_35kV_藤蔑山变\版纳_35kV_藤蔑山变/35kV\XF_#2主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000824">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="2806" xml:space="preserve" y="1069">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709369" ObjectName="版纳_35kV_藤蔑山变\版纳_35kV_藤蔑山变/35kV\XF_#1主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000823">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="2806" xml:space="preserve" y="1003">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709331" ObjectName="版纳_35kV_龙林变\版纳_35kV_龙林变/35kV\XF_＃2主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000822">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="2806" xml:space="preserve" y="939">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709329" ObjectName="版纳_35kV_龙林变\版纳_35kV_龙林变/35kV\XF_＃1主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000821">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="2806" xml:space="preserve" y="875">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708637" ObjectName="版纳_35kV_老尚岗变\版纳_35kV_老尚岗变/35kV\XF_2号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000820">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="2806" xml:space="preserve" y="807">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708635" ObjectName="版纳_35kV_老尚岗变\版纳_35kV_老尚岗变/35kV\XF_1号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000819">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="2806" xml:space="preserve" y="745">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708417" ObjectName="版纳_35kV_易武变\版纳_35kV_易武变-35kV\XF_#2主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000818">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="2806" xml:space="preserve" y="678">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708415" ObjectName="版纳_35kV_易武变\版纳_35kV_易武变-35kV\XF_#1主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000817">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="2806" xml:space="preserve" y="615">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709184" ObjectName="版纳_35kV_曼庄变\版纳_35kV_曼庄变-35kV\XF_#2主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000816">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="2806" xml:space="preserve" y="553">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708377" ObjectName="版纳_35kV_曼庄变\版纳_35kV_曼庄变-35kV\XF_#1主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000840">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3373" xml:space="preserve" y="579">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387180" ObjectName="121878664915714220:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33000841">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3373" xml:space="preserve" y="711">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387181" ObjectName="121878664915714221:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33000842">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3373" xml:space="preserve" y="842">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387183" ObjectName="121878664915714223:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33000843">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3373" xml:space="preserve" y="973">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387184" ObjectName="121878664915714224:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33000844">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3373" xml:space="preserve" y="454">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387176" ObjectName="121878664915714216:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33000845">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="2806" xml:space="preserve" y="485">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708729" ObjectName="版纳_35kV_弘源变\版纳_35kV_弘源变-35kV\XF_＃2主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000846">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3204" xml:space="preserve" y="485">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093826534834352" ObjectName="版纳_35kV_弘源变\XFMR_＃2主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000847">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="3373" xml:space="preserve" y="1101">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387187" ObjectName="121878664915714227:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33000849">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="3369" xml:space="preserve" y="1232">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387178" ObjectName="121878664915714218:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33000850">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="3373" xml:space="preserve" y="1333">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387179" ObjectName="121878664915714219:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33000851">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="3373" xml:space="preserve" y="1398">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387182" ObjectName="121878664915714222:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33000852">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="3373" xml:space="preserve" y="1462">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387185" ObjectName="121878664915714225:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33000853">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="3373" xml:space="preserve" y="1527">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387186" ObjectName="121878664915714226:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33000854">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="2805" xml:space="preserve" y="1332">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708653" ObjectName="版纳_35kV_象明变\版纳_35kV_象明变-35kV\XF_＃1主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000855">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="3204" xml:space="preserve" y="1332">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093826534834314" ObjectName="版纳_35kV_象明变\XFMR_＃1主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000857">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="3204" xml:space="preserve" y="1397">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638194" ObjectName="版纳_35kV_龙门变\XFMR_35kV#1主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000856">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="2805" xml:space="preserve" y="1397">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708862" ObjectName="版纳_35kV_龙门变\版纳_35kV_龙门变-35kV\XF_35kV#1主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000858">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="2805" xml:space="preserve" y="1462">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708858" ObjectName="版纳_35kV_曼腊变\版纳_35kV_曼腊变-35kV\XF_35kV1号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000859">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="3204" xml:space="preserve" y="1462">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638192" ObjectName="版纳_35kV_曼腊变\XFMR_35kV1号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000861">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="3204" xml:space="preserve" y="1527">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093826534834617" ObjectName="版纳_35kV_磨憨变\XFMR_1号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000860">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="2805" xml:space="preserve" y="1527">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709379" ObjectName="版纳_35kV_磨憨变\版纳_35kV_磨憨变/35kV\XF_1号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000872">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="1055" xml:space="preserve" y="1525">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709246" ObjectName="版纳_35kV_岔河变\版纳_35kV_岔河变-35kV\XF_2号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000873">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="1454" xml:space="preserve" y="1525">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638332" ObjectName="版纳_35kV_岔河变\XFMR_2号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000880">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="2806" xml:space="preserve" y="1264">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709363" ObjectName="版纳_35kV_纳卓变\版纳_35kV_纳卓变/35kV\XF_2号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000881">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,0)" writing-mode="lr" x="3204" xml:space="preserve" y="1264">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117093852304638385" ObjectName="版纳_35kV_纳卓变\XFMR_2号主变:other" Plane="0"/>
  </metadata>
 </g>
</g>
<g id="Text_Layer">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="40" font-size="40" font-width="44" stroke="rgb(255,255,254)" writing-mode="lr" x="3327" xml:space="preserve" y="203">上一页</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="62" font-size="62" font-width="62" stroke="rgb(0,255,255)" writing-mode="lr" x="1575" xml:space="preserve" y="123">主变监视表</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="309" xml:space="preserve" y="421">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="79" xml:space="preserve" y="453">35kV中心变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="39" stroke="rgb(255,255,254)" writing-mode="lr" x="2470" xml:space="preserve" y="203">实时态</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="39" stroke="rgb(255,255,254)" writing-mode="lr" x="2937" xml:space="preserve" y="203">越上限</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="39" stroke="rgb(255,255,254)" writing-mode="lr" x="2707" xml:space="preserve" y="203">不变化</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1280" xml:space="preserve" y="1201">0.8</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1272" xml:space="preserve" y="1134">5.04</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="890" xml:space="preserve" y="1201">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="890" xml:space="preserve" y="1134">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="691" xml:space="preserve" y="1201">0.8</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="683" xml:space="preserve" y="1134">5.04</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="493" xml:space="preserve" y="1201">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="478" xml:space="preserve" y="1134">6.3</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="309" xml:space="preserve" y="1201">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="309" xml:space="preserve" y="1134">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="79" xml:space="preserve" y="1133">35kV西定变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1295" xml:space="preserve" y="1069">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1280" xml:space="preserve" y="1003">6.4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="890" xml:space="preserve" y="1069">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="890" xml:space="preserve" y="1003">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="706" xml:space="preserve" y="1069">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="691" xml:space="preserve" y="1003">6.4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="493" xml:space="preserve" y="1069">5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="493" xml:space="preserve" y="1003">8</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="309" xml:space="preserve" y="1069">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="309" xml:space="preserve" y="1003">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="79" xml:space="preserve" y="974">35kV勐混变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1295" xml:space="preserve" y="939">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1280" xml:space="preserve" y="874">3.2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="890" xml:space="preserve" y="939">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="890" xml:space="preserve" y="874">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="706" xml:space="preserve" y="939">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="691" xml:space="preserve" y="874">3.2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="493" xml:space="preserve" y="939">5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="493" xml:space="preserve" y="874">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="309" xml:space="preserve" y="939">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="309" xml:space="preserve" y="874">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="79" xml:space="preserve" y="841">35kV打洛变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1295" xml:space="preserve" y="807">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1280" xml:space="preserve" y="745">6.4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="890" xml:space="preserve" y="807">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="890" xml:space="preserve" y="747">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="706" xml:space="preserve" y="807">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="691" xml:space="preserve" y="746">6.4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="493" xml:space="preserve" y="807">5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="493" xml:space="preserve" y="745">8</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="309" xml:space="preserve" y="807">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="309" xml:space="preserve" y="743">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="79" xml:space="preserve" y="703">35kV勐遮变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1280" xml:space="preserve" y="674">6.4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1295" xml:space="preserve" y="615">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="890" xml:space="preserve" y="679">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="890" xml:space="preserve" y="615">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="691" xml:space="preserve" y="679">6.4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="706" xml:space="preserve" y="615">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="493" xml:space="preserve" y="679">8</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="478" xml:space="preserve" y="615">2.5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="309" xml:space="preserve" y="679">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="309" xml:space="preserve" y="615">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="79" xml:space="preserve" y="583">35kV勐阿变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1295" xml:space="preserve" y="553">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1443" xml:space="preserve" y="355">实时油温</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="890" xml:space="preserve" y="553">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="706" xml:space="preserve" y="553">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="493" xml:space="preserve" y="553">5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="309" xml:space="preserve" y="553">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1202" xml:space="preserve" y="354">告警负荷（MW）</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1001" xml:space="preserve" y="355">实时负荷（MW）</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="815" xml:space="preserve" y="355">油温上限（C）</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="608" xml:space="preserve" y="355">负载上限（MW）</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="388" xml:space="preserve" y="355">主变容量（MVA）</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="260" xml:space="preserve" y="353">主变编号</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1443" xml:space="preserve" y="292">油温监视</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1143" xml:space="preserve" y="290">负荷监控</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="95" xml:space="preserve" y="355">厂站名称</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="123" xml:space="preserve" y="290">项目</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="537" xml:space="preserve" y="290">设备及参数</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1597" xml:space="preserve" y="304">高压侧有功</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1597" xml:space="preserve" y="331">绝对值相加</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="39" font-size="39" font-width="39" stroke="rgb(255,255,254)" writing-mode="lr" x="3160" xml:space="preserve" y="203">越下限</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="890" xml:space="preserve" y="421">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="493" xml:space="preserve" y="421">5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="706" xml:space="preserve" y="421">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1295" xml:space="preserve" y="421">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="79" xml:space="preserve" y="1067">35kV勐宋变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="79" xml:space="preserve" y="1199">35kV巴达变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="309" xml:space="preserve" y="485">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="493" xml:space="preserve" y="485">5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="706" xml:space="preserve" y="485">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="890" xml:space="preserve" y="485">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1295" xml:space="preserve" y="485">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="79" xml:space="preserve" y="1264">35kV勐往变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="65" xml:space="preserve" y="1329">35kV布郎山变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="79" xml:space="preserve" y="1394">35kV勐满变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="79" xml:space="preserve" y="1491">35kV岔河变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="309" xml:space="preserve" y="1266">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="492" xml:space="preserve" y="1266">5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="706" xml:space="preserve" y="1266">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="890" xml:space="preserve" y="1266">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1294" xml:space="preserve" y="1266">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1271" xml:space="preserve" y="1331">2.52</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="890" xml:space="preserve" y="1331">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="683" xml:space="preserve" y="1331">2.52</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="469" xml:space="preserve" y="1331">3.15</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="309" xml:space="preserve" y="1331">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="309" xml:space="preserve" y="1396">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="492" xml:space="preserve" y="1396">5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="706" xml:space="preserve" y="1396">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="890" xml:space="preserve" y="1396">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1294" xml:space="preserve" y="1396">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1294" xml:space="preserve" y="1461">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="890" xml:space="preserve" y="1461">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="706" xml:space="preserve" y="1461">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="492" xml:space="preserve" y="1461">5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="309" xml:space="preserve" y="1461">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2059" xml:space="preserve" y="421">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1835" xml:space="preserve" y="453">35kV弘源变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="3025" xml:space="preserve" y="1200">2.52</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="3029" xml:space="preserve" y="1134">2.52</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2636" xml:space="preserve" y="1200">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2640" xml:space="preserve" y="1134">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2432" xml:space="preserve" y="1200">2.52</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2432" xml:space="preserve" y="1134">2.52</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2220" xml:space="preserve" y="1200">3.15</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2220" xml:space="preserve" y="1134">3.15</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2059" xml:space="preserve" y="1200">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2059" xml:space="preserve" y="1133">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1821" xml:space="preserve" y="1100">35kV藤蔑山变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="3029" xml:space="preserve" y="1069">2.52</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="3029" xml:space="preserve" y="1003">2.52</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2640" xml:space="preserve" y="1069">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2640" xml:space="preserve" y="1003">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2432" xml:space="preserve" y="1069">2.52</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2432" xml:space="preserve" y="1003">2.52</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2220" xml:space="preserve" y="1069">3.15</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2220" xml:space="preserve" y="1003">3.15</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2059" xml:space="preserve" y="1069">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2059" xml:space="preserve" y="1003">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1827" xml:space="preserve" y="974">35kV龙林变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="3029" xml:space="preserve" y="939">2.52</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="3050" xml:space="preserve" y="874">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2640" xml:space="preserve" y="939">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2640" xml:space="preserve" y="874">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2432" xml:space="preserve" y="939">2.52</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2452" xml:space="preserve" y="874">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2220" xml:space="preserve" y="939">3.15</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2241" xml:space="preserve" y="874">5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2059" xml:space="preserve" y="939">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2059" xml:space="preserve" y="874">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1835" xml:space="preserve" y="841">35kV尚岗变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="3036" xml:space="preserve" y="807">1.6</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="3050" xml:space="preserve" y="745">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2640" xml:space="preserve" y="807">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2640" xml:space="preserve" y="747">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2439" xml:space="preserve" y="807">1.6</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2452" xml:space="preserve" y="746">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2241" xml:space="preserve" y="807">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2241" xml:space="preserve" y="745">5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2059" xml:space="preserve" y="807">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2059" xml:space="preserve" y="743">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1835" xml:space="preserve" y="712">35kV易武变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="3050" xml:space="preserve" y="674">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="3036" xml:space="preserve" y="615">6.4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2640" xml:space="preserve" y="679">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2640" xml:space="preserve" y="615">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2452" xml:space="preserve" y="679">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2439" xml:space="preserve" y="615">6.4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2241" xml:space="preserve" y="679">5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2241" xml:space="preserve" y="615">8</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2059" xml:space="preserve" y="681">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2059" xml:space="preserve" y="615">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1835" xml:space="preserve" y="583">35kV曼庄变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="3036" xml:space="preserve" y="553">3.2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="3193" xml:space="preserve" y="355">实时油温</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2640" xml:space="preserve" y="553">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2439" xml:space="preserve" y="553">3.2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2241" xml:space="preserve" y="553">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2059" xml:space="preserve" y="553">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2952" xml:space="preserve" y="354">告警负荷（MW）</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2751" xml:space="preserve" y="355">实时负荷（MW）</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2565" xml:space="preserve" y="355">油温上限（C）</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2358" xml:space="preserve" y="355">负载上限（MW）</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2138" xml:space="preserve" y="355">主变容量（MVA）</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2010" xml:space="preserve" y="353">主变编号</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="3193" xml:space="preserve" y="292">油温监视</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2893" xml:space="preserve" y="290">负荷监控</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1852" xml:space="preserve" y="355">厂站名称</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1873" xml:space="preserve" y="290">项目</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2287" xml:space="preserve" y="290">设备及参数</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="3347" xml:space="preserve" y="304">高压侧有功</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="3347" xml:space="preserve" y="331">绝对值相加</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2640" xml:space="preserve" y="421">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2227" xml:space="preserve" y="421">6.3</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2432" xml:space="preserve" y="421">5.04</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="3029" xml:space="preserve" y="421">5.04</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1835" xml:space="preserve" y="1230">35kV纳卓变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2059" xml:space="preserve" y="485">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2227" xml:space="preserve" y="485">6.3</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2432" xml:space="preserve" y="485">5.04</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="2640" xml:space="preserve" y="485">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="3029" xml:space="preserve" y="485">5.04</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1835" xml:space="preserve" y="1328">35kV象明变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1835" xml:space="preserve" y="1393">35kV龙门变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1835" xml:space="preserve" y="1458">35kV曼腊变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1835" xml:space="preserve" y="1523">35kV磨憨变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2059" xml:space="preserve" y="1332">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2241" xml:space="preserve" y="1332">5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2453" xml:space="preserve" y="1332">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2640" xml:space="preserve" y="1332">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="3050" xml:space="preserve" y="1332">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="3050" xml:space="preserve" y="1397">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2640" xml:space="preserve" y="1397">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2452" xml:space="preserve" y="1397">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2227" xml:space="preserve" y="1397">2.5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2059" xml:space="preserve" y="1397">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2059" xml:space="preserve" y="1462">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2241" xml:space="preserve" y="1462">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2439" xml:space="preserve" y="1462">1.6</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2640" xml:space="preserve" y="1462">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="3036" xml:space="preserve" y="1462">1.6</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="3050" xml:space="preserve" y="1527">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2640" xml:space="preserve" y="1527">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2453" xml:space="preserve" y="1527">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2241" xml:space="preserve" y="1527">5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2059" xml:space="preserve" y="1527">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="309" xml:space="preserve" y="1525">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="492" xml:space="preserve" y="1525">8</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="687" xml:space="preserve" y="1525">6.4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="890" xml:space="preserve" y="1525">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1279" xml:space="preserve" y="1525">6.4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2056" xml:space="preserve" y="1264">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2241" xml:space="preserve" y="1264">5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2453" xml:space="preserve" y="1264">4</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="2640" xml:space="preserve" y="1264">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="3049" xml:space="preserve" y="1264">4</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href="主变监控表.fac.svg"><rect fill-opacity="0" height="43" stroke-opacity="0" stroke-width="1" width="146" x="3322" y="163"/></g>
 <g ChangePicPlane="0," Plane="0" href="监视列表.sys.svg"><rect fill-opacity="0" height="69" stroke-opacity="0" stroke-width="2" width="352" x="1566" y="62"/></g>
</g>
</svg>