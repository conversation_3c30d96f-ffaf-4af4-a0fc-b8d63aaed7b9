<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="sys" height="900" id="thSvg" viewBox="0 0 1700 900" width="1700">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="-1" InitShowingPlane="0," fill="rgb(0,0,0)" height="900" width="1700" x="0" y="0"/>
</g>
<g id="Other_Layer">
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="23" x2="728" y1="109" y2="109"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="729" x2="729" y1="109" y2="863"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="636" x2="636" y1="108" y2="863"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="186" x2="186" y1="864" y2="110"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="187" x2="728" y1="139" y2="139"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="186" x2="728" y1="199" y2="199"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="23" x2="730" y1="862" y2="862"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="792" x2="792" y1="105" y2="865"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1549" x2="1549" y1="107" y2="866"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1481" x2="1481" y1="107" y2="864"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="957" x2="957" y1="863" y2="108"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="188" x2="728" y1="229" y2="229"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="186" x2="728" y1="289" y2="289"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="186" x2="728" y1="319" y2="319"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="186" x2="726" y1="379" y2="379"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="22" x2="728" y1="409" y2="409"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="186" x2="728" y1="469" y2="469"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="186" x2="728" y1="499" y2="499"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="22" x2="728" y1="559" y2="559"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="186" x2="728" y1="589" y2="589"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="187" x2="728" y1="649" y2="649"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="187" x2="728" y1="679" y2="679"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="958" x2="1548" y1="595" y2="595"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="957" x2="1549" y1="625" y2="625"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="957" x2="1547" y1="655" y2="655"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="957" x2="1548" y1="685" y2="685"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="793" x2="1548" y1="715" y2="715"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="959" x2="1548" y1="745" y2="745"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="958" x2="1548" y1="775" y2="775"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="957" x2="1548" y1="805" y2="805"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="957" x2="1547" y1="835" y2="835"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="791" x2="1548" y1="865" y2="865"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="186" x2="728" y1="349" y2="349"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="22" x2="729" y1="259" y2="259"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="186" x2="728" y1="169" y2="169"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="23" x2="23" y1="110" y2="861"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="186" x2="728" y1="619" y2="619"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="187" x2="729" y1="529" y2="529"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="186" x2="728" y1="439" y2="439"/>
 
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="22" x2="728" y1="709" y2="709"/>
 
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="187" x2="729" y1="739" y2="739"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="187" x2="730" y1="769" y2="769"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="188" x2="730" y1="799" y2="799"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="188" x2="730" y1="829" y2="829"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="792" x2="1548" y1="562" y2="562"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="958" x2="1549" y1="135" y2="135"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="957" x2="1548" y1="165" y2="165"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="957" x2="1548" y1="225" y2="225"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="793" x2="1548" y1="255" y2="255"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="958" x2="1548" y1="315" y2="315"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="959" x2="1548" y1="345" y2="345"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="792" x2="1549" y1="405" y2="405"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="957" x2="1549" y1="435" y2="435"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="958" x2="1548" y1="495" y2="495"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="956" x2="1548" y1="525" y2="525"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="957" x2="1549" y1="375" y2="375"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="958" x2="1548" y1="195" y2="195"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="793" x2="1548" y1="105" y2="105"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="957" x2="1549" y1="285" y2="285"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="957" x2="1550" y1="465" y2="465"/>
</g>
<g id="Text_Layer">
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="50" xml:space="preserve" y="342">景德寨I回线</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="39" xml:space="preserve" y="211">景岱寨IⅤ回线</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="813" xml:space="preserve" y="498">东部客栈I回线</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="61" xml:space="preserve" y="807">果庄I回线</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="813" xml:space="preserve" y="803">星光夜市I回线</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="39" xml:space="preserve" y="496">景兰酒店I回线</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="67" xml:space="preserve" y="643">告庄支线</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="807" xml:space="preserve" y="655">景兰酒店II回线</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="48" font-size="29" font-width="29" stroke="rgb(0,0,0)" writing-mode="lr" x="581" xml:space="preserve" y="56">10kV告庄3号开闭所</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="20" font-size="20" font-width="24" stroke="rgb(0,0,0)" writing-mode="lr" x="1394" xml:space="preserve" y="64">下一页</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="61" xml:space="preserve" y="669">4号环网柜</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="823" xml:space="preserve" y="329">告庄2号/3号</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="807" xml:space="preserve" y="194">星光夜市II回线</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="21" font-size="21" font-width="21" stroke="rgb(255,255,254)" writing-mode="lr" x="818" xml:space="preserve" y="353">开闭所联络线</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href="告庄3号开闭所.fac.svg"><rect fill-opacity="0" height="61" stroke-opacity="0" stroke-width="2" width="266" x="572" y="5"/></g>
 <g ChangePicPlane="0," Plane="0" href="告庄3号开闭所软压板2.fac.svg"><rect fill-opacity="0" height="39" stroke-opacity="0" stroke-width="2" width="103" x="1374" y="35"/></g>
</g>
</svg>