<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="113997365567815983" height="1050" id="thSvg" viewBox="0 0 2200 1050" width="2200">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="Breaker:0_0" viewBox="0,0,50,50">
 <use Plane="0" x="14" xlink:href="#terminal" y="7"/>
 <use Plane="0" x="14" xlink:href="#terminal" y="38"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="31" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,14,22)" width="16" x="6" y="7"/>
</symbol>
<symbol id="Breaker:0_1" viewBox="0,0,50,50">
 <use Plane="0" x="14" xlink:href="#terminal" y="7"/>
 <use Plane="0" x="14" xlink:href="#terminal" y="38"/>
 <rect AFMask="2147483647" Plane="0" fill="rgb(0,0,255)" height="31" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,15,22)" width="16" x="7" y="7"/>
</symbol>
<symbol id="PT:44551_0" viewBox="0,0,60,60">
 <use Plane="0" x="30" xlink:href="#terminal" y="5"/>
 <circle AFMask="2147483647" Plane="0" cx="35" cy="35" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="35" cy="25" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="25" cy="25" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="25" cy="35" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="35" x2="32" y1="35" y2="32"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="35" x2="35" y1="39" y2="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="38" x2="35" y1="32" y2="35"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="11" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,52,33)" width="6" x="49" y="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="47" x2="57" y1="29" y2="39"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="0" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,28,11)" width="0" x="28" y="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="52" x2="52" y1="39" y2="45"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="22" x2="29" y1="25" y2="25"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="22" x2="24" y1="25" y2="22"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="29" x2="27" y1="25" y2="22"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="30" x2="30" y1="20" y2="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="52" x2="35" y1="25" y2="25"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="52" x2="52" y1="25" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="38" x2="35" y1="21" y2="24"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="35" x2="32" y1="24" y2="21"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="35" x2="35" y1="28" y2="24"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="28" x2="25" y1="32" y2="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="25" x2="22" y1="35" y2="32"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="25" x2="25" y1="39" y2="35"/>
 <circle AFMask="2147483647" Plane="0" cx="30" cy="44" fill="none" r="6" stroke="rgb(255,255,254)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="30" x2="30" y1="49" y2="45"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="30" x2="27" y1="45" y2="42"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="33" x2="30" y1="42" y2="45"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="49" x2="52" y1="42" y2="45"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="52" x2="55" y1="45" y2="42"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="49" x2="52" y1="45" y2="48"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="52" x2="55" y1="48" y2="45"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="52" x2="52" y1="48" y2="51"/>
 <polygon AFMask="2147483647" Plane="0" fill="none" points="51,52 52,54 54,52" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Terminal:bn_35kV电缆_0" viewBox="0,0,38,12">
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,0)" stroke-width="1" x1="11" x2="25" y1="6" y2="6"/>
 <path AFMask="2147483647" Plane="0" d="M 8 2 L 11 5 L 8 8 " fill="none" stroke="rgb(255,255,0)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,0)" stroke-width="1" x1="7" x2="7" y1="3" y2="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,0)" stroke-width="1" x1="29" x2="29" y1="2" y2="8"/>
 <path AFMask="2147483647" Plane="0" d="M 28 8 L 25 5 L 28 2 " fill="none" stroke="rgb(255,255,0)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,0)" stroke-width="1" x1="2" x2="7" y1="6" y2="6"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,0)" stroke-width="1" x1="29" x2="34" y1="6" y2="6"/>
</symbol>
<symbol id="Arrester:bn_避雷器11_0" viewBox="0,0,40,20">
 <use Plane="0" x="36" xlink:href="#terminal" y="10"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="10" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,20,10)" width="22" x="9" y="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="29" x2="23" y1="10" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="5" x2="5" y1="6" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="3" x2="3" y1="7" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="22" x2="22" y1="8" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="6" x2="9" y1="10" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="29" x2="35" y1="10" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="22" x2="19" y1="8" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="19" x2="22" y1="10" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1" x2="1" y1="8" y2="12"/>
</symbol>
<symbol id="Status:bn_不变化颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(0,0,255)" height="1" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_不变化颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(85,0,255)" height="1" stroke="rgb(85,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Disconnector:bn_刀闸3_0" viewBox="0,0,20,40">
 <use Plane="0" x="7" xlink:href="#terminal" y="3"/>
 <use Plane="0" x="7" xlink:href="#terminal" y="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="35" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="7" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="1" x2="15" y1="31" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="18" y1="7" y2="31"/>
</symbol>
<symbol id="Disconnector:bn_刀闸3_1" viewBox="0,0,20,40">
 <use Plane="0" x="7" xlink:href="#terminal" y="3"/>
 <use Plane="0" x="7" xlink:href="#terminal" y="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="35" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="1" x2="15" y1="31" y2="31"/>
</symbol>
<symbol id="PT:bn_电压互感器_移动变_0" viewBox="0,0,100,70">
 <use Plane="0" x="21" xlink:href="#terminal" y="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="52" x2="52" y1="52" y2="44"/>
 <circle AFMask="2147483647" Plane="0" cx="63" cy="34" fill="none" r="8" stroke="rgb(255,255,254)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="49" cy="34" fill="none" r="8" stroke="rgb(255,255,254)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="58" x2="64" y1="35" y2="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="64" x2="68" y1="35" y2="40"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="42" x2="48" y1="35" y2="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="48" x2="51" y1="35" y2="29"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="48" x2="52" y1="35" y2="40"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="41" x2="22" y1="35" y2="35"/>
 <circle AFMask="2147483647" Plane="0" cx="49" cy="48" fill="none" r="8" stroke="rgb(255,255,254)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="63" cy="48" fill="none" r="8" stroke="rgb(255,255,254)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="63" x2="67" y1="49" y2="54"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="63" x2="66" y1="49" y2="43"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="57" x2="63" y1="49" y2="49"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="64" x2="67" y1="35" y2="29"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="45" x2="52" y1="46" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="45" x2="52" y1="50" y2="52"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="48" x2="48" y1="35" y2="19"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="63" x2="63" y1="35" y2="19"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="75" x2="75" y1="40" y2="19"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="64" x2="75" y1="49" y2="40"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="82" x2="48" y1="19" y2="19"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="83" x2="83" y1="15" y2="23"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="86" x2="86" y1="16" y2="22"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="89" x2="89" y1="18" y2="20"/>
</symbol>
<symbol id="Status:bn_非实测颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,85,0)" height="1" stroke="rgb(170,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_非实测颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,85,0)" height="1" stroke="rgb(170,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_工况退出颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,170,255)" height="1" stroke="rgb(170,170,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_工况退出颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,170,255)" height="1" stroke="rgb(170,170,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸12_0" viewBox="0,0,40,20">
 <use Plane="0" x="4" xlink:href="#terminal" y="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="31" y1="6" y2="18"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="28" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="33" y1="8" y2="16"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="35" x2="35" y1="10" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="28" y1="2" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="4" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="9" y1="5" y2="19"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸12_1" viewBox="0,0,40,20">
 <use Plane="0" x="4" xlink:href="#terminal" y="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="31" y1="6" y2="18"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="3" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="33" y1="8" y2="16"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="35" x2="35" y1="10" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="9" y1="5" y2="19"/>
</symbol>
<symbol id="Transformer2:bn_两卷变压器2_0" viewBox="0,0,56,76">
 <use Plane="0" x="25" xlink:href="#terminal" y="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="25" x2="33" y1="20" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="25" x2="17" y1="20" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="25" x2="25" y1="20" y2="30"/>
 <circle AFMask="2147483647" Plane="0" cx="25" cy="21" fill="none" r="18" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="47" y1="48" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="41" x2="47" y1="6" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="45" x2="47" y1="9" y2="3"/>
</symbol>
<symbol id="Transformer2:bn_两卷变压器2_1" viewBox="0,0,56,76">
 <use Plane="1" x="25" xlink:href="#terminal" y="71"/>
 <circle AFMask="2147483647" Plane="1" cx="25" cy="53" fill="none" r="18" stroke="rgb(0,0,255)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 25 43 L 15 59 L 35 59 Z" fill="none" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Fuse:bn_熔断器1_0" viewBox="0,0,20,50">
 <use Plane="0" x="10" xlink:href="#terminal" y="3"/>
 <use Plane="0" x="10" xlink:href="#terminal" y="43"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="3" y2="43"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="30" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,10,23)" width="14" x="3" y="8"/>
</symbol>
<symbol id="Disconnector:bn_手车熔断器_0" viewBox="0,0,20,70">
 <use Plane="0" x="9" xlink:href="#terminal" y="66"/>
 <use Plane="0" x="9" xlink:href="#terminal" y="3"/>
 <path AFMask="2147483647" Plane="0" d="M 3 17 L 9 11 L 15 17 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="57" y2="12"/>
 <path AFMask="2147483647" Plane="0" d="M 3 11 L 9 5 L 15 12 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="0" d="M 3 51 L 9 57 L 15 51 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="0" d="M 3 57 L 9 63 L 15 57 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="26" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,10,34)" width="12" x="4" y="21"/>
</symbol>
<symbol id="Disconnector:bn_手车熔断器_1" viewBox="0,0,20,70">
 <use Plane="0" x="9" xlink:href="#terminal" y="66"/>
 <use Plane="0" x="9" xlink:href="#terminal" y="3"/>
 <path AFMask="2147483647" Plane="0" d="M 4 57 L 9 62 L 14 57 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
 <path AFMask="2147483647" Plane="0" d="M 4 11 L 9 6 L 14 11 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="26" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,10,34)" width="12" x="4" y="21"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="9" y1="9" y2="60"/>
</symbol>
<symbol id="Status:bn_数据封锁颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,85,0)" height="1" stroke="rgb(255,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_数据封锁颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,85,0)" height="1" stroke="rgb(255,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_0_0" viewBox="0,0,20,74">
 <rect AFMask="2147483647" Plane="0" fill="none" height="0" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,4,16)" width="0" x="4" y="16"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="30" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,9,37)" width="13" x="3" y="22"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_0_1" viewBox="0,0,20,74">
 <rect AFMask="2147483647" Plane="0" fill="none" height="30" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,9,36)" width="13" x="3" y="21"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_0_2" viewBox="0,0,20,74">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,254)" height="30" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,9,36)" width="13" x="3" y="21"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_0_3" viewBox="0,0,20,74">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,254)" height="30" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,9,37)" width="13" x="3" y="22"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_1_0" viewBox="0,0,20,74">
 <use Plane="1" x="9" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="9" xlink:href="#terminal" y="69"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="21" y2="10"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="52" y2="63"/>
 <path AFMask="2147483647" Plane="1" d="M 3 11 L 10 5 L 17 12 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 3 57 L 9 64 L 16 57 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 3 15 L 10 9 L 17 16 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 3 61 L 9 68 L 16 61 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_1_1" viewBox="0,0,20,74">
 <use Plane="1" x="9" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="9" xlink:href="#terminal" y="69"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="21" y2="9"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="52" y2="65"/>
 <path AFMask="2147483647" Plane="1" d="M 4 12 L 10 6 L 16 12 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
 <path AFMask="2147483647" Plane="1" d="M 4 61 L 10 67 L 16 61 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_1_2" viewBox="0,0,20,74">
 <use Plane="1" x="9" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="9" xlink:href="#terminal" y="69"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="22" y2="10"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="52" y2="63"/>
 <path AFMask="2147483647" Plane="1" d="M 3 11 L 9 4 L 16 11 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 3 15 L 9 8 L 16 15 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 2 57 L 9 63 L 16 56 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 2 61 L 9 67 L 16 60 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_1_3" viewBox="0,0,20,74">
 <use Plane="1" x="9" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="9" xlink:href="#terminal" y="69"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="21" y2="8"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="52" y2="65"/>
 <path AFMask="2147483647" Plane="1" d="M 3 11 L 9 5 L 15 11 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
 <path AFMask="2147483647" Plane="1" d="M 3 60 L 9 66 L 15 60 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
</symbol>
<symbol id="Status:bn_越限颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_越限颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Terminal:bn_终端设备9_0" viewBox="0,0,38,12">
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,255,0)" stroke-width="1" x1="11" x2="25" y1="6" y2="6"/>
 <path AFMask="2147483647" Plane="0" d="M 8 2 L 11 5 L 8 8 " fill="none" stroke="rgb(0,255,0)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,255,0)" stroke-width="1" x1="7" x2="7" y1="3" y2="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,255,0)" stroke-width="1" x1="29" x2="29" y1="2" y2="8"/>
 <path AFMask="2147483647" Plane="0" d="M 28 8 L 25 5 L 28 2 " fill="none" stroke="rgb(0,255,0)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,255,0)" stroke-width="1" x1="2" x2="7" y1="6" y2="6"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,255,0)" stroke-width="1" x1="29" x2="34" y1="6" y2="6"/>
 <circle AFMask="2147483647" Plane="0" cx="18" cy="5" fill="none" r="2" stroke="rgb(0,255,0)" stroke-width="1"/>
</symbol>
<symbol id="GZP:gg_光子牌1_0" viewBox="0,0,36,36">
 <circle AFMask="2147483647" Plane="0" cx="18" cy="18" fill="rgb(0,255,0)" r="15" stroke="rgb(0,255,0)" stroke-width="1"/>
</symbol>
<symbol id="GZP:gg_光子牌1_1" viewBox="0,0,36,36">
 <circle AFMask="2147483647" Plane="0" cx="18" cy="18" fill="rgb(255,0,0)" r="15" stroke="rgb(255,0,0)" stroke-width="1"/>
</symbol>
<symbol id="Arrester:qj_ocs_blq2_0" viewBox="0,0,40,20">
 <use Plane="0" x="2" xlink:href="#terminal" y="9"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="10" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,18,10)" width="23" x="7" y="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="10" y1="10" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="36" x2="36" y1="7" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="34" x2="34" y1="15" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="38" x2="38" y1="9" y2="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="30" x2="33" y1="10" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="4" x2="10" y1="10" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="17" x2="17" y1="7" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="17" x2="17" y1="11" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="17" x2="21" y1="7" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="21" x2="17" y1="10" y2="13"/>
</symbol>
<symbol id="Disconnector:qj_ocs_xc5_0" viewBox="0,0,16,10">
 <use Plane="0" x="8" xlink:href="#terminal" y="2"/>
 <use Plane="0" x="8" xlink:href="#terminal" y="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="8" y1="5" y2="2"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="14" y1="2" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="8" y1="9" y2="6"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="14" y1="6" y2="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="6" y2="9"/>
</symbol>
<symbol id="Disconnector:qj_ocs_xc5_1" viewBox="0,0,16,10">
 <use Plane="0" x="8" xlink:href="#terminal" y="2"/>
 <use Plane="0" x="8" xlink:href="#terminal" y="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="8" y1="5" y2="2"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="14" y1="2" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="8" y1="6" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="14" y1="3" y2="6"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="3" y2="9"/>
</symbol>
<symbol id="Terminal:尚岗临时变站用变_0" viewBox="0,0,22,70">
 <use Plane="0" x="11" xlink:href="#terminal" y="5"/>
 <circle AFMask="2147483647" Plane="0" cx="11" cy="23" fill="none" r="8" stroke="rgb(255,255,254)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="11" cy="37" fill="none" r="8" stroke="rgb(255,255,254)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="11" x2="11" y1="37" y2="43"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="11" x2="6" y1="37" y2="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="11" x2="15" y1="37" y2="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="11" x2="11" y1="7" y2="15"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="7" x2="16" y1="20" y2="20"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="6" x2="11" y1="20" y2="25"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="16" x2="12" y1="20" y2="24"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="11" x2="11" y1="45" y2="62"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="14" y1="51" y2="51"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="8" y1="51" y2="51"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="8" x2="11" y1="51" y2="54"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="14" x2="11" y1="51" y2="54"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="12" y1="63" y2="63"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="11" y1="62" y2="64"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="13" x2="11" y1="62" y2="64"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="13" y1="62" y2="62"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="版纳_35kV_尚岗临时变" InitShowingPlane="0," fill="rgb(0,0,0)" height="1050" width="2200" x="0" y="0"/>
</g>
<g id="Other_Layer">
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="16" x2="424" y1="15" y2="15"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="424" x2="424" y1="16" y2="1041"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="15" x2="15" y1="16" y2="1040"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="15" x2="424" y1="701" y2="701"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="16" x2="424" y1="641" y2="641"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="15" x2="424" y1="585" y2="585"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="15" x2="424" y1="529" y2="529"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="114" x2="424" y1="473" y2="473"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="114" x2="424" y1="416" y2="416"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="16" x2="424" y1="360" y2="360"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="114" x2="114" y1="702" y2="851"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="201" x2="201" y1="530" y2="640"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="114" x2="114" y1="362" y2="528"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="16" x2="424" y1="135" y2="135"/>
 
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="18" x2="422" y1="225" y2="225"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="345" x2="345" y1="224" y2="358"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="265" x2="265" y1="224" y2="358"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="180" x2="180" y1="224" y2="358"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="98" x2="98" y1="224" y2="358"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="18" x2="422" y1="289" y2="289"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="116" x2="424" y1="751" y2="751"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="116" x2="424" y1="801" y2="801"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="15" x2="424" y1="851" y2="851"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="15" x2="424" y1="901" y2="901"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="15" x2="424" y1="951" y2="951"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="15" x2="424" y1="1041" y2="1041"/>
</g>
<g id="Bus_Layer">
 <g id="30000199">
  <path d="M 655 664 L 1787 664" stroke="rgb(0,0,255)" stroke-width="3"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115404740451369894" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\BS_10kVI母" Plane="0"/>
  <cge:TPSR_Ref TObjectID="115404740451369894"/></metadata>
 <path d="M 655 664 L 1787 664" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="Breaker_Layer">
 <g id="100000184">
  <use class="kv35kV" height="50" transform="rotate(0,933,347) scale(1,1) translate(-14,-23)" width="50" x="933" xlink:href="#Breaker:0_0" y="347"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521243332" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变35kV\CB_腊尚线301断路器" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319935172"/>
  <cge:TPSR_Ref TObjectID="114560315521243332"/></metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,933,347) scale(1,1) translate(-14,-23)" width="50" x="933" y="347"/></g>
</g>
<g id="Disconnector_Layer">
 <g id="101000179">
  <use class="kv35kV" height="40" transform="rotate(0,932,281) scale(1,1) translate(-8,-19)" width="20" x="932" xlink:href="#Disconnector:bn_刀闸3_0" y="281"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959969" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变35kV\SW_腊尚线3011刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346978849"/>
  <cge:TPSR_Ref TObjectID="114841790497959969"/></metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,932,281) scale(1,1) translate(-8,-19)" width="20" x="932" y="281"/></g>
 <g id="101000193">
  <use class="kv-1" height="40" transform="rotate(0,932,159) scale(1,1) translate(-8,-19)" width="20" x="932" xlink:href="#Disconnector:bn_刀闸3_0" y="159"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,932,159) scale(1,1) translate(-8,-19)" width="20" x="932" y="159"/></g>
 <g id="101000211">
  <use class="kv10kV" height="10" transform="rotate(0,877,707) scale(2.076,2.076) translate(-462.553,-372.441)" width="16" x="877" xlink:href="#Disconnector:qj_ocs_xc5_0" y="707"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959975" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\SW_0901手车" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346978855"/>
  <cge:TPSR_Ref TObjectID="114841790497959975"/></metadata>
 <rect fill="white" height="10" opacity="0" stroke="white" transform="rotate(0,877,707) scale(2.076,2.076) translate(-462.553,-372.441)" width="16" x="877" y="707"/></g>
 <g id="101000235">
  <use class="kv10kV" height="70" transform="rotate(0,1035,759) scale(1,1) translate(-10,-35)" width="20" x="1035" xlink:href="#Disconnector:bn_手车熔断器_0" y="759"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959976" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\SW_0R2手车" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346978856"/>
  <cge:TPSR_Ref TObjectID="114841790497959976"/></metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,1035,759) scale(1,1) translate(-10,-35)" width="20" x="1035" y="759"/></g>
</g>
<g id="GroundDisconnector_Layer">
 <g id="111000249">
  <use class="kv10kV" height="20" transform="rotate(360,1181,835) scale(-1,1) translate(-2366,-12)" width="40" x="1181" xlink:href="#GroundDisconnector:bn_接地刀闸12_0" y="835"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115123265474666857" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\GRNDSW_盟尚联络线0217接地刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="115123394323685737"/>
  <cge:TPSR_Ref TObjectID="115123265474666857"/></metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(360,1181,835) scale(-1,1) translate(-2366,-12)" width="40" x="1181" y="835"/></g>
 <g id="111000296">
  <use class="kv10kV" height="20" transform="rotate(360,1331,835) scale(-1,1) translate(-2666,-12)" width="40" x="1331" xlink:href="#GroundDisconnector:bn_接地刀闸12_0" y="835"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115123265474666858" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\GRNDSW_尚勇线0227接地刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="115123394323685738"/>
  <cge:TPSR_Ref TObjectID="115123265474666858"/></metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(360,1331,835) scale(-1,1) translate(-2666,-12)" width="40" x="1331" y="835"/></g>
 <g id="111000312">
  <use class="kv10kV" height="20" transform="rotate(360,1481,835) scale(-1,1) translate(-2966,-12)" width="40" x="1481" xlink:href="#GroundDisconnector:bn_接地刀闸12_0" y="835"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115123265474666859" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\GRNDSW_尚岗线0237接地刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="115123394323685739"/>
  <cge:TPSR_Ref TObjectID="115123265474666859"/></metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(360,1481,835) scale(-1,1) translate(-2966,-12)" width="40" x="1481" y="835"/></g>
 <g id="111000328">
  <use class="kv10kV" height="20" transform="rotate(360,1631,835) scale(-1,1) translate(-3266,-12)" width="40" x="1631" xlink:href="#GroundDisconnector:bn_接地刀闸12_0" y="835"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115123265474666860" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\GRNDSW_盐厂线、雷达线0247接地刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="115123394323685740"/>
  <cge:TPSR_Ref TObjectID="115123265474666860"/></metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(360,1631,835) scale(-1,1) translate(-3266,-12)" width="40" x="1631" y="835"/></g>
 <g id="111000342">
  <use class="kv35kV" height="20" transform="rotate(360,909,320) scale(-1,1) translate(-1822,-12)" width="40" x="909" xlink:href="#GroundDisconnector:bn_接地刀闸12_0" y="320"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115123265474666856" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变35kV\GRNDSW_腊尚线30117接地刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="115123394323685736"/>
  <cge:TPSR_Ref TObjectID="115123265474666856"/></metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(360,909,320) scale(-1,1) translate(-1822,-12)" width="40" x="909" y="320"/></g>
</g>
<g id="Transformer2_Layer">
<g id="102000156">
 <g id="1020001560">
  <use class="kv35kV" height="76" transform="rotate(0,932,448) scale(1.5,1.5) translate(-335.667,-186.333)" width="56" x="932" xlink:href="#Transformer2:bn_两卷变压器2_0" y="448"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="117375065288344863" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变35kV\XF_1号主变-高" Plane="0"/>
  </metadata>
 </g>
 <g id="1020001561">
  <use class="kv10kV" height="76" transform="rotate(0,932,448) scale(1.5,1.5) translate(-335.667,-186.333)" width="56" x="932" xlink:href="#Transformer2:bn_两卷变压器2_1" y="448"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="117375065288344864" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\XF_1号主变-低" Plane="0"/>
  </metadata>
 </g>
 <metadata>
  <cge:PSR_Ref ObjectID="117093590311633475" ObjectName="版纳_35kV_尚岗临时变\XFMR_1号主变" Plane="0"/>
 <cge:TPSR_Ref TObjectID="117093590311633475"/></metadata>
<rect fill="white" height="76" opacity="0" stroke="white" transform="rotate(0,932,448) scale(1.5,1.5) translate(-335.667,-186.333)" width="56" x="932" y="448"/></g>
</g>
<g id="DollyBreaker_Layer">
 <g id="110000189">
  <use class="kv10kV" height="74" transform="rotate(0,932,599) scale(1,0.853) translate(-10,66.2275)" width="20" x="932" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="599"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521243333" ObjectName=" 版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\CB_1号主变低001断路器" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319935173"/>
  <cge:TPSR_Ref TObjectID="114560315521243333"/></metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,932,599) scale(1,0.853) translate(-10,66.2275)" width="20" x="932" y="599"/></g>
 <g id="110000189">
  <use class="kv10kV" height="74" transform="rotate(0,932,599) scale(1,0.853) translate(-10,66.2275)" width="20" x="932" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="599"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959970" ObjectName=" 版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\SW_1号主变低001手车" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841962296651810"/>
  <cge:TPSR_Ref TObjectID="114841790497959970"/></metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,932,599) scale(1,0.853) translate(-10,66.2275)" width="20" x="932" y="599"/></g>
 <g id="110000237">
  <use class="kv10kV" height="74" transform="rotate(360,1205,757) scale(1,1) translate(-10,-37)" width="20" x="1205" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="757"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521243334" ObjectName=" 版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\CB_盟尚联络线021断路器" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319935174"/>
  <cge:TPSR_Ref TObjectID="114560315521243334"/></metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,1205,757) scale(1,1) translate(-10,-37)" width="20" x="1205" y="757"/></g>
 <g id="110000237">
  <use class="kv10kV" height="74" transform="rotate(360,1205,757) scale(1,1) translate(-10,-37)" width="20" x="1205" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="757"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959971" ObjectName=" 版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\SW_盟尚联络线021手车" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841962296651811"/>
  <cge:TPSR_Ref TObjectID="114841790497959971"/></metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,1205,757) scale(1,1) translate(-10,-37)" width="20" x="1205" y="757"/></g>
 <g id="110000295">
  <use class="kv10kV" height="74" transform="rotate(360,1355,757) scale(1,1) translate(-10,-37)" width="20" x="1355" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="757"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521243335" ObjectName=" 版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\CB_尚勇线022断路器" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319935175"/>
  <cge:TPSR_Ref TObjectID="114560315521243335"/></metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,1355,757) scale(1,1) translate(-10,-37)" width="20" x="1355" y="757"/></g>
 <g id="110000295">
  <use class="kv10kV" height="74" transform="rotate(360,1355,757) scale(1,1) translate(-10,-37)" width="20" x="1355" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="757"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959972" ObjectName=" 版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\SW_尚勇线022手车" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841962296651812"/>
  <cge:TPSR_Ref TObjectID="114841790497959972"/></metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,1355,757) scale(1,1) translate(-10,-37)" width="20" x="1355" y="757"/></g>
 <g id="110000311">
  <use class="kv10kV" height="74" transform="rotate(360,1505,757) scale(1,1) translate(-10,-37)" width="20" x="1505" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="757"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521243336" ObjectName=" 版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\CB_尚岗线023断路器" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319935176"/>
  <cge:TPSR_Ref TObjectID="114560315521243336"/></metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,1505,757) scale(1,1) translate(-10,-37)" width="20" x="1505" y="757"/></g>
 <g id="110000311">
  <use class="kv10kV" height="74" transform="rotate(360,1505,757) scale(1,1) translate(-10,-37)" width="20" x="1505" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="757"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959973" ObjectName=" 版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\SW_尚岗线023手车" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841962296651813"/>
  <cge:TPSR_Ref TObjectID="114841790497959973"/></metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,1505,757) scale(1,1) translate(-10,-37)" width="20" x="1505" y="757"/></g>
 <g id="110000327">
  <use class="kv10kV" height="74" transform="rotate(360,1655,757) scale(1,1) translate(-10,-37)" width="20" x="1655" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="757"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521243337" ObjectName=" 版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\CB_盐厂线、雷达线024断路器" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319935177"/>
  <cge:TPSR_Ref TObjectID="114560315521243337"/></metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,1655,757) scale(1,1) translate(-10,-37)" width="20" x="1655" y="757"/></g>
 <g id="110000327">
  <use class="kv10kV" height="74" transform="rotate(360,1655,757) scale(1,1) translate(-10,-37)" width="20" x="1655" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="757"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959974" ObjectName=" 版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\SW_盐厂线、雷达线024手车" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841962296651814"/>
  <cge:TPSR_Ref TObjectID="114841790497959974"/></metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(360,1655,757) scale(1,1) translate(-10,-37)" width="20" x="1655" y="757"/></g>
</g>
<g id="Load_Layer">
 <g id="32000258">
 <path d="M 1205 835 L 1205 902" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115967690404792220" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\LD_盟尚联络线" Plane="0"/>
  <cge:TPSR_Ref TObjectID="115967690404792220"/></metadata>
 <path d="M 1205 835 L 1205 902" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32000286">
 <path d="M 1355 835 L 1355 902" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115967690404792221" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\LD_尚勇线" Plane="0"/>
  <cge:TPSR_Ref TObjectID="115967690404792221"/></metadata>
 <path d="M 1355 835 L 1355 902" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32000302">
 <path d="M 1505 835 L 1505 902" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115967690404792222" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\LD_尚岗线" Plane="0"/>
  <cge:TPSR_Ref TObjectID="115967690404792222"/></metadata>
 <path d="M 1505 835 L 1505 902" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32000318">
 <path d="M 1655 835 L 1655 902" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115967690404792223" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\LD_盐厂线、雷达线" Plane="0"/>
  <cge:TPSR_Ref TObjectID="115967690404792223"/></metadata>
 <path d="M 1655 835 L 1655 902" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="ACLineSegment_Layer">
 <g id="36000333">
 <path d="M 932 130 L 932 61" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="ST=113997365567815983/LN=116530640358212549" ObjectName="ST=版纳_35kV_尚岗临时变/LN=ACLN_腊尚线（临时变）" Plane="0"/>
  <cge:TPSR_Ref TObjectID="116530640358212549_113997365567815983"/></metadata>
 <path d="M 932 130 L 932 61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="PT_Layer">
 <g id="107000171">
  <use class="kv35kV" height="70" transform="rotate(0,964,228) scale(1,1) translate(-22,-35)" width="100" x="964" xlink:href="#PT:bn_电压互感器_移动变_0" y="228"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195190282" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变35kV\TERM_腊尚线电压互感器" Plane="0"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,964,228) scale(1,1) translate(-22,-35)" width="100" x="964" y="228"/></g>
 <g id="107000212">
  <use class="kv10kV" height="60" transform="rotate(0,877,888) scale(1.594,1.594) translate(-356.812,-336.911)" width="60" x="877" xlink:href="#PT:44551_0" y="888"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195190289" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\TERM_0901电压互感器" Plane="0"/>
  </metadata>
 <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,877,888) scale(1.594,1.594) translate(-356.812,-336.911)" width="60" x="877" y="888"/></g>
</g>
<g id="GZP_Layer">
 <g id="135000123">
  <use class="kv-1" height="36" transform="rotate(0,302,672) scale(0.8,0.8) translate(57.5,150)" width="36" x="302" xlink:href="#GZP:gg_光子牌1_0" y="672"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,302,672) scale(0.8,0.8) translate(57.5,150)" width="36" x="302" y="672"/></g>
</g>
<g id="Arrester_Layer">
 <g id="133000174">
  <use class="kv35kV" height="20" transform="rotate(0,902,228) scale(1,1) translate(-36,-10)" width="40" x="902" xlink:href="#Arrester:bn_避雷器11_0" y="228"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195190281" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变35kV\TERM_腊尚线301避雷器" Plane="0"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,902,228) scale(1,1) translate(-36,-10)" width="40" x="902" y="228"/></g>
 <g id="133000178">
  <use class="kv10kV" height="20" transform="rotate(0,904,534) scale(1,1) translate(-36,-10)" width="40" x="904" xlink:href="#Arrester:bn_避雷器11_0" y="534"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195190290" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\TERM_001避雷器" Plane="0"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,904,534) scale(1,1) translate(-36,-10)" width="40" x="904" y="534"/></g>
 <g id="133000214">
  <use class="kv10kV" height="20" transform="rotate(90,949,780) scale(1,1) translate(-2,-10)" width="40" x="949" xlink:href="#Arrester:qj_ocs_blq2_0" y="780"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195190283" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\TERM_0901避雷器" Plane="0"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,949,780) scale(1,1) translate(-2,-10)" width="40" x="949" y="780"/></g>
 <g id="133000240">
  <use class="kv10kV" height="20" transform="rotate(0,1231,835) scale(1,1) translate(-2,-10)" width="40" x="1231" xlink:href="#Arrester:qj_ocs_blq2_0" y="835"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195190285" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\TERM_盟尚联络线021避雷器" Plane="0"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1231,835) scale(1,1) translate(-2,-10)" width="40" x="1231" y="835"/></g>
 <g id="133000298">
  <use class="kv10kV" height="20" transform="rotate(0,1381,835) scale(1,1) translate(-2,-10)" width="40" x="1381" xlink:href="#Arrester:qj_ocs_blq2_0" y="835"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195190286" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\TERM_尚勇线022避雷器" Plane="0"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1381,835) scale(1,1) translate(-2,-10)" width="40" x="1381" y="835"/></g>
 <g id="133000314">
  <use class="kv10kV" height="20" transform="rotate(0,1531,835) scale(1,1) translate(-2,-10)" width="40" x="1531" xlink:href="#Arrester:qj_ocs_blq2_0" y="835"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195190287" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\TERM_尚岗线023避雷器" Plane="0"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1531,835) scale(1,1) translate(-2,-10)" width="40" x="1531" y="835"/></g>
 <g id="133000330">
  <use class="kv10kV" height="20" transform="rotate(0,1681,835) scale(1,1) translate(-2,-10)" width="40" x="1681" xlink:href="#Arrester:qj_ocs_blq2_0" y="835"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195190288" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\TERM_盐厂线、雷达线024避雷器" Plane="0"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1681,835) scale(1,1) translate(-2,-10)" width="40" x="1681" y="835"/></g>
</g>
<g id="Fuse_Layer">
 <g id="131000213">
  <use class="kv10kV" height="50" transform="rotate(0,877,809) scale(1,1) translate(-10,-23)" width="20" x="877" xlink:href="#Fuse:bn_熔断器1_0" y="809"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195190291" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\TERM_0901熔断器" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,877,809) scale(1,1) translate(-10,-23)" width="20" x="877" y="809"/></g>
</g>
<g id="Status_Layer">
 <g id="126000113">
  <use class="kv-1" height="40" transform="rotate(0,56,255) scale(0.7,0.7) translate(-6,89.2857)" width="60" x="56" xlink:href="#Status:bn_工况退出颜色显示_0" y="255"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,56,255) scale(0.7,0.7) translate(-6,89.2857)" width="60" x="56" y="255"/></g>
 <g id="126000114">
  <use class="kv-1" height="40" transform="rotate(0,137,255) scale(0.7,0.7) translate(28.7143,89.2857)" width="60" x="137" xlink:href="#Status:bn_不变化颜色显示_0" y="255"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,137,255) scale(0.7,0.7) translate(28.7143,89.2857)" width="60" x="137" y="255"/></g>
 <g id="126000115">
  <use class="kv-1" height="40" transform="rotate(0,222,255) scale(0.7,0.7) translate(65.1429,89.2857)" width="60" x="222" xlink:href="#Status:bn_越限颜色显示_0" y="255"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,222,255) scale(0.7,0.7) translate(65.1429,89.2857)" width="60" x="222" y="255"/></g>
 <g id="126000116">
  <use class="kv-1" height="40" transform="rotate(0,304,255) scale(0.7,0.7) translate(100.286,89.2857)" width="60" x="304" xlink:href="#Status:bn_非实测颜色显示_0" y="255"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,304,255) scale(0.7,0.7) translate(100.286,89.2857)" width="60" x="304" y="255"/></g>
 <g id="126000117">
  <use class="kv-1" height="40" transform="rotate(0,386,255) scale(0.7,0.7) translate(135.429,89.2857)" width="60" x="386" xlink:href="#Status:bn_数据封锁颜色显示_0" y="255"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,386,255) scale(0.7,0.7) translate(135.429,89.2857)" width="60" x="386" y="255"/></g>
</g>
<g id="Clock_Layer">
 <g id="56000102">
  
 <metadata/></g>
</g>
<g id="Term_Layer">
 <g id="112000160">
  <use class="kv-1" height="12" transform="rotate(90,932,520) scale(0.618,1) translate(558.091,-6)" width="38" x="932" xlink:href="#Terminal:bn_终端设备9_0" y="520"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,932,520) scale(0.618,1) translate(558.091,-6)" width="38" x="932" y="520"/></g>
 <g id="112000161">
  <use class="kv-1" height="12" transform="rotate(89,932,208) scale(0.613,1) translate(570.392,-6)" width="38" x="932" xlink:href="#Terminal:bn_35kV电缆_0" y="208"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(89,932,208) scale(0.613,1) translate(570.392,-6)" width="38" x="932" y="208"/></g>
 <g id="112000239">
  <use class="kv-1" height="12" transform="rotate(90,1205,821) scale(1,1) translate(-18,-6)" width="38" x="1205" xlink:href="#Terminal:bn_终端设备9_0" y="821"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,1205,821) scale(1,1) translate(-18,-6)" width="38" x="1205" y="821"/></g>
 <g id="112000297">
  <use class="kv-1" height="12" transform="rotate(90,1355,821) scale(1,1) translate(-18,-6)" width="38" x="1355" xlink:href="#Terminal:bn_终端设备9_0" y="821"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,1355,821) scale(1,1) translate(-18,-6)" width="38" x="1355" y="821"/></g>
 <g id="112000313">
  <use class="kv-1" height="12" transform="rotate(90,1505,821) scale(1,1) translate(-18,-6)" width="38" x="1505" xlink:href="#Terminal:bn_终端设备9_0" y="821"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,1505,821) scale(1,1) translate(-18,-6)" width="38" x="1505" y="821"/></g>
 <g id="112000329">
  <use class="kv-1" height="12" transform="rotate(90,1655,821) scale(1,1) translate(-18,-6)" width="38" x="1655" xlink:href="#Terminal:bn_终端设备9_0" y="821"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,1655,821) scale(1,1) translate(-18,-6)" width="38" x="1655" y="821"/></g>
 <g id="112000399">
  <use class="kv10kV" height="70" transform="rotate(0,1035,826) scale(2,2) translate(-528.5,-418)" width="22" x="1035" xlink:href="#Terminal:尚岗临时变站用变_0" y="826"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195190284" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\TERM_2号站用变" Plane="0"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,1035,826) scale(2,2) translate(-528.5,-418)" width="22" x="1035" y="826"/></g>
</g>
<g id="Link_Layer">
 <g id="34000147">
 <path d="M 890 545 L 890 545" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 890 545 L 890 545" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000152">
 <path d="M 837 182 L 837 182" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Plane="0"/>
  </metadata>
 <path d="M 837 182 L 837 182" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000169">
 <path d="M 932 265 L 932 228" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000179_0" Pin1InfoVect0LinkObjId="34000194_1" Pin1InfoVect1LinkObjId="34000195_0" Pin1InfoVect2LinkObjId="34000170_0" Plane="0"/>
  </metadata>
 <path d="M 932 265 L 932 228" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000170">
 <path d="M 932 228 L 932 175" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000194_1" Pin0InfoVect1LinkObjId="34000195_0" Pin0InfoVect2LinkObjId="34000169_1" Pin1InfoVect0LinkObjId="101000193_1" Plane="0"/>
  </metadata>
 <path d="M 932 228 L 932 175" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000187">
 <path d="M 932 362 L 932 397" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000184_1" Pin1InfoVect0LinkObjId="102000156_0" Plane="0"/>
  </metadata>
 <path d="M 932 362 L 932 397" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000194">
 <path d="M 902 228 L 932 228" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133000174_0" Pin1InfoVect0LinkObjId="34000169_1" Pin1InfoVect1LinkObjId="34000170_0" Pin1InfoVect2LinkObjId="34000195_0" Plane="0"/>
  </metadata>
 <path d="M 902 228 L 932 228" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000195">
 <path d="M 932 228 L 964 228" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000194_1" Pin0InfoVect1LinkObjId="34000169_1" Pin0InfoVect2LinkObjId="34000170_0" Pin1InfoVect0LinkObjId="107000171_0" Plane="0"/>
  </metadata>
 <path d="M 932 228 L 964 228" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000196">
 <path d="M 904 534 L 932 534" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133000178_0" Pin1InfoVect0LinkObjId="34000197_1" Pin1InfoVect1LinkObjId="34000198_0" Plane="0"/>
  </metadata>
 <path d="M 904 534 L 932 534" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000197">
 <path d="M 932 499 L 932 534" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="102000156_1" Pin1InfoVect0LinkObjId="34000196_1" Pin1InfoVect1LinkObjId="34000198_0" Plane="0"/>
  </metadata>
 <path d="M 932 499 L 932 534" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000198">
 <path d="M 932 534 L 932 539 L 932 571" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000196_1" Pin0InfoVect1LinkObjId="34000197_1" Pin1InfoVect0LinkObjId="110000189_0" Plane="0"/>
  </metadata>
 <path d="M 932 534 L 932 539 L 932 571" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000207">
 <path d="M 877 754 L 949 754 L 949 780" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000208_1" Pin0InfoVect1LinkObjId="34000209_0" Pin1InfoVect0LinkObjId="133000214_0" Plane="0"/>
  </metadata>
 <path d="M 877 754 L 949 754 L 949 780" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000208">
 <path d="M 877 715 L 877 754" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000211_1" Pin1InfoVect0LinkObjId="34000207_0" Pin1InfoVect1LinkObjId="34000209_0" Plane="0"/>
  </metadata>
 <path d="M 877 715 L 877 754" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000209">
 <path d="M 877 754 L 877 789" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000207_0" Pin0InfoVect1LinkObjId="34000208_1" Pin1InfoVect0LinkObjId="131000213_0" Plane="0"/>
  </metadata>
 <path d="M 877 754 L 877 789" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000210">
 <path d="M 877 829 L 877 891" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="131000213_1" Pin1InfoVect0LinkObjId="107000212_0" Plane="0"/>
  </metadata>
 <path d="M 877 829 L 877 891" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000252">
 <path d="M 1035 727 L 1035 664" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000235_1" Pin1InfoVect0LinkObjId="30000199_0" Plane="0"/>
  </metadata>
 <path d="M 1035 727 L 1035 664" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000253">
 <path d="M 877 701 L 877 664" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000211_0" Pin1InfoVect0LinkObjId="30000199_0" Plane="0"/>
  </metadata>
 <path d="M 877 701 L 877 664" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000254">
 <path d="M 1035 790 L 1035 826" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000235_0" Pin1InfoVect0LinkObjId="112000399_0" Plane="0"/>
  </metadata>
 <path d="M 1035 790 L 1035 826" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000256">
 <path d="M 1205 724 L 1205 664" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000237_0" Pin1InfoVect0LinkObjId="30000199_0" Plane="0"/>
  </metadata>
 <path d="M 1205 724 L 1205 664" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000263">
 <path d="M 1205 789 L 1205 835" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000237_1" Pin1InfoVect0LinkObjId="34000266_1" Pin1InfoVect1LinkObjId="32000258_0" Pin1InfoVect2LinkObjId="34000264_0" Pin1InfoVect3LinkObjId="34000265_1" Plane="0"/>
  </metadata>
 <path d="M 1205 789 L 1205 835" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000264">
 <path d="M 1205 835 L 1205 857" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000266_1" Pin0InfoVect1LinkObjId="34000263_1" Pin0InfoVect2LinkObjId="32000258_0" Pin0InfoVect3LinkObjId="34000265_1" Plane="0"/>
  </metadata>
 <path d="M 1205 835 L 1205 857" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000265">
 <path d="M 1181 835 L 1205 835" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111000249_0" Pin1InfoVect0LinkObjId="34000266_1" Pin1InfoVect1LinkObjId="34000263_1" Pin1InfoVect2LinkObjId="34000264_0" Pin1InfoVect3LinkObjId="32000258_0" Plane="0"/>
  </metadata>
 <path d="M 1181 835 L 1205 835" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000266">
 <path d="M 1231 835 L 1205 835" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133000240_0" Pin1InfoVect0LinkObjId="32000258_0" Pin1InfoVect1LinkObjId="34000263_1" Pin1InfoVect2LinkObjId="34000264_0" Pin1InfoVect3LinkObjId="34000265_1" Plane="0"/>
  </metadata>
 <path d="M 1231 835 L 1205 835" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000290">
 <path d="M 1355 724 L 1355 664" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000295_0" Pin1InfoVect0LinkObjId="30000199_0" Plane="0"/>
  </metadata>
 <path d="M 1355 724 L 1355 664" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000291">
 <path d="M 1355 789 L 1355 835" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000295_1" Pin1InfoVect0LinkObjId="34000294_1" Pin1InfoVect1LinkObjId="32000286_0" Pin1InfoVect2LinkObjId="34000292_0" Pin1InfoVect3LinkObjId="34000293_1" Plane="0"/>
  </metadata>
 <path d="M 1355 789 L 1355 835" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000292">
 <path d="M 1355 835 L 1355 857" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000294_1" Pin0InfoVect1LinkObjId="34000291_1" Pin0InfoVect2LinkObjId="32000286_0" Pin0InfoVect3LinkObjId="34000293_1" Plane="0"/>
  </metadata>
 <path d="M 1355 835 L 1355 857" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000293">
 <path d="M 1331 835 L 1355 835" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111000296_0" Pin1InfoVect0LinkObjId="34000294_1" Pin1InfoVect1LinkObjId="34000291_1" Pin1InfoVect2LinkObjId="34000292_0" Pin1InfoVect3LinkObjId="32000286_0" Plane="0"/>
  </metadata>
 <path d="M 1331 835 L 1355 835" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000294">
 <path d="M 1381 835 L 1355 835" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133000298_0" Pin1InfoVect0LinkObjId="32000286_0" Pin1InfoVect1LinkObjId="34000291_1" Pin1InfoVect2LinkObjId="34000292_0" Pin1InfoVect3LinkObjId="34000293_1" Plane="0"/>
  </metadata>
 <path d="M 1381 835 L 1355 835" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000310">
 <path d="M 1531 835 L 1505 835" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133000314_0" Pin1InfoVect0LinkObjId="34000309_1" Pin1InfoVect1LinkObjId="34000308_0" Pin1InfoVect2LinkObjId="32000302_0" Pin1InfoVect3LinkObjId="34000307_1" Plane="0"/>
  </metadata>
 <path d="M 1531 835 L 1505 835" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000309">
 <path d="M 1481 835 L 1505 835" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111000312_0" Pin1InfoVect0LinkObjId="32000302_0" Pin1InfoVect1LinkObjId="34000307_1" Pin1InfoVect2LinkObjId="34000308_0" Pin1InfoVect3LinkObjId="34000310_1" Plane="0"/>
  </metadata>
 <path d="M 1481 835 L 1505 835" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000308">
 <path d="M 1505 835 L 1505 857" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000309_1" Pin0InfoVect1LinkObjId="32000302_0" Pin0InfoVect2LinkObjId="34000307_1" Pin0InfoVect3LinkObjId="34000310_1" Plane="0"/>
  </metadata>
 <path d="M 1505 835 L 1505 857" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000307">
 <path d="M 1505 789 L 1505 835" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000311_1" Pin1InfoVect0LinkObjId="34000309_1" Pin1InfoVect1LinkObjId="34000308_0" Pin1InfoVect2LinkObjId="34000310_1" Pin1InfoVect3LinkObjId="32000302_0" Plane="0"/>
  </metadata>
 <path d="M 1505 789 L 1505 835" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000306">
 <path d="M 1505 724 L 1505 664" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000311_0" Pin1InfoVect0LinkObjId="30000199_0" Plane="0"/>
  </metadata>
 <path d="M 1505 724 L 1505 664" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000322">
 <path d="M 1655 724 L 1655 664" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000327_0" Pin1InfoVect0LinkObjId="30000199_0" Plane="0"/>
  </metadata>
 <path d="M 1655 724 L 1655 664" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000323">
 <path d="M 1655 789 L 1655 835" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000327_1" Pin1InfoVect0LinkObjId="32000318_0" Pin1InfoVect1LinkObjId="34000324_0" Pin1InfoVect2LinkObjId="34000325_1" Pin1InfoVect3LinkObjId="34000326_1" Plane="0"/>
  </metadata>
 <path d="M 1655 789 L 1655 835" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000324">
 <path d="M 1655 835 L 1655 857" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000323_1" Pin0InfoVect1LinkObjId="34000325_1" Pin0InfoVect2LinkObjId="32000318_0" Pin0InfoVect3LinkObjId="34000326_1" Plane="0"/>
  </metadata>
 <path d="M 1655 835 L 1655 857" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000325">
 <path d="M 1631 835 L 1655 835" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111000328_0" Pin1InfoVect0LinkObjId="34000323_1" Pin1InfoVect1LinkObjId="32000318_0" Pin1InfoVect2LinkObjId="34000324_0" Pin1InfoVect3LinkObjId="34000326_1" Plane="0"/>
  </metadata>
 <path d="M 1631 835 L 1655 835" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000326">
 <path d="M 1681 835 L 1655 835" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133000330_0" Pin1InfoVect0LinkObjId="34000323_1" Pin1InfoVect1LinkObjId="34000325_1" Pin1InfoVect2LinkObjId="34000324_0" Pin1InfoVect3LinkObjId="32000318_0" Plane="0"/>
  </metadata>
 <path d="M 1681 835 L 1655 835" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000331">
 <path d="M 932 626 L 932 664" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110000189_1" Pin1InfoVect0LinkObjId="30000199_0" Plane="0"/>
  </metadata>
 <path d="M 932 626 L 932 664" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000338">
 <path d="M 932 143 L 932 130" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000193_0" Pin1InfoVect0LinkObjId="36000333_0" Plane="0"/>
  </metadata>
 <path d="M 932 143 L 932 130" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000343">
 <path d="M 909 320 L 932 320" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111000342_0" Pin1InfoVect0LinkObjId="34000344_1" Pin1InfoVect1LinkObjId="34000345_0" Plane="0"/>
  </metadata>
 <path d="M 909 320 L 932 320" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000344">
 <path d="M 932 297 L 932 320" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000179_1" Pin1InfoVect0LinkObjId="34000343_1" Pin1InfoVect1LinkObjId="34000345_0" Plane="0"/>
  </metadata>
 <path d="M 932 297 L 932 320" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000345">
 <path d="M 932 320 L 932 331" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000343_1" Pin0InfoVect1LinkObjId="34000344_1" Pin1InfoVect0LinkObjId="100000184_0" Plane="0"/>
  </metadata>
 <path d="M 932 320 L 932 331" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="MeasurementValue_Layer">
 <g id="33000074">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="287" xml:space="preserve" y="746">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="117093852304638531" ObjectName="版纳_35kV_尚岗临时变\XFMR_1号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000072">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="263" xml:space="preserve" y="573">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000073">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="263" xml:space="preserve" y="630">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000135">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1071" xml:space="preserve" y="528">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709664" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\XF_1号主变-低:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000136">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1071" xml:space="preserve" y="558">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375322986382624" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\XF_1号主变-低:Q" Plane="0"/>
  </metadata>
 </g>
 <g id="33000137">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1071" xml:space="preserve" y="588">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375451835401504" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\XF_1号主变-低:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33000140">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1068" xml:space="preserve" y="372">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375451835401503" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变35kV\XF_1号主变-高:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33000139">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1068" xml:space="preserve" y="343">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375322986382623" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变35kV\XF_1号主变-高:Q" Plane="0"/>
  </metadata>
 </g>
 <g id="33000138">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1068" xml:space="preserve" y="314">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709663" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变35kV\XF_1号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000200">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1848" xml:space="preserve" y="713">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115405084048753574" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\BS_10kVI母:V_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33000202">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1848" xml:space="preserve" y="689">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115404869300388774" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\BS_10kVI母:V" Plane="0"/>
  </metadata>
 </g>
 <g id="33000205">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1848" xml:space="preserve" y="738">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115405126998426534" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\BS_10kVI母:V_B" Plane="0"/>
  </metadata>
 </g>
 <g id="33000259">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1185" xml:space="preserve" y="937">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967862203484060" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\LD_盟尚联络线:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000260">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1185" xml:space="preserve" y="957">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967905153157020" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\LD_盟尚联络线:Q" Plane="0"/>
  </metadata>
 </g>
 <g id="33000262">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1185" xml:space="preserve" y="977">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052502940" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\LD_盟尚联络线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33000287">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1335" xml:space="preserve" y="937">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967862203484061" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\LD_尚勇线:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000288">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1335" xml:space="preserve" y="957">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967905153157021" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\LD_尚勇线:Q" Plane="0"/>
  </metadata>
 </g>
 <g id="33000289">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1335" xml:space="preserve" y="977">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052502941" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\LD_尚勇线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33000303">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1485" xml:space="preserve" y="937">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967862203484062" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\LD_尚岗线:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000304">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1485" xml:space="preserve" y="957">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967905153157022" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\LD_尚岗线:Q" Plane="0"/>
  </metadata>
 </g>
 <g id="33000305">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1485" xml:space="preserve" y="977">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052502942" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\LD_尚岗线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33000319">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1635" xml:space="preserve" y="937">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967862203484063" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\LD_盐厂线、雷达线:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000320">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1635" xml:space="preserve" y="957">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967905153157023" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\LD_盐厂线、雷达线:Q" Plane="0"/>
  </metadata>
 </g>
 <g id="33000321">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1635" xml:space="preserve" y="977">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052502943" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\LD_盐厂线、雷达线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33000376">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1848" xml:space="preserve" y="761">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115405169948099494" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变10kV\BS_10kVI母:V_C" Plane="0"/>
  </metadata>
 </g>
 <g id="33000379">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="287" xml:space="preserve" y="796">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="117093826534834755" ObjectName="版纳_35kV_尚岗临时变\XFMR_1号主变:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000381">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1848" xml:space="preserve" y="786">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795532800" ObjectName="122723089845859840:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33000385">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="31" stroke="rgb(255,255,0)" writing-mode="lr" x="288" xml:space="preserve" y="844">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="117375408885728543" ObjectName="版纳_35kV_尚岗临时变\版纳_35kV_尚岗临时变35kV\XF_1号主变-高:TAP" Plane="0"/>
  </metadata>
 </g>
 <g id="33000391">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="33" font-size="33" font-width="33" stroke="rgb(255,255,0)" writing-mode="lr" x="247" xml:space="preserve" y="897">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795532805" ObjectName="122723089845859845:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33000392">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="33" font-size="33" font-width="33" stroke="rgb(255,255,0)" writing-mode="lr" x="247" xml:space="preserve" y="941">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795532804" ObjectName="122723089845859844:M" Plane="0"/>
  </metadata>
 </g>
</g>
<g id="Text_Layer">
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="60" font-size="37" font-width="37" stroke="rgb(255,255,254)" writing-mode="lr" x="165" xml:space="preserve" y="98">35kV曼迈变</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="33" font-size="33" font-width="35" stroke="rgb(255,255,254)" writing-mode="tb" x="67" xml:space="preserve" y="812">变</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="33" font-size="33" font-width="35" stroke="rgb(255,255,254)" writing-mode="tb" x="67" xml:space="preserve" y="773">主</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="33" font-size="33" font-width="35" stroke="rgb(255,255,254)" writing-mode="tb" x="67" xml:space="preserve" y="736">号</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="33" font-size="33" font-width="35" stroke="rgb(255,255,254)" writing-mode="tb" x="65" xml:space="preserve" y="469">位</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="33" font-size="33" font-width="35" stroke="rgb(255,255,254)" writing-mode="tb" x="66" xml:space="preserve" y="397">单</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="27" font-size="27" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="67" xml:space="preserve" y="628">总无功</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="33" stroke="rgb(170,0,0)" writing-mode="lr" x="352" xml:space="preserve" y="513">℃</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="33" stroke="rgb(255,255,254)" writing-mode="lr" x="265" xml:space="preserve" y="513">温度:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="33" stroke="rgb(170,0,0)" writing-mode="lr" x="208" xml:space="preserve" y="513">Hz</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="33" stroke="rgb(255,255,254)" writing-mode="lr" x="128" xml:space="preserve" y="513">频率:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="33" stroke="rgb(170,0,0)" writing-mode="lr" x="352" xml:space="preserve" y="458">kV</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="33" stroke="rgb(255,255,254)" writing-mode="lr" x="265" xml:space="preserve" y="458">电压:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="33" stroke="rgb(170,0,0)" writing-mode="lr" x="208" xml:space="preserve" y="458">A</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="33" stroke="rgb(255,255,254)" writing-mode="lr" x="128" xml:space="preserve" y="458">电流:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="33" stroke="rgb(170,0,0)" writing-mode="lr" x="352" xml:space="preserve" y="403">MVr</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="33" stroke="rgb(255,255,254)" writing-mode="lr" x="265" xml:space="preserve" y="403">无功:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="33" stroke="rgb(255,255,254)" writing-mode="lr" x="128" xml:space="preserve" y="403">有功:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="33" stroke="rgb(170,0,0)" writing-mode="lr" x="208" xml:space="preserve" y="403">MW</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="29" font-size="29" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="174" xml:space="preserve" y="746">油温</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="33" font-size="33" font-width="35" stroke="rgb(255,255,254)" writing-mode="tb" x="67" xml:space="preserve" y="704">一</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="27" font-size="27" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="67" xml:space="preserve" y="571">总有功</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="27" font-size="27" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="118" xml:space="preserve" y="685">事故总:</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="26" font-size="26" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="30" xml:space="preserve" y="323">工况</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="26" font-size="26" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="30" xml:space="preserve" y="349">退出</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="26" font-size="26" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="127" xml:space="preserve" y="323">不</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="26" font-size="26" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="114" xml:space="preserve" y="351">变化</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="26" font-size="26" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="194" xml:space="preserve" y="338">越限</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="26" font-size="26" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="291" xml:space="preserve" y="323">非</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="26" font-size="26" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="275" xml:space="preserve" y="351">实测</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="26" font-size="26" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="358" xml:space="preserve" y="323">数据</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="26" font-size="26" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="358" xml:space="preserve" y="349">封锁</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="26" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1026" xml:space="preserve" y="422">   1号主变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="26" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1026" xml:space="preserve" y="448">SZ11-10000/35</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="1043" xml:space="preserve" y="372">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="1043" xml:space="preserve" y="343">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="1044" xml:space="preserve" y="314">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="1045" xml:space="preserve" y="529">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="1044" xml:space="preserve" y="558">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="1045" xml:space="preserve" y="587">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="942" xml:space="preserve" y="291">3011</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="22" font-size="22" font-width="22" stroke="rgb(255,255,254)" writing-mode="lr" x="947" xml:space="preserve" y="359">301</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="947" xml:space="preserve" y="609">001</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="942" xml:space="preserve" y="169">3016</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="26" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1674" xml:space="preserve" y="652">10kVI母</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="23" font-size="23" font-width="23" stroke="rgb(255,255,254)" writing-mode="lr" x="797" xml:space="preserve" y="817">0901</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="22" font-size="22" font-width="22" stroke="rgb(255,255,254)" writing-mode="lr" x="1223" xml:space="preserve" y="759">021</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="22" font-size="22" font-width="22" stroke="rgb(255,255,254)" writing-mode="lr" x="1051" xml:space="preserve" y="759">0R2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1138" xml:space="preserve" y="865">0217</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="26" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1138" xml:space="preserve" y="1031">盟尚联络线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="22" font-size="22" font-width="22" stroke="rgb(255,255,254)" writing-mode="lr" x="1373" xml:space="preserve" y="759">022</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1288" xml:space="preserve" y="865">0227</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="26" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1314" xml:space="preserve" y="1031">尚勇线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="26" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1458" xml:space="preserve" y="1031">尚岗线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1438" xml:space="preserve" y="865">0237</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="22" font-size="22" font-width="22" stroke="rgb(255,255,254)" writing-mode="lr" x="1523" xml:space="preserve" y="759">023</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="22" font-size="22" font-width="22" stroke="rgb(255,255,254)" writing-mode="lr" x="1673" xml:space="preserve" y="759">024</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1588" xml:space="preserve" y="865">0247</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="26" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1587" xml:space="preserve" y="1031">盐厂线、雷达线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="26" font-size="26" font-width="26" stroke="rgb(230,232,254)" writing-mode="lr" x="889" xml:space="preserve" y="40">腊尚线</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="64" font-size="37" font-width="37" stroke="rgb(0,0,0)" writing-mode="lr" x="146" xml:space="preserve" y="109">35kV尚岗临时变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="866" xml:space="preserve" y="350">30117</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="1157" xml:space="preserve" y="957">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="1157" xml:space="preserve" y="938">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="1157" xml:space="preserve" y="977">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="1307" xml:space="preserve" y="964">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="1307" xml:space="preserve" y="943">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="1307" xml:space="preserve" y="981">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="1460" xml:space="preserve" y="964">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="1460" xml:space="preserve" y="943">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="1460" xml:space="preserve" y="981">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="1614" xml:space="preserve" y="980">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="1614" xml:space="preserve" y="943">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="1614" xml:space="preserve" y="964">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="1793" xml:space="preserve" y="680">Uab</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="1793" xml:space="preserve" y="705">Ua</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="1793" xml:space="preserve" y="729">Ub</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="1793" xml:space="preserve" y="757">Uc</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="29" font-size="29" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="174" xml:space="preserve" y="798">绕温</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(35,169,128)" writing-mode="lr" x="1793" xml:space="preserve" y="784">3U0</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="29" font-size="29" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="175" xml:space="preserve" y="844">档位</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="36" font-size="35" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="49" xml:space="preserve" y="896">合母电压</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="36" font-size="35" font-width="35" stroke="rgb(255,255,254)" writing-mode="lr" x="49" xml:space="preserve" y="946">控母电压</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="26" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="975" xml:space="preserve" y="1031">2号站用变</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href="综自改造勐仑变.other.svg"><rect fill-opacity="0" height="107" stroke-opacity="0" stroke-width="1" width="402" x="19" y="20"/></g>
</g>
</svg>