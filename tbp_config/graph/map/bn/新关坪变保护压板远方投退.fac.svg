<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="sys" height="800" id="thSvg" viewBox="0 0 1700 800" width="1700">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="Protect:bn_保护图元1_0" viewBox="0,0,18,18">
 <rect AFMask="2147483647" Plane="0" fill="none" height="13" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,8,8)" width="13" x="2" y="2"/>
</symbol>
<symbol id="Protect:bn_保护图元1_1" viewBox="0,0,18,18">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="13" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,8,8)" width="13" x="2" y="2"/>
</symbol>
<symbol id="Protect:软压板投退_0" viewBox="0,0,50,50">
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,0,0)" stroke-width="3" x1="40" x2="13" y1="31" y2="4"/>
 <circle AFMask="2147483647" Plane="0" cx="9" cy="31" fill="none" r="4" stroke="rgb(255,0,0)" stroke-width="8"/>
 <circle AFMask="2147483647" Plane="0" cx="40" cy="31" fill="none" r="4" stroke="rgb(255,0,0)" stroke-width="8"/>
</symbol>
<symbol id="Protect:软压板投退_1" viewBox="0,0,50,50">
 <circle AFMask="2147483647" Plane="0" cx="9" cy="24" fill="none" r="4" stroke="rgb(0,255,0)" stroke-width="8"/>
 <circle AFMask="2147483647" Plane="0" cx="40" cy="24" fill="none" r="4" stroke="rgb(0,255,0)" stroke-width="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,255,0)" stroke-width="3" x1="9" x2="41" y1="24" y2="24"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="-1" InitShowingPlane="0," fill="rgb(0,0,0)" height="800" width="1700" x="0" y="0"/>
</g>
<g id="Other_Layer">
 
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="530" x2="1160" y1="99" y2="99"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="658" x2="1160" y1="159" y2="159"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="529" x2="529" y1="99" y2="381"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="658" x2="658" y1="98" y2="381"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1161" x2="1161" y1="99" y2="380"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1087" x2="1087" y1="100" y2="372"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="658" x2="1160" y1="189" y2="189"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="658" x2="1161" y1="219" y2="219"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="658" x2="1160" y1="249" y2="249"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="658" x2="1160" y1="279" y2="279"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="658" x2="1160" y1="309" y2="309"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="659" x2="1163" y1="339" y2="339"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="530" x2="1160" y1="129" y2="129"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="529" x2="1161" y1="372" y2="372"/>
</g>
<g id="Protect_Layer">
 <g id="127000402">
  <use class="kv35kV" height="50" transform="rotate(0,1127,144) scale(0.6,0.6) translate(726.333,75)" width="50" x="1127" xlink:href="#Protect:软压板投退_0" y="144"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892567804" ObjectName="122160139892567804" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1127,144) scale(0.6,0.6) translate(726.333,75)" width="50" x="1127" y="144"/></g>
 <g id="127000403">
  <use class="kv35kV" height="50" transform="rotate(0,1127,173) scale(0.6,0.6) translate(726.333,94.3333)" width="50" x="1127" xlink:href="#Protect:软压板投退_0" y="173"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892567805" ObjectName="122160139892567805" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1127,173) scale(0.6,0.6) translate(726.333,94.3333)" width="50" x="1127" y="173"/></g>
 <g id="127000405">
  <use class="kv35kV" height="50" transform="rotate(0,1127,204) scale(0.6,0.6) translate(726.333,115)" width="50" x="1127" xlink:href="#Protect:软压板投退_0" y="204"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892567813" ObjectName="122160139892567813" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1127,204) scale(0.6,0.6) translate(726.333,115)" width="50" x="1127" y="204"/></g>
 <g id="127000406">
  <use class="kv35kV" height="50" transform="rotate(0,1127,234) scale(0.6,0.6) translate(726.333,135)" width="50" x="1127" xlink:href="#Protect:软压板投退_0" y="234"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892567830" ObjectName="122160139892567830" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1127,234) scale(0.6,0.6) translate(726.333,135)" width="50" x="1127" y="234"/></g>
 <g id="127000407">
  <use class="kv35kV" height="50" transform="rotate(0,1127,264) scale(0.6,0.6) translate(726.333,155)" width="50" x="1127" xlink:href="#Protect:软压板投退_0" y="264"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892567831" ObjectName="122160139892567831" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1127,264) scale(0.6,0.6) translate(726.333,155)" width="50" x="1127" y="264"/></g>
 <g id="127000408">
  <use class="kv10kV" height="50" transform="rotate(0,1127,294) scale(0.6,0.6) translate(726.333,175)" width="50" x="1127" xlink:href="#Protect:软压板投退_0" y="294"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892567847" ObjectName="122160139892567847" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1127,294) scale(0.6,0.6) translate(726.333,175)" width="50" x="1127" y="294"/></g>
 <g id="127000409">
  <use class="kv10kV" height="50" transform="rotate(0,1127,324) scale(0.6,0.6) translate(726.333,195)" width="50" x="1127" xlink:href="#Protect:软压板投退_0" y="324"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892567848" ObjectName="122160139892567848" Plane="0"/>
  </metadata>
 <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1127,324) scale(0.6,0.6) translate(726.333,195)" width="50" x="1127" y="324"/></g>
 <g id="127000455">
  <use class="kv35kV" height="18" transform="rotate(0,1128,355) scale(1.61,1.61) translate(-436.379,-143.503)" width="18" x="1128" xlink:href="#Protect:bn_保护图元1_0" y="355"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892568119" ObjectName="122160139892568119" Plane="0"/>
  </metadata>
 <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1128,355) scale(1.61,1.61) translate(-436.379,-143.503)" width="18" x="1128" y="355"/></g>
</g>
<g id="Text_Layer">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="30" font-size="30" font-width="30" stroke="rgb(85,255,255)" writing-mode="lr" x="674" xml:space="preserve" y="44">关坪变继电保护远方操作</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="571" xml:space="preserve" y="126">线路</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="557" xml:space="preserve" y="252">1号主变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="735" xml:space="preserve" y="126">名称</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="683" xml:space="preserve" y="156">＃1主变本体测控出口使能软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="683" xml:space="preserve" y="186">＃1主变本体测控外间隔连锁退出软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="683" xml:space="preserve" y="217">1主变差动保护差动保护软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="683" xml:space="preserve" y="247">＃1主变高后备保护高压侧电压投入软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="683" xml:space="preserve" y="277">＃1主变高后备保护间隙保护投入软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="683" xml:space="preserve" y="307">＃1主变低后备保护低压侧电压投入软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="683" xml:space="preserve" y="337">＃1主变低后备保护简易母线保护投入软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1095" xml:space="preserve" y="126">软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="683" xml:space="preserve" y="366">＃1主变 本体测控信号复归</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href="版纳_35kV_新关坪变.fac.svg"><rect fill-opacity="0" height="46" stroke-opacity="0" stroke-width="1" width="443" x="652" y="61"/></g>
</g>
</svg>