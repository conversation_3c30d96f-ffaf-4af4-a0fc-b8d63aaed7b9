<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="113997365567815959" height="1000" id="thSvg" viewBox="0 0 2200 1000" width="2200">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="Transformer2:123_0" viewBox="0,0,76,80">
 <use Plane="0" x="20" xlink:href="#terminal" y="3"/>
 <circle AFMask="2147483647" Plane="0" cx="19" cy="21" fill="none" r="18" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="20" x2="28" y1="21" y2="29"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="20" x2="20" y1="21" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="20" x2="12" y1="21" y2="29"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="20" x2="66" y1="21" y2="21"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="66" x2="66" y1="21" y2="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="63" x2="69" y1="34" y2="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="65" x2="68" y1="36" y2="36"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="64" x2="64" y1="36" y2="36"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="65" x2="67" y1="38" y2="38"/>
</symbol>
<symbol id="Transformer2:123_1" viewBox="0,0,76,80">
 <use Plane="1" x="20" xlink:href="#terminal" y="72"/>
 <circle AFMask="2147483647" Plane="1" cx="19" cy="53" fill="none" r="18" stroke="rgb(0,0,255)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 19 47 L 10 60 L 29 60 Z" fill="none" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Arrester:bn_避雷器11_0" viewBox="0,0,40,20">
 <use Plane="0" x="36" xlink:href="#terminal" y="10"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="10" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,20,10)" width="22" x="9" y="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="29" x2="23" y1="10" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="5" x2="5" y1="6" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="3" x2="3" y1="7" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="22" x2="22" y1="8" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="6" x2="9" y1="10" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="29" x2="35" y1="10" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="22" x2="19" y1="8" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="19" x2="22" y1="10" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1" x2="1" y1="8" y2="12"/>
</symbol>
<symbol id="Disconnector:bn_刀闸15_0" viewBox="0,0,18,40">
 <use Plane="0" x="9" xlink:href="#terminal" y="2"/>
 <use Plane="0" x="9" xlink:href="#terminal" y="38"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="9" y1="5" y2="2"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="16" y1="2" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="9" y1="8" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="16" y1="5" y2="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="9" y1="35" y2="38"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="16" y1="38" y2="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="9" y1="32" y2="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="16" y1="35" y2="32"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="9" y1="5" y2="35"/>
</symbol>
<symbol id="Disconnector:bn_刀闸15_1" viewBox="0,0,18,40">
 <use Plane="0" x="9" xlink:href="#terminal" y="2"/>
 <use Plane="0" x="9" xlink:href="#terminal" y="38"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="9" y1="5" y2="2"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="16" y1="2" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="9" y1="6" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="16" y1="3" y2="6"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="9" y1="35" y2="38"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="16" y1="38" y2="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="9" y1="34" y2="37"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="16" y1="37" y2="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="9" y1="3" y2="37"/>
</symbol>
<symbol id="Disconnector:bn_刀闸25_0" viewBox="0,0,16,30">
 <use Plane="0" x="7" xlink:href="#terminal" y="3"/>
 <use Plane="0" x="7" xlink:href="#terminal" y="26"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="26" y2="22"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="7" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="13" y1="22" y2="22"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="13" y1="8" y2="20"/>
</symbol>
<symbol id="Disconnector:bn_刀闸25_1" viewBox="0,0,16,30">
 <use Plane="0" x="7" xlink:href="#terminal" y="3"/>
 <use Plane="0" x="7" xlink:href="#terminal" y="26"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="26" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="13" y1="22" y2="22"/>
</symbol>
<symbol id="Disconnector:bn_刀闸3_0" viewBox="0,0,20,40">
 <use Plane="0" x="7" xlink:href="#terminal" y="3"/>
 <use Plane="0" x="7" xlink:href="#terminal" y="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="35" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="7" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="1" x2="15" y1="31" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="18" y1="7" y2="31"/>
</symbol>
<symbol id="Disconnector:bn_刀闸3_1" viewBox="0,0,20,40">
 <use Plane="0" x="7" xlink:href="#terminal" y="3"/>
 <use Plane="0" x="7" xlink:href="#terminal" y="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="35" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="1" x2="15" y1="31" y2="31"/>
</symbol>
<symbol id="Disconnector:bn_刀闸5_0" viewBox="0,0,20,40">
 <use Plane="0" x="7" xlink:href="#terminal" y="4"/>
 <use Plane="0" x="7" xlink:href="#terminal" y="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="35" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="9" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="1" x2="15" y1="10" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="18" x2="8" y1="10" y2="30"/>
</symbol>
<symbol id="Disconnector:bn_刀闸5_1" viewBox="0,0,20,40">
 <use Plane="0" x="7" xlink:href="#terminal" y="4"/>
 <use Plane="0" x="7" xlink:href="#terminal" y="34"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="35" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="1" x2="15" y1="10" y2="10"/>
</symbol>
<symbol id="Breaker:bn_断路器2_0" viewBox="0,0,20,40">
 <use Plane="0" x="10" xlink:href="#terminal" y="4"/>
 <use Plane="0" x="10" xlink:href="#terminal" y="36"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="30" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,10,20)" width="14" x="3" y="5"/>
</symbol>
<symbol id="Breaker:bn_断路器2_1" viewBox="0,0,20,40">
 <use Plane="0" x="10" xlink:href="#terminal" y="4"/>
 <use Plane="0" x="10" xlink:href="#terminal" y="36"/>
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,254)" height="30" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,10,20)" width="14" x="3" y="5"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸11_0" viewBox="0,0,34,16">
 <use Plane="0" x="1" xlink:href="#terminal" y="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="28" x2="28" y1="3" y2="15"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="28" x2="17" y1="9" y2="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="30" x2="30" y1="6" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="32" x2="32" y1="8" y2="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="4" x2="17" y1="2" y2="9"/>
 <circle AFMask="2147483647" Plane="0" cx="17" cy="9" fill="none" r="2" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸11_1" viewBox="0,0,34,16">
 <use Plane="0" x="1" xlink:href="#terminal" y="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="28" x2="28" y1="3" y2="15"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="28" x2="17" y1="9" y2="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="30" x2="30" y1="6" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="32" x2="32" y1="8" y2="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="1" x2="17" y1="8" y2="9"/>
 <circle AFMask="2147483647" Plane="0" cx="17" cy="9" fill="none" r="2" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸16_0" viewBox="0,0,40,20">
 <use Plane="0" x="36" xlink:href="#terminal" y="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="31" y1="5" y2="19"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="13" x2="10" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="7" x2="7" y1="8" y2="16"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="5" x2="5" y1="10" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="35" x2="31" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="9" y1="6" y2="18"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="13" x2="31" y1="12" y2="2"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸16_1" viewBox="0,0,40,20">
 <use Plane="0" x="36" xlink:href="#terminal" y="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="31" y1="5" y2="19"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="35" x2="10" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="7" x2="7" y1="8" y2="16"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="5" x2="5" y1="10" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="9" y1="6" y2="18"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸23_0" viewBox="0,0,34,16">
 <use Plane="0" x="1" xlink:href="#terminal" y="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="28" x2="28" y1="3" y2="15"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="28" x2="17" y1="9" y2="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="30" x2="30" y1="6" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="32" x2="32" y1="8" y2="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="4" x2="17" y1="2" y2="9"/>
 <circle AFMask="2147483647" Plane="0" cx="17" cy="9" fill="none" r="2" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸23_1" viewBox="0,0,34,16">
 <use Plane="0" x="1" xlink:href="#terminal" y="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="28" x2="28" y1="3" y2="15"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="28" x2="17" y1="9" y2="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="30" x2="30" y1="6" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="32" x2="32" y1="8" y2="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="1" x2="17" y1="8" y2="9"/>
 <circle AFMask="2147483647" Plane="0" cx="17" cy="9" fill="none" r="2" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Sensitive:gd_敏感替代图元_0" viewBox="0,0,72,36">
 <rect AFMask="2147483647" Plane="0" fill="none" height="32" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,36,18)" width="68" x="2" y="2"/>
</symbol>
<symbol id="PT:qj_ocs电压互感器带接地下（五卷）_0" viewBox="0,0,76,74">
 <use Plane="0" x="61" xlink:href="#terminal" y="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="48" x2="48" y1="60" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="43" x2="53" y1="61" y2="61"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="44" x2="52" y1="63" y2="63"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="46" x2="50" y1="65" y2="65"/>
 <circle AFMask="2147483647" Plane="0" cx="19" cy="28" fill="none" r="8" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="27" cy="49" fill="none" r="8" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="37" cy="39" fill="none" r="8" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="19" cy="39" fill="none" r="8" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="20" x2="20" y1="22" y2="30"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="19" x2="13" y1="22" y2="27"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="19" x2="13" y1="30" y2="27"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="19" x2="19" y1="40" y2="46"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="18" x2="13" y1="40" y2="37"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="19" x2="23" y1="40" y2="37"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="38" x2="42" y1="39" y2="36"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="38" x2="33" y1="39" y2="36"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="38" x2="38" y1="39" y2="45"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="28" x2="28" y1="51" y2="57"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="28" x2="25" y1="51" y2="49"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="28" x2="32" y1="51" y2="48"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="36" x2="36" y1="19" y2="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="21" x2="42" y1="11" y2="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="42" x2="42" y1="8" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="44" x2="44" y1="8" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="26" x2="26" y1="8" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="24" x2="24" y1="8" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="21" x2="12" y1="11" y2="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="44" x2="48" y1="28" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="37" x2="37" y1="29" y2="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="37" x2="41" y1="29" y2="26"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="37" x2="32" y1="29" y2="26"/>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="28" fill="none" r="8" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="12" x2="12" y1="8" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="10" x2="10" y1="9" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="10" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="10" x2="10" y1="13" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="44" x2="58" y1="11" y2="11"/>
</symbol>
<symbol id="PT:药园变电压互感器999_0" viewBox="0,0,120,120">
 <use Plane="0" x="56" xlink:href="#terminal" y="102"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="57" x2="61" y1="43" y2="46"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="57" x2="53" y1="43" y2="46"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="57" x2="57" y1="38" y2="43"/>
 <circle AFMask="2147483647" Plane="0" cx="57" cy="43" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="39" cy="44" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="39" x2="39" y1="39" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="39" x2="35" y1="44" y2="47"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="39" x2="43" y1="44" y2="47"/>
 <circle AFMask="2147483647" Plane="0" cx="39" cy="60" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="39" x2="39" y1="55" y2="60"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="39" x2="35" y1="60" y2="63"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="39" x2="43" y1="60" y2="63"/>
 <circle AFMask="2147483647" Plane="0" cx="47" cy="29" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="56" cy="60" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="57" x2="57" y1="71" y2="101"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="22" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,57,87)" width="10" x="52" y="76"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="56" x2="15" y1="60" y2="60"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="15" x2="15" y1="60" y2="37"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="18" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,15,28)" width="12" x="9" y="19"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="14" x2="16" y1="10" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="12" x2="18" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="10" x2="20" y1="14" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="15" x2="15" y1="14" y2="19"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="1" x2="24" y1="34" y2="17"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="24" x2="27" y1="17" y2="17"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="27" x2="26" y1="17" y2="20"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="56" x2="60" y1="60" y2="63"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="56" x2="52" y1="60" y2="63"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="56" x2="56" y1="55" y2="60"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="41" x2="53" y1="26" y2="26"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="41" x2="47" y1="26" y2="32"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="53" x2="47" y1="26" y2="32"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="15" x2="58" y1="43" y2="43"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="版纳110kV_顺控测试变" InitShowingPlane="0," fill="rgb(0,0,0)" height="1000" width="2200" x="0" y="0"/>
</g>
<g id="Other_Layer">
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="95" x2="544" y1="253" y2="253"/>
 
</g>
<g id="Bus_Layer">
 <g id="30000000">
  <path d="M 656 466 L 1349 466" stroke="rgb(0,0,255)" stroke-width="3"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115404740451369874" ObjectName="版纳110kV_顺控测试变\版纳110kV_顺控测试变/110kV\BS_110kVI母" Plane="0"/>
  <cge:TPSR_Ref TObjectID="115404740451369874"/></metadata>
 <path d="M 656 466 L 1349 466" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="30000015">
  <path d="M 673 946 L 1353 946" stroke="rgb(0,0,255)" stroke-width="4"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115404740451369875" ObjectName="版纳110kV_顺控测试变\版纳110kV_顺控测试变/10kV\BS_10kI母" Plane="0"/>
  <cge:TPSR_Ref TObjectID="115404740451369875"/></metadata>
 <path d="M 673 946 L 1353 946" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="Breaker_Layer">
 <g id="100000025">
  <use class="kv10kV" height="40" transform="rotate(0,1025,852) scale(1,1) translate(-10,-20)" width="20" x="1025" xlink:href="#Breaker:bn_断路器2_0" y="852"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521243782" ObjectName="版纳110kV_顺控测试变\版纳110kV_顺控测试变/10kV\CB_低压测001断路器" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319935622"/>
  <cge:TPSR_Ref TObjectID="114560315521243782"/></metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1025,852) scale(1,1) translate(-10,-20)" width="20" x="1025" y="852"/></g>
 <g id="100000040">
  <use class="kv110kV" height="40" transform="rotate(0,1025,332) scale(1,1) translate(-10,-20)" width="20" x="1025" xlink:href="#Breaker:bn_断路器2_0" y="332"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521243999" ObjectName="版纳110kV_顺控测试变\版纳110kV_顺控测试变/110kV\CB_测试Ⅰ线153断路器" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319935839"/>
  <cge:TPSR_Ref TObjectID="114560315521243999"/></metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1025,332) scale(1,1) translate(-10,-20)" width="20" x="1025" y="332"/></g>
 <g id="100000136">
  <use class="kv110kV" height="40" transform="rotate(0,1026,586) scale(1,1) translate(-10,-20)" width="20" x="1026" xlink:href="#Breaker:bn_断路器2_0" y="586"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521243998" ObjectName="版纳110kV_顺控测试变\版纳110kV_顺控测试变/110kV\CB_#1主变高压侧101断路器" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319935838"/>
  <cge:TPSR_Ref TObjectID="114560315521243998"/></metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1026,586) scale(1,1) translate(-10,-20)" width="20" x="1026" y="586"/></g>
 <g id="100000189">
  <use class="kv-1" height="40" transform="rotate(0,1184,852) scale(1,1) translate(-10,-20)" width="20" x="1184" xlink:href="#Breaker:bn_断路器2_0" y="852"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1184,852) scale(1,1) translate(-10,-20)" width="20" x="1184" y="852"/></g>
</g>
<g id="Disconnector_Layer">
 <g id="101000022">
  <use class="kv110kV" height="30" transform="rotate(0,1026,529) scale(1,1) translate(-8,-15)" width="16" x="1026" xlink:href="#Disconnector:bn_刀闸25_0" y="529"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959761" ObjectName="版纳110kV_顺控测试变\版纳110kV_顺控测试变/110kV\SW_高压侧1011刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346978641"/>
  <cge:TPSR_Ref TObjectID="114841790497959761"/></metadata>
 <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1026,529) scale(1,1) translate(-8,-15)" width="16" x="1026" y="529"/></g>
 <g id="101000044">
  <use class="kv110kV" height="30" transform="rotate(0,1025,416) scale(1,1) translate(-8,-15)" width="16" x="1025" xlink:href="#Disconnector:bn_刀闸25_0" y="416"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959759" ObjectName="版纳110kV_顺控测试变\版纳110kV_顺控测试变/110kV\SW_测试Ⅰ线1531刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346978639"/>
  <cge:TPSR_Ref TObjectID="114841790497959759"/></metadata>
 <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1025,416) scale(1,1) translate(-8,-15)" width="16" x="1025" y="416"/></g>
 <g id="101000115">
  <use class="kv110kV" height="40" transform="rotate(0,1025,274) scale(1,1) translate(-8,-19)" width="20" x="1025" xlink:href="#Disconnector:bn_刀闸3_0" y="274"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959760" ObjectName="版纳110kV_顺控测试变\版纳110kV_顺控测试变/110kV\SW_测试Ⅰ线1532刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346978640"/>
  <cge:TPSR_Ref TObjectID="114841790497959760"/></metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1025,274) scale(1,1) translate(-8,-19)" width="20" x="1025" y="274"/></g>
 <g id="101000140">
  <use class="kv110kV" height="30" transform="rotate(0,1026,642) scale(1,1) translate(-8,-15)" width="16" x="1026" xlink:href="#Disconnector:bn_刀闸25_0" y="642"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497959762" ObjectName="版纳110kV_顺控测试变\版纳110kV_顺控测试变/110kV\SW_高压侧1016刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346978642"/>
  <cge:TPSR_Ref TObjectID="114841790497959762"/></metadata>
 <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1026,642) scale(1,1) translate(-8,-15)" width="16" x="1026" y="642"/></g>
 <g id="101000144">
  <use class="kv10kV" height="30" transform="rotate(0,1025,796) scale(1,1) translate(-8,-15)" width="16" x="1025" xlink:href="#Disconnector:bn_刀闸25_0" y="796"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497961052" ObjectName="版纳110kV_顺控测试变\版纳110kV_顺控测试变/10kV\SW_低压侧0016刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346979932"/>
  <cge:TPSR_Ref TObjectID="114841790497961052"/></metadata>
 <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1025,796) scale(1,1) translate(-8,-15)" width="16" x="1025" y="796"/></g>
 <g id="101000146">
  <use class="kv10kV" height="30" transform="rotate(0,1025,905) scale(1,1) translate(-8,-15)" width="16" x="1025" xlink:href="#Disconnector:bn_刀闸25_0" y="905"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497961051" ObjectName="版纳110kV_顺控测试变\版纳110kV_顺控测试变/10kV\SW_低压侧0011刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346979931"/>
  <cge:TPSR_Ref TObjectID="114841790497961051"/></metadata>
 <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1025,905) scale(1,1) translate(-8,-15)" width="16" x="1025" y="905"/></g>
 <g id="101000158">
  <use class="kv110kV" height="40" transform="rotate(0,853,407) scale(-1,-1) translate(-1714,-834)" width="20" x="853" xlink:href="#Disconnector:bn_刀闸5_0" y="407"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497961054" ObjectName="版纳110kV_顺控测试变\版纳110kV_顺控测试变/110kV\SW_测试站1901刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346979934"/>
  <cge:TPSR_Ref TObjectID="114841790497961054"/></metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,853,407) scale(-1,-1) translate(-1714,-834)" width="20" x="853" y="407"/></g>
 <g id="101000167">
  <use class="kv10kV" height="40" transform="rotate(0,852,878) scale(1,1) translate(-9,-20)" width="18" x="852" xlink:href="#Disconnector:bn_刀闸15_0" y="878"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497961055" ObjectName="版纳110kV_顺控测试变\版纳110kV_顺控测试变/10kV\SW_测试站0901刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346979935"/>
  <cge:TPSR_Ref TObjectID="114841790497961055"/></metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,852,878) scale(1,1) translate(-9,-20)" width="18" x="852" y="878"/></g>
 <g id="101000190">
  <use class="kv-1" height="30" transform="rotate(0,1184,796) scale(1,1) translate(-8,-15)" width="16" x="1184" xlink:href="#Disconnector:bn_刀闸25_0" y="796"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1184,796) scale(1,1) translate(-8,-15)" width="16" x="1184" y="796"/></g>
 <g id="101000191">
  <use class="kv-1" height="30" transform="rotate(0,1184,905) scale(1,1) translate(-8,-15)" width="16" x="1184" xlink:href="#Disconnector:bn_刀闸25_0" y="905"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1184,905) scale(1,1) translate(-8,-15)" width="16" x="1184" y="905"/></g>
</g>
<g id="GroundDisconnector_Layer">
 <g id="111000059">
  <use class="kv110kV" height="16" transform="rotate(0,1073,238) scale(1,1) translate(-1,-8)" width="34" x="1073" xlink:href="#GroundDisconnector:bn_接地刀闸11_0" y="238"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115123265474666689" ObjectName="版纳110kV_顺控测试变\版纳110kV_顺控测试变/110kV\GRNDSW_05311接地刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="115123394323685569"/>
  <cge:TPSR_Ref TObjectID="115123265474666689"/></metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(0,1073,238) scale(1,1) translate(-1,-8)" width="34" x="1073" y="238"/></g>
 <g id="111000109">
  <use class="kv110kV" height="16" transform="rotate(0,1070,374) scale(1,1) translate(-1,-8)" width="34" x="1070" xlink:href="#GroundDisconnector:bn_接地刀闸23_0" y="374"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115123265474666690" ObjectName="版纳110kV_顺控测试变\版纳110kV_顺控测试变/110kV\GRNDSW_05312接地刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="115123394323685570"/>
  <cge:TPSR_Ref TObjectID="115123265474666690"/></metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(0,1070,374) scale(1,1) translate(-1,-8)" width="34" x="1070" y="374"/></g>
 <g id="111000160">
  <use class="kv110kV" height="20" transform="rotate(360,789,365) scale(1,1) translate(-36,-12)" width="40" x="789" xlink:href="#GroundDisconnector:bn_接地刀闸16_0" y="365"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115123265474667692" ObjectName="版纳110kV_顺控测试变\版纳110kV_顺控测试变/110kV\GRNDSW_顺控测试站19017接地刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="115123394323686572"/>
  <cge:TPSR_Ref TObjectID="115123265474667692"/></metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(360,789,365) scale(1,1) translate(-36,-12)" width="40" x="789" y="365"/></g>
</g>
<g id="Transformer2_Layer">
<g id="102000095">
 <g id="1020000950">
  <use class="kv110kV" height="80" transform="rotate(0,1026,719) scale(1,1) translate(-20,-38)" width="76" x="1026" xlink:href="#Transformer2:123_0" y="719"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="117375065288344838" ObjectName="版纳110kV_顺控测试变\版纳110kV_顺控测试变/110kV\XF_＃1主变-高" Plane="0"/>
  </metadata>
 </g>
 <g id="1020000951">
  <use class="kv10kV" height="80" transform="rotate(0,1026,719) scale(1,1) translate(-20,-38)" width="76" x="1026" xlink:href="#Transformer2:123_1" y="719"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="117375065288344839" ObjectName="版纳110kV_顺控测试变\版纳110kV_顺控测试变/10kV\XF_＃1主变-低" Plane="0"/>
  </metadata>
 </g>
 <metadata>
  <cge:PSR_Ref ObjectID="117093590311633463" ObjectName="版纳110kV_顺控测试变\XFMR_＃1主变" Plane="0"/>
 <cge:TPSR_Ref TObjectID="117093590311633463"/></metadata>
<rect fill="white" height="80" opacity="0" stroke="white" transform="rotate(0,1026,719) scale(1,1) translate(-20,-38)" width="76" x="1026" y="719"/></g>
</g>
<g id="ACLine_Layer">
 <g id="31000175">
 <path d="M 181 500 L 349 500" stroke="rgb(0,85,255)" stroke-width="3"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="116530640358212282" ObjectName="ACLN_110kV天生桥电站天江南线" Plane="0"/>
   <cge:PSR_Link Pin0InfoVect0LinkObjId="134000173_0"/>
  </metadata>
<path d="M 181 500 L 349 500" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="ACLineSegment_Layer">
 <g id="36000067">
 <path d="M 1025 207 L 1025 143" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="ST=113997365567815959/LN=116530640358212643" ObjectName="ST=版纳110kV_顺控测试变/LN=ACLN_110kV测试Ⅰ线" Plane="0"/>
  <cge:TPSR_Ref TObjectID="116530640358212643_113997365567815959"/></metadata>
 <path d="M 1025 207 L 1025 143" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="PT_Layer">
 <g id="107000159">
  <use class="kv110kV" height="74" transform="rotate(92,853,323) scale(1,1) translate(-61,-11)" width="76" x="853" xlink:href="#PT:qj_ocs电压互感器带接地下（五卷）_0" y="323"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195191194" ObjectName="版纳110kV_顺控测试变\版纳110kV_顺控测试变/110kV\TERM_顺控测试站110kVⅠ母TV" Plane="0"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(92,853,323) scale(1,1) translate(-61,-11)" width="76" x="853" y="323"/></g>
 <g id="107000170">
  <use class="kv10kV" height="120" transform="rotate(0,852,792) scale(1,1) translate(-57,-103)" width="120" x="852" xlink:href="#PT:药园变电压互感器999_0" y="792"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195191195" ObjectName="版纳110kV_顺控测试变\版纳110kV_顺控测试变/10kV\TERM_10kVⅠ母TV" Plane="0"/>
  </metadata>
 <rect fill="white" height="120" opacity="0" stroke="white" transform="rotate(0,852,792) scale(1,1) translate(-57,-103)" width="120" x="852" y="792"/></g>
</g>
<g id="Arrester_Layer">
 <g id="133000169">
  <use class="kv10kV" height="20" transform="rotate(180,882,826) scale(1,1) translate(-36,-10)" width="40" x="882" xlink:href="#Arrester:bn_避雷器11_0" y="826"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195191196" ObjectName="版纳110kV_顺控测试变\版纳110kV_顺控测试变/10kV\TERM_避雷器" Plane="0"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,882,826) scale(1,1) translate(-36,-10)" width="40" x="882" y="826"/></g>
</g>
<g id="Sensitive_Layer">
 <g id="134000173">
  <use class="kv110kV" height="36" transform="rotate(0,147,498) scale(1,1) translate(-36,-18)" width="72" x="147" xlink:href="#Sensitive:gd_敏感替代图元_0" y="498"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="113997365567815959" ObjectName="版纳110kV_顺控测试变" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,147,498) scale(1,1) translate(-36,-18)" width="72" x="147" y="498"/></g>
 <g id="134000174">
  <use class="kv10kV" height="36" transform="rotate(0,384,497) scale(1,1) translate(-36,-18)" width="72" x="384" xlink:href="#Sensitive:gd_敏感替代图元_0" y="497"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="113997365567815844" ObjectName="版纳_10kV_曼景兰开闭所" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,384,497) scale(1,1) translate(-36,-18)" width="72" x="384" y="497"/></g>
</g>
<g id="Clock_Layer">
 <g id="56000107">
  
 <metadata/></g>
</g>
<g id="Link_Layer">
 <g id="34000028">
 <path d="M 1026 517 L 1026 466" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000022_0" Pin1InfoVect0LinkObjId="30000000_0" Plane="0"/>
  </metadata>
 <path d="M 1026 517 L 1026 466" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000062">
 <path d="M 1073 238 L 1025 238" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111000059_0" Pin1InfoVect0LinkObjId="34000064_0" Pin1InfoVect1LinkObjId="34000119_1" Plane="0"/>
  </metadata>
 <path d="M 1073 238 L 1025 238" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000064">
 <path d="M 1025 238 L 1025 207" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000062_1" Pin0InfoVect1LinkObjId="34000119_1" Pin1InfoVect0LinkObjId="36000067_0" Plane="0"/>
  </metadata>
 <path d="M 1025 238 L 1025 207" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000065">
 <path d="M 1025 427 L 1025 466" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000044_1" Pin1InfoVect0LinkObjId="30000000_0" Plane="0"/>
  </metadata>
 <path d="M 1025 427 L 1025 466" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000112">
 <path d="M 1070 374 L 1025 374" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111000109_0" Pin1InfoVect0LinkObjId="34000114_0" Pin1InfoVect1LinkObjId="34000113_1" Plane="0"/>
  </metadata>
 <path d="M 1070 374 L 1025 374" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000113">
 <path d="M 1025 348 L 1025 374" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000040_1" Pin1InfoVect0LinkObjId="34000114_0" Pin1InfoVect1LinkObjId="34000112_1" Plane="0"/>
  </metadata>
 <path d="M 1025 348 L 1025 374" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000114">
 <path d="M 1025 374 L 1025 404" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000112_1" Pin0InfoVect1LinkObjId="34000113_1" Pin1InfoVect0LinkObjId="101000044_0" Plane="0"/>
  </metadata>
 <path d="M 1025 374 L 1025 404" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000118">
 <path d="M 1025 290 L 1025 316" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000115_1" Pin1InfoVect0LinkObjId="100000040_0" Plane="0"/>
  </metadata>
 <path d="M 1025 290 L 1025 316" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000119">
 <path d="M 1025 258 L 1025 238" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000115_0" Pin1InfoVect0LinkObjId="34000064_0" Pin1InfoVect1LinkObjId="34000062_1" Plane="0"/>
  </metadata>
 <path d="M 1025 258 L 1025 238" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000138">
 <path d="M 1026 570 L 1026 540" stroke="rgb(85,85,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000136_0" Pin1InfoVect0LinkObjId="101000022_1" Plane="0"/>
  </metadata>
 <path d="M 1026 570 L 1026 540" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000141">
 <path d="M 1026 602 L 1026 630" stroke="rgb(85,85,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000136_1" Pin1InfoVect0LinkObjId="101000140_0" Plane="0"/>
  </metadata>
 <path d="M 1026 602 L 1026 630" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000142">
 <path d="M 1026 653 L 1026 684" stroke="rgb(85,85,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000140_1" Pin1InfoVect0LinkObjId="102000095_0" Plane="0"/>
  </metadata>
 <path d="M 1026 653 L 1026 684" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000147">
 <path d="M 1025 784 L 1025 753" stroke="rgb(85,85,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000144_0" Pin1InfoVect0LinkObjId="102000095_1" Plane="0"/>
  </metadata>
 <path d="M 1025 784 L 1025 753" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000148">
 <path d="M 1025 807 L 1025 836" stroke="rgb(85,85,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000144_1" Pin1InfoVect0LinkObjId="100000025_0" Plane="0"/>
  </metadata>
 <path d="M 1025 807 L 1025 836" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000149">
 <path d="M 1025 868 L 1025 893" stroke="rgb(85,85,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000025_1" Pin1InfoVect0LinkObjId="101000146_0" Plane="0"/>
  </metadata>
 <path d="M 1025 868 L 1025 893" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000150">
 <path d="M 1025 916 L 1025 946" stroke="rgb(85,85,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000146_1" Pin1InfoVect0LinkObjId="30000015_0" Plane="0"/>
  </metadata>
 <path d="M 1025 916 L 1025 946" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000154">
 <path d="M 853 466 L 853 422" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="30000000_0" Pin1InfoVect0LinkObjId="101000158_0" Plane="0"/>
  </metadata>
 <path d="M 853 466 L 853 422" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000157">
 <path d="M 853 365 L 853 324" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000155_1" Pin0InfoVect1LinkObjId="34000156_0" Pin1InfoVect0LinkObjId="107000159_0" Plane="0"/>
  </metadata>
 <path d="M 853 365 L 853 324" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000156">
 <path d="M 853 365 L 789 365" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000157_0" Pin0InfoVect1LinkObjId="34000155_1" Pin1InfoVect0LinkObjId="111000160_0" Plane="0"/>
  </metadata>
 <path d="M 853 365 L 789 365" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000155">
 <path d="M 853 392 L 853 365" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000158_1" Pin1InfoVect0LinkObjId="34000157_0" Pin1InfoVect1LinkObjId="34000156_0" Plane="0"/>
  </metadata>
 <path d="M 853 392 L 853 365" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000163">
 <path d="M 852 826 L 882 826" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000166_0" Pin0InfoVect1LinkObjId="34000165_1" Pin1InfoVect0LinkObjId="133000169_0" Plane="0"/>
  </metadata>
 <path d="M 852 826 L 882 826" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000164">
 <path d="M 852 896 L 852 946" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000167_1" Pin1InfoVect0LinkObjId="30000015_0" Plane="0"/>
  </metadata>
 <path d="M 852 896 L 852 946" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000165">
 <path d="M 852 792 L 852 826" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="107000170_0" Pin1InfoVect0LinkObjId="34000166_0" Pin1InfoVect1LinkObjId="34000163_0" Plane="0"/>
  </metadata>
 <path d="M 852 792 L 852 826" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000166">
 <path d="M 852 826 L 852 860" stroke="rgb(0,0,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34000163_0" Pin0InfoVect1LinkObjId="34000165_1" Pin1InfoVect0LinkObjId="101000167_0" Plane="0"/>
  </metadata>
 <path d="M 852 826 L 852 860" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000186">
 <path d="M 1184 807 L 1184 836" stroke="rgb(85,85,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000190_1" Pin1InfoVect0LinkObjId="100000189_0" Plane="0"/>
  </metadata>
 <path d="M 1184 807 L 1184 836" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000187">
 <path d="M 1184 868 L 1184 893" stroke="rgb(85,85,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000189_1" Pin1InfoVect0LinkObjId="101000191_0" Plane="0"/>
  </metadata>
 <path d="M 1184 868 L 1184 893" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34000188">
 <path d="M 1184 916 L 1184 946" stroke="rgb(85,85,255)" stroke-width="1"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000191_1" Pin1InfoVect0LinkObjId="30000015_0" Plane="0"/>
  </metadata>
 <path d="M 1184 916 L 1184 946" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="MeasurementValue_Layer">
 <g id="33000001">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="648" xml:space="preserve" y="431">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115405084048753554" ObjectName="版纳110kV_顺控测试变\版纳110kV_顺控测试变/110kV\BS_110kVI母:V_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33000003">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="648" xml:space="preserve" y="371">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115404869300388754" ObjectName="版纳110kV_顺控测试变\版纳110kV_顺控测试变/110kV\BS_110kVI母:V" Plane="0"/>
  </metadata>
 </g>
 <g id="33000004">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="648" xml:space="preserve" y="391">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115404998149407634" ObjectName="版纳110kV_顺控测试变\版纳110kV_顺控测试变/110kV\BS_110kVI母:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000005">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="648" xml:space="preserve" y="411">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115405041099080594" ObjectName="版纳110kV_顺控测试变\版纳110kV_顺控测试变/110kV\BS_110kVI母:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000006">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="648" xml:space="preserve" y="451">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115404955199734674" ObjectName="版纳110kV_顺控测试变\版纳110kV_顺控测试变/110kV\BS_110kVI母:F" Plane="0"/>
  </metadata>
 </g>
 <g id="33000016">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="670" xml:space="preserve" y="912">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115405084048753555" ObjectName="版纳110kV_顺控测试变\版纳110kV_顺控测试变/10kV\BS_10kI母:V_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33000018">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="670" xml:space="preserve" y="852">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115404869300388755" ObjectName="版纳110kV_顺控测试变\版纳110kV_顺控测试变/10kV\BS_10kI母:V" Plane="0"/>
  </metadata>
 </g>
 <g id="33000019">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="670" xml:space="preserve" y="872">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115404998149407635" ObjectName="版纳110kV_顺控测试变\版纳110kV_顺控测试变/10kV\BS_10kI母:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000020">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="670" xml:space="preserve" y="892">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115405041099080595" ObjectName="版纳110kV_顺控测试变\版纳110kV_顺控测试变/10kV\BS_10kI母:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33000021">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="670" xml:space="preserve" y="932">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115404955199734675" ObjectName="版纳110kV_顺控测试变\版纳110kV_顺控测试变/10kV\BS_10kI母:F" Plane="0"/>
  </metadata>
 </g>
 <g id="33000068">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1005" xml:space="preserve" y="108">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="116812244183943290" ObjectName="ST=版纳110kV_顺控测试变/LN=ACLN_110kV测试Ⅰ线:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000069">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1005" xml:space="preserve" y="128">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="116812287133616250" ObjectName="ST=版纳110kV_顺控测试变/LN=ACLN_110kV测试Ⅰ线:Q" Plane="0"/>
  </metadata>
 </g>
 <g id="33000071">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1005" xml:space="preserve" y="148">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="116812415982635130" ObjectName="ST=版纳110kV_顺控测试变/LN=ACLN_110kV测试Ⅰ线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33000097">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="946" xml:space="preserve" y="669">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709638" ObjectName="版纳110kV_顺控测试变\版纳110kV_顺控测试变/110kV\XF_＃1主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000098">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="946" xml:space="preserve" y="689">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375322986382598" ObjectName="版纳110kV_顺控测试变\版纳110kV_顺控测试变/110kV\XF_＃1主变-高:Q" Plane="0"/>
  </metadata>
 </g>
 <g id="33000099">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="946" xml:space="preserve" y="709">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375451835401478" ObjectName="版纳110kV_顺控测试变\版纳110kV_顺控测试变/110kV\XF_＃1主变-高:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33000100">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="946" xml:space="preserve" y="799">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709639" ObjectName="版纳110kV_顺控测试变\版纳110kV_顺控测试变/10kV\XF_＃1主变-低:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33000101">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="946" xml:space="preserve" y="819">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375322986382599" ObjectName="版纳110kV_顺控测试变\版纳110kV_顺控测试变/10kV\XF_＃1主变-低:Q" Plane="0"/>
  </metadata>
 </g>
 <g id="33000102">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="946" xml:space="preserve" y="839">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375451835401479" ObjectName="版纳110kV_顺控测试变\版纳110kV_顺控测试变/10kV\XF_＃1主变-低:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33000182">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1249" xml:space="preserve" y="699">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795531007" ObjectName="122723089845858047:M" Plane="0"/>
  </metadata>
 </g>
</g>
<g id="Text_Layer">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1302" xml:space="preserve" y="461">110kVI母</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1331" xml:space="preserve" y="941">10kVI母</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1036" xml:space="preserve" y="539">1011</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1040" xml:space="preserve" y="862">001</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1040" xml:space="preserve" y="342">153</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1035" xml:space="preserve" y="426">1531</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1083" xml:space="preserve" y="268">05311</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="40" font-size="40" font-width="40" stroke="rgb(255,255,127)" writing-mode="lr" x="913" xml:space="preserve" y="84">110kV测试Ⅰ线</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="65" font-size="40" font-width="40" stroke="rgb(0,0,0)" writing-mode="lr" x="222" xml:space="preserve" y="125">110kV顺控测试变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1080" xml:space="preserve" y="404">05312</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1035" xml:space="preserve" y="284">1532</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1041" xml:space="preserve" y="596">101</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1036" xml:space="preserve" y="652">1016</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1035" xml:space="preserve" y="806">0016</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1035" xml:space="preserve" y="915">0011</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="785" xml:space="preserve" y="410">1901</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="733" xml:space="preserve" y="259">测试站110kVⅠ母TV</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="742" xml:space="preserve" y="350">19017</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="794" xml:space="preserve" y="887">0901</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="792" xml:space="preserve" y="670">10kVⅠ母TV</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="358" xml:space="preserve" y="510">1212</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="123" xml:space="preserve" y="509">1213</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1199" xml:space="preserve" y="862">001</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1194" xml:space="preserve" y="806">0016</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1194" xml:space="preserve" y="915">0011</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href="监视列表new.sys.svg"><rect fill-opacity="0" height="106" stroke-opacity="0" stroke-width="2" width="440" x="90" y="38"/></g>
</g>
</svg>