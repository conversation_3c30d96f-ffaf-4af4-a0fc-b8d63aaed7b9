<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1372" id="thSvg" viewBox="0 0 2820 1372" width="2820">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="Status:微机保护设备3_0" viewBox="0,0,46,26">
 <circle AFMask="2147483647" Plane="0" cx="36" cy="8" fill="rgb(0,255,0)" r="5" stroke="rgb(0,255,0)" stroke-width="1"/>
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="9" font-size="8" font-width="8" stroke="rgb(0,255,0)" writing-mode="lr" x="29" xml:space="preserve" y="23">投运</text>
</symbol>
<symbol id="Status:微机保护设备3_1" viewBox="0,0,46,26">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="9" font-size="8" font-width="8" stroke="rgb(255,255,254)" writing-mode="lr" x="29" xml:space="preserve" y="22">停运</text>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(255,255,254)" r="5" stroke="rgb(255,255,254)" stroke-width="1"/>
</symbol>
<symbol id="Status:微机保护设备3_2" viewBox="0,0,46,26">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="9" font-size="8" font-width="8" stroke="rgb(255,255,0)" writing-mode="lr" x="29" xml:space="preserve" y="21">调试</text>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Status:微机保护设备3_3" viewBox="0,0,46,26">
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(255,255,0)" r="5" stroke="rgb(255,0,0)" stroke-width="1"/>
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="8" font-size="7" font-width="7" stroke="rgb(255,0,0)" writing-mode="lr" x="29" xml:space="preserve" y="22">检修</text>
</symbol>
<symbol id="Status:微机保护设备3_4" viewBox="0,0,46,26">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="9" font-size="8" font-width="8" stroke="rgb(255,255,0)" writing-mode="lr" x="28" xml:space="preserve" y="22">动作</text>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(255,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Status:微机保护设备3_5" viewBox="0,0,46,26">
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(255,170,255)" stroke-width="1"/>
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="8" font-size="8" font-width="8" stroke="rgb(255,85,255)" writing-mode="lr" x="29" xml:space="preserve" y="20">告警</text>
</symbol>
<symbol id="Status:微机保护设备3_6" viewBox="0,0,46,26">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="10" font-size="8" font-width="8" stroke="rgb(255,255,0)" writing-mode="lr" x="29" xml:space="preserve" y="23">变位</text>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Status:微机保护设备3_7" viewBox="0,0,46,26">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="8" font-size="8" font-width="8" stroke="rgb(255,0,0)" writing-mode="lr" x="29" xml:space="preserve" y="20">中断</text>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(255,0,0)" r="5" stroke="rgb(255,0,0)" stroke-width="1"/>
</symbol>
<symbol id="Status:微机保护设备3_8" viewBox="0,0,46,26">
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(255,0,127)" r="5" stroke="rgb(255,0,127)" stroke-width="1"/>
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="4" font-size="4" font-width="5" stroke="rgb(255,0,127)" writing-mode="lr" x="30" xml:space="preserve" y="17">抑制</text>
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="4" font-size="4" font-width="5" stroke="rgb(255,0,127)" writing-mode="lr" x="30" xml:space="preserve" y="21">告警</text>
</symbol>
<symbol id="Status:微机保护设备3_9" viewBox="0,0,46,26">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="8" font-size="4" font-width="4" stroke="rgb(0,0,255)" writing-mode="lr" x="28" xml:space="preserve" y="22">告警抑制</text>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Status:微机保护设备3_10" viewBox="0,0,46,26">
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="6" font-size="2" font-width="2" stroke="rgb(0,0,255)" writing-mode="lr" x="28" xml:space="preserve" y="19">正在修改定值</text>
</symbol>
<symbol id="Status:微机保护设备3_11" viewBox="0,0,46,26">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="7" font-size="2" font-width="2" stroke="rgb(0,0,255)" writing-mode="lr" x="28" xml:space="preserve" y="20">闭锁修改定值</text>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Status:微机保护设备3_12" viewBox="0,0,46,26">
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="9" font-size="8" font-width="8" stroke="rgb(0,0,255)" writing-mode="lr" x="29" xml:space="preserve" y="21">动作</text>
</symbol>
<symbol id="Status:微机保护设备3_13" viewBox="0,0,46,26">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="9" font-size="8" font-width="8" stroke="rgb(0,0,255)" writing-mode="lr" x="29" xml:space="preserve" y="21">告警</text>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Status:微机保护设备3_14" viewBox="0,0,46,26">
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="9" font-size="8" font-width="8" stroke="rgb(0,0,255)" writing-mode="lr" x="29" xml:space="preserve" y="21">变位</text>
</symbol>
<symbol id="Status:微机保护设备3_15" viewBox="0,0,46,26">
 <text AFMask="2147483647" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="7" font-size="5" font-width="5" stroke="rgb(0,0,255)" writing-mode="lr" x="28" xml:space="preserve" y="21">未确认</text>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="7" fill="rgb(0,255,0)" r="5" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="版纳_110kV_东盟变" InitShowingPlane="0," fill="rgb(0,0,0)" height="1372" width="2820" x="0" y="0"/>
</g>
<g id="Other_Layer">
 
</g>
<g id="Status_Layer">
 <g id="126002182">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="279786126850392072" ObjectName="279786126850392072" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,127,240) scale(2.915,2.915) translate(-106.432,-171.667)" width="46" x="127" y="240"/></g>
 <g id="126002186">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="279786126850392071" ObjectName="279786126850392071" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,127,355) scale(2.915,2.915) translate(-106.432,-247.216)" width="46" x="127" y="355"/></g>
 <g id="126002187">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="279786126850392073" ObjectName="279786126850392073" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,127,470) scale(2.915,2.915) translate(-106.432,-322.765)" width="46" x="127" y="470"/></g>
 <g id="126002188">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="279786126850392074" ObjectName="279786126850392074" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,127,584) scale(2.915,2.915) translate(-106.432,-397.657)" width="46" x="127" y="584"/></g>
 <g id="126002189">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="279786126850392075" ObjectName="279786126850392075" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,127,699) scale(2.915,2.915) translate(-106.432,-473.206)" width="46" x="127" y="699"/></g>
 <g id="126002190">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="279786126850392077" ObjectName="279786126850392077" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,127,814) scale(2.915,2.915) translate(-106.432,-548.755)" width="46" x="127" y="814"/></g>
 <g id="126002191">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="279786126850392078" ObjectName="279786126850392078" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,127,929) scale(2.915,2.915) translate(-106.432,-624.304)" width="46" x="127" y="929"/></g>
 <g id="126002192">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="279786126850392079" ObjectName="279786126850392079" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,127,1043) scale(2.915,2.915) translate(-106.432,-699.195)" width="46" x="127" y="1043"/></g>
 <g id="126002193">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="279786126850392076" ObjectName="279786126850392076" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,127,1158) scale(2.915,2.915) translate(-106.432,-774.744)" width="46" x="127" y="1158"/></g>
 <g id="126002194">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="279786126850392080" ObjectName="279786126850392080" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,127,1273) scale(2.915,2.915) translate(-106.432,-850.293)" width="46" x="127" y="1273"/></g>
 <g id="126002214">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="279786126850392081" ObjectName="279786126850392081" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1489,238) scale(2.915,2.915) translate(-1001.19,-170.353)" width="46" x="1489" y="238"/></g>
 <g id="126002215">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="279786126850392082" ObjectName="279786126850392082" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1489,353) scale(2.915,2.915) translate(-1001.19,-245.902)" width="46" x="1489" y="353"/></g>
 <g id="126002216">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="279786126850392068" ObjectName="279786126850392068" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1489,468) scale(2.915,2.915) translate(-1001.19,-321.451)" width="46" x="1489" y="468"/></g>
 <g id="126002217">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="279786126850392069" ObjectName="279786126850392069" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1489,582) scale(2.915,2.915) translate(-1001.19,-396.343)" width="46" x="1489" y="582"/></g>
 <g id="126002218">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="279786126850392070" ObjectName="279786126850392070" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1489,697) scale(2.915,2.915) translate(-1001.19,-471.892)" width="46" x="1489" y="697"/></g>
 <g id="126002219">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="279786126850392083" ObjectName="279786126850392083" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1489,812) scale(2.915,2.915) translate(-1001.19,-547.441)" width="46" x="1489" y="812"/></g>
 <g id="126002220">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="279786126850392084" ObjectName="279786126850392084" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1489,927) scale(2.915,2.915) translate(-1001.19,-622.99)" width="46" x="1489" y="927"/></g>
 <g id="126002221">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="279786126850392066" ObjectName="279786126850392066" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1489,1041) scale(2.915,2.915) translate(-1001.19,-697.882)" width="46" x="1489" y="1041"/></g>
 <g id="126002222">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="279786126850392067" ObjectName="279786126850392067" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1489,1156) scale(2.915,2.915) translate(-1001.19,-773.43)" width="46" x="1489" y="1156"/></g>
 <g id="126002247">
  
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1489,1275) scale(2.915,2.915) translate(-1001.19,-851.607)" width="46" x="1489" y="1275"/></g>
</g>
<g id="MeasurementValue_Layer">
 <g id="33002224">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="53" font-size="53" font-width="64" stroke="rgb(255,255,254)" writing-mode="lr" x="1130" xml:space="preserve" y="274">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795531543" ObjectName="122723089845858583:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002225">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="52" font-size="52" font-width="63" stroke="rgb(255,255,254)" writing-mode="lr" x="1130" xml:space="preserve" y="387">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795531544" ObjectName="122723089845858584:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002226">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="52" font-size="52" font-width="63" stroke="rgb(255,255,254)" writing-mode="lr" x="1130" xml:space="preserve" y="502">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795531541" ObjectName="122723089845858581:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002227">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="53" font-size="53" font-width="64" stroke="rgb(255,255,254)" writing-mode="lr" x="1130" xml:space="preserve" y="617">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795531542" ObjectName="122723089845858582:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002228">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="53" font-size="53" font-width="64" stroke="rgb(255,255,254)" writing-mode="lr" x="1130" xml:space="preserve" y="731">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795531533" ObjectName="122723089845858573:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002229">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="53" font-size="53" font-width="64" stroke="rgb(255,255,254)" writing-mode="lr" x="1130" xml:space="preserve" y="845">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795531535" ObjectName="122723089845858575:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002230">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="53" font-size="53" font-width="64" stroke="rgb(255,255,254)" writing-mode="lr" x="1130" xml:space="preserve" y="960">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795531536" ObjectName="122723089845858576:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002231">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="53" font-size="53" font-width="64" stroke="rgb(255,255,254)" writing-mode="lr" x="1130" xml:space="preserve" y="1074">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795531537" ObjectName="122723089845858577:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002232">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="52" font-size="52" font-width="63" stroke="rgb(255,255,254)" writing-mode="lr" x="1130" xml:space="preserve" y="1187">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795531534" ObjectName="122723089845858574:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002233">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="52" font-size="52" font-width="63" stroke="rgb(255,255,254)" writing-mode="lr" x="1130" xml:space="preserve" y="1302">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795531538" ObjectName="122723089845858578:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002234">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="53" font-size="53" font-width="64" stroke="rgb(255,255,254)" writing-mode="lr" x="2555" xml:space="preserve" y="273">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795531539" ObjectName="122723089845858579:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002235">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="52" font-size="52" font-width="63" stroke="rgb(255,255,254)" writing-mode="lr" x="2555" xml:space="preserve" y="386">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795531540" ObjectName="122723089845858580:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002236">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="52" font-size="52" font-width="63" stroke="rgb(255,255,254)" writing-mode="lr" x="2555" xml:space="preserve" y="501">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795531507" ObjectName="122723089845858547:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002237">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="53" font-size="53" font-width="64" stroke="rgb(255,255,254)" writing-mode="lr" x="2555" xml:space="preserve" y="616">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795531512" ObjectName="122723089845858552:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002238">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="53" font-size="53" font-width="64" stroke="rgb(255,255,254)" writing-mode="lr" x="2555" xml:space="preserve" y="730">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795531601" ObjectName="122723089845858641:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002239">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="53" font-size="53" font-width="64" stroke="rgb(255,255,254)" writing-mode="lr" x="2555" xml:space="preserve" y="844">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795531527" ObjectName="122723089845858567:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002240">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="53" font-size="53" font-width="64" stroke="rgb(255,255,254)" writing-mode="lr" x="2555" xml:space="preserve" y="959">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795531528" ObjectName="122723089845858568:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002241">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="53" font-size="53" font-width="64" stroke="rgb(255,255,254)" writing-mode="lr" x="2555" xml:space="preserve" y="1073">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795531599" ObjectName="122723089845858639:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002242">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="52" font-size="52" font-width="63" stroke="rgb(255,255,254)" writing-mode="lr" x="2555" xml:space="preserve" y="1186">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795531600" ObjectName="122723089845858640:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002246">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="52" font-size="52" font-width="63" stroke="rgb(255,255,254)" writing-mode="lr" x="2555" xml:space="preserve" y="1305">-000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="122723132795532988" ObjectName="122723089845860028:M" Plane="0"/>
  </metadata>
 </g>
</g>
<g id="Text_Layer">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="56" font-size="34" font-width="34" stroke="rgb(85,255,255)" writing-mode="lr" x="1120" xml:space="preserve" y="105">110kV东盟变继电保护远方操作定值区切换</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="50" font-size="50" font-width="56" stroke="rgb(255,255,254)" writing-mode="lr" x="206" xml:space="preserve" y="260">10kV＃2站用变092保护测控装置</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="50" font-size="50" font-width="56" stroke="rgb(255,255,254)" writing-mode="lr" x="206" xml:space="preserve" y="375">10kV＃3接地变093保护测控装置</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="50" font-size="50" font-width="56" stroke="rgb(255,255,254)" writing-mode="lr" x="206" xml:space="preserve" y="489">10kV＃5电容器089保护测控装置</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="49" font-size="49" font-width="55" stroke="rgb(255,255,254)" writing-mode="lr" x="206" xml:space="preserve" y="603">10kV＃6电容器091保护测控装置</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="49" font-size="49" font-width="55" stroke="rgb(255,255,254)" writing-mode="lr" x="206" xml:space="preserve" y="717">10kV备用一线081保护测控装置</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="49" font-size="49" font-width="55" stroke="rgb(255,255,254)" writing-mode="lr" x="206" xml:space="preserve" y="832">10kV东方红线083保护测控装置</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="49" font-size="49" font-width="55" stroke="rgb(255,255,254)" writing-mode="lr" x="206" xml:space="preserve" y="947">10kV盟尚联络线084保护测控装置</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="49" font-size="49" font-width="55" stroke="rgb(255,255,254)" writing-mode="lr" x="206" xml:space="preserve" y="1061">10kV磨憨II回线085保护测控装置</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="50" font-size="50" font-width="50" stroke="rgb(255,255,254)" writing-mode="lr" x="206" xml:space="preserve" y="1177">10kV磨憨车站II回线082保护测控装置</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="49" font-size="49" font-width="55" stroke="rgb(255,255,254)" writing-mode="lr" x="206" xml:space="preserve" y="1290">10kV磨憨线086保护测控装置</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="50" font-size="50" font-width="56" stroke="rgb(255,255,254)" writing-mode="lr" x="1569" xml:space="preserve" y="258">10kV磨憨站前I回线087保护测控装置</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="50" font-size="50" font-width="56" stroke="rgb(255,255,254)" writing-mode="lr" x="1569" xml:space="preserve" y="373">10kV磨憨站前II回线088保护测控装置</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="50" font-size="50" font-width="56" stroke="rgb(255,255,254)" writing-mode="lr" x="1569" xml:space="preserve" y="487">110kV东盟-那磨线173保护装置</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="49" font-size="49" font-width="55" stroke="rgb(255,255,254)" writing-mode="lr" x="1569" xml:space="preserve" y="601">110kV腊东线174保护装置</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="49" font-size="49" font-width="55" stroke="rgb(255,255,254)" writing-mode="lr" x="1569" xml:space="preserve" y="715">110kV母线保护装置</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="49" font-size="49" font-width="55" stroke="rgb(255,255,254)" writing-mode="lr" x="1569" xml:space="preserve" y="830">35kV腊东龙线361保护测控装置</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="49" font-size="49" font-width="55" stroke="rgb(255,255,254)" writing-mode="lr" x="1569" xml:space="preserve" y="945">35kV盟憨线362保护测控装置</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="49" font-size="49" font-width="55" stroke="rgb(255,255,254)" writing-mode="lr" x="1569" xml:space="preserve" y="1059">＃3主变第I套保护装置</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="49" font-size="49" font-width="55" stroke="rgb(255,255,254)" writing-mode="lr" x="1569" xml:space="preserve" y="1174">＃3主变第II套保护装置</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="50" font-size="50" font-width="56" stroke="rgb(255,255,254)" writing-mode="lr" x="1082" xml:space="preserve" y="191">当前定值区</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="50" font-size="50" font-width="56" stroke="rgb(255,255,254)" writing-mode="lr" x="2473" xml:space="preserve" y="182">当前定值区</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="49" font-size="49" font-width="55" stroke="rgb(255,255,254)" writing-mode="lr" x="1569" xml:space="preserve" y="1293">110kV茶东线171保测定值运行区号</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href="版纳_110kV_东盟变.fac.svg"><rect fill-opacity="0" height="68" stroke-opacity="0" stroke-width="1" width="646" x="1114" y="44"/></g>
</g>
</svg>