<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1700" id="thSvg" viewBox="0 0 3800 1700" width="3800">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="Status:bn_不变化颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(0,0,255)" height="1" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_不变化颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(85,0,255)" height="1" stroke="rgb(85,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_越限颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_越限颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:正常状态显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,127)" height="1" stroke="rgb(255,255,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:正常状态显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(85,0,255)" height="1" stroke="rgb(85,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="版纳_主变高压侧绝对值" InitShowingPlane="0," fill="rgb(0,0,0)" height="1700" width="3800" x="0" y="0"/>
</g>
<g id="Other_Layer">
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1016" x2="1016" y1="130" y2="1486"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1242" x2="2433" y1="820" y2="820"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1243" x2="2433" y1="1150" y2="1150"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1017" x2="2666" y1="614" y2="614"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1019" x2="2666" y1="712" y2="712"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1242" x2="2433" y1="915" y2="915"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1244" x2="2433" y1="1011" y2="1011"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1018" x2="2668" y1="1058" y2="1058"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1243" x2="2433" y1="1237" y2="1237"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1243" x2="2433" y1="1435" y2="1435"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1018" x2="2665" y1="511" y2="511"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1244" x2="2433" y1="1336" y2="1336"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1017" x2="2667" y1="131" y2="131"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1242" x2="1242" y1="131" y2="1486"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1018" x2="2667" y1="200" y2="200"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1018" x2="2667" y1="240" y2="240"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1017" x2="2665" y1="1192" y2="1192"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1825" x2="1825" y1="201" y2="1485"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="2004" x2="2004" y1="129" y2="1486"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="2434" x2="2434" y1="131" y2="1485"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="2667" x2="2667" y1="134" y2="1486"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1244" x2="2433" y1="286" y2="286"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1018" x2="2667" y1="330" y2="330"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1241" x2="2433" y1="375" y2="375"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1018" x2="2667" y1="421" y2="421"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1241" x2="2435" y1="465" y2="465"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1242" x2="2435" y1="560" y2="560"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1242" x2="2433" y1="667" y2="667"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1019" x2="2667" y1="870" y2="870"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1015" x2="2665" y1="965" y2="965"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1015" x2="2667" y1="1107" y2="1107"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1017" x2="2667" y1="1387" y2="1387"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1017" x2="2667" y1="1486" y2="1486"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1522" x2="1522" y1="371" y2="371"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1242" x2="2435" y1="767" y2="767"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1018" x2="2665" y1="1284" y2="1284"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1438" x2="1438" y1="202" y2="1485"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1631" x2="1631" y1="201" y2="1485"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="2235" x2="2235" y1="200" y2="1483"/>
 <rect AFMask="39039" Plane="0" fill="none" height="87" stroke="rgb(85,255,255)" stroke-width="1" transform="rotate(0,1878,58)" width="496" x="1630" y="15"/>
</g>
<g id="Status_Layer">
 <g id="126008875">
  <use class="kv-1" height="40" transform="rotate(0,1409,71) scale(1,1) translate(-30,-20)" width="60" x="1409" xlink:href="#Status:bn_越限颜色显示_0" y="71"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1409,71) scale(1,1) translate(-30,-20)" width="60" x="1409" y="71"/></g>
 <g id="126008876">
  <use class="kv-1" height="40" transform="rotate(0,1089,71) scale(1,1) translate(-30,-20)" width="60" x="1089" xlink:href="#Status:bn_不变化颜色显示_0" y="71"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1089,71) scale(1,1) translate(-30,-20)" width="60" x="1089" y="71"/></g>
 <g id="126008877">
  <use class="kv-1" height="40" transform="rotate(0,2232,72) scale(1,1) translate(-30,-20)" width="60" x="2232" xlink:href="#Status:正常状态显示_0" y="72"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,2232,72) scale(1,1) translate(-30,-20)" width="60" x="2232" y="72"/></g>
</g>
<g id="MeasurementValue_Layer">
 <g id="33008781">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2069" xml:space="preserve" y="280">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708374" ObjectName="版纳_220kV_老景洪变\版纳_220kV_老景洪变/220kV\XF_1号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33008785">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2069" xml:space="preserve" y="325">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708367" ObjectName="版纳_220kV_老景洪变\版纳_220kV_老景洪变/220kV\XF_2号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33008786">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2069" xml:space="preserve" y="370">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708463" ObjectName="版纳_220kV_黎明变\版纳_220kV_黎明变-220kV\XF_#1主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33008787">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2069" xml:space="preserve" y="415">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708466" ObjectName="版纳_220kV_黎明变\版纳_220kV_黎明变-220kV\XF_#2主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33008788">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2069" xml:space="preserve" y="462">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709125" ObjectName="版纳_110kV_江北变\版纳_110kV_江北变/110kV\XF_1号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33008789">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2069" xml:space="preserve" y="505">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709128" ObjectName="版纳_110kV_江北变\版纳_110kV_江北变/110kV\XF_2号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33008790">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2069" xml:space="preserve" y="550">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="116812244183942565" ObjectName="ST=版纳_110kV_老城南变/LN=ACLN_110kV景城I回线:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33008791">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2069" xml:space="preserve" y="603">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708409" ObjectName="版纳_110kV_老城南变\版纳_110kV_老城南变/110kV\XF_#2主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33008792">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2069" xml:space="preserve" y="656">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708565" ObjectName="版纳_110kV_曼弄枫变\版纳_110kV_曼弄枫变-110kV\XF_1号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33008793">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2069" xml:space="preserve" y="710">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708567" ObjectName="版纳_110kV_曼弄枫变\版纳_110kV_曼弄枫变-110kV\XF_2号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33008794">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2069" xml:space="preserve" y="760">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708495" ObjectName="版纳_110kV_嘎栋变\版纳_110kV_嘎栋变-110kV\XF_#1主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33008795">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2069" xml:space="preserve" y="810">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708498" ObjectName="版纳_110kV_嘎栋变\版纳_110kV_嘎栋变-110kV\XF_#2主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33008796">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2069" xml:space="preserve" y="907">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708434" ObjectName="版纳_110kV_勐罕变\版纳_110kV_勐罕变-110kV\XF_#1主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33008797">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2069" xml:space="preserve" y="959">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708431" ObjectName="版纳_110kV_勐罕变\版纳_110kV_勐罕变-110kV\XF_#2主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33008798">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2069" xml:space="preserve" y="1007">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708548" ObjectName="版纳_110kV_大渡岗变\版纳_110kV_大渡岗变-110kV\XF_1号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33008799">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2069" xml:space="preserve" y="1053">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708551" ObjectName="版纳_110kV_大渡岗变\版纳_110kV_大渡岗变-110kV\XF_2号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33008800">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2069" xml:space="preserve" y="1097">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708560" ObjectName="版纳_110kV_勐龙变\版纳_110kV_勐龙变-10kV\XF_1号站用变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33008801">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2069" xml:space="preserve" y="1144">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708516" ObjectName="版纳_110kV_老勐腊变\版纳_110kV_老勐腊变/110kV\XF_1号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33008802">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2069" xml:space="preserve" y="1192">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708518" ObjectName="版纳_110kV_老勐腊变\版纳_110kV_老勐腊变/110kV\XF_2号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33008803">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2069" xml:space="preserve" y="1234">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709077" ObjectName="版纳_110kV_金象变\版纳_110kV_金象变110kV\XF_1号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33008804">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2069" xml:space="preserve" y="1278">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036709080" ObjectName="版纳_110kV_金象变\版纳_110kV_金象变110kV\XF_2号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33008805">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2069" xml:space="preserve" y="1324">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708542" ObjectName="版纳_110kV_勐海变\版纳_110kV_勐海变-110kV\XF_1号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33008806">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2069" xml:space="preserve" y="1377">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708545" ObjectName="版纳_110kV_勐海变\版纳_110kV_勐海变-110kV\XF_2号主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33008807">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2069" xml:space="preserve" y="1424">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708391" ObjectName="版纳_110kV_佛海变\版纳_110kV_佛海变-110kV\XF_#1主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33008808">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2069" xml:space="preserve" y="1473">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708394" ObjectName="版纳_110kV_佛海变\版纳_110kV_佛海变-110kV\XF_#2主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33008828">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2511" xml:space="preserve" y="304">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387120" ObjectName="121878664915714160:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33008830">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2511" xml:space="preserve" y="394">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387121" ObjectName="121878664915714161:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33008832">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2511" xml:space="preserve" y="484">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387142" ObjectName="121878664915714182:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33008834">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2511" xml:space="preserve" y="574">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387107" ObjectName="121878664915714147:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33008836">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2511" xml:space="preserve" y="680">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387110" ObjectName="121878664915714150:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33008838">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2511" xml:space="preserve" y="818">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387108" ObjectName="121878664915714148:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33008840">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2511" xml:space="preserve" y="931">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387116" ObjectName="121878664915714156:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33008842">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2511" xml:space="preserve" y="1031">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387118" ObjectName="121878664915714158:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33008844">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2510" xml:space="preserve" y="1098">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387114" ObjectName="121878664915714154:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33008845">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2511" xml:space="preserve" y="1170">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387112" ObjectName="121878664915714152:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33008847">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2511" xml:space="preserve" y="1262">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387119" ObjectName="121878664915714159:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33008849">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2511" xml:space="preserve" y="1349">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387111" ObjectName="121878664915714151:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33008851">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2511" xml:space="preserve" y="1448">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="121878707865387117" ObjectName="121878664915714157:VALUE" Plane="0"/>
  </metadata>
 </g>
 <g id="33008884">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,0)" writing-mode="lr" x="2069" xml:space="preserve" y="858">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708501" ObjectName="版纳_110kV_嘎栋变\版纳_110kV_嘎栋变-110kV\XF_#3主变-高:P" Plane="0"/>
  </metadata>
 </g>
</g>
<g id="Text_Layer">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="42" font-size="42" font-width="48" stroke="rgb(0,255,255)" writing-mode="lr" x="1633" xml:space="preserve" y="75">高压侧有功绝对值相加</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="51" font-size="25" font-width="25" stroke="rgb(255,0,0)" writing-mode="lr" x="1569" xml:space="preserve" y="188">设备及参数</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="48" font-size="20" font-width="20" stroke="rgb(255,0,0)" writing-mode="lr" x="1099" xml:space="preserve" y="185">项目</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="34" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="1082" xml:space="preserve" y="239">厂站名称</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="48" font-size="20" font-width="20" stroke="rgb(255,0,0)" writing-mode="lr" x="2167" xml:space="preserve" y="185">负荷监控</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="48" font-size="20" font-width="20" stroke="rgb(255,0,0)" writing-mode="lr" x="2449" xml:space="preserve" y="185">高压侧有功绝对值相加</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="36" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="1303" xml:space="preserve" y="240">主变编号</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1328" xml:space="preserve" y="280">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="36" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="1474" xml:space="preserve" y="239">主变容量（MVA）</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="36" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="1658" xml:space="preserve" y="238">负载上限（MW）</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="36" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="1860" xml:space="preserve" y="242">油温上限（C）</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1045" xml:space="preserve" y="303">220kV景洪变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="36" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="2066" xml:space="preserve" y="241">实时负荷（MW）</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="36" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="2275" xml:space="preserve" y="238">告警负荷（MW）</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1328" xml:space="preserve" y="325">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1506" xml:space="preserve" y="325">120</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1506" xml:space="preserve" y="280">120</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1709" xml:space="preserve" y="280">96</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1709" xml:space="preserve" y="325">96</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1893" xml:space="preserve" y="280">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1893" xml:space="preserve" y="323">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="36" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="2524" xml:space="preserve" y="241">绝对值</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2302" xml:space="preserve" y="275">96</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2302" xml:space="preserve" y="320">96</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1045" xml:space="preserve" y="392">220kV黎明变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1328" xml:space="preserve" y="370">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1328" xml:space="preserve" y="415">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1506" xml:space="preserve" y="369">180</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1506" xml:space="preserve" y="415">180</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1709" xml:space="preserve" y="370">144</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1709" xml:space="preserve" y="415">144</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1893" xml:space="preserve" y="370">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1893" xml:space="preserve" y="415">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2302" xml:space="preserve" y="365">144</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2302" xml:space="preserve" y="410">144</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1045" xml:space="preserve" y="482">110kV江北变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1328" xml:space="preserve" y="460">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1328" xml:space="preserve" y="505">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1506" xml:space="preserve" y="460">40</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1506" xml:space="preserve" y="505">31.5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1709" xml:space="preserve" y="460">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1709" xml:space="preserve" y="504">25.2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1893" xml:space="preserve" y="460">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1893" xml:space="preserve" y="505">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2302" xml:space="preserve" y="460">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2302" xml:space="preserve" y="504">25.2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1045" xml:space="preserve" y="571">110kV城南变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1328" xml:space="preserve" y="556">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1328" xml:space="preserve" y="602">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1506" xml:space="preserve" y="553">31.5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1506" xml:space="preserve" y="602">40</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1709" xml:space="preserve" y="553">25.2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1709" xml:space="preserve" y="602">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1893" xml:space="preserve" y="552">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1893" xml:space="preserve" y="602">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2302" xml:space="preserve" y="551">25.2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2302" xml:space="preserve" y="602">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1045" xml:space="preserve" y="681">110kV曼弄枫变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1328" xml:space="preserve" y="657">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1328" xml:space="preserve" y="705">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1506" xml:space="preserve" y="655">40</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1506" xml:space="preserve" y="705">40</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1709" xml:space="preserve" y="657">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1709" xml:space="preserve" y="705">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1893" xml:space="preserve" y="658">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1893" xml:space="preserve" y="705">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2302" xml:space="preserve" y="653">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2302" xml:space="preserve" y="705">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1045" xml:space="preserve" y="808">110kV嘎栋变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1328" xml:space="preserve" y="761">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1328" xml:space="preserve" y="810">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1506" xml:space="preserve" y="760">40</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1506" xml:space="preserve" y="810">40</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1709" xml:space="preserve" y="760">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1709" xml:space="preserve" y="810">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1893" xml:space="preserve" y="760">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1893" xml:space="preserve" y="810">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2302" xml:space="preserve" y="760">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2302" xml:space="preserve" y="809">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1045" xml:space="preserve" y="931">110kV勐罕变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1506" xml:space="preserve" y="910">31.5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1506" xml:space="preserve" y="957">40</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1331" xml:space="preserve" y="907">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1331" xml:space="preserve" y="957">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1712" xml:space="preserve" y="910">25.2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1712" xml:space="preserve" y="957">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1896" xml:space="preserve" y="907">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1896" xml:space="preserve" y="957">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2305" xml:space="preserve" y="907">25.2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2305" xml:space="preserve" y="957">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="26" font-width="26" stroke="rgb(255,255,254)" writing-mode="lr" x="1045" xml:space="preserve" y="1028">110kV大渡岗变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1331" xml:space="preserve" y="1006">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1331" xml:space="preserve" y="1052">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1506" xml:space="preserve" y="1004">20</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1506" xml:space="preserve" y="1052">20</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1712" xml:space="preserve" y="1006">16</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1712" xml:space="preserve" y="1052">16</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1896" xml:space="preserve" y="1008">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1896" xml:space="preserve" y="1052">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2305" xml:space="preserve" y="1001">16</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2305" xml:space="preserve" y="1052">16</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1045" xml:space="preserve" y="1098">110kV勐龙变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1331" xml:space="preserve" y="1099">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1506" xml:space="preserve" y="1099">40</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1712" xml:space="preserve" y="1094">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1896" xml:space="preserve" y="1099">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2305" xml:space="preserve" y="1094">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1045" xml:space="preserve" y="1165">110kV勐腊变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1331" xml:space="preserve" y="1144">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1506" xml:space="preserve" y="1144">31.5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1712" xml:space="preserve" y="1143">25.2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1896" xml:space="preserve" y="1145">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2305" xml:space="preserve" y="1141">25.2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1331" xml:space="preserve" y="1191">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1506" xml:space="preserve" y="1191">31.5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1712" xml:space="preserve" y="1190">25.2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1896" xml:space="preserve" y="1191">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2305" xml:space="preserve" y="1190">25.2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1045" xml:space="preserve" y="1255">110kV金象变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1045" xml:space="preserve" y="1342">110kV勐海变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="27" font-width="27" stroke="rgb(255,255,254)" writing-mode="lr" x="1045" xml:space="preserve" y="1449">110kV佛海变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1331" xml:space="preserve" y="1233">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1331" xml:space="preserve" y="1280">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1506" xml:space="preserve" y="1232">40</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1506" xml:space="preserve" y="1279">40</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1712" xml:space="preserve" y="1233">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1712" xml:space="preserve" y="1278">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1896" xml:space="preserve" y="1235">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1896" xml:space="preserve" y="1280">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2305" xml:space="preserve" y="1232">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2305" xml:space="preserve" y="1277">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1331" xml:space="preserve" y="1324">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1331" xml:space="preserve" y="1374">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1506" xml:space="preserve" y="1324">31.5</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1506" xml:space="preserve" y="1373">40</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1712" xml:space="preserve" y="1324">25.2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1712" xml:space="preserve" y="1373">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1896" xml:space="preserve" y="1324">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1896" xml:space="preserve" y="1373">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2305" xml:space="preserve" y="1324">25.2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2305" xml:space="preserve" y="1373">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1331" xml:space="preserve" y="1426">1</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1331" xml:space="preserve" y="1472">2</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1506" xml:space="preserve" y="1424">40</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1506" xml:space="preserve" y="1473">40</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1712" xml:space="preserve" y="1426">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1712" xml:space="preserve" y="1472">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1896" xml:space="preserve" y="1427">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1896" xml:space="preserve" y="1472">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2305" xml:space="preserve" y="1423">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2305" xml:space="preserve" y="1472">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="40" font-size="40" font-width="44" stroke="rgb(255,255,254)" writing-mode="lr" x="1146" xml:space="preserve" y="86">不变化</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="40" font-size="40" font-width="44" stroke="rgb(255,255,254)" writing-mode="lr" x="1457" xml:space="preserve" y="86">越上限</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="40" font-size="40" font-width="44" stroke="rgb(255,255,254)" writing-mode="lr" x="2278" xml:space="preserve" y="89">实时态</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1331" xml:space="preserve" y="858">3</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1506" xml:space="preserve" y="858">40</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1712" xml:space="preserve" y="858">32</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="1896" xml:space="preserve" y="858">70</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="28" font-size="28" font-width="28" stroke="rgb(255,255,254)" writing-mode="lr" x="2305" xml:space="preserve" y="857">32</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href=""><rect fill-opacity="0" height="79" stroke-opacity="0" stroke-width="2" width="490" x="1633" y="19"/></g>
</g>
</svg>