<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1500" id="thSvg" viewBox="0 0 3150 1500" width="3150">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="Status:bn_不变化颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(0,0,255)" height="1" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_不变化颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(85,0,255)" height="1" stroke="rgb(85,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_非实测颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,85,0)" height="1" stroke="rgb(170,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_非实测颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,85,0)" height="1" stroke="rgb(170,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_工况退出颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,170,255)" height="1" stroke="rgb(170,170,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_工况退出颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,170,255)" height="1" stroke="rgb(170,170,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_数据封锁颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,85,0)" height="1" stroke="rgb(255,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_数据封锁颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,85,0)" height="1" stroke="rgb(255,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_越限颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_越限颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="GZP:gg_光子牌1_0" viewBox="0,0,36,36">
 <circle AFMask="2147483647" Plane="0" cx="18" cy="18" fill="rgb(0,255,0)" r="15" stroke="rgb(0,255,0)" stroke-width="1"/>
</symbol>
<symbol id="GZP:gg_光子牌1_1" viewBox="0,0,36,36">
 <circle AFMask="2147483647" Plane="0" cx="18" cy="18" fill="rgb(255,0,0)" r="15" stroke="rgb(255,0,0)" stroke-width="1"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="版纳_110kV_测试变" InitShowingPlane="0," fill="rgb(0,0,0)" height="1500" width="3150" x="0" y="0"/>
</g>
<g id="Other_Layer">
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="418" x2="418" y1="30" y2="1465"/>
 
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="10" x2="418" y1="162" y2="162"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="9" x2="9" y1="29" y2="1467"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="108" x2="108" y1="424" y2="667"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="195" x2="195" y1="670" y2="831"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="108" x2="108" y1="914" y2="1236"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="10" x2="418" y1="1466" y2="1466"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="10" x2="418" y1="423" y2="423"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="108" x2="418" y1="505" y2="505"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="108" x2="418" y1="586" y2="586"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="9" x2="418" y1="668" y2="668"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="9" x2="418" y1="749" y2="749"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="10" x2="418" y1="831" y2="831"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="9" x2="418" y1="912" y2="912"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="109" x2="418" y1="994" y2="994"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="9" x2="418" y1="1075" y2="1075"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="9" x2="418" y1="1311" y2="1311"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="9" x2="418" y1="1371" y2="1371"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="10" x2="418" y1="29" y2="29"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="11" x2="418" y1="266" y2="266"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="344" x2="344" y1="270" y2="421"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="261" x2="261" y1="270" y2="421"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="175" x2="175" y1="270" y2="421"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="91" x2="91" y1="270" y2="421"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="11" x2="418" y1="343" y2="343"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="109" x2="418" y1="1159" y2="1159"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="9" x2="418" y1="1240" y2="1240"/>
 
</g>
<g id="GZP_Layer">
 <g id="135002297">
  <use class="kv-1" height="36" transform="rotate(0,282,872) scale(0.8,0.8) translate(52.5,200)" width="36" x="282" xlink:href="#GZP:gg_光子牌1_0" y="872"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,282,872) scale(0.8,0.8) translate(52.5,200)" width="36" x="282" y="872"/></g>
</g>
<g id="Status_Layer">
 <g id="126002343">
  <use class="kv-1" height="40" transform="rotate(0,49,307) scale(0.7,0.7) translate(-9,111.571)" width="60" x="49" xlink:href="#Status:bn_工况退出颜色显示_0" y="307"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,49,307) scale(0.7,0.7) translate(-9,111.571)" width="60" x="49" y="307"/></g>
 <g id="126002344">
  <use class="kv-1" height="40" transform="rotate(0,133,307) scale(0.7,0.7) translate(27,111.571)" width="60" x="133" xlink:href="#Status:bn_不变化颜色显示_0" y="307"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,133,307) scale(0.7,0.7) translate(27,111.571)" width="60" x="133" y="307"/></g>
 <g id="126002345">
  <use class="kv-1" height="40" transform="rotate(0,219,307) scale(0.7,0.7) translate(63.8571,111.571)" width="60" x="219" xlink:href="#Status:bn_越限颜色显示_0" y="307"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,219,307) scale(0.7,0.7) translate(63.8571,111.571)" width="60" x="219" y="307"/></g>
 <g id="126002346">
  <use class="kv-1" height="40" transform="rotate(0,302,307) scale(0.7,0.7) translate(99.4286,111.571)" width="60" x="302" xlink:href="#Status:bn_非实测颜色显示_0" y="307"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,302,307) scale(0.7,0.7) translate(99.4286,111.571)" width="60" x="302" y="307"/></g>
 <g id="126002347">
  <use class="kv-1" height="40" transform="rotate(0,382,307) scale(0.7,0.7) translate(133.714,111.571)" width="60" x="382" xlink:href="#Status:bn_数据封锁颜色显示_0" y="307"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,382,307) scale(0.7,0.7) translate(133.714,111.571)" width="60" x="382" y="307"/></g>
</g>
<g id="Clock_Layer">
 <g id="56002296">
  
 <metadata/></g>
</g>
<g id="MeasurementValue_Layer">
 <g id="33002220">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="279" xml:space="preserve" y="1049">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002221">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="279" xml:space="preserve" y="968">-00000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002223">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="279" xml:space="preserve" y="1289">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002228">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="279" xml:space="preserve" y="1356">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002292">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="264" xml:space="preserve" y="809">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002291">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="264" xml:space="preserve" y="722">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002489">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="279" xml:space="preserve" y="1214">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002490">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="27" font-size="27" font-width="27" stroke="rgb(255,255,0)" writing-mode="lr" x="279" xml:space="preserve" y="1133">-00000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
</g>
<g id="Text_Layer">
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="27" font-size="27" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="112" xml:space="preserve" y="884">事故总:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="66" font-size="44" font-width="44" stroke="rgb(0,0,0)" writing-mode="lr" x="151" xml:space="preserve" y="124">35kV岔河变</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="27" font-size="27" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="59" xml:space="preserve" y="721">总有功</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="29" font-size="29" font-width="30" stroke="rgb(255,255,254)" writing-mode="lr" x="168" xml:space="preserve" y="970">档位</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="33" font-size="33" font-width="35" stroke="rgb(255,255,254)" writing-mode="tb" x="60" xml:space="preserve" y="919">一</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="29" font-size="29" font-width="30" stroke="rgb(255,255,254)" writing-mode="lr" x="168" xml:space="preserve" y="1051">油温</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="29" font-size="29" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="47" xml:space="preserve" y="1291">控制母线电压</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="29" font-size="29" font-width="31" stroke="rgb(255,255,254)" writing-mode="lr" x="47" xml:space="preserve" y="1357">合闸母线电压</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="33" stroke="rgb(170,0,0)" writing-mode="lr" x="202" xml:space="preserve" y="482">MW</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="33" stroke="rgb(255,255,254)" writing-mode="lr" x="122" xml:space="preserve" y="482">有功:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="33" stroke="rgb(255,255,254)" writing-mode="lr" x="263" xml:space="preserve" y="482">无功:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="33" stroke="rgb(170,0,0)" writing-mode="lr" x="346" xml:space="preserve" y="482">MVr</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="33" stroke="rgb(255,255,254)" writing-mode="lr" x="122" xml:space="preserve" y="564">电流:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="33" stroke="rgb(170,0,0)" writing-mode="lr" x="202" xml:space="preserve" y="564">A</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="33" stroke="rgb(255,255,254)" writing-mode="lr" x="263" xml:space="preserve" y="564">电压:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="33" stroke="rgb(170,0,0)" writing-mode="lr" x="346" xml:space="preserve" y="564">kV</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="33" stroke="rgb(255,255,254)" writing-mode="lr" x="122" xml:space="preserve" y="645">频率:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="33" stroke="rgb(170,0,0)" writing-mode="lr" x="202" xml:space="preserve" y="645">Hz</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="33" stroke="rgb(255,255,254)" writing-mode="lr" x="263" xml:space="preserve" y="645">温度:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="31" font-size="31" font-width="33" stroke="rgb(170,0,0)" writing-mode="lr" x="346" xml:space="preserve" y="645">℃</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="27" font-size="27" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="59" xml:space="preserve" y="805">总无功</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="33" font-size="33" font-width="35" stroke="rgb(255,255,254)" writing-mode="tb" x="60" xml:space="preserve" y="476">单</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="33" font-size="33" font-width="35" stroke="rgb(255,255,254)" writing-mode="tb" x="59" xml:space="preserve" y="580">位</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="33" font-size="33" font-width="35" stroke="rgb(255,255,254)" writing-mode="tb" x="60" xml:space="preserve" y="949">号</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="33" font-size="33" font-width="35" stroke="rgb(255,255,254)" writing-mode="tb" x="60" xml:space="preserve" y="986">主</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="33" font-size="33" font-width="35" stroke="rgb(255,255,254)" writing-mode="tb" x="60" xml:space="preserve" y="1025">变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="22" xml:space="preserve" y="381">工况</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="22" xml:space="preserve" y="410">退出</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="119" xml:space="preserve" y="381">不</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="106" xml:space="preserve" y="412">变化</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="191" xml:space="preserve" y="397">越限</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="288" xml:space="preserve" y="381">非</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="274" xml:space="preserve" y="412">实测</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="356" xml:space="preserve" y="381">数据</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="29" font-size="29" font-width="29" stroke="rgb(255,255,254)" writing-mode="lr" x="356" xml:space="preserve" y="410">封锁</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="29" font-size="29" font-width="30" stroke="rgb(255,255,254)" writing-mode="lr" x="168" xml:space="preserve" y="1135">档位</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="33" font-size="33" font-width="35" stroke="rgb(255,255,254)" writing-mode="tb" x="60" xml:space="preserve" y="1087">二号主变</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="29" font-size="29" font-width="30" stroke="rgb(255,255,254)" writing-mode="lr" x="168" xml:space="preserve" y="1216">油温</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="56" font-size="37" font-width="37" stroke="rgb(255,255,254)" writing-mode="lr" x="46" xml:space="preserve" y="1451">继电保护远方操控</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href="勐腊地区.sys.svg"><rect fill-opacity="0" height="107" stroke-opacity="0" stroke-width="2" width="379" x="26" y="43"/></g>
 <g ChangePicPlane="0," Plane="0" href="35kV岔河变变软压板遥控表.fac.svg"><rect fill-opacity="0" height="86" stroke-opacity="0" stroke-width="1" width="353" x="20" y="1380"/></g>
</g>
</svg>