<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="113997365567815709" height="1500" id="thSvg" viewBox="0 0 3143 1500" width="3143">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="Arrester:bn_避雷器3_0" viewBox="0,0,20,40">
 <use Plane="0" x="10" xlink:href="#terminal" y="4"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="22" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,10,20)" width="10" x="5" y="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="31" y2="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="5" y2="18"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="5" x2="15" y1="35" y2="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="7" x2="13" y1="37" y2="37"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="11" y1="39" y2="39"/>
 <path AFMask="2147483647" Plane="0" d="M 8 17 L 10 23 L 12 17 Z" fill="none" stroke="rgb(93,92,88)" stroke-width="1"/>
</symbol>
<symbol id="Status:bn_不变化颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(0,0,255)" height="1" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_不变化颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(85,0,255)" height="1" stroke="rgb(85,0,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Disconnector:bn_刀闸2_0" viewBox="0,0,32,16">
 <use Plane="0" x="3" xlink:href="#terminal" y="7"/>
 <use Plane="0" x="28" xlink:href="#terminal" y="7"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="25" x2="25" y1="13" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="29" x2="25" y1="8" y2="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="3" y1="8" y2="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="23" y1="8" y2="1"/>
</symbol>
<symbol id="Disconnector:bn_刀闸2_1" viewBox="0,0,32,16">
 <use Plane="0" x="3" xlink:href="#terminal" y="7"/>
 <use Plane="0" x="28" xlink:href="#terminal" y="7"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="25" x2="25" y1="13" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="29" y1="8" y2="8"/>
</symbol>
<symbol id="PT:bn_电压互感器004_0" viewBox="0,0,100,100">
 <use Plane="0" x="33" xlink:href="#terminal" y="97"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="28" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,75,51)" width="12" x="69" y="37"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="21" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,33,67)" width="10" x="28" y="57"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="33" y1="17" y2="24"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="28" y1="24" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="38" y1="24" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="38" y1="40" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="28" y1="40" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="33" y1="33" y2="40"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="49" x2="49" y1="33" y2="40"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="49" x2="44" y1="40" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="49" x2="54" y1="40" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="52" x2="52" y1="17" y2="30"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="52" x2="45" y1="30" y2="27"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="52" x2="45" y1="17" y2="21"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="5" x2="15" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="7" x2="13" y1="10" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="11" y1="8" y2="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="10" x2="10" y1="40" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="10" x2="22" y1="40" y2="40"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="70" x2="80" y1="32" y2="32"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="72" x2="78" y1="30" y2="30"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="74" x2="76" y1="28" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="75" x2="75" y1="32" y2="37"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="75" x2="75" y1="65" y2="62"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="33" y1="51" y2="97"/>
 <path AFMask="2147483647" Plane="0" d="M 72 61 L 75 44 L 78 61 Z" fill="rgb(0,0,255)" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="33" cy="24" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="33" cy="40" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="49" cy="40" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="49" cy="24" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="0" d="M 75 66 L 75 84 L 33 84 " fill="none" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="PT:bn_电压互感器111_0" viewBox="0,0,100,100">
 <use Plane="0" x="50" xlink:href="#terminal" y="84"/>
 <circle AFMask="2147483647" Plane="0" cx="50" cy="24" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="50" x2="50" y1="17" y2="24"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="50" x2="45" y1="24" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="50" x2="55" y1="24" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="50" x2="55" y1="40" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="50" x2="45" y1="40" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="50" x2="50" y1="33" y2="40"/>
 <circle AFMask="2147483647" Plane="0" cx="50" cy="40" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="66" cy="40" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="66" x2="66" y1="33" y2="40"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="66" x2="61" y1="40" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="66" x2="71" y1="40" y2="44"/>
 <circle AFMask="2147483647" Plane="0" cx="66" cy="24" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="69" x2="69" y1="17" y2="30"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="69" x2="62" y1="30" y2="27"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="69" x2="62" y1="17" y2="21"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="24" x2="30" y1="5" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="25" x2="29" y1="3" y2="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="26" x2="28" y1="1" y2="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="27" x2="27" y1="40" y2="6"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="27" x2="50" y1="40" y2="40"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="50" x2="50" y1="51" y2="83"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="50" x2="28" y1="24" y2="24"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="43" x2="43" y1="58" y2="78"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="43" x2="57" y1="58" y2="58"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="43" x2="56" y1="78" y2="78"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="57" x2="57" y1="58" y2="78"/>
</symbol>
<symbol id="Breaker:bn_断路器2_0" viewBox="0,0,20,40">
 <use Plane="0" x="10" xlink:href="#terminal" y="4"/>
 <use Plane="0" x="10" xlink:href="#terminal" y="36"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="30" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,10,20)" width="14" x="3" y="5"/>
</symbol>
<symbol id="Breaker:bn_断路器2_1" viewBox="0,0,20,40">
 <use Plane="0" x="10" xlink:href="#terminal" y="4"/>
 <use Plane="0" x="10" xlink:href="#terminal" y="36"/>
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,254)" height="30" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,10,20)" width="14" x="3" y="5"/>
</symbol>
<symbol id="Status:bn_非实测颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,85,0)" height="1" stroke="rgb(170,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_非实测颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,85,0)" height="1" stroke="rgb(170,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_工况退出颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,170,255)" height="1" stroke="rgb(170,170,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_工况退出颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(170,170,255)" height="1" stroke="rgb(170,170,255)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸12_0" viewBox="0,0,40,20">
 <use Plane="0" x="4" xlink:href="#terminal" y="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="31" y1="6" y2="18"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="28" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="33" y1="8" y2="16"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="35" x2="35" y1="10" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="28" y1="2" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="4" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="9" y1="5" y2="19"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸12_1" viewBox="0,0,40,20">
 <use Plane="0" x="4" xlink:href="#terminal" y="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="31" y1="6" y2="18"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="3" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="33" y1="8" y2="16"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="35" x2="35" y1="10" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="9" y1="5" y2="19"/>
</symbol>
<symbol id="Transformer2:bn_两卷变压器8_0" viewBox="0,0,50,76">
 <use Plane="0" x="26" xlink:href="#terminal" y="3"/>
 <circle AFMask="2147483647" Plane="0" cx="25" cy="21" fill="none" r="18" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="25" x2="33" y1="21" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="26" x2="18" y1="21" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="25" x2="25" y1="21" y2="31"/>
</symbol>
<symbol id="Transformer2:bn_两卷变压器8_1" viewBox="0,0,50,76">
 <use Plane="1" x="26" xlink:href="#terminal" y="72"/>
 <circle AFMask="2147483647" Plane="1" cx="25" cy="53" fill="none" r="18" stroke="rgb(0,0,255)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 25 47 L 16 60 L 35 60 Z" fill="none" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Fuse:bn_熔断器3_0" viewBox="0,0,32,16">
 <use Plane="0" x="4" xlink:href="#terminal" y="12"/>
 <use Plane="0" x="26" xlink:href="#terminal" y="12"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="5" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(23,16,7)" width="18" x="7" y="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="5" x2="27" y1="3" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="7" x2="7" y1="9" y2="15"/>
</symbol>
<symbol id="Fuse:bn_熔断器7_0" viewBox="0,0,32,16">
 <use Plane="0" x="5" xlink:href="#terminal" y="12"/>
 <use Plane="0" x="28" xlink:href="#terminal" y="12"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="5" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(23,16,7)" width="18" x="7" y="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="5" x2="27" y1="3" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="7" x2="7" y1="9" y2="15"/>
</symbol>
<symbol id="Status:bn_数据封锁颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,85,0)" height="1" stroke="rgb(255,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_数据封锁颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,85,0)" height="1" stroke="rgb(255,85,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_越限颜色显示_0" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Status:bn_越限颜色显示_1" viewBox="0,0,60,40">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,0,0)" height="1" stroke="rgb(255,0,0)" stroke-width="1" transform="rotate(0,2,2)" width="1" x="2" y="2"/>
</symbol>
<symbol id="Transformer2:bn_站用变2_0" viewBox="0,0,60,86">
 <use Plane="0" x="36" xlink:href="#terminal" y="22"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="36" x2="45" y1="40" y2="49"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="36" x2="29" y1="40" y2="47"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="36" x2="36" y1="24" y2="40"/>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="39" fill="none" r="15" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Transformer2:bn_站用变2_1" viewBox="0,0,60,86">
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="28" x2="43" y1="66" y2="66"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="36" x2="43" y1="58" y2="65"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="35" x2="28" y1="58" y2="65"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="10" x2="36" y1="40" y2="40"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="38" x2="38" y1="79" y2="82"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="12" x2="12" y1="29" y2="39"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="5" x2="5" y1="39" y2="42"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="7" x2="7" y1="42" y2="38"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="9" y1="37" y2="43"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="25" x2="18" y1="25" y2="25"/>
 <circle AFMask="2147483647" Plane="1" cx="36" cy="63" fill="none" r="15" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="12" x2="30" y1="40" y2="18"/>
</symbol>
<symbol id="Terminal:bn_终端设备10_0" viewBox="0,0,38,12">
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,255,0)" stroke-width="1" x1="2" x2="7" y1="6" y2="6"/>
 <polygon AFMask="2147483647" Plane="0" fill="none" points="7,3 14,5 7,8" stroke="rgb(0,255,0)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,255,0)" stroke-width="1" x1="12" x2="22" y1="6" y2="6"/>
 <polygon AFMask="2147483647" Plane="0" fill="none" points="21,5 28,3 28,8" stroke="rgb(0,255,0)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,255,0)" stroke-width="1" x1="27" x2="32" y1="6" y2="6"/>
</symbol>
<symbol id="GZP:gg_光子牌1_0" viewBox="0,0,36,36">
 <circle AFMask="2147483647" Plane="0" cx="18" cy="18" fill="rgb(0,255,0)" r="15" stroke="rgb(0,255,0)" stroke-width="1"/>
</symbol>
<symbol id="GZP:gg_光子牌1_1" viewBox="0,0,36,36">
 <circle AFMask="2147483647" Plane="0" cx="18" cy="18" fill="rgb(255,0,0)" r="15" stroke="rgb(255,0,0)" stroke-width="1"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="版纳_35kV_老关坪变" InitShowingPlane="0," fill="rgb(0,0,0)" height="1500" width="3143" x="0" y="0"/>
</g>
<g id="Other_Layer">
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="38" x2="446" y1="26" y2="26"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="447" x2="447" y1="27" y2="1469"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="39" x2="447" y1="1468" y2="1468"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="38" x2="38" y1="27" y2="1469"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="38" x2="447" y1="1233" y2="1233"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="38" x2="447" y1="1152" y2="1152"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="38" x2="447" y1="1070" y2="1070"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="38" x2="447" y1="907" y2="907"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="39" x2="447" y1="826" y2="826"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="38" x2="447" y1="744" y2="744"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="38" x2="447" y1="663" y2="663"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="137" x2="447" y1="581" y2="581"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="137" x2="447" y1="500" y2="500"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="39" x2="447" y1="418" y2="418"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="137" x2="137" y1="909" y2="1069"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="224" x2="224" y1="665" y2="826"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="137" x2="137" y1="419" y2="662"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="39" x2="447" y1="146" y2="146"/>
 
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="40" x2="445" y1="268" y2="268"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="369" x2="369" y1="272" y2="415"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="288" x2="288" y1="272" y2="415"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="203" x2="203" y1="272" y2="415"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="120" x2="120" y1="272" y2="415"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="2" x1="40" x2="445" y1="342" y2="342"/>
</g>
<g id="Bus_Layer">
 <g id="30000089">
  <path d="M 1009 852 L 2565 852" stroke="rgb(128,128,128)" stroke-width="6"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115404740451368984" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/10kV\BS_10kVⅠ母" Plane="0"/>
  <cge:TPSR_Ref TObjectID="115404740451368984"/></metadata>
 <path d="M 1009 852 L 2565 852" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="Breaker_Layer">
 <g id="100000027">
  <use class="kv35kV" height="40" transform="rotate(0,1773,441) scale(1.6,1.6) translate(-674.875,-185.375)" width="20" x="1773" xlink:href="#Breaker:bn_断路器2_0" y="441"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521237055" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/35kV\CB_#1主变高压侧301开关" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319928895"/>
  <cge:TPSR_Ref TObjectID="114560315521237055"/></metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1773,441) scale(1.6,1.6) translate(-674.875,-185.375)" width="20" x="1773" y="441"/></g>
 <g id="100000084">
  <use class="kv10kV" height="40" transform="rotate(0,1773,693) scale(1.6,1.6) translate(-674.875,-279.875)" width="20" x="1773" xlink:href="#Breaker:bn_断路器2_0" y="693"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521237056" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/10kV\CB_#1主变低压侧001开关" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319928896"/>
  <cge:TPSR_Ref TObjectID="114560315521237056"/></metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1773,693) scale(1.6,1.6) translate(-674.875,-279.875)" width="20" x="1773" y="693"/></g>
 <g id="100000101">
  <use class="kv10kV" height="40" transform="rotate(0,1175,1014) scale(1.6,1.6) translate(-450.625,-400.25)" width="20" x="1175" xlink:href="#Breaker:bn_断路器2_0" y="1014"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521237057" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/10kV\CB_10kV备用线一041开关" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319928897"/>
  <cge:TPSR_Ref TObjectID="114560315521237057"/></metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1175,1014) scale(1.6,1.6) translate(-450.625,-400.25)" width="20" x="1175" y="1014"/></g>
 <g id="100002144">
  <use class="kv10kV" height="40" transform="rotate(0,1482,1014) scale(1.6,1.6) translate(-565.75,-400.25)" width="20" x="1482" xlink:href="#Breaker:bn_断路器2_0" y="1014"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521237058" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/10kV\CB_10kV二台坡线042开关" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319928898"/>
  <cge:TPSR_Ref TObjectID="114560315521237058"/></metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1482,1014) scale(1.6,1.6) translate(-565.75,-400.25)" width="20" x="1482" y="1014"/></g>
 <g id="100002176">
  <use class="kv10kV" height="40" transform="rotate(0,1788,1014) scale(1.6,1.6) translate(-680.5,-400.25)" width="20" x="1788" xlink:href="#Breaker:bn_断路器2_0" y="1014"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114560315521237060" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/10kV\CB_10kV备用线二044开关" Plane="0"/>
   <cge:Meas_Ref ObjectID="114560487319928900"/>
  <cge:TPSR_Ref TObjectID="114560315521237060"/></metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1788,1014) scale(1.6,1.6) translate(-680.5,-400.25)" width="20" x="1788" y="1014"/></g>
</g>
<g id="Disconnector_Layer">
 <g id="101000022">
  <use class="kv35kV" height="16" transform="rotate(90,1773,326) scale(1.8,-1.8) translate(-804,-515.111)" width="32" x="1773" xlink:href="#Disconnector:bn_刀闸2_0" y="326"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497947830" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/35kV\SW_#1主变高压侧3016刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346966710"/>
  <cge:TPSR_Ref TObjectID="114841790497947830"/></metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(90,1773,326) scale(1.8,-1.8) translate(-804,-515.111)" width="32" x="1773" y="326"/></g>
 <g id="101001784">
  <use class="kv10kV" height="16" transform="rotate(90,1773,781) scale(1.8,-1.8) translate(-804,-1222.89)" width="32" x="1773" xlink:href="#Disconnector:bn_刀闸2_0" y="781"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497947831" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/10kV\SW_#1主变低压侧0011刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346966711"/>
  <cge:TPSR_Ref TObjectID="114841790497947831"/></metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(90,1773,781) scale(1.8,-1.8) translate(-804,-1222.89)" width="32" x="1773" y="781"/></g>
 <g id="101002340">
  <use class="kv10kV" height="16" transform="rotate(90,1175,928) scale(1.8,-1.8) translate(-538.222,-1451.56)" width="32" x="1175" xlink:href="#Disconnector:bn_刀闸2_0" y="928"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497947833" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/10kV\SW_10kV备用线一0411刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346966713"/>
  <cge:TPSR_Ref TObjectID="114841790497947833"/></metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(90,1175,928) scale(1.8,-1.8) translate(-538.222,-1451.56)" width="32" x="1175" y="928"/></g>
 <g id="101002342">
  <use class="kv10kV" height="16" transform="rotate(90,1482,928) scale(1.8,-1.8) translate(-674.667,-1451.56)" width="32" x="1482" xlink:href="#Disconnector:bn_刀闸2_0" y="928"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497947834" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/10kV\SW_10kV二台坡线0421刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346966714"/>
  <cge:TPSR_Ref TObjectID="114841790497947834"/></metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(90,1482,928) scale(1.8,-1.8) translate(-674.667,-1451.56)" width="32" x="1482" y="928"/></g>
 <g id="101002344">
  <use class="kv10kV" height="16" transform="rotate(90,1788,928) scale(1.8,-1.8) translate(-810.667,-1451.56)" width="32" x="1788" xlink:href="#Disconnector:bn_刀闸2_0" y="928"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497947836" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/10kV\SW_10kV备用线二0441刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346966716"/>
  <cge:TPSR_Ref TObjectID="114841790497947836"/></metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(90,1788,928) scale(1.8,-1.8) translate(-810.667,-1451.56)" width="32" x="1788" y="928"/></g>
 <g id="101002346">
  <use class="kv10kV" height="16" transform="rotate(90,2093,928) scale(1.8,-1.8) translate(-946.222,-1451.56)" width="32" x="2093" xlink:href="#Disconnector:bn_刀闸2_0" y="928"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="114841790497947837" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/10kV\SW_10kVⅠ母TV0901刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="114841919346966717"/>
  <cge:TPSR_Ref TObjectID="114841790497947837"/></metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(90,2093,928) scale(1.8,-1.8) translate(-946.222,-1451.56)" width="32" x="2093" y="928"/></g>
</g>
<g id="GroundDisconnector_Layer">
 <g id="111001778">
  <use class="kv35kV" height="20" transform="rotate(180,1657,384) scale(1.8,-1.8) translate(-740.444,-609.333)" width="40" x="1657" xlink:href="#GroundDisconnector:bn_接地刀闸12_0" y="384"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115123265474658423" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/35kV\GRNDSW_#1主变高压侧30167接地刀闸" Plane="0"/>
   <cge:Meas_Ref ObjectID="115123394323677303"/>
  <cge:TPSR_Ref TObjectID="115123265474658423"/></metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1657,384) scale(1.8,-1.8) translate(-740.444,-609.333)" width="40" x="1657" y="384"/></g>
</g>
<g id="Transformer2_Layer">
<g id="102001655">
 <g id="1020016550">
  <use class="kv35kV" height="76" transform="rotate(0,1773,570) scale(1.5,1.5) translate(-617,-228)" width="50" x="1773" xlink:href="#Transformer2:bn_两卷变压器8_0" y="570"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="117375065288343583" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/35kV\XF_#1主变-高" Plane="0"/>
  </metadata>
 </g>
 <g id="1020016551">
  <use class="kv10kV" height="76" transform="rotate(0,1773,570) scale(1.5,1.5) translate(-617,-228)" width="50" x="1773" xlink:href="#Transformer2:bn_两卷变压器8_1" y="570"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="117375065288343584" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/10kV\XF_#1主变-低" Plane="0"/>
  </metadata>
 </g>
 <metadata>
  <cge:PSR_Ref ObjectID="117093590311632911" ObjectName="版纳_35kV_老关坪变\XFMR_#1主变" Plane="0"/>
 <cge:TPSR_Ref TObjectID="117093590311632911"/></metadata>
<rect fill="white" height="76" opacity="0" stroke="white" transform="rotate(0,1773,570) scale(1.5,1.5) translate(-617,-228)" width="50" x="1773" y="570"/></g>
<g id="102002217">
 <g id="1020022170">
  <use class="kv10kV" height="86" transform="rotate(0,2397,1164) scale(1.5,1.5) translate(-835,-410)" width="60" x="2397" xlink:href="#Transformer2:bn_站用变2_0" y="1164"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="117375065288343585" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/10kV\XF_10kV站用变-高" Plane="0"/>
  </metadata>
 </g>
 <g id="1020022171">
  <use class="kv0" height="86" transform="rotate(0,2397,1164) scale(1.5,1.5) translate(-835,-410)" width="60" x="2397" xlink:href="#Transformer2:bn_站用变2_1" y="1164"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="117375065288343586" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/0.4kV\XF_10kV站用变-低" Plane="0"/>
  </metadata>
 </g>
 <metadata>
  <cge:PSR_Ref ObjectID="117093590311632912" ObjectName="版纳_35kV_老关坪变\XFMR_10kV站用变" Plane="0"/>
 <cge:TPSR_Ref TObjectID="117093590311632912"/></metadata>
<rect fill="white" height="86" opacity="0" stroke="white" transform="rotate(0,2397,1164) scale(1.5,1.5) translate(-835,-410)" width="60" x="2397" y="1164"/></g>
</g>
<g id="Load_Layer">
 <g id="32002006">
 <path d="M 1175 1186 L 1175 1302" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115967690404790301" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/10kV\LD_10kV备用线一" Plane="0"/>
  <cge:TPSR_Ref TObjectID="115967690404790301"/></metadata>
 <path d="M 1175 1186 L 1175 1302" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32002135">
 <path d="M 1482 1186 L 1482 1294" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115967690404790302" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/10kV\LD_10kV二台坡线" Plane="0"/>
  <cge:TPSR_Ref TObjectID="115967690404790302"/></metadata>
 <path d="M 1482 1186 L 1482 1294" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="32002167">
 <path d="M 1788 1190 L 1788 1294" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="115967690404790304" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/10kV\LD_10kV备用线二" Plane="0"/>
  <cge:TPSR_Ref TObjectID="115967690404790304"/></metadata>
 <path d="M 1788 1190 L 1788 1294" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="ACLineSegment_Layer">
 <g id="36002000">
 <path d="M 1773 224 L 1773 144" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="ST=-1/LN=-1" ObjectName="ST=-1/LN=-1" Plane="0"/>
  </metadata>
 <path d="M 1773 224 L 1773 144" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="PT_Layer">
 <g id="107002302">
  <use class="kv10kV" height="100" transform="rotate(180,2093,1159) scale(1.5,1.5) translate(-730.667,-483.333)" width="100" x="2093" xlink:href="#PT:bn_电压互感器004_0" y="1159"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195186267" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/10kV\TERM_10kVI母PT" Plane="0"/>
  </metadata>
 <rect fill="white" height="100" opacity="0" stroke="white" transform="rotate(180,2093,1159) scale(1.5,1.5) translate(-730.667,-483.333)" width="100" x="2093" y="1159"/></g>
 <g id="107002305">
  <use class="kv35kV" height="100" transform="rotate(180,1504,308) scale(1.5,1.5) translate(-551.333,-186.667)" width="100" x="1504" xlink:href="#PT:bn_电压互感器111_0" y="308"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195188019" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/35kV\TERM_茶关线电压互感器" Plane="0"/>
  </metadata>
 <rect fill="white" height="100" opacity="0" stroke="white" transform="rotate(180,1504,308) scale(1.5,1.5) translate(-551.333,-186.667)" width="100" x="1504" y="308"/></g>
</g>
<g id="GZP_Layer">
 <g id="135002469">
  <use class="kv35kV" height="36" transform="rotate(0,325,867) scale(0.8,0.8) translate(63.25,198.75)" width="36" x="325" xlink:href="#GZP:gg_光子牌1_0" y="867"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="122160139892427012" ObjectName="122160139892427012" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,325,867) scale(0.8,0.8) translate(63.25,198.75)" width="36" x="325" y="867"/></g>
</g>
<g id="Arrester_Layer">
 <g id="133000134">
  <use class="kv10kV" height="40" transform="rotate(0,1230,1165) scale(1.8,1.8) translate(-556.667,-522.778)" width="20" x="1230" xlink:href="#Arrester:bn_避雷器3_0" y="1165"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195186265" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/10kV\TERM_湘塘线避雷器" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1230,1165) scale(1.8,1.8) translate(-556.667,-522.778)" width="20" x="1230" y="1165"/></g>
 <g id="133001985">
  <use class="kv35kV" height="40" transform="rotate(270,1816,282) scale(1.5,1.5) translate(-615.333,-99)" width="20" x="1816" xlink:href="#Arrester:bn_避雷器3_0" y="282"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195186264" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/35kV\TERM_避雷器" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(270,1816,282) scale(1.5,1.5) translate(-615.333,-99)" width="20" x="1816" y="282"/></g>
 <g id="133002147">
  <use class="kv10kV" height="40" transform="rotate(0,1537,1165) scale(1.8,1.8) translate(-693.111,-522.778)" width="20" x="1537" xlink:href="#Arrester:bn_避雷器3_0" y="1165"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195186266" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/10kV\TERM_二台坡线避雷器" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1537,1165) scale(1.8,1.8) translate(-693.111,-522.778)" width="20" x="1537" y="1165"/></g>
</g>
<g id="Fuse_Layer">
 <g id="131001992">
  <use class="kv35kV" height="16" transform="rotate(359,1644,266) scale(1.5,1.5) translate(-564,-100.667)" width="32" x="1644" xlink:href="#Fuse:bn_熔断器3_0" y="266"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195186262" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/35kV\TERM_3019跌落开关" Plane="0"/>
  </metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(359,1644,266) scale(1.5,1.5) translate(-564,-100.667)" width="32" x="1644" y="266"/></g>
 <g id="131002215">
  <use class="kv10kV" height="16" transform="rotate(450,2400,942) scale(1.5,-1.5) translate(-817,-1583)" width="32" x="2400" xlink:href="#Fuse:bn_熔断器7_0" y="942"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195186268" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/10kV\TERM_0451跌落开关" Plane="0"/>
  </metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(450,2400,942) scale(1.5,-1.5) translate(-817,-1583)" width="32" x="2400" y="942"/></g>
</g>
<g id="Status_Layer">
 <g id="126002489">
  <use class="kv-1" height="40" transform="rotate(0,79,306) scale(0.75,0.75) translate(-3.66666,82)" width="60" x="79" xlink:href="#Status:bn_工况退出颜色显示_0" y="306"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,79,306) scale(0.75,0.75) translate(-3.66666,82)" width="60" x="79" y="306"/></g>
 <g id="126002490">
  <use class="kv-1" height="40" transform="rotate(0,160,306) scale(0.75,0.75) translate(23.3333,82)" width="60" x="160" xlink:href="#Status:bn_不变化颜色显示_0" y="306"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,160,306) scale(0.75,0.75) translate(23.3333,82)" width="60" x="160" y="306"/></g>
 <g id="126002491">
  <use class="kv-1" height="40" transform="rotate(0,246,306) scale(0.75,0.75) translate(52,82)" width="60" x="246" xlink:href="#Status:bn_越限颜色显示_0" y="306"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,246,306) scale(0.75,0.75) translate(52,82)" width="60" x="246" y="306"/></g>
 <g id="126002492">
  <use class="kv-1" height="40" transform="rotate(0,328,306) scale(0.75,0.75) translate(79.3333,82)" width="60" x="328" xlink:href="#Status:bn_非实测颜色显示_0" y="306"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,328,306) scale(0.75,0.75) translate(79.3333,82)" width="60" x="328" y="306"/></g>
 <g id="126002493">
  <use class="kv-1" height="40" transform="rotate(0,409,306) scale(0.75,0.75) translate(106.333,82)" width="60" x="409" xlink:href="#Status:bn_数据封锁颜色显示_0" y="306"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,409,306) scale(0.75,0.75) translate(106.333,82)" width="60" x="409" y="306"/></g>
</g>
<g id="Clock_Layer">
 <g id="56002468">
  
 <metadata/></g>
</g>
<g id="Term_Layer">
 <g id="112002017">
  <use class="kv-1" height="12" transform="rotate(90,1175,1096) scale(1,1) translate(-17,-6)" width="38" x="1175" xlink:href="#Terminal:bn_终端设备10_0" y="1096"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195187790" ObjectName="版纳_35kV_老关坪变\\TERM_041－电缆" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,1175,1096) scale(1,1) translate(-17,-6)" width="38" x="1175" y="1096"/></g>
 <g id="112002146">
  <use class="kv-1" height="12" transform="rotate(90,1482,1096) scale(1,1) translate(-17,-6)" width="38" x="1482" xlink:href="#Terminal:bn_终端设备10_0" y="1096"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="118500965195187791" ObjectName="版纳_35kV_老关坪变\\TERM_042－电缆" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,1482,1096) scale(1,1) translate(-17,-6)" width="38" x="1482" y="1096"/></g>
</g>
<g id="Link_Layer">
 <g id="34000102">
 <path d="M 1175 951 L 1175 988" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101002340_1" Pin1InfoVect0LinkObjId="100000101_0" Plane="0"/>
  </metadata>
 <path d="M 1175 951 L 1175 988" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001782">
 <path d="M 1773 621 L 1773 667" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="102001655_1" Pin1InfoVect0LinkObjId="100000084_0" Plane="0"/>
  </metadata>
 <path d="M 1773 621 L 1773 667" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001787">
 <path d="M 1773 804 L 1773 852" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101001784_1" Pin1InfoVect0LinkObjId="30000089_0" Plane="0"/>
  </metadata>
 <path d="M 1773 804 L 1773 852" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002011">
 <path d="M 1230 1165 L 1175 1165" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133000134_0" Pin1InfoVect0LinkObjId="34002012_1" Pin1InfoVect1LinkObjId="34002013_0" Plane="0"/>
  </metadata>
 <path d="M 1230 1165 L 1175 1165" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002012">
 <path d="M 1175 1040 L 1175 1165" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000101_1" Pin1InfoVect0LinkObjId="34002011_1" Pin1InfoVect1LinkObjId="34002013_0" Plane="0"/>
  </metadata>
 <path d="M 1175 1040 L 1175 1165" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002013">
 <path d="M 1175 1165 L 1175 1186" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002011_1" Pin0InfoVect1LinkObjId="34002012_1" Pin1InfoVect0LinkObjId="32002006_0" Plane="0"/>
  </metadata>
 <path d="M 1175 1165 L 1175 1186" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002140">
 <path d="M 1482 951 L 1482 988" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101002342_1" Pin1InfoVect0LinkObjId="100002144_0" Plane="0"/>
  </metadata>
 <path d="M 1482 951 L 1482 988" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002141">
 <path d="M 1537 1165 L 1482 1165" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133002147_0" Pin1InfoVect0LinkObjId="34002143_0" Pin1InfoVect1LinkObjId="34002142_1" Plane="0"/>
  </metadata>
 <path d="M 1537 1165 L 1482 1165" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002142">
 <path d="M 1482 1040 L 1482 1165" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100002144_1" Pin1InfoVect0LinkObjId="34002143_0" Pin1InfoVect1LinkObjId="34002141_1" Plane="0"/>
  </metadata>
 <path d="M 1482 1040 L 1482 1165" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002143">
 <path d="M 1482 1165 L 1482 1186" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002141_1" Pin0InfoVect1LinkObjId="34002142_1" Pin1InfoVect0LinkObjId="32002135_0" Plane="0"/>
  </metadata>
 <path d="M 1482 1165 L 1482 1186" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002172">
 <path d="M 1788 951 L 1788 988" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101002344_1" Pin1InfoVect0LinkObjId="100002176_0" Plane="0"/>
  </metadata>
 <path d="M 1788 951 L 1788 988" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002174">
 <path d="M 1788 1040 L 1788 1165" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100002176_1" Pin1InfoVect0LinkObjId="34002175_0" Plane="0"/>
  </metadata>
 <path d="M 1788 1040 L 1788 1165" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002175">
 <path d="M 1788 1165 L 1788 1190" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002174_1" Pin1InfoVect0LinkObjId="32002167_0" Plane="0"/>
  </metadata>
 <path d="M 1788 1165 L 1788 1190" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002188">
 <path d="M 2093 951 L 2093 1159" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101002346_1" Pin1InfoVect0LinkObjId="107002302_0" Plane="0"/>
  </metadata>
 <path d="M 2093 951 L 2093 1159" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002278">
 <path d="M 2400 959 L 2400 1136" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="131002215_1" Pin1InfoVect0LinkObjId="102002217_0" Plane="0"/>
  </metadata>
 <path d="M 2400 959 L 2400 1136" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002299">
 <path d="M 1626 266 L 1504 266 L 1504 308" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="131001992_0" Pin1InfoVect0LinkObjId="107002305_0" Plane="0"/>
  </metadata>
 <path d="M 1626 266 L 1504 266 L 1504 308" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002357">
 <path d="M 1660 266 L 1773 266" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="131001992_1" Pin1InfoVect0LinkObjId="34002358_1" Pin1InfoVect1LinkObjId="34002361_0" Plane="0"/>
  </metadata>
 <path d="M 1660 266 L 1773 266" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002358">
 <path d="M 1773 224 L 1773 266" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="36002000_0" Pin1InfoVect0LinkObjId="34002357_1" Pin1InfoVect1LinkObjId="34002361_0" Plane="0"/>
  </metadata>
 <path d="M 1773 224 L 1773 266" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002360">
 <path d="M 1816 282 L 1773 282" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133001985_0" Pin1InfoVect0LinkObjId="34002404_1" Pin1InfoVect1LinkObjId="34002361_1" Plane="0"/>
  </metadata>
 <path d="M 1816 282 L 1773 282" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002361">
 <path d="M 1773 266 L 1773 282" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002357_1" Pin0InfoVect1LinkObjId="34002358_1" Pin1InfoVect0LinkObjId="34002404_1" Pin1InfoVect1LinkObjId="34002360_1" Plane="0"/>
  </metadata>
 <path d="M 1773 266 L 1773 282" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002363">
 <path d="M 1773 467 L 1773 518" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="100000027_1" Pin1InfoVect0LinkObjId="102001655_0" Plane="0"/>
  </metadata>
 <path d="M 1773 467 L 1773 518" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002393">
 <path d="M 1658 385 L 1773 385" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111001778_0" Pin1InfoVect0LinkObjId="34002395_0" Pin1InfoVect1LinkObjId="34002403_1" Plane="0"/>
  </metadata>
 <path d="M 1658 385 L 1773 385" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002395">
 <path d="M 1773 385 L 1773 415" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002393_1" Pin0InfoVect1LinkObjId="34002403_1" Pin1InfoVect0LinkObjId="100000027_0" Plane="0"/>
  </metadata>
 <path d="M 1773 385 L 1773 415" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002396">
 <path d="M 2399 924 L 2399 852" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="131002215_0" Pin1InfoVect0LinkObjId="30000089_0" Plane="0"/>
  </metadata>
 <path d="M 2399 924 L 2399 852" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002398">
 <path d="M 2093 905 L 2093 852" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101002346_0" Pin1InfoVect0LinkObjId="30000089_0" Plane="0"/>
  </metadata>
 <path d="M 2093 905 L 2093 852" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002399">
 <path d="M 1788 905 L 1788 852" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101002344_0" Pin1InfoVect0LinkObjId="30000089_0" Plane="0"/>
  </metadata>
 <path d="M 1788 905 L 1788 852" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002400">
 <path d="M 1482 905 L 1482 852" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101002342_0" Pin1InfoVect0LinkObjId="30000089_0" Plane="0"/>
  </metadata>
 <path d="M 1482 905 L 1482 852" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002401">
 <path d="M 1175 905 L 1175 852" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101002340_0" Pin1InfoVect0LinkObjId="30000089_0" Plane="0"/>
  </metadata>
 <path d="M 1175 905 L 1175 852" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002402">
 <path d="M 1773 758 L 1773 719" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101001784_0" Pin1InfoVect0LinkObjId="100000084_1" Plane="0"/>
  </metadata>
 <path d="M 1773 758 L 1773 719" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002403">
 <path d="M 1773 349 L 1773 385" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000022_1" Pin1InfoVect0LinkObjId="34002393_1" Pin1InfoVect1LinkObjId="34002395_0" Plane="0"/>
  </metadata>
 <path d="M 1773 349 L 1773 385" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002404">
 <path d="M 1773 303 L 1773 282" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101000022_0" Pin1InfoVect0LinkObjId="34002360_1" Pin1InfoVect1LinkObjId="34002361_1" Plane="0"/>
  </metadata>
 <path d="M 1773 303 L 1773 282" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="MeasurementValue_Layer">
 <g id="33002239">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,0)" writing-mode="lr" x="1883" xml:space="preserve" y="428">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708383" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/35kV\XF_#1主变-高:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33002240">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,0)" writing-mode="lr" x="1883" xml:space="preserve" y="456">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375322986381343" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/35kV\XF_#1主变-高:Q" Plane="0"/>
  </metadata>
 </g>
 <g id="33002241">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,0)" writing-mode="lr" x="1883" xml:space="preserve" y="485">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375451835400223" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/35kV\XF_#1主变-高:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33002243">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,0)" writing-mode="lr" x="1883" xml:space="preserve" y="683">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375280036708384" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/10kV\XF_#1主变-低:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33002244">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,0)" writing-mode="lr" x="1883" xml:space="preserve" y="712">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375322986381344" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/10kV\XF_#1主变-低:Q" Plane="0"/>
  </metadata>
 </g>
 <g id="33002245">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,0)" writing-mode="lr" x="1883" xml:space="preserve" y="740">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375451835400224" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/10kV\XF_#1主变-低:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33002312">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,0)" writing-mode="lr" x="1758" xml:space="preserve" y="1351">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967862203482144" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/10kV\LD_10kV备用线二:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33002313">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,0)" writing-mode="lr" x="1758" xml:space="preserve" y="1381">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967905153155104" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/10kV\LD_10kV备用线二:Q" Plane="0"/>
  </metadata>
 </g>
 <g id="33002315">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,0)" writing-mode="lr" x="1758" xml:space="preserve" y="1412">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052501024" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/10kV\LD_10kV备用线二:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33002316">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,0)" writing-mode="lr" x="1452" xml:space="preserve" y="1351">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967862203482142" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/10kV\LD_10kV二台坡线:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33002317">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,0)" writing-mode="lr" x="1452" xml:space="preserve" y="1381">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967905153155102" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/10kV\LD_10kV二台坡线:Q" Plane="0"/>
  </metadata>
 </g>
 <g id="33002319">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,0)" writing-mode="lr" x="1452" xml:space="preserve" y="1412">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052501022" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/10kV\LD_10kV二台坡线:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33002320">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,0)" writing-mode="lr" x="1145" xml:space="preserve" y="1351">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967862203482141" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/10kV\LD_10kV备用线一:P" Plane="0"/>
  </metadata>
 </g>
 <g id="33002321">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,0)" writing-mode="lr" x="1145" xml:space="preserve" y="1381">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967905153155101" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/10kV\LD_10kV备用线一:Q" Plane="0"/>
  </metadata>
 </g>
 <g id="33002323">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,0)" writing-mode="lr" x="1145" xml:space="preserve" y="1412">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115967991052501021" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/10kV\LD_10kV备用线一:I_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33002350">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,0)" writing-mode="lr" x="996" xml:space="preserve" y="747">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115405084048752664" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/10kV\BS_10kVⅠ母:V_A" Plane="0"/>
  </metadata>
 </g>
 <g id="33002351">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,0)" writing-mode="lr" x="996" xml:space="preserve" y="662">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115404869300387864" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/10kV\BS_10kVⅠ母:V" Plane="0"/>
  </metadata>
 </g>
 <g id="33002352">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,0)" writing-mode="lr" x="996" xml:space="preserve" y="690">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115404998149406744" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/10kV\BS_10kVⅠ母:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33002353">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,0)" writing-mode="lr" x="996" xml:space="preserve" y="718">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115405041099079704" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/10kV\BS_10kVⅠ母:other" Plane="0"/>
  </metadata>
 </g>
 <g id="33002409">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,0)" writing-mode="lr" x="996" xml:space="preserve" y="776">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115405126998425624" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/10kV\BS_10kVⅠ母:V_B" Plane="0"/>
  </metadata>
 </g>
 <g id="33002411">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,0)" writing-mode="lr" x="996" xml:space="preserve" y="805">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="115405169948098584" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/10kV\BS_10kVⅠ母:V_C" Plane="0"/>
  </metadata>
 </g>
 <g id="33002467">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,0)" writing-mode="lr" x="310" xml:space="preserve" y="1128">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="122723132795518986" ObjectName="122723089845846026:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002466">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,0)" writing-mode="lr" x="310" xml:space="preserve" y="1213">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="122723132795518985" ObjectName="122723089845846025:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002465">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,0)" writing-mode="lr" x="296" xml:space="preserve" y="1010">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="122723132795518984" ObjectName="122723089845846024:M" Plane="0"/>
  </metadata>
 </g>
 <g id="33002462">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,0)" writing-mode="lr" x="286" xml:space="preserve" y="722">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="113997494416834589" ObjectName="版纳_35kV_老关坪变:P_LOAD" Plane="0"/>
  </metadata>
 </g>
 <g id="33002463">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,0)" writing-mode="lr" x="286" xml:space="preserve" y="804">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="113997515891671069" ObjectName="版纳_35kV_老关坪变:Q_LOAD" Plane="0"/>
  </metadata>
 </g>
 <g id="33002495">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,0)" writing-mode="lr" x="1883" xml:space="preserve" y="515">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="117375580684419103" ObjectName="版纳_35kV_老关坪变\版纳_35kV_老关坪变/35kV\XF_#1主变-高:factor" Plane="0"/>
  </metadata>
 </g>
</g>
<g id="Text_Layer">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="1795" xml:space="preserve" y="340">3016</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="1795" xml:space="preserve" y="457">301</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="1193" xml:space="preserve" y="1031">041</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="1568" xml:space="preserve" y="433">30167</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="1795" xml:space="preserve" y="796">0011</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="1500" xml:space="preserve" y="1031">042</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="1807" xml:space="preserve" y="1031">044</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="1822" xml:space="preserve" y="562">1号主变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="1795" xml:space="preserve" y="708">001</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="2311" xml:space="preserve" y="1361">10kV站用变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="2416" xml:space="preserve" y="961">0451</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="1618" xml:space="preserve" y="308">3019</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(230,232,254)" writing-mode="lr" x="1723" xml:space="preserve" y="102">茶关线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="1999" xml:space="preserve" y="1362">10kVⅠ母TV</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="1718" xml:space="preserve" y="1450">备用线二</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="1409" xml:space="preserve" y="1450">二台坡线</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="1109" xml:space="preserve" y="1450">备用线一</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="1193" xml:space="preserve" y="947">0411</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="1500" xml:space="preserve" y="947">0421</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="1807" xml:space="preserve" y="947">0441</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="2111" xml:space="preserve" y="947">0901</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(36,169,128)" writing-mode="lr" x="1862" xml:space="preserve" y="428">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(36,169,128)" writing-mode="lr" x="1862" xml:space="preserve" y="456">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(36,169,128)" writing-mode="lr" x="1862" xml:space="preserve" y="485">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(36,169,128)" writing-mode="lr" x="1863" xml:space="preserve" y="683">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(36,169,128)" writing-mode="lr" x="1863" xml:space="preserve" y="712">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(36,169,128)" writing-mode="lr" x="1863" xml:space="preserve" y="740">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(36,169,128)" writing-mode="lr" x="1125" xml:space="preserve" y="1351">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(36,169,128)" writing-mode="lr" x="1125" xml:space="preserve" y="1381">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(36,169,128)" writing-mode="lr" x="1125" xml:space="preserve" y="1412">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(36,169,128)" writing-mode="lr" x="1432" xml:space="preserve" y="1351">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(36,169,128)" writing-mode="lr" x="1432" xml:space="preserve" y="1381">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(36,169,128)" writing-mode="lr" x="1432" xml:space="preserve" y="1412">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(36,169,128)" writing-mode="lr" x="1737" xml:space="preserve" y="1412">Ia</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(36,169,128)" writing-mode="lr" x="1737" xml:space="preserve" y="1381">Q</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(36,169,128)" writing-mode="lr" x="1737" xml:space="preserve" y="1351">P</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(36,169,128)" writing-mode="lr" x="930" xml:space="preserve" y="662">Uab</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(36,169,128)" writing-mode="lr" x="930" xml:space="preserve" y="690">Ubc</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(36,169,128)" writing-mode="lr" x="930" xml:space="preserve" y="718">Uca</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(36,169,128)" writing-mode="lr" x="930" xml:space="preserve" y="747">Ua</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="929" xml:space="preserve" y="841">10kVⅠ母</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(36,169,128)" writing-mode="lr" x="930" xml:space="preserve" y="776">Ub</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(36,169,128)" writing-mode="lr" x="930" xml:space="preserve" y="805">Uc</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="1369" xml:space="preserve" y="491">茶关线电压互感器</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="1822" xml:space="preserve" y="598">(S=1.6MVA)</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="tb" x="89" xml:space="preserve" y="1023">变</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="tb" x="89" xml:space="preserve" y="984">主</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="tb" x="89" xml:space="preserve" y="947">号</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="tb" x="87" xml:space="preserve" y="576">位</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="281" xml:space="preserve" y="1363">66751</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="130" xml:space="preserve" y="1364">联系方式：</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="tb" x="88" xml:space="preserve" y="472">单</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="90" xml:space="preserve" y="804">总无功</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(170,0,0)" writing-mode="lr" x="375" xml:space="preserve" y="641">℃</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="288" xml:space="preserve" y="641">温度:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(170,0,0)" writing-mode="lr" x="231" xml:space="preserve" y="641">Hz</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="151" xml:space="preserve" y="641">频率:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(170,0,0)" writing-mode="lr" x="375" xml:space="preserve" y="560">kV</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="288" xml:space="preserve" y="560">电压:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(170,0,0)" writing-mode="lr" x="231" xml:space="preserve" y="560">A</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="151" xml:space="preserve" y="560">电流:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(170,0,0)" writing-mode="lr" x="375" xml:space="preserve" y="479">MVr</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="288" xml:space="preserve" y="479">无功:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="151" xml:space="preserve" y="479">有功:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(170,0,0)" writing-mode="lr" x="231" xml:space="preserve" y="479">MW</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="76" xml:space="preserve" y="1213">合闸母线电压</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="76" xml:space="preserve" y="1128">控制母线电压</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="183" xml:space="preserve" y="1010">油温</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="tb" x="89" xml:space="preserve" y="915">一</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="90" xml:space="preserve" y="722">总有功</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="141" xml:space="preserve" y="885">事故总:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="61" font-size="43" font-width="43" stroke="rgb(0,0,0)" writing-mode="lr" x="175" xml:space="preserve" y="114">35kV老关坪变</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="50" xml:space="preserve" y="383">工况</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="50" xml:space="preserve" y="415">退出</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="149" xml:space="preserve" y="383">不</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="134" xml:space="preserve" y="411">变化</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="217" xml:space="preserve" y="399">越限</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="314" xml:space="preserve" y="383">非</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="299" xml:space="preserve" y="411">实测</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="382" xml:space="preserve" y="383">数据</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(255,255,254)" writing-mode="lr" x="382" xml:space="preserve" y="415">封锁</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="32" font-size="32" font-width="32" stroke="rgb(36,169,128)" writing-mode="lr" x="1860" xml:space="preserve" y="515">Cs</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href="综自改造勐仑变.other.svg"><rect fill-opacity="0" height="101" stroke-opacity="0" stroke-width="1" width="375" x="56" y="36"/></g>
</g>
</svg>