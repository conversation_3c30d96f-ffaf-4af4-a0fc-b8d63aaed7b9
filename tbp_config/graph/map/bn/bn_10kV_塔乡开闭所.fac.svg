<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1500" id="thSvg" viewBox="0 0 2100 1500" width="2100">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="Arrester:bn_避雷器3_0" viewBox="0,0,20,40">
 <use Plane="0" x="10" xlink:href="#terminal" y="4"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="22" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,10,20)" width="10" x="5" y="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="31" y2="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="5" y2="18"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="5" x2="15" y1="35" y2="35"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="7" x2="13" y1="37" y2="37"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="9" x2="11" y1="39" y2="39"/>
 <path AFMask="2147483647" Plane="0" d="M 8 17 L 10 23 L 12 17 Z" fill="none" stroke="rgb(93,92,88)" stroke-width="1"/>
</symbol>
<symbol id="Disconnector:bn_刀闸24_0" viewBox="0,0,20,40">
 <use Plane="0" x="8" xlink:href="#terminal" y="4"/>
 <use Plane="0" x="7" xlink:href="#terminal" y="36"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="35" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="9" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="1" x2="15" y1="31" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="18" y1="9" y2="31"/>
</symbol>
<symbol id="Disconnector:bn_刀闸24_1" viewBox="0,0,20,40">
 <use Plane="0" x="8" xlink:href="#terminal" y="4"/>
 <use Plane="0" x="7" xlink:href="#terminal" y="36"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="35" y2="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="1" x2="15" y1="31" y2="31"/>
</symbol>
<symbol id="Reactance:bn_电抗001_0" viewBox="0,0,28,40">
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="14" x2="14" y1="5" y2="11"/>
 <path AFMask="2147483647" Plane="0" d="M 5 20 A 9 9 0 1 0 14 11" fill="none" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="5" x2="14" y1="20" y2="20"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="14" x2="14" y1="20" y2="33"/>
</symbol>
<symbol id="Reactance:bn_电抗001_1" viewBox="0,0,28,40">
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="7" x2="7" y1="5" y2="11"/>
 <path AFMask="2147483647" Plane="0" d="M 2 15 A 5 5 0 1 0 7 10" fill="none" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="7" y1="16" y2="16"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="7" x2="7" y1="16" y2="25"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="8" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,7,29)" width="4" x="5" y="25"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="7" x2="7" y1="33" y2="41"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="3" y1="37" y2="41"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="11" x2="11" y1="37" y2="41"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="3" x2="11" y1="41" y2="41"/>
</symbol>
<symbol id="Compensator:bn_电容器002_0" viewBox="0,0,32,48">
 <use Plane="0" x="15" xlink:href="#terminal" y="3"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="15" x2="15" y1="4" y2="17"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="11" x2="19" y1="18" y2="18"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="11" x2="19" y1="24" y2="24"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="15" x2="15" y1="24" y2="39"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="15" x2="7" y1="7" y2="7"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="7" x2="7" y1="7" y2="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="7" x2="7" y1="29" y2="32"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="7" x2="15" y1="32" y2="32"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="15" x2="27" y1="7" y2="7"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="27" x2="27" y1="7" y2="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="24" x2="30" y1="11" y2="11"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="30" x2="30" y1="12" y2="25"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="30" x2="24" y1="25" y2="25"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="24" x2="24" y1="11" y2="24"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="27" x2="27" y1="11" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="25" x2="29" y1="13" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="29" x2="27" y1="13" y2="15"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="25" x2="27" y1="13" y2="16"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="27" x2="27" y1="25" y2="29"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="24" x2="30" y1="29" y2="29"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="25" x2="29" y1="31" y2="31"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="26" x2="28" y1="33" y2="33"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="2" x2="2" y1="8" y2="32"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="9" y1="35" y2="39"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="20" y1="39" y2="39"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="21" x2="21" y1="39" y2="35"/>
 <path AFMask="2147483647" Plane="0" d="M 7 11 L 4 14 L 7 17 L 4 20 L 7 23 L 4 26 L 7 29 " fill="none" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="PT:bn_电压互感器004_0" viewBox="0,0,100,100">
 <use Plane="0" x="33" xlink:href="#terminal" y="97"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="28" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,75,51)" width="12" x="69" y="37"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="21" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,33,67)" width="10" x="28" y="57"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="33" y1="17" y2="24"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="28" y1="24" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="38" y1="24" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="38" y1="40" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="28" y1="40" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="33" y1="33" y2="40"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="49" x2="49" y1="33" y2="40"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="49" x2="44" y1="40" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="49" x2="54" y1="40" y2="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="52" x2="52" y1="17" y2="30"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="52" x2="45" y1="30" y2="27"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="52" x2="45" y1="17" y2="21"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="5" x2="15" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="7" x2="13" y1="10" y2="10"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="11" y1="8" y2="8"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="10" x2="10" y1="40" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="10" x2="22" y1="40" y2="40"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="70" x2="80" y1="32" y2="32"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="72" x2="78" y1="30" y2="30"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="74" x2="76" y1="28" y2="28"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="75" x2="75" y1="32" y2="37"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="75" x2="75" y1="65" y2="62"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="33" x2="33" y1="51" y2="97"/>
 <path AFMask="2147483647" Plane="0" d="M 72 61 L 75 44 L 78 61 Z" fill="rgb(0,0,255)" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="33" cy="24" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="33" cy="40" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="49" cy="40" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="49" cy="24" fill="none" r="11" stroke="rgb(0,0,255)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="0" d="M 75 66 L 75 84 L 33 84 " fill="none" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸16_0" viewBox="0,0,40,20">
 <use Plane="0" x="36" xlink:href="#terminal" y="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="31" y1="5" y2="19"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="13" x2="10" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="7" x2="7" y1="8" y2="16"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="5" x2="5" y1="10" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="35" x2="31" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="9" y1="6" y2="18"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="13" x2="31" y1="12" y2="2"/>
</symbol>
<symbol id="GroundDisconnector:bn_接地刀闸16_1" viewBox="0,0,40,20">
 <use Plane="0" x="36" xlink:href="#terminal" y="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="31" x2="31" y1="5" y2="19"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="35" x2="10" y1="12" y2="12"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="7" x2="7" y1="8" y2="16"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="5" x2="5" y1="10" y2="14"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="9" y1="6" y2="18"/>
</symbol>
<symbol id="Transformer2:bn_两卷变压器002_0" viewBox="0,0,60,86">
 <use Plane="0" x="38" xlink:href="#terminal" y="4"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="38" x2="45" y1="19" y2="26"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="38" x2="31" y1="19" y2="26"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="38" x2="38" y1="11" y2="19"/>
 <circle AFMask="2147483647" Plane="0" cx="38" cy="20" fill="none" r="15" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Transformer2:bn_两卷变压器002_1" viewBox="0,0,60,86">
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="38" x2="38" y1="43" y2="35"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="38" x2="45" y1="43" y2="50"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="38" x2="31" y1="43" y2="50"/>
 <circle AFMask="2147483647" Plane="1" cx="38" cy="44" fill="none" r="15" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Fuse:bn_熔断器3_0" viewBox="0,0,32,16">
 <use Plane="0" x="4" xlink:href="#terminal" y="12"/>
 <use Plane="0" x="26" xlink:href="#terminal" y="12"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="5" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(23,16,7)" width="18" x="7" y="5"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="5" x2="27" y1="3" y2="13"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="7" x2="7" y1="9" y2="15"/>
</symbol>
<symbol id="Fuse:bn_熔断器9_0" viewBox="0,0,20,60">
 <use Plane="0" x="10" xlink:href="#terminal" y="4"/>
 <use Plane="0" x="10" xlink:href="#terminal" y="44"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="4" y2="44"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="30" stroke="rgb(255,255,254)" stroke-width="1" transform="rotate(0,10,24)" width="14" x="3" y="9"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_0_0" viewBox="0,0,20,74">
 <rect AFMask="2147483647" Plane="0" fill="none" height="0" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,4,16)" width="0" x="4" y="16"/>
 <rect AFMask="2147483647" Plane="0" fill="none" height="30" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,9,37)" width="13" x="3" y="22"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_0_1" viewBox="0,0,20,74">
 <rect AFMask="2147483647" Plane="0" fill="none" height="30" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,9,36)" width="13" x="3" y="21"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_0_2" viewBox="0,0,20,74">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,254)" height="30" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,9,36)" width="13" x="3" y="21"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_0_3" viewBox="0,0,20,74">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,254)" height="30" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,9,37)" width="13" x="3" y="22"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_1_0" viewBox="0,0,20,74">
 <use Plane="1" x="9" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="9" xlink:href="#terminal" y="69"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="21" y2="10"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="52" y2="63"/>
 <path AFMask="2147483647" Plane="1" d="M 3 11 L 10 5 L 17 12 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 3 57 L 9 64 L 16 57 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 3 15 L 10 9 L 17 16 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 3 61 L 9 68 L 16 61 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_1_1" viewBox="0,0,20,74">
 <use Plane="1" x="9" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="9" xlink:href="#terminal" y="69"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="21" y2="9"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="52" y2="65"/>
 <path AFMask="2147483647" Plane="1" d="M 4 12 L 10 6 L 16 12 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
 <path AFMask="2147483647" Plane="1" d="M 4 61 L 10 67 L 16 61 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_1_2" viewBox="0,0,20,74">
 <use Plane="1" x="9" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="9" xlink:href="#terminal" y="69"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="22" y2="10"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="52" y2="63"/>
 <path AFMask="2147483647" Plane="1" d="M 3 11 L 9 4 L 16 11 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 3 15 L 9 8 L 16 15 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 2 57 L 9 63 L 16 56 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 2 61 L 9 67 L 16 60 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关2_1_3" viewBox="0,0,20,74">
 <use Plane="1" x="9" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="9" xlink:href="#terminal" y="69"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="21" y2="8"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="52" y2="65"/>
 <path AFMask="2147483647" Plane="1" d="M 3 11 L 9 5 L 15 11 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
 <path AFMask="2147483647" Plane="1" d="M 3 60 L 9 66 L 15 60 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关4_0_0" viewBox="0,0,20,70">
 <rect AFMask="2147483647" Plane="0" fill="none" height="0" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,4,14)" width="0" x="4" y="14"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关4_0_1" viewBox="0,0,20,70">
 <rect AFMask="2147483647" Plane="0" fill="none" height="29" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,10,34)" width="12" x="4" y="20"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关4_0_2" viewBox="0,0,20,70">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,254)" height="27" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,10,34)" width="12" x="4" y="21"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关4_0_3" viewBox="0,0,20,70">
 <rect AFMask="2147483647" Plane="0" fill="rgb(255,255,254)" height="27" stroke="rgb(255,255,254)" stroke-width="2" transform="rotate(0,10,34)" width="12" x="4" y="21"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关4_1_0" viewBox="0,0,20,70">
 <use Plane="1" x="10" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="10" xlink:href="#terminal" y="66"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="57" y2="12"/>
 <path AFMask="2147483647" Plane="1" d="M 4 17 L 9 11 L 16 17 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 4 11 L 9 5 L 16 11 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 4 51 L 9 57 L 16 51 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 4 57 L 9 63 L 16 57 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关4_1_1" viewBox="0,0,20,70">
 <use Plane="1" x="10" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="10" xlink:href="#terminal" y="66"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="19" y2="8"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="49" y2="60"/>
 <path AFMask="2147483647" Plane="1" d="M 5 11 L 10 6 L 15 11 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
 <path AFMask="2147483647" Plane="1" d="M 5 57 L 10 62 L 15 57 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关4_1_2" viewBox="0,0,20,70">
 <use Plane="1" x="10" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="10" xlink:href="#terminal" y="66"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="20" y2="12"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="49" y2="57"/>
 <path AFMask="2147483647" Plane="1" d="M 4 16 L 9 11 L 16 17 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 4 11 L 9 5 L 16 11 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 4 51 L 9 57 L 16 51 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
 <path AFMask="2147483647" Plane="1" d="M 4 57 L 9 63 L 16 57 " fill="none" stroke="rgb(255,255,254)" stroke-width="1"/>
</symbol>
<symbol id="DollyBreaker:bn_小车开关4_1_3" viewBox="0,0,20,70">
 <use Plane="1" x="10" xlink:href="#terminal" y="3"/>
 <use Plane="1" x="10" xlink:href="#terminal" y="66"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="20" y2="9"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="10" x2="10" y1="49" y2="60"/>
 <path AFMask="2147483647" Plane="1" d="M 4 11 L 10 5 L 14 11 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
 <path AFMask="2147483647" Plane="1" d="M 4 56 L 10 62 L 14 56 " fill="none" stroke="rgb(255,255,254)" stroke-width="3"/>
</symbol>
<symbol id="Transformer2:bn_站用变2_0" viewBox="0,0,60,86">
 <use Plane="0" x="36" xlink:href="#terminal" y="22"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="36" x2="45" y1="40" y2="49"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="36" x2="29" y1="40" y2="47"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="36" x2="36" y1="24" y2="40"/>
 <circle AFMask="2147483647" Plane="0" cx="36" cy="39" fill="none" r="15" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Transformer2:bn_站用变2_1" viewBox="0,0,60,86">
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="28" x2="43" y1="66" y2="66"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="36" x2="43" y1="58" y2="65"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="35" x2="28" y1="58" y2="65"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="10" x2="36" y1="40" y2="40"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="38" x2="38" y1="79" y2="82"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="12" x2="12" y1="29" y2="39"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="5" x2="5" y1="39" y2="42"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="7" x2="7" y1="42" y2="38"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="9" x2="9" y1="37" y2="43"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="25" x2="18" y1="25" y2="25"/>
 <circle AFMask="2147483647" Plane="1" cx="36" cy="63" fill="none" r="15" stroke="rgb(0,0,255)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="1" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="12" x2="30" y1="40" y2="18"/>
</symbol>
<symbol id="Terminal:bn_终端设备10_0" viewBox="0,0,38,12">
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,255,0)" stroke-width="1" x1="2" x2="7" y1="6" y2="6"/>
 <polygon AFMask="2147483647" Plane="0" fill="none" points="7,3 14,5 7,8" stroke="rgb(0,255,0)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,255,0)" stroke-width="1" x1="12" x2="22" y1="6" y2="6"/>
 <polygon AFMask="2147483647" Plane="0" fill="none" points="21,5 28,3 28,8" stroke="rgb(0,255,0)" stroke-width="1"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,255,0)" stroke-width="1" x1="27" x2="32" y1="6" y2="6"/>
</symbol>
<symbol id="GZP:gg_光子牌1_0" viewBox="0,0,36,36">
 <circle AFMask="2147483647" Plane="0" cx="18" cy="18" fill="rgb(0,255,0)" r="15" stroke="rgb(0,255,0)" stroke-width="1"/>
</symbol>
<symbol id="GZP:gg_光子牌1_1" viewBox="0,0,36,36">
 <circle AFMask="2147483647" Plane="0" cx="18" cy="18" fill="rgb(255,0,0)" r="15" stroke="rgb(255,0,0)" stroke-width="1"/>
</symbol>
<symbol id="Disconnector:ocs_dz2_0" viewBox="0,0,16,32">
 <use Plane="0" x="8" xlink:href="#terminal" y="3"/>
 <use Plane="0" x="8" xlink:href="#terminal" y="29"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="1" y1="27" y2="9"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="2" x1="4" x2="12" y1="7" y2="7"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="5" y2="7"/>
 <circle AFMask="2147483647" Plane="0" cx="8" cy="3" fill="none" r="2" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="8" cy="29" fill="none" r="2" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
<symbol id="Disconnector:ocs_dz2_1" viewBox="0,0,16,32">
 <use Plane="0" x="8" xlink:href="#terminal" y="3"/>
 <use Plane="0" x="8" xlink:href="#terminal" y="29"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="1" x1="8" x2="8" y1="5" y2="27"/>
 <line AFMask="2147483647" Plane="0" fill="none" stroke="rgb(0,0,255)" stroke-width="2" x1="4" x2="12" y1="7" y2="7"/>
 <circle AFMask="2147483647" Plane="0" cx="8" cy="3" fill="none" r="2" stroke="rgb(0,0,255)" stroke-width="1"/>
 <circle AFMask="2147483647" Plane="0" cx="8" cy="29" fill="none" r="2" stroke="rgb(0,0,255)" stroke-width="1"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="版纳_10kV_塔乡开闭所" InitShowingPlane="0," fill="rgb(0,0,0)" height="1500" width="2100" x="0" y="0"/>
</g>
<g id="Other_Layer">
 
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="356" x2="356" y1="15" y2="1487"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="14" x2="355" y1="417" y2="417"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="15" x2="356" y1="216" y2="216"/>
</g>
<g id="Bus_Layer">
 <g id="30000683">
  <path d="M 1294 493 L 1913 493" stroke="rgb(128,128,128)" stroke-width="4"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 1294 493 L 1913 493" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="30000089">
  <path d="M 492 493 L 1189 493" stroke="rgb(128,128,128)" stroke-width="4"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <path d="M 492 493 L 1189 493" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="Disconnector_Layer">
 <g id="101002009">
  <use class="kv-1" height="32" transform="rotate(0,823,316) scale(1,1) translate(-8,-16)" width="16" x="823" xlink:href="#Disconnector:ocs_dz2_0" y="316"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(0,823,316) scale(1,1) translate(-8,-16)" width="16" x="823" y="316"/></g>
 <g id="101002042">
  <use class="kv-1" height="32" transform="rotate(0,1458,314) scale(1,1) translate(-8,-16)" width="16" x="1458" xlink:href="#Disconnector:ocs_dz2_0" y="314"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(0,1458,314) scale(1,1) translate(-8,-16)" width="16" x="1458" y="314"/></g>
 <g id="101002220">
  <use class="kv-1" height="40" transform="rotate(0,745,729) scale(1,1) translate(-8,-20)" width="20" x="745" xlink:href="#Disconnector:bn_刀闸24_0" y="729"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,745,729) scale(1,1) translate(-8,-20)" width="20" x="745" y="729"/></g>
 <g id="101002280">
  <use class="kv-1" height="40" transform="rotate(0,1477,729) scale(1,1) translate(-8,-20)" width="20" x="1477" xlink:href="#Disconnector:bn_刀闸24_0" y="729"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1477,729) scale(1,1) translate(-8,-20)" width="20" x="1477" y="729"/></g>
 <g id="101002300">
  <use class="kv-1" height="40" transform="rotate(0,1577,729) scale(1,1) translate(-8,-20)" width="20" x="1577" xlink:href="#Disconnector:bn_刀闸24_0" y="729"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1577,729) scale(1,1) translate(-8,-20)" width="20" x="1577" y="729"/></g>
 <g id="101002320">
  <use class="kv-1" height="40" transform="rotate(0,1677,729) scale(1,1) translate(-8,-20)" width="20" x="1677" xlink:href="#Disconnector:bn_刀闸24_0" y="729"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1677,729) scale(1,1) translate(-8,-20)" width="20" x="1677" y="729"/></g>
</g>
<g id="GroundDisconnector_Layer">
 <g id="111002065">
  <use class="kv-1" height="20" transform="rotate(0,532,608) scale(1,1) translate(-36,-12)" width="40" x="532" xlink:href="#GroundDisconnector:bn_接地刀闸16_0" y="608"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,532,608) scale(1,1) translate(-36,-12)" width="40" x="532" y="608"/></g>
 <g id="111002087">
  <use class="kv-1" height="20" transform="rotate(0,525,776) scale(1,1) translate(-36,-12)" width="40" x="525" xlink:href="#GroundDisconnector:bn_接地刀闸16_0" y="776"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,525,776) scale(1,1) translate(-36,-12)" width="40" x="525" y="776"/></g>
 <g id="111002147">
  <use class="kv-1" height="20" transform="rotate(0,632,608) scale(1,1) translate(-36,-12)" width="40" x="632" xlink:href="#GroundDisconnector:bn_接地刀闸16_0" y="608"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,632,608) scale(1,1) translate(-36,-12)" width="40" x="632" y="608"/></g>
 <g id="111002171">
  <use class="kv-1" height="20" transform="rotate(0,732,608) scale(1,1) translate(-36,-12)" width="40" x="732" xlink:href="#GroundDisconnector:bn_接地刀闸16_0" y="608"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,732,608) scale(1,1) translate(-36,-12)" width="40" x="732" y="608"/></g>
 <g id="111002185">
  <use class="kv-1" height="20" transform="rotate(0,832,608) scale(1,1) translate(-36,-12)" width="40" x="832" xlink:href="#GroundDisconnector:bn_接地刀闸16_0" y="608"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,832,608) scale(1,1) translate(-36,-12)" width="40" x="832" y="608"/></g>
 <g id="111002199">
  <use class="kv-1" height="20" transform="rotate(0,982,608) scale(1,1) translate(-36,-12)" width="40" x="982" xlink:href="#GroundDisconnector:bn_接地刀闸16_0" y="608"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,982,608) scale(1,1) translate(-36,-12)" width="40" x="982" y="608"/></g>
 <g id="111002213">
  <use class="kv-1" height="20" transform="rotate(0,1063,608) scale(1,1) translate(-36,-12)" width="40" x="1063" xlink:href="#GroundDisconnector:bn_接地刀闸16_0" y="608"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1063,608) scale(1,1) translate(-36,-12)" width="40" x="1063" y="608"/></g>
 <g id="111002282">
  <use class="kv-1" height="20" transform="rotate(0,1464,608) scale(1,1) translate(-36,-12)" width="40" x="1464" xlink:href="#GroundDisconnector:bn_接地刀闸16_0" y="608"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1464,608) scale(1,1) translate(-36,-12)" width="40" x="1464" y="608"/></g>
 <g id="111002302">
  <use class="kv-1" height="20" transform="rotate(0,1564,608) scale(1,1) translate(-36,-12)" width="40" x="1564" xlink:href="#GroundDisconnector:bn_接地刀闸16_0" y="608"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1564,608) scale(1,1) translate(-36,-12)" width="40" x="1564" y="608"/></g>
 <g id="111002322">
  <use class="kv-1" height="20" transform="rotate(0,1664,608) scale(1,1) translate(-36,-12)" width="40" x="1664" xlink:href="#GroundDisconnector:bn_接地刀闸16_0" y="608"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1664,608) scale(1,1) translate(-36,-12)" width="40" x="1664" y="608"/></g>
 <g id="111002340">
  <use class="kv-1" height="20" transform="rotate(0,1805,610) scale(1,1) translate(-36,-12)" width="40" x="1805" xlink:href="#GroundDisconnector:bn_接地刀闸16_0" y="610"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1805,610) scale(1,1) translate(-36,-12)" width="40" x="1805" y="610"/></g>
 <g id="111002341">
  <use class="kv-1" height="20" transform="rotate(0,1798,778) scale(1,1) translate(-36,-12)" width="40" x="1798" xlink:href="#GroundDisconnector:bn_接地刀闸16_0" y="778"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281341832724481"/>
  </metadata>
 <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1798,778) scale(1,1) translate(-36,-12)" width="40" x="1798" y="778"/></g>
</g>
<g id="Transformer2_Layer">
<g id="102001990">
 <g id="1020019900">
  <use class="kv-1" height="86" transform="rotate(180,602,272) scale(0.892,0.892) translate(36.8879,10.9327)" width="60" x="602" xlink:href="#Transformer2:bn_站用变2_0" y="272"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="1020019901">
  <use class="kv-1" height="86" transform="rotate(180,602,272) scale(0.892,0.892) translate(36.8879,10.9327)" width="60" x="602" xlink:href="#Transformer2:bn_站用变2_1" y="272"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <metadata>
  <cge:PSR_Ref ObjectID="-1" ObjectName="-1" Plane="0"/>
 </metadata>
<rect fill="white" height="86" opacity="0" stroke="white" transform="rotate(180,602,272) scale(0.892,0.892) translate(36.8879,10.9327)" width="60" x="602" y="272"/></g>
<g id="102002247">
 <g id="1020022470">
  <use class="kv-1" height="86" transform="rotate(0,916,755) scale(1,1) translate(-38,-4)" width="60" x="916" xlink:href="#Transformer2:bn_两卷变压器002_0" y="755"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <g id="1020022471">
  <use class="kv-1" height="86" transform="rotate(0,916,755) scale(1,1) translate(-38,-4)" width="60" x="916" xlink:href="#Transformer2:bn_两卷变压器002_1" y="755"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 </g>
 <metadata>
  <cge:PSR_Ref ObjectID="-1" ObjectName="-1" Plane="0"/>
 </metadata>
<rect fill="white" height="86" opacity="0" stroke="white" transform="rotate(0,916,755) scale(1,1) translate(-38,-4)" width="60" x="916" y="755"/></g>
</g>
<g id="DollyBreaker_Layer">
 <g id="110001991">
  <use class="kv-1" height="74" transform="rotate(0,601,434) scale(1,1) translate(-10,-37)" width="20" x="601" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="434"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,601,434) scale(1,1) translate(-10,-37)" width="20" x="601" y="434"/></g>
 <g id="110001991">
  <use class="kv-1" height="74" transform="rotate(0,601,434) scale(1,1) translate(-10,-37)" width="20" x="601" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="434"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,601,434) scale(1,1) translate(-10,-37)" width="20" x="601" y="434"/></g>
 <g id="110002005">
  <use class="kv-1" height="74" transform="rotate(0,823,432) scale(1,1) translate(-10,-37)" width="20" x="823" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="432"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,823,432) scale(1,1) translate(-10,-37)" width="20" x="823" y="432"/></g>
 <g id="110002005">
  <use class="kv-1" height="74" transform="rotate(0,823,432) scale(1,1) translate(-10,-37)" width="20" x="823" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="432"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,823,432) scale(1,1) translate(-10,-37)" width="20" x="823" y="432"/></g>
 <g id="110002025">
  <use class="kv-1" height="70" transform="rotate(0,1050,412) scale(1,1) translate(-10,-35)" width="20" x="1050" xlink:href="#DollyBreaker:bn_小车开关4_0_0" y="412"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,1050,412) scale(1,1) translate(-10,-35)" width="20" x="1050" y="412"/></g>
 <g id="110002025">
  <use class="kv-1" height="70" transform="rotate(0,1050,412) scale(1,1) translate(-10,-35)" width="20" x="1050" xlink:href="#DollyBreaker:bn_小车开关4_1_0" y="412"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,1050,412) scale(1,1) translate(-10,-35)" width="20" x="1050" y="412"/></g>
 <g id="110002043">
  <use class="kv-1" height="74" transform="rotate(0,1458,430) scale(1,1) translate(-10,-37)" width="20" x="1458" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="430"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1458,430) scale(1,1) translate(-10,-37)" width="20" x="1458" y="430"/></g>
 <g id="110002043">
  <use class="kv-1" height="74" transform="rotate(0,1458,430) scale(1,1) translate(-10,-37)" width="20" x="1458" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="430"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1458,430) scale(1,1) translate(-10,-37)" width="20" x="1458" y="430"/></g>
 <g id="110002056">
  <use class="kv-1" height="70" transform="rotate(0,1693,410) scale(1,1) translate(-10,-35)" width="20" x="1693" xlink:href="#DollyBreaker:bn_小车开关4_0_0" y="410"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,1693,410) scale(1,1) translate(-10,-35)" width="20" x="1693" y="410"/></g>
 <g id="110002056">
  <use class="kv-1" height="70" transform="rotate(0,1693,410) scale(1,1) translate(-10,-35)" width="20" x="1693" xlink:href="#DollyBreaker:bn_小车开关4_1_0" y="410"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,1693,410) scale(1,1) translate(-10,-35)" width="20" x="1693" y="410"/></g>
 <g id="110002059">
  <use class="kv-1" height="74" transform="rotate(0,545,551) scale(1,1) translate(-10,-37)" width="20" x="545" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="551"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,545,551) scale(1,1) translate(-10,-37)" width="20" x="545" y="551"/></g>
 <g id="110002059">
  <use class="kv-1" height="74" transform="rotate(0,545,551) scale(1,1) translate(-10,-37)" width="20" x="545" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="551"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,545,551) scale(1,1) translate(-10,-37)" width="20" x="545" y="551"/></g>
 <g id="110002146">
  <use class="kv-1" height="74" transform="rotate(0,645,551) scale(1,1) translate(-10,-37)" width="20" x="645" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="551"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,645,551) scale(1,1) translate(-10,-37)" width="20" x="645" y="551"/></g>
 <g id="110002146">
  <use class="kv-1" height="74" transform="rotate(0,645,551) scale(1,1) translate(-10,-37)" width="20" x="645" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="551"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,645,551) scale(1,1) translate(-10,-37)" width="20" x="645" y="551"/></g>
 <g id="110002170">
  <use class="kv-1" height="74" transform="rotate(0,745,551) scale(1,1) translate(-10,-37)" width="20" x="745" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="551"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,745,551) scale(1,1) translate(-10,-37)" width="20" x="745" y="551"/></g>
 <g id="110002170">
  <use class="kv-1" height="74" transform="rotate(0,745,551) scale(1,1) translate(-10,-37)" width="20" x="745" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="551"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,745,551) scale(1,1) translate(-10,-37)" width="20" x="745" y="551"/></g>
 <g id="110002184">
  <use class="kv-1" height="74" transform="rotate(0,845,551) scale(1,1) translate(-10,-37)" width="20" x="845" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="551"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,845,551) scale(1,1) translate(-10,-37)" width="20" x="845" y="551"/></g>
 <g id="110002184">
  <use class="kv-1" height="74" transform="rotate(0,845,551) scale(1,1) translate(-10,-37)" width="20" x="845" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="551"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,845,551) scale(1,1) translate(-10,-37)" width="20" x="845" y="551"/></g>
 <g id="110002198">
  <use class="kv-1" height="74" transform="rotate(0,995,551) scale(1,1) translate(-10,-37)" width="20" x="995" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="551"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,995,551) scale(1,1) translate(-10,-37)" width="20" x="995" y="551"/></g>
 <g id="110002198">
  <use class="kv-1" height="74" transform="rotate(0,995,551) scale(1,1) translate(-10,-37)" width="20" x="995" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="551"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,995,551) scale(1,1) translate(-10,-37)" width="20" x="995" y="551"/></g>
 <g id="110002212">
  <use class="kv-1" height="74" transform="rotate(0,1076,551) scale(1,1) translate(-10,-37)" width="20" x="1076" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="551"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1076,551) scale(1,1) translate(-10,-37)" width="20" x="1076" y="551"/></g>
 <g id="110002212">
  <use class="kv-1" height="74" transform="rotate(0,1076,551) scale(1,1) translate(-10,-37)" width="20" x="1076" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="551"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1076,551) scale(1,1) translate(-10,-37)" width="20" x="1076" y="551"/></g>
 <g id="110002256">
  <use class="kv-1" height="70" transform="rotate(0,1150,544) scale(1,0.75) translate(-10,146.333)" width="20" x="1150" xlink:href="#DollyBreaker:bn_小车开关4_0_0" y="544"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,1150,544) scale(1,0.75) translate(-10,146.333)" width="20" x="1150" y="544"/></g>
 <g id="110002256">
  <use class="kv-1" height="70" transform="rotate(0,1150,544) scale(1,0.75) translate(-10,146.333)" width="20" x="1150" xlink:href="#DollyBreaker:bn_小车开关4_1_0" y="544"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="70" opacity="0" stroke="white" transform="rotate(0,1150,544) scale(1,0.75) translate(-10,146.333)" width="20" x="1150" y="544"/></g>
 <g id="110002262">
  <use class="kv-1" height="74" transform="rotate(0,1360,551) scale(1,1) translate(-10,-37)" width="20" x="1360" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="551"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1360,551) scale(1,1) translate(-10,-37)" width="20" x="1360" y="551"/></g>
 <g id="110002262">
  <use class="kv-1" height="74" transform="rotate(0,1360,551) scale(1,1) translate(-10,-37)" width="20" x="1360" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="551"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1360,551) scale(1,1) translate(-10,-37)" width="20" x="1360" y="551"/></g>
 <g id="110002281">
  <use class="kv-1" height="74" transform="rotate(0,1477,551) scale(1,1) translate(-10,-37)" width="20" x="1477" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="551"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1477,551) scale(1,1) translate(-10,-37)" width="20" x="1477" y="551"/></g>
 <g id="110002281">
  <use class="kv-1" height="74" transform="rotate(0,1477,551) scale(1,1) translate(-10,-37)" width="20" x="1477" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="551"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1477,551) scale(1,1) translate(-10,-37)" width="20" x="1477" y="551"/></g>
 <g id="110002301">
  <use class="kv-1" height="74" transform="rotate(0,1577,551) scale(1,1) translate(-10,-37)" width="20" x="1577" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="551"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1577,551) scale(1,1) translate(-10,-37)" width="20" x="1577" y="551"/></g>
 <g id="110002301">
  <use class="kv-1" height="74" transform="rotate(0,1577,551) scale(1,1) translate(-10,-37)" width="20" x="1577" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="551"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1577,551) scale(1,1) translate(-10,-37)" width="20" x="1577" y="551"/></g>
 <g id="110002321">
  <use class="kv-1" height="74" transform="rotate(0,1677,551) scale(1,1) translate(-10,-37)" width="20" x="1677" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="551"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1677,551) scale(1,1) translate(-10,-37)" width="20" x="1677" y="551"/></g>
 <g id="110002321">
  <use class="kv-1" height="74" transform="rotate(0,1677,551) scale(1,1) translate(-10,-37)" width="20" x="1677" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="551"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1677,551) scale(1,1) translate(-10,-37)" width="20" x="1677" y="551"/></g>
 <g id="110002339">
  <use class="kv-1" height="74" transform="rotate(0,1818,553) scale(1,1) translate(-10,-37)" width="20" x="1818" xlink:href="#DollyBreaker:bn_小车开关2_0_0" y="553"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1818,553) scale(1,1) translate(-10,-37)" width="20" x="1818" y="553"/></g>
 <g id="110002339">
  <use class="kv-1" height="74" transform="rotate(0,1818,553) scale(1,1) translate(-10,-37)" width="20" x="1818" xlink:href="#DollyBreaker:bn_小车开关2_1_0" y="553"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName=" -1" Plane="0"/>
   <cge:Meas_Ref ObjectID="-281298883051521"/>
  </metadata>
 <rect fill="white" height="74" opacity="0" stroke="white" transform="rotate(0,1818,553) scale(1,1) translate(-10,-37)" width="20" x="1818" y="553"/></g>
</g>
<g id="Compensator_Layer">
 <g id="113002071">
  <use class="kv-1" height="48" transform="rotate(0,544,811) scale(1.47,1.47) translate(-188.932,-262.299)" width="32" x="544" xlink:href="#Compensator:bn_电容器002_0" y="811"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="48" opacity="0" stroke="white" transform="rotate(0,544,811) scale(1.47,1.47) translate(-188.932,-262.299)" width="32" x="544" y="811"/></g>
 <g id="113002343">
  <use class="kv-1" height="48" transform="rotate(0,1817,813) scale(1.47,1.47) translate(-595.946,-262.939)" width="32" x="1817" xlink:href="#Compensator:bn_电容器002_0" y="813"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="48" opacity="0" stroke="white" transform="rotate(0,1817,813) scale(1.47,1.47) translate(-595.946,-262.939)" width="32" x="1817" y="813"/></g>
</g>
<g id="Reactor_Layer">
 <g id="114002076">
  <use class="kv-1" height="40" transform="rotate(0,544,720) scale(1,1) translate(-13,-23)" width="28" x="544" xlink:href="#Reactance:bn_电抗001_0" y="720"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,544,720) scale(1,1) translate(-13,-23)" width="28" x="544" y="720"/></g>
 <g id="114002344">
  <use class="kv-1" height="40" transform="rotate(0,1817,722) scale(1,1) translate(-13,-23)" width="28" x="1817" xlink:href="#Reactance:bn_电抗001_0" y="722"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1817,722) scale(1,1) translate(-13,-23)" width="28" x="1817" y="722"/></g>
</g>
<g id="ACLineSegment_Layer">
 <g id="36002012">
 <path d="M 823 263 L 823 227" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="ST=-1/LN=-1" ObjectName="ST=-1/LN=-1" Plane="0"/>
  </metadata>
 <path d="M 823 263 L 823 227" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="36002041">
 <path d="M 1458 261 L 1458 225" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="ST=-1/LN=-1" ObjectName="ST=-1/LN=-1" Plane="0"/>
  </metadata>
 <path d="M 1458 261 L 1458 225" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="36002154">
 <path d="M 645 788 L 645 841" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="ST=-1/LN=-1" ObjectName="ST=-1/LN=-1" Plane="0"/>
  </metadata>
 <path d="M 645 788 L 645 841" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="36002169">
 <path d="M 745 788 L 745 841" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="ST=-1/LN=-1" ObjectName="ST=-1/LN=-1" Plane="0"/>
  </metadata>
 <path d="M 745 788 L 745 841" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="36002183">
 <path d="M 845 788 L 845 841" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="ST=-1/LN=-1" ObjectName="ST=-1/LN=-1" Plane="0"/>
  </metadata>
 <path d="M 845 788 L 845 841" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="36002197">
 <path d="M 995 788 L 995 841" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="ST=-1/LN=-1" ObjectName="ST=-1/LN=-1" Plane="0"/>
  </metadata>
 <path d="M 995 788 L 995 841" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="36002211">
 <path d="M 1076 788 L 1076 841" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="ST=-1/LN=-1" ObjectName="ST=-1/LN=-1" Plane="0"/>
  </metadata>
 <path d="M 1076 788 L 1076 841" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="36002279">
 <path d="M 1477 788 L 1477 841" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="ST=-1/LN=-1" ObjectName="ST=-1/LN=-1" Plane="0"/>
  </metadata>
 <path d="M 1477 788 L 1477 841" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="36002299">
 <path d="M 1577 788 L 1577 841" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="ST=-1/LN=-1" ObjectName="ST=-1/LN=-1" Plane="0"/>
  </metadata>
 <path d="M 1577 788 L 1577 841" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="36002319">
 <path d="M 1677 788 L 1677 841" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="ST=-1/LN=-1" ObjectName="ST=-1/LN=-1" Plane="0"/>
  </metadata>
 <path d="M 1677 788 L 1677 841" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="PT_Layer">
 <g id="107002022">
  <use class="kv-1" height="100" transform="rotate(0,1050,364) scale(1,1) translate(-33,-97)" width="100" x="1050" xlink:href="#PT:bn_电压互感器004_0" y="364"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="100" opacity="0" stroke="white" transform="rotate(0,1050,364) scale(1,1) translate(-33,-97)" width="100" x="1050" y="364"/></g>
 <g id="107002055">
  <use class="kv-1" height="100" transform="rotate(0,1693,362) scale(1,1) translate(-33,-97)" width="100" x="1693" xlink:href="#PT:bn_电压互感器004_0" y="362"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="100" opacity="0" stroke="white" transform="rotate(0,1693,362) scale(1,1) translate(-33,-97)" width="100" x="1693" y="362"/></g>
</g>
<g id="GZP_Layer">
 <g id="135000016">
  <use class="kv-1" height="36" transform="rotate(0,168,355) scale(1,1) translate(-18,-18)" width="36" x="168" xlink:href="#GZP:gg_光子牌1_0" y="355"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,168,355) scale(1,1) translate(-18,-18)" width="36" x="168" y="355"/></g>
</g>
<g id="Arrester_Layer">
 <g id="133002020">
  <use class="kv-1" height="40" transform="rotate(-90,868,282) scale(1,1) translate(-10,-5)" width="20" x="868" xlink:href="#Arrester:bn_避雷器3_0" y="282"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(-90,868,282) scale(1,1) translate(-10,-5)" width="20" x="868" y="282"/></g>
 <g id="133002045">
  <use class="kv-1" height="40" transform="rotate(-90,1503,280) scale(1,1) translate(-10,-5)" width="20" x="1503" xlink:href="#Arrester:bn_避雷器3_0" y="280"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(-90,1503,280) scale(1,1) translate(-10,-5)" width="20" x="1503" y="280"/></g>
 <g id="133002068">
  <use class="kv-1" height="40" transform="rotate(0,571,606) scale(1,1) translate(-10,-5)" width="20" x="571" xlink:href="#Arrester:bn_避雷器3_0" y="606"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,571,606) scale(1,1) translate(-10,-5)" width="20" x="571" y="606"/></g>
 <g id="133002228">
  <use class="kv-1" height="40" transform="rotate(0,782,779) scale(1,1) translate(-10,-5)" width="20" x="782" xlink:href="#Arrester:bn_避雷器3_0" y="779"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,782,779) scale(1,1) translate(-10,-5)" width="20" x="782" y="779"/></g>
 <g id="133002229">
  <use class="kv-1" height="40" transform="rotate(0,1034,776) scale(1,1) translate(-10,-5)" width="20" x="1034" xlink:href="#Arrester:bn_避雷器3_0" y="776"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1034,776) scale(1,1) translate(-10,-5)" width="20" x="1034" y="776"/></g>
 <g id="133002235">
  <use class="kv-1" height="40" transform="rotate(0,1114,776) scale(1,1) translate(-10,-5)" width="20" x="1114" xlink:href="#Arrester:bn_避雷器3_0" y="776"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1114,776) scale(1,1) translate(-10,-5)" width="20" x="1114" y="776"/></g>
 <g id="133002284">
  <use class="kv-1" height="40" transform="rotate(0,1514,779) scale(1,1) translate(-10,-5)" width="20" x="1514" xlink:href="#Arrester:bn_避雷器3_0" y="779"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1514,779) scale(1,1) translate(-10,-5)" width="20" x="1514" y="779"/></g>
 <g id="133002304">
  <use class="kv-1" height="40" transform="rotate(0,1614,779) scale(1,1) translate(-10,-5)" width="20" x="1614" xlink:href="#Arrester:bn_避雷器3_0" y="779"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1614,779) scale(1,1) translate(-10,-5)" width="20" x="1614" y="779"/></g>
 <g id="133002324">
  <use class="kv-1" height="40" transform="rotate(0,1714,779) scale(1,1) translate(-10,-5)" width="20" x="1714" xlink:href="#Arrester:bn_避雷器3_0" y="779"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1714,779) scale(1,1) translate(-10,-5)" width="20" x="1714" y="779"/></g>
 <g id="133002345">
  <use class="kv-1" height="40" transform="rotate(0,1845,608) scale(1,1) translate(-10,-5)" width="20" x="1845" xlink:href="#Arrester:bn_避雷器3_0" y="608"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1845,608) scale(1,1) translate(-10,-5)" width="20" x="1845" y="608"/></g>
</g>
<g id="Fuse_Layer">
 <g id="131002048">
  <use class="kv-1" height="16" transform="rotate(359,1376,279) scale(1.168,1.168) translate(-213.918,-52.1301)" width="32" x="1376" xlink:href="#Fuse:bn_熔断器3_0" y="279"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="16" opacity="0" stroke="white" transform="rotate(359,1376,279) scale(1.168,1.168) translate(-213.918,-52.1301)" width="32" x="1376" y="279"/></g>
 <g id="131002243">
  <use class="kv-1" height="60" transform="rotate(0,918,735) scale(1,1) translate(-10,-24)" width="20" x="918" xlink:href="#Fuse:bn_熔断器9_0" y="735"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,918,735) scale(1,1) translate(-10,-24)" width="20" x="918" y="735"/></g>
</g>
<g id="Clock_Layer">
 <g id="56000015">
  
 <metadata/></g>
</g>
<g id="Term_Layer">
 <g id="112002000">
  <use class="kv-1" height="12" transform="rotate(90,600,365) scale(1.144,1.144) translate(-92.5245,-51.9441)" width="38" x="600" xlink:href="#Terminal:bn_终端设备10_0" y="365"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,600,365) scale(1.144,1.144) translate(-92.5245,-51.9441)" width="38" x="600" y="365"/></g>
 <g id="112002006">
  <use class="kv-1" height="12" transform="rotate(90,822,363) scale(1.144,1.144) translate(-120.469,-51.6923)" width="38" x="822" xlink:href="#Terminal:bn_终端设备10_0" y="363"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,822,363) scale(1.144,1.144) translate(-120.469,-51.6923)" width="38" x="822" y="363"/></g>
 <g id="112002044">
  <use class="kv-1" height="12" transform="rotate(90,1457,361) scale(1.144,1.144) translate(-200.399,-51.4406)" width="38" x="1457" xlink:href="#Terminal:bn_终端设备10_0" y="361"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,1457,361) scale(1.144,1.144) translate(-200.399,-51.4406)" width="38" x="1457" y="361"/></g>
 <g id="112002047">
  <use class="kv-1" height="12" transform="rotate(0,1421,280) scale(1.144,1.144) translate(-195.867,-41.2448)" width="38" x="1421" xlink:href="#Terminal:bn_终端设备10_0" y="280"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(0,1421,280) scale(1.144,1.144) translate(-195.867,-41.2448)" width="38" x="1421" y="280"/></g>
 <g id="112002070">
  <use class="kv-1" height="12" transform="rotate(90,544,647) scale(1.144,1.144) translate(-85.4755,-87.4406)" width="38" x="544" xlink:href="#Terminal:bn_终端设备10_0" y="647"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,544,647) scale(1.144,1.144) translate(-85.4755,-87.4406)" width="38" x="544" y="647"/></g>
 <g id="112002149">
  <use class="kv-1" height="12" transform="rotate(90,644,647) scale(1.144,1.144) translate(-98.063,-87.4406)" width="38" x="644" xlink:href="#Terminal:bn_终端设备10_0" y="647"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,644,647) scale(1.144,1.144) translate(-98.063,-87.4406)" width="38" x="644" y="647"/></g>
 <g id="112002172">
  <use class="kv-1" height="12" transform="rotate(90,744,647) scale(1.144,1.144) translate(-110.65,-87.4406)" width="38" x="744" xlink:href="#Terminal:bn_终端设备10_0" y="647"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,744,647) scale(1.144,1.144) translate(-110.65,-87.4406)" width="38" x="744" y="647"/></g>
 <g id="112002186">
  <use class="kv-1" height="12" transform="rotate(90,844,647) scale(1.144,1.144) translate(-123.238,-87.4406)" width="38" x="844" xlink:href="#Terminal:bn_终端设备10_0" y="647"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,844,647) scale(1.144,1.144) translate(-123.238,-87.4406)" width="38" x="844" y="647"/></g>
 <g id="112002200">
  <use class="kv-1" height="12" transform="rotate(90,995,647) scale(1.144,1.144) translate(-142.245,-87.4406)" width="38" x="995" xlink:href="#Terminal:bn_终端设备10_0" y="647"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,995,647) scale(1.144,1.144) translate(-142.245,-87.4406)" width="38" x="995" y="647"/></g>
 <g id="112002214">
  <use class="kv-1" height="12" transform="rotate(90,1075,647) scale(1.144,1.144) translate(-152.315,-87.4406)" width="38" x="1075" xlink:href="#Terminal:bn_终端设备10_0" y="647"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,1075,647) scale(1.144,1.144) translate(-152.315,-87.4406)" width="38" x="1075" y="647"/></g>
 <g id="112002242">
  <use class="kv-1" height="12" transform="rotate(0,944,703) scale(1.144,1.144) translate(-135.825,-94.4895)" width="38" x="944" xlink:href="#Terminal:bn_终端设备10_0" y="703"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(0,944,703) scale(1.144,1.144) translate(-135.825,-94.4895)" width="38" x="944" y="703"/></g>
 <g id="112002283">
  <use class="kv-1" height="12" transform="rotate(90,1476,647) scale(1.144,1.144) translate(-202.79,-87.4406)" width="38" x="1476" xlink:href="#Terminal:bn_终端设备10_0" y="647"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,1476,647) scale(1.144,1.144) translate(-202.79,-87.4406)" width="38" x="1476" y="647"/></g>
 <g id="112002303">
  <use class="kv-1" height="12" transform="rotate(90,1576,647) scale(1.144,1.144) translate(-215.378,-87.4406)" width="38" x="1576" xlink:href="#Terminal:bn_终端设备10_0" y="647"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,1576,647) scale(1.144,1.144) translate(-215.378,-87.4406)" width="38" x="1576" y="647"/></g>
 <g id="112002323">
  <use class="kv-1" height="12" transform="rotate(90,1676,647) scale(1.144,1.144) translate(-227.965,-87.4406)" width="38" x="1676" xlink:href="#Terminal:bn_终端设备10_0" y="647"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,1676,647) scale(1.144,1.144) translate(-227.965,-87.4406)" width="38" x="1676" y="647"/></g>
 <g id="112002342">
  <use class="kv-1" height="12" transform="rotate(90,1817,649) scale(1.144,1.144) translate(-245.713,-87.6923)" width="38" x="1817" xlink:href="#Terminal:bn_终端设备10_0" y="649"/>
  <metadata>
   <cge:PSR_Ref AFMask="39039" ObjectID="-1" ObjectName="-1" Plane="0"/>
  </metadata>
 <rect fill="white" height="12" opacity="0" stroke="white" transform="rotate(90,1817,649) scale(1.144,1.144) translate(-245.713,-87.6923)" width="38" x="1817" y="649"/></g>
</g>
<g id="Link_Layer">
 <g id="34001995">
 <path d="M 601 289 L 601 400" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="102001990_0" Pin1InfoVect0LinkObjId="110001991_0" Plane="0"/>
  </metadata>
 <path d="M 601 289 L 601 400" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34001996">
 <path d="M 601 463 L 601 493" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110001991_1" Pin1InfoVect0LinkObjId="30000089_0" Plane="0"/>
  </metadata>
 <path d="M 601 463 L 601 493" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002004">
 <path d="M 823 461 L 823 493" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110002005_1" Pin1InfoVect0LinkObjId="30000089_0" Plane="0"/>
  </metadata>
 <path d="M 823 461 L 823 493" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002010">
 <path d="M 823 398 L 823 329" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110002005_0" Pin1InfoVect0LinkObjId="101002009_1" Plane="0"/>
  </metadata>
 <path d="M 823 398 L 823 329" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002017">
 <path d="M 823 282 L 864 282" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002018_1" Pin0InfoVect1LinkObjId="34002019_0" Pin1InfoVect0LinkObjId="133002020_0" Plane="0"/>
  </metadata>
 <path d="M 823 282 L 864 282" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002018">
 <path d="M 823 303 L 823 282" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101002009_0" Pin1InfoVect0LinkObjId="34002017_0" Pin1InfoVect1LinkObjId="34002019_0" Plane="0"/>
  </metadata>
 <path d="M 823 303 L 823 282" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002019">
 <path d="M 823 282 L 823 263" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002017_0" Pin0InfoVect1LinkObjId="34002018_1" Pin1InfoVect0LinkObjId="36002012_0" Plane="0"/>
  </metadata>
 <path d="M 823 282 L 823 263" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002028">
 <path d="M 1050 493 L 1050 443" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="30000089_0" Pin1InfoVect0LinkObjId="110002025_1" Plane="0"/>
  </metadata>
 <path d="M 1050 493 L 1050 443" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002029">
 <path d="M 1050 380 L 1050 364" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110002025_0" Pin1InfoVect0LinkObjId="107002022_0" Plane="0"/>
  </metadata>
 <path d="M 1050 380 L 1050 364" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002036">
 <path d="M 1458 459 L 1458 493" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110002043_1" Pin1InfoVect0LinkObjId="30000683_0" Plane="0"/>
  </metadata>
 <path d="M 1458 459 L 1458 493" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002037">
 <path d="M 1458 396 L 1458 327" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110002043_0" Pin1InfoVect0LinkObjId="101002042_1" Plane="0"/>
  </metadata>
 <path d="M 1458 396 L 1458 327" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002038">
 <path d="M 1458 280 L 1499 280" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002040_0" Pin0InfoVect1LinkObjId="34002049_0" Pin0InfoVect2LinkObjId="34002039_1" Pin1InfoVect0LinkObjId="133002045_0" Plane="0"/>
  </metadata>
 <path d="M 1458 280 L 1499 280" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002039">
 <path d="M 1458 301 L 1458 280" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101002042_0" Pin1InfoVect0LinkObjId="34002040_0" Pin1InfoVect1LinkObjId="34002049_0" Pin1InfoVect2LinkObjId="34002038_0" Plane="0"/>
  </metadata>
 <path d="M 1458 301 L 1458 280" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002040">
 <path d="M 1458 280 L 1458 261" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002038_0" Pin0InfoVect1LinkObjId="34002039_1" Pin0InfoVect2LinkObjId="34002049_0" Pin1InfoVect0LinkObjId="36002041_0" Plane="0"/>
  </metadata>
 <path d="M 1458 280 L 1458 261" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002049">
 <path d="M 1458 280 L 1388 280" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002040_0" Pin0InfoVect1LinkObjId="34002038_0" Pin0InfoVect2LinkObjId="34002039_1" Pin1InfoVect0LinkObjId="131002048_1" Plane="0"/>
  </metadata>
 <path d="M 1458 280 L 1388 280" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002050">
 <path d="M 1362 280 L 1330 280" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="131002048_0" Plane="0"/>
  </metadata>
 <path d="M 1362 280 L 1330 280" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002053">
 <path d="M 1693 493 L 1693 441" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="30000683_0" Pin1InfoVect0LinkObjId="110002056_1" Plane="0"/>
  </metadata>
 <path d="M 1693 493 L 1693 441" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002054">
 <path d="M 1693 378 L 1693 362" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110002056_0" Pin1InfoVect0LinkObjId="107002055_0" Plane="0"/>
  </metadata>
 <path d="M 1693 378 L 1693 362" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002060">
 <path d="M 545 493 L 545 517" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="30000089_0" Pin1InfoVect0LinkObjId="110002059_0" Plane="0"/>
  </metadata>
 <path d="M 545 493 L 545 517" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002062">
 <path d="M 545 604 L 527 604" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002069_1" Pin0InfoVect1LinkObjId="34002064_0" Pin0InfoVect2LinkObjId="34002063_1" Pin1InfoVect0LinkObjId="111002065_0" Plane="0"/>
  </metadata>
 <path d="M 545 604 L 527 604" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002063">
 <path d="M 545 580 L 545 604" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110002059_1" Pin1InfoVect0LinkObjId="34002069_1" Pin1InfoVect1LinkObjId="34002064_0" Pin1InfoVect2LinkObjId="34002062_0" Plane="0"/>
  </metadata>
 <path d="M 545 580 L 545 604" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002064">
 <path d="M 545 604 L 545 700" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002069_1" Pin0InfoVect1LinkObjId="34002062_0" Pin0InfoVect2LinkObjId="34002063_1" Plane="0"/>
  </metadata>
 <path d="M 545 604 L 545 700" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002069">
 <path d="M 569 604 L 545 604" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133002068_0" Pin1InfoVect0LinkObjId="34002062_0" Pin1InfoVect1LinkObjId="34002063_1" Pin1InfoVect2LinkObjId="34002064_0" Plane="0"/>
  </metadata>
 <path d="M 569 604 L 545 604" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002088">
 <path d="M 520 772 L 545 772" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111002087_0" Pin1InfoVect0LinkObjId="34002089_1" Pin1InfoVect1LinkObjId="34002090_0" Plane="0"/>
  </metadata>
 <path d="M 520 772 L 545 772" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002089">
 <path d="M 545 732 L 545 772" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin1InfoVect0LinkObjId="34002088_1" Pin1InfoVect1LinkObjId="34002090_0" Plane="0"/>
  </metadata>
 <path d="M 545 732 L 545 772" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002090">
 <path d="M 545 772 L 545 812" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002088_1" Pin0InfoVect1LinkObjId="34002089_1" Pin1InfoVect0LinkObjId="113002071_0" Plane="0"/>
  </metadata>
 <path d="M 545 772 L 545 812" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002139">
 <path d="M 645 604 L 627 604" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002140_1" Pin0InfoVect1LinkObjId="34002141_0" Pin1InfoVect0LinkObjId="111002147_0" Plane="0"/>
  </metadata>
 <path d="M 645 604 L 627 604" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002140">
 <path d="M 645 580 L 645 604" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110002146_1" Pin1InfoVect0LinkObjId="34002139_0" Pin1InfoVect1LinkObjId="34002141_0" Plane="0"/>
  </metadata>
 <path d="M 645 580 L 645 604" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002141">
 <path d="M 645 604 L 645 788" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002139_0" Pin0InfoVect1LinkObjId="34002140_1" Pin1InfoVect0LinkObjId="36002154_0" Plane="0"/>
  </metadata>
 <path d="M 645 604 L 645 788" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002153">
 <path d="M 645 517 L 645 493" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110002146_0" Pin1InfoVect0LinkObjId="30000089_0" Plane="0"/>
  </metadata>
 <path d="M 645 517 L 645 493" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002165">
 <path d="M 745 604 L 727 604" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002223_0" Pin0InfoVect1LinkObjId="34002166_1" Pin1InfoVect0LinkObjId="111002171_0" Plane="0"/>
  </metadata>
 <path d="M 745 604 L 727 604" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002166">
 <path d="M 745 580 L 745 604" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110002170_1" Pin1InfoVect0LinkObjId="34002223_0" Pin1InfoVect1LinkObjId="34002165_0" Plane="0"/>
  </metadata>
 <path d="M 745 580 L 745 604" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002168">
 <path d="M 745 517 L 745 493" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110002170_0" Pin1InfoVect0LinkObjId="30000089_0" Plane="0"/>
  </metadata>
 <path d="M 745 517 L 745 493" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002182">
 <path d="M 845 517 L 845 493" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110002184_0" Pin1InfoVect0LinkObjId="30000089_0" Plane="0"/>
  </metadata>
 <path d="M 845 517 L 845 493" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002181">
 <path d="M 845 604 L 845 788" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002179_0" Pin0InfoVect1LinkObjId="34002180_1" Pin1InfoVect0LinkObjId="36002183_0" Plane="0"/>
  </metadata>
 <path d="M 845 604 L 845 788" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002180">
 <path d="M 845 580 L 845 604" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110002184_1" Pin1InfoVect0LinkObjId="34002181_0" Pin1InfoVect1LinkObjId="34002179_0" Plane="0"/>
  </metadata>
 <path d="M 845 580 L 845 604" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002179">
 <path d="M 845 604 L 827 604" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002181_0" Pin0InfoVect1LinkObjId="34002180_1" Pin1InfoVect0LinkObjId="111002185_0" Plane="0"/>
  </metadata>
 <path d="M 845 604 L 827 604" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002193">
 <path d="M 995 604 L 977 604" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002240_0" Pin0InfoVect1LinkObjId="34002194_1" Pin1InfoVect0LinkObjId="111002199_0" Plane="0"/>
  </metadata>
 <path d="M 995 604 L 977 604" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002194">
 <path d="M 995 580 L 995 604" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110002198_1" Pin1InfoVect0LinkObjId="34002240_0" Pin1InfoVect1LinkObjId="34002193_0" Plane="0"/>
  </metadata>
 <path d="M 995 580 L 995 604" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002196">
 <path d="M 995 517 L 995 493" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110002198_0" Pin1InfoVect0LinkObjId="30000089_0" Plane="0"/>
  </metadata>
 <path d="M 995 517 L 995 493" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002210">
 <path d="M 1076 517 L 1076 493" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110002212_0" Pin1InfoVect0LinkObjId="30000089_0" Plane="0"/>
  </metadata>
 <path d="M 1076 517 L 1076 493" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002208">
 <path d="M 1076 580 L 1076 604" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110002212_1" Pin1InfoVect0LinkObjId="34002237_0" Pin1InfoVect1LinkObjId="34002207_0" Plane="0"/>
  </metadata>
 <path d="M 1076 580 L 1076 604" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002207">
 <path d="M 1076 604 L 1058 604" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002237_0" Pin0InfoVect1LinkObjId="34002208_1" Pin1InfoVect0LinkObjId="111002213_0" Plane="0"/>
  </metadata>
 <path d="M 1076 604 L 1058 604" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002223">
 <path d="M 745 604 L 745 712" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002165_0" Pin0InfoVect1LinkObjId="34002166_1" Pin1InfoVect0LinkObjId="101002220_0" Plane="0"/>
  </metadata>
 <path d="M 745 604 L 745 712" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002225">
 <path d="M 745 755 L 780 755 L 780 777" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002226_1" Pin0InfoVect1LinkObjId="34002227_0" Pin1InfoVect0LinkObjId="133002228_0" Plane="0"/>
  </metadata>
 <path d="M 745 755 L 780 755 L 780 777" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002226">
 <path d="M 745 735 L 745 755" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101002220_1" Pin1InfoVect0LinkObjId="34002225_0" Pin1InfoVect1LinkObjId="34002227_0" Plane="0"/>
  </metadata>
 <path d="M 745 735 L 745 755" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002227">
 <path d="M 745 755 L 745 788" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002225_0" Pin0InfoVect1LinkObjId="34002226_1" Pin1InfoVect0LinkObjId="36002169_0" Plane="0"/>
  </metadata>
 <path d="M 745 755 L 745 788" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002232">
 <path d="M 995 751 L 995 788" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002241_1" Pin0InfoVect1LinkObjId="34002233_1" Pin1InfoVect0LinkObjId="36002197_0" Plane="0"/>
  </metadata>
 <path d="M 995 751 L 995 788" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002233">
 <path d="M 1032 774 L 1032 751 L 995 751" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="133002229_0" Pin1InfoVect0LinkObjId="34002241_1" Pin1InfoVect1LinkObjId="34002232_0" Plane="0"/>
  </metadata>
 <path d="M 1032 774 L 1032 751 L 995 751" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002236">
 <path d="M 1076 752 L 1112 752 L 1112 774" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002237_1" Pin0InfoVect1LinkObjId="34002238_0" Pin1InfoVect0LinkObjId="133002235_0" Plane="0"/>
  </metadata>
 <path d="M 1076 752 L 1112 752 L 1112 774" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002237">
 <path d="M 1076 604 L 1076 752" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002207_0" Pin0InfoVect1LinkObjId="34002208_1" Pin1InfoVect0LinkObjId="34002236_0" Pin1InfoVect1LinkObjId="34002238_0" Plane="0"/>
  </metadata>
 <path d="M 1076 604 L 1076 752" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002238">
 <path d="M 1076 752 L 1076 788" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002237_1" Pin0InfoVect1LinkObjId="34002236_0" Pin1InfoVect0LinkObjId="36002211_0" Plane="0"/>
  </metadata>
 <path d="M 1076 752 L 1076 788" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002240">
 <path d="M 995 604 L 995 703" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002193_0" Pin0InfoVect1LinkObjId="34002194_1" Pin1InfoVect0LinkObjId="34002241_0" Pin1InfoVect1LinkObjId="34002245_0" Plane="0"/>
  </metadata>
 <path d="M 995 604 L 995 703" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002241">
 <path d="M 995 703 L 995 751" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002240_1" Pin0InfoVect1LinkObjId="34002245_0" Pin1InfoVect0LinkObjId="34002232_0" Pin1InfoVect1LinkObjId="34002233_1" Plane="0"/>
  </metadata>
 <path d="M 995 703 L 995 751" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002245">
 <path d="M 995 703 L 916 703 L 916 714" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002240_1" Pin0InfoVect1LinkObjId="34002241_0" Pin1InfoVect0LinkObjId="131002243_0" Plane="0"/>
  </metadata>
 <path d="M 995 703 L 916 703 L 916 714" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002246">
 <path d="M 916 739 L 916 755" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="131002243_1" Pin1InfoVect0LinkObjId="102002247_0" Plane="0"/>
  </metadata>
 <path d="M 916 739 L 916 755" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002259">
 <path d="M 1150 493 L 1150 520" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="30000089_0" Pin1InfoVect0LinkObjId="110002256_0" Plane="0"/>
  </metadata>
 <path d="M 1150 493 L 1150 520" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002263">
 <path d="M 1150 567 L 1150 583 L 1150 623 L 1360 623 L 1360 580" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110002256_1" Pin1InfoVect0LinkObjId="110002262_1" Plane="0"/>
  </metadata>
 <path d="M 1150 567 L 1150 583 L 1150 623 L 1360 623 L 1360 580" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002264">
 <path d="M 1360 517 L 1360 493" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110002262_0" Pin1InfoVect0LinkObjId="30000683_0" Plane="0"/>
  </metadata>
 <path d="M 1360 517 L 1360 493" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002272">
 <path d="M 1477 604 L 1459 604" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002275_0" Pin0InfoVect1LinkObjId="34002273_1" Pin1InfoVect0LinkObjId="111002282_0" Plane="0"/>
  </metadata>
 <path d="M 1477 604 L 1459 604" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002273">
 <path d="M 1477 580 L 1477 604" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110002281_1" Pin1InfoVect0LinkObjId="34002275_0" Pin1InfoVect1LinkObjId="34002272_0" Plane="0"/>
  </metadata>
 <path d="M 1477 580 L 1477 604" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002274">
 <path d="M 1477 517 L 1477 493" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110002281_0" Pin1InfoVect0LinkObjId="30000683_0" Plane="0"/>
  </metadata>
 <path d="M 1477 517 L 1477 493" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002275">
 <path d="M 1477 604 L 1477 712" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002272_0" Pin0InfoVect1LinkObjId="34002273_1" Pin1InfoVect0LinkObjId="101002280_0" Plane="0"/>
  </metadata>
 <path d="M 1477 604 L 1477 712" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002276">
 <path d="M 1477 755 L 1512 755 L 1512 777" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002277_1" Pin0InfoVect1LinkObjId="34002278_0" Pin1InfoVect0LinkObjId="133002284_0" Plane="0"/>
  </metadata>
 <path d="M 1477 755 L 1512 755 L 1512 777" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002277">
 <path d="M 1477 735 L 1477 755" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101002280_1" Pin1InfoVect0LinkObjId="34002276_0" Pin1InfoVect1LinkObjId="34002278_0" Plane="0"/>
  </metadata>
 <path d="M 1477 735 L 1477 755" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002278">
 <path d="M 1477 755 L 1477 788" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002276_0" Pin0InfoVect1LinkObjId="34002277_1" Pin1InfoVect0LinkObjId="36002279_0" Plane="0"/>
  </metadata>
 <path d="M 1477 755 L 1477 788" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002292">
 <path d="M 1577 604 L 1559 604" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002293_1" Pin0InfoVect1LinkObjId="34002295_0" Pin1InfoVect0LinkObjId="111002302_0" Plane="0"/>
  </metadata>
 <path d="M 1577 604 L 1559 604" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002293">
 <path d="M 1577 580 L 1577 604" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110002301_1" Pin1InfoVect0LinkObjId="34002292_0" Pin1InfoVect1LinkObjId="34002295_0" Plane="0"/>
  </metadata>
 <path d="M 1577 580 L 1577 604" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002294">
 <path d="M 1577 517 L 1577 493" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110002301_0" Pin1InfoVect0LinkObjId="30000683_0" Plane="0"/>
  </metadata>
 <path d="M 1577 517 L 1577 493" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002295">
 <path d="M 1577 604 L 1577 712" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002292_0" Pin0InfoVect1LinkObjId="34002293_1" Pin1InfoVect0LinkObjId="101002300_0" Plane="0"/>
  </metadata>
 <path d="M 1577 604 L 1577 712" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002296">
 <path d="M 1577 755 L 1612 755 L 1612 777" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002297_1" Pin0InfoVect1LinkObjId="34002298_0" Pin1InfoVect0LinkObjId="133002304_0" Plane="0"/>
  </metadata>
 <path d="M 1577 755 L 1612 755 L 1612 777" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002297">
 <path d="M 1577 735 L 1577 755" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101002300_1" Pin1InfoVect0LinkObjId="34002296_0" Pin1InfoVect1LinkObjId="34002298_0" Plane="0"/>
  </metadata>
 <path d="M 1577 735 L 1577 755" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002298">
 <path d="M 1577 755 L 1577 788" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002296_0" Pin0InfoVect1LinkObjId="34002297_1" Pin1InfoVect0LinkObjId="36002299_0" Plane="0"/>
  </metadata>
 <path d="M 1577 755 L 1577 788" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002318">
 <path d="M 1677 755 L 1677 788" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002316_0" Pin0InfoVect1LinkObjId="34002317_1" Pin1InfoVect0LinkObjId="36002319_0" Plane="0"/>
  </metadata>
 <path d="M 1677 755 L 1677 788" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002317">
 <path d="M 1677 735 L 1677 755" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="101002320_1" Pin1InfoVect0LinkObjId="34002318_0" Pin1InfoVect1LinkObjId="34002316_0" Plane="0"/>
  </metadata>
 <path d="M 1677 735 L 1677 755" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002316">
 <path d="M 1677 755 L 1712 755 L 1712 777" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002318_0" Pin0InfoVect1LinkObjId="34002317_1" Pin1InfoVect0LinkObjId="133002324_0" Plane="0"/>
  </metadata>
 <path d="M 1677 755 L 1712 755 L 1712 777" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002315">
 <path d="M 1677 604 L 1677 712" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002312_0" Pin0InfoVect1LinkObjId="34002313_1" Pin1InfoVect0LinkObjId="101002320_0" Plane="0"/>
  </metadata>
 <path d="M 1677 604 L 1677 712" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002314">
 <path d="M 1677 517 L 1677 493" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110002321_0" Pin1InfoVect0LinkObjId="30000683_0" Plane="0"/>
  </metadata>
 <path d="M 1677 517 L 1677 493" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002313">
 <path d="M 1677 580 L 1677 604" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110002321_1" Pin1InfoVect0LinkObjId="34002312_0" Pin1InfoVect1LinkObjId="34002315_0" Plane="0"/>
  </metadata>
 <path d="M 1677 580 L 1677 604" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002312">
 <path d="M 1677 604 L 1659 604" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002313_1" Pin0InfoVect1LinkObjId="34002315_0" Pin1InfoVect0LinkObjId="111002322_0" Plane="0"/>
  </metadata>
 <path d="M 1677 604 L 1659 604" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002331">
 <path d="M 1818 493 L 1818 519" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="30000683_0" Pin1InfoVect0LinkObjId="110002339_0" Plane="0"/>
  </metadata>
 <path d="M 1818 493 L 1818 519" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002333">
 <path d="M 1818 582 L 1818 606" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="110002339_1" Pin1InfoVect0LinkObjId="34002334_0" Pin1InfoVect1LinkObjId="34002346_1" Pin1InfoVect2LinkObjId="34002347_0" Plane="0"/>
  </metadata>
 <path d="M 1818 582 L 1818 606" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002334">
 <path d="M 1818 606 L 1818 702" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002333_1" Pin0InfoVect1LinkObjId="34002346_1" Pin0InfoVect2LinkObjId="34002347_0" Plane="0"/>
  </metadata>
 <path d="M 1818 606 L 1818 702" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002336">
 <path d="M 1793 774 L 1818 774" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111002341_0" Pin1InfoVect0LinkObjId="34002337_1" Pin1InfoVect1LinkObjId="34002338_0" Plane="0"/>
  </metadata>
 <path d="M 1793 774 L 1818 774" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002337">
 <path d="M 1818 734 L 1818 774" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin1InfoVect0LinkObjId="34002336_1" Pin1InfoVect1LinkObjId="34002338_0" Plane="0"/>
  </metadata>
 <path d="M 1818 734 L 1818 774" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002338">
 <path d="M 1818 774 L 1818 814" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002336_1" Pin0InfoVect1LinkObjId="34002337_1" Pin1InfoVect0LinkObjId="113002343_0" Plane="0"/>
  </metadata>
 <path d="M 1818 774 L 1818 814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002346">
 <path d="M 1800 606 L 1818 606" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="111002340_0" Pin1InfoVect0LinkObjId="34002333_1" Pin1InfoVect1LinkObjId="34002334_0" Pin1InfoVect2LinkObjId="34002347_0" Plane="0"/>
  </metadata>
 <path d="M 1800 606 L 1818 606" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 <g id="34002347">
 <path d="M 1818 606 L 1843 606" stroke="rgb(128,128,128)" stroke-width="2"/>
  <metadata>
   <cge:PSR_Link AFMask="39039" MaxPinNum="2" Pin0InfoVect0LinkObjId="34002333_1" Pin0InfoVect1LinkObjId="34002334_0" Pin0InfoVect2LinkObjId="34002346_1" Pin1InfoVect0LinkObjId="133002345_0" Plane="0"/>
  </metadata>
 <path d="M 1818 606 L 1843 606" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
</g>
<g id="MeasurementValue_Layer">
 <g id="33000013">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,0)" writing-mode="lr" x="212" xml:space="preserve" y="261">-0.0</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000014">
 <text AFMask="34943" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,0)" writing-mode="lr" x="212" xml:space="preserve" y="302">-0.0</text>
  <metadata>
   <cge:Meas_Ref AFMask="34943" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000684">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1356" xml:space="preserve" y="458">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000685">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1356" xml:space="preserve" y="478">-00000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000090">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="442" xml:space="preserve" y="465">-00.00</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33000091">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="442" xml:space="preserve" y="485">-00000</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001984">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,0,0)" writing-mode="lr" x="633" xml:space="preserve" y="176">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001985">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(0,255,127)" writing-mode="lr" x="633" xml:space="preserve" y="196">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001986">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="633" xml:space="preserve" y="216">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001987">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,0,0)" writing-mode="lr" x="633" xml:space="preserve" y="276">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001988">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(85,255,127)" writing-mode="lr" x="633" xml:space="preserve" y="296">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33001989">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="633" xml:space="preserve" y="316">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002013">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,0,0)" writing-mode="lr" x="803" xml:space="preserve" y="192">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002014">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(0,255,127)" writing-mode="lr" x="803" xml:space="preserve" y="212">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002016">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="803" xml:space="preserve" y="232">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002033">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,0,0)" writing-mode="lr" x="1438" xml:space="preserve" y="190">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002034">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(0,255,127)" writing-mode="lr" x="1438" xml:space="preserve" y="210">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002035">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1438" xml:space="preserve" y="230">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002073">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(0,255,127)" writing-mode="lr" x="520" xml:space="preserve" y="893">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002075">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="520" xml:space="preserve" y="913">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002155">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,0,0)" writing-mode="lr" x="625" xml:space="preserve" y="889">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002156">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(0,255,127)" writing-mode="lr" x="625" xml:space="preserve" y="909">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002158">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="625" xml:space="preserve" y="929">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002162">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,0,0)" writing-mode="lr" x="725" xml:space="preserve" y="889">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002163">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(0,255,127)" writing-mode="lr" x="725" xml:space="preserve" y="909">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002164">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="725" xml:space="preserve" y="929">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002176">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,0,0)" writing-mode="lr" x="825" xml:space="preserve" y="889">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002177">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(0,255,127)" writing-mode="lr" x="825" xml:space="preserve" y="909">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002178">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="825" xml:space="preserve" y="929">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002190">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,0,0)" writing-mode="lr" x="975" xml:space="preserve" y="889">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002191">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(0,255,127)" writing-mode="lr" x="975" xml:space="preserve" y="909">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002192">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="975" xml:space="preserve" y="929">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002204">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,0,0)" writing-mode="lr" x="1056" xml:space="preserve" y="889">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002205">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(0,255,127)" writing-mode="lr" x="1056" xml:space="preserve" y="909">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002206">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1056" xml:space="preserve" y="929">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002249">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,0,0)" writing-mode="lr" x="865" xml:space="preserve" y="669">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002250">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(0,255,127)" writing-mode="lr" x="865" xml:space="preserve" y="689">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002251">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="865" xml:space="preserve" y="709">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002252">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,0,0)" writing-mode="lr" x="883" xml:space="preserve" y="830">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002253">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(85,255,127)" writing-mode="lr" x="883" xml:space="preserve" y="850">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002254">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="883" xml:space="preserve" y="870">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002269">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,0,0)" writing-mode="lr" x="1457" xml:space="preserve" y="889">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002270">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(0,255,127)" writing-mode="lr" x="1457" xml:space="preserve" y="909">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002271">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1457" xml:space="preserve" y="929">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002289">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,0,0)" writing-mode="lr" x="1557" xml:space="preserve" y="889">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002290">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(0,255,127)" writing-mode="lr" x="1557" xml:space="preserve" y="909">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002291">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1557" xml:space="preserve" y="929">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002309">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,0,0)" writing-mode="lr" x="1657" xml:space="preserve" y="889">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002310">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(0,255,127)" writing-mode="lr" x="1657" xml:space="preserve" y="909">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002311">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1657" xml:space="preserve" y="929">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002329">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(0,255,127)" writing-mode="lr" x="1793" xml:space="preserve" y="895">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
 <g id="33002330">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,0)" writing-mode="lr" x="1793" xml:space="preserve" y="915">******</text>
  <metadata>
   <cge:Meas_Ref AFMask="39039" ObjectID="-1" ObjectName="" Plane="0"/>
  </metadata>
 </g>
</g>
<g id="Text_Layer">
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="24" font-size="24" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="62" xml:space="preserve" y="263">总负荷：P=</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="281" xml:space="preserve" y="261">MW</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="18" font-size="18" font-width="18" stroke="rgb(255,255,254)" writing-mode="lr" x="274" xml:space="preserve" y="302">MVar</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="47" font-size="31" font-width="31" stroke="rgb(0,0,0)" writing-mode="lr" x="107" xml:space="preserve" y="100">10kV塔乡开闭所</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="24" font-size="24" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="62" xml:space="preserve" y="366">事故总:</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="24" font-size="24" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="62" xml:space="preserve" y="479">危险点说明：</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="24" font-size="24" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="62" xml:space="preserve" y="710">联系方式：</text>
 <text AFMask="34943" Plane="0" fill="rgb(255,255,254)" font-family="SimSun" font-height="24" font-size="24" font-width="24" stroke="rgb(255,255,254)" writing-mode="lr" x="157" xml:space="preserve" y="305">Q=</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1309" xml:space="preserve" y="476">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="510" xml:space="preserve" y="481">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="616" xml:space="preserve" y="441">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="838" xml:space="preserve" y="439">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="836" xml:space="preserve" y="331">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(230,232,254)" writing-mode="lr" x="803" xml:space="preserve" y="172">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1045" xml:space="preserve" y="260">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1065" xml:space="preserve" y="421">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1473" xml:space="preserve" y="437">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1471" xml:space="preserve" y="329">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(230,232,254)" writing-mode="lr" x="1438" xml:space="preserve" y="170">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1688" xml:space="preserve" y="258">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1708" xml:space="preserve" y="419">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="561" xml:space="preserve" y="560">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="499" xml:space="preserve" y="634">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="520" xml:space="preserve" y="942">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="492" xml:space="preserve" y="802">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="661" xml:space="preserve" y="560">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="599" xml:space="preserve" y="634">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(230,232,254)" writing-mode="lr" x="625" xml:space="preserve" y="946">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="761" xml:space="preserve" y="560">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="699" xml:space="preserve" y="634">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(230,232,254)" writing-mode="lr" x="725" xml:space="preserve" y="946">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(230,232,254)" writing-mode="lr" x="825" xml:space="preserve" y="946">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="799" xml:space="preserve" y="634">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="861" xml:space="preserve" y="560">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1011" xml:space="preserve" y="560">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="949" xml:space="preserve" y="634">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(230,232,254)" writing-mode="lr" x="975" xml:space="preserve" y="946">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(230,232,254)" writing-mode="lr" x="1056" xml:space="preserve" y="946">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1030" xml:space="preserve" y="634">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1092" xml:space="preserve" y="560">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="755" xml:space="preserve" y="734">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1166" xml:space="preserve" y="556">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1375" xml:space="preserve" y="557">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1493" xml:space="preserve" y="560">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1431" xml:space="preserve" y="634">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(230,232,254)" writing-mode="lr" x="1457" xml:space="preserve" y="946">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1487" xml:space="preserve" y="734">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1593" xml:space="preserve" y="560">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1531" xml:space="preserve" y="634">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(230,232,254)" writing-mode="lr" x="1557" xml:space="preserve" y="946">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1587" xml:space="preserve" y="734">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1687" xml:space="preserve" y="734">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(230,232,254)" writing-mode="lr" x="1657" xml:space="preserve" y="946">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1631" xml:space="preserve" y="634">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1693" xml:space="preserve" y="560">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1834" xml:space="preserve" y="562">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1772" xml:space="preserve" y="636">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1793" xml:space="preserve" y="944">???</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,254)" writing-mode="lr" x="1765" xml:space="preserve" y="804">???</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href="勐腊地区.sys.svg"><rect fill-opacity="0" height="93" stroke-opacity="0" stroke-width="2" width="311" x="20" y="34"/></g>
</g>
</svg>