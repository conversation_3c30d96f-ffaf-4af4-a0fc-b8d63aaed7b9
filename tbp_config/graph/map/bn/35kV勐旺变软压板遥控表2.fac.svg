<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="sys" height="1380" id="thSvg" viewBox="0 0 2820 1380" width="2820">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(170,170,255);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,84,127);fill:none}
.kv115kV{stroke:rgb(170,170,255);fill:none}
.kv11kV{stroke:rgb(68,129,155);fill:none}
.kv220kV{stroke:rgb(255,255,254);fill:none}
.kv22kV{stroke:rgb(255,255,127);fill:none}
.kv230kV{stroke:rgb(212,68,255);fill:none}
.kv330kV{stroke:rgb(30,146,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(119,255,240);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="版纳_35kV_勐旺变" InitShowingPlane="0," fill="rgb(0,0,0)" height="1380" width="2820" x="0" y="0"/>
</g>
<g id="Other_Layer">
 
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="77" x2="1367" y1="221" y2="221"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="76" x2="76" y1="156" y2="1311"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="409" x2="1367" y1="709" y2="709"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="410" x2="1369" y1="1136" y2="1136"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="77" x2="1367" y1="1075" y2="1075"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="411" x2="1369" y1="1014" y2="1014"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="411" x2="1367" y1="953" y2="953"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="411" x2="1367" y1="892" y2="892"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="410" x2="1369" y1="831" y2="831"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="412" x2="1363" y1="770" y2="770"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="77" x2="1367" y1="648" y2="648"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="410" x2="1365" y1="587" y2="587"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="411" x2="1367" y1="526" y2="526"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="411" x2="1365" y1="465" y2="465"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="411" x2="1367" y1="404" y2="404"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="410" x2="1369" y1="343" y2="343"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1229" x2="1229" y1="157" y2="1311"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1370" x2="1370" y1="156" y2="1313"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="410" x2="410" y1="156" y2="1309"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="412" x2="1369" y1="282" y2="282"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="409" x2="1370" y1="1196" y2="1196"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="78" x2="1369" y1="157" y2="157"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1433" x2="2713" y1="157" y2="157"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1767" x2="2711" y1="1196" y2="1196"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1768" x2="2711" y1="282" y2="282"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1766" x2="1766" y1="156" y2="1256"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="2714" x2="2714" y1="157" y2="1256"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="2573" x2="2573" y1="157" y2="1256"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1769" x2="2713" y1="343" y2="343"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1433" x2="2713" y1="404" y2="404"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1767" x2="2711" y1="465" y2="465"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1766" x2="2715" y1="526" y2="526"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1766" x2="2711" y1="587" y2="587"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1766" x2="2713" y1="648" y2="648"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1766" x2="2711" y1="770" y2="770"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1434" x2="2711" y1="831" y2="831"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1767" x2="2711" y1="892" y2="892"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1767" x2="2711" y1="953" y2="953"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1767" x2="2711" y1="1014" y2="1014"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1766" x2="2711" y1="1075" y2="1075"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1766" x2="2711" y1="1136" y2="1136"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1767" x2="2711" y1="709" y2="709"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1432" x2="1432" y1="157" y2="1255"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1433" x2="2711" y1="221" y2="221"/>
 
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="409" x2="1369" y1="1250" y2="1250"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="77" x2="1369" y1="1311" y2="1311"/>
 <line AFMask="39039" Plane="0" fill="none" stroke="rgb(255,255,254)" stroke-width="1" x1="1431" x2="2713" y1="1258" y2="1258"/>
</g>
<g id="Text_Layer">
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="49" font-size="37" font-width="37" stroke="rgb(255,255,254)" writing-mode="lr" x="1247" xml:space="preserve" y="212">软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="49" font-size="37" font-width="37" stroke="rgb(255,255,254)" writing-mode="lr" x="808" xml:space="preserve" y="214">名称</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="49" font-size="37" font-width="37" stroke="rgb(255,255,254)" writing-mode="lr" x="186" xml:space="preserve" y="214">线路</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="49" font-size="37" font-width="37" stroke="rgb(255,255,254)" writing-mode="lr" x="1542" xml:space="preserve" y="214">线路</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="49" font-size="37" font-width="37" stroke="rgb(255,255,254)" writing-mode="lr" x="2114" xml:space="preserve" y="214">名称</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="49" font-size="37" font-width="37" stroke="rgb(255,255,254)" writing-mode="lr" x="2591" xml:space="preserve" y="212">软压板</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="56" font-size="34" font-width="34" stroke="rgb(85,255,255)" writing-mode="lr" x="1151" xml:space="preserve" y="71">35kV勐旺变继电保护远方操作</text>
 <text AFMask="39039" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="25" font-size="25" font-width="25" stroke="rgb(255,255,254)" writing-mode="lr" x="2598" xml:space="preserve" y="126">上一页</text>
</g>
<g id="Poke_Layer">
 <g ChangePicPlane="0," Plane="0" href="版纳_35kV_勐旺变.fac.svg"><rect fill-opacity="0" height="68" stroke-opacity="0" stroke-width="1" width="456" x="1149" y="10"/></g>
 <g ChangePicPlane="0," Plane="0" href="35kV勐旺变软压板遥控表.fac.svg"><rect fill-opacity="0" height="35" stroke-opacity="0" stroke-width="1" width="119" x="2570" y="98"/></g>
</g>
</svg>