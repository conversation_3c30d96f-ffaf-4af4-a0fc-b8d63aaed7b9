test.title=Beryl XML GUI Demo
test.file=File
test.file.wizard=Wizard test
test.file.wizard.accelerator=C+W
test.file.outlook=Outlook Bar test
test.file.outlook.accelerator=C+O
test.file.dnd=DnD test
test.file.dnd.accelerator=C+D
test.file.quit=Quit
test.file.quit.accelerator=A+Q
test.file.groovy=Groovy test
test.file.groovy.accelerator=C+G
test.help=Help
test.help.about=About
test.help.about.accelerator=C+A
test.messages=Messages
test.messages.info=Information
test.messages.info.accelerator=C+I
test.messages.warning=Warning
test.messages.warning.accelerator=C+A
test.messages.error=Error
test.messages.error.accelerator=C+E

test.groovy.title=Groovy test
test.groovy.text=This window has been created by a Groovy script

test.login=Login
test.login.mnemonic=l
test.login.tooltip=This is an example login dialog
test.login.username=Username :
test.login.username.mnemonic=u
test.login.password=Password :
test.login.password.mnemonic=p
test.login.ssl=Use SSL
test.login.verbose=Verbose

test.mvc=MVC
test.mvc.static=Static :
test.mvc.static.mnemonic=s
test.mvc.mvc1=MVC 1 :
test.mvc.mvc1.mnemonic=1
test.mvc.mvc2=MVC 2 :
test.mvc.mvc2.mnemonic=2

test.table=Table
test.table.first=First name
test.table.last=Last name
test.table.email=Email address
test.table.edit=Edit
test.table.edit.mnemonic=e

test.radio=Radio
test.radio.label=Make a choice :
test.radio.choice1=Choice 1
test.radio.choice2=Choice 2
test.radio.choice3=Choice 3

test.misc=Misc
test.misc.left=Left side
test.misc.right=Right side

test.icon=Icon
test.icon.widget=Widget
test.icon.java=Java
test.icon.document=Document
test.icon.binary=Binary
test.icon.image=Image
test.icon.shell=Shell script

test.console=Console
test.console.command=Command "
test.console.notfound=" not found

test.outlook.title=Outlook Bar test
test.outlook.bar1=Bar 1
test.outlook.bar2=Bar 2

test.about.title=About ..
test.about.line1=Beryl XML GUI
test.about.line2=(c) 2004 by Wenzel Jakob
test.about.close=Close
test.about.close.mnemonic=c

test.dnd.title=DnD
test.dnd.label=Use Drag'n'Drop to move items from the first to the second list

test.editor.title=Edit person ..
test.editor.first=First name :
test.editor.first.mnemonic=f
test.editor.last=Last name :
test.editor.last.mnemonic=l
test.editor.email=Email :
test.editor.email.mnemonic=e
test.editor.birthday=Birthday :
test.editor.birthday.mnemonic=b

test.wizard.title=Test Wizard ..
test.wizard.1.title=First page
test.wizard.1.desc=You need to enter a value here in order to go to the next page.
test.wizard.1.value=Test value :
test.wizard.1.value.mnemonic=t
test.wizard.2.title=Second page
test.wizard.2.desc=This page has no real content. Just presh 'finish' in order to complete the wizard.

test.message.info.title=Information message
test.message.info.message=This is an example information message

test.message.warning.title=Warning message
test.message.warning.message=This is an example warning message

test.checkbox=CheckBox