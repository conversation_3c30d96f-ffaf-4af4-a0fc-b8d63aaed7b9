test.title=Beryl XML GUI Demo
test.file=Datei
test.file.wizard=Wizard test
test.file.wizard.accelerator=C+W
test.file.outlook=Outlook Bar test
test.file.outlook.accelerator=C+O
test.file.dnd=DnD test
test.file.dnd.accelerator=C+D
test.file.quit=Beenden
test.file.quit.accelerator=A+B
test.file.groovy=Groovy test
test.file.groovy.accelerator=C+G
test.help=Hilfe
test.help.about=Über
test.help.about.accelerator=C+r
test.messages=Meldungen
test.messages.info=Information
test.messages.info.accelerator=C+I
test.messages.warning=Warnung
test.messages.warning.accelerator=C+A
test.messages.error=<PERSON>hler
test.messages.error.accelerator=C+F

test.groovy.title=Groovy test
test.groovy.text=Dieses Fen<PERSON> wurde von einem Groovy-Skript erzeugt

test.login=Login
test.login.mnemonic=l
test.login.tooltip=Dies ist ein Beispiel eines Login-Dialoges
test.login.username=<PERSON>utzername :
test.login.username.mnemonic=e
test.login.password=Password :
test.login.password.mnemonic=p
test.login.ssl=SSL verwenden
test.login.verbose=Verbose

test.mvc=MVC
test.mvc.static=Statisch :
test.mvc.static.mnemonic=s
test.mvc.mvc1=MVC 1 :
test.mvc.mvc1.mnemonic=1
test.mvc.mvc2=MVC 2 :
test.mvc.mvc2.mnemonic=2

test.table=Tabelle
test.table.first=Vorname
test.table.last=Nachname
test.table.email=Email-Adresse
test.table.edit=Editieren
test.table.edit.mnemonic=e

test.radio=Radio
test.radio.label=Auswahl :
test.radio.choice1=Möglichkeit 1
test.radio.choice2=Möglichkeit 2
test.radio.choice3=Möglichkeit 3

test.misc=Verschiedenes
test.misc.left=Linke Seite
test.misc.right=Rechte Seite

test.icon=Icon
test.icon.widget=Komponente
test.icon.java=Java
test.icon.document=Dokument
test.icon.binary=Binär
test.icon.image=Bild
test.icon.shell=Shell skript

test.console=Konsole
test.console.command=Befehl "
test.console.notfound=" nicht gefunden

test.outlook.title=Outlook Bar test
test.outlook.bar1=Bar 1
test.outlook.bar2=Bar 2

test.about.title=Über ..
test.about.line1=Beryl XML GUI
test.about.line2=(c) 2004 by Wenzel Jakob
test.about.close=Schlissen
test.about.close.mnemonic=s

test.dnd.title=DnD
test.dnd.label=Benutzen Sie Drag'n'Drop, um Elemente von der ersten in die zweite Liste zu bewegen

test.editor.title=Person editieren..
test.editor.first=Vorname :
test.editor.first.mnemonic=v
test.editor.last=Nachname :
test.editor.last.mnemonic=n
test.editor.email=Email :
test.editor.email.mnemonic=e
test.editor.birthday=Geburtstag :
test.editor.birthday.mnemonic=g

test.wizard.title=Test Wizard ..
test.wizard.1.title=Erste Seite
test.wizard.1.desc=Sie müssen hier einen Wert eingeben, um zur nächsten Seite gehen zu können.
test.wizard.1.value=Test-Wert :
test.wizard.1.value.mnemonic=t
test.wizard.2.title=Zweite Seite
test.wizard.2.desc=Diese Seite hat keinen echten Inhalt. Drücken sie "Fertig", um den Wizard abzuschlissen

test.message.info.title=Informations-Meldung
test.message.info.message=Dies ist ein Beispiel einer Informations-Meldung

test.message.warning.title=Warnungs-Meldung
test.message.warning.message=Dies ist ein Beispiel einer Warnungs-Meldung

test.checkbox=CheckBox