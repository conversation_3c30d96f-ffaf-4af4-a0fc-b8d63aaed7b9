<?xml version="1.0" encoding="iso-8859-1"?>

<xsl:stylesheet version="1.0"
				xmlns:xsl="http://www.w3.org/1999/XSL/Transform">
	<xsl:output method="html" encoding="ISO-8859-1" indent="yes"/>

	<xsl:param name="locale"/>

	<xsl:template match="/helpfile">
		<html>
			<xsl:apply-templates select="/helpfile/page[@locale=$locale]"/>
		</html>
	</xsl:template>
	
	<xsl:template match="/helpfile/page">
		<head>
			<title><xsl:value-of select="@title"/></title>
			<link rel="stylesheet" type="text/css" href="help.css" name="Style"/>
		</head>
		<body>
			<h1><xsl:value-of select="@title"/></h1>
			<xsl:apply-templates/>
		</body>
	</xsl:template>

	<xsl:template match="bullet">
		<table border="0" cellpadding="5" align="left">
			<tr valign="top">
				<td><img src="images/help_bullet.png"/></td>
				<td align="left">
					<xsl:apply-templates/>
				</td>
			</tr>
		</table>
	</xsl:template>

	<xsl:template match="figure">
		<p align="center">
			<img src="images/{@src}"/><br/>
			<xsl:apply-templates/>
		</p><br/>
	</xsl:template>
	
	<xsl:template match="link">
		<a>
			<xsl:attribute name="href"><xsl:value-of select="@page"/>.<xsl:value-of select="$locale"/>.html</xsl:attribute>
			<xsl:apply-templates/>
		</a>
	</xsl:template>
	
	<xsl:template match="see">
		<p>
			<img src="images/help_see.png"/>
			<b>
				<xsl:choose>
					<xsl:when test="$locale='en_US'">
						<xsl:text>See also :</xsl:text>
					</xsl:when>
					<xsl:when test="$locale='de_DE'">
						<xsl:text>Siehe auch :</xsl:text>
					</xsl:when>
				</xsl:choose>
			</b>
			<blockquote>
				<xsl:apply-templates/>
			</blockquote>
		</p>
	</xsl:template>
	
	<xsl:template match="warning|note">
		<table class="{name()}" align="center" cellpadding="1" cellspacing="0">
			<tr>
				<td>
					<table cellpadding="1" cellspacing="0">
						<tr class="label">
							<td colspan="4"><xsl:value-of select="@title"/></td>
						</tr>
						<tr class="content">
							<td width="3"></td>
							<img src="images/help_{name()}.png"/>
							<td width="3"></td>
							<td>
								<xsl:apply-templates/>
							</td>
							<td width="3"></td>
						</tr>
					</table>
				</td>
			</tr>
		</table>
	</xsl:template>

	<xsl:template match="node()|@*">
		<xsl:copy>
			<xsl:apply-templates select="node()|@*"/>
		</xsl:copy>
	</xsl:template>
</xsl:stylesheet>
