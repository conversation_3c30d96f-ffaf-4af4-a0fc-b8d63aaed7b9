xmlgui.wizard.back=< Zurück
xmlgui.wizard.next=Wei<PERSON> >
xmlgui.wizard.cancel=Abbrechen
xmlgui.wizard.finish=Fertig
xmlgui.button.ok=OK
xmlgui.button.ok.mnemonic=O
xmlgui.button.yes=Ja
xmlgui.button.yes.mnemonic=J
xmlgui.button.no=Nein
xmlgui.button.no.mnemonic=N
xmlgui.button.cancel=Abbrechen
xmlgui.button.cancel.mnemonic=A
xmlgui.messagedialog.exception.title=Interner Fehler
xmlgui.messagedialog.exception=Ein interner Fehler ist aufgetreten. Klicken sie auf "Mehr", um zusätzliche Informationen zu bekommen.
xmlgui.messagedialog.more=Mehr
xmlgui.messagedialog.less=Weniger
xmlgui.messagedialog.close=Schließen
xmlgui.messagedialog.close.mnemonic=s

xmlgui.validator.date.invalid=Ungültiges Datum
xmlgui.validator.number.invalid=Ungültige Nummer
xmlgui.validator.email.invalid=Ungültige Email-Adresse
xmlgui.validator.phone.invalid=Ungültige Telefonnummer
xmlgui.validator.character.invalid=Ungültiger Buchstabe
xmlgui.validator.empty=Darf nicht leer sein
xmlgui.validator.file.notset=Keine Datei angegeben
xmlgui.validator.file.existsnot=Datei existiert nicht
xmlgui.validator.file.notafile=Dateiname zeigt nicht zu einer Datei
xmlgui.validator.file.cantread=Von Datei kann nicht gelesen werden

xmlgui.Widget=Abstrakte Komponente
xmlgui.Widget.property.anchor=Der Anker im Layout der Vorgängerkomponente
xmlgui.Widget.property.background=Die Hintergrundfarbe
xmlgui.Widget.property.foreground=Die Vordergrundfarbe
xmlgui.Widget.property.font=Die Schriftart
xmlgui.Widget.property.opaque=Bestimmt, ob der Hintergrund einer Komponente gezeichnet werden soll
xmlgui.Widget.property.enabled=Bestimmt, ob die Komponente aktiv oder ausgegraut ist
xmlgui.Widget.property.helpid=Die JavaHelp ID
xmlgui.Button=Ein klickbarer Button
xmlgui.Button.property.text=Die Beschriftung des Buttons
xmlgui.Button.property.mnemonic=Das Buchstabenkürzel des Buttons
xmlgui.Button.property.icon=Das Icon des Buttons
xmlgui.Button.property.rolloverIcon=Das Maus roll-over Icon des Buttons
xmlgui.Button.property.default=Bestimmt, ob dieser Button der 'Haupt'-Button des Dialoges ist
xmlgui.Button.preset.ok=Ein OK Button
xmlgui.Button.preset.cancel=Ein Abbrechen Button
xmlgui.Button.preset.hover=Ein Button mit einem "roll"-Effekt
xmlgui.Button.event.clicked=Wird aufgerufen, wenn der button geklickt wird
xmlgui.ButtonGroup=Ein Panel, das RadioButton komponenten enthält
xmlgui.ButtonGroup.property.key=Der Datenmodell-Schlüssel der ButtonGroup
xmlgui.CheckBox=Eine CheckBox
xmlgui.CheckBox.property.key=Der Datenmodell-Schlüssel der CheckBox
xmlgui.CheckBox.property.text=Die Beschriftung der CheckBox
xmlgui.ComboBox=Eine ComboBox
xmlgui.ComboBox.property.valuekey=Der Index-Datenmodell-Schlüssel der ComboBox
xmlgui.ComboBox.property.indexkey=Der Wert-Datenmodell-Schlüssel der ComboBox
xmlgui.Console=Eine Kommandozeilen-Konsole
xmlgui.Console.event.command=Wird aufgerufen, wenn ein Befehl eingegeben wurde
xmlgui.Dialog=Ein Dialog, der immer im Vordergrund bleibt
xmlgui.Dialog.property.border=Der Rand des Dialoges
xmlgui.Dialog.property.defaultCloseOperation=Die auszuführende Aktion, wenn der Dialog geschlossen wird
xmlgui.Dialog.property.layout=Das Layout des Dialoges
xmlgui.Dialog.property.location=Die Position des Dialoges
xmlgui.Dialog.property.resizable=Bestimmt, ob der Dialog die Größe ändern kann
xmlgui.Dialog.property.size=Die Größe des Dialoges
xmlgui.Dialog.property.spacing=Der Rand-Abstand des Dialoges
xmlgui.Dialog.property.title=Der Titel des Dialoges
xmlgui.Dialog.event.close=Wird aufgerufen, wenn der Dialog geschlossen wird
xmlgui.Dialog.event.open=Wird vom Event-Loop des Dialoges aufgerufen, nachdem er erstellt wurde
xmlgui.Frame=Ein Fenster
xmlgui.Frame.property.border=Der Rand des Fensters
xmlgui.Frame.property.defaultCloseOperation=Die auszuführende Aktion, wenn das Fenster geschlossen wird
xmlgui.Frame.property.layout=Das Layout des Fensters
xmlgui.Frame.property.location=Die Position des Fensters
xmlgui.Frame.property.resizable=Bestimmt, ob das Fenster die Größe ändern kann
xmlgui.Frame.property.size=Die Fenstergrösse
xmlgui.Frame.property.spacing=Der Rand-Abstand des Fensters
xmlgui.Frame.property.title=Der Titel des Fensters
xmlgui.Frame.property.visible=Bestimmt, ob das Fenster sichtbar ist
xmlgui.Frame.property.iconImage=Das Windowmanager-Icon des Fensters
xmlgui.Frame.event.close=Wird aufgerufen, wenn das Fenster geschlossen wird
xmlgui.Group=Panel, welches seine Komponenten nach den LnF Design Guidelines ausrichtet
xmlgui.IconView=Eine Icon ansicht
xmlgui.IconView.property.indexkey=Der Index-Datenmodell-Schlüssel
xmlgui.IconView.property.valuekey=Der Wert-Datenmodell-Schlüssel
xmlgui.Item=Ein mehrzweck element (List, Combo, IconView)
xmlgui.Item.property.icon=Das Icon des Elementes
xmlgui.Item.property.text=Der Text des Elementes
xmlgui.Label=Ein Label, der Text, ein Icon oder beides anzeigen kann
xmlgui.Label.property.text=Die Beschriftung des Labels
xmlgui.Label.property.horizontalAlignment=Die horizontale Ausrichtung des Labels
xmlgui.Label.property.verticalAlignment=Die vertikale Ausrichtung des Labels
xmlgui.Label.property.icon=Das Icon dieses Labels
xmlgui.LabeledWidget=Platziert eine Beschriftung vor eine Komponente
xmlgui.LabeledWidget.property.label.text=Die Beschriftung
xmlgui.LabeledWidget.property.mnemonic=Das Buchstabenkürzel der Komponente
xmlgui.List=Eine Liste mit selektierbaren Einträgen
xmlgui.List.property.indexkey=Der Index-Datenmodell-Schlüssel
xmlgui.List.property.valuekey=Der Wert-Datenmodell-Schlüssel
xmlgui.List.property.selectionMode=Der Selektions-Modus (single/single_interval/multiple_interval)
xmlgui.List.property.verticalScrollBar=Bestimmt, ob die vertikale ScrollBar sichtbar sein soll
xmlgui.List.property.horizontalScrollBar=Bestimmt, ob die horizontale ScrollBar sichtbar sein soll
xmlgui.List.event.rightclick=Wird nach einem Rechtsklick aufgerufen
xmlgui.List.event.doubleclick=Wird nach einem Doppelklick aufgerufen
xmlgui.Menu=Ein Menü
xmlgui.Menu.property.mnemonic=Das Buchstabenkürzel des Menüs
xmlgui.Menu.property.text=Die Beschriftung des Menüs
xmlgui.MenuBar=Eine Menüleiste
xmlgui.MenuItem=Ein Menü-Eintrag
xmlgui.MenuItem.property.accelerator=Das Tastenkombination des Menü-Eintrages
xmlgui.MenuItem.property.mnemonic=Das Buchstabenkürzel des Menü-Eintrages
xmlgui.MenuItem.property.text=Die Beschriftung des Menü-Eintrages
xmlgui.MenuItem.event.selected=Wird aufgerufen, wenn der Menü-Eintrag selektiert wird
xmlgui.OutlookBar=Eine wohlbekannte Komponente aus Microsoft Outlook
xmlgui.OutlookPanel=Ein Panel, das in eine OutlookBar eingebettet werden kann
xmlgui.Panel=Ein leeres Panel
xmlgui.Panel.property.border=Der Rand des Panel
xmlgui.Panel.property.layout=Das Layout des Panel
xmlgui.PasswordField=Ein Textfeld, das seinen Inhalt versteckt
xmlgui.PopupMenu=Ein Popup-Menü
xmlgui.ProgressBar=Eine Fortschrittsanzeige
xmlgui.ProgressBar.property.indeterminate=Bestimmt, ob die ProgressBar einen "unbestimmbaren" Fortschritt anzeigt
xmlgui.ProgressBar.property.stringPainted=Bestimmt, ob die Prozent-Werte angezeigt werden
xmlgui.ProgressBar.property.minimum=Das Minimum der ProgressBar
xmlgui.ProgressBar.property.maximum=Das Maximum der ProgressBar
xmlgui.ProgressBar.property.value=Der Wert der ProgressBar
xmlgui.RadioButton=Ein RadioButton
xmlgui.RadioButton.property.text=Die Beschriftung dieses RadioButtons
xmlgui.RadioButton.property.text=Der Wert dieses RadioButtons (nützlich in Verbindung mit ButtonGroups)
xmlgui.Separator=Eine Mehrzweck-Trennlinie (Menu, ToolBar, PopupMenu, Group)
xmlgui.Spacer=Ein Platzhalter für Box Layouts und andere Verwendungen
xmlgui.Spacer.property.type=Die Platzhalter-Typ (strut/glue)
xmlgui.Spacer.property.axis=Die Platzhalter-Achse (h/v)
xmlgui.Spacer.property.size=Die Grösse des Platzhalters
xmlgui.SplitPane=Eine Komponente, die Kind-Komponenten mit einer beweglichen Trennlinie separiert
xmlgui.SplitPane.property.orientation=Die Orientierung (entweder 'h' oder 'v')
xmlgui.SplitPane.property.resizeWeight=Bestimmt, wie der verfügbare Platz verteilt wird (0.0 - 1.0)
xmlgui.TabbedPane=Ein Panel mit 'Tabs'
xmlgui.TabbedPane.property.closeicon=Fügt ein "Schliessen"-Icon zu allen Tabs hinzu
xmlgui.TabbedPane.event.close=Wird nach einem Klick auf ein "Schliessen"-Icon aufgerufen
xmlgui.TabbedPane.event.rightclick=Wird nach einem Rechtsklick auf ein Tab aufgerufen
xmlgui.Table=Eine vielseitige Tabellen-Komponente
xmlgui.Table.property.indexkey=Der Index-Datenmodell-Schlüssel
xmlgui.Table.property.valuekey=Der Wert-Datenmodell-Schlüssel
xmlgui.Table.property.verticalScrollBar=Bestimmt, ob die vertikale ScrollBar sichtbar sein soll
xmlgui.Table.property.horizontalScrollBar=Bestimmt, ob die horizontale ScrollBar sichtbar sein soll
xmlgui.Table.property.selectionMode=Der Selektions-Modus (single/single_interval/multiple_interval)
xmlgui.Table.event.doubleclick=Wird nach einem Doppelklick aufgerufen
xmlgui.Table.event.rightclick=Wird nach einem Rechtsklick aufgerufen
xmlgui.TextField=Ein Textfeld
xmlgui.TextField.property.key=Der Datenmodell-Schlüssel
xmlgui.TextField.event.activated=Wird aufgerufen, wenn der Benutzer die "Enter"-Taste drückt
xmlgui.TextPane=Ein Textbereich
xmlgui.TextPane.property.key=Der Datenmodell-Schlüssel
xmlgui.TextPane.property.verticalScrollBar=Bestimmt, ob die vertikale ScrollBar sichtbar sein soll
xmlgui.TextPane.property.horizontalScrollBar=Bestimmt, ob die horizontale ScrollBar sichtbar sein soll
xmlgui.ToolBar=Eine ToolBar, die Buttons aufnehmen kann
xmlgui.ToolBar.property.floatable=Bestimmt, ob die ToolBar sich aus dem Programm heraustrennen lässt
xmlgui.ToolBar.property.borderPainted=Bestimmt, ob der Rand um die ToolBar gezeichnet werden soll
xmlgui.ToolBar.property.rollover=Bestimmt, ob der Rand um die ToolBar Buttons einen 'roll'-Effekt haben soll
xmlgui.Tree=Eine Baum-Komponente
xmlgui.Tree.property.key=Der Datenmodell-Schlüssel
xmlgui.Tree.property.verticalScrollBar=Bestimmt, ob die vertikale ScrollBar sichtbar sein soll
xmlgui.Tree.property.horizontalScrollBar=Bestimmt, ob die horizontale ScrollBar sichtbar sein soll
xmlgui.Tree.event.doubleclick=Wird nach einem Doppelklick aufgerufen
xmlgui.Tree.event.rightclick=Wird nach einem Rechtsklick aufgerufen
xmlgui.SyntaxEditor=Ein Syntax Highlighting Editor
xmlgui.SyntaxEditor.property.verticalScrollBar=Definiert, ob die vertikale ScrollBar sichtbar sein soll
xmlgui.SyntaxEditor.property.horizontalScrollBar=Bestimmt, ob die horizontale ScrollBar sichtbar sein soll
xmlgui.SyntaxEditor.property.gutter=Bestimmt, ob die Zeilen/Struktur-Spalte angezeigt werden soll
xmlgui.SyntaxEditor.property.lineHighlight=Bestimmt, ob Zeilen-Hervorhebung aktiv sein soll
xmlgui.SyntaxEditor.property.bracketHighlight=Bestimmt, ob Klammern-Hervorhebung aktiv sein soll
xmlgui.SyntaxEditor.property.antiAlias=Bestimmt, ob die Schrift mit Anti Aliasing gezeichnet werden soll
xmlgui.SyntaxEditor.property.mode=Der Syntax-Modus der Komponente
xmlgui.SyntaxEditor.property.theme=Das Farbschema der Komponente
