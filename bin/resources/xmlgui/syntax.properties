# Default colors
default.comment1.fg=808080
default.comment2.fg=808080
default.comment3.fg=200080
default.comment3.font=italic
default.digit.fg=0000FF
default.literal1.fg=4010FF
default.literal2.fg=800080
default.keyword1.fg=4080FF
default.keyword2.fg=3870E0
default.keyword3.fg=3060C0
default.keyword4.fg=2850A0
default.keyword5.fg=204080
default.function.fg=3060C0
default.operator.fg=000000
default.markup.fg=404040
default.markup.font=italic
default.label.fg=6000FF
default.label.font=italic
default.invalid.fg=FF0000
default.invalid.bg=40FFFF00
default.text.fg=000000
default.text.bg=FFFFFF
default.caret.fg=000000
default.gutter.fg=505050
default.gutter.bg=FFFFFF
default.lineinterval.fg=000000
default.lineinterval.bg=FFFFFF
default.currentline.fg=505050
default.currentline.bg=E1EBE0
default.linehighlight.fg=E1EBE0
default.selectioncolor.bg=4B6983
default.divider.fg=000000
default.divider.bg=40808080
default.bracketscope.fg=0000C0
default.brackethighlight.fg=A0A0A0
default.gutterselection.fg=204080
default.gutterselection.bg=404080FF

# Colors more suited for XML editing
xml.comment1.fg=808080
xml.comment2.fg=808080
xml.comment3.fg=200080
xml.comment3.font=italic
xml.digit.fg=0000FF
xml.literal1.fg=000099
xml.literal2.fg=800080
xml.keyword1.fg=4080FF
xml.keyword2.fg=3870E0
xml.keyword3.fg=3060C0
xml.keyword4.fg=2850A0
xml.keyword5.fg=204080
xml.function.fg=3060C0
xml.operator.fg=000000
xml.markup.fg=990000
xml.label.fg=660000
xml.label.font=bold
xml.invalid.fg=FF0000
xml.invalid.bg=40FFFF00
xml.null.font=bold
xml.text.fg=000000
xml.text.bg=FFFFFF
xml.caret.fg=000000
xml.gutter.fg=505050
xml.gutter.bg=FFFFFF
xml.lineinterval.fg=000000
xml.lineinterval.bg=FFFFFF
xml.currentline.fg=505050
xml.currentline.bg=E1EBE0
xml.linehighlight.fg=E1EBE0
xml.selectioncolor.bg=4B6983
xml.divider.fg=000000
xml.divider.bg=40808080
xml.bracketscope.fg=0000C0
xml.brackethighlight.fg=A0A0A0
xml.gutterselection.fg=204080
xml.gutterselection.bg=404080FF
