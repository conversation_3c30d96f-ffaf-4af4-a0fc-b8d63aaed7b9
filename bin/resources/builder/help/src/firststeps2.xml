<?xml version="1.0" encoding="iso-8859-1"?>


<helpfile id="firststeps2">
	<page locale="en_US" title="Creating a window">
		This page will guide you through an example session using the
		Builder. The task is to create a dialog which will ask the user
		for his age.

		<h3>Creating the widget</h3>
		First, please select the tree item called "Widget Tree". When
		you create a widget, you always need to select the widget which
		should be the parent. In this case, the item "Widget Tree" is
		the root item, meaning that the widget should have no parent.
		<figure src="2_wtreeselected.png">The widget tree root item</figure>

		Press the "Create Frame" icon. In the upcoming dialog, just press OK.

		<figure src="2_frameicon.png">The "Create Frame" icon</figure>
		<figure src="2_addwidget.png">The "Add a widget" dialog</figure>
		
		Now, the widget tree should have changed and display this:

		<figure src="2_wtreeframe.png">The widget tree after the frame has been added</figure>
		<h3>Adding properties to the frame</h3>
		You have created the window, however it is not yet visible.
		To make it visible and to give it a title, you need to add some
		properties. Click on the "Add property" icon

		<figure src="2_addproperty.png">The "Add property" icon</figure>
		
		In the dialog, select the <b>size</b>, <b>title</b> and <b> visible</b> properties as shown in the screenshot.

		<figure src="2_addpropertydlg.png">The "Add property" dialog</figure>

        <note title="Note">
			Click on the properties while holding the control<br/>key to select multiple items
		</note>

		<h3>Setting the properties</h3>
		Set the property <b>name</b> to "AgeTest". This is important once
		you want to recreate the dialog in your application. Set the
		<b>size</b> to 400, 100. Set the <b>title</b> to "Test Application".
		Don't be disturbed by the parentheses, they only indicate that the
		contained text will be internationalized. We will look into that later.
		Finally, set the visible flag and an empty window with your title
		will appear.
		<h3>The result</h3>
		<figure src="2_result.png">The created window</figure>
		<see>
			<link page="firststeps3">Adding content</link>
		</see>					 
	</page>
</helpfile>

