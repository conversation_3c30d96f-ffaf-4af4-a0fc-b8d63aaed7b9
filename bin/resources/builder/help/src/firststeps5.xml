<?xml version="1.0" encoding="iso-8859-1"?>

<helpfile id="firststeps5">
	<page locale="en_US" title="Running it">
		Before compiling the application, you will need to copy over
		some dependencies:
		<bullet>XML GUI Library - Obviously you will need this</bullet>
		<bullet>Log4J, a logging library. This is a must for every serious java project</bullet>
		<bullet>Xerces, the Apache Software Foundation's XML parser. Feel free to use another XML parser</bullet>
		<bullet>The HIG Layout manager. It is internally used for the Group container and the exception message dialog</bullet><br/>

		Copy the <b>xmlgui-1.0.jar</b>, <b>log4j-1.2.8.jar</b>, <b>xml-apis.jar</b>, <b>xerces-2.4.1.jar</b> and <b>higlayout-1.0a-wenzel.jar</b> files to your project directory.

		<h3>Compiling the project</h3>
		Enter <code>javac -classpath xml-apis.jar:xerces-2.4.1.jar:higlayout-1.0a-wenzel.jar:log4j-1.2.8.jar:xmlgui-1.0.jar Test.java</code> in the project directory to compile it.

		<h3>Running the project</h3>
		Enter <code>java -classpath xml-apis.jar:xerces-2.4.1.jar:higlayout-1.0a-wenzel.jar:log4j-1.2.8.jar:xmlgui-1.0.jar:. Test</code> in the project directory to run the test application:

		<figure src="5_result.png">The running test application</figure>

		There will be warning messages on the console which look
		something like this:<br/><br/>
		<code>18.02.2004 19:13:27,455  WARN [  main] (InternationalizationManager.java:  78) - no internationalization for identifier [Test Application]</code><br/><br/>

		They appear in order to inform the developer that he/she forgot to
		internationalize a caption. To find out how to internationalize the
		test application, have a look at the following chapters.
	</page>
</helpfile>
