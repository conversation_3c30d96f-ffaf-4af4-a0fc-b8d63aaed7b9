<?xml version="1.0" encoding="iso-8859-1"?>

<helpfile id="firststeps3">
	<page locale="en_US" title="Adding content">
		Now that we have created the window, we are ready to add some
		content to it. We will use a custom container called "Group"
		to make designing this dialog as easy as possible. Select
		the "Frame" item in the tree and press the "Create Group" icon
		on the "Custom" tab of the Builder window.<br/>

		<note title="Note">
			The "Group" container automatically arranges labeled<br/>
			widgets and buttons, which are very common in simple<br/>
			dialogs
		</note>

		<figure src="3_group.png">The "Create Group" icon</figure>

		As before, just click "OK" inside the dialog. Now select the
		created "Group" item inside the tree and press the
		"Create Labeled Widget" icon, which is also on the "Custom" tab.

		<figure src="3_lwidget.png">The "Create Labeled Widget" icon</figure>

		Add the <b>label.text</b> property to the labeled widget as we have
		done it before with the frame. Set its value to
		"Please enter your name : "

		<figure src="3_lwproperties.png">The labeled widget's properties</figure>

		Select the "LabeledWidget" item in the tree and add a text field
		by pressing the "Create Text Field" icon

		<figure src="3_textfield.png">The "Create Text Field" icon</figure>

		Now, the widget tree should look like this:

		<figure src="3_tree.png">
			The widget tree after the text field creation
		</figure>

		Add the <b>key</b> property to the text field and set it to "age". This
		is needed later, to access the contents of the text field.

		<h3>Adding buttons</h3>
		Finally, we need to add the "OK" and "Cancel" buttons. Select
		the "Group" item in the widget tree and Click the "Create Button"
		icon.

		<figure src="3_button.png">The "Create Button" icon</figure>

		This time, don't just press "OK" but choose a preset: First add
		a cancel button with the "cancel" preset. Then select the "Group"
		item again and add a button with the "ok" preset. 

		<figure src="3_withbuttons.png">
			The dialog after the creation of the two buttons
		</figure>

		Did you notice how the buttons were put in the lower right of
		the window? That's what the "Group" container is for. For more
		advanced layouts, you will need to use panels and set the layout
		yourself.

		Select the "OK" button, and add the <b>default</b> property.
		Activate the flag and set the <b>name</b> property to "OKButton".

		<h3>Adding events</h3>
		This is all rather nice, but so far the buttons are useless
		because they won't tell you when they are clicked. So select
		the cancel button and press the "Add event" button.

		<figure src="3_addevent.png">The "Add event" button</figure>

		In the dialog, press the "Add" button. Another dialog will appear
		where you enter "cancel" into the "Name" field and press ok.
		The previous dialog should now update and look like this:

		<figure src="3_events.png">The events list</figure>

		Press "Close" and now do these same steps for the OK Button,
		except that you should use "ok" as the event name.

		<see>
			<link page="firststeps4">Creating the project</link>
		</see>
	</page>
</helpfile>

