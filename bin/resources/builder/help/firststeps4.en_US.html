<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>Creating the project</title>
<link name="Style" href="help.css" type="text/css" rel="stylesheet">
</head>
<body>
<h1>Creating the project</h1>
		Create a new directory which should hold your project. Before
		you save the created dialog to the directory, select the "Frame"
		item in the widget tree and make sure the visible property is
		unchecked. This is important because when we recreate the window
		in the final program, we want to do some initialization before
		making it visible. Now save the dialog to your created project
		directory as "Test.xml"

		<h3>Generating a skeleton</h3>
		Select "Generate skeleton .." from the Tools menu. Enter "Test"
		as the class name. Now click the "Browse" button, navigate to
		your project directory and save the skeleton as "Test.java".
		Press the "OK" button to proceed.

		<p align="center">
<img src="images/4_skeleton.png"><br>The "Generate skeleton" dialog</p>
<br>

		
<h3>The generated skeleton</h3>
		This skeleton recreates the top-level widgets and has an event
		handler for the events inside the XML file
<pre>
import org.beryl.gui.Controller;
import org.beryl.gui.GUIEvent;
import org.beryl.gui.GUIException;
import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.widgets.*;

public class Test extends Controller {
	private MapDataModel dataModel = null;
	private Frame ageTest = null;

	public Test() throws GUIException {
		dataModel = new MapDataModel();
		ageTest = (Frame) constructWidget("AgeTest", dataModel);
	}

	public void eventOccured(GUIEvent event) {
		String name = event.getName();
		
		if (name.equals("cancel")) {
		} else if (name.equals("ok")) {
		}
	}
}
</pre>
		
<h3>Adding a main function</h3>
		This main function initializes the XML GUI, creates the
		example application. In case something goes wrong, an
		message dialog documenting the failure will be displayed.
<pre>
	public static void main(String args[]) {
		try {
			GUIUtils.defaultInitialization(Locale.US);
			new Test();
		} catch (Exception e) {
			new MessageDialog(e);
		}
	}
</pre>
		
<h3>Finishing the constructor</h3>
		This constructor needs to be changed as below to set the
		initial value of the age text field to an empty string. Then,
		it should make the dialog visible.
<pre>
	public Test() throws GUIException {
		dataModel = new MapDataModel();
		ageTest = (Frame) constructWidget("AgeTest", dataModel);

		dataModel.setValue("age", "");
		ageTest.show();
	}
</pre>
		
<h3>Implementing the event handler</h3>
		Change the event handler to the function below so that
		a click on the cancel button closes the window. A click on
		the "OK" Button will print out the age on the console and
		then close the window.
<pre>
	public void eventOccured(GUIEvent event) {
		String name = event.getName();
		
		if (name.equals("cancel")) {
			ageTest.dispose();
		} else if (name.equals("ok")) {
			String age = (String) dataModel.getValue("age");
			System.out.println("You are " + age + " years old");
			ageTest.dispose();
		}
	}
</pre>
		
<p>
<img src="images/help_see.png"><b>See also :</b>
<blockquote>
			
<a href="firststeps5.en_US.html">Running it</a>
		
</blockquote>
</p>
	
</body>
</html>
