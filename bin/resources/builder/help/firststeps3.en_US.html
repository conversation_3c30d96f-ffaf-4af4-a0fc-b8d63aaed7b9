<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>Adding content</title>
<link name="Style" href="help.css" type="text/css" rel="stylesheet">
</head>
<body>
<h1>Adding content</h1>
		Now that we have created the window, we are ready to add some
		content to it. We will use a custom container called "Group"
		to make designing this dialog as easy as possible. Select
		the "Frame" item in the tree and press the "Create Group" icon
		on the "Custom" tab of the Builder window.<br>

		
<table cellspacing="0" cellpadding="1" align="center" class="note">
<tr>
<td>
<table cellspacing="0" cellpadding="1">
<tr class="label">
<td colspan="4">Note</td>
</tr>
<tr class="content">
<td width="3"></td><img src="images/help_note.png"><td width="3"></td><td>
			The "Group" container automatically arranges labeled<br>
			widgets and buttons, which are very common in simple<br>
			dialogs
		</td><td width="3"></td>
</tr>
</table>
</td>
</tr>
</table>

		
<p align="center">
<img src="images/3_group.png"><br>The "Create Group" icon</p>
<br>

		As before, just click "OK" inside the dialog. Now select the
		created "Group" item inside the tree and press the
		"Create Labeled Widget" icon, which is also on the "Custom" tab.

		<p align="center">
<img src="images/3_lwidget.png"><br>The "Create Labeled Widget" icon</p>
<br>

		Add the <b>label.text</b> property to the labeled widget as we have
		done it before with the frame. Set its value to
		"Please enter your name : "

		<p align="center">
<img src="images/3_lwproperties.png"><br>The labeled widget's properties</p>
<br>

		Select the "LabeledWidget" item in the tree and add a text field
		by pressing the "Create Text Field" icon

		<p align="center">
<img src="images/3_textfield.png"><br>The "Create Text Field" icon</p>
<br>

		Now, the widget tree should look like this:

		<p align="center">
<img src="images/3_tree.png"><br>
			The widget tree after the text field creation
		</p>
<br>

		Add the <b>key</b> property to the text field and set it to "age". This
		is needed later, to access the contents of the text field.

		<h3>Adding buttons</h3>
		Finally, we need to add the "OK" and "Cancel" buttons. Select
		the "Group" item in the widget tree and Click the "Create Button"
		icon.

		<p align="center">
<img src="images/3_button.png"><br>The "Create Button" icon</p>
<br>

		This time, don't just press "OK" but choose a preset: First add
		a cancel button with the "cancel" preset. Then select the "Group"
		item again and add a button with the "ok" preset. 

		<p align="center">
<img src="images/3_withbuttons.png"><br>
			The dialog after the creation of the two buttons
		</p>
<br>

		Did you notice how the buttons were put in the lower right of
		the window? That's what the "Group" container is for. For more
		advanced layouts, you will need to use panels and set the layout
		yourself.

		Select the "OK" button, and add the <b>default</b> property.
		Activate the flag and set the <b>name</b> property to "OKButton".

		<h3>Adding events</h3>
		This is all rather nice, but so far the buttons are useless
		because they won't tell you when they are clicked. So select
		the cancel button and press the "Add event" button.

		<p align="center">
<img src="images/3_addevent.png"><br>The "Add event" button</p>
<br>

		In the dialog, press the "Add" button. Another dialog will appear
		where you enter "cancel" into the "Name" field and press ok.
		The previous dialog should now update and look like this:

		<p align="center">
<img src="images/3_events.png"><br>The events list</p>
<br>

		Press "Close" and now do these same steps for the OK Button,
		except that you should use "ok" as the event name.

		<p>
<img src="images/help_see.png"><b>See also :</b>
<blockquote>
			
<a href="firststeps4.en_US.html">Creating the project</a>
		
</blockquote>
</p>
	
</body>
</html>
