builder.common.name=Name
builder.common.type=Typ
builder.common.value=Wert
builder.common.left=Links
builder.common.right=Rechts
builder.common.top=Oben
builder.common.bottom=Unten
builder.common.apply=躡ernehmen
builder.common.new=Neu
builder.common.new.accelerator=C+N
builder.common.open=謋fnen...
builder.common.open.accelerator=C+O
builder.common.save=Speichern
builder.common.save.mnemonic=s
builder.common.save.accelerator=C+S
builder.common.saveas=Speichern Unter...
builder.common.saveas.accelerator=C+U
builder.common.quit=Beenden
builder.common.quit.accelerator=A+B
builder.common.helpcontents=Inhaltsverzeichnis
builder.common.helpcontents.accelerator=C+I
builder.common.about=躡er
builder.common.about.accelerator=C+u
builder.common.add=Hinzuf黦en
builder.common.add.mnemonic=h
builder.common.delete=L鰏chen
builder.common.close=Schlie遝n
builder.common.close.mnemonic=s
builder.common.browse=Durchsuchen
builder.common.help=Hilfe
builder.common.file=Datei
builder.common.description=Beschreibung
builder.common.apply=Anwenden
builder.common.yes=Ja
builder.common.yes.mnemonic=j
builder.common.no=Nein
builder.common.no.mnemonic=n
builder.common.cancel=Abbrechen
builder.common.cancel.mnemonic=a
builder.common.import=Importieren
builder.common.import.mnemonic=i
builder.common.refresh=Auffrischen
builder.common.refresh.mnemonic=a
builder.common.clear=Leeren
builder.common.clear.mnemonic=l

builder.builder.title=XML GUI Builder
builder.builder.tools=Tools
builder.builder.i18n=Internationalisierungs-Editor
builder.builder.i18n.accelerator=C+I
builder.builder.genskel=Skelett generieren ..
builder.builder.genskel.accelerator=C+G
builder.builder.lookandfeel=Look And Feel Auswahl
builder.builder.lookandfeel.accelerator=C+L
builder.builder.palette.basic=Basis
builder.builder.palette.menu=Men�
builder.builder.palette.input=Eingabe
builder.builder.palette.other=Andere
builder.builder.palette.custom=Spezifisch
builder.builder.palette.special=Speziell
builder.builder.widget.frame.desc=Frame
builder.builder.widget.dialog.desc=Dialog
builder.builder.widget.panel.desc=Panel
builder.builder.widget.label.desc=Label
builder.builder.widget.button.desc=Button
builder.builder.widget.checkbox.desc=Checkbox
builder.builder.widget.radiobutton.desc=Radio button
builder.builder.widget.buttongroup.desc=ButtonGroup f黵 RadioButtons
builder.builder.widget.combobox.desc=Combo Box
builder.builder.widget.list.desc=List
builder.builder.widget.textfield.desc=Text Field
builder.builder.widget.passwordfield.desc=Password Field
builder.builder.widget.textpane.desc=Text Pane
builder.builder.widget.image.desc=Image
builder.builder.widget.table.desc=Table
builder.builder.widget.tree.desc=Tree
builder.builder.widget.progressbar.desc=Progress Bar
builder.builder.widget.tabbedpane.desc=Tabbed Pane
builder.builder.widget.splitpane.desc=Split Pane
builder.builder.widget.separator.desc=Separator (f黵 men黶, groups etc)
builder.builder.widget.item.desc=Mehrzweck-Element (ComboBox, List, IconView)
builder.builder.widget.treeitem.desc=Tree Item
builder.builder.widget.labeledwidget.desc=Labeled widget
builder.builder.widget.group.desc=Group panel (Erstellt ein layout f黵 LabeledWidgets, buttons etc)
builder.builder.widget.menubar.desc=Menu Bar
builder.builder.widget.menu.desc=Menu
builder.builder.widget.menuitem.desc=Menu Item
builder.builder.widget.popupmenu.desc=Popup Menu
builder.builder.widget.toolbar.desc=Tool Bar
builder.builder.widget.spacer.desc=Spacer
builder.builder.widget.outlookbar.desc=Outlook Bar
builder.builder.widget.outlookpanel.desc=Outlook Panel
builder.builder.widget.iconview.desc=Icon View
builder.builder.widget.syntaxeditor.desc=Syntax Editor

builder.i18n.title=Internationalisierungs-Editor
builder.i18n.add.title=Internationalisierung hinzuf黦en ..
builder.i18n.add.name=Name :
builder.i18n.add.value=Wert :

builder.about.title=躡er
builder.about.line1=Beryl XML GUI Builder 1.0
builder.about.line2=(c) 2004 by Wenzel Jakob

builder.save.title=Unspeicherte 膎derungen
builder.save.label=Die Datei wurde bearbeitet. 膎derungen speichern?
builder.savei18n.title=Ungespeicherte 膎derungen
builder.savei18n.label=Die Internationalisierungen wurden bearbeitet. 膎derungen speichern?

builder.lookandfeel.title=Look And Feel Auswahl 
builder.lookandfeel.class=Look And Feel Klasse :
builder.lookandfeel.theme=Theme Klasse :
builder.lookandfeel.license=Lizenzcode :
builder.lookandfeel.javax.swing.plaf.metal.MetalLookAndFeel=Metal
builder.lookandfeel.javax.swing.plaf.metal.DefaultMetalTheme=Default
builder.lookandfeel.com.sun.java.swing.plaf.windows.WindowsLookAndFeel=Windows
builder.lookandfeel.com.sun.java.swing.plaf.motif.MotifLookAndFeel=Motif
builder.lookandfeel.com.incors.plaf.kunststoff.KunststoffLookAndFeel=INCORS Kunststoff
builder.lookandfeel.com.incors.plaf.kunststoff.KunststoffTheme=Default
builder.lookandfeel.com.incors.plaf.alloy.AlloyLookAndFeel=INCORS Alloy
builder.lookandfeel.com.incors.plaf.alloy.DefaultAlloyTheme=Default
builder.lookandfeel.com.incors.plaf.alloy.themes.acid.AcidTheme=Acid
builder.lookandfeel.com.incors.plaf.alloy.themes.bedouin.BedouinTheme=Bedouin
builder.lookandfeel.com.incors.plaf.alloy.themes.glass.GlassTheme=Glass
builder.lookandfeel.com.jgoodies.looks.windows.WindowsLookAndFeel=JGoodies Extended Windows
builder.lookandfeel.com.jgoodies.looks.plastic.PlasticLookAndFeel=JGoodies Plastic
builder.lookandfeel.com.jgoodies.looks.plastic.Plastic3DLookAndFeel=JGoodies Plastic 3D
builder.lookandfeel.com.jgoodies.looks.plastic.PlasticXPLookAndFeel=JGoodies Plastic XP
builder.lookandfeel.com.jgoodies.looks.plastic.theme.BrownSugar=Brown Sugar
builder.lookandfeel.com.jgoodies.looks.plastic.theme.DarkStar=Dark Star
builder.lookandfeel.com.jgoodies.looks.plastic.theme.DesertBlue=Desert Blue
builder.lookandfeel.com.jgoodies.looks.plastic.theme.DesertBluer=Desert Bluer
builder.lookandfeel.com.jgoodies.looks.plastic.theme.DesertGreen=Desert Green
builder.lookandfeel.com.jgoodies.looks.plastic.theme.DesertRed=Desert Red
builder.lookandfeel.com.jgoodies.looks.plastic.theme.DesertYellow=Desert Yellow
builder.lookandfeel.com.jgoodies.looks.plastic.theme.ExperienceBlue=Experience Blue
builder.lookandfeel.com.jgoodies.looks.plastic.theme.ExperienceGreen=Experience Green
builder.lookandfeel.com.jgoodies.looks.plastic.theme.Silver=Silver
builder.lookandfeel.com.jgoodies.looks.plastic.theme.SkyBlue=Sky Blue
builder.lookandfeel.com.jgoodies.looks.plastic.theme.SkyBluer=Sky Bluer
builder.lookandfeel.com.jgoodies.looks.plastic.theme.SkyBluerTahoma=Sky Bluer Tahoma
builder.lookandfeel.com.jgoodies.looks.plastic.theme.SkyGreen=Sky Green
builder.lookandfeel.com.jgoodies.looks.plastic.theme.SkyKrupp=Sky Krupp
builder.lookandfeel.com.jgoodies.looks.plastic.theme.SkyPink=Sky Pink
builder.lookandfeel.com.jgoodies.looks.plastic.theme.SkyRed=Sky Red
builder.lookandfeel.com.jgoodies.looks.plastic.theme.SkyYellow=Sky Yellow

builder.wtree.title=XML GUI Widget Tree
builder.wtree.root=Widget Tree
builder.wtree.addevent=Event hinzuf黦en
builder.wtree.addproperty=Property hinzuf黦en
builder.wtree.moveup=Nach oben bewegen
builder.wtree.movedown=Nach unten bewegen

builder.genskel.fcn=Voller Klassenname :
builder.genskel.dest=Zieldatei :

builder.addwidget.title=Komponente hinzuf黦en...
builder.addwidget.widget=Komponente :
builder.addwidget.preset=Vordefiniert :
builder.addwidget.anchor=Anker :
builder.addwidget.anchor.set=Setzen

builder.addproperty.title=Property hinzuf黦en...
builder.addproperty.name=Name :
builder.addproperty.type=Typ :
builder.addproperty.value=Wert :
builder.addproperty.common=Allgemein
builder.addproperty.manual=Manuell
builder.addproperty.istring=Internationalisierter string (istring)
builder.addproperty.string=String (string)
builder.addproperty.int=Integer (int)
builder.addproperty.bool=Boolean (bool)
builder.addproperty.float=Flie遦ommazahl einfacher Pr鋤ision (float)
builder.addproperty.double=Flie遦ommazahl doppelter Pr鋤ision (double)

builder.addevent.title=Event hinzuf黦en...
builder.addevent.event=Event :
builder.addevent.name=Name :

builder.aneditor.title=Anker setzen...
builder.aneditor.type=Typ :
builder.aneditor.row=Reihe :
builder.aneditor.column=Spalte :
builder.aneditor.width=Breite :
builder.aneditor.height=H鰄e :
builder.aneditor.alignment=Ausrichtung :
builder.aneditor.xalignment=X Ausrichtung :
builder.aneditor.yalignment=Y Ausrichtung :

builder.bdeditor.title=Rahmen setzen...
builder.bdeditor.type=Typ :
builder.bdeditor.top=Oben :
builder.bdeditor.bottom=Unten :
builder.bdeditor.left=Links :
builder.bdeditor.right=Rechts :
builder.bdeditor.title=Titel :

builder.higeditor.title=HIG Layout editieren...
builder.higeditor.rows=Reihen
builder.higeditor.columns=Spalten
builder.higeditor.pixels=Pixel :
builder.higeditor.weight=Gewicht :
builder.higeditor.insbefore=Davor einf黦en
builder.higeditor.insafter=Danach einf黦en
builder.higeditor.col.index=Index
builder.higeditor.col.weight=Gewicht
builder.higeditor.col.pixels=Pixel

builder.event.title=Events...

