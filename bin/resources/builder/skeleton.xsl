<?xml version="1.0" encoding="iso-8859-1"?>

<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
	<xsl:param name="class"/>
	<xsl:param name="package"/>
	<xsl:output method="text"/>

	<xsl:template match="/">
		<xsl:if test="$package!=''">
			<xsl:text>package </xsl:text>
			<xsl:value-of select="$package"/>
			<xsl:text>;

</xsl:text>
		</xsl:if>
		<xsl:text>import org.beryl.gui.Controller;
import org.beryl.gui.GUIEvent;
import org.beryl.gui.GUIException;
import org.beryl.gui.model.MapDataModel;
import org.beryl.gui.widgets.*;

public class </xsl:text>
	<xsl:value-of select="$class"/>
	<xsl:text> extends Controller {
	private MapDataModel dataModel = null;
</xsl:text>
	<xsl:for-each select="/UI/widget">
		<xsl:if test="@name">
			<xsl:text>	private </xsl:text><xsl:value-of select="@class"/>
			<xsl:text> </xsl:text>
			<xsl:call-template name="lowerFirstCharacter">
				<xsl:with-param name="name">
					<xsl:value-of select="@name"/>
				</xsl:with-param>
			</xsl:call-template>
			<xsl:text> = null;
</xsl:text>
		</xsl:if>
	</xsl:for-each><xsl:text>
	public </xsl:text>
	<xsl:value-of select="$class"/>
	<xsl:text>() throws GUIException {
		dataModel = new MapDataModel();
</xsl:text>
	<xsl:for-each select="/UI/widget">
		<xsl:if test="@name">
			<xsl:text>		</xsl:text>
			<xsl:call-template name="lowerFirstCharacter">
				<xsl:with-param name="name">
					<xsl:value-of select="@name"/>
				</xsl:with-param>
			</xsl:call-template> = (<xsl:value-of select="@class"/>) constructWidget("<xsl:value-of select="@name"/>", dataModel);<xsl:text>
</xsl:text>
		</xsl:if>
	</xsl:for-each>
	<xsl:text>	}
</xsl:text>
	<xsl:text>
	public void eventOccured(GUIEvent event) {
		String name = event.getName();
		
</xsl:text>
	<xsl:if test="//emit">
		<xsl:text>		</xsl:text>
	</xsl:if>
	<xsl:for-each select="//emit">
		<xsl:text>if (name.equals("</xsl:text>
		<xsl:value-of select="@name"/>
		<xsl:text>")) {
		}</xsl:text>
		<xsl:choose>
			<xsl:when test="position() != last()">
				<xsl:text> else </xsl:text>
			</xsl:when>
			<xsl:otherwise><xsl:text>
</xsl:text>
			</xsl:otherwise>
		</xsl:choose>
		
	</xsl:for-each>
	<xsl:text>	}
}
</xsl:text>
	</xsl:template>
	<xsl:template name="lowerFirstCharacter">
		<xsl:param name="name"/>
		<xsl:value-of select="translate(substring($name, 1, 1), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz')"/>
		<xsl:value-of select="substring($name, 2)"/>
	</xsl:template>
</xsl:stylesheet>
