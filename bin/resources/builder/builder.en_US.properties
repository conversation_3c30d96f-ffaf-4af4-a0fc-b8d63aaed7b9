builder.common.name=Name
builder.common.type=Type
builder.common.value=Value
builder.common.left=Left
builder.common.right=Right
builder.common.top=Top
builder.common.bottom=Bottom
builder.common.apply=Apply
builder.common.new=New
builder.common.new.accelerator=C+N
builder.common.open=Open...
builder.common.open.accelerator=C+O
builder.common.save=Save
builder.common.save.mnemonic=s
builder.common.save.accelerator=C+S
builder.common.saveas=Save As...
builder.common.saveas.accelerator=C+A
builder.common.quit=Quit
builder.common.quit.accelerator=A+Q
builder.common.helpcontents=Help Contents
builder.common.helpcontents.accelerator=C+H
builder.common.about=About
builder.common.about.accelerator=C+B
builder.common.add=Add
builder.common.add.mnemonic=a
builder.common.delete=Delete
builder.common.close=Close
builder.common.close.mnemonic=c
builder.common.browse=Browse
builder.common.help=Help
builder.common.file=File
builder.common.description=Description
builder.common.apply=Apply
builder.common.yes=Yes
builder.common.yes.mnemonic=y
builder.common.no=No
builder.common.no.mnemonic=n
builder.common.cancel=Cancel
builder.common.cancel.mnemonic=c
builder.common.import=Import
builder.common.import.mnemonic=i
builder.common.refresh=Refresh
builder.common.refresh.mnemonic=r
builder.common.clear=Clear
builder.common.clear.mnemonic=c

builder.builder.title=XML GUI Builder
builder.builder.tools=Tools
builder.builder.i18n=Internationalization editor
builder.builder.i18n.accelerator=C+I
builder.builder.genskel=Generate skeleton ..
builder.builder.genskel.accelerator=C+G
builder.builder.lookandfeel=Look And Feel chooser
builder.builder.lookandfeel.accelerator=C+L
builder.builder.palette.basic=Basic
builder.builder.palette.menu=Menu
builder.builder.palette.input=Input
builder.builder.palette.other=Other
builder.builder.palette.custom=Custom
builder.builder.palette.special=Special
builder.builder.widget.frame.desc=Frame
builder.builder.widget.dialog.desc=Dialog
builder.builder.widget.panel.desc=Panel
builder.builder.widget.label.desc=Label
builder.builder.widget.button.desc=Button
builder.builder.widget.checkbox.desc=Checkbox
builder.builder.widget.radiobutton.desc=Radio button
builder.builder.widget.buttongroup.desc=ButtonGroup for RadioButtons
builder.builder.widget.combobox.desc=Combo Box
builder.builder.widget.list.desc=List
builder.builder.widget.textfield.desc=Text Field
builder.builder.widget.passwordfield.desc=Password Field
builder.builder.widget.textpane.desc=Text Pane
builder.builder.widget.image.desc=Image
builder.builder.widget.table.desc=Table
builder.builder.widget.tree.desc=Tree
builder.builder.widget.progressbar.desc=Progress Bar
builder.builder.widget.tabbedpane.desc=Tabbed Pane
builder.builder.widget.splitpane.desc=Split Pane
builder.builder.widget.separator.desc=Separator (for menus, groups etc)
builder.builder.widget.item.desc=Multi-Purpose Item (ComboBox, List, IconView)
builder.builder.widget.treeitem.desc=Tree Item
builder.builder.widget.labeledwidget.desc=Labeled widget
builder.builder.widget.group.desc=Group panel (Creates a layout for labeled widgets, buttons etc)
builder.builder.widget.menubar.desc=Menu Bar
builder.builder.widget.menu.desc=Menu
builder.builder.widget.menuitem.desc=Menu Item
builder.builder.widget.popupmenu.desc=Popup Menu
builder.builder.widget.toolbar.desc=Tool Bar
builder.builder.widget.spacer.desc=Spacer
builder.builder.widget.outlookbar.desc=Outlook Bar
builder.builder.widget.outlookpanel.desc=Outlook Panel
builder.builder.widget.iconview.desc=Icon View
builder.builder.widget.syntaxeditor.desc=Syntax Editor

builder.i18n.title=Internationalization Editor
builder.i18n.add.title=Add an internationalization ..
builder.i18n.add.name=Name :
builder.i18n.add.value=Value :

builder.about.title=About
builder.about.line1=Beryl XML GUI Builder 1.0
builder.about.line2=(c) 2004 by Wenzel Jakob

builder.save.title=Unsaved changes
builder.save.label=The file has been modified. Save changes?
builder.savei18n.title=Unsaved changes
builder.savei18n.label=Internationalizations were modified. Save changes?

builder.lookandfeel.title=Look And Feel chooser
builder.lookandfeel.class=Look And Feel class :
builder.lookandfeel.theme=Theme class :
builder.lookandfeel.license=License code :
builder.lookandfeel.javax.swing.plaf.metal.MetalLookAndFeel=Metal
builder.lookandfeel.javax.swing.plaf.metal.DefaultMetalTheme=Default
builder.lookandfeel.com.sun.java.swing.plaf.windows.WindowsLookAndFeel=Windows
builder.lookandfeel.com.sun.java.swing.plaf.motif.MotifLookAndFeel=Motif
builder.lookandfeel.com.incors.plaf.kunststoff.KunststoffLookAndFeel=INCORS Kunststoff
builder.lookandfeel.com.incors.plaf.kunststoff.KunststoffTheme=Default
builder.lookandfeel.com.incors.plaf.alloy.AlloyLookAndFeel=INCORS Alloy
builder.lookandfeel.com.incors.plaf.alloy.DefaultAlloyTheme=Default
builder.lookandfeel.com.incors.plaf.alloy.themes.acid.AcidTheme=Acid
builder.lookandfeel.com.incors.plaf.alloy.themes.bedouin.BedouinTheme=Bedouin
builder.lookandfeel.com.incors.plaf.alloy.themes.glass.GlassTheme=Glass
builder.lookandfeel.com.jgoodies.looks.windows.WindowsLookAndFeel=JGoodies Extended Windows
builder.lookandfeel.com.jgoodies.looks.plastic.PlasticLookAndFeel=JGoodies Plastic
builder.lookandfeel.com.jgoodies.looks.plastic.Plastic3DLookAndFeel=JGoodies Plastic 3D
builder.lookandfeel.com.jgoodies.looks.plastic.PlasticXPLookAndFeel=JGoodies Plastic XP
builder.lookandfeel.com.jgoodies.looks.plastic.theme.BrownSugar=Brown Sugar
builder.lookandfeel.com.jgoodies.looks.plastic.theme.DarkStar=Dark Star
builder.lookandfeel.com.jgoodies.looks.plastic.theme.DesertBlue=Desert Blue
builder.lookandfeel.com.jgoodies.looks.plastic.theme.DesertBluer=Desert Bluer
builder.lookandfeel.com.jgoodies.looks.plastic.theme.DesertGreen=Desert Green
builder.lookandfeel.com.jgoodies.looks.plastic.theme.DesertRed=Desert Red
builder.lookandfeel.com.jgoodies.looks.plastic.theme.DesertYellow=Desert Yellow
builder.lookandfeel.com.jgoodies.looks.plastic.theme.ExperienceBlue=Experience Blue
builder.lookandfeel.com.jgoodies.looks.plastic.theme.ExperienceGreen=Experience Green
builder.lookandfeel.com.jgoodies.looks.plastic.theme.Silver=Silver
builder.lookandfeel.com.jgoodies.looks.plastic.theme.SkyBlue=Sky Blue
builder.lookandfeel.com.jgoodies.looks.plastic.theme.SkyBluer=Sky Bluer
builder.lookandfeel.com.jgoodies.looks.plastic.theme.SkyBluerTahoma=Sky Bluer Tahoma
builder.lookandfeel.com.jgoodies.looks.plastic.theme.SkyGreen=Sky Green
builder.lookandfeel.com.jgoodies.looks.plastic.theme.SkyKrupp=Sky Krupp
builder.lookandfeel.com.jgoodies.looks.plastic.theme.SkyPink=Sky Pink
builder.lookandfeel.com.jgoodies.looks.plastic.theme.SkyRed=Sky Red
builder.lookandfeel.com.jgoodies.looks.plastic.theme.SkyYellow=Sky Yellow

builder.wtree.title=XML GUI Widget Tree
builder.wtree.root=Widget Tree
builder.wtree.addevent=Add an event
builder.wtree.addproperty=Add a property
builder.wtree.moveup=Move up
builder.wtree.movedown=Move down

builder.genskel.fcn=Full class name :
builder.genskel.dest=Destination file :

builder.addwidget.title=Add a widget...
builder.addwidget.widget=Widget :
builder.addwidget.preset=Preset :
builder.addwidget.anchor=Anchor :
builder.addwidget.anchor.set=Set

builder.addproperty.title=Add a property...
builder.addproperty.name=Name :
builder.addproperty.type=Type :
builder.addproperty.value=Value :
builder.addproperty.common=Common
builder.addproperty.manual=Manual
builder.addproperty.istring=Internationalized string (istring)
builder.addproperty.string=String (string)
builder.addproperty.int=Integer (int)
builder.addproperty.bool=Boolean (bool)
builder.addproperty.float=Single precision floating point (float)
builder.addproperty.double=Double precision floating point (double)

builder.addevent.title=Add an event...
builder.addevent.event=Event :
builder.addevent.name=Name :

builder.aneditor.title=Set anchor...
builder.aneditor.type=Type :
builder.aneditor.row=Row :
builder.aneditor.column=Column :
builder.aneditor.width=Width :
builder.aneditor.height=Height :
builder.aneditor.alignment=Alignment :
builder.aneditor.xalignment=X Alignment :
builder.aneditor.yalignment=Y Alignment :

builder.bdeditor.title=Set border...
builder.bdeditor.type=Type :
builder.bdeditor.top=Top :
builder.bdeditor.bottom=Bottom :
builder.bdeditor.left=Left :
builder.bdeditor.right=Right :
builder.bdeditor.title=Title :

builder.higeditor.title=Edit HIG Layout...
builder.higeditor.rows=Rows
builder.higeditor.columns=Columns
builder.higeditor.pixels=Pixels :
builder.higeditor.weight=Weight :
builder.higeditor.insbefore=Insert before
builder.higeditor.insafter=Insert after
builder.higeditor.col.index=Index
builder.higeditor.col.weight=Weight
builder.higeditor.col.pixels=Pixels

builder.event.title=Events...

