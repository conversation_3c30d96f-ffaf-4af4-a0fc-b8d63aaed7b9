<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry kind="src" path="src"/>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER"/>
	<classpathentry kind="lib" path="lib/AbsoluteLayout.jar"/>
	<classpathentry kind="lib" path="lib/ant.jar"/>
	<classpathentry kind="lib" path="lib/aopalliance-1.0.jar"/>
	<classpathentry kind="lib" path="lib/axis.jar"/>
	<classpathentry kind="lib" path="lib/batik-1.7.jar"/>
	<classpathentry kind="lib" path="lib/c3p0-0.9.0.jar"/>
	<classpathentry kind="lib" path="lib/commons-codec-1.6.jar"/>
	<classpathentry kind="lib" path="lib/commons-collections-3.1.jar"/>
	<classpathentry kind="lib" path="lib/commons-dbcp.jar"/>
	<classpathentry kind="lib" path="lib/commons-discovery-0.2.jar"/>
	<classpathentry kind="lib" path="lib/commons-fileupload-1.3.1.jar"/>
	<classpathentry kind="lib" path="lib/commons-httpclient-3.1.jar"/>
	<classpathentry kind="lib" path="lib/commons-io-1.4.jar"/>
	<classpathentry kind="lib" path="lib/commons-logging-1.1.1.jar"/>
	<classpathentry kind="lib" path="lib/commons-net-2.0.jar"/>
	<classpathentry kind="lib" path="lib/commons-pool.jar"/>
	<classpathentry kind="lib" path="lib/Dm6JdbcDriver.jar"/>
	<classpathentry kind="lib" path="lib/DmDialect.jar"/>
	<classpathentry kind="lib" path="lib/dom4j-1.6.1.jar"/>
	<classpathentry kind="lib" path="lib/dubbo-2.5.3.jar"/>
	<classpathentry kind="lib" path="lib/fastjson-1.2.59-sources.jar"/>
	<classpathentry kind="lib" path="lib/fastjson-1.2.59.jar"/>
	<classpathentry kind="lib" path="lib/filemanager.jar"/>
	<classpathentry kind="lib" path="lib/gnu-regexp-1.1.4.jar"/>
	<classpathentry kind="lib" path="lib/GraphPlatResource.jar"/>
	<classpathentry kind="lib" path="lib/groovy-1.0-beta-1.jar"/>
	<classpathentry kind="lib" path="lib/hibernate3.jar"/>
	<classpathentry kind="lib" path="lib/higlayout-1.0a-wenzel.jar"/>
	<classpathentry kind="lib" path="lib/httpclient-4.3.1.jar"/>
	<classpathentry kind="lib" path="lib/httpcore-4.3.jar"/>
	<classpathentry kind="lib" path="lib/javassist-3.18.1-GA.jar"/>
	<classpathentry kind="lib" path="lib/jaxrpc.jar"/>
	<classpathentry kind="lib" path="lib/jcommon-1.0.12.jar"/>
	<classpathentry kind="lib" path="lib/JFontChooser.jar"/>
	<classpathentry kind="lib" path="lib/jfreechart-1.0.13.jar"/>
	<classpathentry kind="lib" path="lib/jfreereport-0.8.7.jar"/>
	<classpathentry kind="lib" path="lib/jfreereport-bsf-2.3.0.jar"/>
	<classpathentry kind="lib" path="lib/jfreereport-bsh-1.3.0.jar"/>
	<classpathentry kind="lib" path="lib/jfreereport-demo-0.8.7.jar"/>
	<classpathentry kind="lib" path="lib/jfreereport-itext-1.3.6.jar"/>
	<classpathentry kind="lib" path="lib/jfreereport-jcommon-1.0.1.jar"/>
	<classpathentry kind="lib" path="lib/jfreereport-jcommon-xml-1.0.1.jar"/>
	<classpathentry kind="lib" path="lib/jfreereport-libfonts-0.1.4.jar"/>
	<classpathentry kind="lib" path="lib/jfreereport-pixie-0.8.4.jar"/>
	<classpathentry kind="lib" path="lib/jfreereport-poi-3.0-alpha1.jar"/>
	<classpathentry kind="lib" path="lib/jhall-2.0.1.jar"/>
	<classpathentry kind="lib" path="lib/jl1.0.1.jar"/>
	<classpathentry kind="lib" path="lib/js.jar"/>
	<classpathentry kind="lib" path="lib/json-lib-2.2.1-jdk13.jar"/>
	<classpathentry kind="lib" path="lib/jta.jar"/>
	<classpathentry kind="lib" path="lib/kingbasejdbc3.jar"/>
	<classpathentry kind="lib" path="lib/log4j-1.2.8.jar"/>
	<classpathentry kind="lib" path="lib/netty-3.2.9.Final.jar"/>
	<classpathentry kind="lib" path="lib/novaworx-syntax-0.0.7.jar"/>
	<classpathentry kind="lib" path="lib/ojdbc14.jar"/>
	<classpathentry kind="lib" path="lib/oracle-jdbc-10.1.0.2.0.jar"/>
	<classpathentry kind="lib" path="lib/pinyin4j-2.5.0.jar"/>
	<classpathentry kind="lib" path="lib/poi-3.9.jar"/>
	<classpathentry kind="lib" path="lib/poi-ooxml-3.9.jar"/>
	<classpathentry kind="lib" path="lib/poi-ooxml-schemas-3.9.jar"/>
	<classpathentry kind="lib" path="lib/RmiJDBC.jar"/>
	<classpathentry kind="lib" path="lib/saaj.jar"/>
	<classpathentry kind="lib" path="lib/spring-aop-3.1.1.RELEASE.jar"/>
	<classpathentry kind="lib" path="lib/spring-asm-3.1.1.RELEASE.jar"/>
	<classpathentry kind="lib" path="lib/spring-beans-3.1.1.RELEASE.jar"/>
	<classpathentry kind="lib" path="lib/spring-context-3.1.1.RELEASE.jar"/>
	<classpathentry kind="lib" path="lib/spring-context-support-3.1.1.RELEASE.jar"/>
	<classpathentry kind="lib" path="lib/spring-core-3.1.1.RELEASE.jar"/>
	<classpathentry kind="lib" path="lib/spring-expression-3.1.1.RELEASE.jar"/>
	<classpathentry kind="lib" path="lib/spring-hibernate.jar"/>
	<classpathentry kind="lib" path="lib/spring-jdbc-3.1.1.RELEASE.jar"/>
	<classpathentry kind="lib" path="lib/spring-orm-3.1.1.RELEASE.jar"/>
	<classpathentry kind="lib" path="lib/spring-tx-3.1.1.RELEASE.jar"/>
	<classpathentry kind="lib" path="lib/spring-web-3.1.1.RELEASE.jar"/>
	<classpathentry kind="lib" path="lib/spring-webmvc-3.1.1.RELEASE.jar"/>
	<classpathentry kind="lib" path="lib/spring.jar"/>
	<classpathentry kind="lib" path="lib/sqlite-jdbc-3.7.2.jar"/>
	<classpathentry kind="lib" path="lib/swing-layout-1.0.3.jar"/>
	<classpathentry kind="lib" path="lib/tbp-common-configuration-1.0.0.jar"/>
	<classpathentry kind="lib" path="lib/tbp-common-exception-1.0.0.jar"/>
	<classpathentry kind="lib" path="lib/tbp-common-jdbc-1.0.0.jar"/>
	<classpathentry kind="lib" path="lib/tbp-common-utils-1.0.0.jar"/>
	<classpathentry kind="lib" path="lib/tm-extractors-0.4.jar"/>
	<classpathentry kind="lib" path="lib/tomcat-apr-5.5.15.jar"/>
	<classpathentry kind="lib" path="lib/webserviceutils.jar"/>
	<classpathentry kind="lib" path="lib/wsdl4j.jar"/>
	<classpathentry kind="lib" path="lib/xbean-2.2.0.jar"/>
	<classpathentry kind="lib" path="lib/xercesImpl-2.6.2.jar"/>
	<classpathentry kind="lib" path="lib/xml-apis-ext.jar"/>
	<classpathentry kind="lib" path="lib/zkclient-0.4.jar"/>
	<classpathentry kind="lib" path="lib/zookeeper-3.3.1.jar"/>
	<classpathentry kind="lib" path="lib/ZTable.jar"/>
	<classpathentry combineaccessrules="false" kind="src" path="/powernet-graphic-framework"/>
	<classpathentry combineaccessrules="false" kind="src" path="/powernet-graphic-xmlgui"/>
	<classpathentry combineaccessrules="false" kind="src" path="/powernet-graphic-app-czp-new-yndd"/>
	<classpathentry kind="lib" path="lib/commons-compress-1.18.jar"/>
	<classpathentry kind="lib" path="lib/commons-io-2.11.0.jar"/>
	<classpathentry kind="lib" path="lib/Dm7JdbcDriver-7.0.jar"/>
	<classpathentry kind="lib" path="lib/poi-scratchpad-3.9.jar"/>
	<classpathentry kind="lib" path="lib/xmlbeans-3.1.0.jar"/>
	<classpathentry kind="output" path="bin"/>
</classpath>
