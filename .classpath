<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry kind="src" path="src"/>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER"/>
	<classpathentry kind="lib" path="lib/commons-io-1.4.jar"/>
	<classpathentry kind="lib" path="lib/log4j-1.2.8.jar"/>
	<classpathentry kind="lib" path="lib/xml-apis-ext.jar"/>
	<classpathentry kind="lib" path="lib/commons-logging-1.1.1.jar"/>
	<classpathentry kind="lib" path="lib/batik-1.7.jar" sourcepath="/batik"/>
	<classpathentry combineaccessrules="false" kind="src" path="/powernet-graphic-xmlgui"/>
	<classpathentry kind="lib" path="lib/pinyin4j-2.5.0.jar"/>
	<classpathentry kind="output" path="bin"/>
</classpath>
