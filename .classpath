<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry kind="src" path="src"/>
	<classpathentry kind="lib" path="lib/com.incors/kunststoff-2.0.1.jar"/>
	<classpathentry kind="lib" path="lib/cz.autel.dmi/higlayout-1.0a-wenzel.jar"/>
	<classpathentry kind="lib" path="lib/gnu.regexp/gnu-regexp-1.1.4.jar"/>
	<classpathentry kind="lib" path="lib/javax.help/jhall-2.0.1.jar"/>
	<classpathentry kind="lib" path="lib/novaworx.syntax/novaworx-syntax-0.0.7.jar"/>
	<classpathentry kind="lib" path="lib/org.apache.log4j/log4j-1.2.8.jar"/>
	<classpathentry kind="lib" path="lib/org.apache.xalan/xalan-2.4.1.jar"/>
	<classpathentry kind="lib" path="lib/org.apache.xerces/xml-apis.jar"/>
	<classpathentry kind="lib" path="lib/org.codehaus.groovy/asm-1.3.4.jar"/>
	<classpathentry kind="lib" path="lib/org.codehaus.groovy/groovy-1.0-beta-1.jar"/>
	<classpathentry kind="lib" path="lib/say.fontchooser/JFontChooser.jar"/>
	<classpathentry kind="lib" path="lib/com.jgoodies/jgoodies-common-1.3.0.jar"/>
	<classpathentry kind="lib" path="lib/com.jgoodies/jgoodies-looks-2.5.0.jar"/>
	<classpathentry kind="lib" path="lib/org.apache.xerces/xercesImpl-2.6.2.jar"/>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER/org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType/jdk1.6.0_10"/>
	<classpathentry kind="output" path="bin"/>
</classpath>
