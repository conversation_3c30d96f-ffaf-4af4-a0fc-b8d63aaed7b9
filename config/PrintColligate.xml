<?xml version="1.0" encoding="GB2312"?>
<report name="调度综合命令操作票" pageformat="A4" leftmargin="40" rightmargin="40">
  <configuration>
    <property name="com.jrefinery.report.preview.PreferredWidth">1024</property>
    <property name="com.jrefinery.report.preview.PreferredHeight">768</property>
    <property name="org.jfree.report.modules.output.pageable.pdf.Encoding">Identity-H</property>
    <property name="org.jfree.report.modules.output.pageable.pdf.EmbedFonts">true</property>
  </configuration>
    
  <!-- 页首  -->
  <pageheader fontname="宋体" fontsize="10">
    <!-- 文本域，用于显示表数据中某列的内容  -->
    <!-- x,y: 为坐标点（以页首区域的左上角为(0, 0)点）  -->
    <!-- width, height: 为文本域的宽度和高度，如果为数值则表示为绝对尺寸，如果为百分数，则自动以百分比方式调整尺寸大小  -->
    <!-- alignment: 排列方式 -->
    <!-- fieldname: 域名，即数据域的列名 -->
    <!-- nullstring: 当数据域内容为空时显示的字符串内容 -->
    <!-- alignment: 水平排列方式center，left或right -->
    <!-- vertical-alignment: 竖直排列方式top、middle或bottom -->
    <!-- dynamic: 是否动态调整单元格高度，如果为true，当文本内容不足以在当前尺寸中显示时将自动增加单元格高度 -->
    
    <label x="0" y="0" width="100%" height="20" alignment="center" fontname="黑体" fontsize="20">调 度 综 合 命 令 操 作 票</label>
    <label x="0" y="20" width="100%" height="20" alignment="center" fontname="黑体" fontsize="16">======================================================</label>
    
    <label name="unit" x="2%" y="45" width="30%" height="20"></label>
    <label name="czpsj" x="30%" y="45" width="80%" height="20">年      月     日     星期       </label>
    <label name="no" x="75%" y="45" width="30%" height="20"></label>
    
    <rectangle x="0%" y="60" width="100%" height="30" color="#000000" draw="true" fill="false"/>
    <label x="2%" y="65" width="10%" height="30" alignment="left" vertical-alignment="top">操作任务： </label>
    <label name="task" x="12%" y="65" width="100%" height="30" alignment="left" vertical-alignment="top" dynamic="true"></label>
    
    <rectangle x="0%" y="90" width="10%" height="20" color="#000000" draw="true" fill="false"/>
    <rectangle x="10%" y="90" width="30%" height="20" color="#000000" draw="true" fill="false"/>
    <rectangle x="40%" y="90" width="15%" height="20" color="#000000" draw="true" fill="false"/>
    <rectangle x="55%" y="90" width="15%" height="20" color="#000000" draw="true" fill="false"/>
    <rectangle x="70%" y="90" width="15%" height="20" color="#000000" draw="true" fill="false"/>
    <rectangle x="85%" y="90" width="15%" height="20" color="#000000" draw="true" fill="false"/>
    
    <label x="0%" y="90" width="10%" height="20" alignment="center" vertical-alignment="middle">发令时间</label>
    <label x="10%" y="90" width="30%" height="20" alignment="center" vertical-alignment="middle">     月     日     时     分</label>
    <label x="40%" y="90" width="15%" height="20" alignment="center" vertical-alignment="middle">发　令　人</label>
    <label x="55%" y="90" width="15%" height="20" alignment="center" vertical-alignment="middle"></label>
    <label x="70%" y="90" width="15%" height="20" alignment="center" vertical-alignment="middle">受　令　人</label>
    <label x="85%" y="90" width="15%" height="20" alignment="center" vertical-alignment="middle"></label>
    
    <rectangle x="0%" y="110" width="10%" height="20" color="#000000" draw="true" fill="false"/>
    <rectangle x="10%" y="110" width="30%" height="20" color="#000000" draw="true" fill="false"/>
    <rectangle x="40%" y="110" width="15%" height="20" color="#000000" draw="true" fill="false"/>
    <rectangle x="55%" y="110" width="15%" height="20" color="#000000" draw="true" fill="false"/>
    <rectangle x="70%" y="110" width="15%" height="20" color="#000000" draw="true" fill="false"/>
    <rectangle x="85%" y="110" width="15%" height="20" color="#000000" draw="true" fill="false"/>
    
    <label x="0%" y="110" width="10%" height="20" alignment="center" vertical-alignment="middle">汇报时间</label>
    <label x="10%" y="110" width="30%" height="20" alignment="center" vertical-alignment="middle">     月     日     时     分</label>
    <label x="40%" y="110" width="15%" height="20" alignment="center" vertical-alignment="middle">汇　报　人</label>
    <label x="55%" y="110" width="15%" height="20" alignment="center" vertical-alignment="middle"></label>
    <label x="70%" y="110" width="15%" height="20" alignment="center" vertical-alignment="middle">调　度　员</label>
    <label x="85%" y="110" width="15%" height="20" alignment="center" vertical-alignment="middle"></label>
  </pageheader>
  
  <!-- 分组 -->
  <groups>
  <group name="Group1">
  <!-- 分组字段 -->
    <fields>
      <field>page</field>
    </fields>
  <!-- 分组尾 -->
  <groupfooter height="18" fontname="宋体" fontsize="10">
    <rectangle x="0%" y="0" width="10%" height="17" color="#000000" draw="true" fill="false"/>
    <rectangle x="10%" y="0" width="100%" height="17" color="#000000" draw="true" fill="false"/>
    <label x="0%" y="0" width="10%" height="17" alignment="center" vertical-alignment="middle">备注事项</label>
    <label name="remark" x="11%" y="0" width="100%" height="17" alignment="left" vertical-alignment="middle" dynamic="true"></label>
  	<label x="10%" y="25" width="40%" height="30" alignment="left" vertical-alignment="top">拟令：</label>
  	<label name="npr" x="15%" y="25" width="40%" height="30" alignment="left" vertical-alignment="top"></label>
  	<label x="33%" y="25" width="40%" height="30" alignment="left" vertical-alignment="top">审查：</label>
	<label name="scr" x="38%" y="25" width="40%" height="30" alignment="left" vertical-alignment="top"></label>
  	<label x="57%" y="25" width="40%" height="30" alignment="left" vertical-alignment="top">发令：</label>
	<label name="flr" x="62%" y="25" width="40%" height="30" alignment="left" vertical-alignment="top"></label>
  	<label x="80%" y="25" width="40%" height="30" alignment="left" vertical-alignment="top">监护：</label>
	<label name="jhr" x="85%" y="25" width="40%" height="30" alignment="left" vertical-alignment="top"></label>
  	</groupfooter>
    </group>
    </groups>
      
  <!-- 数据域  -->
  <items fontname="宋体" fontsize="10">  
    <string-field fieldname="note" x="0%" y="5" width="10%" height="17" alignment="center" vertical-alignment="middle" nullstring="" dynamic="true"/>
    <string-field fieldname="operation" x="12%" y="5" width="100%" height="17" alignment="left" vertical-alignment="middle" nullstring="" dynamic="true"/>
    <line x1="0" y1="0" x2="0" y2="100%"/>
    <line x1="10%" y1="0" x2="10%" y2="100%"/>
    <line x1="100%" y1="0" x2="100%" y2="100%"/>
    <line x1="0" y1="100%" x2="100%" y2="100%"/>
  </items>
    
</report>