<?xml version="1.0" encoding="GB2312"?>
<report name="变电站倒闸操作票" pageformat="A4" leftmargin="40" rightmargin="40">
  <configuration>
    <property name="com.jrefinery.report.preview.PreferredWidth">1024</property>
    <property name="com.jrefinery.report.preview.PreferredHeight">768</property>
    <property name="org.jfree.report.modules.output.pageable.pdf.Encoding">Identity-H</property>
    <property name="org.jfree.report.modules.output.pageable.pdf.EmbedFonts">true</property>
  </configuration>
    
  <!-- 页首  -->
  <pageheader fontname="宋体" fontsize="10">
    <!-- 文本域，用于显示表数据中某列的内容  -->
    <!-- x,y: 为坐标点（以页首区域的左上角为(0, 0)点）  -->
    <!-- width, height: 为文本域的宽度和高度，如果为数值则表示为绝对尺寸，如果为百分数，则自动以百分比方式调整尺寸大小  -->
    <!-- alignment: 排列方式 -->
    <!-- fieldname: 域名，即数据域的列名 -->
    <!-- nullstring: 当数据域内容为空时显示的字符串内容 -->
    <!-- alignment: 水平排列方式center，left或right -->
    <!-- vertical-alignment: 竖直排列方式top、middle或bottom -->
    <!-- dynamic: 是否动态调整单元格高度，如果为true，当文本内容不足以在当前尺寸中显示时将自动增加单元格高度 -->
    
    <label x="0" y="0" width="100%" height="20" alignment="center" fontname="黑体" fontsize="20">变 电 站 倒 闸 操 作 票</label>
    <label x="0" y="20" width="100%" height="20" alignment="center" fontname="黑体" fontsize="16">======================================================</label>
     
    <label name="czpsj" x="20%" y="45" width="80%" height="20">年      月     日     星期       </label>
    <label name="no" x="70%" y="45" width="30%" height="20"></label>
    
    <rectangle x="0%" y="60" width="100%" height="30" color="#000000" draw="true" fill="false"/>
    <label x="2%" y="65" width="10%" height="30" alignment="left" vertical-alignment="top">操作任务： </label>
    <label name="task" x="12%" y="65" width="100%" height="30" alignment="left" vertical-alignment="top" dynamic="true"></label>
    
    <rectangle x="0%" y="90" width="100%" height="20" color="#000000" draw="true" fill="false"/>
    <label name="nlsj" x="5%" y="90" width="40%" height="20" alignment="left" vertical-alignment="middle">拟令日期：     年     月     日</label>
    <label name="flsj" x="60%" y="90" width="40%" height="20" alignment="left" vertical-alignment="middle">发令日期：     年     月     日 </label>
    
    <rectangle x="0%" y="110" width="3%" height="30" color="#000000" draw="true" fill="false"/>
    <rectangle x="3%" y="110" width="12%" height="30" color="#000000" draw="true" fill="false"/>
    <rectangle x="15%" y="110" width="3%" height="30" color="#000000" draw="true" fill="false"/>
    <rectangle x="18%" y="110" width="46%" height="30" color="#000000" draw="true" fill="false"/>
    <rectangle x="64%" y="110" width="8%" height="30" color="#000000" draw="true" fill="false"/>
    <rectangle x="72%" y="110" width="10%" height="30" color="#000000" draw="true" fill="false"/>
    <rectangle x="82%" y="110" width="8%" height="30" color="#000000" draw="true" fill="false"/>
    <rectangle x="90%" y="110" width="10%" height="30" color="#000000" draw="true" fill="false"/>
    <label x="0%" y="110" width="3%" height="30" alignment="center" vertical-alignment="middle">顺序</label>
    <label x="3%" y="110" width="12%" height="30" alignment="center" vertical-alignment="middle">操作单位</label>
    <label x="15%" y="110" width="3%" height="30" alignment="center" vertical-alignment="middle">步骤</label>
    <label x="18%" y="110" width="46%" height="30" alignment="center" vertical-alignment="middle">操作内容</label>
    <label x="64%" y="110" width="8%" height="30" alignment="center" vertical-alignment="middle" dynamic="true">发令时间</label>
    <label x="72%" y="110" width="10%" height="30" alignment="center" vertical-alignment="middle">受令人</label>
    <label x="82%" y="110" width="8%" height="30" alignment="center" vertical-alignment="middle" dynamic="true">汇报时间</label>
    <label x="90%" y="110" width="10%" height="30" alignment="center" vertical-alignment="middle">汇报人</label>
  </pageheader>
  
  <!-- 分组 -->
  <groups>
  <group name="Group1">
  <!-- 分组字段 -->
    <fields>
      <field>page</field>
    </fields>
  <!-- 分组尾 -->
  <groupfooter height="18" fontname="宋体" fontsize="10">
    <rectangle x="0%" y="0" width="5%" height="30" color="#000000" draw="true" fill="false"/>
    <rectangle x="5%" y="0" width="100%" height="30" color="#000000" draw="true" fill="false"/>
    <label x="0%" y="0" width="5%" height="30" alignment="center" vertical-alignment="middle">备注事项</label>
    <label name="remark" x="6%" y="0" width="100%" height="30" alignment="left" vertical-alignment="middle" dynamic="true"></label>
  	<label x="10%" y="35" width="40%" height="15" alignment="left" vertical-alignment="top">拟令：</label>
  	<label name="npr" x="15%" y="35" width="40%" height="15" alignment="left" vertical-alignment="top"></label>
  	<label x="33%" y="35" width="40%" height="15" alignment="left" vertical-alignment="top">审查：</label>
	<label name="scr" x="38%" y="35" width="40%" height="15" alignment="left" vertical-alignment="top"></label>
  	<label x="57%" y="35" width="40%" height="15" alignment="left" vertical-alignment="top">发令：</label>
	<label name="flr" x="62%" y="35" width="40%" height="15" alignment="left" vertical-alignment="top"></label>
  	<label x="80%" y="35" width="40%" height="15" alignment="left" vertical-alignment="top">监护：</label>
	<label name="jhr" x="85%" y="35" width="40%" height="15" alignment="left" vertical-alignment="top"></label>
  	</groupfooter>
    </group>
    </groups>
      
  <!-- 数据域  -->
  <items fontname="宋体" fontsize="10">  
    <string-field fieldname="num" x="0%" y="5" width="3%" height="17" alignment="center" vertical-alignment="middle" nullstring="" dynamic="true"/>
    <string-field fieldname="unit" x="3%" y="5" width="12%" height="17" alignment="center" vertical-alignment="middle" nullstring="" dynamic="true"/>
    <string-field fieldname="step" x="15%" y="5" width="3%" height="17" alignment="center" vertical-alignment="middle" nullstring="" dynamic="true"/>
    <string-field fieldname="operation" x="19%" y="5" width="45%" height="17" alignment="left" vertical-alignment="middle" nullstring="" dynamic="true"/>
    <string-field fieldname="flsj" x="64%" y="5" width="8%" height="17" alignment="center" vertical-alignment="middle" nullstring="" dynamic="true"/>
    <string-field fieldname="slr" x="72%" y="5" width="10%" height="17" alignment="center" vertical-alignment="middle" nullstring="" dynamic="true"/>
    <string-field fieldname="hbsj" x="82%" y="5" width="8%" height="17" alignment="center" vertical-alignment="middle" nullstring="" dynamic="true"/>
    <string-field fieldname="hbr" x="90%" y="5" width="10%" height="17" alignment="center" vertical-alignment="middle" nullstring="" dynamic="true"/>
    <line x1="0" y1="0" x2="0" y2="100%"/>
    <line x1="3%" y1="0" x2="3%" y2="100%"/>
    <line x1="15%" y1="0" x2="15%" y2="100%"/>
    <line x1="18%" y1="0" x2="18%" y2="100%"/>
    <line x1="64%" y1="0" x2="64%" y2="100%"/>
    <line x1="72%" y1="0" x2="72%" y2="100%"/>
    <line x1="82%" y1="0" x2="82%" y2="100%"/>
    <line x1="90%" y1="0" x2="90%" y2="100%"/>
    <line x1="100%" y1="0" x2="100%" y2="100%"/>
    <line x1="0" y1="100%" x2="100%" y2="100%"/>
  </items>
    
</report>