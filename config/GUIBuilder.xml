<?xml version="1.0" encoding="utf-8"?>
<UI version="1.0">
    <widget class="Frame" name="MainFrame">
        <property name="title" type="istring">云南地调防误操作票系统-v1.0.0</property>
        <property name="spacing" type="int">4</property>
        <property name="iconImage" type="icon">title.png</property>
        <property name="size" type="dimension">
            <width>1800</width>
            <height>1600</height>
        </property>
        <property name="extendedState" type="int">6</property>
        <emit event="close" name="quit"/>
        <widget class="MenuBar" name="menuBar">
			<property name="backgroundIcon" type="icon">bg1.png</property>
            <widget class="Menu">
                <property name="text" type="istring">系统管理</property>
				<property name="backgroundIcon" type="icon">bg2.png</property>
                <widget class="MenuItem">
                    <property name="text" type="istring">常用语管理</property>
                    <emit event="selected" name="glossaryMgr"/>
                </widget>
                <widget class="Separator"/>
                <widget class="MenuItem" name="system">
                    <property name="text" type="istring">用户管理</property>
                    <emit event="selected" name="userManager"/>
                </widget>
                <widget class="MenuItem" name="codeAuthManager">
                    <property name="text" type="istring">角色权限管理</property>
                    <emit event="selected" name="codeAuthManager"/>
                </widget>
                  <widget class="MenuItem">
                    <property name="text" type="istring">权限管理</property>
                    <emit event="selected" name="authManager"/>
                </widget>
                <widget class="MenuItem">
                    <property name="text" type="istring">修改密码</property>
                    <emit event="selected" name="alterUser"/>
                </widget>
                <widget class="Separator"/>
                <widget class="MenuItem">
                    <property name="text" type="istring">重新登录</property>
                    <emit event="selected" name="loginSystem"/>
                </widget>
                <widget class="MenuItem">
                    <property name="text" type="istring">退出</property>
                    <emit event="selected" name="exitSystem"/>
                </widget>
            </widget>
            <widget class="Menu">
                <property name="text" type="istring">操作票管理</property>
				<property name="backgroundIcon" type="icon">bg2.png</property>
                <widget class="MenuItem">
                    <property name="text" type="istring">命令票</property>
                    <emit event="selected" name="lookMLTicket"/>
                </widget>
                <widget class="MenuItem">
                    <property name="text" type="istring">典型票</property>
                    <emit event="selected" name="lookDXTicket"/>
                </widget>
               <!--  <widget class="MenuItem">
                    <property name="text" type="istring">手工拟票</property>
                    <emit event="selected" name="lookSGTicket"/>
                </widget> --> 
                
                <!--
                <widget class="MenuItem">
                    <property name="text" type="istring">新设备投运</property>
                    <emit event="selected" name="newlinecard"/>
                </widget>
                -->
				<!-- 
                <widget class="Separator"/>
                <widget class="MenuItem">
                    <property name="text" type="istring">票号设置</property>
                    <emit event="selected" name="flowNumSet"/>
                </widget>
                <widget class="Separator"/>
                <widget class="MenuItem">
                    <property name="text" type="istring">操作票统计</property>
                    <emit event="selected" name="countTicket"/>
                </widget>
                -->
            </widget>
            
            <widget class="Menu">
                <property name="text" type="istring">设置</property>
				<property name="backgroundIcon" type="icon">bg2.png</property>
				
                <widget class="MenuItem">
                    <property name="text" type="istring">系统设置</property>
                    <emit event="selected" name="systemConfig"/>
                </widget>
                <widget class="MenuItem">
                    <property name="text" type="istring">操作菜单配置</property>
                    <emit event="selected" name="opMenuConfig"/>
                </widget>
                 
                <widget class="MenuItem">
                    <property name="text" type="istring">操作票模板维护</property>
                    <emit event="selected" name="deviceDesc"/>
                </widget>
                <widget class="MenuItem">
                    <property name="text" type="istring">操作规则维护</property>
                    <emit event="selected" name="deviceRule"/>
                </widget>
                <widget class="MenuItem">
                    <property name="text" type="istring">自定义规则术语</property>
                    <emit event="selected" name="customCodex"/>
                </widget>
                <widget class="MenuItem">
                    <property name="text" type="istring">模型监测</property>
                    <emit event="selected" name="modelMonitor"/>
                </widget>
                <widget class="MenuItem" >
                    <property name="text" type="istring">线路用户站术语维护</property>
                    <emit event="selected" name="userStation"/>
                </widget>
                <widget class="MenuItem" >
                    <property name="text" type="istring">厂站树维护</property>
                    <emit event="selected" name="treeStation"/>
                </widget>
                <widget class="MenuItem" >
                    <property name="text" type="istring">线路电压互感器维护</property>
                    <emit event="selected" name="lineStationTransformer"/>
                </widget> 
                <widget class="MenuItem" >
                    <property name="text" type="istring">线路运维站维护</property>
                    <emit event="selected" name="lineYwStation"/>
                </widget> 
                 <widget class="MenuItem" >
                    <property name="text" type="istring">厂站压板维护</property>
                    <emit event="selected" name="stationBoard"/>
                </widget> 
                <widget class="MenuItem" >
                    <property name="text" type="istring">厂站站用变维护</property>
                    <emit event="selected" name="devStationTransformer"/>
                </widget> 
                 <widget class="MenuItem" >
                    <property name="text" type="istring">术语模板维护</property>
                    <emit event="selected" name="cardwordSet"/>
                </widget> 
                <widget class="Separator"/>
                <!-- 
                <widget class="MenuItem">
                    <property name="text" type="istring">设备状态初始化</property>
                    <emit event="selected" name="DeviceStatusInit"/>
                </widget>
                 
                 <widget class="CheckBox">
                    <property name="text" type="istring">设备选择模式</property>
                    <emit event="selected" name="mouseMode"/>
                </widget>
                -->
                 <widget class="MenuItem">
                    <property name="text" type="istring">调度机构设置</property>
                    <emit event="selected" name="organSet"/>
                </widget>
                <widget class="MenuItem">
                    <property name="text" type="istring">许可机构设置</property>
                    <emit event="selected" name="permissionSet"/>
                </widget>
                
                <!--
                <widget class="MenuItem">
                    <property name="text" type="istring">调管机构初始化</property>
                    <emit event="selected" name="organInit"/>
                </widget>
                -->
                
                <widget class="MenuItem">
                    <property name="text" type="istring">接线方式初始化</property>
                    <emit event="selected" name="DeviceTypeInit"/>
                </widget>
                
                <widget class="MenuItem">
                    <property name="text" type="istring">所有厂站状态同步</property>
                    <emit event="selected" name="statusCheckAll"/>
                </widget>
                <!--
				<widget class="MenuItem">
                    <property name="text" type="istring">状态初始化</property>
                    <emit event="selected" name="DeviceStatusInit"/>
                </widget>
                -->
                <!-- 
                <widget class="MenuItem">
                    <property name="text" type="istring">操作票设备状态</property>
                    <emit event="selected" name="DeviceCZPstatus"/>
                </widget>
                <widget class="MenuItem">
                    <property name="text" type="istring">EMS设备状态</property>
                    <emit event="selected" name="DeviceEMSstatus"/>
                </widget>
                 -->
                <widget class="MenuItem">
                    <property name="text" type="istring">设备属性</property>
                    <emit event="selected" name="attribute"/>
                </widget>
                <!-- 
                <widget class="MenuItem">
                    <property name="text" type="istring">设备对位</property>
                    <emit event="selected" name="equipStatusSet"/>
                </widget>
                
                <widget class="MenuItem">
                    <property name="text" type="istring">代码管理</property>
                    <emit event="selected" name="codeManage"/>
                </widget>
                <widget class="MenuItem">
                    <property name="text" type="istring">设备选择</property>
                    <emit event="selected" name="driverchange"/>
                </widget>
                <widget class="MenuItem">
                    <property name="text" type="istring">临时票面</property>
                    <emit event="selected" name="temporary"/>
                </widget>
                 -->
                 <!--  
                 <widget class="MenuItem">
                    <property name="text" type="istring">代码管理</property>
                    <emit event="selected" name="codeManager"/>
                </widget>
               	 -->
                <widget class="MenuItem">
                    <property name="text" type="istring">设备状态管理</property>
                    <emit event="selected" name="statusManager"/>
                </widget>
                <widget class="MenuItem">
                    <property name="text" type="istring">设备操作管理</property>
                    <emit event="selected" name="operationManager"/>
                </widget>
                <widget class="MenuItem">
                    <property name="text" type="istring">图模校核</property>
                    <emit event="selected" name="checkSvgAndData"/>
                </widget>
                <!--
                <widget class="MenuItem">
                    <property name="text" type="istring">机构管理</property>
                    <emit event="selected" name="unitManager"/>
                </widget>
                 -->
            </widget>
            
            <widget class="Menu">
                <property name="text">帮助</property>
				<property name="backgroundIcon" type="icon">bg2.png</property>
				<!--
				<widget class="MenuItem">
                    <property name="text" type="istring">CIM更新</property>
                    <emit event="selected" name="CIM"/>
                </widget>
				<widget class="MenuItem">
                    <property name="text" type="istring">SVG更新</property>
                    <emit event="selected" name="SVG"/>
                </widget>
				-->
                <widget class="MenuItem">
                    <property name="text">使用手册</property>
                    <emit event="selected" name="help"/>
                </widget>
                <widget class="MenuItem">
                    <property name="text">关于系统</property>
                    <emit event="selected" name="about"/>
                </widget>
            </widget>
            
            <!--
            <widget class="Menu">
                <property name="text" type="istring">安全校核</property>
                <widget class="MenuItem">
                    <property name="text" type="istring">BPA校核结果</property>
                    <emit event="selected" name="aqjh"/>
                </widget>
                 <widget class="MenuItem">
                    <property name="text" type="istring">EMS校核结果</property>
                    <emit event="selected" name="emsjh"/>
                </widget>
                <widget class="MenuItem">
                    <property name="text" type="istring">图形校核</property>
                    <emit event="selected" name="graph"/>
                </widget>
            </widget>
            -->
            <!-- 
            <widget class="Menu">
                <property name="text" type="istring">系统接口</property>
                <widget class="MenuItem">
                    <property name="text" type="istring">系统接口子菜单</property>
                    <emit event="selected" name="3"/>
                </widget>
                 <widget class="MenuItem">
                    <property name="text" type="istring">专家规则库1</property>
                    <emit event="selected" name="expertRule"/>
                </widget>
                <widget class="MenuItem">
                    <property name="text" type="istring">专家规则库2</property>
                    <emit event="selected" name="expertRuleT"/>
                </widget>
            </widget>
            -->
            <widget class="Spacer">
                <property name="type" type="string">glue</property>
                <property name="axis" type="string">h</property>
            </widget>
            
        </widget>
		<widget class="ToolBarPanel">
            <anchor border="north" type="border"/>
			<widget class="ToolBar" name="toolBar">
			<property name="backgroundIcon" type="icon">bg2.png</property>
					<anchor border="north" type="border"/>
					<property name="floatable" type="bool">false</property>
					<property name="orientation" type="int">0</property>
					
						       
					<widget class="Button" preset="hover" name="smartTicket">
						<property name="icon" type="icon">zncp.png</property>
						<property name="text">智能出票</property>
						<property name="toolTipText">进入智能出票模式</property>
						<emit event="clicked" name="smartTicket"/>
					</widget>           
					
<!-- 					<widget class="Button" preset="hover" name="stepTicket"> -->
<!-- 						<property name="icon" type="icon">dtkp.png</property> -->
<!-- 						<property name="text">点图开票</property> -->
<!-- 						<property name="toolTipText">进入点图开票模式</property> -->
<!-- 						<emit event="clicked" name="stepTicket"/> -->
<!-- 					</widget> -->
					
<!-- 					<widget class="Button" preset="hover" name="samestepTicket"> -->
<!-- 						<property name="icon" type="icon">dxkp.png</property> -->
<!-- 						<property name="text">多选开票</property> -->
<!-- 						<property name="toolTipText">可同时选择同类设备进行点图开票</property> -->
<!-- 						<emit event="clicked" name="samestepTicket"/> -->
<!-- 					</widget> -->
					
					<widget class="Button" preset="hover" name="equipStatusSet">
						<property name="icon" type="icon">ztzwbule.png</property>
						<property name="text">设备置位</property>
						<property name="toolTipText">进入设备置位模式</property>
						<emit event="clicked" name="equipStatusSet"/>
					</widget>
					
					<widget class="Button" preset="hover" name="lookTransMap">
						<property name="icon" type="icon">xstx.png</property>
						<property name="text">显示图形</property>
						<property name="toolTipText">显示图形</property>
						<emit event="clicked" name="lookTransMap"/>
					</widget>    
					
					<widget class="Button" preset="hover" name="lookMLTicket">
						<property name="icon" type="icon">manage.png</property>
						<property name="text">操作票管理</property>
						<property name="toolTipText">操作票管理</property>
						<emit event="clicked" name="lookMLTicket"/>
					</widget>	
					
					<widget class="Button" preset="hover" name="glossaryMgr">
						<property name="icon" type="icon">glossary.png</property>
						<property name="text">常用语</property>
						<property name="toolTipText">常用语</property>
						<emit event="clicked" name="glossaryMgr"/>
					</widget> 
					<widget class="Button" preset="hover" name="statusCheck">
						<property name="icon" type="icon">zttb.png</property>
						<property name="text">状态同步</property>
						<property name="toolTipText">以实时遥信数据设置设备状态</property>
						<emit event="clicked" name="statusCheck"/>
					</widget>
					
					<widget class="Button" preset="hover" name="terminalCheck">
						<property name="icon" type="icon">ljd.png</property>
						<property name="text">连接点检测</property>
						<property name="toolTipText">检测厂站的连接点信息</property>
						<emit event="clicked" name="terminalCheck"/>
					</widget>
					
					<widget class="Button" preset="hover" name="channelWord">
						<property name="icon" type="icon">channel.png</property>
						<property name="text">导入Word</property>
						<property name="toolTipText">导入Word</property>
						<emit event="clicked" name="channelWord"/>
					</widget>
					
					<widget name="zoomeItem" class="Panel">
						  <widget name="zoomSlider" class="com.tellhow.czp.widget.ZoomItemWidget"></widget>
					</widget>
					 
					<widget class="Separator"/>	 
					
					<widget name="statuesList"  class="Panel">
						<widget name="statues" class="com.tellhow.czp.widget.StatusListRTWidget"></widget>
					</widget>
					
					<widget name="statuscheckList"  class="Panel">
						<widget name="statuscheck" class="com.tellhow.czp.app.yndd.view.StatusCheckWidget"></widget>
					</widget>
				   
			</widget>
		</widget>
        <widget class="PopupMenu" name="svgCloseMenu">
	        <widget class="MenuItem" name="closeSVGPanel">
	            <property name="text">关闭</property>
	            <emit event="selected" name="closeSVGPanel"/>
	        </widget>
	        <widget class="MenuItem" name="closeOthers">
	            <property name="text">关闭其他</property>
	            <emit event="selected" name="closeOthers"/>
	        </widget>
	        <widget class="MenuItem" name="closeAll">
	            <property name="text">关闭所有</property>
	            <emit event="selected" name="closeAll"/>
	        </widget>
	    </widget>
        <widget class="Panel">
	        <widget class="Panel">
	        <layout type="border"/>
	        		<widget class="SplitPane">
					<anchor border="center" type="border"/>
	        			<widget class="TabbedPane">
						<property name="Panel2.title">厂站视图</property>
                        <widget name="Panel2" class="Panel">
<!--                             <widget name="Tree2" class="com.tellhow.czp.app.yndd.view.TransTreeWidgetHH"></widget>-->
<!--                             <widget name="Tree2" class="com.tellhow.czp.app.yndd.view.TransTreeWidgetQJ"></widget>-->
<!--                            fake-->
<!--                             <widget name="Tree2" class="com.tellhow.czp.app.yndd.view.TransTreeWidgetKM"></widget>-->
                             <widget name="Tree2" class="com.tellhow.czp.app.yndd.view.TransTreeWidget"></widget>
                       </widget>
                    	</widget>
			        	<widget class="SplitPane" name="splitPane">
						<property name="backgroundIcon" type="icon">bg1.png</property>
			        		<property name="resizeWeight" type="double">1.0</property>
			        		<widget name="svgTabbedPane" class="TabbedPane">
			        		<property name="backgroundIcon" type="icon">bg_02.png</property>
			        			<emit event="close" name="closeSVGPanel"/>
			        			<emit event="rightclick" name="closeMenu"/>
			        		</widget>
			        		<widget class="Panel" name="OtwPanel">
							</widget> 
			        	</widget> 	
			        </widget>
			        <widget class="com.tellhow.czp.widget.FootWidgetTH" name="footPane">
					<anchor border="south" type="border"/>
					</widget>
	        </widget>
	       
        </widget>
       
    </widget>
</UI>
