<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!--*****************************************************************************
				系	统	参	数
******************************************************************************** --><params>
    <param name="projectparam">
        <value key="cardtype">0</value> <!--开票方式，0元件令，1状态令  -->
        <value key="apptype">0</value> <!--业务类型，0操作票，1监控票 -->
        <value key="unitCode">0</value> <!--区域编码36017000000 -->
        <value key="projectID">czp</value> <!--系统ID -->
        <value key="isAutoLoadSVGFile">true</value> <!--登录是否更新SVG图形 -->
        <value key="isvalidateMAC">false</value> <!--登录是否验证MAC地址 -->
        <value key="isInitEMSStatus" name="是否自动更新EMS状态">true</value> <!--登录是否使用EMS状态 -->
        <value key="isInitDoubleScreen" name="是否双屏显示">fa lse</value> <!--是否双屏显示 -->
        <value key="mapNamePattern"/> <!--图形名称 -->
        <value key="defaultUser">1</value> <!--默认用户 -->
        <value key="flowNum">[类型]yyyyMMdd0000</value>
        <value key="isMaxRangeOff">true</value> <!--是否启用最大范围停电 -->
        <value key="isCheckTicket">true</value>
        <value key="isPrintSQL">true</value>
<!--        <value key="appConfig">/AppConfigXSBN.properties</value>-->
<!--        <value key="equipuser">OPCARDXSBN.</value>-->
<!--        <value key="opcarduser">OPCARDXSBN.</value>-->

<!--        曲靖-->
<!--        <value key="appConfig">/AppConfigQJ.properties</value>-->
<!--        <value key="equipuser">OPCARDQJ.</value>-->
<!--        <value key="opcarduser">OPCARDQJ.</value>-->

<!--        红河-->
<!--        <value key="appConfig">/AppConfigHH.properties</value>-->
<!--        <value key="equipuser">OPCARDHH.</value>-->
<!--        <value key="opcarduser">OPCARDHH.</value>-->

<!--        普洱-->
<!--        <value key="appConfig">/AppConfigPE.properties</value>-->
<!--        <value key="equipuser">OPCARDPE.</value>-->
<!--        <value key="opcarduser">OPCARDPE.</value>-->

<!--        玉溪-->
<!--        <value key="appConfig">/AppConfigYX.properties</value>-->
<!--        <value key="equipuser">OPCARDYX.</value>-->
<!--        <value key="opcarduser">OPCARDYX.</value>-->

<!--        昆明-->
<!--        <value key="appConfig">/AppConfigKM.properties</value>-->
<!--        <value key="equipuser">OPCARDKM.</value>-->
<!--        <value key="opcarduser">OPCARDKM.</value>-->

<!--       西双版纳-->
<!--        <value key="appConfig">/AppConfigXSBN.properties</value>-->
<!--        <value key="equipuser">OPCARDXSBN.</value>-->
<!--        <value key="opcarduser">OPCARDXSBN.</value>-->

<!--        昭通-->
<!--        <value key="appConfig">/AppConfigZT.properties</value>-->
<!--        <value key="equipuser">OPCARDZT.</value>-->
<!--        <value key="opcarduser">OPCARDZT.</value>-->

<!--        临沧-->
<!--        <value key="appConfig">/AppConfigLC.properties</value>-->
<!--        <value key="equipuser">OPCARDLC.</value>-->
<!--        <value key="opcarduser">OPCARDLC.</value>-->

<!--        文山-->
<!--        <value key="appConfig">/AppConfigWS.properties</value>-->
<!--        <value key="equipuser">OPCARDWS.</value>-->
<!--        <value key="opcarduser">OPCARDWS.</value>-->

<!--        怒江-->
<!--        <value key="appConfig">/AppConfigNJ.properties</value>-->
<!--        <value key="equipuser">OPCARDNJ.</value>-->
<!--        <value key="opcarduser">OPCARDNJ.</value>-->

<!--        德宏-->
<!--        <value key="appConfig">/AppConfigDH.properties</value>-->
<!--        <value key="equipuser">OPCARDDH.</value>-->
<!--        <value key="opcarduser">OPCARDDH.</value>-->

<!--        保山-->
        <value key="appConfig">/AppConfigBS.properties</value>
        <value key="equipuser">OPCARDBS.</value>
        <value key="opcarduser">OPCARDBS.</value>


        <!-- 数据库PLATFORM模式全局变量  -->
        <value key="platformuser">PLATFORM.</value>
    </param>
 	<param name="deviceCode">
    <!-- 设备编号规范 -->
       <value code="31,32,33,34" key="MULIANKG"/>
    <!-- 母联开关(包括母联分段开关)序位 -->
       <value code="41" key="PANGMUKG"/>
    <!-- 旁母开关序位 -->
       <value code="11,12,13,14,15,16,17,18,19,21,22,23,24,25,26,27,28,29,61,62,63,64,65,66,67,68,69,81,82,83,84,85,86,87,88,89" key="XIANLUKG"/>
    <!-- 线路开关序位 -->
       <value code="01,02,03,04,05,06,07,08,09" key="ZHUBIANKG"/>
    <!-- 主变开关序位 --> 
       <value code="5" key="PTDZ"/> <!-- PT刀闸第二位编号 -->
       <value code="9" key="DRDZ"/> <!-- 电容刀闸第二位编号 -->
       <value code="7" key="XHXQDZ"/> <!-- 消弧线圈刀闸第二位编号 -->
       <value code="10,20,30" key="MUXIANDD"/>
    <!-- 母线接地刀闸编号 -->
       <value code="1,2" key="MUXIANDZ"/> <!-- 母线刀闸编号尾号 -->
       <value code="3" key="XIANLUDZ"/>
    <!-- 线路刀闸编号尾号 -->
       <value code="4" key="PANGMUDZ"/>
    <!-- 旁母刀闸编号尾号 -->
       <value code="03,30,10" key="XIANLUDD"/>
    <!-- 线路接地刀闸编号尾号 -->
    </param>
    <param name="svgLayer">
    <!--
    isVisiable默认true; isHandleEvent默认true; isRefresh默认false; refreshInterval默认0(单位秒)
    <value layerID="Example" layerName="背景" isVisiable="true" isHandleEvent="true" isRefresh="false" refreshInterval="0"/>
    -->
    <value layerID="Head_Layer" layerName="背景"/>
    <value layerID="Substation_Layer" layerName="厂站"/>
    <value layerID="Other_Layer" layerName="其他"/>
    <value isVisiable="true" layerID="Sensitive_Layer" layerName="敏感图元"/>
    <value layerID="PowerFlow_Layer" layerName="潮流"/>
    <value isRefresh="false" isVisiable="false" layerID="MeasurementClass" layerName="量测值" refreshInterval="300"/>
    <value isRefresh="false" isVisiable="false" layerID="MeasurementValue_Layer" layerName="量测值" refreshInterval="300"/>
    <value isRefresh="false" isVisiable="false" layerID="MeasureValue_Layer" layerName="量测值" refreshInterval="300"/>
    <value isRefresh="false" isVisiable="false" layerID="TermMeasure_Layer" layerName="量测值" refreshInterval="300"/>
    <value layerID="Text_Layer" layerName="文本"/>
    <value isHandleEvent="false" layerID="Link_Layer" layerName="连接线"/>
    <value isHandleEvent="false" layerID="ConnectNode_Layer" layerName="连接线"/>
    <value layerID="BusSection_Layer" layerName="母线"/>
    <value layerID="Bus_Layer" layerName="母线"/>
    <value layerID="Breaker_Layer" layerName="开关"/>
    <value layerID="Disconnector_Layer" layerName="刀闸"/>
    <value layerID="GroundDisconnector_Layer" layerName="接地刀闸"/>
    <value layerID="GroundLine_Layer" layerName="接地线"/>
    <value layerID="Transformer3_Layer" layerName="三卷变"/>
    <value layerID="Transformer2_Layer" layerName="两卷变"/>
    <value layerID="Generator_Layer" layerName="发电机"/>
    <value layerID="Compensator_Layer" layerName="电容器"/>
    <value layerID="Reactor_Layer" layerName="电抗器"/>
    <value layerID="PT_Layer" layerName="电压互感器"/>
    <value layerID="Load_Layer" layerName="负荷"/>
    <value layerID="ACLine_Layer" layerName="线路"/>
    <value layerID="ACLineSegment_Layer" layerName="线路"/>
    <value layerID="Flag_Layer" layerName="标志牌"/>
    <value isHandleEvent="false" layerID="DynamicPoint_Layer" layerName="动态点"/>
    </param>
    <param name="deviceColor">
       <value code="rgb(0,0,255)" key="1000"/>
       <value code="rgb(0,0,255)" key="800"/>
       <value code="rgb(250,128,10)" key="750"/>
       <value code="rgb(250,128,10)" key="660"/>
       <value code="rgb(255,0,0)" key="500"/>
       <value code="rgb(30,144,255)" key="330"/>
       <value code="rgb(255,255,255)" key="220"/>
       <value code="rgb(255,0,0)" key="110"/>
       <value code="rgb(255,204,0)" key="66"/>
       <value code="rgb(255,255,0)" key="35"/>
       <value code="rgb(226, 171, 3)" key="20"/>
       <value code="rgb(237,107,158)" key="18"/>
       <value code="rgb(0,128,0)" key="15"/>
       <value code="rgb(185,72,66)" key="13"/>
       <value code="rgb(0,210,0)" key="10"/>
       <value code="rgb(0,0,139)" key="6"/>
        <value code="rgb(192,192,192)" key="0"/>
    <!-- 开位电压颜色 -->
       <value code="rgb(255,255,255)" key="-1"/>
    <!-- 无状态颜色 -->
       

       
    </param>
    <param name="menuProviderPackage">
    	 <value>com.tellhow.graphicframework.menu.provider,com.tellhow.czp.mainframe.menu.provider</value>
    </param>
    <param name="svgDocumentResolver">
    	 <value>com.tellhow.czp.svg.document.DefaultSVGDocumentResolver</value>
    </param>
    <param name="czpOperator">
    	 <value>com.tellhow.czp.app.CZPOperatorBJ</value>
    </param>
</params>
